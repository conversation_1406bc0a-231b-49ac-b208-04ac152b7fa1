<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage for src/api/data/constants.py: 0%</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_6b508a39.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="pyfile">
<header>
    <div class="content">
        <h1>
            <span class="text">Coverage for </span><b>src/api/data/constants.py</b>:
            <span class="pc_cov">0%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>r</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        &nbsp; toggle line displays
                    </p>
                    <p>
                        <kbd>j</kbd>
                        <kbd>k</kbd>
                        &nbsp; next/prev highlighted chunk
                    </p>
                    <p>
                        <kbd>0</kbd> &nbsp; (zero) top of page
                    </p>
                    <p>
                        <kbd>1</kbd> &nbsp; (one) first highlighted chunk
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>u</kbd> &nbsp; up to the index
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <h2>
            <span class="text">6 statements &nbsp;</span>
            <button type="button" class="run button_toggle_run" value="run" data-shortcut="r" title="Toggle lines run">0<span class="text"> run</span></button>
            <button type="button" class="mis show_mis button_toggle_mis" value="mis" data-shortcut="m" title="Toggle lines missing">6<span class="text"> missing</span></button>
            <button type="button" class="exc show_exc button_toggle_exc" value="exc" data-shortcut="x" title="Toggle lines excluded">0<span class="text"> excluded</span></button>
        </h2>
        <p class="text">
            <a id="prevFileLink" class="nav" href="z_d8b1f68d7960fbdc_cache_warming_scheduler_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a id="indexLink" class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a id="nextFileLink" class="nav" href="z_d8b1f68d7960fbdc_market_data_service_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.6">coverage.py v7.10.6</a>,
            created at 2025-09-28 09:58 -0400
        </p>
        <aside class="hidden">
            <button type="button" class="button_next_chunk" data-shortcut="j"></button>
            <button type="button" class="button_prev_chunk" data-shortcut="k"></button>
            <button type="button" class="button_top_of_page" data-shortcut="0"></button>
            <button type="button" class="button_first_chunk" data-shortcut="1"></button>
            <button type="button" class="button_prev_file" data-shortcut="["></button>
            <button type="button" class="button_next_file" data-shortcut="]"></button>
            <button type="button" class="button_to_index" data-shortcut="u"></button>
            <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
        </aside>
    </div>
</header>
<main id="source">
    <p class="pln"><span class="n"><a id="t1" href="#t1">1</a></span><span class="t"><span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t2" href="#t2">2</a></span><span class="t"><span class="str">Market data constants and configuration values.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t3" href="#t3">3</a></span><span class="t"><span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t4" href="#t4">4</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t5" href="#t5">5</a></span><span class="t"><span class="com"># Top 50 symbols configuration</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t6" href="#t6">6</a></span><span class="t"><span class="nam">TOP_SYMBOLS</span> <span class="op">=</span> <span class="op">[</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t7" href="#t7">7</a></span><span class="t">    <span class="str">'AAPL'</span><span class="op">,</span> <span class="str">'MSFT'</span><span class="op">,</span> <span class="str">'GOOGL'</span><span class="op">,</span> <span class="str">'AMZN'</span><span class="op">,</span> <span class="str">'NVDA'</span><span class="op">,</span> <span class="str">'TSLA'</span><span class="op">,</span> <span class="str">'META'</span><span class="op">,</span> <span class="str">'BRK.B'</span><span class="op">,</span> <span class="str">'UNH'</span><span class="op">,</span> <span class="str">'JNJ'</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t8" href="#t8">8</a></span><span class="t">    <span class="str">'XOM'</span><span class="op">,</span> <span class="str">'V'</span><span class="op">,</span> <span class="str">'PG'</span><span class="op">,</span> <span class="str">'JPM'</span><span class="op">,</span> <span class="str">'HD'</span><span class="op">,</span> <span class="str">'CVX'</span><span class="op">,</span> <span class="str">'MA'</span><span class="op">,</span> <span class="str">'PFE'</span><span class="op">,</span> <span class="str">'ABBV'</span><span class="op">,</span> <span class="str">'BAC'</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t9" href="#t9">9</a></span><span class="t">    <span class="str">'KO'</span><span class="op">,</span> <span class="str">'AVGO'</span><span class="op">,</span> <span class="str">'PEP'</span><span class="op">,</span> <span class="str">'TMO'</span><span class="op">,</span> <span class="str">'COST'</span><span class="op">,</span> <span class="str">'MRK'</span><span class="op">,</span> <span class="str">'WMT'</span><span class="op">,</span> <span class="str">'ACN'</span><span class="op">,</span> <span class="str">'LLY'</span><span class="op">,</span> <span class="str">'DHR'</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t10" href="#t10">10</a></span><span class="t">    <span class="str">'VZ'</span><span class="op">,</span> <span class="str">'ABT'</span><span class="op">,</span> <span class="str">'ADBE'</span><span class="op">,</span> <span class="str">'NKE'</span><span class="op">,</span> <span class="str">'TXN'</span><span class="op">,</span> <span class="str">'NEE'</span><span class="op">,</span> <span class="str">'BMY'</span><span class="op">,</span> <span class="str">'PM'</span><span class="op">,</span> <span class="str">'RTX'</span><span class="op">,</span> <span class="str">'QCOM'</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t11" href="#t11">11</a></span><span class="t">    <span class="str">'T'</span><span class="op">,</span> <span class="str">'LOW'</span><span class="op">,</span> <span class="str">'HON'</span><span class="op">,</span> <span class="str">'UPS'</span><span class="op">,</span> <span class="str">'SBUX'</span><span class="op">,</span> <span class="str">'AMD'</span><span class="op">,</span> <span class="str">'LMT'</span><span class="op">,</span> <span class="str">'IBM'</span><span class="op">,</span> <span class="str">'CAT'</span><span class="op">,</span> <span class="str">'GS'</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t12" href="#t12">12</a></span><span class="t"><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t13" href="#t13">13</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t14" href="#t14">14</a></span><span class="t"><span class="com"># Default cache settings</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t15" href="#t15">15</a></span><span class="t"><span class="nam">DEFAULT_CACHE_TTL</span> <span class="op">=</span> <span class="num">300</span>  <span class="com"># 5 minutes</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t16" href="#t16">16</a></span><span class="t"><span class="nam">HISTORICAL_DATA_TTL</span> <span class="op">=</span> <span class="num">86400</span>  <span class="com"># 24 hours</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t17" href="#t17">17</a></span><span class="t"><span class="nam">WEEKEND_DATA_TTL</span> <span class="op">=</span> <span class="num">259200</span>  <span class="com"># 3 days</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t18" href="#t18">18</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t19" href="#t19">19</a></span><span class="t"><span class="com"># Batch processing settings</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t20" href="#t20">20</a></span><span class="t"><span class="nam">DEFAULT_BATCH_SIZE</span> <span class="op">=</span> <span class="num">10</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t21" href="#t21">21</a></span><span class="t"><span class="nam">WEEKEND_BATCH_SIZE</span> <span class="op">=</span> <span class="num">5</span>&nbsp;</span><span class="r"></span></p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="z_d8b1f68d7960fbdc_cache_warming_scheduler_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a class="nav" href="z_d8b1f68d7960fbdc_market_data_service_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.6">coverage.py v7.10.6</a>,
            created at 2025-09-28 09:58 -0400
        </p>
    </div>
</footer>
</body>
</html>
