{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.10.6", "globals": "9f805957ce78d4f66917f2645178f468", "files": {"z_145eef247bfb46b6___init___py": {"hash": "e6baa73cda2916dad605215f937a92e1", "index": {"url": "z_145eef247bfb46b6___init___py.html", "file": "src/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c491fc8e7f435b94___init___py": {"hash": "2378f2b1da56d091b4085b97c7dfa48e", "index": {"url": "z_c491fc8e7f435b94___init___py.html", "file": "src/analysis/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 2, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_502cac173ba52a86___init___py": {"hash": "8f96e0487d400bed7b4c91e861310773", "index": {"url": "z_502cac173ba52a86___init___py.html", "file": "src/analysis/ai/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_fbbe37d21d225bb3___init___py": {"hash": "8f96e0487d400bed7b4c91e861310773", "index": {"url": "z_fbbe37d21d225bb3___init___py.html", "file": "src/analysis/ai/calculators/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_fbbe37d21d225bb3_sentiment_calculator_py": {"hash": "a52edd521f9866a76669657243603903", "index": {"url": "z_fbbe37d21d225bb3_sentiment_calculator_py.html", "file": "src/analysis/ai/calculators/sentiment_calculator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 38, "n_excluded": 0, "n_missing": 38, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_502cac173ba52a86_enhancement_strategy_py": {"hash": "75927e9a8051726f3ca66bd145a95cc0", "index": {"url": "z_502cac173ba52a86_enhancement_strategy_py.html", "file": "src/analysis/ai/enhancement_strategy.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 45, "n_excluded": 0, "n_missing": 45, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_502cac173ba52a86_ml_models_py": {"hash": "c8b09bf5d8c75aea461d452cac229a4b", "index": {"url": "z_502cac173ba52a86_ml_models_py.html", "file": "src/analysis/ai/ml_models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 294, "n_excluded": 0, "n_missing": 294, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_502cac173ba52a86_ml_training_service_py": {"hash": "bb5bb481b5e915bc6a8b2fb841e7b3a0", "index": {"url": "z_502cac173ba52a86_ml_training_service_py.html", "file": "src/analysis/ai/ml_training_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 72, "n_excluded": 0, "n_missing": 72, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_502cac173ba52a86_recommendation_engine_py": {"hash": "65926513ac8870ecb34ac9d870f61a18", "index": {"url": "z_502cac173ba52a86_recommendation_engine_py.html", "file": "src/analysis/ai/recommendation_engine.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 355, "n_excluded": 0, "n_missing": 355, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c491fc8e7f435b94_enhanced_evaluator_py": {"hash": "5a8313b5cb206961bdd927ab9e2bc7b7", "index": {"url": "z_c491fc8e7f435b94_enhanced_evaluator_py.html", "file": "src/analysis/enhanced_evaluator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 392, "n_excluded": 2, "n_missing": 392, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_059825b5ec61c55c___init___py": {"hash": "8f96e0487d400bed7b4c91e861310773", "index": {"url": "z_059825b5ec61c55c___init___py.html", "file": "src/analysis/fundamental/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d055e12b06a86006___init___py": {"hash": "8f96e0487d400bed7b4c91e861310773", "index": {"url": "z_d055e12b06a86006___init___py.html", "file": "src/analysis/fundamental/calculators/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d055e12b06a86006_growth_calculator_py": {"hash": "78de98710dc8b1b28e6da194367ebb96", "index": {"url": "z_d055e12b06a86006_growth_calculator_py.html", "file": "src/analysis/fundamental/calculators/growth_calculator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 114, "n_excluded": 0, "n_missing": 114, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d055e12b06a86006_pe_calculator_py": {"hash": "56b287e0c48e0fdb67f364b9770b3d9a", "index": {"url": "z_d055e12b06a86006_pe_calculator_py.html", "file": "src/analysis/fundamental/calculators/pe_calculator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 62, "n_excluded": 0, "n_missing": 62, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_059825b5ec61c55c_metrics_py": {"hash": "d886132fb6ff4ab5583f5da1e887f9b3", "index": {"url": "z_059825b5ec61c55c_metrics_py.html", "file": "src/analysis/fundamental/metrics.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 105, "n_excluded": 0, "n_missing": 105, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_60d25a6d9f4f2ff6___init___py": {"hash": "8f96e0487d400bed7b4c91e861310773", "index": {"url": "z_60d25a6d9f4f2ff6___init___py.html", "file": "src/analysis/orchestration/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_60d25a6d9f4f2ff6_analysis_orchestrator_py": {"hash": "346bef95832ab7246b5a8e4f946975b9", "index": {"url": "z_60d25a6d9f4f2ff6_analysis_orchestrator_py.html", "file": "src/analysis/orchestration/analysis_orchestrator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 204, "n_excluded": 0, "n_missing": 204, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_60d25a6d9f4f2ff6_enhancement_strategy_py": {"hash": "2530e1f979355c6308b86264b9e1c06d", "index": {"url": "z_60d25a6d9f4f2ff6_enhancement_strategy_py.html", "file": "src/analysis/orchestration/enhancement_strategy.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 48, "n_excluded": 0, "n_missing": 48, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bca56fec82a9a03a___init___py": {"hash": "47f3b642a01d6dc980a292d021f0fa1f", "index": {"url": "z_bca56fec82a9a03a___init___py.html", "file": "src/analysis/probability/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bca56fec82a9a03a_monte_carlo_simulator_py": {"hash": "87bbe6ac97609da30699621e65a05aa5", "index": {"url": "z_bca56fec82a9a03a_monte_carlo_simulator_py.html", "file": "src/analysis/probability/monte_carlo_simulator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 151, "n_excluded": 0, "n_missing": 151, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bca56fec82a9a03a_probability_engine_py": {"hash": "9dfeb771bfff1348d9e7ceb802b5c68d", "index": {"url": "z_bca56fec82a9a03a_probability_engine_py.html", "file": "src/analysis/probability/probability_engine.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 342, "n_excluded": 0, "n_missing": 342, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bca56fec82a9a03a_probability_response_service_py": {"hash": "b6e23c1efae5fae2892b5db596a1f16f", "index": {"url": "z_bca56fec82a9a03a_probability_response_service_py.html", "file": "src/analysis/probability/probability_response_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 119, "n_excluded": 0, "n_missing": 119, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_cd87d0a98354ad1b___init___py": {"hash": "8f96e0487d400bed7b4c91e861310773", "index": {"url": "z_cd87d0a98354ad1b___init___py.html", "file": "src/analysis/risk/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_cd87d0a98354ad1b_assessment_py": {"hash": "7404c7be76e93387f6e387326cebd52c", "index": {"url": "z_cd87d0a98354ad1b_assessment_py.html", "file": "src/analysis/risk/assessment.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 93, "n_excluded": 0, "n_missing": 93, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d03366fc80f3d5f7___init___py": {"hash": "8f96e0487d400bed7b4c91e861310773", "index": {"url": "z_d03366fc80f3d5f7___init___py.html", "file": "src/analysis/risk/calculators/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d03366fc80f3d5f7_beta_calculator_py": {"hash": "7922cd11141a06cbac890365b6bc9664", "index": {"url": "z_d03366fc80f3d5f7_beta_calculator_py.html", "file": "src/analysis/risk/calculators/beta_calculator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 95, "n_excluded": 0, "n_missing": 95, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d03366fc80f3d5f7_volatility_calculator_py": {"hash": "557372a29c8e4246adc81b73f2a128d8", "index": {"url": "z_d03366fc80f3d5f7_volatility_calculator_py.html", "file": "src/analysis/risk/calculators/volatility_calculator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 80, "n_excluded": 0, "n_missing": 80, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_cd87d0a98354ad1b_enhanced_risk_assessment_py": {"hash": "ab96ea69483548391aa3c3282361643e", "index": {"url": "z_cd87d0a98354ad1b_enhanced_risk_assessment_py.html", "file": "src/analysis/risk/enhanced_risk_assessment.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 216, "n_excluded": 0, "n_missing": 216, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7e16ef80c1feda06___init___py": {"hash": "8f96e0487d400bed7b4c91e861310773", "index": {"url": "z_7e16ef80c1feda06___init___py.html", "file": "src/analysis/technical/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_52b1575cbddbb4a9___init___py": {"hash": "765a6b3ad34f62fad26237f85478e156", "index": {"url": "z_52b1575cbddbb4a9___init___py.html", "file": "src/analysis/templates/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_52b1575cbddbb4a9_analysis_response_template_py": {"hash": "47144f9a1d33dee97e782f709f9e5c70", "index": {"url": "z_52b1575cbddbb4a9_analysis_response_template_py.html", "file": "src/analysis/templates/analysis_response_template.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 99, "n_excluded": 32, "n_missing": 99, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6b3b45bdd92f6a4a___init___py": {"hash": "8f96e0487d400bed7b4c91e861310773", "index": {"url": "z_6b3b45bdd92f6a4a___init___py.html", "file": "src/analysis/utils/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6b3b45bdd92f6a4a_data_validators_py": {"hash": "9e556ca2ef0839135259b333c27fea30", "index": {"url": "z_6b3b45bdd92f6a4a_data_validators_py.html", "file": "src/analysis/utils/data_validators.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 124, "n_excluded": 0, "n_missing": 124, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f0e24b7f04f99860___init___py": {"hash": "8f96e0487d400bed7b4c91e861310773", "index": {"url": "z_f0e24b7f04f99860___init___py.html", "file": "src/api/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ae5c5dec56ba18a8___init___py": {"hash": "8f96e0487d400bed7b4c91e861310773", "index": {"url": "z_ae5c5dec56ba18a8___init___py.html", "file": "src/api/analytics/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f0e24b7f04f99860_config_py": {"hash": "dba94dfc12d91f36d54cbbca78e5f0cd", "index": {"url": "z_f0e24b7f04f99860_config_py.html", "file": "src/api/config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 21, "n_excluded": 0, "n_missing": 21, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d8b1f68d7960fbdc___init___py": {"hash": "8f96e0487d400bed7b4c91e861310773", "index": {"url": "z_d8b1f68d7960fbdc___init___py.html", "file": "src/api/data/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d8b1f68d7960fbdc_cache_py": {"hash": "9d6a70d62dabd05c21230b2ffcc01909", "index": {"url": "z_d8b1f68d7960fbdc_cache_py.html", "file": "src/api/data/cache.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 179, "n_excluded": 0, "n_missing": 179, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d8b1f68d7960fbdc_cache_warming_scheduler_py": {"hash": "b1683694e56f9a7daffb83ffaa3d7bd2", "index": {"url": "z_d8b1f68d7960fbdc_cache_warming_scheduler_py.html", "file": "src/api/data/cache_warming_scheduler.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 124, "n_excluded": 0, "n_missing": 124, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d8b1f68d7960fbdc_constants_py": {"hash": "b74fbf8f74ad0aaf11b3f50f2882342c", "index": {"url": "z_d8b1f68d7960fbdc_constants_py.html", "file": "src/api/data/constants.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 6, "n_excluded": 0, "n_missing": 6, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d8b1f68d7960fbdc_market_data_service_py": {"hash": "d04880f92b224aa6a4fd2869f7d59978", "index": {"url": "z_d8b1f68d7960fbdc_market_data_service_py.html", "file": "src/api/data/market_data_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 70, "n_excluded": 0, "n_missing": 70, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d8b1f68d7960fbdc_metrics_py": {"hash": "ed40dcfb317445fddbc20a7a0c091959", "index": {"url": "z_d8b1f68d7960fbdc_metrics_py.html", "file": "src/api/data/metrics.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 209, "n_excluded": 0, "n_missing": 209, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9288a479c37533a0___init___py": {"hash": "8f96e0487d400bed7b4c91e861310773", "index": {"url": "z_9288a479c37533a0___init___py.html", "file": "src/api/data/providers/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9288a479c37533a0_alpha_vantage_py": {"hash": "7d82f1b43b85aad2f4d1929f68557391", "index": {"url": "z_9288a479c37533a0_alpha_vantage_py.html", "file": "src/api/data/providers/alpha_vantage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 95, "n_excluded": 0, "n_missing": 95, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9288a479c37533a0_base_py": {"hash": "396971b80ce4df5570e0f8fbe7074afe", "index": {"url": "z_9288a479c37533a0_base_py.html", "file": "src/api/data/providers/base.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 133, "n_excluded": 14, "n_missing": 133, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9288a479c37533a0_data_source_manager_py": {"hash": "ff3a2436721463d971cbf6b8621f3b2d", "index": {"url": "z_9288a479c37533a0_data_source_manager_py.html", "file": "src/api/data/providers/data_source_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 139, "n_excluded": 0, "n_missing": 139, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9288a479c37533a0_finnhub_py": {"hash": "69d0d4dbeec99c4a054350d23779df01", "index": {"url": "z_9288a479c37533a0_finnhub_py.html", "file": "src/api/data/providers/finnhub.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 94, "n_excluded": 0, "n_missing": 94, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6051117ca093f979___init___py": {"hash": "56d026d9c5ec4e324954d5ea6247f97b", "index": {"url": "z_6051117ca093f979___init___py.html", "file": "src/api/data/providers/modules/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 5, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6051117ca093f979_auditing_py": {"hash": "1c76ea481c8deb8be2587477578ef59c", "index": {"url": "z_6051117ca093f979_auditing_py.html", "file": "src/api/data/providers/modules/auditing.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 59, "n_excluded": 0, "n_missing": 59, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6051117ca093f979_config_py": {"hash": "eb6b2fd3cc29a2add86e8913d333ec74", "index": {"url": "z_6051117ca093f979_config_py.html", "file": "src/api/data/providers/modules/config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 83, "n_excluded": 0, "n_missing": 83, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6051117ca093f979_rate_limiting_py": {"hash": "9bddc9b8a308221bd364183466c17c27", "index": {"url": "z_6051117ca093f979_rate_limiting_py.html", "file": "src/api/data/providers/modules/rate_limiting.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 73, "n_excluded": 0, "n_missing": 73, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6051117ca093f979_validation_py": {"hash": "4ac6637b2036083e3a1301caa65ad795", "index": {"url": "z_6051117ca093f979_validation_py.html", "file": "src/api/data/providers/modules/validation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 168, "n_excluded": 0, "n_missing": 168, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9288a479c37533a0_polygon_py": {"hash": "********************************", "index": {"url": "z_9288a479c37533a0_polygon_py.html", "file": "src/api/data/providers/polygon.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 167, "n_excluded": 0, "n_missing": 167, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d8b1f68d7960fbdc_scheduled_tasks_py": {"hash": "c6aa3a9c1acc2d6d0a69b90bf4d41c47", "index": {"url": "z_d8b1f68d7960fbdc_scheduled_tasks_py.html", "file": "src/api/data/scheduled_tasks.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 132, "n_excluded": 0, "n_missing": 132, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f0e24b7f04f99860_main_py": {"hash": "e8baca83dc353c88881ce85ffaac5eb2", "index": {"url": "z_f0e24b7f04f99860_main_py.html", "file": "src/api/main.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 58, "n_excluded": 5, "n_missing": 58, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8868f58259af6c17___init___py": {"hash": "8f96e0487d400bed7b4c91e861310773", "index": {"url": "z_8868f58259af6c17___init___py.html", "file": "src/api/middleware/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8868f58259af6c17_advanced_security_py": {"hash": "ad757213f59989f73f31a9a5295cff5f", "index": {"url": "z_8868f58259af6c17_advanced_security_py.html", "file": "src/api/middleware/advanced_security.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 225, "n_excluded": 0, "n_missing": 225, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8868f58259af6c17_security_py": {"hash": "17df612ba8d201c181f9db50b6a07b8a", "index": {"url": "z_8868f58259af6c17_security_py.html", "file": "src/api/middleware/security.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 102, "n_excluded": 0, "n_missing": 102, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8868f58259af6c17_security_utils_py": {"hash": "0c5b289de863146c209c9bd9501733c9", "index": {"url": "z_8868f58259af6c17_security_utils_py.html", "file": "src/api/middleware/security_utils.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 35, "n_excluded": 0, "n_missing": 35, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5d636249625465ac___init___py": {"hash": "8f96e0487d400bed7b4c91e861310773", "index": {"url": "z_5d636249625465ac___init___py.html", "file": "src/api/routers/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5d636249625465ac_market_data_py": {"hash": "d86af9b6a945b77195b8ba9c5bb748b3", "index": {"url": "z_5d636249625465ac_market_data_py.html", "file": "src/api/routers/market_data.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 65, "n_excluded": 0, "n_missing": 65, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6f57f923ae69d111___init___py": {"hash": "8f96e0487d400bed7b4c91e861310773", "index": {"url": "z_6f57f923ae69d111___init___py.html", "file": "src/api/routes/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6f57f923ae69d111_analytics_py": {"hash": "8b23d40d97a7e6742be4c73898c9bd85", "index": {"url": "z_6f57f923ae69d111_analytics_py.html", "file": "src/api/routes/analytics.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 151, "n_excluded": 0, "n_missing": 151, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6f57f923ae69d111_bot_health_py": {"hash": "70eb3e1ee00b3db0ffa59dca23d0b413", "index": {"url": "z_6f57f923ae69d111_bot_health_py.html", "file": "src/api/routes/bot_health.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 107, "n_excluded": 0, "n_missing": 107, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6f57f923ae69d111_dashboard_py": {"hash": "54c83004b22f0c227a644d07cdbb6c77", "index": {"url": "z_6f57f923ae69d111_dashboard_py.html", "file": "src/api/routes/dashboard.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 57, "n_excluded": 0, "n_missing": 57, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6f57f923ae69d111_debug_py": {"hash": "f1816be0c2289837c2346b16fd7be729", "index": {"url": "z_6f57f923ae69d111_debug_py.html", "file": "src/api/routes/debug.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 35, "n_excluded": 0, "n_missing": 35, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6f57f923ae69d111_feedback_py": {"hash": "0e6f79ed0fb8343c8398fc09794e8030", "index": {"url": "z_6f57f923ae69d111_feedback_py.html", "file": "src/api/routes/feedback.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 26, "n_excluded": 0, "n_missing": 26, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6f57f923ae69d111_health_py": {"hash": "57b7dfb1d38c157687fe4be5031cbc49", "index": {"url": "z_6f57f923ae69d111_health_py.html", "file": "src/api/routes/health.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 77, "n_excluded": 0, "n_missing": 77, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6f57f923ae69d111_market_data_py": {"hash": "9163b76049a938323276a7cb3d3f7fdc", "index": {"url": "z_6f57f923ae69d111_market_data_py.html", "file": "src/api/routes/market_data.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 113, "n_excluded": 0, "n_missing": 113, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6f57f923ae69d111_metrics_py": {"hash": "e1c51a2f6b17cdf88e6dc56114b05ee7", "index": {"url": "z_6f57f923ae69d111_metrics_py.html", "file": "src/api/routes/metrics.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 20, "n_excluded": 0, "n_missing": 20, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9ab2fed7f6b8459c___init___py": {"hash": "8f96e0487d400bed7b4c91e861310773", "index": {"url": "z_9ab2fed7f6b8459c___init___py.html", "file": "src/api/schemas/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9ab2fed7f6b8459c_feedback_schema_py": {"hash": "7bd76b2e60769cc724d29db845e4c62c", "index": {"url": "z_9ab2fed7f6b8459c_feedback_schema_py.html", "file": "src/api/schemas/feedback_schema.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 25, "n_excluded": 0, "n_missing": 25, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9ab2fed7f6b8459c_metrics_schema_py": {"hash": "bcc28dc694c9be59d49edeba7a7f2c88", "index": {"url": "z_9ab2fed7f6b8459c_metrics_schema_py.html", "file": "src/api/schemas/metrics_schema.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 11, "n_excluded": 0, "n_missing": 11, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2839512a6d00c75b___init___py": {"hash": "8f96e0487d400bed7b4c91e861310773", "index": {"url": "z_2839512a6d00c75b___init___py.html", "file": "src/api/webhooks/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_93b1c7bd489fca84___init___py": {"hash": "e6baa73cda2916dad605215f937a92e1", "index": {"url": "z_93b1c7bd489fca84___init___py.html", "file": "src/bot/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_06e375abcc969e3f___init___py": {"hash": "83885ad4692a46c168983e579121ae8c", "index": {"url": "z_06e375abcc969e3f___init___py.html", "file": "src/bot/audit/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 6, "n_excluded": 0, "n_missing": 6, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_06e375abcc969e3f_audit_service_py": {"hash": "bc97ed53d11b53e79681b10f53ccfc01", "index": {"url": "z_06e375abcc969e3f_audit_service_py.html", "file": "src/bot/audit/audit_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 179, "n_excluded": 0, "n_missing": 179, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_06e375abcc969e3f_example_usage_py": {"hash": "74ed128002a2b8800a3c2a801a0908ae", "index": {"url": "z_06e375abcc969e3f_example_usage_py.html", "file": "src/bot/audit/example_usage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 42, "n_excluded": 10, "n_missing": 42, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_06e375abcc969e3f_rate_limiter_py": {"hash": "d5584d473023073f6ff4ece1c4d6b1d6", "index": {"url": "z_06e375abcc969e3f_rate_limiter_py.html", "file": "src/bot/audit/rate_limiter.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 215, "n_excluded": 0, "n_missing": 215, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_06e375abcc969e3f_request_visualizer_py": {"hash": "3969f310a404e31d8d780cc9fb9b9efc", "index": {"url": "z_06e375abcc969e3f_request_visualizer_py.html", "file": "src/bot/audit/request_visualizer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 145, "n_excluded": 0, "n_missing": 145, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_06e375abcc969e3f_session_manager_py": {"hash": "fd91a71dfc904a695ede02a63968af69", "index": {"url": "z_06e375abcc969e3f_session_manager_py.html", "file": "src/bot/audit/session_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 171, "n_excluded": 0, "n_missing": 171, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_06e375abcc969e3f_setup_audit_py": {"hash": "693f9165c63300f3674d719e8a4897a0", "index": {"url": "z_06e375abcc969e3f_setup_audit_py.html", "file": "src/bot/audit/setup_audit.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 94, "n_excluded": 0, "n_missing": 94, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_93b1c7bd489fca84_client_py": {"hash": "481cf4866b2b1c7ed66fc2dec826c301", "index": {"url": "z_93b1c7bd489fca84_client_py.html", "file": "src/bot/client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 5, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_93b1c7bd489fca84_client_audit_integration_py": {"hash": "f7b84e8c5db77a1f1d986ce950a7ea72", "index": {"url": "z_93b1c7bd489fca84_client_audit_integration_py.html", "file": "src/bot/client_audit_integration.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 76, "n_excluded": 0, "n_missing": 76, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_93b1c7bd489fca84_client_with_monitoring_py": {"hash": "8a1166c9694ee174bf50309733738b78", "index": {"url": "z_93b1c7bd489fca84_client_with_monitoring_py.html", "file": "src/bot/client_with_monitoring.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 132, "n_excluded": 0, "n_missing": 132, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a14d6168cd383d17___init___py": {"hash": "e6baa73cda2916dad605215f937a92e1", "index": {"url": "z_a14d6168cd383d17___init___py.html", "file": "src/bot/core/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a14d6168cd383d17_bot_py": {"hash": "08fd5547781f5b80543614a182ceb515", "index": {"url": "z_a14d6168cd383d17_bot_py.html", "file": "src/bot/core/bot.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 110, "n_excluded": 2, "n_missing": 110, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a14d6168cd383d17_error_handler_py": {"hash": "ab520b4e59e4d39418d3298b6ca2545a", "index": {"url": "z_a14d6168cd383d17_error_handler_py.html", "file": "src/bot/core/error_handler.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 18, "n_excluded": 0, "n_missing": 15, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7ac26b0fd80cd4a6___init___py": {"hash": "bb8f1781420ad48f896b23dfa1e27e0d", "index": {"url": "z_7ac26b0fd80cd4a6___init___py.html", "file": "src/bot/core/security/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 5, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7ac26b0fd80cd4a6_auth_py": {"hash": "5e1f0929bbf426599231fae0885a5c92", "index": {"url": "z_7ac26b0fd80cd4a6_auth_py.html", "file": "src/bot/core/security/auth.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 52, "n_excluded": 0, "n_missing": 52, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7ac26b0fd80cd4a6_input_validator_py": {"hash": "d504b540ce612589712919fe867144ee", "index": {"url": "z_7ac26b0fd80cd4a6_input_validator_py.html", "file": "src/bot/core/security/input_validator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 34, "n_excluded": 0, "n_missing": 34, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7ac26b0fd80cd4a6_rate_limiter_py": {"hash": "f7365064541a123d23973d9219010423", "index": {"url": "z_7ac26b0fd80cd4a6_rate_limiter_py.html", "file": "src/bot/core/security/rate_limiter.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 45, "n_excluded": 0, "n_missing": 45, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7ac26b0fd80cd4a6_security_scanner_py": {"hash": "70f7f44f10cacef8b9e376bdceda197e", "index": {"url": "z_7ac26b0fd80cd4a6_security_scanner_py.html", "file": "src/bot/core/security/security_scanner.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 44, "n_excluded": 0, "n_missing": 44, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7ac26b0fd80cd4a6_simplified_security_py": {"hash": "23e1219d38222acce8623c12c778f5f5", "index": {"url": "z_7ac26b0fd80cd4a6_simplified_security_py.html", "file": "src/bot/core/security/simplified_security.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 44, "n_excluded": 0, "n_missing": 44, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a14d6168cd383d17_services_py": {"hash": "9dd08304c965dcc2d032f5267966019e", "index": {"url": "z_a14d6168cd383d17_services_py.html", "file": "src/bot/core/services.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 152, "n_excluded": 0, "n_missing": 152, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_93b1c7bd489fca84_database_manager_py": {"hash": "63db31e23fdae7b140c4f9eb7253120c", "index": {"url": "z_93b1c7bd489fca84_database_manager_py.html", "file": "src/bot/database_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 72, "n_excluded": 0, "n_missing": 45, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8b651bcae9720a9b___init___py": {"hash": "2a72f34142844c4f5e64c96b2735fbf6", "index": {"url": "z_8b651bcae9720a9b___init___py.html", "file": "src/bot/extensions/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8b651bcae9720a9b_alerts_py": {"hash": "9f5b9b0b07651d3e465c2651807387e8", "index": {"url": "z_8b651bcae9720a9b_alerts_py.html", "file": "src/bot/extensions/alerts.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 249, "n_excluded": 0, "n_missing": 249, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8b651bcae9720a9b_analyze_py": {"hash": "c6ff47a3084e22db327a4f9017f00e61", "index": {"url": "z_8b651bcae9720a9b_analyze_py.html", "file": "src/bot/extensions/analyze.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 260, "n_excluded": 0, "n_missing": 176, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8b651bcae9720a9b_ask_py": {"hash": "af0b617fe751d2097514e2eb0e1ab61e", "index": {"url": "z_8b651bcae9720a9b_ask_py.html", "file": "src/bot/extensions/ask.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 121, "n_excluded": 0, "n_missing": 76, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8b651bcae9720a9b_audit_py": {"hash": "cc7bda7cbd109d2086a9626aaf12d9b4", "index": {"url": "z_8b651bcae9720a9b_audit_py.html", "file": "src/bot/extensions/audit.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 102, "n_excluded": 0, "n_missing": 102, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8b651bcae9720a9b_batch_analyze_py": {"hash": "94e45ca043b1978184062235467bf156", "index": {"url": "z_8b651bcae9720a9b_batch_analyze_py.html", "file": "src/bot/extensions/batch_analyze.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 250, "n_excluded": 0, "n_missing": 250, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8b651bcae9720a9b_discord_ux_py": {"hash": "b6b5f9d1e3a6ac5ae4cbf5eeb82ad808", "index": {"url": "z_8b651bcae9720a9b_discord_ux_py.html", "file": "src/bot/extensions/discord_ux.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8b651bcae9720a9b_error_handler_py": {"hash": "0113e3489c078888aa6f664b9f91350b", "index": {"url": "z_8b651bcae9720a9b_error_handler_py.html", "file": "src/bot/extensions/error_handler.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 48, "n_excluded": 0, "n_missing": 48, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8b651bcae9720a9b_help_py": {"hash": "d57dc055a226f72de569d9a62e32ae5f", "index": {"url": "z_8b651bcae9720a9b_help_py.html", "file": "src/bot/extensions/help.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 168, "n_excluded": 0, "n_missing": 168, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8b651bcae9720a9b_ml_admin_py": {"hash": "6618d5346c239034de0698917814bc4e", "index": {"url": "z_8b651bcae9720a9b_ml_admin_py.html", "file": "src/bot/extensions/ml_admin.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 139, "n_excluded": 0, "n_missing": 139, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8b651bcae9720a9b_performance_monitor_py": {"hash": "300cea62778837b0b16688ce0cc7e99c", "index": {"url": "z_8b651bcae9720a9b_performance_monitor_py.html", "file": "src/bot/extensions/performance_monitor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 284, "n_excluded": 0, "n_missing": 284, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8b651bcae9720a9b_pipeline_visualizer_py": {"hash": "9e746c95ac26db761ddf83b9d50d8b98", "index": {"url": "z_8b651bcae9720a9b_pipeline_visualizer_py.html", "file": "src/bot/extensions/pipeline_visualizer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8b651bcae9720a9b_portfolio_py": {"hash": "b493f2893a152955d4c0ed184b7dd4e5", "index": {"url": "z_8b651bcae9720a9b_portfolio_py.html", "file": "src/bot/extensions/portfolio.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 488, "n_excluded": 0, "n_missing": 488, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8b651bcae9720a9b_recommendations_py": {"hash": "d6ab8be8879001ea924cb1dc2bd3eced", "index": {"url": "z_8b651bcae9720a9b_recommendations_py.html", "file": "src/bot/extensions/recommendations.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 256, "n_excluded": 0, "n_missing": 256, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8b651bcae9720a9b_status_py": {"hash": "c46108f1eee18db581b0de9c8e843a61", "index": {"url": "z_8b651bcae9720a9b_status_py.html", "file": "src/bot/extensions/status.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 88, "n_excluded": 0, "n_missing": 88, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8b651bcae9720a9b_utility_py": {"hash": "85f18ce765d293c8348f9fa0471b83d6", "index": {"url": "z_8b651bcae9720a9b_utility_py.html", "file": "src/bot/extensions/utility.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 134, "n_excluded": 0, "n_missing": 65, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8b651bcae9720a9b_watchlist_py": {"hash": "47865641b2d20a3ccd006892d2aec449", "index": {"url": "z_8b651bcae9720a9b_watchlist_py.html", "file": "src/bot/extensions/watchlist.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 286, "n_excluded": 0, "n_missing": 286, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8b651bcae9720a9b_zones_py": {"hash": "51d7416b035a341b84947119969aa661", "index": {"url": "z_8b651bcae9720a9b_zones_py.html", "file": "src/bot/extensions/zones.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 263, "n_excluded": 0, "n_missing": 263, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_93b1c7bd489fca84_main_py": {"hash": "5df53f4c8c32d418bfd70c9d10fc14a0", "index": {"url": "z_93b1c7bd489fca84_main_py.html", "file": "src/bot/main.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 136, "n_excluded": 6, "n_missing": 136, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_93b1c7bd489fca84_market_sentiment_analyzer_py": {"hash": "16efe9f82a1b881ad115d7f6ff8b8c58", "index": {"url": "z_93b1c7bd489fca84_market_sentiment_analyzer_py.html", "file": "src/bot/market_sentiment_analyzer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 171, "n_excluded": 0, "n_missing": 171, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_93b1c7bd489fca84_metrics_collector_py": {"hash": "9a4187170cf6a7896ef7f5a7c6e62f5a", "index": {"url": "z_93b1c7bd489fca84_metrics_collector_py.html", "file": "src/bot/metrics_collector.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 123, "n_excluded": 0, "n_missing": 123, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_93b1c7bd489fca84_performance_analytics_py": {"hash": "7248e8b935e63fa7c7b18e1a73d3cdca", "index": {"url": "z_93b1c7bd489fca84_performance_analytics_py.html", "file": "src/bot/performance_analytics.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 99, "n_excluded": 0, "n_missing": 99, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_93b1c7bd489fca84_permissions_py": {"hash": "e84285c400d505ddc3668b30685abc3a", "index": {"url": "z_93b1c7bd489fca84_permissions_py.html", "file": "src/bot/permissions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 84, "n_excluded": 0, "n_missing": 60, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_fddd24340cee9d05___init___py": {"hash": "e6baa73cda2916dad605215f937a92e1", "index": {"url": "z_fddd24340cee9d05___init___py.html", "file": "src/bot/pipeline/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_334a150c392fe995___init___py": {"hash": "e6baa73cda2916dad605215f937a92e1", "index": {"url": "z_334a150c392fe995___init___py.html", "file": "src/bot/pipeline/commands/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_18831fe259ea92e4___init___py": {"hash": "e6baa73cda2916dad605215f937a92e1", "index": {"url": "z_18831fe259ea92e4___init___py.html", "file": "src/bot/pipeline/commands/ask/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8c0c370aaec88e18___init___py": {"hash": "2335248d9daa50cf1d8f87f980a7723f", "index": {"url": "z_8c0c370aaec88e18___init___py.html", "file": "src/bot/pipeline/commands/ask/api/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 5, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8c0c370aaec88e18_backward_compatibility_py": {"hash": "d85d225b11197fa3d779fa9302dffc8f", "index": {"url": "z_8c0c370aaec88e18_backward_compatibility_py.html", "file": "src/bot/pipeline/commands/ask/api/backward_compatibility.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 260, "n_excluded": 0, "n_missing": 260, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8c0c370aaec88e18_contracts_py": {"hash": "201458674a04aeac15776aeb22d67b94", "index": {"url": "z_8c0c370aaec88e18_contracts_py.html", "file": "src/bot/pipeline/commands/ask/api/contracts.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 220, "n_excluded": 0, "n_missing": 220, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8c0c370aaec88e18_integration_py": {"hash": "658e88523792ac3202b02fb9f86ca4d8", "index": {"url": "z_8c0c370aaec88e18_integration_py.html", "file": "src/bot/pipeline/commands/ask/api/integration.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 363, "n_excluded": 12, "n_missing": 363, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8c0c370aaec88e18_versioning_py": {"hash": "36fbfac0cc1cad490c7d2ca78f7dab5a", "index": {"url": "z_8c0c370aaec88e18_versioning_py.html", "file": "src/bot/pipeline/commands/ask/api/versioning.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 208, "n_excluded": 0, "n_missing": 208, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_18831fe259ea92e4_audit_py": {"hash": "4e2a3f2ae07b605c97dd503700fcf6fa", "index": {"url": "z_18831fe259ea92e4_audit_py.html", "file": "src/bot/pipeline/commands/ask/audit.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_12806b501f857678___init___py": {"hash": "ed9ff2fffa8028ac7481d39c224a7112", "index": {"url": "z_12806b501f857678___init___py.html", "file": "src/bot/pipeline/commands/ask/audit/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_12806b501f857678_audit_logger_py": {"hash": "ed773193b09c0e4d044e11c63526bcb7", "index": {"url": "z_12806b501f857678_audit_logger_py.html", "file": "src/bot/pipeline/commands/ask/audit/audit_logger.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 193, "n_excluded": 0, "n_missing": 111, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_3a43e718a1f310ec___init___py": {"hash": "1c7a2105a1a27987076e864cb0fe654d", "index": {"url": "z_3a43e718a1f310ec___init___py.html", "file": "src/bot/pipeline/commands/ask/cache/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 34, "n_excluded": 0, "n_missing": 28, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_3a43e718a1f310ec_intelligent_cache_py": {"hash": "42496797607fb94bd38479d3ef0ab22d", "index": {"url": "z_3a43e718a1f310ec_intelligent_cache_py.html", "file": "src/bot/pipeline/commands/ask/cache/intelligent_cache.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 175, "n_excluded": 0, "n_missing": 124, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_3a43e718a1f310ec_unified_cache_py": {"hash": "b578375e6860a9d09b152d4ac8d8c988", "index": {"url": "z_3a43e718a1f310ec_unified_cache_py.html", "file": "src/bot/pipeline/commands/ask/cache/unified_cache.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 142, "n_excluded": 0, "n_missing": 111, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2991336f3a420345___init___py": {"hash": "8d925824b04dc0bfdcae910c4ea30728", "index": {"url": "z_2991336f3a420345___init___py.html", "file": "src/bot/pipeline/commands/ask/cleanup/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2991336f3a420345_dead_code_analyzer_py": {"hash": "1df4193cef0df0e45322103a5aed914f", "index": {"url": "z_2991336f3a420345_dead_code_analyzer_py.html", "file": "src/bot/pipeline/commands/ask/cleanup/dead_code_analyzer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 209, "n_excluded": 0, "n_missing": 209, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2991336f3a420345_legacy_archiver_py": {"hash": "d44a5e678363a02a62ff3bd9973438eb", "index": {"url": "z_2991336f3a420345_legacy_archiver_py.html", "file": "src/bot/pipeline/commands/ask/cleanup/legacy_archiver.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 165, "n_excluded": 0, "n_missing": 165, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2297a8013ac870ab___init___py": {"hash": "cbc18e8bddd9b90265e560d84b17f171", "index": {"url": "z_2297a8013ac870ab___init___py.html", "file": "src/bot/pipeline/commands/ask/compliance/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 5, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2297a8013ac870ab_audit_logger_py": {"hash": "ce0dc3c494602c1c478fd53216987958", "index": {"url": "z_2297a8013ac870ab_audit_logger_py.html", "file": "src/bot/pipeline/commands/ask/compliance/audit_logger.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 286, "n_excluded": 0, "n_missing": 286, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2297a8013ac870ab_data_manager_py": {"hash": "f439962d0192ddb1bd923167d2cb10e3", "index": {"url": "z_2297a8013ac870ab_data_manager_py.html", "file": "src/bot/pipeline/commands/ask/compliance/data_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 47, "n_excluded": 0, "n_missing": 47, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f24854da83285069___init___py": {"hash": "cb03f9e3eeaff896dcae23b763a7a5fa", "index": {"url": "z_f24854da83285069___init___py.html", "file": "src/bot/pipeline/commands/ask/config/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 8, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f24854da83285069_ask_config_py": {"hash": "dda5e92384d1afc07ae872b132f0bafd", "index": {"url": "z_f24854da83285069_ask_config_py.html", "file": "src/bot/pipeline/commands/ask/config/ask_config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 316, "n_excluded": 0, "n_missing": 198, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f24854da83285069_config_manager_py": {"hash": "5d696c568c1fce2a65da48dcc893ccb2", "index": {"url": "z_f24854da83285069_config_manager_py.html", "file": "src/bot/pipeline/commands/ask/config/config_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 279, "n_excluded": 0, "n_missing": 200, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f24854da83285069_environment_profiles_py": {"hash": "9a5fe92b0d9d4d22daa2744b74d5c55e", "index": {"url": "z_f24854da83285069_environment_profiles_py.html", "file": "src/bot/pipeline/commands/ask/config/environment_profiles.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 373, "n_excluded": 0, "n_missing": 280, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f24854da83285069_feature_flags_py": {"hash": "c528d4dfa774b15d632186dbce210955", "index": {"url": "z_f24854da83285069_feature_flags_py.html", "file": "src/bot/pipeline/commands/ask/config/feature_flags.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 46, "n_excluded": 0, "n_missing": 21, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f24854da83285069_secrets_manager_py": {"hash": "59f0a120423f2c5d6cde34246abe85f4", "index": {"url": "z_f24854da83285069_secrets_manager_py.html", "file": "src/bot/pipeline/commands/ask/config/secrets_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 171, "n_excluded": 0, "n_missing": 119, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7871925a5b1f2799___init___py": {"hash": "04447878ea8788484aff148655cead9e", "index": {"url": "z_7871925a5b1f2799___init___py.html", "file": "src/bot/pipeline/commands/ask/core/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 6, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7871925a5b1f2799_controller_py": {"hash": "fed020627fa2745e4fd314e1c50b0a36", "index": {"url": "z_7871925a5b1f2799_controller_py.html", "file": "src/bot/pipeline/commands/ask/core/controller.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 90, "n_excluded": 0, "n_missing": 70, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7871925a5b1f2799_error_coordinator_py": {"hash": "cf2ae61d70da8c3df3a94f6213c81f2f", "index": {"url": "z_7871925a5b1f2799_error_coordinator_py.html", "file": "src/bot/pipeline/commands/ask/core/error_coordinator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 226, "n_excluded": 0, "n_missing": 177, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7871925a5b1f2799_models_py": {"hash": "82d9f68e510b3e44a201d87f7ce60182", "index": {"url": "z_7871925a5b1f2799_models_py.html", "file": "src/bot/pipeline/commands/ask/core/models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 41, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7871925a5b1f2799_stage_executor_py": {"hash": "894691359484cdcb106316c7038f0bc3", "index": {"url": "z_7871925a5b1f2799_stage_executor_py.html", "file": "src/bot/pipeline/commands/ask/core/stage_executor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 134, "n_excluded": 0, "n_missing": 120, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7871925a5b1f2799_stage_manager_py": {"hash": "88df431dbf5ea0cb9adccfe81f7ae744", "index": {"url": "z_7871925a5b1f2799_stage_manager_py.html", "file": "src/bot/pipeline/commands/ask/core/stage_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 30, "n_excluded": 0, "n_missing": 11, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_060a22be0368d7a2___init___py": {"hash": "978ed78bbfb6ddfc6aa8e55dd05a5dfe", "index": {"url": "z_060a22be0368d7a2___init___py.html", "file": "src/bot/pipeline/commands/ask/cost/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 2, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_060a22be0368d7a2_cost_tracker_py": {"hash": "9956891d99cd74956053b4fd1d7577ae", "index": {"url": "z_060a22be0368d7a2_cost_tracker_py.html", "file": "src/bot/pipeline/commands/ask/cost/cost_tracker.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 256, "n_excluded": 0, "n_missing": 256, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_060a22be0368d7a2_resource_optimizer_py": {"hash": "c9c62e1ea4f8bf81e374be59a1035930", "index": {"url": "z_060a22be0368d7a2_resource_optimizer_py.html", "file": "src/bot/pipeline/commands/ask/cost/resource_optimizer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 41, "n_excluded": 0, "n_missing": 41, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a2a8fd9815ec5b3a___init___py": {"hash": "e2eea36485874f1a79c5f596a56e6673", "index": {"url": "z_a2a8fd9815ec5b3a___init___py.html", "file": "src/bot/pipeline/commands/ask/deployment/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a2a8fd9815ec5b3a_cicd_pipeline_py": {"hash": "c6b4c9440270631c9f7dbfab436723ea", "index": {"url": "z_a2a8fd9815ec5b3a_cicd_pipeline_py.html", "file": "src/bot/pipeline/commands/ask/deployment/cicd_pipeline.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 21, "n_excluded": 0, "n_missing": 21, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_be7e545879d6abeb___init___py": {"hash": "66e4958fd02986d02305fb617e245ef7", "index": {"url": "z_be7e545879d6abeb___init___py.html", "file": "src/bot/pipeline/commands/ask/errors/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_be7e545879d6abeb_error_handler_py": {"hash": "caa16835161ca0ef537e49f0d846f1fd", "index": {"url": "z_be7e545879d6abeb_error_handler_py.html", "file": "src/bot/pipeline/commands/ask/errors/error_handler.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 1, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_be7e545879d6abeb_error_manager_py": {"hash": "c3aa75aca18f084ea72eece1f2849ad3", "index": {"url": "z_be7e545879d6abeb_error_manager_py.html", "file": "src/bot/pipeline/commands/ask/errors/error_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 158, "n_excluded": 0, "n_missing": 158, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_be7e545879d6abeb_fallback_strategy_py": {"hash": "6386ddef8ad2bea481e200dc3c6debaf", "index": {"url": "z_be7e545879d6abeb_fallback_strategy_py.html", "file": "src/bot/pipeline/commands/ask/errors/fallback_strategy.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 107, "n_excluded": 0, "n_missing": 107, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_18831fe259ea92e4_executor_py": {"hash": "8a62c25e9043884e61cf0bc0ed69c898", "index": {"url": "z_18831fe259ea92e4_executor_py.html", "file": "src/bot/pipeline/commands/ask/executor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 42, "n_excluded": 0, "n_missing": 27, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2ab3a6815f15944e___init___py": {"hash": "d80c06d0036929ce3bf20a11478d70f0", "index": {"url": "z_2ab3a6815f15944e___init___py.html", "file": "src/bot/pipeline/commands/ask/observability/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 35, "n_excluded": 0, "n_missing": 19, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2ab3a6815f15944e_health_checker_py": {"hash": "17fd6164493ac45f0ec1cf5d5e4e3175", "index": {"url": "z_2ab3a6815f15944e_health_checker_py.html", "file": "src/bot/pipeline/commands/ask/observability/health_checker.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 147, "n_excluded": 0, "n_missing": 89, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2ab3a6815f15944e_log_analyzer_py": {"hash": "d7a1edb4a0f3e29525b64900a06ad0d0", "index": {"url": "z_2ab3a6815f15944e_log_analyzer_py.html", "file": "src/bot/pipeline/commands/ask/observability/log_analyzer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 197, "n_excluded": 0, "n_missing": 127, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ab68463ebc69ca4f___init___py": {"hash": "8f96e0487d400bed7b4c91e861310773", "index": {"url": "z_ab68463ebc69ca4f___init___py.html", "file": "src/bot/pipeline/commands/ask/performance/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_18831fe259ea92e4_pipeline_py": {"hash": "ee78d31358171c617f62068606961551", "index": {"url": "z_18831fe259ea92e4_pipeline_py.html", "file": "src/bot/pipeline/commands/ask/pipeline.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 7, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ef1425a604312b50___init___py": {"hash": "8f96e0487d400bed7b4c91e861310773", "index": {"url": "z_ef1425a604312b50___init___py.html", "file": "src/bot/pipeline/commands/ask/quality/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_72457536bcd71851___init___py": {"hash": "8f96e0487d400bed7b4c91e861310773", "index": {"url": "z_72457536bcd71851___init___py.html", "file": "src/bot/pipeline/commands/ask/security/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_72457536bcd71851_security_manager_py": {"hash": "27460c78305cd2125644063032dc7ff6", "index": {"url": "z_72457536bcd71851_security_manager_py.html", "file": "src/bot/pipeline/commands/ask/security/security_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 198, "n_excluded": 0, "n_missing": 198, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6b708e61bbf6f9e2___init___py": {"hash": "bd9154796d937d000ea7f89389e13251", "index": {"url": "z_6b708e61bbf6f9e2___init___py.html", "file": "src/bot/pipeline/commands/ask/tools/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 2, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6b708e61bbf6f9e2_mcp_manager_py": {"hash": "454620c418eb20ae309cc027e0e1bc91", "index": {"url": "z_6b708e61bbf6f9e2_mcp_manager_py.html", "file": "src/bot/pipeline/commands/ask/tools/mcp_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 353, "n_excluded": 0, "n_missing": 353, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1f4b72d24847ac97___init___py": {"hash": "8f96e0487d400bed7b4c91e861310773", "index": {"url": "z_1f4b72d24847ac97___init___py.html", "file": "src/bot/pipeline/core/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1f4b72d24847ac97_context_manager_py": {"hash": "73d2b26a289fb30e1d56a594b7b7e06e", "index": {"url": "z_1f4b72d24847ac97_context_manager_py.html", "file": "src/bot/pipeline/core/context_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 24, "n_excluded": 0, "n_missing": 24, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_93b1c7bd489fca84_pipeline_framework_py": {"hash": "3dc07088064aa65ae3a37ee6a1741d4b", "index": {"url": "z_93b1c7bd489fca84_pipeline_framework_py.html", "file": "src/bot/pipeline_framework.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 127, "n_excluded": 4, "n_missing": 127, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_93b1c7bd489fca84_rate_limiter_py": {"hash": "5f023d5fbddc35025b8d913592604188", "index": {"url": "z_93b1c7bd489fca84_rate_limiter_py.html", "file": "src/bot/rate_limiter.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 81, "n_excluded": 0, "n_missing": 81, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_93b1c7bd489fca84_real_time_data_stream_py": {"hash": "f53693ff72bd7f38cc0ffe40810f2098", "index": {"url": "z_93b1c7bd489fca84_real_time_data_stream_py.html", "file": "src/bot/real_time_data_stream.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 187, "n_excluded": 0, "n_missing": 187, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_93b1c7bd489fca84_risk_management_system_py": {"hash": "a275c07e40b6cc249b142bc1f697869d", "index": {"url": "z_93b1c7bd489fca84_risk_management_system_py.html", "file": "src/bot/risk_management_system.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 176, "n_excluded": 0, "n_missing": 176, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_93b1c7bd489fca84_setup_audit_py": {"hash": "941daee6a7d9f969f2648e3febb2f150", "index": {"url": "z_93b1c7bd489fca84_setup_audit_py.html", "file": "src/bot/setup_audit.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 185, "n_excluded": 0, "n_missing": 185, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_93b1c7bd489fca84_start_bot_py": {"hash": "3df29016d7d7b15632c0eb7d4a49e3a8", "index": {"url": "z_93b1c7bd489fca84_start_bot_py.html", "file": "src/bot/start_bot.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 32, "n_excluded": 4, "n_missing": 32, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_93b1c7bd489fca84_update_imports_py": {"hash": "ac84ae46ddaf841eb2effb0a078663e9", "index": {"url": "z_93b1c7bd489fca84_update_imports_py.html", "file": "src/bot/update_imports.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_93b1c7bd489fca84_watchlist_alerts_py": {"hash": "248ed2a9a344b41d3fba968b97a2d159", "index": {"url": "z_93b1c7bd489fca84_watchlist_alerts_py.html", "file": "src/bot/watchlist_alerts.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 11, "n_excluded": 0, "n_missing": 11, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0618756b1ff51bca___init___py": {"hash": "e6baa73cda2916dad605215f937a92e1", "index": {"url": "z_0618756b1ff51bca___init___py.html", "file": "src/core/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0618756b1ff51bca_advanced_config_manager_py": {"hash": "63188c6837620a098aa09005c0822e1f", "index": {"url": "z_0618756b1ff51bca_advanced_config_manager_py.html", "file": "src/core/advanced_config_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 280, "n_excluded": 0, "n_missing": 280, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8b591a4c69fc2395___init___py": {"hash": "8f96e0487d400bed7b4c91e861310773", "index": {"url": "z_8b591a4c69fc2395___init___py.html", "file": "src/core/automation/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8b591a4c69fc2395_analysis_scheduler_py": {"hash": "fbeafdbcb7fe6be90b45e8c843ecfb4f", "index": {"url": "z_8b591a4c69fc2395_analysis_scheduler_py.html", "file": "src/core/automation/analysis_scheduler.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 266, "n_excluded": 0, "n_missing": 266, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8b591a4c69fc2395_discord_handler_py": {"hash": "7d504f8b6c599fbb68fd1bc8e52032f5", "index": {"url": "z_8b591a4c69fc2395_discord_handler_py.html", "file": "src/core/automation/discord_handler.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 120, "n_excluded": 0, "n_missing": 120, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8b591a4c69fc2395_report_engine_py": {"hash": "aa5281a6acdde52b459a81bab87ebd70", "index": {"url": "z_8b591a4c69fc2395_report_engine_py.html", "file": "src/core/automation/report_engine.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 547, "n_excluded": 0, "n_missing": 547, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8b591a4c69fc2395_report_formatter_py": {"hash": "278b3293d002b83a4db6630797c7bc14", "index": {"url": "z_8b591a4c69fc2395_report_formatter_py.html", "file": "src/core/automation/report_formatter.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 372, "n_excluded": 0, "n_missing": 372, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8b591a4c69fc2395_report_scheduler_py": {"hash": "6c910de22b2b365b77965254c3ca8c34", "index": {"url": "z_8b591a4c69fc2395_report_scheduler_py.html", "file": "src/core/automation/report_scheduler.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 332, "n_excluded": 0, "n_missing": 332, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0618756b1ff51bca_config_manager_py": {"hash": "e8f21d389df0efba477c0391f923a685", "index": {"url": "z_0618756b1ff51bca_config_manager_py.html", "file": "src/core/config_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 116, "n_excluded": 0, "n_missing": 64, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0618756b1ff51bca_data_quality_validator_py": {"hash": "9e275b454014827ccd31441ab5eba65b", "index": {"url": "z_0618756b1ff51bca_data_quality_validator_py.html", "file": "src/core/data_quality_validator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 99, "n_excluded": 0, "n_missing": 99, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d09ff228a3163297___init___py": {"hash": "8f96e0487d400bed7b4c91e861310773", "index": {"url": "z_d09ff228a3163297___init___py.html", "file": "src/core/enums/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d09ff228a3163297_stock_analysis_py": {"hash": "f1729ca42483c42c6c0b3f8ec3a59d20", "index": {"url": "z_d09ff228a3163297_stock_analysis_py.html", "file": "src/core/enums/stock_analysis.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 30, "n_excluded": 0, "n_missing": 30, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0618756b1ff51bca_exceptions_py": {"hash": "ebf867e7908ab39c6dc15b8b0f40a0e1", "index": {"url": "z_0618756b1ff51bca_exceptions_py.html", "file": "src/core/exceptions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 47, "n_excluded": 0, "n_missing": 16, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0618756b1ff51bca_feedback_mechanism_py": {"hash": "c9c5a94cc55ceb36b455ed747f057ee9", "index": {"url": "z_0618756b1ff51bca_feedback_mechanism_py.html", "file": "src/core/feedback_mechanism.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 233, "n_excluded": 0, "n_missing": 233, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5b0edee8f7d4f9c4___init___py": {"hash": "e892735a0d79d0022f548d4b06237ee1", "index": {"url": "z_5b0edee8f7d4f9c4___init___py.html", "file": "src/core/formatting/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 4, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5b0edee8f7d4f9c4_analysis_template_py": {"hash": "84fc84915e16d849b23879f796079cda", "index": {"url": "z_5b0edee8f7d4f9c4_analysis_template_py.html", "file": "src/core/formatting/analysis_template.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 172, "n_excluded": 0, "n_missing": 172, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5b0edee8f7d4f9c4_response_templates_py": {"hash": "5e7a7f3d6fbf30df16abe30eabea0c28", "index": {"url": "z_5b0edee8f7d4f9c4_response_templates_py.html", "file": "src/core/formatting/response_templates.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 43, "n_excluded": 0, "n_missing": 43, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5b0edee8f7d4f9c4_technical_analysis_py": {"hash": "7329cfc0887e45fcc546c81420b72e66", "index": {"url": "z_5b0edee8f7d4f9c4_technical_analysis_py.html", "file": "src/core/formatting/technical_analysis.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 166, "n_excluded": 0, "n_missing": 166, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5b0edee8f7d4f9c4_text_formatting_py": {"hash": "4a6e468689d68d94a39b29bef292c75f", "index": {"url": "z_5b0edee8f7d4f9c4_text_formatting_py.html", "file": "src/core/formatting/text_formatting.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 13, "n_excluded": 0, "n_missing": 13, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0618756b1ff51bca_market_calendar_py": {"hash": "e641705bea867d75b6de0289ad7f05fe", "index": {"url": "z_0618756b1ff51bca_market_calendar_py.html", "file": "src/core/market_calendar.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 185, "n_excluded": 0, "n_missing": 185, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_35b456b0e3ec380c___init___py": {"hash": "2994806e106ce2336b12ff9f757307b8", "index": {"url": "z_35b456b0e3ec380c___init___py.html", "file": "src/core/monitoring_pkg/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_35b456b0e3ec380c_bot_monitor_py": {"hash": "74f14cf7e1377dc798b2e4ac9689846e", "index": {"url": "z_35b456b0e3ec380c_bot_monitor_py.html", "file": "src/core/monitoring_pkg/bot_monitor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 127, "n_excluded": 0, "n_missing": 89, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_35b456b0e3ec380c_performance_tracker_py": {"hash": "4c4bb12667d0c296dc26967ea2dd233c", "index": {"url": "z_35b456b0e3ec380c_performance_tracker_py.html", "file": "src/core/monitoring_pkg/performance_tracker.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 58, "n_excluded": 0, "n_missing": 42, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0618756b1ff51bca_pipeline_engine_py": {"hash": "d1f64be1b2d3b03c6ae63d8cc59e2ec9", "index": {"url": "z_0618756b1ff51bca_pipeline_engine_py.html", "file": "src/core/pipeline_engine.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 123, "n_excluded": 1, "n_missing": 123, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e4cdad6bd2c70039___init___py": {"hash": "b5c72bebd60f6da74cc32b88f3d56f73", "index": {"url": "z_e4cdad6bd2c70039___init___py.html", "file": "src/core/prompts/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 44, "n_excluded": 0, "n_missing": 22, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_022707a548f470bc___init___py": {"hash": "62672b12f887be10eacde78df86ee54c", "index": {"url": "z_022707a548f470bc___init___py.html", "file": "src/core/prompts/base/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_022707a548f470bc_compliance_py": {"hash": "43bdd7ff466d45d04fa76206c75647c1", "index": {"url": "z_022707a548f470bc_compliance_py.html", "file": "src/core/prompts/base/compliance.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 52, "n_excluded": 0, "n_missing": 26, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_022707a548f470bc_personas_py": {"hash": "0dfb1111bf8c1bfbe5201c9f168cb966", "index": {"url": "z_022707a548f470bc_personas_py.html", "file": "src/core/prompts/base/personas.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 42, "n_excluded": 0, "n_missing": 15, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_022707a548f470bc_system_prompts_py": {"hash": "5cbe92448993e52a6573743a16f4b755", "index": {"url": "z_022707a548f470bc_system_prompts_py.html", "file": "src/core/prompts/base/system_prompts.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 75, "n_excluded": 0, "n_missing": 46, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_39420e7102c5ca14___init___py": {"hash": "8f066b6f95e490708c9edb08b5e5da81", "index": {"url": "z_39420e7102c5ca14___init___py.html", "file": "src/core/prompts/commands/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_39420e7102c5ca14_analyze_prompts_py": {"hash": "8769834fd1310332dcd3927fd0c2b9d7", "index": {"url": "z_39420e7102c5ca14_analyze_prompts_py.html", "file": "src/core/prompts/commands/analyze_prompts.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 13, "n_excluded": 0, "n_missing": 6, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_39420e7102c5ca14_ask_prompts_py": {"hash": "39c35b409464086ca691f4ca5db50999", "index": {"url": "z_39420e7102c5ca14_ask_prompts_py.html", "file": "src/core/prompts/commands/ask_prompts.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 117, "n_excluded": 0, "n_missing": 58, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_39420e7102c5ca14_general_prompts_py": {"hash": "6b3f4e55159149ada58435c3be6997c1", "index": {"url": "z_39420e7102c5ca14_general_prompts_py.html", "file": "src/core/prompts/commands/general_prompts.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 15, "n_excluded": 0, "n_missing": 6, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e4cdad6bd2c70039_models_py": {"hash": "4506232a6d3523a945ff44f6e0fdef5d", "index": {"url": "z_e4cdad6bd2c70039_models_py.html", "file": "src/core/prompts/models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 66, "n_excluded": 0, "n_missing": 8, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e4cdad6bd2c70039_prompt_manager_py": {"hash": "3581c594b57c7de556c043b11f7ebf8f", "index": {"url": "z_e4cdad6bd2c70039_prompt_manager_py.html", "file": "src/core/prompts/prompt_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 231, "n_excluded": 7, "n_missing": 168, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_360fd4503d3a7f57___init___py": {"hash": "901289506ae4c7ae2aff8cb7de5c4f18", "index": {"url": "z_360fd4503d3a7f57___init___py.html", "file": "src/core/prompts/services/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_360fd4503d3a7f57_anti_hallucination_py": {"hash": "0155fe0e48f39b18827b78e4278446f9", "index": {"url": "z_360fd4503d3a7f57_anti_hallucination_py.html", "file": "src/core/prompts/services/anti_hallucination.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 20, "n_excluded": 0, "n_missing": 13, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_360fd4503d3a7f57_intent_detection_py": {"hash": "368c42bc4d92a7772a1eb5eb45d6d529", "index": {"url": "z_360fd4503d3a7f57_intent_detection_py.html", "file": "src/core/prompts/services/intent_detection.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 29, "n_excluded": 0, "n_missing": 20, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_360fd4503d3a7f57_security_analysis_py": {"hash": "147e02d39b402eea4f498531a9779fc8", "index": {"url": "z_360fd4503d3a7f57_security_analysis_py.html", "file": "src/core/prompts/services/security_analysis.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 12, "n_excluded": 0, "n_missing": 5, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_360fd4503d3a7f57_text_parsing_py": {"hash": "45e7571c28fc8dc879b3588c0da03016", "index": {"url": "z_360fd4503d3a7f57_text_parsing_py.html", "file": "src/core/prompts/services/text_parsing.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 12, "n_excluded": 0, "n_missing": 5, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_df1c8cb064ba181c___init___py": {"hash": "156750fed489b8d12d75b216671f3c23", "index": {"url": "z_df1c8cb064ba181c___init___py.html", "file": "src/core/prompts/templates/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e4cdad6bd2c70039_unified_prompts_py": {"hash": "b4b310e14e58934cc1b137e9d5393ba4", "index": {"url": "z_e4cdad6bd2c70039_unified_prompts_py.html", "file": "src/core/prompts/unified_prompts.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 52, "n_excluded": 0, "n_missing": 25, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_52225557544a60b6___init___py": {"hash": "df959b06a3f975842d9de830da569c27", "index": {"url": "z_52225557544a60b6___init___py.html", "file": "src/core/prompts/utils/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_52225557544a60b6_context_injection_py": {"hash": "b749143620ae0c587a32e46037ad4549", "index": {"url": "z_52225557544a60b6_context_injection_py.html", "file": "src/core/prompts/utils/context_injection.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 134, "n_excluded": 0, "n_missing": 121, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_52225557544a60b6_formatters_py": {"hash": "45ed7eb4c156828a161ba6671d4b941c", "index": {"url": "z_52225557544a60b6_formatters_py.html", "file": "src/core/prompts/utils/formatters.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 141, "n_excluded": 0, "n_missing": 123, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_52225557544a60b6_validation_py": {"hash": "0b0f4bbee43084677bbe9d30eaa654eb", "index": {"url": "z_52225557544a60b6_validation_py.html", "file": "src/core/prompts/utils/validation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 156, "n_excluded": 0, "n_missing": 137, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0618756b1ff51bca_resource_manager_py": {"hash": "6afccf44f2bab34e2a1a60f6f0890555", "index": {"url": "z_0618756b1ff51bca_resource_manager_py.html", "file": "src/core/resource_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 253, "n_excluded": 0, "n_missing": 253, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0618756b1ff51bca_response_generator_py": {"hash": "467e9f570fd29af140a8db23859047b6", "index": {"url": "z_0618756b1ff51bca_response_generator_py.html", "file": "src/core/response_generator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 88, "n_excluded": 0, "n_missing": 88, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b32cc051564944b5___init___py": {"hash": "aeae465a446e5a2f6734c6b6b1b0917a", "index": {"url": "z_b32cc051564944b5___init___py.html", "file": "src/core/risk_management/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 2, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b32cc051564944b5_atr_calculator_py": {"hash": "16012af3a48ddbe6d9a0a2a1b8108708", "index": {"url": "z_b32cc051564944b5_atr_calculator_py.html", "file": "src/core/risk_management/atr_calculator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 364, "n_excluded": 0, "n_missing": 364, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b32cc051564944b5_compliance_framework_py": {"hash": "ddc4d4e9131bc798c595c48aa3436ed0", "index": {"url": "z_b32cc051564944b5_compliance_framework_py.html", "file": "src/core/risk_management/compliance_framework.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 129, "n_excluded": 0, "n_missing": 129, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0618756b1ff51bca_scheduler_py": {"hash": "39a41cfb5667460abb53296a4ed187cf", "index": {"url": "z_0618756b1ff51bca_scheduler_py.html", "file": "src/core/scheduler.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 37, "n_excluded": 2, "n_missing": 37, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0618756b1ff51bca_trade_scanner_py": {"hash": "7312eb916169e6245dd9dc27016992b1", "index": {"url": "z_0618756b1ff51bca_trade_scanner_py.html", "file": "src/core/trade_scanner.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 101, "n_excluded": 0, "n_missing": 101, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9d33f2899f9f6498___init___py": {"hash": "4121d8b3c0f5b9003b4a9a4e0c4f97c6", "index": {"url": "z_9d33f2899f9f6498___init___py.html", "file": "src/core/validation/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9d33f2899f9f6498_financial_validator_py": {"hash": "e0b46f58995f5573ef4d383363183520", "index": {"url": "z_9d33f2899f9f6498_financial_validator_py.html", "file": "src/core/validation/financial_validator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 39, "n_excluded": 0, "n_missing": 32, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_3bb8a0f1ddb1d3c9___init___py": {"hash": "c9a483d2299774ebbf53e79578367602", "index": {"url": "z_3bb8a0f1ddb1d3c9___init___py.html", "file": "src/core/watchlist/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 1, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f1aa34bc7b1afc18___init___py": {"hash": "e6baa73cda2916dad605215f937a92e1", "index": {"url": "z_f1aa34bc7b1afc18___init___py.html", "file": "src/database/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f1aa34bc7b1afc18_config_py": {"hash": "ef61c62871e6fb8560e6f3b3302f57f8", "index": {"url": "z_f1aa34bc7b1afc18_config_py.html", "file": "src/database/config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 27, "n_excluded": 0, "n_missing": 27, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d5d913579db937fa___init___py": {"hash": "333baa2ed271d42f45cebf7f9a7dff4d", "index": {"url": "z_d5d913579db937fa___init___py.html", "file": "src/database/models/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 5, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d5d913579db937fa_alerts_py": {"hash": "d70bd63dba92b39e4b33d076423743cf", "index": {"url": "z_d5d913579db937fa_alerts_py.html", "file": "src/database/models/alerts.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 33, "n_excluded": 4, "n_missing": 33, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d5d913579db937fa_analysis_py": {"hash": "55545d18c0cdcfd7ddc4e8384bd87b02", "index": {"url": "z_d5d913579db937fa_analysis_py.html", "file": "src/database/models/analysis.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 31, "n_excluded": 4, "n_missing": 31, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d5d913579db937fa_compliance_py": {"hash": "fd46571d973fe36e7ebb145768d13022", "index": {"url": "z_d5d913579db937fa_compliance_py.html", "file": "src/database/models/compliance.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 20, "n_excluded": 0, "n_missing": 20, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d5d913579db937fa_data_models_py": {"hash": "3ffc88c69023e5d6321485b95df8015c", "index": {"url": "z_d5d913579db937fa_data_models_py.html", "file": "src/database/models/data_models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 84, "n_excluded": 0, "n_missing": 84, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d5d913579db937fa_interactions_py": {"hash": "7363d67de608954f35060af959c19bd6", "index": {"url": "z_d5d913579db937fa_interactions_py.html", "file": "src/database/models/interactions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 25, "n_excluded": 4, "n_missing": 25, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d5d913579db937fa_market_data_py": {"hash": "0fee81d12259faaf3c8bb075b0b6ed60", "index": {"url": "z_d5d913579db937fa_market_data_py.html", "file": "src/database/models/market_data.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 26, "n_excluded": 4, "n_missing": 26, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f1aa34bc7b1afc18_query_optimizer_py": {"hash": "5d433d7b652aea021d19726f0a384576", "index": {"url": "z_f1aa34bc7b1afc18_query_optimizer_py.html", "file": "src/database/query_optimizer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 189, "n_excluded": 0, "n_missing": 189, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f1aa34bc7b1afc18_query_wrapper_py": {"hash": "a18885a4bc59817e134504e556516e1a", "index": {"url": "z_f1aa34bc7b1afc18_query_wrapper_py.html", "file": "src/database/query_wrapper.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 67, "n_excluded": 0, "n_missing": 67, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2bce53edfefc088a___init___py": {"hash": "8f96e0487d400bed7b4c91e861310773", "index": {"url": "z_2bce53edfefc088a___init___py.html", "file": "src/database/repositories/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f1aa34bc7b1afc18_unified_client_py": {"hash": "6305c9f75c1c8cb0fb3811bdcf517ffd", "index": {"url": "z_f1aa34bc7b1afc18_unified_client_py.html", "file": "src/database/unified_client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 70, "n_excluded": 55, "n_missing": 70, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f1aa34bc7b1afc18_unified_db_py": {"hash": "c27a39a4b566480785eef113b9e80c50", "index": {"url": "z_f1aa34bc7b1afc18_unified_db_py.html", "file": "src/database/unified_db.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 127, "n_excluded": 0, "n_missing": 76, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_main_py": {"hash": "c9407fb61cff15ff10a38fd18c0e5c2f", "index": {"url": "z_145eef247bfb46b6_main_py.html", "file": "src/main.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 127, "n_excluded": 6, "n_missing": 127, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_276c81cb0f9851cf___init___py": {"hash": "44b9be3c68206d5f10635654b10229ef", "index": {"url": "z_276c81cb0f9851cf___init___py.html", "file": "src/security/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 4, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_276c81cb0f9851cf_middleware_py": {"hash": "651e5cf749d0374b08a99de803c0843c", "index": {"url": "z_276c81cb0f9851cf_middleware_py.html", "file": "src/security/middleware.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 120, "n_excluded": 0, "n_missing": 120, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_98093a27b7e7f29c___init___py": {"hash": "e6baa73cda2916dad605215f937a92e1", "index": {"url": "z_98093a27b7e7f29c___init___py.html", "file": "src/shared/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f49d35fbbf1fc1aa___init___py": {"hash": "e6baa73cda2916dad605215f937a92e1", "index": {"url": "z_f49d35fbbf1fc1aa___init___py.html", "file": "src/shared/ai_services/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f49d35fbbf1fc1aa_ai_chat_processor_py": {"hash": "8a5dec20480eab8c21aed2ac9fb9e631", "index": {"url": "z_f49d35fbbf1fc1aa_ai_chat_processor_py.html", "file": "src/shared/ai_services/ai_chat_processor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 55, "n_excluded": 0, "n_missing": 55, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f49d35fbbf1fc1aa_ai_processor_robust_py": {"hash": "934251e4c74719b22e2fecf45747b9f2", "index": {"url": "z_f49d35fbbf1fc1aa_ai_processor_robust_py.html", "file": "src/shared/ai_services/ai_processor_robust.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 795, "n_excluded": 2, "n_missing": 795, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f49d35fbbf1fc1aa_ai_security_detector_py": {"hash": "92d7fe4782dc79e5ba8abbf54f1604b4", "index": {"url": "z_f49d35fbbf1fc1aa_ai_security_detector_py.html", "file": "src/shared/ai_services/ai_security_detector.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 131, "n_excluded": 0, "n_missing": 131, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f49d35fbbf1fc1aa_ai_tool_registry_py": {"hash": "bad188c31b088ada009562be588f3509", "index": {"url": "z_f49d35fbbf1fc1aa_ai_tool_registry_py.html", "file": "src/shared/ai_services/ai_tool_registry.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 102, "n_excluded": 0, "n_missing": 102, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f49d35fbbf1fc1aa_anti_hallucination_prompt_py": {"hash": "88e06f9e0c3a11c6b6f23d6bee42cc35", "index": {"url": "z_f49d35fbbf1fc1aa_anti_hallucination_prompt_py.html", "file": "src/shared/ai_services/anti_hallucination_prompt.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 20, "n_excluded": 0, "n_missing": 20, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f49d35fbbf1fc1aa_circuit_breaker_py": {"hash": "6064bd05e029223b3d6f40ee63a9980a", "index": {"url": "z_f49d35fbbf1fc1aa_circuit_breaker_py.html", "file": "src/shared/ai_services/circuit_breaker.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 97, "n_excluded": 0, "n_missing": 60, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f49d35fbbf1fc1aa_cross_validation_ai_py": {"hash": "3d70d022af0c746c29c2575ff530e353", "index": {"url": "z_f49d35fbbf1fc1aa_cross_validation_ai_py.html", "file": "src/shared/ai_services/cross_validation_ai.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 221, "n_excluded": 0, "n_missing": 221, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f49d35fbbf1fc1aa_enhanced_ai_client_py": {"hash": "c462e0a44bb5c88b92ec2db5f37cbf82", "index": {"url": "z_f49d35fbbf1fc1aa_enhanced_ai_client_py.html", "file": "src/shared/ai_services/enhanced_ai_client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 101, "n_excluded": 0, "n_missing": 101, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f49d35fbbf1fc1aa_enhanced_intent_detector_py": {"hash": "6f47f97d753146af666e63ab31eb23b0", "index": {"url": "z_f49d35fbbf1fc1aa_enhanced_intent_detector_py.html", "file": "src/shared/ai_services/enhanced_intent_detector.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 174, "n_excluded": 0, "n_missing": 174, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f49d35fbbf1fc1aa_enhanced_symbol_extractor_py": {"hash": "f1ee281ad5edd0937b2a2f1e088172b9", "index": {"url": "z_f49d35fbbf1fc1aa_enhanced_symbol_extractor_py.html", "file": "src/shared/ai_services/enhanced_symbol_extractor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 191, "n_excluded": 0, "n_missing": 191, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f49d35fbbf1fc1aa_fact_verifier_py": {"hash": "3ee4adfe910101e31c0c1eb0a27b2810", "index": {"url": "z_f49d35fbbf1fc1aa_fact_verifier_py.html", "file": "src/shared/ai_services/fact_verifier.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 131, "n_excluded": 0, "n_missing": 131, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f49d35fbbf1fc1aa_fast_price_lookup_py": {"hash": "cad9bb3d5f6955159d565b5cb75168be", "index": {"url": "z_f49d35fbbf1fc1aa_fast_price_lookup_py.html", "file": "src/shared/ai_services/fast_price_lookup.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 66, "n_excluded": 0, "n_missing": 66, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f49d35fbbf1fc1aa_intelligent_text_parser_py": {"hash": "efdf8428696d92f634f50982d5b77c27", "index": {"url": "z_f49d35fbbf1fc1aa_intelligent_text_parser_py.html", "file": "src/shared/ai_services/intelligent_text_parser.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 175, "n_excluded": 0, "n_missing": 175, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f49d35fbbf1fc1aa_local_fallback_ai_py": {"hash": "091ecb48485dc036bd4dea9bd873d45c", "index": {"url": "z_f49d35fbbf1fc1aa_local_fallback_ai_py.html", "file": "src/shared/ai_services/local_fallback_ai.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 167, "n_excluded": 0, "n_missing": 167, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f49d35fbbf1fc1aa_openrouter_key_py": {"hash": "85dc6d40f0fef8042a894f04810be7df", "index": {"url": "z_f49d35fbbf1fc1aa_openrouter_key_py.html", "file": "src/shared/ai_services/openrouter_key.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f49d35fbbf1fc1aa_query_cache_py": {"hash": "843854c94b90cacdef86c4d099ba0f33", "index": {"url": "z_f49d35fbbf1fc1aa_query_cache_py.html", "file": "src/shared/ai_services/query_cache.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 95, "n_excluded": 0, "n_missing": 95, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f49d35fbbf1fc1aa_query_router_py": {"hash": "82a23df8b288f21e435a1975f896252a", "index": {"url": "z_f49d35fbbf1fc1aa_query_router_py.html", "file": "src/shared/ai_services/query_router.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 135, "n_excluded": 0, "n_missing": 135, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f49d35fbbf1fc1aa_rate_limit_handler_py": {"hash": "a5d43f54da39fc5d29aedcbdf7952f07", "index": {"url": "z_f49d35fbbf1fc1aa_rate_limit_handler_py.html", "file": "src/shared/ai_services/rate_limit_handler.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 151, "n_excluded": 0, "n_missing": 151, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f49d35fbbf1fc1aa_response_synthesizer_py": {"hash": "7e46e3df0dbcf52136ee8d2278c7981b", "index": {"url": "z_f49d35fbbf1fc1aa_response_synthesizer_py.html", "file": "src/shared/ai_services/response_synthesizer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 142, "n_excluded": 0, "n_missing": 142, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f49d35fbbf1fc1aa_simple_model_config_py": {"hash": "25496b5d2f8e12e8e508b99369cafff5", "index": {"url": "z_f49d35fbbf1fc1aa_simple_model_config_py.html", "file": "src/shared/ai_services/simple_model_config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 75, "n_excluded": 0, "n_missing": 45, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f49d35fbbf1fc1aa_simple_query_analyzer_py": {"hash": "076cb9332255bf091c249e9ec387ed89", "index": {"url": "z_f49d35fbbf1fc1aa_simple_query_analyzer_py.html", "file": "src/shared/ai_services/simple_query_analyzer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 82, "n_excluded": 0, "n_missing": 82, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f49d35fbbf1fc1aa_smart_model_router_py": {"hash": "6b9bea78e3531af23c1c6dc222331468", "index": {"url": "z_f49d35fbbf1fc1aa_smart_model_router_py.html", "file": "src/shared/ai_services/smart_model_router.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 281, "n_excluded": 0, "n_missing": 195, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f49d35fbbf1fc1aa_timeout_manager_py": {"hash": "b14a41595db51e5b406ee70f114c27db", "index": {"url": "z_f49d35fbbf1fc1aa_timeout_manager_py.html", "file": "src/shared/ai_services/timeout_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 57, "n_excluded": 0, "n_missing": 32, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f49d35fbbf1fc1aa_tool_registry_py": {"hash": "a34ac9bf29409d953fdf0102a14916b8", "index": {"url": "z_f49d35fbbf1fc1aa_tool_registry_py.html", "file": "src/shared/ai_services/tool_registry.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 116, "n_excluded": 0, "n_missing": 116, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f49d35fbbf1fc1aa_unified_ai_processor_py": {"hash": "5be71ad163dd47927d4faedef00ceddc", "index": {"url": "z_f49d35fbbf1fc1aa_unified_ai_processor_py.html", "file": "src/shared/ai_services/unified_ai_processor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 212, "n_excluded": 0, "n_missing": 212, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a7f47544f5bcd001___init___py": {"hash": "afab06526142e31b76f2fb1816ceb983", "index": {"url": "z_a7f47544f5bcd001___init___py.html", "file": "src/shared/cache/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a7f47544f5bcd001_cache_service_py": {"hash": "bb86d2b738f5b0cbc1426267f5c769a3", "index": {"url": "z_a7f47544f5bcd001_cache_service_py.html", "file": "src/shared/cache/cache_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 262, "n_excluded": 0, "n_missing": 183, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a7f47544f5bcd001_local_cache_manager_py": {"hash": "c1a41a9517a569b5af7d36e24c14a566", "index": {"url": "z_a7f47544f5bcd001_local_cache_manager_py.html", "file": "src/shared/cache/local_cache_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 137, "n_excluded": 0, "n_missing": 87, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a7f47544f5bcd001_secure_cache_py": {"hash": "a8e3f5db626e764f937145d77467835a", "index": {"url": "z_a7f47544f5bcd001_secure_cache_py.html", "file": "src/shared/cache/secure_cache.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 66, "n_excluded": 0, "n_missing": 66, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_dadfb7ec26efdb7c___init___py": {"hash": "3cc7ae3a798fbe77db8a2b03a93b95b8", "index": {"url": "z_dadfb7ec26efdb7c___init___py.html", "file": "src/shared/config/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_dadfb7ec26efdb7c_config_manager_py": {"hash": "f847191ec05436693b763e7759655e6f", "index": {"url": "z_dadfb7ec26efdb7c_config_manager_py.html", "file": "src/shared/config/config_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 89, "n_excluded": 0, "n_missing": 17, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_98093a27b7e7f29c_config_loader_py": {"hash": "412c4c049142ea954d27cd33eb851c92", "index": {"url": "z_98093a27b7e7f29c_config_loader_py.html", "file": "src/shared/config_loader.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 20, "n_excluded": 3, "n_missing": 20, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9c2f08a226d091be___init___py": {"hash": "6a04c02361cb5a327fde104ff368ad99", "index": {"url": "z_9c2f08a226d091be___init___py.html", "file": "src/shared/data_providers/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9c2f08a226d091be_aggregator_py": {"hash": "aff0f22a4ad46da40c2637fb3727e75c", "index": {"url": "z_9c2f08a226d091be_aggregator_py.html", "file": "src/shared/data_providers/aggregator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 166, "n_excluded": 0, "n_missing": 166, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9c2f08a226d091be_alpaca_provider_py": {"hash": "de0a37d9551f248881b29825f368ab8d", "index": {"url": "z_9c2f08a226d091be_alpaca_provider_py.html", "file": "src/shared/data_providers/alpaca_provider.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 158, "n_excluded": 0, "n_missing": 158, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9c2f08a226d091be_alpha_vantage_py": {"hash": "dcfaea4162fd5fa5cf58d5a6dd965f77", "index": {"url": "z_9c2f08a226d091be_alpha_vantage_py.html", "file": "src/shared/data_providers/alpha_vantage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 163, "n_excluded": 0, "n_missing": 163, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9c2f08a226d091be_alpha_vantage_mcp_py": {"hash": "bfe62f799d685f7034834cdcaf301e22", "index": {"url": "z_9c2f08a226d091be_alpha_vantage_mcp_py.html", "file": "src/shared/data_providers/alpha_vantage_mcp.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 131, "n_excluded": 0, "n_missing": 131, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9c2f08a226d091be_base_py": {"hash": "5502f168c4e7b0dab677a409fb1f88fd", "index": {"url": "z_9c2f08a226d091be_base_py.html", "file": "src/shared/data_providers/base.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 7, "n_excluded": 42, "n_missing": 7, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9c2f08a226d091be_enhanced_data_fetcher_py": {"hash": "3a344d90ba90a52ed6b2e5ebfee192f0", "index": {"url": "z_9c2f08a226d091be_enhanced_data_fetcher_py.html", "file": "src/shared/data_providers/enhanced_data_fetcher.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 196, "n_excluded": 0, "n_missing": 196, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9c2f08a226d091be_enhanced_error_handler_py": {"hash": "6a9df5da48e7a1ed58c035edb3f6e4c3", "index": {"url": "z_9c2f08a226d091be_enhanced_error_handler_py.html", "file": "src/shared/data_providers/enhanced_error_handler.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 162, "n_excluded": 0, "n_missing": 162, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9c2f08a226d091be_example_usage_py": {"hash": "17ce211466f1c4af3a306e697e989814", "index": {"url": "z_9c2f08a226d091be_example_usage_py.html", "file": "src/shared/data_providers/example_usage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 153, "n_excluded": 2, "n_missing": 153, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9c2f08a226d091be_fallback_provider_py": {"hash": "36f90b1d63a5055cd637b94902798c7d", "index": {"url": "z_9c2f08a226d091be_fallback_provider_py.html", "file": "src/shared/data_providers/fallback_provider.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 43, "n_excluded": 0, "n_missing": 43, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9c2f08a226d091be_finnhub_provider_py": {"hash": "2e20a8900ce44e531e19e0a052ac367a", "index": {"url": "z_9c2f08a226d091be_finnhub_provider_py.html", "file": "src/shared/data_providers/finnhub_provider.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 97, "n_excluded": 0, "n_missing": 97, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9c2f08a226d091be_health_monitor_py": {"hash": "35a5a91322b118dd7fdee5b712e89f85", "index": {"url": "z_9c2f08a226d091be_health_monitor_py.html", "file": "src/shared/data_providers/health_monitor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 181, "n_excluded": 0, "n_missing": 181, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9c2f08a226d091be_hybrid_mcp_provider_py": {"hash": "c7bf98481d3782252b2edead89b3cdc1", "index": {"url": "z_9c2f08a226d091be_hybrid_mcp_provider_py.html", "file": "src/shared/data_providers/hybrid_mcp_provider.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 158, "n_excluded": 0, "n_missing": 158, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9c2f08a226d091be_models_py": {"hash": "3b155b9d064e7bb1219714d5e18caff4", "index": {"url": "z_9c2f08a226d091be_models_py.html", "file": "src/shared/data_providers/models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 24, "n_excluded": 0, "n_missing": 24, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9c2f08a226d091be_polygon_provider_py": {"hash": "********************************", "index": {"url": "z_9c2f08a226d091be_polygon_provider_py.html", "file": "src/shared/data_providers/polygon_provider.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 7, "n_excluded": 0, "n_missing": 7, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9c2f08a226d091be_unified_base_py": {"hash": "07c44fc1377312e765a89cb4a3b517fb", "index": {"url": "z_9c2f08a226d091be_unified_base_py.html", "file": "src/shared/data_providers/unified_base.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 168, "n_excluded": 3, "n_missing": 168, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9c2f08a226d091be_yfinance_provider_py": {"hash": "b85d2a59f46765125785d74b2332a289", "index": {"url": "z_9c2f08a226d091be_yfinance_provider_py.html", "file": "src/shared/data_providers/yfinance_provider.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 118, "n_excluded": 0, "n_missing": 118, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_98093a27b7e7f29c_data_validation_py": {"hash": "43fabe3f39c8e3a6447b5e09c1ac20c3", "index": {"url": "z_98093a27b7e7f29c_data_validation_py.html", "file": "src/shared/data_validation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 202, "n_excluded": 0, "n_missing": 202, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6e2c44bbef529a62___init___py": {"hash": "38323e84b62102a53c220304cb089257", "index": {"url": "z_6e2c44bbef529a62___init___py.html", "file": "src/shared/error_handling/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6e2c44bbef529a62_fallback_py": {"hash": "6c2cee1806940ccc982d76a803f6e029", "index": {"url": "z_6e2c44bbef529a62_fallback_py.html", "file": "src/shared/error_handling/fallback.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 132, "n_excluded": 0, "n_missing": 83, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6e2c44bbef529a62_logging_py": {"hash": "b38198a3211c1263a8c813055a216eb8", "index": {"url": "z_6e2c44bbef529a62_logging_py.html", "file": "src/shared/error_handling/logging.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 88, "n_excluded": 0, "n_missing": 48, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6e2c44bbef529a62_retry_py": {"hash": "a6e11cdd5843af6eb3b0625e17f46f0c", "index": {"url": "z_6e2c44bbef529a62_retry_py.html", "file": "src/shared/error_handling/retry.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 163, "n_excluded": 0, "n_missing": 163, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f2ca99271ac6bc52___init___py": {"hash": "a42f52f1cb50d0104d84d6d00494dac7", "index": {"url": "z_f2ca99271ac6bc52___init___py.html", "file": "src/shared/monitoring/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f2ca99271ac6bc52_intelligent_grader_py": {"hash": "91107b45d55bdd961fdfbc9e3651a03e", "index": {"url": "z_f2ca99271ac6bc52_intelligent_grader_py.html", "file": "src/shared/monitoring/intelligent_grader.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 141, "n_excluded": 0, "n_missing": 141, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f2ca99271ac6bc52_observability_py": {"hash": "38185c98b5f55968e6946f80acf4a8c2", "index": {"url": "z_f2ca99271ac6bc52_observability_py.html", "file": "src/shared/monitoring/observability.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 186, "n_excluded": 0, "n_missing": 99, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f2ca99271ac6bc52_performance_monitor_py": {"hash": "bc15ecd545e50cb9ea1a99474ace3b0b", "index": {"url": "z_f2ca99271ac6bc52_performance_monitor_py.html", "file": "src/shared/monitoring/performance_monitor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 83, "n_excluded": 0, "n_missing": 83, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f2ca99271ac6bc52_pipeline_grader_py": {"hash": "068945b351ec6a17e9aa71d5c9253bea", "index": {"url": "z_f2ca99271ac6bc52_pipeline_grader_py.html", "file": "src/shared/monitoring/pipeline_grader.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 303, "n_excluded": 0, "n_missing": 224, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f2ca99271ac6bc52_pipeline_monitor_py": {"hash": "436f07495ebea60d359a829767e78439", "index": {"url": "z_f2ca99271ac6bc52_pipeline_monitor_py.html", "file": "src/shared/monitoring/pipeline_monitor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 8, "n_excluded": 0, "n_missing": 8, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f2ca99271ac6bc52_step_logger_py": {"hash": "3ae5106a6d0e126687b0bde8fd572b09", "index": {"url": "z_f2ca99271ac6bc52_step_logger_py.html", "file": "src/shared/monitoring/step_logger.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 113, "n_excluded": 0, "n_missing": 113, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1758674a005c55bf___init___py": {"hash": "3da86a9d31d4fe907a68a77a515ada67", "index": {"url": "z_1758674a005c55bf___init___py.html", "file": "src/shared/services/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 4, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1758674a005c55bf_enhanced_performance_optimizer_py": {"hash": "d20415e8da4b916e5e7bfaa4b83fe13a", "index": {"url": "z_1758674a005c55bf_enhanced_performance_optimizer_py.html", "file": "src/shared/services/enhanced_performance_optimizer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 328, "n_excluded": 0, "n_missing": 328, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1758674a005c55bf_optimization_service_py": {"hash": "b6fc3dc9ae140631261260f5c9efe182", "index": {"url": "z_1758674a005c55bf_optimization_service_py.html", "file": "src/shared/services/optimization_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 203, "n_excluded": 0, "n_missing": 203, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1758674a005c55bf_performance_monitor_py": {"hash": "1f58abc63c0711ebaa2cdaebcdd7975e", "index": {"url": "z_1758674a005c55bf_performance_monitor_py.html", "file": "src/shared/services/performance_monitor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 158, "n_excluded": 0, "n_missing": 158, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_647cfec4e88d88b8___init___py": {"hash": "04e5991739ff95828668c91b95f2ecd2", "index": {"url": "z_647cfec4e88d88b8___init___py.html", "file": "src/shared/technical_analysis/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 29, "n_excluded": 0, "n_missing": 29, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_647cfec4e88d88b8_calculator_py": {"hash": "7758a15f438c40e5f94e5079efd71a1b", "index": {"url": "z_647cfec4e88d88b8_calculator_py.html", "file": "src/shared/technical_analysis/calculator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 189, "n_excluded": 0, "n_missing": 189, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_647cfec4e88d88b8_config_py": {"hash": "56dab6290820fffc71422dc0be2c4f39", "index": {"url": "z_647cfec4e88d88b8_config_py.html", "file": "src/shared/technical_analysis/config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 76, "n_excluded": 0, "n_missing": 76, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_647cfec4e88d88b8_enhanced_calculator_py": {"hash": "bc776fb5b76efee503ccdeb9e4b0cdc7", "index": {"url": "z_647cfec4e88d88b8_enhanced_calculator_py.html", "file": "src/shared/technical_analysis/enhanced_calculator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 207, "n_excluded": 0, "n_missing": 207, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_647cfec4e88d88b8_enhanced_indicators_py": {"hash": "0aaf021a8062249c495fc8059cbf5c43", "index": {"url": "z_647cfec4e88d88b8_enhanced_indicators_py.html", "file": "src/shared/technical_analysis/enhanced_indicators.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 342, "n_excluded": 0, "n_missing": 342, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_647cfec4e88d88b8_indicators_py": {"hash": "678897ab76a0969fea4ba81a4a7ddcb5", "index": {"url": "z_647cfec4e88d88b8_indicators_py.html", "file": "src/shared/technical_analysis/indicators.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 145, "n_excluded": 0, "n_missing": 145, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_647cfec4e88d88b8_multi_timeframe_analyzer_py": {"hash": "ef2a998a1bf02c6df86aab1683a8cf14", "index": {"url": "z_647cfec4e88d88b8_multi_timeframe_analyzer_py.html", "file": "src/shared/technical_analysis/multi_timeframe_analyzer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 414, "n_excluded": 0, "n_missing": 414, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_647cfec4e88d88b8_options_greeks_calculator_py": {"hash": "dff59010ee9c032f5ca9d76893dbab52", "index": {"url": "z_647cfec4e88d88b8_options_greeks_calculator_py.html", "file": "src/shared/technical_analysis/options_greeks_calculator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 82, "n_excluded": 0, "n_missing": 82, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_647cfec4e88d88b8_signal_generator_py": {"hash": "4a327e5ce5bd5d09b3b3d49b89e5d982", "index": {"url": "z_647cfec4e88d88b8_signal_generator_py.html", "file": "src/shared/technical_analysis/signal_generator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 50, "n_excluded": 0, "n_missing": 50, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_647cfec4e88d88b8_strategy_calculator_py": {"hash": "a494070e467e3c62ca8170f47e277d64", "index": {"url": "z_647cfec4e88d88b8_strategy_calculator_py.html", "file": "src/shared/technical_analysis/strategy_calculator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 117, "n_excluded": 0, "n_missing": 117, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_647cfec4e88d88b8_unified_calculator_py": {"hash": "2965fb85d97028987b4764f081797cee", "index": {"url": "z_647cfec4e88d88b8_unified_calculator_py.html", "file": "src/shared/technical_analysis/unified_calculator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 146, "n_excluded": 0, "n_missing": 146, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_647cfec4e88d88b8_volume_analyzer_py": {"hash": "92bd479626c3400c71b8787c06d6e075", "index": {"url": "z_647cfec4e88d88b8_volume_analyzer_py.html", "file": "src/shared/technical_analysis/volume_analyzer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 298, "n_excluded": 0, "n_missing": 298, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_647cfec4e88d88b8_zones_py": {"hash": "7d66e0960b4fe10f715be56f8becbda9", "index": {"url": "z_647cfec4e88d88b8_zones_py.html", "file": "src/shared/technical_analysis/zones.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 648, "n_excluded": 0, "n_missing": 648, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_51ec32faff62b207___init___py": {"hash": "adc6e50a94d8e9e180a91fdf089cfa77", "index": {"url": "z_51ec32faff62b207___init___py.html", "file": "src/shared/utils/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_51ec32faff62b207_deprecation_py": {"hash": "73f0351d3f5a90fbe7049039bd4c3bf9", "index": {"url": "z_51ec32faff62b207_deprecation_py.html", "file": "src/shared/utils/deprecation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_51ec32faff62b207_discord_helpers_py": {"hash": "c3deace5d6971a42ed74b17c522f33e3", "index": {"url": "z_51ec32faff62b207_discord_helpers_py.html", "file": "src/shared/utils/discord_helpers.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 123, "n_excluded": 0, "n_missing": 101, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_51ec32faff62b207_lazy_import_py": {"hash": "a77df9bee82c066bd42026675f3eff64", "index": {"url": "z_51ec32faff62b207_lazy_import_py.html", "file": "src/shared/utils/lazy_import.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 61, "n_excluded": 6, "n_missing": 61, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_51ec32faff62b207_symbol_extraction_py": {"hash": "c423534b2edc040b3034bc8bdacef5cd", "index": {"url": "z_51ec32faff62b207_symbol_extraction_py.html", "file": "src/shared/utils/symbol_extraction.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 293, "n_excluded": 0, "n_missing": 195, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_03dd4c803eb1f65e_disclaimer_manager_py": {"hash": "6b68843baa1ce8d11c520b5abfffa29a", "index": {"url": "z_03dd4c803eb1f65e_disclaimer_manager_py.html", "file": "src/bot/utils/disclaimer_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 53, "n_excluded": 0, "n_missing": 33, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_03dd4c803eb1f65e_input_sanitizer_py": {"hash": "e08575948e219b7db156a4cf37be6e1f", "index": {"url": "z_03dd4c803eb1f65e_input_sanitizer_py.html", "file": "src/bot/utils/input_sanitizer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 175, "n_excluded": 0, "n_missing": 133, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f4aeaef6978b2030_health_monitor_py": {"hash": "a750c1f25367f9e8c34e0f2571b6b390", "index": {"url": "z_f4aeaef6978b2030_health_monitor_py.html", "file": "src/core/monitoring/health_monitor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 211, "n_excluded": 0, "n_missing": 137, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f4aeaef6978b2030_logger_py": {"hash": "fa5fdd6127735dbf7a321e1a50c78a46", "index": {"url": "z_f4aeaef6978b2030_logger_py.html", "file": "src/core/monitoring/logger.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 164, "n_excluded": 0, "n_missing": 64, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f4aeaef6978b2030_metrics_py": {"hash": "e9ba6c0acf80c344a831008a81f1cc57", "index": {"url": "z_f4aeaef6978b2030_metrics_py.html", "file": "src/core/monitoring/metrics.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 116, "n_excluded": 0, "n_missing": 59, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f4aeaef6978b2030_tracer_py": {"hash": "be213577c3776349e0d7a69748a87888", "index": {"url": "z_f4aeaef6978b2030_tracer_py.html", "file": "src/core/monitoring/tracer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 146, "n_excluded": 0, "n_missing": 82, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_cbf0818c9be381a0_ai_client_py": {"hash": "e83ddd9f0e0ba1b11188ae520e395f26", "index": {"url": "z_cbf0818c9be381a0_ai_client_py.html", "file": "src/shared/ai_chat/ai_client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 268, "n_excluded": 0, "n_missing": 228, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_cbf0818c9be381a0_config_py": {"hash": "4ea99ea944dbd89fc01eff956079a59d", "index": {"url": "z_cbf0818c9be381a0_config_py.html", "file": "src/shared/ai_chat/config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 31, "n_excluded": 0, "n_missing": 10, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_cbf0818c9be381a0_models_py": {"hash": "ea2155bf6479f76c402f47fc4bfd9ec6", "index": {"url": "z_cbf0818c9be381a0_models_py.html", "file": "src/shared/ai_chat/models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 49, "n_excluded": 0, "n_missing": 9, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_93b1c7bd489fca84_token_validator_py": {"hash": "6e958c7389caa590d71cb8d2d5d89b87", "index": {"url": "z_93b1c7bd489fca84_token_validator_py.html", "file": "src/bot/token_validator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 141, "n_excluded": 0, "n_missing": 141, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}