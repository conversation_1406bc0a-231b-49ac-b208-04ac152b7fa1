<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage for src/bot/extensions/status.py: 0%</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_6b508a39.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="pyfile">
<header>
    <div class="content">
        <h1>
            <span class="text">Coverage for </span><b>src/bot/extensions/status.py</b>:
            <span class="pc_cov">0%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>r</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        &nbsp; toggle line displays
                    </p>
                    <p>
                        <kbd>j</kbd>
                        <kbd>k</kbd>
                        &nbsp; next/prev highlighted chunk
                    </p>
                    <p>
                        <kbd>0</kbd> &nbsp; (zero) top of page
                    </p>
                    <p>
                        <kbd>1</kbd> &nbsp; (one) first highlighted chunk
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>u</kbd> &nbsp; up to the index
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <h2>
            <span class="text">88 statements &nbsp;</span>
            <button type="button" class="run button_toggle_run" value="run" data-shortcut="r" title="Toggle lines run">0<span class="text"> run</span></button>
            <button type="button" class="mis show_mis button_toggle_mis" value="mis" data-shortcut="m" title="Toggle lines missing">88<span class="text"> missing</span></button>
            <button type="button" class="exc show_exc button_toggle_exc" value="exc" data-shortcut="x" title="Toggle lines excluded">0<span class="text"> excluded</span></button>
        </h2>
        <p class="text">
            <a id="prevFileLink" class="nav" href="z_8b651bcae9720a9b_recommendations_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a id="indexLink" class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a id="nextFileLink" class="nav" href="z_8b651bcae9720a9b_utility_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.6">coverage.py v7.10.6</a>,
            created at 2025-09-28 09:58 -0400
        </p>
        <aside class="hidden">
            <button type="button" class="button_next_chunk" data-shortcut="j"></button>
            <button type="button" class="button_prev_chunk" data-shortcut="k"></button>
            <button type="button" class="button_top_of_page" data-shortcut="0"></button>
            <button type="button" class="button_first_chunk" data-shortcut="1"></button>
            <button type="button" class="button_prev_file" data-shortcut="["></button>
            <button type="button" class="button_next_file" data-shortcut="]"></button>
            <button type="button" class="button_to_index" data-shortcut="u"></button>
            <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
        </aside>
    </div>
</header>
<main id="source">
    <p class="pln"><span class="n"><a id="t1" href="#t1">1</a></span><span class="t"><span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t2" href="#t2">2</a></span><span class="t"><span class="str">Status Command Extension - Enhanced Version</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t3" href="#t3">3</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t4" href="#t4">4</a></span><span class="t"><span class="str">Bot health and system status with comprehensive monitoring integration.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t5" href="#t5">5</a></span><span class="t"><span class="str">Enhanced with new bot_monitor for detailed uptime, service health, user activity,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t6" href="#t6">6</a></span><span class="t"><span class="str">database connection status, and performance snapshots. Includes historical uptime</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t7" href="#t7">7</a></span><span class="t"><span class="str">and alert status from core monitoring.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t8" href="#t8">8</a></span><span class="t"><span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t9" href="#t9">9</a></span><span class="t"><span class="key">import</span> <span class="nam">discord</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t10" href="#t10">10</a></span><span class="t"><span class="key">from</span> <span class="nam">discord</span><span class="op">.</span><span class="nam">ext</span> <span class="key">import</span> <span class="nam">commands</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t11" href="#t11">11</a></span><span class="t"><span class="key">from</span> <span class="nam">discord</span> <span class="key">import</span> <span class="nam">app_commands</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t12" href="#t12">12</a></span><span class="t"><span class="key">from</span> <span class="nam">datetime</span> <span class="key">import</span> <span class="nam">datetime</span><span class="op">,</span> <span class="nam">timedelta</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t13" href="#t13">13</a></span><span class="t"><span class="key">import</span> <span class="nam">asyncio</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t14" href="#t14">14</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t15" href="#t15">15</a></span><span class="t"><span class="key">from</span> <span class="nam">src</span><span class="op">.</span><span class="nam">shared</span><span class="op">.</span><span class="nam">error_handling</span><span class="op">.</span><span class="nam">logging</span> <span class="key">import</span> <span class="nam">get_logger</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t16" href="#t16">16</a></span><span class="t"><span class="key">from</span> <span class="nam">src</span><span class="op">.</span><span class="nam">core</span><span class="op">.</span><span class="nam">error_handling</span><span class="op">.</span><span class="nam">fallback</span> <span class="key">import</span> <span class="nam">handle_error_with_fallback</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t17" href="#t17">17</a></span><span class="t"><span class="key">from</span> <span class="nam">src</span><span class="op">.</span><span class="nam">bot</span><span class="op">.</span><span class="nam">core</span><span class="op">.</span><span class="nam">error_handler</span> <span class="key">import</span> <span class="nam">log_and_notify_error</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t18" href="#t18">18</a></span><span class="t"><span class="key">from</span> <span class="nam">src</span><span class="op">.</span><span class="nam">bot</span><span class="op">.</span><span class="nam">permissions</span> <span class="key">import</span> <span class="nam">PermissionLevel</span><span class="op">,</span> <span class="nam">DiscordPermissionChecker</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t19" href="#t19">19</a></span><span class="t"><span class="key">from</span> <span class="nam">src</span><span class="op">.</span><span class="nam">core</span><span class="op">.</span><span class="nam">monitoring_pkg</span><span class="op">.</span><span class="nam">bot_monitor</span> <span class="key">import</span> <span class="nam">BotMonitor</span>  <span class="com"># New monitor</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t20" href="#t20">20</a></span><span class="t"><span class="key">from</span> <span class="nam">src</span><span class="op">.</span><span class="nam">core</span><span class="op">.</span><span class="nam">monitoring</span><span class="op">.</span><span class="nam">health_monitor</span> <span class="key">import</span> <span class="nam">HealthMonitor</span>  <span class="com"># System health</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t21" href="#t21">21</a></span><span class="t"><span class="key">from</span> <span class="nam">src</span><span class="op">.</span><span class="nam">database</span><span class="op">.</span><span class="nam">unified_db</span> <span class="key">import</span> <span class="nam">get_db_connection_status</span>  <span class="com"># DB status</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t22" href="#t22">22</a></span><span class="t"><span class="key">from</span> <span class="nam">src</span><span class="op">.</span><span class="nam">shared</span><span class="op">.</span><span class="nam">services</span><span class="op">.</span><span class="nam">performance_monitor</span> <span class="key">import</span> <span class="nam">get_current_performance</span>  <span class="com"># Perf snapshot</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t23" href="#t23">23</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t24" href="#t24">24</a></span><span class="t"><span class="nam">logger</span> <span class="op">=</span> <span class="nam">get_logger</span><span class="op">(</span><span class="nam">__name__</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t25" href="#t25">25</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t26" href="#t26">26</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t27" href="#t27">27</a></span><span class="t"><span class="key">class</span> <span class="nam">StatusCommand</span><span class="op">(</span><span class="nam">commands</span><span class="op">.</span><span class="nam">Cog</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t28" href="#t28">28</a></span><span class="t">    <span class="str">"""Enhanced status command for comprehensive bot health monitoring."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t29" href="#t29">29</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t30" href="#t30">30</a></span><span class="t">    <span class="key">def</span> <span class="nam">__init__</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">bot</span><span class="op">:</span> <span class="nam">commands</span><span class="op">.</span><span class="nam">Bot</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t31" href="#t31">31</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">bot</span> <span class="op">=</span> <span class="nam">bot</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t32" href="#t32">32</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">permission_checker</span> <span class="op">=</span> <span class="nam">DiscordPermissionChecker</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t33" href="#t33">33</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">bot_monitor</span> <span class="op">=</span> <span class="nam">BotMonitor</span><span class="op">(</span><span class="nam">bot</span><span class="op">)</span>  <span class="com"># New integration</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t34" href="#t34">34</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">health_monitor</span> <span class="op">=</span> <span class="nam">HealthMonitor</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t35" href="#t35">35</a></span><span class="t">        <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"&#9989; Enhanced Status extension initialized"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t36" href="#t36">36</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t37" href="#t37">37</a></span><span class="t">    <span class="op">@</span><span class="nam">app_commands</span><span class="op">.</span><span class="nam">command</span><span class="op">(</span><span class="nam">name</span><span class="op">=</span><span class="str">"status"</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"Comprehensive bot and system status check"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t38" href="#t38">38</a></span><span class="t">    <span class="op">@</span><span class="nam">app_commands</span><span class="op">.</span><span class="nam">describe</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t39" href="#t39">39</a></span><span class="t">        <span class="nam">detailed</span><span class="op">=</span><span class="str">"Include detailed service health and performance metrics"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t40" href="#t40">40</a></span><span class="t">        <span class="nam">uptime_period</span><span class="op">=</span><span class="str">"Uptime period (24h, 7d, 30d)"</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t41" href="#t41">41</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t42" href="#t42">42</a></span><span class="t">    <span class="key">async</span> <span class="key">def</span> <span class="nam">status_command</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">interaction</span><span class="op">:</span> <span class="nam">discord</span><span class="op">.</span><span class="nam">Interaction</span><span class="op">,</span> <span class="nam">detailed</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">bool</span><span class="op">]</span> <span class="op">=</span> <span class="key">False</span><span class="op">,</span> <span class="nam">uptime_period</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="str">"24h"</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t43" href="#t43">43</a></span><span class="t">        <span class="str">"""Enhanced status with monitoring integration."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t44" href="#t44">44</a></span><span class="t">        <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t45" href="#t45">45</a></span><span class="t">            <span class="com"># Permission check (admin only for detailed)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t46" href="#t46">46</a></span><span class="t">            <span class="key">if</span> <span class="nam">detailed</span> <span class="key">and</span> <span class="key">not</span> <span class="nam">self</span><span class="op">.</span><span class="nam">permission_checker</span><span class="op">.</span><span class="nam">has_permission</span><span class="op">(</span><span class="nam">interaction</span><span class="op">.</span><span class="nam">user</span><span class="op">,</span> <span class="nam">PermissionLevel</span><span class="op">.</span><span class="nam">ADMIN</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t47" href="#t47">47</a></span><span class="t">                <span class="key">await</span> <span class="nam">interaction</span><span class="op">.</span><span class="nam">response</span><span class="op">.</span><span class="nam">send_message</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t48" href="#t48">48</a></span><span class="t">                    <span class="str">"&#10060; Detailed status requires admin access."</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t49" href="#t49">49</a></span><span class="t">                    <span class="nam">ephemeral</span><span class="op">=</span><span class="key">True</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t50" href="#t50">50</a></span><span class="t">                <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t51" href="#t51">51</a></span><span class="t">                <span class="key">return</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t52" href="#t52">52</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t53" href="#t53">53</a></span><span class="t">            <span class="key">await</span> <span class="nam">interaction</span><span class="op">.</span><span class="nam">response</span><span class="op">.</span><span class="nam">defer</span><span class="op">(</span><span class="nam">ephemeral</span><span class="op">=</span><span class="nam">detailed</span><span class="op">)</span>  <span class="com"># Ephemeral for detailed</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t54" href="#t54">54</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t55" href="#t55">55</a></span><span class="t">            <span class="com"># Basic bot stats</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t56" href="#t56">56</a></span><span class="t">            <span class="nam">latency</span> <span class="op">=</span> <span class="nam">round</span><span class="op">(</span><span class="nam">self</span><span class="op">.</span><span class="nam">bot</span><span class="op">.</span><span class="nam">latency</span> <span class="op">*</span> <span class="num">1000</span><span class="op">,</span> <span class="num">2</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t57" href="#t57">57</a></span><span class="t">            <span class="nam">guild_count</span> <span class="op">=</span> <span class="nam">len</span><span class="op">(</span><span class="nam">self</span><span class="op">.</span><span class="nam">bot</span><span class="op">.</span><span class="nam">guilds</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t58" href="#t58">58</a></span><span class="t">            <span class="nam">user_count</span> <span class="op">=</span> <span class="nam">sum</span><span class="op">(</span><span class="nam">guild</span><span class="op">.</span><span class="nam">member_count</span> <span class="key">for</span> <span class="nam">guild</span> <span class="key">in</span> <span class="nam">self</span><span class="op">.</span><span class="nam">bot</span><span class="op">.</span><span class="nam">guilds</span> <span class="key">if</span> <span class="nam">guild</span><span class="op">.</span><span class="nam">member_count</span> <span class="key">is</span> <span class="key">not</span> <span class="key">None</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t59" href="#t59">59</a></span><span class="t">            <span class="nam">active_guilds</span> <span class="op">=</span> <span class="nam">len</span><span class="op">(</span><span class="op">[</span><span class="nam">g</span> <span class="key">for</span> <span class="nam">g</span> <span class="key">in</span> <span class="nam">self</span><span class="op">.</span><span class="nam">bot</span><span class="op">.</span><span class="nam">guilds</span> <span class="key">if</span> <span class="nam">g</span><span class="op">.</span><span class="nam">member_count</span> <span class="key">and</span> <span class="nam">g</span><span class="op">.</span><span class="nam">member_count</span> <span class="op">></span> <span class="num">0</span><span class="op">]</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t60" href="#t60">60</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t61" href="#t61">61</a></span><span class="t">            <span class="com"># Enhanced with monitor</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t62" href="#t62">62</a></span><span class="t">            <span class="nam">monitor_stats</span> <span class="op">=</span> <span class="key">await</span> <span class="nam">handle_error_with_fallback</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t63" href="#t63">63</a></span><span class="t">                <span class="nam">self</span><span class="op">.</span><span class="nam">bot_monitor</span><span class="op">.</span><span class="nam">get_status_metrics</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t64" href="#t64">64</a></span><span class="t">                <span class="nam">default</span><span class="op">=</span><span class="op">{</span><span class="str">"uptime"</span><span class="op">:</span> <span class="str">"Unknown"</span><span class="op">,</span> <span class="str">"active_users"</span><span class="op">:</span> <span class="num">0</span><span class="op">,</span> <span class="str">"message_rate"</span><span class="op">:</span> <span class="num">0</span><span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t65" href="#t65">65</a></span><span class="t">            <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t66" href="#t66">66</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t67" href="#t67">67</a></span><span class="t">            <span class="com"># System health</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t68" href="#t68">68</a></span><span class="t">            <span class="nam">system_health</span> <span class="op">=</span> <span class="key">await</span> <span class="nam">self</span><span class="op">.</span><span class="nam">health_monitor</span><span class="op">.</span><span class="nam">get_system_health</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t69" href="#t69">69</a></span><span class="t">            <span class="nam">db_status</span> <span class="op">=</span> <span class="key">await</span> <span class="nam">get_db_connection_status</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t70" href="#t70">70</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t71" href="#t71">71</a></span><span class="t">            <span class="com"># Uptime calculation</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t72" href="#t72">72</a></span><span class="t">            <span class="nam">uptime_str</span> <span class="op">=</span> <span class="key">await</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_calculate_uptime</span><span class="op">(</span><span class="nam">uptime_period</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t73" href="#t73">73</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t74" href="#t74">74</a></span><span class="t">            <span class="com"># Create embed</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t75" href="#t75">75</a></span><span class="t">            <span class="nam">color</span> <span class="op">=</span> <span class="nam">discord</span><span class="op">.</span><span class="nam">Color</span><span class="op">.</span><span class="nam">green</span><span class="op">(</span><span class="op">)</span> <span class="key">if</span> <span class="nam">system_health</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">"overall_status"</span><span class="op">)</span> <span class="op">==</span> <span class="str">"healthy"</span> <span class="key">else</span> <span class="nam">discord</span><span class="op">.</span><span class="nam">Color</span><span class="op">.</span><span class="nam">orange</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t76" href="#t76">76</a></span><span class="t">            <span class="nam">embed</span> <span class="op">=</span> <span class="nam">discord</span><span class="op">.</span><span class="nam">Embed</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t77" href="#t77">77</a></span><span class="t">                <span class="nam">title</span><span class="op">=</span><span class="str">"&#129302; Enhanced Bot Status Dashboard"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t78" href="#t78">78</a></span><span class="t">                <span class="nam">description</span><span class="op">=</span><span class="str">"Comprehensive health and performance overview"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t79" href="#t79">79</a></span><span class="t">                <span class="nam">color</span><span class="op">=</span><span class="nam">color</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t80" href="#t80">80</a></span><span class="t">                <span class="nam">timestamp</span><span class="op">=</span><span class="nam">datetime</span><span class="op">.</span><span class="nam">utcnow</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t81" href="#t81">81</a></span><span class="t">            <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t82" href="#t82">82</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t83" href="#t83">83</a></span><span class="t">            <span class="com"># Basic Bot Info</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t84" href="#t84">84</a></span><span class="t">            <span class="nam">embed</span><span class="op">.</span><span class="nam">add_field</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t85" href="#t85">85</a></span><span class="t">                <span class="nam">name</span><span class="op">=</span><span class="str">"&#127955; Connectivity"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t86" href="#t86">86</a></span><span class="t">                <span class="nam">value</span><span class="op">=</span><span class="fst">f"</span><span class="fst">**Websocket Latency**: </span><span class="op">{</span><span class="nam">latency</span><span class="op">}</span><span class="fst">ms\n</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t87" href="#t87">87</a></span><span class="t">                      <span class="fst">f"</span><span class="fst">**API Response**: </span><span class="op">{</span><span class="nam">monitor_stats</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">'api_latency'</span><span class="op">,</span> <span class="str">'N/A'</span><span class="op">)</span><span class="op">}</span><span class="fst">ms</span><span class="fst">"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t88" href="#t88">88</a></span><span class="t">                <span class="nam">inline</span><span class="op">=</span><span class="key">True</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t89" href="#t89">89</a></span><span class="t">            <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t90" href="#t90">90</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t91" href="#t91">91</a></span><span class="t">            <span class="nam">embed</span><span class="op">.</span><span class="nam">add_field</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t92" href="#t92">92</a></span><span class="t">                <span class="nam">name</span><span class="op">=</span><span class="str">"&#127760; Presence"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t93" href="#t93">93</a></span><span class="t">                <span class="nam">value</span><span class="op">=</span><span class="fst">f"</span><span class="fst">**Servers**: </span><span class="op">{</span><span class="nam">guild_count</span><span class="op">}</span><span class="fst"> (</span><span class="op">{</span><span class="nam">active_guilds</span><span class="op">}</span><span class="fst"> active)\n</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t94" href="#t94">94</a></span><span class="t">                      <span class="fst">f"</span><span class="fst">**Users**: </span><span class="op">{</span><span class="nam">user_count</span><span class="op">:</span><span class="fst">,</span><span class="op">}</span><span class="fst">\n</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t95" href="#t95">95</a></span><span class="t">                      <span class="fst">f"</span><span class="fst">**Active Users (1h)**: </span><span class="op">{</span><span class="nam">monitor_stats</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">'active_users'</span><span class="op">,</span> <span class="num">0</span><span class="op">)</span><span class="op">:</span><span class="fst">,</span><span class="op">}</span><span class="fst">"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t96" href="#t96">96</a></span><span class="t">                <span class="nam">inline</span><span class="op">=</span><span class="key">True</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t97" href="#t97">97</a></span><span class="t">            <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t98" href="#t98">98</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t99" href="#t99">99</a></span><span class="t">            <span class="com"># Uptime</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t100" href="#t100">100</a></span><span class="t">            <span class="nam">embed</span><span class="op">.</span><span class="nam">add_field</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t101" href="#t101">101</a></span><span class="t">                <span class="nam">name</span><span class="op">=</span><span class="str">"&#9201;&#65039; Uptime"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t102" href="#t102">102</a></span><span class="t">                <span class="nam">value</span><span class="op">=</span><span class="nam">uptime_str</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t103" href="#t103">103</a></span><span class="t">                <span class="nam">inline</span><span class="op">=</span><span class="key">True</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t104" href="#t104">104</a></span><span class="t">            <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t105" href="#t105">105</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t106" href="#t106">106</a></span><span class="t">            <span class="com"># Service Health</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t107" href="#t107">107</a></span><span class="t">            <span class="nam">services_status</span> <span class="op">=</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_get_enhanced_service_status</span><span class="op">(</span><span class="nam">detailed</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t108" href="#t108">108</a></span><span class="t">            <span class="nam">embed</span><span class="op">.</span><span class="nam">add_field</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t109" href="#t109">109</a></span><span class="t">                <span class="nam">name</span><span class="op">=</span><span class="str">"&#128295; Services Health"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t110" href="#t110">110</a></span><span class="t">                <span class="nam">value</span><span class="op">=</span><span class="nam">services_status</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t111" href="#t111">111</a></span><span class="t">                <span class="nam">inline</span><span class="op">=</span><span class="key">False</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t112" href="#t112">112</a></span><span class="t">            <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t113" href="#t113">113</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t114" href="#t114">114</a></span><span class="t">            <span class="com"># Database</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t115" href="#t115">115</a></span><span class="t">            <span class="nam">db_emoji</span> <span class="op">=</span> <span class="str">"&#128994;"</span> <span class="key">if</span> <span class="nam">db_status</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">"connected"</span><span class="op">)</span> <span class="key">else</span> <span class="str">"&#128308;"</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t116" href="#t116">116</a></span><span class="t">            <span class="nam">embed</span><span class="op">.</span><span class="nam">add_field</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t117" href="#t117">117</a></span><span class="t">                <span class="nam">name</span><span class="op">=</span><span class="fst">f"</span><span class="op">{</span><span class="nam">db_emoji</span><span class="op">}</span><span class="fst"> Database</span><span class="fst">"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t118" href="#t118">118</a></span><span class="t">                <span class="nam">value</span><span class="op">=</span><span class="fst">f"</span><span class="fst">**Status**: </span><span class="op">{</span><span class="nam">db_status</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">'status'</span><span class="op">,</span> <span class="str">'Unknown'</span><span class="op">)</span><span class="op">}</span><span class="fst">\n</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t119" href="#t119">119</a></span><span class="t">                      <span class="fst">f"</span><span class="fst">**Connections**: </span><span class="op">{</span><span class="nam">db_status</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">'active_connections'</span><span class="op">,</span> <span class="num">0</span><span class="op">)</span><span class="op">}</span><span class="fst">/</span><span class="op">{</span><span class="nam">db_status</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">'max_connections'</span><span class="op">,</span> <span class="num">0</span><span class="op">)</span><span class="op">}</span><span class="fst">\n</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t120" href="#t120">120</a></span><span class="t">                      <span class="fst">f"</span><span class="fst">**Last Query**: </span><span class="op">{</span><span class="nam">db_status</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">'last_query_time'</span><span class="op">,</span> <span class="str">'N/A'</span><span class="op">)</span><span class="op">}</span><span class="fst">"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t121" href="#t121">121</a></span><span class="t">                <span class="nam">inline</span><span class="op">=</span><span class="key">True</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t122" href="#t122">122</a></span><span class="t">            <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t123" href="#t123">123</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t124" href="#t124">124</a></span><span class="t">            <span class="com"># Performance Snapshot if detailed</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t125" href="#t125">125</a></span><span class="t">            <span class="key">if</span> <span class="nam">detailed</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t126" href="#t126">126</a></span><span class="t">                <span class="nam">perf_snapshot</span> <span class="op">=</span> <span class="key">await</span> <span class="nam">get_current_performance</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t127" href="#t127">127</a></span><span class="t">                <span class="nam">perf_emoji</span> <span class="op">=</span> <span class="str">"&#128994;"</span> <span class="key">if</span> <span class="nam">perf_snapshot</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">"health_score"</span><span class="op">,</span> <span class="num">0</span><span class="op">)</span> <span class="op">></span> <span class="num">80</span> <span class="key">else</span> <span class="str">"&#128993;"</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t128" href="#t128">128</a></span><span class="t">                <span class="nam">embed</span><span class="op">.</span><span class="nam">add_field</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t129" href="#t129">129</a></span><span class="t">                    <span class="nam">name</span><span class="op">=</span><span class="fst">f"</span><span class="op">{</span><span class="nam">perf_emoji</span><span class="op">}</span><span class="fst"> Performance Snapshot</span><span class="fst">"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t130" href="#t130">130</a></span><span class="t">                    <span class="nam">value</span><span class="op">=</span><span class="fst">f"</span><span class="fst">**Health Score**: </span><span class="op">{</span><span class="nam">perf_snapshot</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">'health_score'</span><span class="op">,</span> <span class="num">0</span><span class="op">)</span><span class="op">}</span><span class="fst">/100\n</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t131" href="#t131">131</a></span><span class="t">                          <span class="fst">f"</span><span class="fst">**Load Average**: </span><span class="op">{</span><span class="nam">perf_snapshot</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">'load_avg'</span><span class="op">,</span> <span class="str">'N/A'</span><span class="op">)</span><span class="op">}</span><span class="fst">\n</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t132" href="#t132">132</a></span><span class="t">                          <span class="fst">f"</span><span class="fst">**Memory**: </span><span class="op">{</span><span class="nam">perf_snapshot</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">'memory_usage'</span><span class="op">,</span> <span class="num">0</span><span class="op">)</span><span class="op">:</span><span class="fst">.1f</span><span class="op">}</span><span class="fst">%\n</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t133" href="#t133">133</a></span><span class="t">                          <span class="fst">f"</span><span class="fst">**Recent Errors**: </span><span class="op">{</span><span class="nam">perf_snapshot</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">'error_rate_5min'</span><span class="op">,</span> <span class="num">0</span><span class="op">)</span><span class="op">:</span><span class="fst">.2f</span><span class="op">}</span><span class="fst">%</span><span class="fst">"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t134" href="#t134">134</a></span><span class="t">                    <span class="nam">inline</span><span class="op">=</span><span class="key">False</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t135" href="#t135">135</a></span><span class="t">                <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t136" href="#t136">136</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t137" href="#t137">137</a></span><span class="t">                <span class="com"># Active alerts</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t138" href="#t138">138</a></span><span class="t">                <span class="nam">active_alerts</span> <span class="op">=</span> <span class="key">await</span> <span class="nam">self</span><span class="op">.</span><span class="nam">bot_monitor</span><span class="op">.</span><span class="nam">get_active_alerts</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t139" href="#t139">139</a></span><span class="t">                <span class="nam">alert_count</span> <span class="op">=</span> <span class="nam">len</span><span class="op">(</span><span class="nam">active_alerts</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t140" href="#t140">140</a></span><span class="t">                <span class="nam">alert_status</span> <span class="op">=</span> <span class="fst">f"</span><span class="op">{</span><span class="nam">alert_count</span><span class="op">}</span><span class="fst"> active alerts</span><span class="fst">"</span> <span class="key">if</span> <span class="nam">alert_count</span> <span class="op">></span> <span class="num">0</span> <span class="key">else</span> <span class="str">"No active alerts"</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t141" href="#t141">141</a></span><span class="t">                <span class="nam">embed</span><span class="op">.</span><span class="nam">add_field</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t142" href="#t142">142</a></span><span class="t">                    <span class="nam">name</span><span class="op">=</span><span class="str">"&#128680; Monitoring Alerts"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t143" href="#t143">143</a></span><span class="t">                    <span class="nam">value</span><span class="op">=</span><span class="nam">alert_status</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t144" href="#t144">144</a></span><span class="t">                    <span class="nam">inline</span><span class="op">=</span><span class="key">True</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t145" href="#t145">145</a></span><span class="t">                <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t146" href="#t146">146</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t147" href="#t147">147</a></span><span class="t">            <span class="com"># Message rate and activity</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t148" href="#t148">148</a></span><span class="t">            <span class="nam">msg_rate</span> <span class="op">=</span> <span class="nam">monitor_stats</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">"message_rate"</span><span class="op">,</span> <span class="num">0</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t149" href="#t149">149</a></span><span class="t">            <span class="nam">embed</span><span class="op">.</span><span class="nam">add_field</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t150" href="#t150">150</a></span><span class="t">                <span class="nam">name</span><span class="op">=</span><span class="str">"&#128172; Activity"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t151" href="#t151">151</a></span><span class="t">                <span class="nam">value</span><span class="op">=</span><span class="fst">f"</span><span class="fst">**Messages/min**: </span><span class="op">{</span><span class="nam">msg_rate</span><span class="op">}</span><span class="fst">\n</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t152" href="#t152">152</a></span><span class="t">                      <span class="fst">f"</span><span class="fst">**Commands/min**: </span><span class="op">{</span><span class="nam">monitor_stats</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">'command_rate'</span><span class="op">,</span> <span class="num">0</span><span class="op">)</span><span class="op">}</span><span class="fst">\n</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t153" href="#t153">153</a></span><span class="t">                      <span class="fst">f"</span><span class="fst">**Error Rate**: </span><span class="op">{</span><span class="nam">monitor_stats</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">'error_rate'</span><span class="op">,</span> <span class="num">0</span><span class="op">)</span><span class="op">:</span><span class="fst">.2f</span><span class="op">}</span><span class="fst">%</span><span class="fst">"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t154" href="#t154">154</a></span><span class="t">                <span class="nam">inline</span><span class="op">=</span><span class="key">True</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t155" href="#t155">155</a></span><span class="t">            <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t156" href="#t156">156</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t157" href="#t157">157</a></span><span class="t">            <span class="nam">embed</span><span class="op">.</span><span class="nam">set_footer</span><span class="op">(</span><span class="nam">text</span><span class="op">=</span><span class="str">"Status generated with real-time monitoring | Detailed view enabled"</span> <span class="key">if</span> <span class="nam">detailed</span> <span class="key">else</span> <span class="str">"Basic status | Use /status detailed for more info"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t158" href="#t158">158</a></span><span class="t">            <span class="key">await</span> <span class="nam">interaction</span><span class="op">.</span><span class="nam">followup</span><span class="op">.</span><span class="nam">send</span><span class="op">(</span><span class="nam">embed</span><span class="op">=</span><span class="nam">embed</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t159" href="#t159">159</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t160" href="#t160">160</a></span><span class="t">        <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t161" href="#t161">161</a></span><span class="t">            <span class="key">await</span> <span class="nam">log_and_notify_error</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t162" href="#t162">162</a></span><span class="t">                <span class="nam">e</span><span class="op">,</span> <span class="nam">interaction</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t163" href="#t163">163</a></span><span class="t">                <span class="str">"Error generating status"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t164" href="#t164">164</a></span><span class="t">                <span class="nam">fallback_message</span><span class="op">=</span><span class="str">"&#10060; Status check failed. Bot is online but services may be impacted."</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t165" href="#t165">165</a></span><span class="t">                <span class="nam">ephemeral</span><span class="op">=</span><span class="nam">detailed</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t166" href="#t166">166</a></span><span class="t">            <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t167" href="#t167">167</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t168" href="#t168">168</a></span><span class="t">    <span class="key">async</span> <span class="key">def</span> <span class="nam">_calculate_uptime</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">period</span><span class="op">:</span> <span class="nam">str</span><span class="op">)</span> <span class="op">-></span> <span class="nam">str</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t169" href="#t169">169</a></span><span class="t">        <span class="str">"""Calculate uptime for period."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t170" href="#t170">170</a></span><span class="t">        <span class="nam">uptime_data</span> <span class="op">=</span> <span class="key">await</span> <span class="nam">handle_error_with_fallback</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t171" href="#t171">171</a></span><span class="t">            <span class="nam">self</span><span class="op">.</span><span class="nam">bot_monitor</span><span class="op">.</span><span class="nam">get_uptime</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t172" href="#t172">172</a></span><span class="t">            <span class="nam">default</span><span class="op">=</span><span class="op">{</span><span class="str">"uptime_hours"</span><span class="op">:</span> <span class="num">0</span><span class="op">,</span> <span class="str">"downtime_events"</span><span class="op">:</span> <span class="num">0</span><span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t173" href="#t173">173</a></span><span class="t">        <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t174" href="#t174">174</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t175" href="#t175">175</a></span><span class="t">        <span class="key">if</span> <span class="nam">period</span> <span class="op">==</span> <span class="str">"24h"</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t176" href="#t176">176</a></span><span class="t">            <span class="nam">uptime_pct</span> <span class="op">=</span> <span class="nam">uptime_data</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">"uptime_24h"</span><span class="op">,</span> <span class="num">100</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t177" href="#t177">177</a></span><span class="t">            <span class="nam">downtime</span> <span class="op">=</span> <span class="nam">uptime_data</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">"downtime_24h_minutes"</span><span class="op">,</span> <span class="num">0</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t178" href="#t178">178</a></span><span class="t">        <span class="key">elif</span> <span class="nam">period</span> <span class="op">==</span> <span class="str">"7d"</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t179" href="#t179">179</a></span><span class="t">            <span class="nam">uptime_pct</span> <span class="op">=</span> <span class="nam">uptime_data</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">"uptime_7d"</span><span class="op">,</span> <span class="num">100</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t180" href="#t180">180</a></span><span class="t">            <span class="nam">downtime</span> <span class="op">=</span> <span class="nam">uptime_data</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">"downtime_7d_hours"</span><span class="op">,</span> <span class="num">0</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t181" href="#t181">181</a></span><span class="t">        <span class="key">elif</span> <span class="nam">period</span> <span class="op">==</span> <span class="str">"30d"</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t182" href="#t182">182</a></span><span class="t">            <span class="nam">uptime_pct</span> <span class="op">=</span> <span class="nam">uptime_data</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">"uptime_30d"</span><span class="op">,</span> <span class="num">100</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t183" href="#t183">183</a></span><span class="t">            <span class="nam">downtime</span> <span class="op">=</span> <span class="nam">uptime_data</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">"downtime_30d_days"</span><span class="op">,</span> <span class="num">0</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t184" href="#t184">184</a></span><span class="t">        <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t185" href="#t185">185</a></span><span class="t">            <span class="nam">uptime_pct</span> <span class="op">=</span> <span class="num">100</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t186" href="#t186">186</a></span><span class="t">            <span class="nam">downtime</span> <span class="op">=</span> <span class="num">0</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t187" href="#t187">187</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t188" href="#t188">188</a></span><span class="t">        <span class="nam">uptime_emoji</span> <span class="op">=</span> <span class="str">"&#128994;"</span> <span class="key">if</span> <span class="nam">uptime_pct</span> <span class="op">></span> <span class="num">99</span> <span class="key">else</span> <span class="str">"&#128993;"</span> <span class="key">if</span> <span class="nam">uptime_pct</span> <span class="op">></span> <span class="num">95</span> <span class="key">else</span> <span class="str">"&#128308;"</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t189" href="#t189">189</a></span><span class="t">        <span class="key">return</span> <span class="fst">f"</span><span class="op">{</span><span class="nam">uptime_emoji</span><span class="op">}</span><span class="fst"> **</span><span class="op">{</span><span class="nam">uptime_pct</span><span class="op">:</span><span class="fst">.2f</span><span class="op">}</span><span class="fst">%** uptime\nDowntime: </span><span class="op">{</span><span class="nam">downtime</span><span class="op">}</span><span class="fst"> </span><span class="op">{</span><span class="str">'min'</span> <span class="key">if</span> <span class="nam">period</span><span class="op">==</span><span class="str">'24h'</span> <span class="key">else</span> <span class="str">'h'</span> <span class="key">if</span> <span class="nam">period</span><span class="op">==</span><span class="str">'7d'</span> <span class="key">else</span> <span class="str">'d'</span><span class="op">}</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t190" href="#t190">190</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t191" href="#t191">191</a></span><span class="t">    <span class="key">def</span> <span class="nam">_get_enhanced_service_status</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">detailed</span><span class="op">:</span> <span class="nam">bool</span><span class="op">)</span> <span class="op">-></span> <span class="nam">str</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t192" href="#t192">192</a></span><span class="t">        <span class="str">"""Get service status with new monitor."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t193" href="#t193">193</a></span><span class="t">        <span class="key">if</span> <span class="key">not</span> <span class="nam">detailed</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t194" href="#t194">194</a></span><span class="t">            <span class="key">return</span> <span class="str">"&#128994; All core services operational (detailed view for breakdown)"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t195" href="#t195">195</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t196" href="#t196">196</a></span><span class="t">        <span class="com"># Use bot_monitor for detailed</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t197" href="#t197">197</a></span><span class="t">        <span class="nam">service_statuses</span> <span class="op">=</span> <span class="nam">self</span><span class="op">.</span><span class="nam">bot_monitor</span><span class="op">.</span><span class="nam">get_service_statuses</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t198" href="#t198">198</a></span><span class="t">        <span class="nam">status_lines</span> <span class="op">=</span> <span class="op">[</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t199" href="#t199">199</a></span><span class="t">        <span class="key">for</span> <span class="nam">service</span><span class="op">,</span> <span class="nam">status</span> <span class="key">in</span> <span class="nam">service_statuses</span><span class="op">.</span><span class="nam">items</span><span class="op">(</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t200" href="#t200">200</a></span><span class="t">            <span class="nam">emoji</span> <span class="op">=</span> <span class="str">"&#128994;"</span> <span class="key">if</span> <span class="nam">status</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">"healthy"</span><span class="op">)</span> <span class="key">else</span> <span class="str">"&#128993;"</span> <span class="key">if</span> <span class="nam">status</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">"degraded"</span><span class="op">)</span> <span class="key">else</span> <span class="str">"&#128308;"</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t201" href="#t201">201</a></span><span class="t">            <span class="nam">details</span> <span class="op">=</span> <span class="nam">status</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">"details"</span><span class="op">,</span> <span class="str">"OK"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t202" href="#t202">202</a></span><span class="t">            <span class="nam">status_lines</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="fst">f"</span><span class="op">{</span><span class="nam">emoji</span><span class="op">}</span><span class="fst"> **</span><span class="op">{</span><span class="nam">service</span><span class="op">.</span><span class="nam">replace</span><span class="op">(</span><span class="str">'_'</span><span class="op">,</span> <span class="str">' '</span><span class="op">)</span><span class="op">.</span><span class="nam">title</span><span class="op">(</span><span class="op">)</span><span class="op">}</span><span class="fst">**: </span><span class="op">{</span><span class="nam">details</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t203" href="#t203">203</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t204" href="#t204">204</a></span><span class="t">        <span class="key">return</span> <span class="str">"\n"</span><span class="op">.</span><span class="nam">join</span><span class="op">(</span><span class="nam">status_lines</span><span class="op">)</span> <span class="key">if</span> <span class="nam">status_lines</span> <span class="key">else</span> <span class="str">"No service details available"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t205" href="#t205">205</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t206" href="#t206">206</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t207" href="#t207">207</a></span><span class="t"><span class="key">async</span> <span class="key">def</span> <span class="nam">setup</span><span class="op">(</span><span class="nam">bot</span><span class="op">:</span> <span class="nam">commands</span><span class="op">.</span><span class="nam">Bot</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t208" href="#t208">208</a></span><span class="t">    <span class="str">"""Setup enhanced status extension."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t209" href="#t209">209</a></span><span class="t">    <span class="key">await</span> <span class="nam">bot</span><span class="op">.</span><span class="nam">add_cog</span><span class="op">(</span><span class="nam">StatusCommand</span><span class="op">(</span><span class="nam">bot</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t210" href="#t210">210</a></span><span class="t">    <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"&#9989; Enhanced Status cog loaded with monitoring integration"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="z_8b651bcae9720a9b_recommendations_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a class="nav" href="z_8b651bcae9720a9b_utility_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.6">coverage.py v7.10.6</a>,
            created at 2025-09-28 09:58 -0400
        </p>
    </div>
</footer>
</body>
</html>
