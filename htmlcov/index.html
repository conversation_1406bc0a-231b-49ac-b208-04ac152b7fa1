<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_6b508a39.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">8%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button current">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.6">coverage.py v7.10.6</a>,
            created at 2025-09-28 11:00 -0400
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6___init___py.html">src/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c491fc8e7f435b94___init___py.html">src/analysis/__init__.py</a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_502cac173ba52a86___init___py.html">src/analysis/ai/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fbbe37d21d225bb3___init___py.html">src/analysis/ai/calculators/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fbbe37d21d225bb3_sentiment_calculator_py.html">src/analysis/ai/calculators/sentiment_calculator.py</a></td>
                <td>38</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="0 38">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_502cac173ba52a86_enhancement_strategy_py.html">src/analysis/ai/enhancement_strategy.py</a></td>
                <td>45</td>
                <td>45</td>
                <td>0</td>
                <td class="right" data-ratio="0 45">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_502cac173ba52a86_ml_models_py.html">src/analysis/ai/ml_models.py</a></td>
                <td>294</td>
                <td>294</td>
                <td>0</td>
                <td class="right" data-ratio="0 294">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_502cac173ba52a86_ml_training_service_py.html">src/analysis/ai/ml_training_service.py</a></td>
                <td>72</td>
                <td>72</td>
                <td>0</td>
                <td class="right" data-ratio="0 72">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_502cac173ba52a86_recommendation_engine_py.html">src/analysis/ai/recommendation_engine.py</a></td>
                <td>355</td>
                <td>355</td>
                <td>0</td>
                <td class="right" data-ratio="0 355">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c491fc8e7f435b94_enhanced_evaluator_py.html">src/analysis/enhanced_evaluator.py</a></td>
                <td>392</td>
                <td>392</td>
                <td>2</td>
                <td class="right" data-ratio="0 392">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_059825b5ec61c55c___init___py.html">src/analysis/fundamental/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d055e12b06a86006___init___py.html">src/analysis/fundamental/calculators/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d055e12b06a86006_growth_calculator_py.html">src/analysis/fundamental/calculators/growth_calculator.py</a></td>
                <td>114</td>
                <td>114</td>
                <td>0</td>
                <td class="right" data-ratio="0 114">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d055e12b06a86006_pe_calculator_py.html">src/analysis/fundamental/calculators/pe_calculator.py</a></td>
                <td>62</td>
                <td>62</td>
                <td>0</td>
                <td class="right" data-ratio="0 62">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_059825b5ec61c55c_metrics_py.html">src/analysis/fundamental/metrics.py</a></td>
                <td>105</td>
                <td>105</td>
                <td>0</td>
                <td class="right" data-ratio="0 105">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60d25a6d9f4f2ff6___init___py.html">src/analysis/orchestration/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60d25a6d9f4f2ff6_analysis_orchestrator_py.html">src/analysis/orchestration/analysis_orchestrator.py</a></td>
                <td>204</td>
                <td>204</td>
                <td>0</td>
                <td class="right" data-ratio="0 204">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60d25a6d9f4f2ff6_enhancement_strategy_py.html">src/analysis/orchestration/enhancement_strategy.py</a></td>
                <td>48</td>
                <td>48</td>
                <td>0</td>
                <td class="right" data-ratio="0 48">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bca56fec82a9a03a___init___py.html">src/analysis/probability/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bca56fec82a9a03a_monte_carlo_simulator_py.html">src/analysis/probability/monte_carlo_simulator.py</a></td>
                <td>151</td>
                <td>151</td>
                <td>0</td>
                <td class="right" data-ratio="0 151">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bca56fec82a9a03a_probability_engine_py.html">src/analysis/probability/probability_engine.py</a></td>
                <td>342</td>
                <td>342</td>
                <td>0</td>
                <td class="right" data-ratio="0 342">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bca56fec82a9a03a_probability_response_service_py.html">src/analysis/probability/probability_response_service.py</a></td>
                <td>119</td>
                <td>119</td>
                <td>0</td>
                <td class="right" data-ratio="0 119">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cd87d0a98354ad1b___init___py.html">src/analysis/risk/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cd87d0a98354ad1b_assessment_py.html">src/analysis/risk/assessment.py</a></td>
                <td>93</td>
                <td>93</td>
                <td>0</td>
                <td class="right" data-ratio="0 93">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d03366fc80f3d5f7___init___py.html">src/analysis/risk/calculators/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d03366fc80f3d5f7_beta_calculator_py.html">src/analysis/risk/calculators/beta_calculator.py</a></td>
                <td>95</td>
                <td>95</td>
                <td>0</td>
                <td class="right" data-ratio="0 95">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d03366fc80f3d5f7_volatility_calculator_py.html">src/analysis/risk/calculators/volatility_calculator.py</a></td>
                <td>80</td>
                <td>80</td>
                <td>0</td>
                <td class="right" data-ratio="0 80">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cd87d0a98354ad1b_enhanced_risk_assessment_py.html">src/analysis/risk/enhanced_risk_assessment.py</a></td>
                <td>216</td>
                <td>216</td>
                <td>0</td>
                <td class="right" data-ratio="0 216">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7e16ef80c1feda06___init___py.html">src/analysis/technical/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_52b1575cbddbb4a9___init___py.html">src/analysis/templates/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_52b1575cbddbb4a9_analysis_response_template_py.html">src/analysis/templates/analysis_response_template.py</a></td>
                <td>99</td>
                <td>99</td>
                <td>32</td>
                <td class="right" data-ratio="0 99">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6b3b45bdd92f6a4a___init___py.html">src/analysis/utils/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6b3b45bdd92f6a4a_data_validators_py.html">src/analysis/utils/data_validators.py</a></td>
                <td>124</td>
                <td>124</td>
                <td>0</td>
                <td class="right" data-ratio="0 124">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0e24b7f04f99860___init___py.html">src/api/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae5c5dec56ba18a8___init___py.html">src/api/analytics/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0e24b7f04f99860_config_py.html">src/api/config.py</a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8b1f68d7960fbdc___init___py.html">src/api/data/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8b1f68d7960fbdc_cache_py.html">src/api/data/cache.py</a></td>
                <td>179</td>
                <td>179</td>
                <td>0</td>
                <td class="right" data-ratio="0 179">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8b1f68d7960fbdc_cache_warming_scheduler_py.html">src/api/data/cache_warming_scheduler.py</a></td>
                <td>124</td>
                <td>124</td>
                <td>0</td>
                <td class="right" data-ratio="0 124">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8b1f68d7960fbdc_constants_py.html">src/api/data/constants.py</a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8b1f68d7960fbdc_market_data_service_py.html">src/api/data/market_data_service.py</a></td>
                <td>70</td>
                <td>70</td>
                <td>0</td>
                <td class="right" data-ratio="0 70">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8b1f68d7960fbdc_metrics_py.html">src/api/data/metrics.py</a></td>
                <td>209</td>
                <td>209</td>
                <td>0</td>
                <td class="right" data-ratio="0 209">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9288a479c37533a0___init___py.html">src/api/data/providers/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9288a479c37533a0_alpha_vantage_py.html">src/api/data/providers/alpha_vantage.py</a></td>
                <td>95</td>
                <td>95</td>
                <td>0</td>
                <td class="right" data-ratio="0 95">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9288a479c37533a0_base_py.html">src/api/data/providers/base.py</a></td>
                <td>133</td>
                <td>133</td>
                <td>14</td>
                <td class="right" data-ratio="0 133">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9288a479c37533a0_data_source_manager_py.html">src/api/data/providers/data_source_manager.py</a></td>
                <td>139</td>
                <td>139</td>
                <td>0</td>
                <td class="right" data-ratio="0 139">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9288a479c37533a0_finnhub_py.html">src/api/data/providers/finnhub.py</a></td>
                <td>94</td>
                <td>94</td>
                <td>0</td>
                <td class="right" data-ratio="0 94">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6051117ca093f979___init___py.html">src/api/data/providers/modules/__init__.py</a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6051117ca093f979_auditing_py.html">src/api/data/providers/modules/auditing.py</a></td>
                <td>59</td>
                <td>59</td>
                <td>0</td>
                <td class="right" data-ratio="0 59">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6051117ca093f979_config_py.html">src/api/data/providers/modules/config.py</a></td>
                <td>83</td>
                <td>83</td>
                <td>0</td>
                <td class="right" data-ratio="0 83">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6051117ca093f979_rate_limiting_py.html">src/api/data/providers/modules/rate_limiting.py</a></td>
                <td>73</td>
                <td>73</td>
                <td>0</td>
                <td class="right" data-ratio="0 73">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6051117ca093f979_validation_py.html">src/api/data/providers/modules/validation.py</a></td>
                <td>168</td>
                <td>168</td>
                <td>0</td>
                <td class="right" data-ratio="0 168">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9288a479c37533a0_polygon_py.html">src/api/data/providers/polygon.py</a></td>
                <td>167</td>
                <td>167</td>
                <td>0</td>
                <td class="right" data-ratio="0 167">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8b1f68d7960fbdc_scheduled_tasks_py.html">src/api/data/scheduled_tasks.py</a></td>
                <td>132</td>
                <td>132</td>
                <td>0</td>
                <td class="right" data-ratio="0 132">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0e24b7f04f99860_main_py.html">src/api/main.py</a></td>
                <td>58</td>
                <td>58</td>
                <td>5</td>
                <td class="right" data-ratio="0 58">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8868f58259af6c17___init___py.html">src/api/middleware/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8868f58259af6c17_advanced_security_py.html">src/api/middleware/advanced_security.py</a></td>
                <td>225</td>
                <td>225</td>
                <td>0</td>
                <td class="right" data-ratio="0 225">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8868f58259af6c17_security_py.html">src/api/middleware/security.py</a></td>
                <td>102</td>
                <td>102</td>
                <td>0</td>
                <td class="right" data-ratio="0 102">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8868f58259af6c17_security_utils_py.html">src/api/middleware/security_utils.py</a></td>
                <td>35</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="0 35">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5d636249625465ac___init___py.html">src/api/routers/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5d636249625465ac_market_data_py.html">src/api/routers/market_data.py</a></td>
                <td>65</td>
                <td>65</td>
                <td>0</td>
                <td class="right" data-ratio="0 65">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f57f923ae69d111___init___py.html">src/api/routes/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f57f923ae69d111_analytics_py.html">src/api/routes/analytics.py</a></td>
                <td>151</td>
                <td>151</td>
                <td>0</td>
                <td class="right" data-ratio="0 151">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f57f923ae69d111_bot_health_py.html">src/api/routes/bot_health.py</a></td>
                <td>107</td>
                <td>107</td>
                <td>0</td>
                <td class="right" data-ratio="0 107">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f57f923ae69d111_dashboard_py.html">src/api/routes/dashboard.py</a></td>
                <td>57</td>
                <td>57</td>
                <td>0</td>
                <td class="right" data-ratio="0 57">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f57f923ae69d111_debug_py.html">src/api/routes/debug.py</a></td>
                <td>35</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="0 35">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f57f923ae69d111_feedback_py.html">src/api/routes/feedback.py</a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f57f923ae69d111_health_py.html">src/api/routes/health.py</a></td>
                <td>77</td>
                <td>77</td>
                <td>0</td>
                <td class="right" data-ratio="0 77">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f57f923ae69d111_market_data_py.html">src/api/routes/market_data.py</a></td>
                <td>113</td>
                <td>113</td>
                <td>0</td>
                <td class="right" data-ratio="0 113">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f57f923ae69d111_metrics_py.html">src/api/routes/metrics.py</a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9ab2fed7f6b8459c___init___py.html">src/api/schemas/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9ab2fed7f6b8459c_feedback_schema_py.html">src/api/schemas/feedback_schema.py</a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9ab2fed7f6b8459c_metrics_schema_py.html">src/api/schemas/metrics_schema.py</a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2839512a6d00c75b___init___py.html">src/api/webhooks/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93b1c7bd489fca84___init___py.html">src/bot/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_06e375abcc969e3f___init___py.html">src/bot/audit/__init__.py</a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_06e375abcc969e3f_audit_service_py.html">src/bot/audit/audit_service.py</a></td>
                <td>179</td>
                <td>179</td>
                <td>0</td>
                <td class="right" data-ratio="0 179">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_06e375abcc969e3f_example_usage_py.html">src/bot/audit/example_usage.py</a></td>
                <td>42</td>
                <td>42</td>
                <td>10</td>
                <td class="right" data-ratio="0 42">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_06e375abcc969e3f_rate_limiter_py.html">src/bot/audit/rate_limiter.py</a></td>
                <td>215</td>
                <td>215</td>
                <td>0</td>
                <td class="right" data-ratio="0 215">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_06e375abcc969e3f_request_visualizer_py.html">src/bot/audit/request_visualizer.py</a></td>
                <td>145</td>
                <td>145</td>
                <td>0</td>
                <td class="right" data-ratio="0 145">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_06e375abcc969e3f_session_manager_py.html">src/bot/audit/session_manager.py</a></td>
                <td>171</td>
                <td>171</td>
                <td>0</td>
                <td class="right" data-ratio="0 171">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_06e375abcc969e3f_setup_audit_py.html">src/bot/audit/setup_audit.py</a></td>
                <td>94</td>
                <td>94</td>
                <td>0</td>
                <td class="right" data-ratio="0 94">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93b1c7bd489fca84_client_py.html">src/bot/client.py</a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93b1c7bd489fca84_client_audit_integration_py.html">src/bot/client_audit_integration.py</a></td>
                <td>76</td>
                <td>76</td>
                <td>0</td>
                <td class="right" data-ratio="0 76">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93b1c7bd489fca84_client_with_monitoring_py.html">src/bot/client_with_monitoring.py</a></td>
                <td>132</td>
                <td>132</td>
                <td>0</td>
                <td class="right" data-ratio="0 132">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a14d6168cd383d17___init___py.html">src/bot/core/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a14d6168cd383d17_bot_py.html">src/bot/core/bot.py</a></td>
                <td>110</td>
                <td>110</td>
                <td>2</td>
                <td class="right" data-ratio="0 110">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a14d6168cd383d17_error_handler_py.html">src/bot/core/error_handler.py</a></td>
                <td>18</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="3 18">17%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac26b0fd80cd4a6___init___py.html">src/bot/core/security/__init__.py</a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac26b0fd80cd4a6_auth_py.html">src/bot/core/security/auth.py</a></td>
                <td>52</td>
                <td>52</td>
                <td>0</td>
                <td class="right" data-ratio="0 52">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac26b0fd80cd4a6_input_validator_py.html">src/bot/core/security/input_validator.py</a></td>
                <td>34</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac26b0fd80cd4a6_rate_limiter_py.html">src/bot/core/security/rate_limiter.py</a></td>
                <td>45</td>
                <td>45</td>
                <td>0</td>
                <td class="right" data-ratio="0 45">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac26b0fd80cd4a6_security_scanner_py.html">src/bot/core/security/security_scanner.py</a></td>
                <td>44</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="0 44">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac26b0fd80cd4a6_simplified_security_py.html">src/bot/core/security/simplified_security.py</a></td>
                <td>44</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="0 44">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a14d6168cd383d17_services_py.html">src/bot/core/services.py</a></td>
                <td>152</td>
                <td>152</td>
                <td>0</td>
                <td class="right" data-ratio="0 152">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93b1c7bd489fca84_database_manager_py.html">src/bot/database_manager.py</a></td>
                <td>72</td>
                <td>45</td>
                <td>0</td>
                <td class="right" data-ratio="27 72">38%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b651bcae9720a9b___init___py.html">src/bot/extensions/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b651bcae9720a9b_alerts_py.html">src/bot/extensions/alerts.py</a></td>
                <td>249</td>
                <td>249</td>
                <td>0</td>
                <td class="right" data-ratio="0 249">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b651bcae9720a9b_analyze_py.html">src/bot/extensions/analyze.py</a></td>
                <td>260</td>
                <td>176</td>
                <td>0</td>
                <td class="right" data-ratio="84 260">32%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b651bcae9720a9b_ask_py.html">src/bot/extensions/ask.py</a></td>
                <td>121</td>
                <td>76</td>
                <td>0</td>
                <td class="right" data-ratio="45 121">37%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b651bcae9720a9b_audit_py.html">src/bot/extensions/audit.py</a></td>
                <td>102</td>
                <td>102</td>
                <td>0</td>
                <td class="right" data-ratio="0 102">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b651bcae9720a9b_batch_analyze_py.html">src/bot/extensions/batch_analyze.py</a></td>
                <td>250</td>
                <td>250</td>
                <td>0</td>
                <td class="right" data-ratio="0 250">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b651bcae9720a9b_discord_ux_py.html">src/bot/extensions/discord_ux.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b651bcae9720a9b_error_handler_py.html">src/bot/extensions/error_handler.py</a></td>
                <td>48</td>
                <td>48</td>
                <td>0</td>
                <td class="right" data-ratio="0 48">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b651bcae9720a9b_help_py.html">src/bot/extensions/help.py</a></td>
                <td>168</td>
                <td>168</td>
                <td>0</td>
                <td class="right" data-ratio="0 168">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b651bcae9720a9b_ml_admin_py.html">src/bot/extensions/ml_admin.py</a></td>
                <td>139</td>
                <td>139</td>
                <td>0</td>
                <td class="right" data-ratio="0 139">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b651bcae9720a9b_performance_monitor_py.html">src/bot/extensions/performance_monitor.py</a></td>
                <td>284</td>
                <td>284</td>
                <td>0</td>
                <td class="right" data-ratio="0 284">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b651bcae9720a9b_pipeline_visualizer_py.html">src/bot/extensions/pipeline_visualizer.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b651bcae9720a9b_portfolio_py.html">src/bot/extensions/portfolio.py</a></td>
                <td>488</td>
                <td>488</td>
                <td>0</td>
                <td class="right" data-ratio="0 488">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b651bcae9720a9b_recommendations_py.html">src/bot/extensions/recommendations.py</a></td>
                <td>256</td>
                <td>256</td>
                <td>0</td>
                <td class="right" data-ratio="0 256">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b651bcae9720a9b_status_py.html">src/bot/extensions/status.py</a></td>
                <td>88</td>
                <td>88</td>
                <td>0</td>
                <td class="right" data-ratio="0 88">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b651bcae9720a9b_utility_py.html">src/bot/extensions/utility.py</a></td>
                <td>134</td>
                <td>65</td>
                <td>0</td>
                <td class="right" data-ratio="69 134">51%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b651bcae9720a9b_watchlist_py.html">src/bot/extensions/watchlist.py</a></td>
                <td>286</td>
                <td>286</td>
                <td>0</td>
                <td class="right" data-ratio="0 286">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b651bcae9720a9b_zones_py.html">src/bot/extensions/zones.py</a></td>
                <td>263</td>
                <td>263</td>
                <td>0</td>
                <td class="right" data-ratio="0 263">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93b1c7bd489fca84_main_py.html">src/bot/main.py</a></td>
                <td>136</td>
                <td>136</td>
                <td>6</td>
                <td class="right" data-ratio="0 136">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93b1c7bd489fca84_market_sentiment_analyzer_py.html">src/bot/market_sentiment_analyzer.py</a></td>
                <td>171</td>
                <td>171</td>
                <td>0</td>
                <td class="right" data-ratio="0 171">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93b1c7bd489fca84_metrics_collector_py.html">src/bot/metrics_collector.py</a></td>
                <td>123</td>
                <td>123</td>
                <td>0</td>
                <td class="right" data-ratio="0 123">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93b1c7bd489fca84_performance_analytics_py.html">src/bot/performance_analytics.py</a></td>
                <td>99</td>
                <td>99</td>
                <td>0</td>
                <td class="right" data-ratio="0 99">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93b1c7bd489fca84_permissions_py.html">src/bot/permissions.py</a></td>
                <td>84</td>
                <td>60</td>
                <td>0</td>
                <td class="right" data-ratio="24 84">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fddd24340cee9d05___init___py.html">src/bot/pipeline/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_334a150c392fe995___init___py.html">src/bot/pipeline/commands/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18831fe259ea92e4___init___py.html">src/bot/pipeline/commands/ask/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8c0c370aaec88e18___init___py.html">src/bot/pipeline/commands/ask/api/__init__.py</a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8c0c370aaec88e18_backward_compatibility_py.html">src/bot/pipeline/commands/ask/api/backward_compatibility.py</a></td>
                <td>260</td>
                <td>260</td>
                <td>0</td>
                <td class="right" data-ratio="0 260">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8c0c370aaec88e18_contracts_py.html">src/bot/pipeline/commands/ask/api/contracts.py</a></td>
                <td>220</td>
                <td>220</td>
                <td>0</td>
                <td class="right" data-ratio="0 220">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8c0c370aaec88e18_integration_py.html">src/bot/pipeline/commands/ask/api/integration.py</a></td>
                <td>363</td>
                <td>363</td>
                <td>12</td>
                <td class="right" data-ratio="0 363">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8c0c370aaec88e18_versioning_py.html">src/bot/pipeline/commands/ask/api/versioning.py</a></td>
                <td>208</td>
                <td>208</td>
                <td>0</td>
                <td class="right" data-ratio="0 208">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18831fe259ea92e4_audit_py.html">src/bot/pipeline/commands/ask/audit.py</a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_12806b501f857678___init___py.html">src/bot/pipeline/commands/ask/audit/__init__.py</a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_12806b501f857678_audit_logger_py.html">src/bot/pipeline/commands/ask/audit/audit_logger.py</a></td>
                <td>193</td>
                <td>111</td>
                <td>0</td>
                <td class="right" data-ratio="82 193">42%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3a43e718a1f310ec___init___py.html">src/bot/pipeline/commands/ask/cache/__init__.py</a></td>
                <td>34</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="6 34">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3a43e718a1f310ec_intelligent_cache_py.html">src/bot/pipeline/commands/ask/cache/intelligent_cache.py</a></td>
                <td>175</td>
                <td>124</td>
                <td>0</td>
                <td class="right" data-ratio="51 175">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3a43e718a1f310ec_unified_cache_py.html">src/bot/pipeline/commands/ask/cache/unified_cache.py</a></td>
                <td>142</td>
                <td>111</td>
                <td>0</td>
                <td class="right" data-ratio="31 142">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2991336f3a420345___init___py.html">src/bot/pipeline/commands/ask/cleanup/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2991336f3a420345_dead_code_analyzer_py.html">src/bot/pipeline/commands/ask/cleanup/dead_code_analyzer.py</a></td>
                <td>209</td>
                <td>209</td>
                <td>0</td>
                <td class="right" data-ratio="0 209">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2991336f3a420345_legacy_archiver_py.html">src/bot/pipeline/commands/ask/cleanup/legacy_archiver.py</a></td>
                <td>165</td>
                <td>165</td>
                <td>0</td>
                <td class="right" data-ratio="0 165">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2297a8013ac870ab___init___py.html">src/bot/pipeline/commands/ask/compliance/__init__.py</a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2297a8013ac870ab_audit_logger_py.html">src/bot/pipeline/commands/ask/compliance/audit_logger.py</a></td>
                <td>286</td>
                <td>286</td>
                <td>0</td>
                <td class="right" data-ratio="0 286">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2297a8013ac870ab_data_manager_py.html">src/bot/pipeline/commands/ask/compliance/data_manager.py</a></td>
                <td>47</td>
                <td>47</td>
                <td>0</td>
                <td class="right" data-ratio="0 47">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f24854da83285069___init___py.html">src/bot/pipeline/commands/ask/config/__init__.py</a></td>
                <td>8</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="7 8">88%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f24854da83285069_ask_config_py.html">src/bot/pipeline/commands/ask/config/ask_config.py</a></td>
                <td>316</td>
                <td>198</td>
                <td>0</td>
                <td class="right" data-ratio="118 316">37%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f24854da83285069_config_manager_py.html">src/bot/pipeline/commands/ask/config/config_manager.py</a></td>
                <td>279</td>
                <td>200</td>
                <td>0</td>
                <td class="right" data-ratio="79 279">28%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f24854da83285069_environment_profiles_py.html">src/bot/pipeline/commands/ask/config/environment_profiles.py</a></td>
                <td>373</td>
                <td>280</td>
                <td>0</td>
                <td class="right" data-ratio="93 373">25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f24854da83285069_feature_flags_py.html">src/bot/pipeline/commands/ask/config/feature_flags.py</a></td>
                <td>46</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="25 46">54%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f24854da83285069_secrets_manager_py.html">src/bot/pipeline/commands/ask/config/secrets_manager.py</a></td>
                <td>171</td>
                <td>119</td>
                <td>0</td>
                <td class="right" data-ratio="52 171">30%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7871925a5b1f2799___init___py.html">src/bot/pipeline/commands/ask/core/__init__.py</a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7871925a5b1f2799_controller_py.html">src/bot/pipeline/commands/ask/core/controller.py</a></td>
                <td>90</td>
                <td>70</td>
                <td>0</td>
                <td class="right" data-ratio="20 90">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7871925a5b1f2799_error_coordinator_py.html">src/bot/pipeline/commands/ask/core/error_coordinator.py</a></td>
                <td>226</td>
                <td>177</td>
                <td>0</td>
                <td class="right" data-ratio="49 226">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7871925a5b1f2799_models_py.html">src/bot/pipeline/commands/ask/core/models.py</a></td>
                <td>41</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="38 41">93%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7871925a5b1f2799_stage_executor_py.html">src/bot/pipeline/commands/ask/core/stage_executor.py</a></td>
                <td>134</td>
                <td>120</td>
                <td>0</td>
                <td class="right" data-ratio="14 134">10%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7871925a5b1f2799_stage_manager_py.html">src/bot/pipeline/commands/ask/core/stage_manager.py</a></td>
                <td>30</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="19 30">63%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_060a22be0368d7a2___init___py.html">src/bot/pipeline/commands/ask/cost/__init__.py</a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_060a22be0368d7a2_cost_tracker_py.html">src/bot/pipeline/commands/ask/cost/cost_tracker.py</a></td>
                <td>256</td>
                <td>256</td>
                <td>0</td>
                <td class="right" data-ratio="0 256">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_060a22be0368d7a2_resource_optimizer_py.html">src/bot/pipeline/commands/ask/cost/resource_optimizer.py</a></td>
                <td>41</td>
                <td>41</td>
                <td>0</td>
                <td class="right" data-ratio="0 41">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a2a8fd9815ec5b3a___init___py.html">src/bot/pipeline/commands/ask/deployment/__init__.py</a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a2a8fd9815ec5b3a_cicd_pipeline_py.html">src/bot/pipeline/commands/ask/deployment/cicd_pipeline.py</a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_be7e545879d6abeb___init___py.html">src/bot/pipeline/commands/ask/errors/__init__.py</a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_be7e545879d6abeb_error_handler_py.html">src/bot/pipeline/commands/ask/errors/error_handler.py</a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_be7e545879d6abeb_error_manager_py.html">src/bot/pipeline/commands/ask/errors/error_manager.py</a></td>
                <td>158</td>
                <td>158</td>
                <td>0</td>
                <td class="right" data-ratio="0 158">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_be7e545879d6abeb_fallback_strategy_py.html">src/bot/pipeline/commands/ask/errors/fallback_strategy.py</a></td>
                <td>107</td>
                <td>107</td>
                <td>0</td>
                <td class="right" data-ratio="0 107">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18831fe259ea92e4_executor_py.html">src/bot/pipeline/commands/ask/executor.py</a></td>
                <td>42</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="15 42">36%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2ab3a6815f15944e___init___py.html">src/bot/pipeline/commands/ask/observability/__init__.py</a></td>
                <td>35</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="16 35">46%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2ab3a6815f15944e_health_checker_py.html">src/bot/pipeline/commands/ask/observability/health_checker.py</a></td>
                <td>147</td>
                <td>89</td>
                <td>0</td>
                <td class="right" data-ratio="58 147">39%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2ab3a6815f15944e_log_analyzer_py.html">src/bot/pipeline/commands/ask/observability/log_analyzer.py</a></td>
                <td>197</td>
                <td>127</td>
                <td>0</td>
                <td class="right" data-ratio="70 197">36%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab68463ebc69ca4f___init___py.html">src/bot/pipeline/commands/ask/performance/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18831fe259ea92e4_pipeline_py.html">src/bot/pipeline/commands/ask/pipeline.py</a></td>
                <td>7</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="6 7">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ef1425a604312b50___init___py.html">src/bot/pipeline/commands/ask/quality/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_72457536bcd71851___init___py.html">src/bot/pipeline/commands/ask/security/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_72457536bcd71851_security_manager_py.html">src/bot/pipeline/commands/ask/security/security_manager.py</a></td>
                <td>198</td>
                <td>198</td>
                <td>0</td>
                <td class="right" data-ratio="0 198">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6b708e61bbf6f9e2___init___py.html">src/bot/pipeline/commands/ask/tools/__init__.py</a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6b708e61bbf6f9e2_mcp_manager_py.html">src/bot/pipeline/commands/ask/tools/mcp_manager.py</a></td>
                <td>353</td>
                <td>353</td>
                <td>0</td>
                <td class="right" data-ratio="0 353">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1f4b72d24847ac97___init___py.html">src/bot/pipeline/core/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1f4b72d24847ac97_context_manager_py.html">src/bot/pipeline/core/context_manager.py</a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93b1c7bd489fca84_pipeline_framework_py.html">src/bot/pipeline_framework.py</a></td>
                <td>127</td>
                <td>127</td>
                <td>4</td>
                <td class="right" data-ratio="0 127">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93b1c7bd489fca84_rate_limiter_py.html">src/bot/rate_limiter.py</a></td>
                <td>81</td>
                <td>81</td>
                <td>0</td>
                <td class="right" data-ratio="0 81">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93b1c7bd489fca84_real_time_data_stream_py.html">src/bot/real_time_data_stream.py</a></td>
                <td>187</td>
                <td>187</td>
                <td>0</td>
                <td class="right" data-ratio="0 187">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93b1c7bd489fca84_risk_management_system_py.html">src/bot/risk_management_system.py</a></td>
                <td>176</td>
                <td>176</td>
                <td>0</td>
                <td class="right" data-ratio="0 176">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93b1c7bd489fca84_setup_audit_py.html">src/bot/setup_audit.py</a></td>
                <td>185</td>
                <td>185</td>
                <td>0</td>
                <td class="right" data-ratio="0 185">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93b1c7bd489fca84_start_bot_py.html">src/bot/start_bot.py</a></td>
                <td>32</td>
                <td>32</td>
                <td>4</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93b1c7bd489fca84_token_validator_py.html">src/bot/token_validator.py</a></td>
                <td>141</td>
                <td>141</td>
                <td>0</td>
                <td class="right" data-ratio="0 141">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93b1c7bd489fca84_update_imports_py.html">src/bot/update_imports.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_03dd4c803eb1f65e_disclaimer_manager_py.html">src/bot/utils/disclaimer_manager.py</a></td>
                <td>53</td>
                <td>33</td>
                <td>0</td>
                <td class="right" data-ratio="20 53">38%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_03dd4c803eb1f65e_input_sanitizer_py.html">src/bot/utils/input_sanitizer.py</a></td>
                <td>175</td>
                <td>133</td>
                <td>0</td>
                <td class="right" data-ratio="42 175">24%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93b1c7bd489fca84_watchlist_alerts_py.html">src/bot/watchlist_alerts.py</a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca___init___py.html">src/core/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_advanced_config_manager_py.html">src/core/advanced_config_manager.py</a></td>
                <td>280</td>
                <td>280</td>
                <td>0</td>
                <td class="right" data-ratio="0 280">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b591a4c69fc2395___init___py.html">src/core/automation/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b591a4c69fc2395_analysis_scheduler_py.html">src/core/automation/analysis_scheduler.py</a></td>
                <td>266</td>
                <td>266</td>
                <td>0</td>
                <td class="right" data-ratio="0 266">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b591a4c69fc2395_discord_handler_py.html">src/core/automation/discord_handler.py</a></td>
                <td>120</td>
                <td>120</td>
                <td>0</td>
                <td class="right" data-ratio="0 120">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b591a4c69fc2395_report_engine_py.html">src/core/automation/report_engine.py</a></td>
                <td>547</td>
                <td>547</td>
                <td>0</td>
                <td class="right" data-ratio="0 547">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b591a4c69fc2395_report_formatter_py.html">src/core/automation/report_formatter.py</a></td>
                <td>372</td>
                <td>372</td>
                <td>0</td>
                <td class="right" data-ratio="0 372">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b591a4c69fc2395_report_scheduler_py.html">src/core/automation/report_scheduler.py</a></td>
                <td>332</td>
                <td>332</td>
                <td>0</td>
                <td class="right" data-ratio="0 332">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_config_manager_py.html">src/core/config_manager.py</a></td>
                <td>116</td>
                <td>64</td>
                <td>0</td>
                <td class="right" data-ratio="52 116">45%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_data_quality_validator_py.html">src/core/data_quality_validator.py</a></td>
                <td>99</td>
                <td>99</td>
                <td>0</td>
                <td class="right" data-ratio="0 99">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d09ff228a3163297___init___py.html">src/core/enums/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d09ff228a3163297_stock_analysis_py.html">src/core/enums/stock_analysis.py</a></td>
                <td>30</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_exceptions_py.html">src/core/exceptions.py</a></td>
                <td>47</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="31 47">66%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_feedback_mechanism_py.html">src/core/feedback_mechanism.py</a></td>
                <td>233</td>
                <td>233</td>
                <td>0</td>
                <td class="right" data-ratio="0 233">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5b0edee8f7d4f9c4___init___py.html">src/core/formatting/__init__.py</a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5b0edee8f7d4f9c4_analysis_template_py.html">src/core/formatting/analysis_template.py</a></td>
                <td>172</td>
                <td>172</td>
                <td>0</td>
                <td class="right" data-ratio="0 172">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5b0edee8f7d4f9c4_response_templates_py.html">src/core/formatting/response_templates.py</a></td>
                <td>43</td>
                <td>43</td>
                <td>0</td>
                <td class="right" data-ratio="0 43">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5b0edee8f7d4f9c4_technical_analysis_py.html">src/core/formatting/technical_analysis.py</a></td>
                <td>166</td>
                <td>166</td>
                <td>0</td>
                <td class="right" data-ratio="0 166">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5b0edee8f7d4f9c4_text_formatting_py.html">src/core/formatting/text_formatting.py</a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_market_calendar_py.html">src/core/market_calendar.py</a></td>
                <td>185</td>
                <td>185</td>
                <td>0</td>
                <td class="right" data-ratio="0 185">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f4aeaef6978b2030_health_monitor_py.html">src/core/monitoring/health_monitor.py</a></td>
                <td>211</td>
                <td>137</td>
                <td>0</td>
                <td class="right" data-ratio="74 211">35%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f4aeaef6978b2030_logger_py.html">src/core/monitoring/logger.py</a></td>
                <td>164</td>
                <td>64</td>
                <td>0</td>
                <td class="right" data-ratio="100 164">61%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f4aeaef6978b2030_metrics_py.html">src/core/monitoring/metrics.py</a></td>
                <td>116</td>
                <td>59</td>
                <td>0</td>
                <td class="right" data-ratio="57 116">49%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f4aeaef6978b2030_tracer_py.html">src/core/monitoring/tracer.py</a></td>
                <td>146</td>
                <td>82</td>
                <td>0</td>
                <td class="right" data-ratio="64 146">44%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_35b456b0e3ec380c___init___py.html">src/core/monitoring_pkg/__init__.py</a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_35b456b0e3ec380c_bot_monitor_py.html">src/core/monitoring_pkg/bot_monitor.py</a></td>
                <td>127</td>
                <td>89</td>
                <td>0</td>
                <td class="right" data-ratio="38 127">30%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_35b456b0e3ec380c_performance_tracker_py.html">src/core/monitoring_pkg/performance_tracker.py</a></td>
                <td>58</td>
                <td>42</td>
                <td>0</td>
                <td class="right" data-ratio="16 58">28%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_pipeline_engine_py.html">src/core/pipeline_engine.py</a></td>
                <td>123</td>
                <td>123</td>
                <td>1</td>
                <td class="right" data-ratio="0 123">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e4cdad6bd2c70039___init___py.html">src/core/prompts/__init__.py</a></td>
                <td>44</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="22 44">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_022707a548f470bc___init___py.html">src/core/prompts/base/__init__.py</a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_022707a548f470bc_compliance_py.html">src/core/prompts/base/compliance.py</a></td>
                <td>52</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="26 52">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_022707a548f470bc_personas_py.html">src/core/prompts/base/personas.py</a></td>
                <td>42</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="27 42">64%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_022707a548f470bc_system_prompts_py.html">src/core/prompts/base/system_prompts.py</a></td>
                <td>75</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="29 75">39%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_39420e7102c5ca14___init___py.html">src/core/prompts/commands/__init__.py</a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_39420e7102c5ca14_analyze_prompts_py.html">src/core/prompts/commands/analyze_prompts.py</a></td>
                <td>13</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="7 13">54%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_39420e7102c5ca14_ask_prompts_py.html">src/core/prompts/commands/ask_prompts.py</a></td>
                <td>117</td>
                <td>58</td>
                <td>0</td>
                <td class="right" data-ratio="59 117">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_39420e7102c5ca14_general_prompts_py.html">src/core/prompts/commands/general_prompts.py</a></td>
                <td>15</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="9 15">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e4cdad6bd2c70039_models_py.html">src/core/prompts/models.py</a></td>
                <td>66</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="58 66">88%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e4cdad6bd2c70039_prompt_manager_py.html">src/core/prompts/prompt_manager.py</a></td>
                <td>231</td>
                <td>168</td>
                <td>7</td>
                <td class="right" data-ratio="63 231">27%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_360fd4503d3a7f57___init___py.html">src/core/prompts/services/__init__.py</a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_360fd4503d3a7f57_anti_hallucination_py.html">src/core/prompts/services/anti_hallucination.py</a></td>
                <td>20</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="7 20">35%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_360fd4503d3a7f57_intent_detection_py.html">src/core/prompts/services/intent_detection.py</a></td>
                <td>29</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="9 29">31%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_360fd4503d3a7f57_security_analysis_py.html">src/core/prompts/services/security_analysis.py</a></td>
                <td>12</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="7 12">58%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_360fd4503d3a7f57_text_parsing_py.html">src/core/prompts/services/text_parsing.py</a></td>
                <td>12</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="7 12">58%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df1c8cb064ba181c___init___py.html">src/core/prompts/templates/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e4cdad6bd2c70039_unified_prompts_py.html">src/core/prompts/unified_prompts.py</a></td>
                <td>52</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="27 52">52%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_52225557544a60b6___init___py.html">src/core/prompts/utils/__init__.py</a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_52225557544a60b6_context_injection_py.html">src/core/prompts/utils/context_injection.py</a></td>
                <td>134</td>
                <td>121</td>
                <td>0</td>
                <td class="right" data-ratio="13 134">10%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_52225557544a60b6_formatters_py.html">src/core/prompts/utils/formatters.py</a></td>
                <td>141</td>
                <td>123</td>
                <td>0</td>
                <td class="right" data-ratio="18 141">13%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_52225557544a60b6_validation_py.html">src/core/prompts/utils/validation.py</a></td>
                <td>156</td>
                <td>137</td>
                <td>0</td>
                <td class="right" data-ratio="19 156">12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_resource_manager_py.html">src/core/resource_manager.py</a></td>
                <td>253</td>
                <td>253</td>
                <td>0</td>
                <td class="right" data-ratio="0 253">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_response_generator_py.html">src/core/response_generator.py</a></td>
                <td>88</td>
                <td>88</td>
                <td>0</td>
                <td class="right" data-ratio="0 88">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b32cc051564944b5___init___py.html">src/core/risk_management/__init__.py</a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b32cc051564944b5_atr_calculator_py.html">src/core/risk_management/atr_calculator.py</a></td>
                <td>364</td>
                <td>364</td>
                <td>0</td>
                <td class="right" data-ratio="0 364">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b32cc051564944b5_compliance_framework_py.html">src/core/risk_management/compliance_framework.py</a></td>
                <td>129</td>
                <td>129</td>
                <td>0</td>
                <td class="right" data-ratio="0 129">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_scheduler_py.html">src/core/scheduler.py</a></td>
                <td>37</td>
                <td>37</td>
                <td>2</td>
                <td class="right" data-ratio="0 37">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_trade_scanner_py.html">src/core/trade_scanner.py</a></td>
                <td>101</td>
                <td>101</td>
                <td>0</td>
                <td class="right" data-ratio="0 101">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9d33f2899f9f6498___init___py.html">src/core/validation/__init__.py</a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9d33f2899f9f6498_financial_validator_py.html">src/core/validation/financial_validator.py</a></td>
                <td>39</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="7 39">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3bb8a0f1ddb1d3c9___init___py.html">src/core/watchlist/__init__.py</a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f1aa34bc7b1afc18___init___py.html">src/database/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f1aa34bc7b1afc18_config_py.html">src/database/config.py</a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d5d913579db937fa___init___py.html">src/database/models/__init__.py</a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d5d913579db937fa_alerts_py.html">src/database/models/alerts.py</a></td>
                <td>33</td>
                <td>33</td>
                <td>4</td>
                <td class="right" data-ratio="0 33">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d5d913579db937fa_analysis_py.html">src/database/models/analysis.py</a></td>
                <td>31</td>
                <td>31</td>
                <td>4</td>
                <td class="right" data-ratio="0 31">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d5d913579db937fa_compliance_py.html">src/database/models/compliance.py</a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d5d913579db937fa_data_models_py.html">src/database/models/data_models.py</a></td>
                <td>84</td>
                <td>84</td>
                <td>0</td>
                <td class="right" data-ratio="0 84">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d5d913579db937fa_interactions_py.html">src/database/models/interactions.py</a></td>
                <td>25</td>
                <td>25</td>
                <td>4</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d5d913579db937fa_market_data_py.html">src/database/models/market_data.py</a></td>
                <td>26</td>
                <td>26</td>
                <td>4</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f1aa34bc7b1afc18_query_optimizer_py.html">src/database/query_optimizer.py</a></td>
                <td>189</td>
                <td>189</td>
                <td>0</td>
                <td class="right" data-ratio="0 189">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f1aa34bc7b1afc18_query_wrapper_py.html">src/database/query_wrapper.py</a></td>
                <td>67</td>
                <td>67</td>
                <td>0</td>
                <td class="right" data-ratio="0 67">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2bce53edfefc088a___init___py.html">src/database/repositories/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f1aa34bc7b1afc18_unified_client_py.html">src/database/unified_client.py</a></td>
                <td>70</td>
                <td>70</td>
                <td>55</td>
                <td class="right" data-ratio="0 70">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f1aa34bc7b1afc18_unified_db_py.html">src/database/unified_db.py</a></td>
                <td>127</td>
                <td>76</td>
                <td>0</td>
                <td class="right" data-ratio="51 127">40%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html">src/main.py</a></td>
                <td>127</td>
                <td>127</td>
                <td>6</td>
                <td class="right" data-ratio="0 127">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_276c81cb0f9851cf___init___py.html">src/security/__init__.py</a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_276c81cb0f9851cf_middleware_py.html">src/security/middleware.py</a></td>
                <td>120</td>
                <td>120</td>
                <td>0</td>
                <td class="right" data-ratio="0 120">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_98093a27b7e7f29c___init___py.html">src/shared/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cbf0818c9be381a0_ai_client_py.html">src/shared/ai_chat/ai_client.py</a></td>
                <td>268</td>
                <td>228</td>
                <td>0</td>
                <td class="right" data-ratio="40 268">15%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cbf0818c9be381a0_config_py.html">src/shared/ai_chat/config.py</a></td>
                <td>31</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="21 31">68%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cbf0818c9be381a0_models_py.html">src/shared/ai_chat/models.py</a></td>
                <td>49</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="40 49">82%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f49d35fbbf1fc1aa___init___py.html">src/shared/ai_services/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f49d35fbbf1fc1aa_ai_chat_processor_py.html">src/shared/ai_services/ai_chat_processor.py</a></td>
                <td>55</td>
                <td>55</td>
                <td>0</td>
                <td class="right" data-ratio="0 55">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f49d35fbbf1fc1aa_ai_processor_robust_py.html">src/shared/ai_services/ai_processor_robust.py</a></td>
                <td>795</td>
                <td>795</td>
                <td>2</td>
                <td class="right" data-ratio="0 795">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f49d35fbbf1fc1aa_ai_security_detector_py.html">src/shared/ai_services/ai_security_detector.py</a></td>
                <td>131</td>
                <td>131</td>
                <td>0</td>
                <td class="right" data-ratio="0 131">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f49d35fbbf1fc1aa_ai_tool_registry_py.html">src/shared/ai_services/ai_tool_registry.py</a></td>
                <td>102</td>
                <td>102</td>
                <td>0</td>
                <td class="right" data-ratio="0 102">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f49d35fbbf1fc1aa_anti_hallucination_prompt_py.html">src/shared/ai_services/anti_hallucination_prompt.py</a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f49d35fbbf1fc1aa_circuit_breaker_py.html">src/shared/ai_services/circuit_breaker.py</a></td>
                <td>97</td>
                <td>60</td>
                <td>0</td>
                <td class="right" data-ratio="37 97">38%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f49d35fbbf1fc1aa_cross_validation_ai_py.html">src/shared/ai_services/cross_validation_ai.py</a></td>
                <td>221</td>
                <td>221</td>
                <td>0</td>
                <td class="right" data-ratio="0 221">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f49d35fbbf1fc1aa_enhanced_ai_client_py.html">src/shared/ai_services/enhanced_ai_client.py</a></td>
                <td>101</td>
                <td>101</td>
                <td>0</td>
                <td class="right" data-ratio="0 101">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f49d35fbbf1fc1aa_enhanced_intent_detector_py.html">src/shared/ai_services/enhanced_intent_detector.py</a></td>
                <td>174</td>
                <td>174</td>
                <td>0</td>
                <td class="right" data-ratio="0 174">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f49d35fbbf1fc1aa_enhanced_symbol_extractor_py.html">src/shared/ai_services/enhanced_symbol_extractor.py</a></td>
                <td>191</td>
                <td>191</td>
                <td>0</td>
                <td class="right" data-ratio="0 191">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f49d35fbbf1fc1aa_fact_verifier_py.html">src/shared/ai_services/fact_verifier.py</a></td>
                <td>131</td>
                <td>131</td>
                <td>0</td>
                <td class="right" data-ratio="0 131">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f49d35fbbf1fc1aa_fast_price_lookup_py.html">src/shared/ai_services/fast_price_lookup.py</a></td>
                <td>66</td>
                <td>66</td>
                <td>0</td>
                <td class="right" data-ratio="0 66">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f49d35fbbf1fc1aa_intelligent_text_parser_py.html">src/shared/ai_services/intelligent_text_parser.py</a></td>
                <td>175</td>
                <td>175</td>
                <td>0</td>
                <td class="right" data-ratio="0 175">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f49d35fbbf1fc1aa_local_fallback_ai_py.html">src/shared/ai_services/local_fallback_ai.py</a></td>
                <td>167</td>
                <td>167</td>
                <td>0</td>
                <td class="right" data-ratio="0 167">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f49d35fbbf1fc1aa_openrouter_key_py.html">src/shared/ai_services/openrouter_key.py</a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f49d35fbbf1fc1aa_query_cache_py.html">src/shared/ai_services/query_cache.py</a></td>
                <td>95</td>
                <td>95</td>
                <td>0</td>
                <td class="right" data-ratio="0 95">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f49d35fbbf1fc1aa_query_router_py.html">src/shared/ai_services/query_router.py</a></td>
                <td>135</td>
                <td>135</td>
                <td>0</td>
                <td class="right" data-ratio="0 135">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f49d35fbbf1fc1aa_rate_limit_handler_py.html">src/shared/ai_services/rate_limit_handler.py</a></td>
                <td>151</td>
                <td>151</td>
                <td>0</td>
                <td class="right" data-ratio="0 151">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f49d35fbbf1fc1aa_response_synthesizer_py.html">src/shared/ai_services/response_synthesizer.py</a></td>
                <td>142</td>
                <td>142</td>
                <td>0</td>
                <td class="right" data-ratio="0 142">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f49d35fbbf1fc1aa_simple_model_config_py.html">src/shared/ai_services/simple_model_config.py</a></td>
                <td>75</td>
                <td>45</td>
                <td>0</td>
                <td class="right" data-ratio="30 75">40%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f49d35fbbf1fc1aa_simple_query_analyzer_py.html">src/shared/ai_services/simple_query_analyzer.py</a></td>
                <td>82</td>
                <td>82</td>
                <td>0</td>
                <td class="right" data-ratio="0 82">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f49d35fbbf1fc1aa_smart_model_router_py.html">src/shared/ai_services/smart_model_router.py</a></td>
                <td>281</td>
                <td>195</td>
                <td>0</td>
                <td class="right" data-ratio="86 281">31%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f49d35fbbf1fc1aa_timeout_manager_py.html">src/shared/ai_services/timeout_manager.py</a></td>
                <td>57</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="25 57">44%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f49d35fbbf1fc1aa_tool_registry_py.html">src/shared/ai_services/tool_registry.py</a></td>
                <td>116</td>
                <td>116</td>
                <td>0</td>
                <td class="right" data-ratio="0 116">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f49d35fbbf1fc1aa_unified_ai_processor_py.html">src/shared/ai_services/unified_ai_processor.py</a></td>
                <td>212</td>
                <td>212</td>
                <td>0</td>
                <td class="right" data-ratio="0 212">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7f47544f5bcd001___init___py.html">src/shared/cache/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7f47544f5bcd001_cache_service_py.html">src/shared/cache/cache_service.py</a></td>
                <td>262</td>
                <td>183</td>
                <td>0</td>
                <td class="right" data-ratio="79 262">30%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7f47544f5bcd001_local_cache_manager_py.html">src/shared/cache/local_cache_manager.py</a></td>
                <td>137</td>
                <td>87</td>
                <td>0</td>
                <td class="right" data-ratio="50 137">36%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7f47544f5bcd001_secure_cache_py.html">src/shared/cache/secure_cache.py</a></td>
                <td>66</td>
                <td>66</td>
                <td>0</td>
                <td class="right" data-ratio="0 66">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dadfb7ec26efdb7c___init___py.html">src/shared/config/__init__.py</a></td>
                <td>4</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="3 4">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dadfb7ec26efdb7c_config_manager_py.html">src/shared/config/config_manager.py</a></td>
                <td>89</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="72 89">81%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_98093a27b7e7f29c_config_loader_py.html">src/shared/config_loader.py</a></td>
                <td>20</td>
                <td>20</td>
                <td>3</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9c2f08a226d091be___init___py.html">src/shared/data_providers/__init__.py</a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9c2f08a226d091be_aggregator_py.html">src/shared/data_providers/aggregator.py</a></td>
                <td>166</td>
                <td>166</td>
                <td>0</td>
                <td class="right" data-ratio="0 166">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9c2f08a226d091be_alpaca_provider_py.html">src/shared/data_providers/alpaca_provider.py</a></td>
                <td>158</td>
                <td>158</td>
                <td>0</td>
                <td class="right" data-ratio="0 158">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9c2f08a226d091be_alpha_vantage_py.html">src/shared/data_providers/alpha_vantage.py</a></td>
                <td>163</td>
                <td>163</td>
                <td>0</td>
                <td class="right" data-ratio="0 163">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9c2f08a226d091be_alpha_vantage_mcp_py.html">src/shared/data_providers/alpha_vantage_mcp.py</a></td>
                <td>131</td>
                <td>131</td>
                <td>0</td>
                <td class="right" data-ratio="0 131">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9c2f08a226d091be_base_py.html">src/shared/data_providers/base.py</a></td>
                <td>7</td>
                <td>7</td>
                <td>42</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9c2f08a226d091be_enhanced_data_fetcher_py.html">src/shared/data_providers/enhanced_data_fetcher.py</a></td>
                <td>196</td>
                <td>196</td>
                <td>0</td>
                <td class="right" data-ratio="0 196">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9c2f08a226d091be_enhanced_error_handler_py.html">src/shared/data_providers/enhanced_error_handler.py</a></td>
                <td>162</td>
                <td>162</td>
                <td>0</td>
                <td class="right" data-ratio="0 162">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9c2f08a226d091be_example_usage_py.html">src/shared/data_providers/example_usage.py</a></td>
                <td>153</td>
                <td>153</td>
                <td>2</td>
                <td class="right" data-ratio="0 153">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9c2f08a226d091be_fallback_provider_py.html">src/shared/data_providers/fallback_provider.py</a></td>
                <td>43</td>
                <td>43</td>
                <td>0</td>
                <td class="right" data-ratio="0 43">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9c2f08a226d091be_finnhub_provider_py.html">src/shared/data_providers/finnhub_provider.py</a></td>
                <td>97</td>
                <td>97</td>
                <td>0</td>
                <td class="right" data-ratio="0 97">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9c2f08a226d091be_health_monitor_py.html">src/shared/data_providers/health_monitor.py</a></td>
                <td>181</td>
                <td>181</td>
                <td>0</td>
                <td class="right" data-ratio="0 181">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9c2f08a226d091be_hybrid_mcp_provider_py.html">src/shared/data_providers/hybrid_mcp_provider.py</a></td>
                <td>158</td>
                <td>158</td>
                <td>0</td>
                <td class="right" data-ratio="0 158">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9c2f08a226d091be_models_py.html">src/shared/data_providers/models.py</a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9c2f08a226d091be_polygon_provider_py.html">src/shared/data_providers/polygon_provider.py</a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9c2f08a226d091be_unified_base_py.html">src/shared/data_providers/unified_base.py</a></td>
                <td>168</td>
                <td>168</td>
                <td>3</td>
                <td class="right" data-ratio="0 168">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9c2f08a226d091be_yfinance_provider_py.html">src/shared/data_providers/yfinance_provider.py</a></td>
                <td>118</td>
                <td>118</td>
                <td>0</td>
                <td class="right" data-ratio="0 118">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_98093a27b7e7f29c_data_validation_py.html">src/shared/data_validation.py</a></td>
                <td>202</td>
                <td>202</td>
                <td>0</td>
                <td class="right" data-ratio="0 202">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6e2c44bbef529a62___init___py.html">src/shared/error_handling/__init__.py</a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6e2c44bbef529a62_fallback_py.html">src/shared/error_handling/fallback.py</a></td>
                <td>132</td>
                <td>83</td>
                <td>0</td>
                <td class="right" data-ratio="49 132">37%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6e2c44bbef529a62_logging_py.html">src/shared/error_handling/logging.py</a></td>
                <td>88</td>
                <td>48</td>
                <td>0</td>
                <td class="right" data-ratio="40 88">45%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6e2c44bbef529a62_retry_py.html">src/shared/error_handling/retry.py</a></td>
                <td>163</td>
                <td>163</td>
                <td>0</td>
                <td class="right" data-ratio="0 163">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2ca99271ac6bc52___init___py.html">src/shared/monitoring/__init__.py</a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2ca99271ac6bc52_intelligent_grader_py.html">src/shared/monitoring/intelligent_grader.py</a></td>
                <td>141</td>
                <td>141</td>
                <td>0</td>
                <td class="right" data-ratio="0 141">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2ca99271ac6bc52_observability_py.html">src/shared/monitoring/observability.py</a></td>
                <td>186</td>
                <td>99</td>
                <td>0</td>
                <td class="right" data-ratio="87 186">47%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2ca99271ac6bc52_performance_monitor_py.html">src/shared/monitoring/performance_monitor.py</a></td>
                <td>83</td>
                <td>83</td>
                <td>0</td>
                <td class="right" data-ratio="0 83">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2ca99271ac6bc52_pipeline_grader_py.html">src/shared/monitoring/pipeline_grader.py</a></td>
                <td>303</td>
                <td>224</td>
                <td>0</td>
                <td class="right" data-ratio="79 303">26%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2ca99271ac6bc52_pipeline_monitor_py.html">src/shared/monitoring/pipeline_monitor.py</a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f2ca99271ac6bc52_step_logger_py.html">src/shared/monitoring/step_logger.py</a></td>
                <td>113</td>
                <td>113</td>
                <td>0</td>
                <td class="right" data-ratio="0 113">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1758674a005c55bf___init___py.html">src/shared/services/__init__.py</a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1758674a005c55bf_enhanced_performance_optimizer_py.html">src/shared/services/enhanced_performance_optimizer.py</a></td>
                <td>328</td>
                <td>328</td>
                <td>0</td>
                <td class="right" data-ratio="0 328">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1758674a005c55bf_optimization_service_py.html">src/shared/services/optimization_service.py</a></td>
                <td>203</td>
                <td>203</td>
                <td>0</td>
                <td class="right" data-ratio="0 203">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1758674a005c55bf_performance_monitor_py.html">src/shared/services/performance_monitor.py</a></td>
                <td>158</td>
                <td>158</td>
                <td>0</td>
                <td class="right" data-ratio="0 158">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_647cfec4e88d88b8___init___py.html">src/shared/technical_analysis/__init__.py</a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_647cfec4e88d88b8_calculator_py.html">src/shared/technical_analysis/calculator.py</a></td>
                <td>189</td>
                <td>189</td>
                <td>0</td>
                <td class="right" data-ratio="0 189">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_647cfec4e88d88b8_config_py.html">src/shared/technical_analysis/config.py</a></td>
                <td>76</td>
                <td>76</td>
                <td>0</td>
                <td class="right" data-ratio="0 76">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_647cfec4e88d88b8_enhanced_calculator_py.html">src/shared/technical_analysis/enhanced_calculator.py</a></td>
                <td>207</td>
                <td>207</td>
                <td>0</td>
                <td class="right" data-ratio="0 207">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_647cfec4e88d88b8_enhanced_indicators_py.html">src/shared/technical_analysis/enhanced_indicators.py</a></td>
                <td>342</td>
                <td>342</td>
                <td>0</td>
                <td class="right" data-ratio="0 342">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_647cfec4e88d88b8_indicators_py.html">src/shared/technical_analysis/indicators.py</a></td>
                <td>145</td>
                <td>145</td>
                <td>0</td>
                <td class="right" data-ratio="0 145">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_647cfec4e88d88b8_multi_timeframe_analyzer_py.html">src/shared/technical_analysis/multi_timeframe_analyzer.py</a></td>
                <td>414</td>
                <td>414</td>
                <td>0</td>
                <td class="right" data-ratio="0 414">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_647cfec4e88d88b8_options_greeks_calculator_py.html">src/shared/technical_analysis/options_greeks_calculator.py</a></td>
                <td>82</td>
                <td>82</td>
                <td>0</td>
                <td class="right" data-ratio="0 82">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_647cfec4e88d88b8_signal_generator_py.html">src/shared/technical_analysis/signal_generator.py</a></td>
                <td>50</td>
                <td>50</td>
                <td>0</td>
                <td class="right" data-ratio="0 50">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_647cfec4e88d88b8_strategy_calculator_py.html">src/shared/technical_analysis/strategy_calculator.py</a></td>
                <td>117</td>
                <td>117</td>
                <td>0</td>
                <td class="right" data-ratio="0 117">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_647cfec4e88d88b8_unified_calculator_py.html">src/shared/technical_analysis/unified_calculator.py</a></td>
                <td>146</td>
                <td>146</td>
                <td>0</td>
                <td class="right" data-ratio="0 146">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_647cfec4e88d88b8_volume_analyzer_py.html">src/shared/technical_analysis/volume_analyzer.py</a></td>
                <td>298</td>
                <td>298</td>
                <td>0</td>
                <td class="right" data-ratio="0 298">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_647cfec4e88d88b8_zones_py.html">src/shared/technical_analysis/zones.py</a></td>
                <td>648</td>
                <td>648</td>
                <td>0</td>
                <td class="right" data-ratio="0 648">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_51ec32faff62b207___init___py.html">src/shared/utils/__init__.py</a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_51ec32faff62b207_deprecation_py.html">src/shared/utils/deprecation.py</a></td>
                <td>4</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="3 4">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_51ec32faff62b207_discord_helpers_py.html">src/shared/utils/discord_helpers.py</a></td>
                <td>123</td>
                <td>101</td>
                <td>0</td>
                <td class="right" data-ratio="22 123">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_51ec32faff62b207_lazy_import_py.html">src/shared/utils/lazy_import.py</a></td>
                <td>61</td>
                <td>61</td>
                <td>6</td>
                <td class="right" data-ratio="0 61">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_51ec32faff62b207_symbol_extraction_py.html">src/shared/utils/symbol_extraction.py</a></td>
                <td>293</td>
                <td>195</td>
                <td>0</td>
                <td class="right" data-ratio="98 293">33%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td>37256</td>
                <td>34296</td>
                <td>236</td>
                <td class="right" data-ratio="2960 37256">8%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.6">coverage.py v7.10.6</a>,
            created at 2025-09-28 11:00 -0400
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href="z_51ec32faff62b207_symbol_extraction_py.html"></a>
        <a id="nextFileLink" class="nav" href="z_145eef247bfb46b6___init___py.html"></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
