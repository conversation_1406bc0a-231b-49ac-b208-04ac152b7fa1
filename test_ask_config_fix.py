#!/usr/bin/env python3
"""Test the ASK config fix"""

from dotenv import load_dotenv
load_dotenv()

def test_ask_config():
    """Test that get_ask_config works without async errors"""
    print("🧪 Testing ASK Config Fix...")
    
    try:
        from src.bot.pipeline.commands.ask.config.ask_config import get_ask_config
        print("✅ ASK config imported successfully")
        
        # Test getting config
        config = get_ask_config()
        print("✅ ASK config retrieved successfully")
        print(f"📊 Config type: {type(config)}")
        print(f"📊 Config environment: {config.environment}")
        
        # Test with user ID
        user_config = get_ask_config(user_id="test_user_123")
        print("✅ User-specific config retrieved successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ ASK config test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_bot_import():
    """Test that the bot can be imported without errors"""
    print("\n🤖 Testing Bot Import...")
    
    try:
        import src.bot.main
        print("✅ Bot imported successfully")
        return True
        
    except Exception as e:
        print(f"❌ Bot import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Testing ASK Config and Bot Import")
    print("=" * 50)
    
    config_success = test_ask_config()
    bot_success = test_bot_import()
    
    print("\n" + "=" * 50)
    if config_success and bot_success:
        print("✅ All tests passed! Bot should work now.")
    else:
        print("❌ Some tests failed.")
        
    print("\n🎯 If tests pass, try running the Discord bot:")
    print("   python -m src.bot.main")
