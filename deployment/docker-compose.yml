version: '3.11'

services:
  redis:
    image: redis:7-alpine
    container_name: ask-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["C<PERSON>", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - ask-network

  ask-bot:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: ask-bot
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=production
      - REDIS_URL=redis://redis:6379/0
      - LOG_LEVEL=INFO
      - LOG_FORMAT=json
      - ASK_MAX_CONCURRENT_TOOLS=10
      - ASK_MAX_RESPONSE_TIME=5.0
      - ASK_CACHE_ENABLED=true
      - ASK_METRICS_ENABLED=true
      - ASK_RATE_LIMITING_ENABLED=true
      - ASK_AUDIT_LOGGING_ENABLED=true
    depends_on:
      redis:
        condition: service_healthy
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:8000/health', timeout=5)"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - ask-network

  prometheus:
    image: prom/prometheus:latest
    container_name: ask-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
    depends_on:
      - ask-bot
    networks:
      - ask-network

  grafana:
    image: grafana/grafana:latest
    container_name: ask-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
    depends_on:
      - prometheus
    networks:
      - ask-network

volumes:
  redis_data:
  grafana_data:

networks:
  ask-network:
    driver: bridge