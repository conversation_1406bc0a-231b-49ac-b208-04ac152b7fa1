#!/usr/bin/env python3
"""
Simple test script to debug tracer import issues
"""

import sys
import traceback

def test_tracer_import():
    """Test tracer import step by step"""
    print("🔍 Testing tracer import step by step...")
    
    try:
        print("1. Testing core tracer import...")
        from src.core.monitoring.tracer import get_tracer
        print("✅ Core tracer import successful")
        
        print("2. Getting tracer instance...")
        tracer = get_tracer()
        print(f"✅ Tracer instance: {tracer}")
        print(f"✅ Tracer type: {type(tracer)}")
        
        if tracer:
            print("3. Testing start_trace method...")
            context = tracer.start_trace("test_operation")
            print(f"✅ start_trace works: {context}")
            
            print("4. Testing finish_span method...")
            tracer.finish_span(context)
            print("✅ finish_span works")
        else:
            print("❌ Tracer is None")
            
    except Exception as e:
        print(f"❌ Core tracer test failed: {e}")
        traceback.print_exc()
        return False
    
    try:
        print("\n5. Testing observability module import...")
        from src.bot.pipeline.commands.ask.observability import get_tracer as obs_get_tracer
        print("✅ Observability tracer import successful")
        
        print("6. Getting observability tracer instance...")
        obs_tracer = obs_get_tracer()
        print(f"✅ Observability tracer instance: {obs_tracer}")
        print(f"✅ Observability tracer type: {type(obs_tracer)}")
        
        if obs_tracer:
            print("7. Testing observability start_trace method...")
            obs_context = obs_tracer.start_trace("test_operation_obs")
            print(f"✅ observability start_trace works: {obs_context}")
            
            print("8. Testing observability finish_span method...")
            obs_tracer.finish_span(obs_context)
            print("✅ observability finish_span works")
        else:
            print("❌ Observability tracer is None")
            
    except Exception as e:
        print(f"❌ Observability tracer test failed: {e}")
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    print("🚀 Starting Tracer Debug Test")
    success = test_tracer_import()
    if success:
        print("\n✅ All tracer tests passed!")
    else:
        print("\n❌ Some tracer tests failed!")
        sys.exit(1)
