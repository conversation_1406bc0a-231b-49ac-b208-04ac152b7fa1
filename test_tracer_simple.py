#!/usr/bin/env python3
"""
Simple test to isolate tracer import issues
"""

from dotenv import load_dotenv
load_dotenv()

print("1. Testing ConfigManager import...")
try:
    from src.core.config_manager import ConfigManager
    config = ConfigManager()
    print(f"✅ ConfigManager works: {config}")
except Exception as e:
    print(f"❌ ConfigManager failed: {e}")
    import traceback
    traceback.print_exc()

print("\n2. Testing logger import...")
try:
    from src.core.monitoring.logger import get_structured_logger, set_correlation_id
    logger = get_structured_logger(__name__)
    print(f"✅ Logger works: {logger}")
except Exception as e:
    print(f"❌ Logger failed: {e}")
    import traceback
    traceback.print_exc()

print("\n3. Testing tracer classes...")
try:
    import time
    import uuid
    import asyncio
    import threading
    from typing import Dict, Any, Optional, List
    from dataclasses import dataclass, field
    from datetime import datetime
    from enum import Enum
    from contextlib import asynccontextmanager
    print("✅ All imports successful")
except Exception as e:
    print(f"❌ Import failed: {e}")
    import traceback
    traceback.print_exc()

print("\n4. Testing tracer module import...")
try:
    from src.core.monitoring.tracer import DistributedTracer, get_tracer
    print("✅ Tracer module imported successfully")
    
    tracer = get_tracer()
    print(f"✅ get_tracer() returned: {tracer}")
    print(f"✅ Tracer type: {type(tracer)}")
    
    if tracer:
        context = tracer.start_trace("test_operation")
        print(f"✅ start_trace works: {context}")
    else:
        print("❌ Tracer is None")
        
except Exception as e:
    print(f"❌ Tracer module failed: {e}")
    import traceback
    traceback.print_exc()

print("\n5. Testing observability module import...")
try:
    from src.bot.pipeline.commands.ask.observability import get_tracer as obs_get_tracer
    print("✅ Observability module imported successfully")
    
    obs_tracer = obs_get_tracer()
    print(f"✅ obs_get_tracer() returned: {obs_tracer}")
    print(f"✅ Obs tracer type: {type(obs_tracer)}")
    
    if obs_tracer:
        obs_context = obs_tracer.start_trace("test_operation_obs")
        print(f"✅ obs start_trace works: {obs_context}")
    else:
        print("❌ Obs tracer is None")
        
except Exception as e:
    print(f"❌ Observability module failed: {e}")
    import traceback
    traceback.print_exc()

print("\n✅ Test completed")
