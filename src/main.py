import asyncio
import j<PERSON>
import structlog
from datetime import datetime
from typing import Dict, Any, List
from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException, BackgroundTasks
from fastapi.responses import J<PERSON>NResponse
from fastapi.middleware.cors import CORSMiddleware

from .security import SecurityMiddleware, create_security_middleware
from src.shared.ai_services.intelligent_text_parser import PineScriptAlertParser, ParsedAlert
from src.database.unified_db import UnifiedDatabaseManager as StorageManager

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()

# Initialize FastAPI app
app = FastAPI(
    title="TradingView Webhook Receiver",
    description="High-throughput webhook receiver for TradingView alerts",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(CORSMiddleware, allow_origins=["*"], allow_credentials=True, allow_methods=["*"], allow_headers=["*"])

# Add security middleware with configuration
import os
webhook_secret = os.getenv('TRADINGVIEW_WEBHOOK_SECRET')
allowed_origins = os.getenv('ALLOWED_ORIGINS', '*').split(',') if os.getenv('ALLOWED_ORIGINS') else ['*']

app.add_middleware(
    SecurityMiddleware,
    rate_limit=int(os.getenv('RATE_LIMIT', '100')),
    window=int(os.getenv('RATE_LIMIT_WINDOW', '60')),
    max_content_length=int(os.getenv('MAX_CONTENT_LENGTH', '1048576')),  # 1MB
    webhook_secret=webhook_secret,
    allowed_origins=allowed_origins,
    exempt_routes=['/health', '/metrics', '/docs', '/openapi.json', '/bot/status']
)

# Initialize components
parser = PineScriptAlertParser()
storage_manager = StorageManager()

async def lifespan(app: FastAPI):
    """Handle application lifespan events"""
    # Startup
    try:
        await storage_manager.initialize()
        logger.info("Storage manager initialized successfully")
    except Exception as e:
        logger.error("Failed to initialize storage manager", error=str(e))
        raise

    yield  # App is running

    # Shutdown
    try:
        await storage_manager.close()
        logger.info("Storage manager closed successfully")
    except Exception as e:
        logger.error("Error closing storage manager", error=str(e))

# Replace deprecated events with lifespan
app.router.lifespan_context = lifespan

@app.post("/webhook")
async def receive_webhook(request: Request, background_tasks: BackgroundTasks):
    """Receive and process TradingView webhooks"""
    try:
        # Get raw body for debugging
        body = await request.body()
        logger.info("Raw webhook body received", 
                   body_length=len(body),
                   content_type=request.headers.get("content-type", "unknown"))

        if not body:
            logger.warning("Empty webhook body received")
            return JSONResponse(content={"status": "error", "message": "Empty webhook body"}, status_code=400)

        # Try to parse as JSON first
        try:
            webhook_data = await request.json()
            logger.info("Webhook parsed as JSON", data_type="json")
            alerts = [webhook_data]  # Single JSON alert
        except Exception as json_error:
            logger.info("Failed to parse as JSON, trying text parsing", error=str(json_error))

            # Fallback to text parsing
            try:
                text_body = body.decode('utf-8')
                alerts = parser.parse_multiple_alerts(text_body)
                logger.info("Webhook parsed as text", 
                           alert_count=len(alerts),
                           first_alert_type=alerts[0].alert_type if alerts else "none")
            except Exception as text_error:
                logger.error("Failed to parse webhook as text", error=str(text_error))
                return JSONResponse(
                    content={"status": "error", "message": "Failed to parse webhook content"},
                    status_code=400
                )

        # Process alerts in background
        if alerts:
            background_tasks.add_task(process_alerts, alerts)
            logger.info("Alerts queued for background processing", count=len(alerts))

        return JSONResponse(content={"status": "success", "message": f"Processed {len(alerts)} alerts"})

    except Exception as e:
        logger.error("Error processing webhook", error=str(e), exc_info=True)
        return JSONResponse(
            content={"status": "error", "message": "Internal server error"},
            status_code=500
        )

async def process_alerts(alerts: List[ParsedAlert]):
    """Process alerts in background"""
    try:
        for alert in alerts:
            await process_single_alert(alert)
        logger.info("All alerts processed successfully", count=len(alerts))
    except Exception as e:
        logger.error("Error in background alert processing", error=str(e))

async def process_single_alert(alert: ParsedAlert):
    """Process a single parsed alert"""
    try:
        logger.info("Processing alert", 
                   symbol=alert.symbol,
                   alert_type=alert.alert_type,
                   signal=alert.signal,
                   timeframe=alert.timeframe)

        # Store alert data
        await storage_manager.store_alert_data(
            symbol=alert.symbol,
            alert_type=alert.alert_type,
            timeframe=alert.timeframe,
            entry_price=alert.entry_price,
            tp1_price=alert.tp1_price,
            tp2_price=alert.tp2_price,
            tp3_price=alert.tp3_price,
            sl_price=alert.sl_price,
            raw_data=alert.raw_text,
            timestamp=datetime.fromtimestamp(alert.timestamp)
        )

        logger.info("Alert processed successfully", symbol=alert.symbol)

    except Exception as e:
        logger.error("Failed to process alert", 
                    symbol=alert.symbol,
                    error=str(e))

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        # Check storage connectivity
        await storage_manager.check_health()
        return {"status": "healthy", "timestamp": datetime.now().isoformat()}
    except Exception as e:
        logger.error("Health check failed", error=str(e))
        raise HTTPException(status_code=503, detail="Service unhealthy")

@app.get("/metrics")
async def get_metrics():
    """Get service metrics"""
    try:
        metrics = await storage_manager.get_metrics()
        return metrics
    except Exception as e:
        logger.error("Failed to get metrics", error=str(e))
        return {"error": "Failed to retrieve metrics"}

# Add new endpoints for bot management
@app.get("/bot/status")
async def bot_status():
    """Get bot status"""
    try:
        # Import bot status checker
        from src.bot.monitoring.health_monitor import get_bot_health_monitor
        health_monitor = get_bot_health_monitor()
        
        status = await health_monitor.check_health()
        return {"status": "success", "data": status}
    except Exception as e:
        logger.error("Bot status check failed", error=str(e))
        return {"status": "error", "message": str(e)}

@app.post("/bot/restart")
async def restart_bot():
    """Restart the bot"""
    try:
        # Import bot restart functionality
        from src.bot.core.services import restart_bot_service

        if restart_bot_service:
            success = await restart_bot_service()
            if success:
                return {"status": "success", "message": "Bot restart initiated"}
            else:
                return {"status": "error", "message": "Failed to restart bot"}
        else:
            # Fallback restart implementation
            logger.info("Bot restart requested (manual restart required)")
            return {"status": "success", "message": "Bot restart requested - manual restart may be required"}
    except Exception as e:
        logger.error("Bot restart failed", error=str(e))
        return {"status": "error", "message": str(e)}

@app.get("/bot/performance")
async def bot_performance():
    """Get bot performance metrics"""
    try:
        # Import performance monitor
        from src.shared.monitoring.performance_monitor import performance_monitor
        metrics = performance_monitor.get_stats()
        slow_ops = performance_monitor.get_slow_operations()
        
        return {
            "status": "success",
            "metrics": metrics,
            "slow_operations": slow_ops
        }
    except Exception as e:
        logger.error("Bot performance check failed", error=str(e))
        return {"status": "error", "message": str(e)}

if __name__ == "__main__":
    import uvicorn
    import os
    host = os.getenv('WEBHOOK_HOST', '0.0.0.0')
    port = int(os.getenv('WEBHOOK_PORT', '8001'))
    uvicorn.run(app, host=host, port=port)
