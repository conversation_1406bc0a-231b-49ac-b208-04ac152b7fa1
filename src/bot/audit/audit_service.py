"""
Audit Service - Centralized audit logging and request visualization.

This service provides comprehensive audit logging capabilities for the Discord bot,
including request visualization, webhook logging, and audit event management.
"""

import asyncio
import aiohttp
import json
import time
from datetime import datetime
from typing import Dict, Any, Optional, List, Union
from enum import Enum
import discord
from discord.ext import commands

from src.shared.error_handling.logging import get_logger, generate_correlation_id
from .rate_limiter import AuditRateLimiter

logger = get_logger(__name__)

class AuditLevel(Enum):
    """Audit logging levels"""
    DEBUG = "debug"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

class AuditEvent:
    """Represents an audit event"""
    
    def __init__(
        self,
        level: AuditLevel,
        message: str,
        command_name: Optional[str] = None,
        user_id: Optional[int] = None,
        guild_id: Optional[int] = None,
        data: Optional[Dict[str, Any]] = None,
        correlation_id: Optional[str] = None
    ):
        self.timestamp = datetime.utcnow()
        self.level = level
        self.message = message
        self.command_name = command_name
        self.user_id = user_id
        self.guild_id = guild_id
        self.data = data or {}
        self.correlation_id = correlation_id or generate_correlation_id()

class RequestMetrics:
    """Metrics for a Discord command request"""
    
    def __init__(
        self,
        interaction: discord.Interaction,
        command_name: str,
        args: Dict[str, Any],
        correlation_id: str
    ):
        self.interaction = interaction
        self.command_name = command_name
        self.args = args
        self.correlation_id = correlation_id
        self.start_time = time.time()
        self.end_time: Optional[float] = None
        self.duration: Optional[float] = None
        self.success: bool = False
        self.error_message: Optional[str] = None
        self.response_data: Optional[Any] = None

    def complete(self, success: bool = True, error_message: Optional[str] = None, response_data: Optional[Any] = None):
        """Mark the request as completed"""
        self.end_time = time.time()
        self.duration = self.end_time - self.start_time
        self.success = success
        self.error_message = error_message
        self.response_data = response_data

class AuditService:
    """Centralized audit service for the Discord bot"""
    
    def __init__(self):
        self.bot: Optional[discord.Client] = None
        self.dev_channel_id: Optional[int] = None
        self.webhook_url: Optional[str] = None
        self.enabled = True
        self.log_to_console = True
        self.log_to_channel = True
        self.log_to_webhook = False
        self.include_user_data = True
        self.include_guild_data = True
        self.max_content_length = 1000
        self.visualized_commands = set()
        self._session: Optional[aiohttp.ClientSession] = None
        self._session_lock = asyncio.Lock()
        self.rate_limiter = AuditRateLimiter()
        self.active_requests: Dict[str, RequestMetrics] = {}
        
    async def initialize(self, bot: discord.Client, dev_channel_id: Optional[int] = None, webhook_url: Optional[str] = None):
        """Initialize the audit service"""
        self.bot = bot
        self.dev_channel_id = dev_channel_id
        self.webhook_url = webhook_url
        
        # Configure logging based on provided options
        self.log_to_channel = dev_channel_id is not None
        self.log_to_webhook = webhook_url is not None
        
        # Initialize HTTP session if webhook logging is enabled
        if self.log_to_webhook:
            await self._get_session()
        
        logger.info(f"Audit service initialized - Channel: {dev_channel_id}, Webhook: {bool(webhook_url)}")
        
    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create HTTP session for webhook requests"""
        async with self._session_lock:
            if self._session is None or self._session.closed:
                self._session = aiohttp.ClientSession()
            return self._session
    
    async def close(self):
        """Close the audit service and cleanup resources"""
        if self._session and not self._session.closed:
            await self._session.close()
        logger.info("Audit service closed")
    
    def configure(
        self,
        enabled: bool = True,
        log_to_console: bool = True,
        log_to_channel: bool = True,
        log_to_webhook: bool = False,
        include_user_data: bool = True,
        include_guild_data: bool = True,
        max_content_length: int = 1000
    ):
        """Configure audit service settings"""
        self.enabled = enabled
        self.log_to_console = log_to_console
        self.log_to_channel = log_to_channel
        self.log_to_webhook = log_to_webhook
        self.include_user_data = include_user_data
        self.include_guild_data = include_guild_data
        self.max_content_length = max_content_length
        
        logger.info(f"Audit service configured - Enabled: {enabled}, Console: {log_to_console}, Channel: {log_to_channel}, Webhook: {log_to_webhook}")
    
    def register_commands(self, commands: List[str]):
        """Register commands to be visualized"""
        self.visualized_commands.update(commands)
        logger.info(f"Registered {len(commands)} commands for visualization: {commands}")
    
    async def log_audit_event(
        self,
        level: AuditLevel,
        message: str,
        command_name: Optional[str] = None,
        user_id: Optional[int] = None,
        guild_id: Optional[int] = None,
        data: Optional[Dict[str, Any]] = None,
        correlation_id: Optional[str] = None
    ):
        """Log an audit event"""
        if not self.enabled:
            return
        
        event = AuditEvent(
            level=level,
            message=message,
            command_name=command_name,
            user_id=user_id,
            guild_id=guild_id,
            data=data,
            correlation_id=correlation_id
        )
        
        # Log to console
        if self.log_to_console:
            self._log_to_console(event)
        
        # Log to Discord channel
        if self.log_to_channel and self.dev_channel_id:
            await self._log_to_channel(event)
        
        # Log to webhook
        if self.log_to_webhook and self.webhook_url:
            await self._log_to_webhook(event)
    
    def _log_to_console(self, event: AuditEvent):
        """Log event to console"""
        log_level = getattr(logger, event.level.value.upper(), logger.info)
        log_message = f"[AUDIT] {event.timestamp.isoformat()} [{event.level.value.upper()}] {event.message}"
        
        if event.command_name:
            log_message += f" | Command: {event.command_name}"
        if event.user_id:
            log_message += f" | User: {event.user_id}"
        if event.correlation_id:
            log_message += f" | ID: {event.correlation_id}"
        
        log_level(log_message)
    
    async def _log_to_channel(self, event: AuditEvent):
        """Log event to Discord channel"""
        if not self.bot or not self.dev_channel_id:
            return
        
        try:
            channel = self.bot.get_channel(self.dev_channel_id)
            if not channel:
                logger.warning(f"Developer channel {self.dev_channel_id} not found")
                return
            
            embed = self._create_audit_embed(event)
            await channel.send(embed=embed)
            
        except Exception as e:
            logger.error(f"Failed to log audit event to channel: {e}")
    
    async def _log_to_webhook(self, event: AuditEvent):
        """Log event to webhook"""
        if not self.webhook_url:
            return
        
        try:
            # Check rate limiting
            if not await self.rate_limiter.can_send():
                logger.warning("Rate limited: Skipping webhook audit log")
                return
            
            session = await self._get_session()
            payload = self._create_webhook_payload(event)
            
            async with session.post(self.webhook_url, json=payload) as response:
                if response.status == 204:
                    await self.rate_limiter.record_send()
                else:
                    logger.warning(f"Webhook returned status {response.status}")
                    
        except Exception as e:
            logger.error(f"Failed to log audit event to webhook: {e}")
    
    def _create_audit_embed(self, event: AuditEvent) -> discord.Embed:
        """Create Discord embed for audit event"""
        color_map = {
            AuditLevel.DEBUG: 0x808080,
            AuditLevel.INFO: 0x00ff00,
            AuditLevel.WARNING: 0xffff00,
            AuditLevel.ERROR: 0xff0000,
            AuditLevel.CRITICAL: 0x8b0000
        }
        
        embed = discord.Embed(
            title=f"Audit Event - {event.level.value.upper()}",
            description=event.message,
            color=color_map.get(event.level, 0x808080),
            timestamp=event.timestamp
        )
        
        if event.command_name:
            embed.add_field(name="Command", value=event.command_name, inline=True)
        if event.user_id:
            embed.add_field(name="User ID", value=str(event.user_id), inline=True)
        if event.guild_id:
            embed.add_field(name="Guild ID", value=str(event.guild_id), inline=True)
        if event.correlation_id:
            embed.add_field(name="Correlation ID", value=event.correlation_id, inline=True)
        
        if event.data:
            data_str = json.dumps(event.data, indent=2)[:self.max_content_length]
            if len(json.dumps(event.data)) > self.max_content_length:
                data_str += "..."
            embed.add_field(name="Data", value=f"```json\n{data_str}\n```", inline=False)
        
        return embed
    
    def _create_webhook_payload(self, event: AuditEvent) -> Dict[str, Any]:
        """Create webhook payload for audit event"""
        return {
            "content": None,
            "embeds": [{
                "title": f"Audit Event - {event.level.value.upper()}",
                "description": event.message,
                "color": {
                    AuditLevel.DEBUG: 8421504,
                    AuditLevel.INFO: 65280,
                    AuditLevel.WARNING: 16776960,
                    AuditLevel.ERROR: 16711680,
                    AuditLevel.CRITICAL: 9109504
                }.get(event.level, 8421504),
                "timestamp": event.timestamp.isoformat(),
                "fields": [
                    {"name": "Command", "value": event.command_name or "N/A", "inline": True},
                    {"name": "User ID", "value": str(event.user_id or "N/A"), "inline": True},
                    {"name": "Guild ID", "value": str(event.guild_id or "N/A"), "inline": True},
                    {"name": "Correlation ID", "value": event.correlation_id, "inline": True}
                ]
            }]
        }
    
    async def start_request(self, interaction: discord.Interaction, command_name: str, args: Dict[str, Any]) -> str:
        """Start tracking a request"""
        if not self.enabled:
            return ""
        
        correlation_id = generate_correlation_id()
        metrics = RequestMetrics(interaction, command_name, args, correlation_id)
        self.active_requests[correlation_id] = metrics
        
        # Log request start
        await self.log_audit_event(
            level=AuditLevel.INFO,
            message=f"Command request started: /{command_name}",
            command_name=command_name,
            user_id=interaction.user.id,
            guild_id=interaction.guild.id if interaction.guild else None,
            data={"args": args},
            correlation_id=correlation_id
        )
        
        return correlation_id
    
    async def complete_request(
        self,
        correlation_id: str,
        success: bool = True,
        error_message: Optional[str] = None,
        response_data: Optional[Any] = None
    ):
        """Complete tracking a request"""
        if not self.enabled or correlation_id not in self.active_requests:
            return
        
        metrics = self.active_requests[correlation_id]
        metrics.complete(success, error_message, response_data)
        
        # Log request completion
        level = AuditLevel.INFO if success else AuditLevel.ERROR
        message = f"Command request completed: /{metrics.command_name} ({metrics.duration:.2f}s)"
        
        await self.log_audit_event(
            level=level,
            message=message,
            command_name=metrics.command_name,
            user_id=metrics.interaction.user.id,
            guild_id=metrics.interaction.guild.id if metrics.interaction.guild else None,
            data={
                "duration": metrics.duration,
                "success": success,
                "error": error_message,
                "response_size": len(str(response_data)) if response_data else 0
            },
            correlation_id=correlation_id
        )
        
        # Clean up
        del self.active_requests[correlation_id]
    
    async def log_error(
        self,
        interaction: discord.Interaction,
        command_name: str,
        error: Exception,
        correlation_id: Optional[str] = None
    ):
        """Log a command error"""
        await self.log_audit_event(
            level=AuditLevel.ERROR,
            message=f"Command error in /{command_name}: {str(error)}",
            command_name=command_name,
            user_id=interaction.user.id,
            guild_id=interaction.guild.id if interaction.guild else None,
            data={"error_type": type(error).__name__, "error_details": str(error)},
            correlation_id=correlation_id
        )

# Global audit service instance
audit_service = AuditService()
