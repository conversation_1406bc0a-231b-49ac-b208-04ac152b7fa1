"""
Rate Limiter for Trading Bot

This module implements rate limiting functionality with monitoring,
logging, and enforcement to prevent API abuse and ensure fair usage.
"""
import asyncio
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from enum import Enum
from collections import defaultdict
from src.shared.error_handling.logging import get_logger

logger = get_logger(__name__)


class RateLimitStrategy(Enum):
    """Strategies for rate limiting."""
    TOKEN_BUCKET = "token_bucket"
    LEAKY_BUCKET = "leaky_bucket"
    FIXED_WINDOW = "fixed_window"
    SLIDING_WINDOW = "sliding_window"


class RateLimitResult(Enum):
    """Results of rate limit checks."""
    ALLOWED = "allowed"
    REJECTED = "rejected"
    THROTTLED = "throttled"


@dataclass
class RateLimitRequest:
    """Request to check rate limits."""
    identifier: str  # User ID, IP, API key, etc.
    resource: str    # Resource or endpoint being accessed
    quantity: int    # Number of units being requested
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class RateLimitResponse:
    """Response from rate limit check."""
    result: RateLimitResult
    allowed: bool
    remaining: int
    reset_time: datetime
    retry_after: Optional[float] = None
    limit: Optional[int] = None
    metadata: Optional[Dict[str, Any]] = None


class RateLimiter:
    """Implements rate limiting with multiple strategies and monitoring."""
    
    def __init__(self):
        self._logger = get_logger("rate_limiter")
        self._buckets: Dict[str, Dict[str, Any]] = defaultdict(dict)  # identifier -> resource -> bucket_data
        self._request_history: Dict[str, List[Dict[str, Any]]] = defaultdict(list)
        self._default_limits = {
            'api_calls': {'limit': 100, 'window': 3600},  # 100 calls per hour
            'data_fetches': {'limit': 50, 'window': 300},  # 50 per 5 minutes
            'price_queries': {'limit': 200, 'window': 60}  # 200 per minute
        }
        self._strategy = RateLimitStrategy.SLIDING_WINDOW
    
    async def check_rate_limit(self, request: RateLimitRequest) -> RateLimitResponse:
        """Check if a request is allowed under rate limiting rules."""
        start_time = time.time()
        
        try:
            # Determine the appropriate limit for the resource
            limit_config = await self._get_limit_config(request.resource)
            identifier_key = f"{request.identifier}:{request.resource}"
            
            if self._strategy == RateLimitStrategy.SLIDING_WINDOW:
                result = await self._check_sliding_window(
                    identifier_key, request.quantity, limit_config
                )
            elif self._strategy == RateLimitStrategy.FIXED_WINDOW:
                result = await self._check_fixed_window(
                    identifier_key, request.quantity, limit_config
                )
            elif self._strategy == RateLimitStrategy.TOKEN_BUCKET:
                result = await self._check_token_bucket(
                    identifier_key, request.quantity, limit_config
                )
            else:
                # Default to sliding window
                result = await self._check_sliding_window(
                    identifier_key, request.quantity, limit_config
                )
            
            # Log the request for monitoring
            await self._log_request(request, result)
            
            # Calculate duration for metrics
            duration = time.time() - start_time
            
            self._logger.debug(
                f"Rate limit check: identifier={request.identifier}, "
                f"resource={request.resource}, allowed={result.allowed}, "
                f"duration={duration:.3f}s"
            )
            
            return result
            
        except Exception as e:
            self._logger.error(f"Error in rate limit check: {e}", exc_info=True)
            # If there's an error, fail open to avoid blocking legitimate requests
            return RateLimitResponse(
                result=RateLimitResult.ALLOWED,
                allowed=True,
                remaining=limit_config['limit'],
                reset_time=datetime.now() + timedelta(seconds=limit_config['window']),
                metadata={'error': str(e)}
            )
    
    async def _get_limit_config(self, resource: str) -> Dict[str, int]:
        """Get rate limit configuration for a resource."""
        # In a real implementation, this might load from config or database
        for key, config in self._default_limits.items():
            if key in resource.lower() or resource.lower() in key:
                return config
        
        # Default fallback
        return {'limit': 60, 'window': 60}  # 60 requests per minute
    
    async def _check_sliding_window(self, key: str, quantity: int, limit_config: Dict[str, int]) -> RateLimitResponse:
        """Check rate limit using sliding window algorithm."""
        now = time.time()
        window_start = now - limit_config['window']
        
        # Get request history for this key
        if key not in self._buckets:
            self._buckets[key] = {
                'requests': [],
                'limit': limit_config['limit'],
                'window': limit_config['window']
            }
        
        bucket = self._buckets[key]
        
        # Remove old requests outside the window
        bucket['requests'] = [
            req_time for req_time in bucket['requests']
            if req_time > window_start
        ]
        
        # Calculate current usage
        current_usage = sum(
            req.get('quantity', 1) 
            for req in bucket['requests']
        )
        
        # Check if request would exceed limit
        if current_usage + quantity > limit_config['limit']:
            # Calculate when the request would be allowed
            oldest_request = min(bucket['requests']) if bucket['requests'] else now
            reset_time = datetime.fromtimestamp(oldest_request + limit_config['window'])
            
            return RateLimitResponse(
                result=RateLimitResult.REJECTED,
                allowed=False,
                remaining=0,
                reset_time=reset_time,
                retry_after=oldest_request + limit_config['window'] - now,
                limit=limit_config['limit'],
                metadata={'current_usage': current_usage, 'request_quantity': quantity}
            )
        
        # Add the current request
        for _ in range(quantity):
            bucket['requests'].append(now)
        
        remaining = limit_config['limit'] - (current_usage + quantity)
        reset_time = datetime.fromtimestamp(now + limit_config['window'])
        
        return RateLimitResponse(
            result=RateLimitResult.ALLOWED,
            allowed=True,
            remaining=remaining,
            reset_time=reset_time,
            limit=limit_config['limit'],
            metadata={'current_usage': current_usage + quantity}
        )
    
    async def _check_fixed_window(self, key: str, quantity: int, limit_config: Dict[str, int]) -> RateLimitResponse:
        """Check rate limit using fixed window algorithm."""
        now = time.time()
        window_start = (int(now) // limit_config['window']) * limit_config['window']
        window_end = window_start + limit_config['window']
        
        # Get or create bucket
        if key not in self._buckets:
            self._buckets[key] = {
                'count': 0,
                'window_start': window_start,
                'limit': limit_config['limit'],
                'window': limit_config['window']
            }
        
        bucket = self._buckets[key]
        
        # Check if we're in a new window
        if now >= window_end:
            bucket['count'] = 0
            bucket['window_start'] = window_start
        
        # Check if request would exceed limit
        if bucket['count'] + quantity > limit_config['limit']:
            reset_time = datetime.fromtimestamp(window_end)
            return RateLimitResponse(
                result=RateLimitResult.REJECTED,
                allowed=False,
                remaining=0,
                reset_time=reset_time,
                retry_after=window_end - now,
                limit=limit_config['limit'],
                metadata={'current_count': bucket['count'], 'request_quantity': quantity}
            )
        
        # Increment the count
        bucket['count'] += quantity
        remaining = limit_config['limit'] - bucket['count']
        reset_time = datetime.fromtimestamp(window_end)
        
        return RateLimitResponse(
            result=RateLimitResult.ALLOWED,
            allowed=True,
            remaining=remaining,
            reset_time=reset_time,
            limit=limit_config['limit'],
            metadata={'current_count': bucket['count']}
        )
    
    async def _check_token_bucket(self, key: str, quantity: int, limit_config: Dict[str, int]) -> RateLimitResponse:
        """Check rate limit using token bucket algorithm."""
        now = time.time()
        
        # Get or create bucket
        if key not in self._buckets:
            self._buckets[key] = {
                'tokens': limit_config['limit'],
                'max_tokens': limit_config['limit'],
                'refill_rate': limit_config['limit'] / limit_config['window'],
                'last_refill': now,
                'limit': limit_config['limit'],
                'window': limit_config['window']
            }
        
        bucket = self._buckets[key]
        
        # Refill tokens based on time passed
        time_passed = now - bucket['last_refill']
        new_tokens = time_passed * bucket['refill_rate']
        bucket['tokens'] = min(bucket['max_tokens'], bucket['tokens'] + new_tokens)
        bucket['last_refill'] = now
        
        # Check if we have enough tokens
        if bucket['tokens'] >= quantity:
            bucket['tokens'] -= quantity
            remaining = int(bucket['tokens'])
            reset_time = datetime.fromtimestamp(now + (1 / bucket['refill_rate']))
            
            return RateLimitResponse(
                result=RateLimitResult.ALLOWED,
                allowed=True,
                remaining=remaining,
                reset_time=reset_time,
                limit=limit_config['limit'],
                metadata={'current_tokens': bucket['tokens']}
            )
        else:
            # Calculate when the request would be allowed
            tokens_needed = quantity - bucket['tokens']
            wait_time = tokens_needed / bucket['refill_rate']
            reset_time = datetime.fromtimestamp(now + wait_time)
            
            return RateLimitResponse(
                result=RateLimitResult.REJECTED,
                allowed=False,
                remaining=0,
                reset_time=reset_time,
                retry_after=wait_time,
                limit=limit_config['limit'],
                metadata={'current_tokens': bucket['tokens'], 'request_quantity': quantity}
            )
    
    async def _log_request(self, request: RateLimitRequest, response: RateLimitResponse):
        """Log rate limit request for monitoring and analytics."""
        log_entry = {
            'timestamp': datetime.now(),
            'identifier': request.identifier,
            'resource': request.resource,
            'quantity': request.quantity,
            'allowed': response.allowed,
            'result': response.result.value,
            'remaining': response.remaining,
            'reset_time': response.reset_time,
            'metadata': request.metadata or {}
        }
        
        # Add to request history
        self._request_history[request.identifier].append(log_entry)
        
        # Keep only recent history (last 1000 requests per identifier)
        if len(self._request_history[request.identifier]) > 1000:
            self._request_history[request.identifier] = self._request_history[request.identifier][-1000:]
    
    def set_strategy(self, strategy: RateLimitStrategy):
        """Set the rate limiting strategy."""
        self._strategy = strategy
        self._logger.info(f"Rate limiting strategy set to {strategy.value}")
    
    def set_default_limit(self, resource_type: str, limit: int, window: int):
        """Set default rate limit for a resource type."""
        self._default_limits[resource_type] = {'limit': limit, 'window': window}
        self._logger.info(f"Default limit set: {resource_type} = {limit}/{window}s")
    
    async def get_usage_stats(self, identifier: str, resource: str) -> Dict[str, Any]:
        """Get usage statistics for a specific identifier and resource."""
        key = f"{identifier}:{resource}"
        
        if key not in self._buckets:
            return {
                'identifier': identifier,
                'resource': resource,
                'current_usage': 0,
                'limit': self._default_limits.get(resource, {'limit': 60})['limit'],
                'window': self._default_limits.get(resource, {'window': 60})['window'],
                'remaining': self._default_limits.get(resource, {'limit': 60})['limit'],
                'reset_time': datetime.now()
            }
        
        bucket = self._buckets[key]
        limit_config = self._default_limits.get(resource, {'limit': 60, 'window': 60})
        
        return {
            'identifier': identifier,
            'resource': resource,
            'current_usage': bucket.get('count', len(bucket.get('requests', []))),
            'limit': limit_config['limit'],
            'window': limit_config['window'],
            'remaining': max(0, limit_config['limit'] - bucket.get('count', len(bucket.get('requests', [])))),
            'reset_time': datetime.fromtimestamp(
                time.time() + limit_config['window']
            )
        }
    
    async def get_rate_limit_status(self, identifier: str) -> Dict[str, Any]:
        """Get overall rate limit status for an identifier."""
        resources = {}
        
        for key, history_list in self._request_history.items():
            if key.startswith(identifier + ':'):
                resource = key.split(':', 1)[1]  # Get resource part
                recent_requests = [
                    req for req in history_list
                    if datetime.now() - req['timestamp'] < timedelta(minutes=5)
                ]
                
                allowed_count = sum(1 for req in recent_requests if req['allowed'])
                rejected_count = sum(1 for req in recent_requests if not req['allowed'])
                
                resources[resource] = {
                    'allowed_count': allowed_count,
                    'rejected_count': rejected_count,
                    'total_count': len(recent_requests)
                }
        
        return {
            'identifier': identifier,
            'timestamp': datetime.now().isoformat(),
            'resources': resources,
            'total_requests': sum(
                res['total_count'] for res in resources.values()
            )
        }
    
    async def override_limit(self, identifier: str, resource: str, new_limit: int):
        """Temporarily override rate limit for specific identifier and resource."""
        key = f"{identifier}:{resource}"
        if key not in self._buckets:
            config = await self._get_limit_config(resource)
            self._buckets[key] = {
                'limit': new_limit,
                'window': config['window'],
                'requests': []
            }
        else:
            self._buckets[key]['limit'] = new_limit
        
        self._logger.info(f"Rate limit override: {key} = {new_limit}")


# Global rate limiter instance
rate_limiter = RateLimiter()


async def check_rate_limit(request: RateLimitRequest) -> RateLimitResponse:
    """Convenience function to check rate limit."""
    return await rate_limiter.check_rate_limit(request)


async def get_usage_stats(identifier: str, resource: str) -> Dict[str, Any]:
    """Convenience function to get usage stats."""
    return await rate_limiter.get_usage_stats(identifier, resource)


async def get_rate_limit_status(identifier: str) -> Dict[str, Any]:
    """Convenience function to get rate limit status."""
    return await rate_limiter.get_rate_limit_status(identifier)

import time
import asyncio
from typing import Dict, Optional
from dataclasses import dataclass
from src.shared.error_handling.logging import get_logger

logger = get_logger(__name__)

@dataclass
class RateLimitConfig:
    """Configuration for rate limiting"""
    max_requests: int = 10  # Maximum requests per window
    window_seconds: int = 60  # Time window in seconds
    burst_limit: int = 5  # Maximum burst requests
    burst_window: int = 10  # Burst window in seconds

class AuditRateLimiter:
    """Rate limiter for audit requests"""
    
    def __init__(self, config: Optional[RateLimitConfig] = None):
        self.config = config or RateLimitConfig()
        self.requests: Dict[float, int] = {}  # timestamp -> count
        self.burst_requests: Dict[float, int] = {}  # timestamp -> count
        self.lock = asyncio.Lock()
    
    async def can_send(self) -> bool:
        """Check if a request can be sent based on rate limits"""
        async with self.lock:
            now = time.time()
            
            # Clean old entries
            self._clean_old_entries(now)
            
            # Check burst limit
            if not self._check_burst_limit(now):
                logger.debug("Rate limit: Burst limit exceeded")
                return False
            
            # Check regular rate limit
            if not self._check_rate_limit(now):
                logger.debug("Rate limit: Regular limit exceeded")
                return False
            
            return True
    
    async def record_send(self):
        """Record that a request was sent"""
        async with self.lock:
            now = time.time()
            
            # Record in burst window
            burst_key = int(now / self.config.burst_window) * self.config.burst_window
            self.burst_requests[burst_key] = self.burst_requests.get(burst_key, 0) + 1
            
            # Record in regular window
            regular_key = int(now / self.config.window_seconds) * self.config.window_seconds
            self.requests[regular_key] = self.requests.get(regular_key, 0) + 1
    
    def _clean_old_entries(self, now: float):
        """Remove old entries from tracking dictionaries"""
        # Clean burst requests older than burst window
        burst_cutoff = now - self.config.burst_window
        self.burst_requests = {
            timestamp: count for timestamp, count in self.burst_requests.items()
            if timestamp > burst_cutoff
        }
        
        # Clean regular requests older than window
        regular_cutoff = now - self.config.window_seconds
        self.requests = {
            timestamp: count for timestamp, count in self.requests.items()
            if timestamp > regular_cutoff
        }
    
    def _check_burst_limit(self, now: float) -> bool:
        """Check if burst limit is exceeded"""
        burst_key = int(now / self.config.burst_window) * self.config.burst_window
        current_burst = self.burst_requests.get(burst_key, 0)
        return current_burst < self.config.burst_limit
    
    def _check_rate_limit(self, now: float) -> bool:
        """Check if regular rate limit is exceeded"""
        regular_key = int(now / self.config.window_seconds) * self.config.window_seconds
        current_requests = self.requests.get(regular_key, 0)
        return current_requests < self.config.max_requests
    
    def get_status(self) -> Dict[str, any]:
        """Get current rate limiter status"""
        now = time.time()
        self._clean_old_entries(now)
        
        burst_key = int(now / self.config.burst_window) * self.config.burst_window
        regular_key = int(now / self.config.window_seconds) * self.config.window_seconds
        
        return {
            "burst_requests": self.burst_requests.get(burst_key, 0),
            "burst_limit": self.config.burst_limit,
            "regular_requests": self.requests.get(regular_key, 0),
            "regular_limit": self.config.max_requests,
            "can_send": self._check_burst_limit(now) and self._check_rate_limit(now)
        }

# Global rate limiter instance
audit_rate_limiter = AuditRateLimiter()