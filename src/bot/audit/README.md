# Audit System

A comprehensive audit logging and request visualization system for the Discord bot.

## Overview

The audit system provides:
- **Request Visualization**: Real-time visualization of Discord command requests and responses
- **Audit Logging**: Comprehensive logging of all bot activities with different severity levels
- **Session Management**: Persistent session tracking and data storage
- **Rate Limiting**: Prevents spam in audit channels and webhooks
- **Discord Integration**: Native Discord embeds and webhook support
- **Admin Commands**: Discord slash commands for system management

## Components

### 1. AuditService (`audit_service.py`)
Central service for audit logging with support for:
- Multiple log levels (DEBUG, INFO, WARNING, ERROR, CRITICAL)
- Console, Discord channel, and webhook logging
- Request tracking and metrics
- Error logging and correlation

### 2. RequestVisualizer (`request_visualizer.py`)
Visualizes Discord command requests and responses:
- Rich Discord embeds for requests and responses
- Pipeline execution visualization
- Webhook and channel logging
- Configurable content length limits

### 3. AuditSessionManager (`session_manager.py`)
Manages audit sessions and data persistence:
- Session creation and tracking
- Data storage to disk
- Automatic cleanup of old sessions
- Session statistics and monitoring

### 4. AuditRateLimiter (`rate_limiter.py`)
Prevents spam in audit logging:
- Configurable rate limits
- Burst protection
- Request tracking and status monitoring

### 5. ClientAuditIntegration (`client_audit_integration.py`)
Main integration module that hooks into the bot:
- Automatic request tracking
- Event handler registration
- Easy setup and configuration

## Setup

### Basic Setup

```python
from src.bot.client_audit_integration import add_audit_hooks

# Initialize the bot
bot = discord.Bot(...)

# Set up the audit system
audit_integration = await add_audit_hooks(
    bot=bot,
    dev_channel_id=123456789012345678,  # Your dev channel ID
    webhook_url="https://discord.com/api/webhooks/...",  # Optional webhook URL
    enabled=True
)
```

### Configuration

The audit system can be configured through:
- Environment variables (`AUDIT_WEBHOOK_URL`)
- Configuration files
- Discord slash commands (`/audit_config`)

## Discord Commands

### `/audit_config`
Configure the audit system (Admin only)
- `enabled`: Enable/disable the audit system
- `channel_id`: Set Discord channel for audit logs
- `use_webhook`: Enable/disable webhook logging

### `/toggle_webhook`
Toggle webhook logging (Admin only)

### `/test_audit`
Test the audit system (Admin only)

### `/audit_status`
Get audit system status (Admin only)

### `/audit_cleanup`
Clean up old audit data (Admin only)

## Usage in Commands

### Automatic Integration

The audit system automatically tracks all slash commands when properly integrated:

```python
@bot.slash_command(name="example")
async def example_command(ctx, param: str):
    # Audit tracking happens automatically
    await ctx.respond(f"Hello {param}!")
```

### Manual Integration

For more control, you can manually track requests:

```python
class MyCommands(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.audit_integration = None
    
    async def cog_load(self):
        self.audit_integration = self.bot.services.get('audit_service')
    
    @commands.slash_command(name="manual_example")
    async def manual_example(self, ctx, query: str):
        # Start tracking
        correlation_id = await self.audit_integration.start_request(
            interaction=ctx.interaction,
            command_name="manual_example",
            args={"query": query}
        )
        
        try:
            # Your command logic
            result = await self.process_query(query)
            
            # Complete successfully
            await self.audit_integration.complete_request(
                correlation_id=correlation_id,
                success=True,
                response_data=result
            )
            
            await ctx.respond(f"Result: {result}")
            
        except Exception as e:
            # Log error and complete with failure
            await self.audit_integration.log_error(
                interaction=ctx.interaction,
                command_name="manual_example",
                error=e,
                correlation_id=correlation_id
            )
            
            await self.audit_integration.complete_request(
                correlation_id=correlation_id,
                success=False,
                error_message=str(e)
            )
            
            await ctx.respond(f"Error: {str(e)}", ephemeral=True)
```

### Manual Audit Logging

```python
from src.bot.audit.audit_service import audit_service, AuditLevel

# Log an audit event
await audit_service.log_audit_event(
    level=AuditLevel.INFO,
    message="Custom audit event",
    command_name="my_command",
    data={"custom": "data"}
)
```

## Configuration Options

### AuditService Configuration

```python
audit_service.configure(
    enabled=True,                    # Enable/disable audit logging
    log_to_console=True,            # Log to console
    log_to_channel=True,            # Log to Discord channel
    log_to_webhook=False,           # Log to webhook
    include_user_data=True,         # Include user data in logs
    include_guild_data=True,        # Include guild data in logs
    max_content_length=1000         # Max content length for logs
)
```

### Rate Limiter Configuration

```python
from src.bot.audit.rate_limiter import RateLimitConfig

config = RateLimitConfig(
    max_requests=10,        # Max requests per window
    window_seconds=60,      # Time window in seconds
    burst_limit=5,          # Max burst requests
    burst_window=10         # Burst window in seconds
)
```

## Data Storage

Audit data is stored in the `data/audit/` directory:
- Session files: `session_{session_id}.json`
- Record files: `records_{session_id}.json`

Sessions are automatically cleaned up based on:
- Maximum session duration (24 hours by default)
- Maximum records per session (1000 by default)
- Cleanup interval (5 minutes by default)

## Monitoring

The audit system provides comprehensive monitoring through:
- Discord slash commands for status and management
- Session statistics and metrics
- Rate limiter status
- Error tracking and reporting

## Security

- Admin-only commands for configuration
- Rate limiting to prevent spam
- Configurable data inclusion (user/guild data)
- Secure webhook handling
- Automatic cleanup of sensitive data

## Troubleshooting

### Common Issues

1. **Audit logs not appearing**: Check if the audit system is enabled and properly configured
2. **Webhook not working**: Verify webhook URL and rate limiting
3. **Channel not found**: Ensure the bot has access to the developer channel
4. **High memory usage**: Check session cleanup settings and reduce max records per session

### Debug Commands

- `/test_audit`: Test all audit system components
- `/audit_status`: Check system status and configuration
- `/audit_cleanup`: Manually trigger cleanup of old data

## Examples

See `example_usage.py` for comprehensive usage examples.

## Integration with Bot

The audit system is automatically integrated into the bot through:
1. ServiceManager initialization
2. Extension loading
3. Event handler registration
4. Command tree integration

No additional setup is required beyond the initial configuration.
