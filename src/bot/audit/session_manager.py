"""
Audit Session Manager - Manages audit sessions and data persistence.

This module provides session management for audit data, including
session creation, data storage, and cleanup.
"""

import asyncio
import json
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
from pathlib import Path

from src.shared.error_handling.logging import get_logger

logger = get_logger(__name__)

@dataclass
class AuditSession:
    """Represents an audit session"""
    session_id: str
    start_time: datetime
    end_time: Optional[datetime] = None
    user_id: Optional[int] = None
    guild_id: Optional[int] = None
    command_count: int = 0
    error_count: int = 0
    total_duration: float = 0.0
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert session to dictionary"""
        data = asdict(self)
        data['start_time'] = self.start_time.isoformat()
        if self.end_time:
            data['end_time'] = self.end_time.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AuditSession':
        """Create session from dictionary"""
        if 'start_time' in data and isinstance(data['start_time'], str):
            data['start_time'] = datetime.fromisoformat(data['start_time'])
        if 'end_time' in data and isinstance(data['end_time'], str):
            data['end_time'] = datetime.fromisoformat(data['end_time'])
        return cls(**data)

@dataclass
class AuditRecord:
    """Represents an individual audit record"""
    record_id: str
    session_id: str
    timestamp: datetime
    level: str
    message: str
    command_name: Optional[str] = None
    user_id: Optional[int] = None
    guild_id: Optional[int] = None
    correlation_id: Optional[str] = None
    data: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.data is None:
            self.data = {}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert record to dictionary"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AuditRecord':
        """Create record from dictionary"""
        if 'timestamp' in data and isinstance(data['timestamp'], str):
            data['timestamp'] = datetime.fromisoformat(data['timestamp'])
        return cls(**data)

class AuditSessionManager:
    """Manages audit sessions and data persistence"""
    
    def __init__(self, data_dir: Optional[str] = None):
        self.data_dir = Path(data_dir) if data_dir else Path("data/audit")
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        self.active_sessions: Dict[str, AuditSession] = {}
        self.session_records: Dict[str, List[AuditRecord]] = {}
        self.lock = asyncio.Lock()
        
        # Configuration
        self.max_session_duration = timedelta(hours=24)  # Max session duration
        self.max_records_per_session = 1000  # Max records per session
        self.cleanup_interval = 300  # Cleanup every 5 minutes
        self._cleanup_task: Optional[asyncio.Task] = None
        
        logger.info(f"Audit session manager initialized with data dir: {self.data_dir}")
    
    async def start(self):
        """Start the session manager"""
        if self._cleanup_task is None or self._cleanup_task.done():
            self._cleanup_task = asyncio.create_task(self._cleanup_loop())
            logger.info("Audit session manager started")
    
    async def stop(self):
        """Stop the session manager"""
        if self._cleanup_task and not self._cleanup_task.done():
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        # Save all active sessions
        await self._save_all_sessions()
        logger.info("Audit session manager stopped")
    
    async def create_session(
        self,
        user_id: Optional[int] = None,
        guild_id: Optional[int] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """Create a new audit session"""
        session_id = f"session_{int(time.time())}_{id(self)}"
        
        session = AuditSession(
            session_id=session_id,
            start_time=datetime.utcnow(),
            user_id=user_id,
            guild_id=guild_id,
            metadata=metadata or {}
        )
        
        async with self.lock:
            self.active_sessions[session_id] = session
            self.session_records[session_id] = []
        
        logger.info(f"Created audit session: {session_id}")
        return session_id
    
    async def end_session(self, session_id: str) -> Optional[AuditSession]:
        """End an audit session"""
        async with self.lock:
            session = self.active_sessions.get(session_id)
            if not session:
                logger.warning(f"Session {session_id} not found")
                return None
            
            session.end_time = datetime.utcnow()
            session.duration = (session.end_time - session.start_time).total_seconds()
            
            # Save session data
            await self._save_session(session)
            
            # Remove from active sessions
            del self.active_sessions[session_id]
            del self.session_records[session_id]
        
        logger.info(f"Ended audit session: {session_id}")
        return session
    
    async def add_record(
        self,
        session_id: str,
        level: str,
        message: str,
        command_name: Optional[str] = None,
        user_id: Optional[int] = None,
        guild_id: Optional[int] = None,
        correlation_id: Optional[str] = None,
        data: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Add a record to a session"""
        async with self.lock:
            if session_id not in self.active_sessions:
                logger.warning(f"Session {session_id} not found")
                return False
            
            session = self.active_sessions[session_id]
            records = self.session_records[session_id]
            
            # Check record limit
            if len(records) >= self.max_records_per_session:
                logger.warning(f"Session {session_id} has reached record limit")
                return False
            
            record = AuditRecord(
                record_id=f"record_{int(time.time() * 1000000)}",
                session_id=session_id,
                timestamp=datetime.utcnow(),
                level=level,
                message=message,
                command_name=command_name,
                user_id=user_id,
                guild_id=guild_id,
                correlation_id=correlation_id,
                data=data or {}
            )
            
            records.append(record)
            
            # Update session stats
            session.command_count += 1
            if level in ['error', 'critical']:
                session.error_count += 1
        
        return True
    
    async def get_session(self, session_id: str) -> Optional[AuditSession]:
        """Get a session by ID"""
        async with self.lock:
            return self.active_sessions.get(session_id)
    
    async def get_session_records(self, session_id: str) -> List[AuditRecord]:
        """Get records for a session"""
        async with self.lock:
            return self.session_records.get(session_id, []).copy()
    
    async def get_active_sessions(self) -> List[AuditSession]:
        """Get all active sessions"""
        async with self.lock:
            return list(self.active_sessions.values())
    
    async def _save_session(self, session: AuditSession):
        """Save session data to disk"""
        try:
            session_file = self.data_dir / f"session_{session.session_id}.json"
            with open(session_file, 'w') as f:
                json.dump(session.to_dict(), f, indent=2)
            
            # Save records
            records = self.session_records.get(session.session_id, [])
            records_file = self.data_dir / f"records_{session.session_id}.json"
            with open(records_file, 'w') as f:
                json.dump([record.to_dict() for record in records], f, indent=2)
                
        except Exception as e:
            logger.error(f"Failed to save session {session.session_id}: {e}")
    
    async def _save_all_sessions(self):
        """Save all active sessions"""
        async with self.lock:
            for session in self.active_sessions.values():
                await self._save_session(session)
    
    async def _cleanup_loop(self):
        """Background cleanup loop"""
        while True:
            try:
                await asyncio.sleep(self.cleanup_interval)
                await self._cleanup_old_sessions()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in cleanup loop: {e}")
    
    async def _cleanup_old_sessions(self):
        """Clean up old sessions"""
        now = datetime.utcnow()
        sessions_to_remove = []
        
        async with self.lock:
            for session_id, session in self.active_sessions.items():
                if now - session.start_time > self.max_session_duration:
                    sessions_to_remove.append(session_id)
        
        # End old sessions
        for session_id in sessions_to_remove:
            await self.end_session(session_id)
            logger.info(f"Cleaned up old session: {session_id}")
    
    async def get_session_stats(self) -> Dict[str, Any]:
        """Get session statistics"""
        async with self.lock:
            active_count = len(self.active_sessions)
            total_records = sum(len(records) for records in self.session_records.values())
            
            return {
                "active_sessions": active_count,
                "total_records": total_records,
                "data_directory": str(self.data_dir),
                "max_session_duration": str(self.max_session_duration),
                "max_records_per_session": self.max_records_per_session
            }

# Global session manager instance
audit_session_manager = AuditSessionManager()