"""
Audit System Setup - Sets up the audit system for the Discord bot.

This module provides functions to set up the audit system with proper
configuration and Discord slash commands for audit management.
"""

import discord
import logging
import os
import asyncio
from typing import Optional, Union, Any
from discord.ext import commands

from src.shared.error_handling.logging import get_logger
from src.shared.config.config_manager import config
from .audit_service import audit_service, AuditLevel
from .request_visualizer import request_visualizer

logger = get_logger(__name__)

def get_default_webhook_url() -> str:
    """Get the default webhook URL from config or environment variables"""
    # Try to get from config manager first
    webhook_url = config.get('audit', 'webhook_url', '')
    
    # Fall back to environment variable if not in config
    if not webhook_url:
        webhook_url = os.environ.get('AUDIT_WEBHOOK_URL', '')
        
    if not webhook_url:
        logger.warning("No audit webhook URL configured. Audit logs will not be sent to webhook.")
        
    return webhook_url

# Default developer channel ID - replace with your actual developer channel ID
DEFAULT_DEV_CHANNEL_ID = None  # Set to None to use webhook by default

def setup_audit_system(
    bot: Union[discord.Client, commands.Bot], 
    dev_channel_id: Optional[int] = None, 
    webhook_url: Optional[str] = None, 
    enable_logging: bool = True
):
    """
    Set up the audit system for the Discord bot
    
    Args:
        bot: The Discord bot instance
        dev_channel_id: The ID of the developer channel to send audit logs to
        webhook_url: The webhook URL to send audit logs to
        enable_logging: Whether to enable audit logging
    """
    # Use provided values or defaults
    channel_id = dev_channel_id or DEFAULT_DEV_CHANNEL_ID
    webhook = webhook_url or get_default_webhook_url()
    
    # Initialize audit service
    asyncio.create_task(audit_service.initialize(bot, channel_id, webhook))
    
    # Initialize request visualizer
    asyncio.create_task(request_visualizer.initialize(bot, channel_id, webhook))
    
    # Configure based on enable_logging flag
    audit_service.configure(
        enabled=enable_logging,
        log_to_console=enable_logging,
        log_to_channel=enable_logging and channel_id is not None,
        log_to_webhook=enable_logging and webhook is not None
    )
    
    request_visualizer.configure(
        enabled=enable_logging,
        log_to_console=enable_logging,
        log_to_channel=enable_logging and channel_id is not None,
        log_to_webhook=enable_logging and webhook is not None
    )
    
    # Register commands to visualize
    commands_to_visualize = [
        "analyze", "ask", "price", "watchlist", "portfolio", 
        "alerts", "settings", "search", "toggle_debug", "help"
    ]
    
    audit_service.register_commands(commands_to_visualize)
    request_visualizer.register_commands(commands_to_visualize)
    
    # Log setup
    if enable_logging:
        if channel_id:
            logger.info(f"Audit system set up with developer channel ID: {channel_id}")
        if webhook:
            logger.info("Audit system set up with webhook URL for dev logs")
    else:
        logger.info("Audit system is disabled")
    
    # Add audit commands for admins
    _add_audit_commands(bot)
    
    return audit_service

def _add_audit_commands(bot: Union[discord.Client, commands.Bot]):
    """Add audit management commands to the bot"""
    
    @bot.tree.command(
        name="audit_config",
        description="Configure the audit system (Admin only)"
    )
    async def audit_config_command(
        interaction: discord.Interaction,
        enabled: Optional[bool] = None,
        channel_id: Optional[str] = None,
        use_webhook: Optional[bool] = None
    ):
        """Configure the audit system"""
        # Check if user is an admin
        if not interaction.user.guild_permissions.administrator:
            await interaction.response.send_message(
                "❌ You don't have permission to use this command.",
                ephemeral=True
            )
            return
        
        # Update configuration
        if enabled is not None:
            audit_service.enabled = enabled
            audit_service.log_to_console = enabled
            audit_service.log_to_channel = enabled and audit_service.dev_channel_id is not None
            audit_service.log_to_webhook = enabled and audit_service.webhook_url is not None
            
            request_visualizer.enabled = enabled
            request_visualizer.log_to_console = enabled
            request_visualizer.log_to_channel = enabled and request_visualizer.dev_channel_id is not None
            request_visualizer.log_to_webhook = enabled and request_visualizer.webhook_url is not None
        
        if channel_id:
            try:
                new_channel_id = int(channel_id)
                audit_service.dev_channel_id = new_channel_id
                audit_service.log_to_channel = audit_service.enabled and new_channel_id is not None
                
                request_visualizer.dev_channel_id = new_channel_id
                request_visualizer.log_to_channel = request_visualizer.enabled and new_channel_id is not None
            except ValueError:
                await interaction.response.send_message(
                    "❌ Invalid channel ID. Please provide a valid integer.",
                    ephemeral=True
                )
                return
                
        # Toggle webhook usage
        if use_webhook is not None:
            audit_service.log_to_webhook = use_webhook and audit_service.enabled
            request_visualizer.log_to_webhook = use_webhook and request_visualizer.enabled
            
            # Initialize session if needed
            if audit_service.log_to_webhook and not audit_service._session:
                import aiohttp
                audit_service._session = aiohttp.ClientSession()
        
        # Send confirmation
        await interaction.response.send_message(
            f"✅ Audit system configuration updated:\n"
            f"• Enabled: {audit_service.enabled}\n"
            f"• Developer Channel ID: {audit_service.dev_channel_id or 'Not set'}\n"
            f"• Webhook Logging: {'Enabled' if audit_service.log_to_webhook else 'Disabled'}",
            ephemeral=True
        )
        
        # Log configuration change
        await audit_service.log_audit_event(
            level=AuditLevel.INFO,
            message=f"Audit system configuration updated by {interaction.user.name}",
            data={
                "enabled": audit_service.enabled,
                "dev_channel_id": audit_service.dev_channel_id
            }
        )
    
    @bot.tree.command(
        name="toggle_webhook",
        description="Toggle webhook logging for dev logs (Admin only)"
    )
    async def toggle_webhook_command(interaction: discord.Interaction):
        """Toggle webhook logging for dev logs"""
        # Check if user is an admin
        if not interaction.user.guild_permissions.administrator:
            await interaction.response.send_message(
                "❌ You don't have permission to use this command.",
                ephemeral=True
            )
            return
            
        # Toggle webhook logging
        audit_service.log_to_webhook = not audit_service.log_to_webhook
        request_visualizer.log_to_webhook = not request_visualizer.log_to_webhook
        
        # Initialize session if needed
        if audit_service.log_to_webhook and not audit_service._session:
            import aiohttp
            audit_service._session = aiohttp.ClientSession()
        
        # Send confirmation
        status = "enabled" if audit_service.log_to_webhook else "disabled"
        await interaction.response.send_message(
            f"✅ Webhook logging is now {status}.",
            ephemeral=True
        )
        
        # Log configuration change
        await audit_service.log_audit_event(
            level=AuditLevel.INFO,
            message=f"Webhook logging {status} by {interaction.user.name}",
            data={
                "webhook_enabled": audit_service.log_to_webhook
            }
        )
    
    @bot.tree.command(
        name="test_audit",
        description="Test the audit system (Admin only)"
    )
    async def test_audit_command(interaction: discord.Interaction):
        """Test the audit system"""
        # Check if user is an admin
        if not interaction.user.guild_permissions.administrator:
            await interaction.response.send_message(
                "❌ You don't have permission to use this command.",
                ephemeral=True
            )
            return
        
        # Send test message
        await interaction.response.send_message(
            "🧪 Testing audit system...",
            ephemeral=True
        )
        
        # Log test events at different levels
        for level in AuditLevel:
            await audit_service.log_audit_event(
                level=level,
                message=f"Test audit event at {level.value} level",
                command_name="test_audit",
                data={
                    "test": True,
                    "level": level.value,
                    "timestamp": discord.utils.utcnow().isoformat()
                }
            )
        
        # Send test request visualization
        await request_visualizer.visualize_request(
            interaction=interaction,
            command_name="test_audit",
            args={"test": True},
            correlation_id="test-correlation-id"
        )
        
        # Send test response visualization
        await request_visualizer.visualize_response(
            interaction=interaction,
            command_name="test_audit",
            response_data="Test response data",
            execution_time=0.5,
            correlation_id="test-correlation-id",
            success=True
        )
        
        # Send confirmation
        await interaction.followup.send(
            "✅ Audit system test completed. Check the developer channel for results.",
            ephemeral=True
        )
