"""
Example Usage of Audit Integration

This file demonstrates how to use the audit integration system
in your Discord bot commands.
"""

import discord
from discord.ext import commands
from src.bot.client_audit_integration import add_audit_hooks
from src.bot.audit.audit_service import audit_service, AuditLevel

# Example of how to integrate audit hooks into your bot
async def setup_bot_with_audit():
    """Example of setting up a bot with audit integration"""
    
    # Create your bot
    bot = commands.Bot(command_prefix='!', intents=discord.Intents.default())
    
    # Add audit hooks
    # You can specify a developer channel ID and webhook URL
    dev_channel_id = 123456789012345678  # Replace with your dev channel ID
    webhook_url = "https://discord.com/api/webhooks/your-webhook-url"  # Replace with your webhook URL
    
    audit_integration = await add_audit_hooks(
        bot=bot,
        dev_channel_id=dev_channel_id,
        webhook_url=webhook_url,
        enabled=True
    )
    
    return bot, audit_integration

# Example of how to use audit in a command
class ExampleCommands(commands.Cog):
    """Example commands showing audit integration"""
    
    def __init__(self, bot):
        self.bot = bot
        self.audit_integration = None
    
    async def cog_load(self):
        """Called when the cog is loaded"""
        # Get audit integration from bot services
        self.audit_integration = self.bot.services.get('audit_service')
    
    @commands.slash_command(name="example_command", description="An example command with audit")
    async def example_command(self, ctx: discord.ApplicationContext, query: str):
        """Example command that demonstrates audit integration"""
        
        # Start tracking the request
        correlation_id = ""
        if self.audit_integration:
            correlation_id = await self.audit_integration.start_request(
                interaction=ctx.interaction,
                command_name="example_command",
                args={"query": query}
            )
        
        try:
            # Your command logic here
            result = await self.process_query(query)
            
            # Complete the request successfully
            if self.audit_integration:
                await self.audit_integration.complete_request(
                    correlation_id=correlation_id,
                    success=True,
                    response_data=result
                )
            
            await ctx.respond(f"Result: {result}")
            
        except Exception as e:
            # Log the error
            if self.audit_integration:
                await self.audit_integration.log_error(
                    interaction=ctx.interaction,
                    command_name="example_command",
                    error=e,
                    correlation_id=correlation_id
                )
                
                await self.audit_integration.complete_request(
                    correlation_id=correlation_id,
                    success=False,
                    error_message=str(e)
                )
            
            await ctx.respond(f"Error: {str(e)}", ephemeral=True)
    
    async def process_query(self, query: str) -> str:
        """Example processing function"""
        # Simulate some processing
        import asyncio
        await asyncio.sleep(0.1)
        return f"Processed: {query}"

# Example of manual audit logging
async def manual_audit_example():
    """Example of manual audit logging"""
    
    # Log an audit event
    await audit_service.log_audit_event(
        level=AuditLevel.INFO,
        message="Manual audit event example",
        command_name="manual_example",
        data={"example": True, "timestamp": "2024-01-01T00:00:00Z"}
    )
    
    # Log an error
    await audit_service.log_audit_event(
        level=AuditLevel.ERROR,
        message="Example error event",
        command_name="error_example",
        data={"error_type": "ExampleError", "error_message": "This is an example error"}
    )

# Example of using the request visualizer directly
async def visualization_example(interaction: discord.Interaction):
    """Example of using request visualizer directly"""
    
    from src.bot.audit.request_visualizer import request_visualizer
    
    # Visualize a request
    await request_visualizer.visualize_request(
        interaction=interaction,
        command_name="example_visualization",
        args={"param1": "value1", "param2": "value2"},
        correlation_id="example-correlation-id"
    )
    
    # Visualize a response
    await request_visualizer.visualize_response(
        interaction=interaction,
        command_name="example_visualization",
        response_data={"result": "success", "data": "example data"},
        execution_time=1.5,
        correlation_id="example-correlation-id",
        success=True
    )

if __name__ == "__main__":
    # Example of running the bot with audit integration
    import asyncio
    
    async def main():
        bot, audit_integration = await setup_bot_with_audit()
        
        # Add your cogs
        await bot.add_cog(ExampleCommands(bot))
        
        # Start the bot
        await bot.start("YOUR_BOT_TOKEN")
    
    # Run the bot
    # asyncio.run(main())
