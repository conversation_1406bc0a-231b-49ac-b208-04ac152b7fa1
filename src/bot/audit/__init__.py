"""
Audit System Package

This package provides comprehensive audit logging and request visualization
capabilities for the Discord bot, including:

- AuditService: Centralized audit logging
- RequestVisualizer: Discord command request visualization
- AuditSessionManager: Session management and data persistence
- AuditRateLimiter: Rate limiting for audit requests
- Setup functions for easy integration
"""

from .audit_service import audit_service, AuditLevel, AuditEvent, RequestMetrics
from .request_visualizer import request_visualizer, RequestVisualizer
from .session_manager import audit_session_manager, AuditSessionManager, AuditSession, AuditRecord
from .rate_limiter import audit_rate_limiter, AuditRateLimiter, RateLimitConfig
from .setup_audit import setup_audit_system, get_default_webhook_url

__all__ = [
    'audit_service',
    'AuditLevel',
    'AuditEvent',
    'RequestMetrics',
    'request_visualizer',
    'RequestVisualizer',
    'audit_session_manager',
    'AuditSessionManager',
    'AuditSession',
    'AuditRecord',
    'audit_rate_limiter',
    'AuditRateLimiter',
    'RateLimitConfig',
    'setup_audit_system',
    'get_default_webhook_url'
]
