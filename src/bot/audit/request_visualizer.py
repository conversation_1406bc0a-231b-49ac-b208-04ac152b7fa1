"""
Request Visualizer - Visualizes Discord command requests and responses.

This module provides comprehensive visualization of Discord command requests,
including request details, response data, and pipeline execution information.
"""

import asyncio
import json
import time
from datetime import datetime
from typing import Dict, Any, Optional, List, Union
import discord
from discord.ext import commands

from src.shared.error_handling.logging import get_logger, generate_correlation_id
from .audit_service import audit_service, AuditLevel
from .session_manager import audit_session_manager

logger = get_logger(__name__)

class RequestVisualizer:
    """Visualizes Discord command requests and responses"""
    
    def __init__(self):
        self.bot: Optional[discord.Client] = None
        self.dev_channel_id: Optional[int] = None
        self.webhook_url: Optional[str] = None
        self.enabled = True
        self.log_to_console = True
        self.log_to_channel = True
        self.log_to_webhook = False
        self.include_user_data = True
        self.include_guild_data = True
        self.max_content_length = 1000
        self.visualized_commands = set()
        
    async def initialize(self, bot: discord.Client, dev_channel_id: Optional[int] = None, webhook_url: Optional[str] = None):
        """Initialize the request visualizer"""
        self.bot = bot
        self.dev_channel_id = dev_channel_id
        self.webhook_url = webhook_url
        
        # Configure logging based on provided options
        self.log_to_channel = dev_channel_id is not None
        self.log_to_webhook = webhook_url is not None
        
        logger.info(f"Request visualizer initialized - Channel: {dev_channel_id}, Webhook: {bool(webhook_url)}")
    
    def configure(
        self,
        enabled: bool = True,
        log_to_console: bool = True,
        log_to_channel: bool = True,
        log_to_webhook: bool = False,
        include_user_data: bool = True,
        include_guild_data: bool = True,
        max_content_length: int = 1000
    ):
        """Configure visualizer settings"""
        self.enabled = enabled
        self.log_to_console = log_to_console
        self.log_to_channel = log_to_channel
        self.log_to_webhook = log_to_webhook
        self.include_user_data = include_user_data
        self.include_guild_data = include_guild_data
        self.max_content_length = max_content_length
        
        logger.info(f"Request visualizer configured - Enabled: {enabled}, Console: {log_to_console}, Channel: {log_to_channel}, Webhook: {log_to_webhook}")
    
    def register_commands(self, commands: List[str]):
        """Register commands to be visualized"""
        self.visualized_commands.update(commands)
        logger.info(f"Registered {len(commands)} commands for visualization: {commands}")
    
    async def visualize_request(
        self,
        interaction: discord.Interaction,
        command_name: str,
        args: Dict[str, Any],
        correlation_id: Optional[str] = None
    ):
        """Visualize a command request"""
        if not self.enabled or command_name not in self.visualized_commands:
            return
        
        correlation_id = correlation_id or generate_correlation_id()
        
        # Create request data
        request_data = {
            "correlation_id": correlation_id,
            "timestamp": datetime.utcnow().isoformat(),
            "command": command_name,
            "args": args,
            "user": {
                "id": interaction.user.id,
                "name": str(interaction.user),
                "display_name": interaction.user.display_name
            } if self.include_user_data else None,
            "guild": {
                "id": interaction.guild.id,
                "name": interaction.guild.name
            } if self.include_guild_data and interaction.guild else None,
            "channel": {
                "id": interaction.channel.id,
                "name": getattr(interaction.channel, 'name', 'DM')
            }
        }
        
        # Log to console
        if self.log_to_console:
            self._log_request_to_console(request_data)
        
        # Log to Discord channel
        if self.log_to_channel and self.dev_channel_id:
            await self._log_request_to_channel(interaction, request_data)
        
        # Log to webhook
        if self.log_to_webhook and self.webhook_url:
            await self._log_request_to_webhook(request_data)
    
    async def visualize_response(
        self,
        interaction: discord.Interaction,
        command_name: str,
        response_data: Any,
        execution_time: float,
        correlation_id: Optional[str] = None,
        success: bool = True
    ):
        """Visualize a command response"""
        if not self.enabled or command_name not in self.visualized_commands:
            return
        
        # Create response data
        response_info = {
            "correlation_id": correlation_id,
            "timestamp": datetime.utcnow().isoformat(),
            "command": command_name,
            "execution_time": execution_time,
            "success": success,
            "response_size": len(str(response_data)) if response_data else 0,
            "user_id": interaction.user.id,
            "guild_id": interaction.guild.id if interaction.guild else None
        }
        
        # Truncate response data if too long
        if response_data and len(str(response_data)) > self.max_content_length:
            response_info["response_preview"] = str(response_data)[:self.max_content_length] + "..."
        else:
            response_info["response_data"] = response_data
        
        # Log to console
        if self.log_to_console:
            self._log_response_to_console(response_info)
        
        # Log to Discord channel
        if self.log_to_channel and self.dev_channel_id:
            await self._log_response_to_channel(interaction, response_info)
        
        # Log to webhook
        if self.log_to_webhook and self.webhook_url:
            await self._log_response_to_webhook(response_info)
    
    def _log_request_to_console(self, request_data: Dict[str, Any]):
        """Log request to console"""
        logger.info(f"[REQUEST] {request_data['command']} - User: {request_data['user']['name'] if request_data['user'] else 'Unknown'} - Args: {request_data['args']}")
    
    def _log_response_to_console(self, response_info: Dict[str, Any]):
        """Log response to console"""
        status = "SUCCESS" if response_info['success'] else "FAILED"
        logger.info(f"[RESPONSE] {response_info['command']} - {status} - Time: {response_info['execution_time']:.2f}s")
    
    async def _log_request_to_channel(self, interaction: discord.Interaction, request_data: Dict[str, Any]):
        """Log request to Discord channel"""
        if not self.bot or not self.dev_channel_id:
            return
        
        try:
            channel = self.bot.get_channel(self.dev_channel_id)
            if not channel:
                logger.warning(f"Developer channel {self.dev_channel_id} not found")
                return
            
            embed = self._create_request_embed(request_data)
            await channel.send(embed=embed)
            
        except Exception as e:
            logger.error(f"Failed to log request to channel: {e}")
    
    async def _log_response_to_channel(self, interaction: discord.Interaction, response_info: Dict[str, Any]):
        """Log response to Discord channel"""
        if not self.bot or not self.dev_channel_id:
            return
        
        try:
            channel = self.bot.get_channel(self.dev_channel_id)
            if not channel:
                return
            
            embed = self._create_response_embed(response_info)
            await channel.send(embed=embed)
            
        except Exception as e:
            logger.error(f"Failed to log response to channel: {e}")
    
    async def _log_request_to_webhook(self, request_data: Dict[str, Any]):
        """Log request to webhook"""
        if not self.webhook_url:
            return
        
        try:
            payload = self._create_request_webhook_payload(request_data)
            await audit_service._log_to_webhook_payload(payload)
            
        except Exception as e:
            logger.error(f"Failed to log request to webhook: {e}")
    
    async def _log_response_to_webhook(self, response_info: Dict[str, Any]):
        """Log response to webhook"""
        if not self.webhook_url:
            return
        
        try:
            payload = self._create_response_webhook_payload(response_info)
            await audit_service._log_to_webhook_payload(payload)
            
        except Exception as e:
            logger.error(f"Failed to log response to webhook: {e}")
    
    def _create_request_embed(self, request_data: Dict[str, Any]) -> discord.Embed:
        """Create Discord embed for request"""
        embed = discord.Embed(
            title=f"🔍 Command Request: /{request_data['command']}",
            color=0x00ff00,
            timestamp=datetime.fromisoformat(request_data['timestamp'])
        )
        
        if request_data['user']:
            embed.add_field(name="User", value=f"{request_data['user']['name']} (ID: {request_data['user']['id']})", inline=True)
        
        if request_data['guild']:
            embed.add_field(name="Guild", value=f"{request_data['guild']['name']} (ID: {request_data['guild']['id']})", inline=True)
        
        embed.add_field(name="Channel", value=f"<#{request_data['channel']['id']}>", inline=True)
        embed.add_field(name="Correlation ID", value=request_data['correlation_id'], inline=True)
        
        if request_data['args']:
            args_str = json.dumps(request_data['args'], indent=2)[:self.max_content_length]
            if len(json.dumps(request_data['args'])) > self.max_content_length:
                args_str += "..."
            embed.add_field(name="Arguments", value=f"```json\n{args_str}\n```", inline=False)
        
        return embed
    
    def _create_response_embed(self, response_info: Dict[str, Any]) -> discord.Embed:
        """Create Discord embed for response"""
        color = 0x00ff00 if response_info['success'] else 0xff0000
        status = "✅ Success" if response_info['success'] else "❌ Failed"
        
        embed = discord.Embed(
            title=f"📤 Command Response: /{response_info['command']}",
            description=f"{status} - {response_info['execution_time']:.2f}s",
            color=color,
            timestamp=datetime.fromisoformat(response_info['timestamp'])
        )
        
        embed.add_field(name="Execution Time", value=f"{response_info['execution_time']:.2f}s", inline=True)
        embed.add_field(name="Response Size", value=f"{response_info['response_size']} chars", inline=True)
        embed.add_field(name="Correlation ID", value=response_info['correlation_id'], inline=True)
        
        if 'response_preview' in response_info:
            embed.add_field(name="Response Preview", value=f"```\n{response_info['response_preview']}\n```", inline=False)
        elif 'response_data' in response_info and response_info['response_data']:
            response_str = str(response_info['response_data'])[:self.max_content_length]
            if len(str(response_info['response_data'])) > self.max_content_length:
                response_str += "..."
            embed.add_field(name="Response Data", value=f"```\n{response_str}\n```", inline=False)
        
        return embed
    
    def _create_request_webhook_payload(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create webhook payload for request"""
        return {
            "content": None,
            "embeds": [self._create_request_embed(request_data).to_dict()]
        }
    
    def _create_response_webhook_payload(self, response_info: Dict[str, Any]) -> Dict[str, Any]:
        """Create webhook payload for response"""
        return {
            "content": None,
            "embeds": [self._create_response_embed(response_info).to_dict()]
        }

# Global request visualizer instance
request_visualizer = RequestVisualizer()