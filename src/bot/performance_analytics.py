"""
Performance Analytics for Trading Bot

This module provides comprehensive analytics and performance tracking
for the trading bot, including response times, success rates, and user engagement.
"""
import asyncio
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict, Counter
import statistics
from src.shared.error_handling.logging import get_logger

logger = get_logger(__name__)


@dataclass
class PerformanceMetric:
    """Represents a single performance metric."""
    name: str
    value: float
    timestamp: datetime
    tags: Optional[Dict[str, str]] = None
    description: Optional[str] = None


class PerformanceAnalytics:
    """Analyzes and tracks the performance of the trading bot."""
    
    def __init__(self):
        self._metrics: List[PerformanceMetric] = []
        self._request_times: Dict[str, List[float]] = defaultdict(list)
        self._request_outcomes: Dict[str, List[bool]] = defaultdict(list)
        self._user_engagement: Dict[str, Dict[str, Any]] = defaultdict(lambda: {
            'requests_count': 0,
            'successful_requests': 0,
            'last_activity': None,
            'total_response_time': 0.0
        })
        self._logger = get_logger("performance")
    
    def record_request(self, user_id: str, query_type: str, duration: float, success: bool):
        """Record a request with its performance metrics."""
        # Add to request times for this query type
        self._request_times[query_type].append(duration)
        
        # Record success/failure
        self._request_outcomes[query_type].append(success)
        
        # Update user engagement metrics
        user_data = self._user_engagement[user_id]
        user_data['requests_count'] += 1
        if success:
            user_data['successful_requests'] += 1
        user_data['last_activity'] = datetime.now()
        user_data['total_response_time'] += duration
        
        # Log the request
        self._logger.debug(f"Request recorded: user={user_id}, type={query_type}, "
                          f"duration={duration:.3f}s, success={success}")
    
    def get_average_response_time(self, query_type: Optional[str] = None) -> float:
        """Get the average response time for a query type or overall."""
        if query_type:
            times = self._request_times.get(query_type, [])
        else:
            times = [time for times_list in self._request_times.values() for time in times_list]
        
        return statistics.mean(times) if times else 0.0
    
    def get_success_rate(self, query_type: Optional[str] = None) -> float:
        """Get the success rate for a query type or overall."""
        if query_type:
            outcomes = self._request_outcomes.get(query_type, [])
        else:
            outcomes = [outcome for outcomes_list in self._request_outcomes.values() for outcome in outcomes_list]
        
        return (sum(outcomes) / len(outcomes) * 100) if outcomes else 0.0
    
    def get_request_count(self, query_type: Optional[str] = None) -> int:
        """Get the total request count for a query type or overall."""
        if query_type:
            outcomes = self._request_outcomes.get(query_type, [])
        else:
            outcomes = [outcome for outcomes_list in self._request_outcomes.values() for outcome in outcomes_list]
        
        return len(outcomes)
    
    def get_top_query_types(self, limit: int = 5) -> List[Tuple[str, int]]:
        """Get the top query types by request count."""
        counts = {
            query_type: len(outcomes) 
            for query_type, outcomes in self._request_outcomes.items()
        }
        sorted_counts = sorted(counts.items(), key=lambda x: x[1], reverse=True)
        return sorted_counts[:limit]
    
    def get_response_time_percentiles(self, query_type: Optional[str] = None, percentiles: List[int] = [50, 95, 99]) -> Dict[int, float]:
        """Get response time percentiles for a query type or overall."""
        if query_type:
            times = self._request_times.get(query_type, [])
        else:
            times = [time for times_list in self._request_times.values() for time in times_list]
        
        if not times:
            return {p: 0.0 for p in percentiles}
        
        times_sorted = sorted(times)
        result = {}
        
        for p in percentiles:
            index = int(len(times_sorted) * p / 100)
            if index >= len(times_sorted):
                index = len(times_sorted) - 1
            result[p] = times_sorted[index]
        
        return result
    
    def get_user_engagement_summary(self) -> Dict[str, Dict[str, Any]]:
        """Get summary of user engagement metrics."""
        summary = {}
        for user_id, data in self._user_engagement.items():
            avg_response_time = (
                data['total_response_time'] / data['requests_count'] 
                if data['requests_count'] > 0 else 0.0
            )
            
            success_rate = (
                data['successful_requests'] / data['requests_count'] * 100
                if data['requests_count'] > 0 else 0.0
            )
            
            summary[user_id] = {
                'requests_count': data['requests_count'],
                'successful_requests': data['successful_requests'],
                'success_rate_percent': success_rate,
                'average_response_time': avg_response_time,
                'last_activity': data['last_activity'].isoformat() if data['last_activity'] else None,
                'total_response_time': data['total_response_time']
            }
        
        return summary
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get a comprehensive performance summary."""
        return {
            'timestamp': datetime.now().isoformat(),
            'total_requests': self.get_request_count(),
            'overall_success_rate': self.get_success_rate(),
            'overall_avg_response_time': self.get_average_response_time(),
            'top_query_types': self.get_top_query_types(),
            'response_time_percentiles': self.get_response_time_percentiles(),
            'user_count': len(self._user_engagement),
            'query_types_count': len(self._request_outcomes)
        }
    
    def get_query_type_performance(self, query_type: str) -> Dict[str, Any]:
        """Get detailed performance metrics for a specific query type."""
        return {
            'query_type': query_type,
            'request_count': self.get_request_count(query_type),
            'success_rate': self.get_success_rate(query_type),
            'avg_response_time': self.get_average_response_time(query_type),
            'response_time_percentiles': self.get_response_time_percentiles(query_type),
            'trend_data': self._calculate_trend(query_type)
        }
    
    def _calculate_trend(self, query_type: str, hours: int = 24) -> Dict[str, Any]:
        """Calculate trend data for the specified number of hours."""
        # This would require storing timestamps which we're not currently doing
        # For now, return a placeholder
        outcomes = self._request_outcomes.get(query_type, [])
        times = self._request_times.get(query_type, [])
        
        return {
            'recent_count': len(outcomes[-10:]),  # Last 10 requests
            'recent_success_rate': (
                sum(outcomes[-10:]) / len(outcomes[-10:]) * 100 
                if len(outcomes[-10:]) > 0 else 0.0
            ),
            'recent_avg_response_time': (
                statistics.mean(times[-10:]) if len(times[-10:]) > 0 else 0.0
            )
        }
    
    def get_sla_compliance(self) -> Dict[str, Any]:
        """Calculate SLA compliance metrics."""
        # Define SLA thresholds
        response_time_threshold = 2.0  # seconds
        success_rate_threshold = 95.0  # percent
        
        overall_avg_response_time = self.get_average_response_time()
        overall_success_rate = self.get_success_rate()
        
        return {
            'response_time_sla_met': overall_avg_response_time <= response_time_threshold,
            'success_rate_sla_met': overall_success_rate >= success_rate_threshold,
            'current_response_time': overall_avg_response_time,
            'current_success_rate': overall_success_rate,
            'response_time_threshold': response_time_threshold,
            'success_rate_threshold': success_rate_threshold
        }
    
    def export_metrics(self) -> Dict[str, Any]:
        """Export metrics in a structured format."""
        return {
            'performance_summary': self.get_performance_summary(),
            'sla_compliance': self.get_sla_compliance(),
            'user_engagement': self.get_user_engagement_summary(),
            'query_type_performance': {
                qt: self.get_query_type_performance(qt) 
                for qt in self._request_outcomes.keys()
            }
        }


# Global performance analytics instance
performance_analytics = PerformanceAnalytics()


async def record_request(user_id: str, query_type: str, duration: float, success: bool):
    """Convenience function to record a request."""
    performance_analytics.record_request(user_id, query_type, duration, success)


async def get_performance_summary() -> Dict[str, Any]:
    """Convenience function to get performance summary."""
    return performance_analytics.get_performance_summary()


async def get_query_type_performance(query_type: str) -> Dict[str, Any]:
    """Convenience function to get specific query type performance."""
    return performance_analytics.get_query_type_performance(query_type)


async def get_sla_compliance() -> Dict[str, Any]:
    """Convenience function to get SLA compliance metrics."""
    return performance_analytics.get_sla_compliance()