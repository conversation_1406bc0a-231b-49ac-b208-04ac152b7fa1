"""
Market Sentiment Analyzer for Trading Bot

This module analyzes market sentiment from various sources including
news, social media, and technical indicators to provide sentiment scores
that can inform trading decisions.
"""
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass
from enum import Enum
import math
from src.shared.error_handling.logging import get_logger

logger = get_logger(__name__)


class SentimentSource(Enum):
    """Sources of market sentiment data."""
    NEWS = "news"
    SOCIAL_MEDIA = "social_media"
    TECHNICAL = "technical"
    OPTIONS = "options"
    INSTITUTIONAL = "institutional"


class SentimentType(Enum):
    """Types of sentiment classifications."""
    BULLISH = "bullish"
    BEARISH = "bearish"
    NEUTRAL = "neutral"
    VOLATILE = "volatile"


@dataclass
class SentimentData:
    """Data structure for sentiment analysis results."""
    symbol: str
    sentiment_score: float  # -1.0 to 1.0, where -1 is bearish, 1 is bullish
    sentiment_type: SentimentType
    confidence: float  # 0.0 to 1.0
    sources: List[Dict[str, Any]]
    timestamp: datetime
    explanation: Optional[str] = None


class MarketSentimentAnalyzer:
    """Analyzes market sentiment from multiple sources."""
    
    def __init__(self):
        self._logger = get_logger("sentiment_analyzer")
        self._sentiment_cache: Dict[str, SentimentData] = {}
        self._sentiment_history: Dict[str, List[SentimentData]] = {}
    
    async def analyze_sentiment(self, symbol: str, lookback_hours: int = 24) -> SentimentData:
        """Analyze market sentiment for a given symbol."""
        self._logger.info(f"Analyzing sentiment for symbol: {symbol}")
        
        # Check cache first
        cache_key = f"{symbol}_{lookback_hours}"
        if cache_key in self._sentiment_cache:
            cached_sentiment = self._sentiment_cache[cache_key]
            # Check if cache is still valid (less than 15 minutes old)
            if datetime.now() - cached_sentiment.timestamp < timedelta(minutes=15):
                self._logger.debug(f"Returning cached sentiment for {symbol}")
                return cached_sentiment
        
        # Gather sentiment from different sources
        news_sentiment = await self._analyze_news_sentiment(symbol, lookback_hours)
        social_sentiment = await self._analyze_social_sentiment(symbol, lookback_hours)
        technical_sentiment = await self._analyze_technical_sentiment(symbol)
        
        # Combine all sentiment sources
        combined_sentiment = self._combine_sentiments(
            news_sentiment, social_sentiment, technical_sentiment, symbol
        )
        
        # Cache the result
        self._sentiment_cache[cache_key] = combined_sentiment
        
        # Store in history
        if symbol not in self._sentiment_history:
            self._sentiment_history[symbol] = []
        self._sentiment_history[symbol].append(combined_sentiment)
        
        # Keep only recent history (last 100 entries)
        if len(self._sentiment_history[symbol]) > 100:
            self._sentiment_history[symbol] = self._sentiment_history[symbol][-100:]
        
        self._logger.info(f"Sentiment analysis completed for {symbol}: {combined_sentiment.sentiment_type.value} ({combined_sentiment.sentiment_score:.2f})")
        return combined_sentiment
    
    async def _analyze_news_sentiment(self, symbol: str, lookback_hours: int) -> Optional[SentimentData]:
        """Analyze news sentiment for a symbol."""
        # In a real implementation, this would call news APIs
        # For now, we'll simulate news sentiment based on mock data
        try:
            import random
            # Simulate news sentiment score between -0.5 and 0.5
            news_score = random.uniform(-0.5, 0.5)
            
            # Determine sentiment type
            if news_score > 0.1:
                sentiment_type = SentimentType.BULLISH
            elif news_score < -0.1:
                sentiment_type = SentimentType.BEARISH
            else:
                sentiment_type = SentimentType.NEUTRAL
            
            return SentimentData(
                symbol=symbol,
                sentiment_score=news_score,
                sentiment_type=sentiment_type,
                confidence=0.7,  # News confidence
                sources=[{
                    'source': SentimentSource.NEWS.value,
                    'score': news_score,
                    'count': 15  # Mock count of news articles
                }],
                timestamp=datetime.now(),
                explanation=f"News sentiment based on {15} recent articles"
            )
        except Exception as e:
            self._logger.error(f"Error analyzing news sentiment for {symbol}: {e}")
            return None
    
    async def _analyze_social_sentiment(self, symbol: str, lookback_hours: int) -> Optional[SentimentData]:
        """Analyze social media sentiment for a symbol."""
        # In a real implementation, this would call social media APIs
        # For now, we'll simulate social sentiment based on mock data
        try:
            import random
            # Simulate social sentiment score between -0.5 and 0.5
            social_score = random.uniform(-0.5, 0.5)
            
            # Determine sentiment type
            if social_score > 0.1:
                sentiment_type = SentimentType.BULLISH
            elif social_score < -0.1:
                sentiment_type = SentimentType.BEARISH
            else:
                sentiment_type = SentimentType.NEUTRAL
            
            return SentimentData(
                symbol=symbol,
                sentiment_score=social_score,
                sentiment_type=sentiment_type,
                confidence=0.6,  # Social confidence
                sources=[{
                    'source': SentimentSource.SOCIAL_MEDIA.value,
                    'score': social_score,
                    'count': 150  # Mock count of social posts
                }],
                timestamp=datetime.now(),
                explanation=f"Social sentiment based on {150} recent posts"
            )
        except Exception as e:
            self._logger.error(f"Error analyzing social sentiment for {symbol}: {e}")
            return None
    
    async def _analyze_technical_sentiment(self, symbol: str) -> Optional[SentimentData]:
        """Analyze technical sentiment for a symbol."""
        # In a real implementation, this would analyze technical indicators
        # For now, we'll simulate technical sentiment based on mock data
        try:
            import random
            # Simulate technical sentiment score between -0.5 and 0.5
            tech_score = random.uniform(-0.5, 0.5)
            
            # Determine sentiment type
            if tech_score > 0.1:
                sentiment_type = SentimentType.BULLISH
            elif tech_score < -0.1:
                sentiment_type = SentimentType.BEARISH
            else:
                sentiment_type = SentimentType.NEUTRAL
            
            return SentimentData(
                symbol=symbol,
                sentiment_score=tech_score,
                sentiment_type=sentiment_type,
                confidence=0.8,  # Technical confidence
                sources=[{
                    'source': SentimentSource.TECHNICAL.value,
                    'score': tech_score,
                    'indicators': ['RSI', 'MACD', 'Moving Average']
                }],
                timestamp=datetime.now(),
                explanation="Technical sentiment based on RSI, MACD, and Moving Average indicators"
            )
        except Exception as e:
            self._logger.error(f"Error analyzing technical sentiment for {symbol}: {e}")
            return None
    
    def _combine_sentiments(
        self, 
        news_sentiment: Optional[SentimentData], 
        social_sentiment: Optional[SentimentData], 
        technical_sentiment: Optional[SentimentData], 
        symbol: str
    ) -> SentimentData:
        """Combine sentiment from different sources with weighted average."""
        scores = []
        weights = []
        sources = []
        explanations = []
        
        # Weight news sentiment at 25%
        if news_sentiment:
            scores.append(news_sentiment.sentiment_score)
            weights.append(0.25)
            sources.extend(news_sentiment.sources)
            explanations.append(news_sentiment.explanation)
        
        # Weight social sentiment at 25%
        if social_sentiment:
            scores.append(social_sentiment.sentiment_score)
            weights.append(0.25)
            sources.extend(social_sentiment.sources)
            explanations.append(social_sentiment.explanation)
        
        # Weight technical sentiment at 50%
        if technical_sentiment:
            scores.append(technical_sentiment.sentiment_score)
            weights.append(0.5)
            sources.extend(technical_sentiment.sources)
            explanations.append(technical_sentiment.explanation)
        
        # If no scores, return neutral
        if not scores:
            return SentimentData(
                symbol=symbol,
                sentiment_score=0.0,
                sentiment_type=SentimentType.NEUTRAL,
                confidence=0.0,
                sources=[],
                timestamp=datetime.now(),
                explanation="No sentiment data available"
            )
        
        # Calculate weighted average
        weighted_sum = sum(score * weight for score, weight in zip(scores, weights))
        total_weight = sum(weights)
        combined_score = weighted_sum / total_weight if total_weight > 0 else 0.0
        
        # Calculate overall confidence as weighted average
        confidences = [s.confidence for s in [news_sentiment, social_sentiment, technical_sentiment] if s]
        if confidences:
            overall_confidence = sum(
                c * w for c, w in zip(confidences, [0.25, 0.25, 0.5][:len(confidences)])
            )
        else:
            overall_confidence = 0.5
        
        # Determine overall sentiment type
        if combined_score > 0.15:
            sentiment_type = SentimentType.BULLISH
        elif combined_score < -0.15:
            sentiment_type = SentimentType.BEARISH
        else:
            sentiment_type = SentimentType.NEUTRAL
        
        # If the score is very close to zero but any source showed volatility, mark as volatile
        if abs(combined_score) < 0.05:
            has_volatility = any(
                source.get('indicators', {}).get('volatility_score', 0) > 0.7
                for source in sources
            )
            if has_volatility:
                sentiment_type = SentimentType.VOLATILE
        
        return SentimentData(
            symbol=symbol,
            sentiment_score=combined_score,
            sentiment_type=sentiment_type,
            confidence=overall_confidence,
            sources=sources,
            timestamp=datetime.now(),
            explanation=f"Combined from: {', '.join(explanations)}"
        )
    
    def get_sentiment_history(self, symbol: str, limit: int = 10) -> List[SentimentData]:
        """Get historical sentiment for a symbol."""
        if symbol in self._sentiment_history:
            return self._sentiment_history[symbol][-limit:]
        return []
    
    def get_sentiment_trend(self, symbol: str, hours: int = 24) -> Dict[str, Any]:
        """Get sentiment trend for a symbol over specified hours."""
        history = self.get_sentiment_history(symbol, 100)  # Get more data for trend analysis
        
        if not history:
            return {
                'current_sentiment': 0.0,
                'trend_direction': 'stable',
                'volatility': 0.0,
                'confidence': 0.0
            }
        
        recent_history = [
            s for s in history 
            if datetime.now() - s.timestamp <= timedelta(hours=hours)
        ]
        
        if not recent_history:
            return {
                'current_sentiment': 0.0,
                'trend_direction': 'stable',
                'volatility': 0.0,
                'confidence': 0.0
            }
        
        scores = [s.sentiment_score for s in recent_history]
        current_sentiment = scores[-1]
        
        # Calculate trend direction
        if len(scores) >= 2:
            trend_direction = 'increasing' if scores[-1] > scores[0] else 'decreasing' if scores[-1] < scores[0] else 'stable'
        else:
            trend_direction = 'stable'
        
        # Calculate volatility (standard deviation of scores)
        if len(scores) > 1:
            avg_score = sum(scores) / len(scores)
            variance = sum((score - avg_score) ** 2 for score in scores) / len(scores)
            volatility = math.sqrt(variance)
        else:
            volatility = 0.0
        
        # Average confidence of recent readings
        avg_confidence = sum(s.confidence for s in recent_history) / len(recent_history)
        
        return {
            'current_sentiment': current_sentiment,
            'trend_direction': trend_direction,
            'volatility': volatility,
            'confidence': avg_confidence,
            'data_points': len(scores)
        }
    
    async def analyze_sector_sentiment(self, symbols: List[str]) -> Dict[str, SentimentData]:
        """Analyze sentiment for multiple symbols to get sector sentiment."""
        sector_sentiments = {}
        
        for symbol in symbols:
            sentiment = await self.analyze_sentiment(symbol)
            sector_sentiments[symbol] = sentiment
        
        # Calculate average sector sentiment
        all_scores = [s.sentiment_score for s in sector_sentiments.values()]
        avg_sector_sentiment = sum(all_scores) / len(all_scores) if all_scores else 0.0
        
        sector_confidence = sum(s.confidence for s in sector_sentiments.values()) / len(sector_sentiments) if sector_sentiments else 0.0
        
        return {
            'sector_average': {
                'sentiment_score': avg_sector_sentiment,
                'confidence': sector_confidence,
                'symbols_analyzed': len(symbols)
            },
            'individual_sentiments': sector_sentiments
        }


# Global sentiment analyzer instance
market_sentiment_analyzer = MarketSentimentAnalyzer()


async def analyze_sentiment(symbol: str, lookback_hours: int = 24) -> SentimentData:
    """Convenience function to analyze sentiment for a symbol."""
    return await market_sentiment_analyzer.analyze_sentiment(symbol, lookback_hours)


def get_sentiment_history(symbol: str, limit: int = 10) -> List[SentimentData]:
    """Convenience function to get sentiment history."""
    return market_sentiment_analyzer.get_sentiment_history(symbol, limit)


def get_sentiment_trend(symbol: str, hours: int = 24) -> Dict[str, Any]:
    """Convenience function to get sentiment trend."""
    return market_sentiment_analyzer.get_sentiment_trend(symbol, hours)