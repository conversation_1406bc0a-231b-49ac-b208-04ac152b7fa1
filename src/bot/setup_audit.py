"""
Setup Audit for Trading Bot

This module performs comprehensive audits of the trading bot setup
including security checks, configuration validation, and system health assessment.
"""
import asyncio
import logging
import os
import json
from datetime import datetime
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from enum import Enum
import subprocess
import sys
from src.shared.error_handling.logging import get_logger

logger = get_logger(__name__)


class AuditCategory(Enum):
    """Categories of audit checks."""
    SECURITY = "security"
    CONFIGURATION = "configuration"
    PERFORMANCE = "performance"
    DATA_INTEGRITY = "data_integrity"
    COMPLIANCE = "compliance"
    DEPENDENCIES = "dependencies"


class AuditSeverity(Enum):
    """Severity levels for audit findings."""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class AuditFinding:
    """Result of a single audit check."""
    check_id: str
    category: AuditCategory
    severity: AuditSeverity
    title: str
    description: str
    recommendation: str
    timestamp: datetime
    is_resolved: bool = False


class SetupAudit:
    """Performs comprehensive setup audits for the trading bot."""
    
    def __init__(self):
        self._logger = get_logger("setup_audit")
        self._audit_findings: List[AuditFinding] = []
        self._audit_history: List[Dict[str, Any]] = []
        self._config_checks = {}
    
    async def run_comprehensive_audit(self) -> Dict[str, Any]:
        """Run a comprehensive audit of the bot setup."""
        start_time = datetime.now()
        self._logger.info("Starting comprehensive setup audit")
        
        # Run all audit categories
        security_results = await self._audit_security()
        config_results = await self._audit_configuration()
        performance_results = await self._audit_performance()
        dependencies_results = await self._audit_dependencies()
        data_integrity_results = await self._audit_data_integrity()
        
        # Combine results
        all_findings = (
            security_results + config_results + performance_results + 
            dependencies_results + data_integrity_results
        )
        
        # Store findings
        self._audit_findings.extend(all_findings)
        
        # Keep only recent findings (last 1000)
        if len(self._audit_findings) > 1000:
            self._audit_findings = self._audit_findings[-1000:]
        
        # Calculate audit summary
        summary = self._calculate_audit_summary(all_findings)
        
        # Add to audit history
        audit_report = {
            'timestamp': start_time.isoformat(),
            'end_timestamp': datetime.now().isoformat(),
            'duration_seconds': (datetime.now() - start_time).total_seconds(),
            'total_findings': len(all_findings),
            'summary': summary,
            'findings': [
                {
                    'check_id': f.check_id,
                    'category': f.category.value,
                    'severity': f.severity.value,
                    'title': f.title,
                    'description': f.description,
                    'recommendation': f.recommendation,
                    'timestamp': f.timestamp.isoformat(),
                    'is_resolved': f.is_resolved
                }
                for f in all_findings
            ]
        }
        
        self._audit_history.append(audit_report)
        
        # Keep only recent history (last 50 audits)
        if len(self._audit_history) > 50:
            self._audit_history = self._audit_history[-50:]
        
        self._logger.info(f"Audit completed with {len(all_findings)} findings in {audit_report['duration_seconds']:.2f}s")
        
        return audit_report
    
    async def _audit_security(self) -> List[AuditFinding]:
        """Audit security configuration."""
        findings = []
        
        # Check for sensitive environment variables
        sensitive_vars = ['API_KEY', 'SECRET_KEY', 'PASSWORD', 'TOKEN']
        for var in sensitive_vars:
            if var in os.environ:
                if len(os.environ[var]) > 0:
                    findings.append(AuditFinding(
                        check_id="SEC-001",
                        category=AuditCategory.SECURITY,
                        severity=AuditSeverity.INFO,
                        title=f"Environment variable {var} is set",
                        description=f"The environment variable {var} is present in the environment",
                        recommendation="Ensure this variable is properly secured and not exposed in logs",
                        timestamp=datetime.now()
                    ))
        
        # Check file permissions
        try:
            if os.path.exists('.env'):
                stat_info = os.stat('.env')
                permissions = stat_info.st_mode
                
                # Check if .env file is readable by group/other (not ideal for security)
                if permissions & 0o077:  # Group or other has permissions
                    findings.append(AuditFinding(
                        check_id="SEC-002",
                        category=AuditCategory.SECURITY,
                        severity=AuditSeverity.WARNING,
                        title=".env file permissions",
                        description="The .env file has overly permissive access (readable by group/other)",
                        recommendation="Set .env file permissions to 600 (owner read/write only): chmod 600 .env",
                        timestamp=datetime.now()
                    ))
        except Exception as e:
            findings.append(AuditFinding(
                check_id="SEC-003",
                category=AuditCategory.SECURITY,
                severity=AuditSeverity.ERROR,
                title="Error checking file permissions",
                description=f"Could not check .env file permissions: {e}",
                recommendation="Verify file permissions manually",
                timestamp=datetime.now()
            ))
        
        # Check for debug mode
        if os.getenv('DEBUG', '').lower() in ['true', '1', 'yes']:
            findings.append(AuditFinding(
                check_id="SEC-004",
                category=AuditCategory.SECURITY,
                severity=AuditSeverity.WARNING,
                title="Debug mode enabled",
                description="Application is running in debug mode, which may expose sensitive information",
                recommendation="Set DEBUG=false for production environments",
                timestamp=datetime.now()
            ))
        
        return findings
    
    async def _audit_configuration(self) -> List[AuditFinding]:
        """Audit configuration settings."""
        findings = []
        
        # Check required configuration
        required_configs = [
            'API_BASE_URL',
            'DATABASE_URL',
            'LOG_LEVEL'
        ]
        
        for config in required_configs:
            if not os.getenv(config):
                findings.append(AuditFinding(
                    check_id="CONFIG-001",
                    category=AuditCategory.CONFIGURATION,
                    severity=AuditSeverity.WARNING,
                    title=f"Missing required configuration: {config}",
                    description=f"The configuration variable {config} is not set",
                    recommendation=f"Set the {config} environment variable",
                    timestamp=datetime.now()
                ))
        
        # Check log level configuration
        log_level = os.getenv('LOG_LEVEL', 'INFO').upper()
        if log_level not in ['DEBUG', 'INFO', 'WARNING', 'ERROR']:
            findings.append(AuditFinding(
                check_id="CONFIG-002",
                category=AuditCategory.CONFIGURATION,
                severity=AuditSeverity.ERROR,
                title="Invalid log level configuration",
                description=f"LOG_LEVEL is set to invalid value: {log_level}",
                recommendation="Set LOG_LEVEL to DEBUG, INFO, WARNING, or ERROR",
                timestamp=datetime.now()
            ))
        
        # Check database URL format
        db_url = os.getenv('DATABASE_URL', '')
        if db_url and not db_url.startswith(('sqlite://', 'postgresql://', 'mysql://', 'mongodb://')):
            findings.append(AuditFinding(
                check_id="CONFIG-003",
                category=AuditCategory.CONFIGURATION,
                severity=AuditSeverity.WARNING,
                title="Unusual database URL format",
                description=f"DATABASE_URL has unusual format: {db_url[:50]}...",
                recommendation="Verify database URL format is correct",
                timestamp=datetime.now()
            ))
        
        return findings
    
    async def _audit_performance(self) -> List[AuditFinding]:
        """Audit performance configuration."""
        findings = []
        
        # Check for performance-related config
        performance_configs = [
            ('MAX_WORKERS', 10),
            ('CACHE_SIZE', 100),
            ('CONNECTION_POOL_SIZE', 5)
        ]
        
        for config_name, default_value in performance_configs:
            value = os.getenv(config_name)
            if not value:
                findings.append(AuditFinding(
                    check_id="PERF-001",
                    category=AuditCategory.PERFORMANCE,
                    severity=AuditSeverity.INFO,
                    title=f"Performance config {config_name} not set",
                    description=f"The performance configuration {config_name} is not explicitly set, using system defaults",
                    recommendation=f"Consider setting {config_name} for optimal performance",
                    timestamp=datetime.now()
                ))
            else:
                try:
                    int_value = int(value)
                    if int_value <= 0:
                        findings.append(AuditFinding(
                            check_id="PERF-002",
                            category=AuditCategory.PERFORMANCE,
                            severity=AuditSeverity.WARNING,
                            title=f"Invalid performance config value for {config_name}",
                            description=f"The value for {config_name} is {value}, which may impact performance",
                            recommendation=f"Set {config_name} to a positive integer",
                            timestamp=datetime.now()
                        ))
                except ValueError:
                    findings.append(AuditFinding(
                        check_id="PERF-003",
                        category=AuditCategory.PERFORMANCE,
                        severity=AuditSeverity.ERROR,
                        title=f"Invalid performance config type for {config_name}",
                        description=f"The value for {config_name} is {value}, should be an integer",
                        recommendation=f"Set {config_name} to a valid integer value",
                        timestamp=datetime.now()
                    ))
        
        return findings
    
    async def _audit_dependencies(self) -> List[AuditFinding]:
        """Audit installed dependencies."""
        findings = []
        
        try:
            # Check for common security issues in dependencies
            result = subprocess.run([sys.executable, '-m', 'pip', 'list', '--format=json'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                packages = json.loads(result.stdout)
                
                # Check for outdated packages (this is a simplified check)
                outdated_count = 0
                for package in packages:
                    if 'outdated' in package.get('version', '').lower():
                        outdated_count += 1
                
                if outdated_count > 0:
                    findings.append(AuditFinding(
                        check_id="DEP-001",
                        category=AuditCategory.DEPENDENCIES,
                        severity=AuditSeverity.WARNING,
                        title=f"{outdated_count} potentially outdated packages found",
                        description=f"There may be {outdated_count} outdated packages that need updating",
                        recommendation="Run 'pip list --outdated' and update packages as needed",
                        timestamp=datetime.now()
                    ))
            
        except Exception as e:
            findings.append(AuditFinding(
                check_id="DEP-002",
                category=AuditCategory.DEPENDENCIES,
                severity=AuditSeverity.ERROR,
                title="Error checking dependencies",
                description=f"Could not check dependencies: {e}",
                recommendation="Verify pip and package management tools are working",
                timestamp=datetime.now()
            ))
        
        return findings
    
    async def _audit_data_integrity(self) -> List[AuditFinding]:
        """Audit data integrity and backup configurations."""
        findings = []
        
        # Check for backup directory
        backup_dir = os.getenv('BACKUP_DIR', 'backups')
        if not os.path.exists(backup_dir):
            findings.append(AuditFinding(
                check_id="DATA-001",
                category=AuditCategory.DATA_INTEGRITY,
                severity=AuditSeverity.WARNING,
                title="Backup directory does not exist",
                description=f"The backup directory {backup_dir} does not exist",
                recommendation=f"Create the backup directory: mkdir -p {backup_dir}",
                timestamp=datetime.now()
            ))
        
        # Check for backup configuration
        backup_enabled = os.getenv('BACKUP_ENABLED', '').lower()
        if backup_enabled not in ['true', '1', 'yes']:
            findings.append(AuditFinding(
                check_id="DATA-002",
                category=AuditCategory.DATA_INTEGRITY,
                severity=AuditSeverity.WARNING,
                title="Backup system not enabled",
                description="The backup system is not enabled",
                recommendation="Set BACKUP_ENABLED=true to enable automatic backups",
                timestamp=datetime.now()
            ))
        
        # Check for data retention policy
        if not os.getenv('DATA_RETENTION_DAYS'):
            findings.append(AuditFinding(
                check_id="DATA-003",
                category=AuditCategory.DATA_INTEGRITY,
                severity=AuditSeverity.INFO,
                title="Data retention policy not configured",
                description="No data retention policy is configured",
                recommendation="Set DATA_RETENTION_DAYS to specify how long data should be retained",
                timestamp=datetime.now()
            ))
        
        return findings
    
    def _calculate_audit_summary(self, findings: List[AuditFinding]) -> Dict[str, int]:
        """Calculate summary statistics for audit findings."""
        summary = {
            'total': len(findings),
            'critical': 0,
            'error': 0,
            'warning': 0,
            'info': 0
        }
        
        for finding in findings:
            summary[finding.severity.value] += 1
        
        return summary
    
    async def get_audit_status(self) -> Dict[str, Any]:
        """Get the overall audit status."""
        if not self._audit_findings:
            return {
                'status': 'no_audits_performed',
                'last_audit': None,
                'total_findings': 0,
                'critical_findings': 0,
                'timestamp': datetime.now().isoformat()
            }
        
        recent_findings = [
            f for f in self._audit_findings 
            if datetime.now() - f.timestamp < timedelta(hours=24)
        ]
        
        critical_findings = [f for f in recent_findings if f.severity == AuditSeverity.CRITICAL]
        
        status = 'healthy'
        if any(f.severity in [AuditSeverity.CRITICAL, AuditSeverity.ERROR] for f in recent_findings):
            status = 'unhealthy'
        elif any(f.severity == AuditSeverity.WARNING for f in recent_findings):
            status = 'needs_attention'
        
        return {
            'status': status,
            'last_audit': max(f.timestamp for f in self._audit_findings).isoformat(),
            'total_findings': len(recent_findings),
            'critical_findings': len(critical_findings),
            'unresolved_findings': len([f for f in recent_findings if not f.is_resolved]),
            'timestamp': datetime.now().isoformat()
        }
    
    async def mark_finding_resolved(self, check_id: str) -> bool:
        """Mark an audit finding as resolved."""
        for finding in self._audit_findings:
            if finding.check_id == check_id:
                finding.is_resolved = True
                self._logger.info(f"Marked audit finding {check_id} as resolved")
                return True
        return False
    
    async def get_audit_report(self, include_resolved: bool = False) -> Dict[str, Any]:
        """Get a detailed audit report."""
        all_findings = self._audit_findings
        if not include_resolved:
            all_findings = [f for f in all_findings if not f.is_resolved]
        
        findings_by_category = {}
        for finding in all_findings:
            category = finding.category.value
            if category not in findings_by_category:
                findings_by_category[category] = []
            findings_by_category[category].append(finding)
        
        return {
            'timestamp': datetime.now().isoformat(),
            'total_findings': len(all_findings),
            'findings_by_category': {
                category: [
                    {
                        'check_id': f.check_id,
                        'severity': f.severity.value,
                        'title': f.title,
                        'description': f.description,
                        'recommendation': f.recommendation,
                        'timestamp': f.timestamp.isoformat(),
                        'is_resolved': f.is_resolved
                    }
                    for f in findings
                ]
                for category, findings in findings_by_category.items()
            },
            'summary': self._calculate_audit_summary(all_findings)
        }
    
    async def run_security_audit(self) -> Dict[str, Any]:
        """Run only the security audit."""
        security_findings = await self._audit_security()
        
        summary = self._calculate_audit_summary(security_findings)
        
        return {
            'category': 'security',
            'timestamp': datetime.now().isoformat(),
            'findings': [
                {
                    'check_id': f.check_id,
                    'severity': f.severity.value,
                    'title': f.title,
                    'description': f.description,
                    'recommendation': f.recommendation,
                    'timestamp': f.timestamp.isoformat()
                }
                for f in security_findings
            ],
            'summary': summary
        }
    
    async def get_recommendations(self) -> List[Dict[str, str]]:
        """Get prioritized recommendations for addressing audit findings."""
        unresolved_findings = [f for f in self._audit_findings if not f.is_resolved]
        
        # Sort by severity and category priority
        priority_order = {
            AuditSeverity.CRITICAL: 1,
            AuditSeverity.ERROR: 2,
            AuditSeverity.WARNING: 3,
            AuditSeverity.INFO: 4
        }
        
        unresolved_findings.sort(
            key=lambda x: (priority_order[x.severity], x.category.value)
        )
        
        recommendations = []
        for finding in unresolved_findings:
            recommendations.append({
                'check_id': finding.check_id,
                'severity': finding.severity.value,
                'category': finding.category.value,
                'title': finding.title,
                'recommendation': finding.recommendation,
                'description': finding.description
            })
        
        return recommendations


# Global setup audit instance
setup_audit = SetupAudit()


async def run_comprehensive_audit() -> Dict[str, Any]:
    """Convenience function to run comprehensive audit."""
    return await setup_audit.run_comprehensive_audit()


async def get_audit_status() -> Dict[str, Any]:
    """Convenience function to get audit status."""
    return await setup_audit.get_audit_status()


async def run_security_audit() -> Dict[str, Any]:
    """Convenience function to run security-only audit."""
    return await setup_audit.run_security_audit()


async def get_recommendations() -> List[Dict[str, str]]:
    """Convenience function to get recommendations."""
    return await setup_audit.get_recommendations()