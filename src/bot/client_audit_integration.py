"""
Bot Client Audit Integration

Integrates the request visualization and audit system with the main bot client.
This module adds hooks to automatically visualize all requests and send them to a developer channel.
"""

import discord
import asyncio
import time
from typing import Dict, Any, Optional
from datetime import datetime

from .audit.audit_service import audit_service, AuditLevel
from .audit.request_visualizer import request_visualizer
from .audit.setup_audit import setup_audit_system, get_default_webhook_url
from src.shared.error_handling.logging import get_logger, generate_correlation_id

logger = get_logger(__name__)

class AuditIntegration:
    """
    Integrates request visualization and audit logging with the Discord bot
    
    This class adds hooks to automatically visualize all requests and
    send them to a developer channel for auditing.
    """
    
    def __init__(self, bot, dev_channel_id: Optional[int] = None, webhook_url: Optional[str] = None):
        """
        Initialize the audit integration
        
        Args:
            bot: The Discord bot instance
            dev_channel_id: The ID of the developer channel to send audit logs to
            webhook_url: The webhook URL to send audit logs to
        """
        self.bot = bot
        self.dev_channel_id = dev_channel_id
        self.webhook_url = webhook_url or get_default_webhook_url()
        self.enabled = True
        self.active_requests: Dict[str, Dict[str, Any]] = {}
        
        # Register bot event handlers for proper session management
        if hasattr(bot, 'event'):
            @bot.event
            async def on_ready():
                logger.info("Bot is ready, initializing audit system...")
                await self._initialize_audit_system()
                        
            @bot.event
            async def on_close():
                logger.info("Bot is shutting down, cleaning up audit system...")
                await self._cleanup_audit_system()
        
    async def _initialize_audit_system(self):
        """Initialize the audit system"""
        try:
            # Set up the audit system
            await setup_audit_system(
                bot=self.bot, 
                dev_channel_id=self.dev_channel_id,
                webhook_url=self.webhook_url,
                enable_logging=self.enabled
            )
            
            logger.info(f"✅ Audit integration initialized with developer channel ID: {self.dev_channel_id}")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize audit system: {e}")
    
    async def _cleanup_audit_system(self):
        """Cleanup the audit system"""
        try:
            await audit_service.close()
            logger.info("✅ Audit system cleanup completed")
        except Exception as e:
            logger.error(f"❌ Error during audit system cleanup: {e}")
    
    async def setup(self):
        """Set up the audit integration"""
        await self._initialize_audit_system()
        return audit_service
    
    async def start_request(self, interaction: discord.Interaction, command_name: str, args: Dict[str, Any]) -> str:
        """Start tracking a request"""
        if not self.enabled:
            return ""
        
        correlation_id = generate_correlation_id()
        
        # Store request info
        self.active_requests[correlation_id] = {
            "interaction": interaction,
            "command_name": command_name,
            "args": args,
            "start_time": time.time()
        }
        
        # Log request start
        await audit_service.log_audit_event(
            level=AuditLevel.INFO,
            message=f"Command request started: /{command_name}",
            command_name=command_name,
            user_id=interaction.user.id,
            guild_id=interaction.guild.id if interaction.guild else None,
            data={"args": args},
            correlation_id=correlation_id
        )
        
        # Visualize request
        await request_visualizer.visualize_request(
            interaction=interaction,
            command_name=command_name,
            args=args,
            correlation_id=correlation_id
        )
        
        return correlation_id
    
    async def complete_request(
        self,
        correlation_id: str,
        success: bool = True,
        error_message: Optional[str] = None,
        response_data: Optional[Any] = None
    ):
        """Complete tracking a request"""
        if not self.enabled or correlation_id not in self.active_requests:
            return
        
        request_info = self.active_requests[correlation_id]
        interaction = request_info["interaction"]
        command_name = request_info["command_name"]
        start_time = request_info["start_time"]
        
        # Calculate duration
        end_time = time.time()
        duration = end_time - start_time
        
        # Log request completion
        level = AuditLevel.INFO if success else AuditLevel.ERROR
        message = f"Command request completed: /{command_name} ({duration:.2f}s)"
        
        await audit_service.log_audit_event(
            level=level,
            message=message,
            command_name=command_name,
            user_id=interaction.user.id,
            guild_id=interaction.guild.id if interaction.guild else None,
            data={
                "duration": duration,
                "success": success,
                "error": error_message,
                "response_size": len(str(response_data)) if response_data else 0
            },
            correlation_id=correlation_id
        )
        
        # Visualize response
        await request_visualizer.visualize_response(
            interaction=interaction,
            command_name=command_name,
            response_data=response_data,
            execution_time=duration,
            correlation_id=correlation_id,
            success=success
        )
        
        # Clean up
        del self.active_requests[correlation_id]
    
    async def log_error(
        self,
        interaction: discord.Interaction,
        command_name: str,
        error: Exception,
        correlation_id: Optional[str] = None
    ):
        """Log a command error"""
        await audit_service.log_error(interaction, command_name, error, correlation_id)

async def add_audit_hooks(
    bot, 
    dev_channel_id: Optional[int] = None, 
    webhook_url: Optional[str] = None, 
    enabled: bool = True
):
    """
    Add audit hooks to the Discord bot
    
    This function adds hooks to automatically visualize all requests and
    send them to a developer channel or webhook for auditing.
    
    Args:
        bot: The Discord bot instance
        dev_channel_id: The ID of the developer channel to send audit logs to
        webhook_url: The webhook URL to send audit logs to
        enabled: Whether to enable audit logging
        
    Returns:
        The audit integration instance
    """
    integration = AuditIntegration(bot, dev_channel_id, webhook_url)
    integration.enabled = enabled
    await integration.setup()
    return integration

def add_audit_hooks_sync(
    bot, 
    dev_channel_id: Optional[int] = None, 
    webhook_url: Optional[str] = None, 
    enabled: bool = True
):
    """
    Synchronous version of add_audit_hooks for compatibility
    
    This function adds hooks to automatically visualize all requests and
    send them to a developer channel or webhook for auditing.
    
    Args:
        bot: The Discord bot instance
        dev_channel_id: The ID of the developer channel to send audit logs to
        webhook_url: The webhook URL to send audit logs to
        enabled: Whether to enable audit logging
        
    Returns:
        The audit integration instance
    """
    integration = AuditIntegration(bot, dev_channel_id, webhook_url)
    integration.enabled = enabled
    
    # Schedule setup for asynchronous execution
    asyncio.create_task(integration.setup())
    
    logger.info("Scheduled audit system setup for asynchronous execution")
    return integration