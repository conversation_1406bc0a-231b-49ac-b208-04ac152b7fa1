"""
Pipeline Framework for Trading Bot

This module provides a flexible, extensible framework for processing trading requests
through a series of configurable stages. Each stage can perform validation, data fetching,
analysis, and response generation.
"""
import asyncio
import logging
from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Union
from enum import Enum
from src.shared.error_handling.logging import get_logger

logger = get_logger(__name__)


@dataclass
class PipelineContext:
    """
    Context object that flows through the pipeline, carrying data between stages.
    """
    query: str
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    raw_data: Optional[Dict[str, Any]] = None
    processed_data: Optional[Dict[str, Any]] = None
    response: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None
    correlation_id: Optional[str] = None
    pipeline_state: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.raw_data is None:
            self.raw_data = {}
        if self.processed_data is None:
            self.processed_data = {}
        if self.response is None:
            self.response = {}
        if self.metadata is None:
            self.metadata = {}
        if self.pipeline_state is None:
            self.pipeline_state = {}


class PipelineStageStatus(Enum):
    """Status of a pipeline stage execution."""
    SUCCESS = "success"
    FAILURE = "failure"
    SKIPPED = "skipped"
    PENDING = "pending"


@dataclass
class StageResult:
    """Result returned by each pipeline stage."""
    status: PipelineStageStatus
    context: PipelineContext
    error: Optional[Exception] = None
    message: Optional[str] = None


class PipelineStage(ABC):
    """Abstract base class for all pipeline stages."""
    
    def __init__(self, name: str):
        self.name = name
        self.logger = get_logger(f"pipeline.{name}")
    
    @abstractmethod
    async def execute(self, context: PipelineContext) -> StageResult:
        """Execute the pipeline stage."""
        pass


class Pipeline:
    """Main pipeline class that orchestrates the execution of stages."""
    
    def __init__(self, stages: List[PipelineStage], name: str = "unnamed"):
        self.stages = stages
        self.name = name
        self.logger = get_logger(f"pipeline.{name}")
    
    async def execute(self, context: PipelineContext) -> PipelineContext:
        """Execute all stages in sequence."""
        self.logger.info(f"Starting pipeline execution for query: {context.query}")
        
        for stage in self.stages:
            self.logger.debug(f"Executing stage: {stage.name}")
            try:
                result = await stage.execute(context)
                
                if result.status == PipelineStageStatus.FAILURE:
                    self.logger.error(f"Stage {stage.name} failed: {result.error}")
                    # Depending on requirements, we might continue or fail fast
                    # For now, we'll continue with error handling
                    context.pipeline_state[f"{stage.name}_error"] = str(result.error)
                    continue
                elif result.status == PipelineStageStatus.SKIPPED:
                    self.logger.info(f"Stage {stage.name} was skipped")
                    continue
                
                context = result.context
                self.logger.debug(f"Stage {stage.name} completed successfully")
                
            except Exception as e:
                self.logger.error(f"Unexpected error in stage {stage.name}: {e}", exc_info=True)
                context.pipeline_state[f"{stage.name}_error"] = str(e)
        
        self.logger.info(f"Pipeline execution completed for query: {context.query}")
        return context


class InputValidationStage(PipelineStage):
    """Validates the input query and user context."""
    
    def __init__(self):
        super().__init__("input_validation")
    
    async def execute(self, context: PipelineContext) -> StageResult:
        """Validate the input and set appropriate flags."""
        try:
            # Basic validation
            if not context.query or not isinstance(context.query, str):
                return StageResult(
                    status=PipelineStageStatus.FAILURE,
                    context=context,
                    error=Exception("Invalid query: must be a non-empty string"),
                    message="Input validation failed"
                )
            
            # Additional validation can be added here
            
            context.metadata["input_validated"] = True
            return StageResult(
                status=PipelineStageStatus.SUCCESS,
                context=context,
                message="Input validation passed"
            )
        except Exception as e:
            return StageResult(
                status=PipelineStageStatus.FAILURE,
                context=context,
                error=e,
                message="Input validation failed"
            )


class DataFetchStage(PipelineStage):
    """Fetches required data for processing the query."""
    
    def __init__(self):
        super().__init__("data_fetch")
    
    async def execute(self, context: PipelineContext) -> StageResult:
        """Fetch required data and store in context."""
        try:
            # Extract symbols or other relevant data from query
            # This is where we would call data providers
            # For now, just demonstrate the pattern
            
            # Example: extract symbols from query
            query_lower = context.query.lower()
            symbols = []
            if 'symbol' in query_lower or any(ticker in query_lower for ticker in ['aapl', 'msft', 'goog', 'tsla', 'nvda']):
                # In a real implementation, extract actual symbols from query
                symbols = ["AAPL"]  # Placeholder
            
            context.raw_data["symbols"] = symbols
            context.metadata["data_fetched"] = True
            
            return StageResult(
                status=PipelineStageStatus.SUCCESS,
                context=context,
                message="Data fetched successfully"
            )
        except Exception as e:
            return StageResult(
                status=PipelineStageStatus.FAILURE,
                context=context,
                error=e,
                message="Data fetch failed"
            )


class AnalysisStage(PipelineStage):
    """Performs analysis on the fetched data."""
    
    def __init__(self):
        super().__init__("analysis")
    
    async def execute(self, context: PipelineContext) -> StageResult:
        """Perform analysis on the data."""
        try:
            # In a real implementation, call analysis services
            # For now, just simulate analysis
            
            symbols = context.raw_data.get("symbols", [])
            if symbols:
                # Simulate some analysis
                analysis_results = {
                    "sentiment_score": 0.7,
                    "technical_indicators": {
                        "rsi": 65,
                        "macd": 1.23
                    },
                    "summary": f"Analysis completed for {len(symbols)} symbols"
                }
                context.processed_data["analysis"] = analysis_results
            
            context.metadata["analysis_completed"] = True
            
            return StageResult(
                status=PipelineStageStatus.SUCCESS,
                context=context,
                message="Analysis completed"
            )
        except Exception as e:
            return StageResult(
                status=PipelineStageStatus.FAILURE,
                context=context,
                error=e,
                message="Analysis failed"
            )


class ResponseGenerationStage(PipelineStage):
    """Generates the final response to the user."""
    
    def __init__(self):
        super().__init__("response_generation")
    
    async def execute(self, context: PipelineContext) -> StageResult:
        """Generate the final response."""
        try:
            # Generate response based on analysis and data
            query = context.query
            analysis_data = context.processed_data.get("analysis", {})
            
            response_text = f"I've analyzed your request about '{query}'."
            
            if analysis_data:
                sentiment = analysis_data.get("sentiment_score", 0)
                response_text += f" The sentiment appears to be {'positive' if sentiment > 0.5 else 'negative'}."
            
            context.response = {
                "text": response_text,
                "query": query,
                "analysis": analysis_data
            }
            
            context.metadata["response_generated"] = True
            
            return StageResult(
                status=PipelineStageStatus.SUCCESS,
                context=context,
                message="Response generated successfully"
            )
        except Exception as e:
            return StageResult(
                status=PipelineStageStatus.FAILURE,
                context=context,
                error=e,
                message="Response generation failed"
            )


# Convenience function to create a default pipeline
def create_default_pipeline() -> Pipeline:
    """Create a default pipeline with standard stages."""
    stages = [
        InputValidationStage(),
        DataFetchStage(),
        AnalysisStage(),
        ResponseGenerationStage(),
    ]
    return Pipeline(stages, name="default")