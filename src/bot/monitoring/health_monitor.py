"""
Bot Health Monitor for Trading Bot

Provides health monitoring specifically for the Discord bot with simplified interface.
"""

import asyncio
import time
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum

from src.shared.error_handling.logging import get_logger

logger = get_logger(__name__)

class HealthStatus(Enum):
    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    UNKNOWN = "unknown"

@dataclass
class HealthMetric:
    """Individual health metric"""
    name: str
    value: Any
    status: HealthStatus
    threshold_warning: Optional[float] = None
    threshold_critical: Optional[float] = None
    last_updated: datetime = None
    message: str = ""
    
    def __post_init__(self):
        if self.last_updated is None:
            self.last_updated = datetime.now()

@dataclass
class BotHealthStatus:
    """Bot health status"""
    status: HealthStatus
    uptime_seconds: float
    last_check: datetime
    warnings: List[str]
    critical_issues: List[str]
    bot_connected: bool = False
    services_healthy: int = 0
    total_services: int = 0

class BotHealthMonitor:
    """Health monitor specifically for the Discord bot"""
    
    def __init__(self):
        self.start_time = time.time()
        self.metrics: Dict[str, HealthMetric] = {}
        self.running = False
        self.monitor_task: Optional[asyncio.Task] = None
        self.alert_callbacks: List[Callable] = []
        
        # Thresholds
        self.thresholds = {
            'cpu_usage': {'warning': 70.0, 'critical': 90.0},
            'memory_usage': {'warning': 80.0, 'critical': 95.0},
            'disk_usage': {'warning': 85.0, 'critical': 95.0},
            'response_time': {'warning': 5.0, 'critical': 10.0}
        }
    
    async def check_health(self) -> Dict[str, Any]:
        """Check bot health and return status"""
        try:
            # Check system resources
            system_metrics = await self._check_system_resources()
            
            # Check bot connectivity (simplified)
            bot_connected = await self._check_bot_connectivity()
            
            # Check services
            services_status = await self._check_services()
            
            # Determine overall status
            overall_status = HealthStatus.HEALTHY
            warnings = []
            critical_issues = []
            
            # Check for critical issues
            for metric in system_metrics.values():
                if metric.status == HealthStatus.CRITICAL:
                    overall_status = HealthStatus.CRITICAL
                    critical_issues.append(metric.message)
                elif metric.status == HealthStatus.WARNING and overall_status != HealthStatus.CRITICAL:
                    overall_status = HealthStatus.WARNING
                    warnings.append(metric.message)
            
            # Check bot connectivity
            if not bot_connected:
                overall_status = HealthStatus.CRITICAL
                critical_issues.append("Bot is not connected to Discord")
            
            # Check services
            healthy_services = sum(1 for status in services_status.values() if status)
            total_services = len(services_status)
            
            if healthy_services < total_services:
                if overall_status != HealthStatus.CRITICAL:
                    overall_status = HealthStatus.WARNING
                warnings.append(f"Only {healthy_services}/{total_services} services are healthy")
            
            uptime = time.time() - self.start_time
            
            return {
                'status': overall_status.value,
                'uptime_seconds': uptime,
                'uptime_hours': uptime / 3600,
                'bot_connected': bot_connected,
                'services_healthy': healthy_services,
                'total_services': total_services,
                'system_metrics': {
                    name: {
                        'value': metric.value,
                        'status': metric.status.value,
                        'message': metric.message
                    }
                    for name, metric in system_metrics.items()
                },
                'services': services_status,
                'issues': {
                    'warnings': warnings,
                    'critical': critical_issues
                },
                'last_check': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return {
                'status': 'critical',
                'error': str(e),
                'last_check': datetime.now().isoformat()
            }
    
    async def _check_system_resources(self) -> Dict[str, HealthMetric]:
        """Check system resource usage"""
        metrics = {}
        
        try:
            # Try to import psutil, fallback to mock if not available
            try:
                import psutil
                await self._check_with_psutil(metrics)
            except ImportError:
                await self._check_with_mock(metrics)
            
        except Exception as e:
            logger.error(f"Error checking system resources: {e}")
            metrics['system_error'] = HealthMetric(
                name="System Check Error",
                value=str(e),
                status=HealthStatus.CRITICAL,
                message=f"Failed to check system resources: {e}"
            )
        
        return metrics
    
    async def _check_with_psutil(self, metrics: Dict[str, HealthMetric]):
        """Check system resources using psutil"""
        import psutil
        
        # CPU usage
        cpu_percent = psutil.cpu_percent(interval=0.1)
        cpu_status = self._determine_status('cpu_usage', cpu_percent)
        metrics['cpu_usage'] = HealthMetric(
            name="CPU Usage",
            value=cpu_percent,
            status=cpu_status,
            threshold_warning=self.thresholds['cpu_usage']['warning'],
            threshold_critical=self.thresholds['cpu_usage']['critical'],
            message=f"CPU usage at {cpu_percent:.1f}%"
        )
        
        # Memory usage
        memory = psutil.virtual_memory()
        memory_status = self._determine_status('memory_usage', memory.percent)
        metrics['memory_usage'] = HealthMetric(
            name="Memory Usage",
            value=memory.percent,
            status=memory_status,
            threshold_warning=self.thresholds['memory_usage']['warning'],
            threshold_critical=self.thresholds['memory_usage']['critical'],
            message=f"Memory usage at {memory.percent:.1f}% ({memory.used // (1024**3):.1f}GB used)"
        )
        
        # Disk usage
        disk = psutil.disk_usage('/')
        disk_percent = (disk.used / disk.total) * 100
        disk_status = self._determine_status('disk_usage', disk_percent)
        metrics['disk_usage'] = HealthMetric(
            name="Disk Usage",
            value=disk_percent,
            status=disk_status,
            threshold_warning=self.thresholds['disk_usage']['warning'],
            threshold_critical=self.thresholds['disk_usage']['critical'],
            message=f"Disk usage at {disk_percent:.1f}% ({disk.used // (1024**3):.1f}GB used)"
        )
    
    async def _check_with_mock(self, metrics: Dict[str, HealthMetric]):
        """Check system resources using mock data"""
        # Mock CPU usage
        cpu_percent = 25.0 + (time.time() % 20)  # Vary between 25-45%
        cpu_status = self._determine_status('cpu_usage', cpu_percent)
        metrics['cpu_usage'] = HealthMetric(
            name="CPU Usage (Mock)",
            value=cpu_percent,
            status=cpu_status,
            threshold_warning=self.thresholds['cpu_usage']['warning'],
            threshold_critical=self.thresholds['cpu_usage']['critical'],
            message=f"CPU usage at {cpu_percent:.1f}% (mock data)"
        )
        
        # Mock memory usage
        memory_percent = 45.0 + (time.time() % 15)  # Vary between 45-60%
        memory_status = self._determine_status('memory_usage', memory_percent)
        metrics['memory_usage'] = HealthMetric(
            name="Memory Usage (Mock)",
            value=memory_percent,
            status=memory_status,
            threshold_warning=self.thresholds['memory_usage']['warning'],
            threshold_critical=self.thresholds['memory_usage']['critical'],
            message=f"Memory usage at {memory_percent:.1f}% (mock data)"
        )
        
        # Mock disk usage
        disk_percent = 60.0 + (time.time() % 10)  # Vary between 60-70%
        disk_status = self._determine_status('disk_usage', disk_percent)
        metrics['disk_usage'] = HealthMetric(
            name="Disk Usage (Mock)",
            value=disk_percent,
            status=disk_status,
            threshold_warning=self.thresholds['disk_usage']['warning'],
            threshold_critical=self.thresholds['disk_usage']['critical'],
            message=f"Disk usage at {disk_percent:.1f}% (mock data)"
        )
    
    async def _check_bot_connectivity(self) -> bool:
        """Check if bot is connected to Discord"""
        try:
            # This would check actual Discord connection
            # For now, return True as a placeholder
            return True
        except Exception as e:
            logger.error(f"Bot connectivity check failed: {e}")
            return False
    
    async def _check_services(self) -> Dict[str, bool]:
        """Check health of bot services"""
        services = {}
        
        try:
            # Check if service manager exists
            from src.bot.core.services import get_service_manager
            service_manager = get_service_manager()
            
            # Check key services
            services['database'] = service_manager.get_service('database') is not None
            services['cache'] = service_manager.get_service('cache_service') is not None
            services['ai_service'] = service_manager.get_service('ai_service') is not None
            services['rate_limiter'] = service_manager.get_service('rate_limiter') is not None
            
        except Exception as e:
            logger.error(f"Service check failed: {e}")
            services['error'] = False
        
        return services
    
    def _determine_status(self, metric_name: str, value: float) -> HealthStatus:
        """Determine health status based on thresholds"""
        thresholds = self.thresholds.get(metric_name, {})
        
        critical_threshold = thresholds.get('critical')
        warning_threshold = thresholds.get('warning')
        
        if critical_threshold is not None and value >= critical_threshold:
            return HealthStatus.CRITICAL
        elif warning_threshold is not None and value >= warning_threshold:
            return HealthStatus.WARNING
        else:
            return HealthStatus.HEALTHY
    
    async def start_monitoring(self, check_interval: int = 60):
        """Start continuous health monitoring"""
        if self.running:
            return
        
        self.running = True
        self.monitor_task = asyncio.create_task(self._monitor_loop(check_interval))
        logger.info("Bot health monitoring started")
    
    async def stop_monitoring(self):
        """Stop health monitoring"""
        self.running = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        logger.info("Bot health monitoring stopped")
    
    async def _monitor_loop(self, check_interval: int):
        """Background monitoring loop"""
        while self.running:
            try:
                health = await self.check_health()
                
                # Log health status
                if health['status'] == 'critical':
                    logger.error(f"Bot health critical: {health.get('issues', {}).get('critical', [])}")
                elif health['status'] == 'warning':
                    logger.warning(f"Bot health warning: {health.get('issues', {}).get('warnings', [])}")
                else:
                    logger.debug("Bot health check completed: healthy")
                
                # Send alerts if needed
                if health['status'] in ['warning', 'critical']:
                    await self._send_alerts(health)
                
            except Exception as e:
                logger.error(f"Error in health monitoring loop: {e}")
            
            await asyncio.sleep(check_interval)
    
    async def _send_alerts(self, health: Dict[str, Any]):
        """Send health alerts"""
        for callback in self.alert_callbacks:
            try:
                await callback(health)
            except Exception as e:
                logger.error(f"Error sending health alert: {e}")
    
    def add_alert_callback(self, callback: Callable):
        """Add callback for health alerts"""
        self.alert_callbacks.append(callback)

# Global bot health monitor instance
_bot_health_monitor: Optional[BotHealthMonitor] = None

def get_bot_health_monitor() -> BotHealthMonitor:
    """Get global bot health monitor instance"""
    global _bot_health_monitor
    if _bot_health_monitor is None:
        _bot_health_monitor = BotHealthMonitor()
    return _bot_health_monitor

async def cleanup_bot_health_monitor():
    """Cleanup global bot health monitor"""
    global _bot_health_monitor
    if _bot_health_monitor:
        await _bot_health_monitor.stop_monitoring()
        _bot_health_monitor = None
