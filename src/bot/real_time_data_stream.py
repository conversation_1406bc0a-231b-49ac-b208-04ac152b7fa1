"""
Real-Time Data Stream for Trading Bot

This module provides real-time market data streaming capabilities
including price updates, news feeds, and market events.
"""
import asyncio
import logging
import websockets
import json
from datetime import datetime
from typing import Dict, Any, Optional, Callable, List, AsyncGenerator
from dataclasses import dataclass
from enum import Enum
from src.shared.error_handling.logging import get_logger

logger = get_logger(__name__)


class DataType(Enum):
    """Types of real-time data."""
    PRICE_UPDATE = "price_update"
    ORDER_BOOK = "order_book"
    TRADE_TICK = "trade_tick"
    MARKET_DEPTH = "market_depth"
    NEWS_FEED = "news_feed"
    SOCIAL_SENTIMENT = "social_sentiment"
    ECONOMIC_EVENT = "economic_event"


@dataclass
class StreamData:
    """Data structure for stream data."""
    data_type: DataType
    symbol: str
    timestamp: datetime
    data: Dict[str, Any]
    source: Optional[str] = None
    correlation_id: Optional[str] = None


class DataStreamListener:
    """Base class for data stream listeners."""
    
    def __init__(self, name: str):
        self.name = name
        self.logger = get_logger(f"stream_listener.{name}")
    
    async def on_data(self, stream_data: StreamData):
        """Process incoming stream data."""
        pass


class RealTimeDataStream:
    """Manages real-time data streams from various sources."""
    
    def __init__(self):
        self._logger = get_logger("real_time_data_stream")
        self._listeners: Dict[str, DataStreamListener] = {}
        self._active_streams: Dict[str, asyncio.Task] = {}
        self._is_running = False
        self._websocket_connections = {}
        self._symbols = set()
    
    def add_listener(self, listener: DataStreamListener):
        """Add a listener for stream data."""
        self._listeners[listener.name] = listener
        self._logger.info(f"Added stream listener: {listener.name}")
    
    def remove_listener(self, name: str):
        """Remove a listener for stream data."""
        if name in self._listeners:
            del self._listeners[name]
            self._logger.info(f"Removed stream listener: {name}")
    
    def add_symbol(self, symbol: str):
        """Add a symbol to track."""
        self._symbols.add(symbol)
        self._logger.info(f"Added symbol to track: {symbol}")
    
    def remove_symbol(self, symbol: str):
        """Remove a symbol from tracking."""
        if symbol in self._symbols:
            self._symbols.remove(symbol)
            self._logger.info(f"Removed symbol from tracking: {symbol}")
    
    async def start_streaming(self):
        """Start all configured data streams."""
        if self._is_running:
            self._logger.warning("Data streaming is already running")
            return
        
        self._is_running = True
        self._logger.info("Starting real-time data streams")
        
        # Start different types of streams
        self._active_streams['price_stream'] = asyncio.create_task(self._price_stream_worker())
        self._active_streams['news_stream'] = asyncio.create_task(self._news_stream_worker())
        self._active_streams['event_stream'] = asyncio.create_task(self._event_stream_worker())
        
        self._logger.info("All data streams started")
    
    async def stop_streaming(self):
        """Stop all data streams."""
        self._is_running = False
        self._logger.info("Stopping real-time data streams")
        
        # Cancel all active stream tasks
        for name, task in self._active_streams.items():
            if not task.done():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
        
        self._active_streams.clear()
        self._logger.info("All data streams stopped")
    
    async def _price_stream_worker(self):
        """Worker function for price updates."""
        while self._is_running:
            try:
                # Simulate receiving price updates
                for symbol in self._symbols:
                    if not self._is_running:
                        break
                    
                    # Generate mock price data
                    import random
                    price_data = {
                        'symbol': symbol,
                        'price': random.uniform(100, 500),
                        'change': random.uniform(-5, 5),
                        'change_percent': random.uniform(-2, 2),
                        'volume': random.randint(1000, 10000),
                        'timestamp': datetime.now().isoformat()
                    }
                    
                    stream_data = StreamData(
                        data_type=DataType.PRICE_UPDATE,
                        symbol=symbol,
                        timestamp=datetime.now(),
                        data=price_data,
                        source='mock_price_feed'
                    )
                    
                    await self._notify_listeners(stream_data)
                
                # Wait before next price update
                await asyncio.sleep(1)  # Update every second
                
            except Exception as e:
                self._logger.error(f"Error in price stream worker: {e}", exc_info=True)
                await asyncio.sleep(1)
    
    async def _news_stream_worker(self):
        """Worker function for news feeds."""
        while self._is_running:
            try:
                # Simulate receiving news updates
                if self._symbols:
                    symbol = list(self._symbols)[0]  # Use first symbol
                    news_data = {
                        'symbol': symbol,
                        'headline': 'Market News Update',
                        'summary': 'Significant market movement detected',
                        'importance': 'medium',
                        'timestamp': datetime.now().isoformat()
                    }
                    
                    stream_data = StreamData(
                        data_type=DataType.NEWS_FEED,
                        symbol=symbol,
                        timestamp=datetime.now(),
                        data=news_data,
                        source='mock_news_feed'
                    )
                    
                    await self._notify_listeners(stream_data)
                
                # Wait before next news update
                await asyncio.sleep(30)  # Update every 30 seconds
                
            except Exception as e:
                self._logger.error(f"Error in news stream worker: {e}", exc_info=True)
                await asyncio.sleep(5)
    
    async def _event_stream_worker(self):
        """Worker function for market events."""
        while self._is_running:
            try:
                # Simulate receiving economic events
                event_data = {
                    'event_type': 'earnings',
                    'symbol': 'AAPL',
                    'description': 'Company earnings report',
                    'expected_impact': 'high',
                    'timestamp': datetime.now().isoformat()
                }
                
                stream_data = StreamData(
                    data_type=DataType.ECONOMIC_EVENT,
                    symbol='AAPL',
                    timestamp=datetime.now(),
                    data=event_data,
                    source='mock_event_feed'
                )
                
                await self._notify_listeners(stream_data)
                
                # Wait before next event update
                await asyncio.sleep(60)  # Update every minute
                
            except Exception as e:
                self._logger.error(f"Error in event stream worker: {e}", exc_info=True)
                await asyncio.sleep(5)
    
    async def _notify_listeners(self, stream_data: StreamData):
        """Notify all registered listeners of new stream data."""
        for name, listener in self._listeners.items():
            try:
                await listener.on_data(stream_data)
            except Exception as e:
                self._logger.error(f"Error notifying listener {name}: {e}", exc_info=True)
    
    async def subscribe_to_websocket(self, url: str, symbols: List[str], handler: Callable):
        """Subscribe to a WebSocket feed."""
        if url in self._websocket_connections:
            self._logger.warning(f"WebSocket connection already exists for {url}")
            return
        
        async def websocket_worker():
            while self._is_running:
                try:
                    async with websockets.connect(url) as websocket:
                        # Send subscription message
                        subscribe_msg = {
                            'action': 'subscribe',
                            'symbols': symbols
                        }
                        await websocket.send(json.dumps(subscribe_msg))
                        
                        self._logger.info(f"Connected to WebSocket: {url}")
                        
                        async for message in websocket:
                            try:
                                data = json.loads(message)
                                # Process the message with the handler
                                await handler(data)
                            except json.JSONDecodeError:
                                self._logger.error(f"Invalid JSON received: {message}")
                            except Exception as e:
                                self._logger.error(f"Error processing WebSocket message: {e}", exc_info=True)
                
                except websockets.exceptions.ConnectionClosed:
                    self._logger.warning(f"WebSocket connection closed for {url}")
                except Exception as e:
                    self._logger.error(f"WebSocket connection error for {url}: {e}", exc_info=True)
                
                if self._is_running:
                    # Wait before reconnecting
                    await asyncio.sleep(5)
        
        task = asyncio.create_task(websocket_worker())
        self._websocket_connections[url] = task
        self._logger.info(f"Started WebSocket subscription to: {url}")
    
    def get_active_streams(self) -> List[str]:
        """Get list of active stream names."""
        return list(self._active_streams.keys())
    
    def get_subscribed_symbols(self) -> List[str]:
        """Get list of subscribed symbols."""
        return list(self._symbols)
    
    async def send_stream_data(self, stream_data: StreamData):
        """Manually send stream data (for testing or external input)."""
        await self._notify_listeners(stream_data)


class PriceUpdateListener(DataStreamListener):
    """Listener for price updates that performs basic analysis."""
    
    def __init__(self):
        super().__init__("price_updater")
        self._latest_prices: Dict[str, float] = {}
    
    async def on_data(self, stream_data: StreamData):
        """Process price update data."""
        if stream_data.data_type == DataType.PRICE_UPDATE:
            symbol = stream_data.data['symbol']
            price = stream_data.data['price']
            self._latest_prices[symbol] = price
            
            self.logger.info(f"Price update: {symbol} = {price}")
            
            # Perform any needed analysis here
            await self._analyze_price_movement(symbol, price, stream_data)
    
    async def _analyze_price_movement(self, symbol: str, price: float, stream_data: StreamData):
        """Perform basic analysis on price movement."""
        # This could trigger alerts, technical analysis, etc.
        pass


class NewsAlertListener(DataStreamListener):
    """Listener for news alerts that categorizes importance."""
    
    def __init__(self):
        super().__init__("news_alert")
        self._important_news: List[Dict[str, Any]] = []
    
    async def on_data(self, stream_data: StreamData):
        """Process news data."""
        if stream_data.data_type == DataType.NEWS_FEED:
            importance = stream_data.data.get('importance', 'low')
            symbol = stream_data.data['symbol']
            
            self.logger.info(f"News for {symbol}: {stream_data.data['headline']} [{importance}]")
            
            # Store important news
            if importance in ['high', 'medium']:
                self._important_news.append(stream_data.data)
                
                # Limit stored news to last 20 items
                if len(self._important_news) > 20:
                    self._important_news = self._important_news[-20:]


# Global real-time data stream instance
real_time_data_stream = RealTimeDataStream()


async def start_streaming():
    """Convenience function to start streaming."""
    await real_time_data_stream.start_streaming()


async def stop_streaming():
    """Convenience function to stop streaming."""
    await real_time_data_stream.stop_streaming()


def add_symbol_to_watch(symbol: str):
    """Convenience function to add a symbol to watch."""
    real_time_data_stream.add_symbol(symbol)


def add_stream_listener(listener: DataStreamListener):
    """Convenience function to add a stream listener."""
    real_time_data_stream.add_listener(listener)