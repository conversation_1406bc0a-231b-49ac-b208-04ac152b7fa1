"""
Watchlist Alerts Definitions

Contains shared data structures for managing user alerts, including the AlertType Enum.
"""
from enum import Enum

class AlertType(Enum):
    PRICE_CHANGE = "price_change"
    VOLUME_SPIKE = "volume_spike"
    TECHNICAL_SIGNAL = "technical_signal"
    SUPPORT_RESISTANCE = "support_resistance"
    VOLATILITY = "volatility"
    EARNINGS = "earnings"
    NEWS = "news"
    
    def __str__(self):
        return self.value