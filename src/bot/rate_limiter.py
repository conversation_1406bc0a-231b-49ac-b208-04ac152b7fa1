"""
Rate Limiter Service

Provides centralized rate limiting for API calls, Discord interactions, and data provider requests.
Implements token bucket algorithm with Redis backend for distributed rate limiting.
"""

import asyncio
import time
from typing import Optional, Dict, Any
from dataclasses import dataclass
import redis.asyncio as redis
from src.shared.error_handling.logging import get_logger

logger = get_logger(__name__)

@dataclass
class RateLimitConfig:
    """Configuration for rate limiting"""
    max_tokens: int = 10
    refill_rate: float = 1.0  # tokens per second
    burst_size: int = 5

class RateLimiter:
    """Distributed rate limiter using token bucket algorithm"""
    
    def __init__(self, redis_url: Optional[str] = None):
        self.redis_client = None
        self.redis_url = redis_url
        self.local_buckets: Dict[str, Dict[str, Any]] = {}
        
    async def initialize(self):
        """Initialize Redis connection if configured"""
        if self.redis_url:
            try:
                self.redis_client = redis.from_url(self.redis_url)
                await self.redis_client.ping()
                logger.info("✅ Redis rate limiter initialized")
            except Exception as e:
                logger.warning(f"Redis rate limiter failed: {e}, using local fallback")
                self.redis_client = None
    
    async def acquire(
        self, 
        key: str, 
        config: Optional[RateLimitConfig] = None,
        tokens: int = 1
    ) -> bool:
        """Acquire tokens from rate limit bucket"""
        config = config or RateLimitConfig()
        
        if self.redis_client:
            return await self._acquire_redis(key, config, tokens)
        else:
            return self._acquire_local(key, config, tokens)
    
    async def _acquire_redis(self, key: str, config: RateLimitConfig, tokens: int) -> bool:
        """Redis-based rate limiting"""
        try:
            current_time = time.time()
            bucket_key = f"rate_limit:{key}"
            
            # Get current bucket state
            bucket_data = await self.redis_client.hgetall(bucket_key)
            if not bucket_data:
                # Initialize new bucket
                bucket_data = {
                    'tokens': str(config.max_tokens),
                    'last_refill': str(current_time)
                }
            else:
                # Refill tokens based on elapsed time
                last_refill = float(bucket_data.get('last_refill', current_time))
                elapsed = current_time - last_refill
                new_tokens = min(config.max_tokens, float(bucket_data.get('tokens', 0)) + elapsed * config.refill_rate)
                
                bucket_data['tokens'] = str(new_tokens)
                bucket_data['last_refill'] = str(current_time)
            
            current_tokens = float(bucket_data['tokens'])
            
            if current_tokens >= tokens:
                # Acquire tokens
                bucket_data['tokens'] = str(current_tokens - tokens)
                await self.redis_client.hset(bucket_key, mapping=bucket_data)
                await self.redis_client.expire(bucket_key, 3600)  # Expire after 1 hour
                return True
            
            return False
            
        except Exception as e:
            logger.warning(f"Redis rate limit failed: {e}, falling back to local")
            return self._acquire_local(key, config, tokens)
    
    def _acquire_local(self, key: str, config: RateLimitConfig, tokens: int) -> bool:
        """Local in-memory rate limiting"""
        current_time = time.time()
        
        if key not in self.local_buckets:
            self.local_buckets[key] = {
                'tokens': config.max_tokens,
                'last_refill': current_time
            }
        
        bucket = self.local_buckets[key]
        
        # Refill tokens
        elapsed = current_time - bucket['last_refill']
        bucket['tokens'] = min(config.max_tokens, bucket['tokens'] + elapsed * config.refill_rate)
        bucket['last_refill'] = current_time
        
        if bucket['tokens'] >= tokens:
            bucket['tokens'] -= tokens
            return True
        
        return False
    
    async def get_remaining_tokens(self, key: str) -> int:
        """Get remaining tokens in bucket"""
        if self.redis_client:
            try:
                bucket_data = await self.redis_client.hgetall(f"rate_limit:{key}")
                if bucket_data:
                    return int(float(bucket_data.get('tokens', 0)))
            except:
                pass
        
        if key in self.local_buckets:
            return int(self.local_buckets[key]['tokens'])
        
        return 0

# Global rate limiter instance
rate_limiter = RateLimiter()

async def initialize_rate_limiter(redis_url: Optional[str] = None):
    """Initialize the global rate limiter"""
    rate_limiter.redis_url = redis_url
    await rate_limiter.initialize()
    return rate_limiter