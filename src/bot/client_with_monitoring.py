"""
Trading Bot Client with Monitoring

This module provides a client interface with built-in monitoring,
logging, and performance tracking capabilities.
"""
import asyncio
import logging
from datetime import datetime
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
import time
from src.shared.error_handling.logging import get_logger
from src.bot.metrics_collector import record_histogram, increment_counter, get_metrics_summary
from src.bot.performance_analytics import record_request
from src.bot.token_validator import validate_token

logger = get_logger(__name__)


@dataclass
class RequestMetrics:
    """Metrics collected during a request."""
    request_id: str
    start_time: float
    end_time: float
    duration: float
    user_id: str
    query: str
    success: bool
    error_message: Optional[str] = None


class BotClientWithMonitoring:
    """Trading bot client with comprehensive monitoring capabilities."""
    
    def __init__(self, api_key: Optional[str] = None):
        self._logger = get_logger("bot_client")
        self._api_key = api_key
        self._request_history: List[RequestMetrics] = []
        self._is_initialized = False
        self._session_id = None
    
    async def initialize(self):
        """Initialize the client with validation."""
        if self._is_initialized:
            return True
        
        # Validate API key if provided
        if self._api_key:
            try:
                await validate_token(self._api_key)
                self._logger.info("Client initialized with valid API key")
            except Exception as e:
                self._logger.error(f"Failed to validate API key: {e}")
                return False
        
        # Create session
        self._session_id = f"session_{int(time.time())}_{id(self)}"
        self._logger.info(f"Client initialized with session: {self._session_id}")
        self._is_initialized = True
        return True
    
    async def execute_query(self, query: str, user_id: str = "anonymous") -> Dict[str, Any]:
        """Execute a query with monitoring."""
        start_time = time.time()
        request_id = f"req_{int(start_time * 1000000)}"
        
        if not self._is_initialized:
            if not await self.initialize():
                return {"error": "Client initialization failed"}
        
        try:
            # Log the request
            self._logger.info(f"Processing request {request_id} from user {user_id}: {query[:100]}...")
            
            # Validate token if API key is set
            if self._api_key:
                token_info = await validate_token(self._api_key, requires_rate_limit=True)
                if not token_info:
                    return {"error": "Invalid or expired API key"}
            
            # Simulate query processing
            response = await self._process_query(query, user_id, request_id)
            
            # Calculate duration
            end_time = time.time()
            duration = end_time - start_time
            
            # Record metrics
            metrics = RequestMetrics(
                request_id=request_id,
                start_time=start_time,
                end_time=end_time,
                duration=duration,
                user_id=user_id,
                query=query,
                success=True
            )
            
            self._request_history.append(metrics)
            
            # Keep only recent history (last 1000 requests)
            if len(self._request_history) > 1000:
                self._request_history = self._request_history[-1000:]
            
            # Record metrics for analytics
            await record_histogram("request_duration_seconds", duration, {'user_id': user_id})
            await increment_counter("requests_total", {'user_id': user_id, 'status': 'success'})
            await record_request(user_id, self._get_query_type(query), duration, True)
            
            self._logger.info(f"Request {request_id} completed successfully in {duration:.3f}s")
            
            return response
            
        except Exception as e:
            # Calculate duration for failed request
            end_time = time.time()
            duration = end_time - start_time
            
            # Record failed metrics
            metrics = RequestMetrics(
                request_id=request_id,
                start_time=start_time,
                end_time=end_time,
                duration=duration,
                user_id=user_id,
                query=query,
                success=False,
                error_message=str(e)
            )
            
            self._request_history.append(metrics)
            
            # Record failure metrics
            await record_histogram("request_duration_seconds", duration, {'user_id': user_id})
            await increment_counter("requests_total", {'user_id': user_id, 'status': 'failure'})
            await record_request(user_id, self._get_query_type(query), duration, False)
            
            self._logger.error(f"Request {request_id} failed after {duration:.3f}s: {e}", exc_info=True)
            
            return {"error": f"Request failed: {str(e)}"}
    
    async def _process_query(self, query: str, user_id: str, request_id: str) -> Dict[str, Any]:
        """Process the query and return results."""
        # This would call the actual bot logic in a real implementation
        # For now, we'll simulate response based on query content
        
        # Simulate some processing delay
        await asyncio.sleep(0.1)
        
        # Simple query classification and response generation
        query_lower = query.lower()
        
        if any(word in query_lower for word in ['price', 'cost', 'value']):
            return {
                "type": "price_inquiry",
                "response": f"Price information for symbols in '{query}'",
                "query_analyzed": True
            }
        elif any(word in query_lower for word in ['analyze', 'analysis', 'technical']):
            return {
                "type": "technical_analysis",
                "response": f"Technical analysis for '{query}'",
                "query_analyzed": True
            }
        elif any(word in query_lower for word in ['buy', 'sell', 'trade']):
            return {
                "type": "trade_recommendation",
                "response": f"Trade recommendations for '{query}'",
                "query_analyzed": True
                }
        else:
            return {
                "type": "general",
                "response": f"Processed general query: '{query}'",
                "query_analyzed": True
            }
    
    def _get_query_type(self, query: str) -> str:
        """Classify the query type."""
        query_lower = query.lower()
        
        if any(word in query_lower for word in ['price', 'cost', 'quote']):
            return 'price_inquiry'
        elif any(word in query_lower for word in ['analyze', 'analysis', 'technical']):
            return 'technical_analysis'
        elif any(word in query_lower for word in ['buy', 'sell', 'trade']):
            return 'trade_recommendation'
        else:
            return 'general'
    
    def get_request_history(self, limit: int = 10) -> List[RequestMetrics]:
        """Get recent request history."""
        return self._request_history[-limit:]
    
    def get_session_stats(self) -> Dict[str, Any]:
        """Get current session statistics."""
        if not self._request_history:
            return {
                "session_id": self._session_id,
                "total_requests": 0,
                "average_duration": 0.0,
                "success_rate": 0.0
            }
        
        recent_requests = self._request_history[-100:]  # Last 100 requests
        total_requests = len(recent_requests)
        successful_requests = sum(1 for req in recent_requests if req.success)
        avg_duration = sum(req.duration for req in recent_requests) / len(recent_requests)
        success_rate = successful_requests / total_requests * 100 if total_requests > 0 else 0.0
        
        return {
            "session_id": self._session_id,
            "total_requests": total_requests,
            "average_duration": avg_duration,
            "success_rate": success_rate
        }
    
    async def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics."""
        metrics_summary = await get_metrics_summary()
        
        return {
            "timestamp": datetime.now().isoformat(),
            "session_stats": self.get_session_stats(),
            "system_metrics": metrics_summary,
            "active_session": self._session_id is not None
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform a health check of the client."""
        return {
            "status": "healthy" if self._is_initialized else "not_initialized",
            "session_id": self._session_id,
            "request_count": len(self._request_history),
            "last_request_time": (
                self._request_history[-1].end_time if self._request_history 
                else None
            ),
            "timestamp": datetime.now().isoformat()
        }
    
    async def set_api_key(self, api_key: str):
        """Set or update the API key."""
        old_key = self._api_key
        self._api_key = api_key
        
        # Validate the new key
        if api_key:
            try:
                token_info = await validate_token(api_key)
                if token_info:
                    self._logger.info("API key updated and validated successfully")
                else:
                    self._logger.error("New API key validation failed, reverting")
                    self._api_key = old_key
            except Exception as e:
                self._logger.error(f"Error validating new API key: {e}")
                self._api_key = old_key
                raise


# Global bot client with monitoring instance
bot_client_with_monitoring = BotClientWithMonitoring()


async def execute_query(query: str, user_id: str = "anonymous") -> Dict[str, Any]:
    """Convenience function to execute a query."""
    return await bot_client_with_monitoring.execute_query(query, user_id)


async def get_performance_metrics() -> Dict[str, Any]:
    """Convenience function to get performance metrics."""
    return await bot_client_with_monitoring.get_performance_metrics()


async def health_check() -> Dict[str, Any]:
    """Convenience function to perform health check."""
    return await bot_client_with_monitoring.health_check()