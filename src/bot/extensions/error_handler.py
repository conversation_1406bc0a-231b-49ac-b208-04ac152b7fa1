"""
Global Error Handler Extension for Discord Bot

This cog implements a global error handler for all application commands (slash commands).
It catches errors, logs them with context, and provides user-friendly feedback.
"""

import discord
from discord.ext import commands
from discord import app_commands
import traceback
import sys

from src.shared.error_handling.logging import get_logger

logger = get_logger(__name__)

class ErrorHandler(commands.Cog):
    """A cog for global error handling."""

    def __init__(self, bot: commands.Bot):
        self.bot = bot
        # Assign the error handler to the bot's command tree
        bot.tree.on_error = self.on_app_command_error

    async def on_app_command_error(self, interaction: discord.Interaction, error: app_commands.AppCommandError):
        """
        The global error handler for all slash commands.
        """
        # Extract original error if it's wrapped
        original_error = getattr(error, 'original', error)

        # Prepare logging context
        log_context = {
            "command_name": interaction.command.name if interaction.command else "unknown",
            "user_id": interaction.user.id,
            "user_name": str(interaction.user),
            "guild_id": interaction.guild.id if interaction.guild else "DM",
            "guild_name": interaction.guild.name if interaction.guild else "DM",
            "error_type": type(original_error).__name__,
            "error_message": str(original_error)
        }

        # Default user-facing error message
        user_message = "❌ An unexpected error occurred. The development team has been notified."

        # Handle specific, common discord.py errors
        if isinstance(original_error, app_commands.CommandNotFound):
            # This should ideally not happen with slash commands, but good to have
            logger.warning(f"Command not found: {log_context['command_name']}", extra=log_context)
            user_message = f"🤔 The command `/{log_context['command_name']}` could not be found."

        elif isinstance(original_error, app_commands.MissingPermissions):
            logger.warning("User missing permissions.", extra=log_context)
            permissions = ', '.join(original_error.missing_permissions)
            user_message = f"🚫 You don't have the required permissions to use this command. You need: `{permissions}`."

        elif isinstance(original_error, app_commands.BotMissingPermissions):
            logger.error("Bot missing permissions.", extra=log_context)
            permissions = ', '.join(original_error.missing_permissions)
            user_message = f"🤖 I'm missing the permissions I need to do that. Please grant me: `{permissions}`."

        elif isinstance(original_error, app_commands.CommandOnCooldown):
            logger.info("Command on cooldown for user.", extra=log_context)
            retry_after = round(original_error.retry_after, 1)
            user_message = f"⏳ This command is on cooldown. Please try again in `{retry_after}` seconds."

        elif isinstance(original_error, app_commands.CheckFailure):
            # This is a generic check failure, often from custom decorators like our permission system
            logger.warning("Command check failure.", extra=log_context)
            # The decorator should have already sent a message, but we provide a fallback.
            user_message = "🚫 You do not have permission to use this command."

        else:
            # For all other errors, log the full traceback
            logger.error(
                f"Unhandled error in slash command '{log_context['command_name']}'",
                extra=log_context,
                exc_info=original_error
            )

        # Try to send an ephemeral message to the user
        try:
            if interaction.response.is_done():
                await interaction.followup.send(user_message, ephemeral=True)
            else:
                await interaction.response.send_message(user_message, ephemeral=True)
        except discord.errors.InteractionResponded:
            # If the interaction was already responded to (e.g., by a check failure), try a followup
            try:
                await interaction.followup.send(user_message, ephemeral=True)
            except Exception as followup_error:
                logger.error(f"Failed to send error followup message: {followup_error}", extra=log_context)
        except Exception as send_error:
            logger.error(f"Failed to send error message to user: {send_error}", extra=log_context)

async def setup(bot: commands.Bot):
    """Adds the error handler cog to the bot."""
    await bot.add_cog(ErrorHandler(bot))
    logger.info("✅ Global error handler extension loaded.")