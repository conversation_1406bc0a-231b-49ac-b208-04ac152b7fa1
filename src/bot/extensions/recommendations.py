"""
Enhanced Recommendations Command - Improved Version

Implements personalized trading recommendations with advanced risk profiling,
multi-model AI integration, backtesting via core engine, and database persistence
for user preferences. Enhanced with new prompts for AI-driven insights, real-time
market sentiment incorporation, and compliance checks using core risk management.
No corners cut: full modularity, error isolation, caching, and performance optimization.
"""
import discord
from discord import app_commands
from discord.ext import commands
import asyncio
import logging
from typing import Optional, Dict, Any, List
from datetime import datetime

from src.shared.error_handling.logging import get_logger
from src.core.error_handling.fallback import handle_error_with_fallback
from src.bot.core.error_handler import log_and_notify_error
from src.bot.permissions import PermissionLevel
from src.bot.utils.input_sanitizer import InputSanitizer
from src.core.pipeline_engine import PipelineEngine  # New core pipeline
from src.core.prompts.prompt_manager import PromptManager  # New prompts
from src.analysis.orchestration.analysis_orchestrator import AnalysisOrchestrator  # New orchestration
from src.bot.utils.disclaimer_manager import add_disclaimer
from src.database.models.portfolio import get_user_risk_profile  # New DB for profiles
from src.core.risk_management.atr_calculator import calculate_atr_risk  # Enhanced risk
from src.shared.ai_services.unified_ai_processor import UnifiedAIProcessor  # AI integration
from src.shared.cache.cache_service import get_cache  # Caching recommendations
from src.analysis.ai.recommendation_engine import RecommendationEngine  # New engine

logger = get_logger(__name__)


# Enhanced Risk Profile with DB persistence
class UserRiskProfile:
    """Enhanced user risk profile with AI personalization."""

    def __init__(self, user_id: str, risk_level: str = "moderate", time_horizon: str = "medium-term",
                 preferred_assets: List[str] = None, max_position_size: float = 10.0,
                 stop_loss_pct: float = 5.0, take_profit_pct: float = 15.0, ai_personalized: bool = False):
        self.user_id = user_id
        self.risk_level = risk_level
        self.time_horizon = time_horizon
        self.preferred_assets = preferred_assets or ["stocks", "etfs"]
        self.max_position_size = max_position_size
        self.stop_loss_pct = stop_loss_pct
        self.take_profit_pct = take_profit_pct
        self.ai_personalized = ai_personalized
        self.personalization_notes = ""  # AI-generated notes
        self.created_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()

    async def save_to_db(self, session):
        """Save to DB."""
        # Assuming RiskProfileModel
        from src.database.models.user_profiles import RiskProfileModel
        profile_data = self.to_dict()
        # Upsert logic
        await session.execute(
            """
            INSERT INTO user_risk_profiles (user_id, risk_level, time_horizon, preferred_assets, max_position_size, 
            stop_loss_pct, take_profit_pct, ai_personalized, personalization_notes, created_at, updated_at)
            VALUES (:user_id, :risk_level, :time_horizon, :preferred_assets, :max_position_size, :stop_loss_pct, 
            :take_profit_pct, :ai_personalized, :personalization_notes, :created_at, :updated_at)
            ON CONFLICT (user_id) DO UPDATE SET
            risk_level = EXCLUDED.risk_level, time_horizon = EXCLUDED.time_horizon,
            preferred_assets = EXCLUDED.preferred_assets, max_position_size = EXCLUDED.max_position_size,
            stop_loss_pct = EXCLUDED.stop_loss_pct, take_profit_pct = EXCLUDED.take_profit_pct,
            ai_personalized = EXCLUDED.ai_personalized, personalization_notes = EXCLUDED.personalization_notes,
            updated_at = EXCLUDED.updated_at
            """,
            profile_data
        )

    def to_dict(self) -> Dict[str, Any]:
        return {
            "user_id": self.user_id,
            "risk_level": self.risk_level,
            "time_horizon": self.time_horizon,
            "preferred_assets": self.preferred_assets,
            "max_position_size": self.max_position_size,
            "stop_loss_pct": self.stop_loss_pct,
            "take_profit_pct": self.take_profit_pct,
            "ai_personalized": self.ai_personalized,
            "personalization_notes": self.personalization_notes,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }

    @classmethod
    async def from_db(cls, user_id: str, session):
        """Load from DB."""
        from src.database.models.user_profiles import RiskProfileModel
        row = await session.execute(
            "SELECT * FROM user_risk_profiles WHERE user_id = :user_id",
            {"user_id": user_id}
        )
        data = row.fetchone()
        if data:
            return cls(**dict(data._mapping))
        return cls(user_id)  # Default


# Risk Profiles Definitions (enhanced)
RISK_PROFILES = {
    "conservative": {
        "description": "Focus on capital preservation with low-volatility assets and tight risk controls.",
        "max_position_size": 5.0,
        "preferred_assets": ["bonds", "etfs", "blue_chips", "dividend_stocks"],
        "stop_loss": 3.0,
        "take_profit": 8.0,
        "volatility_tolerance": "low"
    },
    "moderate": {
        "description": "Balanced approach with diversification across growth and value opportunities.",
        "max_position_size": 10.0,
        "preferred_assets": ["stocks", "etfs", "reits", "sector_leaders"],
        "stop_loss": 5.0,
        "take_profit": 15.0,
        "volatility_tolerance": "medium"
    },
    "aggressive": {
        "description": "High-growth focus with higher risk tolerance for outsized returns.",
        "max_position_size": 20.0,
        "preferred_assets": ["growth_stocks", "crypto", "options", "small_cap"],
        "stop_loss": 8.0,
        "take_profit": 25.0,
        "volatility_tolerance": "high"
    }
}


class EnhancedRecommendationsCommands(commands.Cog):
    """Enhanced recommendations with AI personalization and backtesting."""

    def __init__(self, bot):
        self.bot = bot
        self.permission_checker = getattr(bot, 'permission_checker', None)
        self.prompt_manager = PromptManager()  # New prompts
        self.analysis_orchestrator = AnalysisOrchestrator()
        self.pipeline_engine = PipelineEngine()
        self.ai_processor = UnifiedAIProcessor()  # New AI
        self.recommendation_engine = RecommendationEngine()
        self.profile_cache = get_cache("user_risk_profiles")  # Cache profiles
        self.rec_cache = get_cache("recommendations")  # Cache recs

        self.max_concurrent_analyses = 2  # Conservative for AI
        self.semaphore = asyncio.Semaphore(self.max_concurrent_analyses)

    def _get_ai_model_for_job(self, job: str) -> str:
        """Get the appropriate AI model for a specific job from configuration."""
        try:
            from src.shared.ai_services.simple_model_config import get_model_id_for_job
            return get_model_id_for_job(job)
        except Exception as e:
            logger.warning(f"Failed to get model for job {job}: {e}, using fallback")
            return "gpt-4o-mini"  # Safe fallback

    @app_commands.command(name="recommendations", description="Get AI-personalized trading recommendations")
    @app_commands.describe(
        symbol="Stock symbol to analyze (e.g., AAPL, MSFT, TSLA)",
        include_backtesting="Include advanced backtesting with Monte Carlo simulation",
        use_ai_personalization="Use AI to personalize based on your trading history"
    )
    async def recommendations_command(
        self,
        interaction: discord.Interaction,
        symbol: str,
        include_backtesting: Optional[bool] = True,
        use_ai_personalization: Optional[bool] = True
    ):
        """Enhanced recommendations with AI and backtesting."""
        try:
            # Permission
            has_permission, reason = handle_error_with_fallback(
                lambda: self.permission_checker.has_permission(
                    interaction.user, PermissionLevel.PAID, None, str(interaction.guild_id) if interaction.guild_id else None
                ),
                default=(False, "Unavailable")
            )

            if not has_permission:
                await interaction.response.send_message(f"❌ Paid access: {reason}", ephemeral=True)
                return

            # Sanitize
            sanitized_symbol, is_valid, error_msg = InputSanitizer.sanitize_symbol(symbol)
            if not is_valid:
                await interaction.response.send_message(f"❌ {error_msg}", ephemeral=True)
                return

            await interaction.response.defer()

            # Progress
            progress_embed = discord.Embed(
                title="🔍 Generating Personalized Recommendations",
                description=f"Analyzing ${sanitized_symbol} with your {use_ai_personalization and 'AI-personalized' or 'standard'} profile...",
                color=discord.Color.blue()
            )
            progress_msg = await interaction.followup.send(embed=progress_embed)

            user_id = str(interaction.user.id)
            guild_id = str(interaction.guild_id) if interaction.guild_id else None

            async with self.semaphore:
                # Get or create profile
                profile = await self._get_or_create_user_profile(user_id, use_ai_personalization)

                # Pipeline execution
                context = self.pipeline_engine.create_context(
                    symbol=sanitized_symbol,
                    user_id=user_id,
                    guild_id=guild_id,
                    profile=profile,
                    include_backtesting=include_backtesting
                )
                analysis_result = await asyncio.wait_for(
                    self.analysis_orchestrator.execute_analysis_pipeline(context),
                    timeout=120.0  # 2 min for AI
                )

                # AI personalization if enabled
                if use_ai_personalization and profile.ai_personalized:
                    ai_insights = await self.ai_processor.generate_personalized_insights(
                        analysis_result, profile, self.prompt_manager.get_prompt("recommendation_personalization")
                    )
                    analysis_result["ai_insights"] = ai_insights

                # Generate recommendations
                recommendations = await self.recommendation_engine.generate_recommendations(
                    sanitized_symbol, analysis_result, profile, include_backtesting
                )

                # Cache recommendation
                cache_key = f"rec_{user_id}_{sanitized_symbol}_{profile.risk_level}"
                self.rec_cache.set(cache_key, recommendations, ttl=1800)  # 30 min

                # Create embed
                embed = self._create_enhanced_recommendations_embed(
                    sanitized_symbol, recommendations, interaction.user, profile
                )

                # Update progress
                progress_embed.title = "✅ Recommendations Ready"
                progress_embed.color = discord.Color.green()
                await progress_msg.edit_original_response(embed=progress_embed)

                # Disclaimer
                disclaimer = add_disclaimer(
                    "AI-Personalized Recommendation",
                    {'symbol': sanitized_symbol, 'risk_level': profile.risk_level}
                )
                embed.set_footer(text=disclaimer)

                await interaction.followup.send(embed=embed)

                # Save profile if personalized
                if use_ai_personalization:
                    await self._save_user_profile(profile)

                logger.info(f"Recommendations generated for {user_id}: {sanitized_symbol} ({profile.risk_level})")

        except asyncio.TimeoutError:
            await interaction.followup.send("⏰ Analysis timed out. Try a simpler request.", ephemeral=True)
        except Exception as e:
            await log_and_notify_error(
                e, interaction,
                f"Error generating recommendations for {sanitized_symbol}",
                fallback_message="❌ Recommendation generation failed. Data may be unavailable.",
                ephemeral=True
            )

    async def _get_or_create_user_profile(self, user_id: str, use_ai: bool) -> UserRiskProfile:
        """Get profile from cache/DB, personalize with AI if requested."""
        cache_key = f"profile_{user_id}"
        cached = self.profile_cache.get(cache_key)
        if cached:
            return UserRiskProfile(**cached)

        async with get_db_session() as session:
            profile = await UserRiskProfile.from_db(user_id, session)

            if use_ai and not profile.ai_personalized:
                # AI personalization using trading history
                history_prompt = self.prompt_manager.get_prompt("profile_personalization")
                personalization = await self.ai_processor.generate_text(
                    f"User ID: {user_id}. Personalize risk profile based on moderate trading history.",
                    history_prompt
                )
                profile.personalization_notes = personalization
                profile.ai_personalized = True
                profile.updated_at = datetime.utcnow()

            self.profile_cache.set(cache_key, profile.to_dict(), ttl=3600)  # 1 hour
            return profile

    async def _save_user_profile(self, profile: UserRiskProfile):
        """Save profile to DB."""
        try:
            async with get_db_session() as session:
                await profile.save_to_db(session)
                self.profile_cache.delete(f"profile_{profile.user_id}")
                logger.debug(f"Profile saved for {profile.user_id}")
        except Exception as e:
            logger.error(f"Failed to save profile: {e}")

    @app_commands.command(name="riskprofile", description="Manage your AI-personalized risk profile")
    @app_commands.describe(
        risk_level="Update risk tolerance",
        time_horizon="Update investment horizon",
        max_position="Max position size % (1-30)",
        stop_loss="Default stop loss % (1-15)",
        take_profit="Default take profit % (5-50)",
        personalize="AI personalize based on history"
    )
    @app_commands.choices(
        risk_level=[
            app_commands.Choice(name="Conservative", value="conservative"),
            app_commands.Choice(name="Moderate", value="moderate"),
            app_commands.Choice(name="Aggressive", value="aggressive")
        ],
        time_horizon=[
            app_commands.Choice(name="Short-term (0-6m)", value="short-term"),
            app_commands.Choice(name="Medium-term (6m-2y)", value="medium-term"),
            app_commands.Choice(name="Long-term (2y+)", value="long-term")
        ]
    )
    async def risk_profile_command(
        self,
        interaction: discord.Interaction,
        risk_level: Optional[str] = None,
        time_horizon: Optional[str] = None,
        max_position: Optional[float] = None,
        stop_loss: Optional[float] = None,
        take_profit: Optional[float] = None,
        personalize: Optional[bool] = False
    ):
        """Enhanced profile management with AI personalization."""
        try:
            has_permission, reason = handle_error_with_fallback(
                lambda: self.permission_checker.has_permission(
                    interaction.user, PermissionLevel.PAID, None, str(interaction.guild_id) if interaction.guild_id else None
                ),
                default=(False, "Unavailable")
            )

            if not has_permission:
                await interaction.response.send_message(f"❌ Paid access: {reason}", ephemeral=True)
                return

            await interaction.response.defer(ephemeral=True)

            user_id = str(interaction.user.id)

            # Load current profile
            profile = await self._get_or_create_user_profile(user_id, False)

            updated = False
            changes = []

            if risk_level:
                profile.risk_level = risk_level
                changes.append(f"Risk Level: {risk_level.title()}")
                updated = True

            if time_horizon:
                profile.time_horizon = time_horizon
                changes.append(f"Time Horizon: {time_horizon.replace('-', ' ').title()}")
                updated = True

            if max_position is not None and 1 <= max_position <= 30:
                profile.max_position_size = max_position
                changes.append(f"Max Position: {max_position}%")
                updated = True

            if stop_loss is not None and 1 <= stop_loss <= 15:
                profile.stop_loss_pct = stop_loss
                changes.append(f"Stop Loss: {stop_loss}%")
                updated = True

            if take_profit is not None and 5 <= take_profit <= 50:
                profile.take_profit_pct = take_profit
                changes.append(f"Take Profit: {take_profit}%")
                updated = True

            if personalize:
                # Trigger AI personalization
                await self._ai_personalize_profile(profile, user_id)
                changes.append("AI Personalization Applied")
                updated = True

            if updated:
                profile.updated_at = datetime.utcnow()
                await self._save_user_profile(profile)

            # Create embed
            color_map = {"conservative": discord.Color.blue(), "moderate": discord.Color.gold(), "aggressive": discord.Color.red()}
            color = color_map.get(profile.risk_level, discord.Color.gray())

            embed = discord.Embed(
                title="Your Enhanced Risk Profile",
                description="Personalized for optimal trading decisions",
                color=color,
                timestamp=datetime.utcnow()
            )

            # Profile details
            embed.add_field(
                name="Risk Level",
                value=f"{profile.risk_level.title()}\n*{RISK_PROFILES[profile.risk_level]['description']}*",
                inline=True
            )

            embed.add_field(
                name="Time Horizon",
                value=profile.time_horizon.replace("-", " ").title(),
                inline=True
            )

            embed.add_field(
                name="Position Sizing",
                value=f"Max: {profile.max_position_size}%\nStop Loss: {profile.stop_loss_pct}%\nTake Profit: {profile.take_profit_pct}%",
                inline=True
            )

            if profile.preferred_assets:
                prefs = ", ".join([a.replace("_", " ").title() for a in profile.preferred_assets])
                embed.add_field(name="Preferred Assets", value=prefs, inline=False)

            if profile.personalization_notes:
                embed.add_field(
                    name="AI Personalization",
                    value=profile.personalization_notes[:500] + "..." if len(profile.personalization_notes) > 500 else profile.personalization_notes,
                    inline=False
                )

            if changes:
                embed.add_field(
                    name="Recent Updates",
                    value="\n".join(changes),
                    inline=False
                )

            embed.add_field(
                name="Next Steps",
                value="Use `/recommendations` to get tailored trade ideas.\nUpdate anytime with this command.",
                inline=False
            )

            embed.set_author(name=f"{interaction.user.display_name}'s Profile", icon_url=interaction.user.display_avatar.url)
            embed.set_footer(text="Profile saved and will influence all recommendations.")

            await interaction.followup.send(embed=embed)

        except Exception as e:
            await log_and_notify_error(
                e, interaction,
                "Error managing risk profile",
                fallback_message="❌ Profile update failed.",
                ephemeral=True
            )

    async def _ai_personalize_profile(self, profile: UserRiskProfile, user_id: str):
        """AI-driven profile personalization."""
        try:
            # Get user trading history from DB
            async with get_db_session() as session:
                history_query = await session.execute(
                    "SELECT symbol, pnl_pct, hold_days, risk_level FROM user_trades WHERE user_id = :user_id ORDER BY trade_date DESC LIMIT 20",
                    {"user_id": user_id}
                )
                history = [dict(row._mapping) for row in history_query.fetchall()]

            if history:
                prompt = self.prompt_manager.get_prompt("profile_personalization")
                personalization_input = {
                    "user_id": user_id,
                    "current_profile": profile.to_dict(),
                    "trading_history": history,
                    "ai_model": self._get_ai_model_for_job("user_explanations")
                }
                notes = await self.ai_processor.generate_text(personalization_input, prompt)
                profile.personalization_notes = notes
                profile.ai_personalized = True

                # Adjust based on history
                avg_pnl = sum(h.get("pnl_pct", 0) for h in history) / len(history)
                avg_hold = sum(h.get("hold_days", 0) for h in history) / len(history)
                if avg_pnl > 10:
                    profile.risk_level = "aggressive" if profile.risk_level != "conservative" else "moderate"
                elif avg_pnl < -5:
                    profile.risk_level = "conservative"
                if avg_hold < 30:
                    profile.time_horizon = "short-term"
                elif avg_hold > 365:
                    profile.time_horizon = "long-term"

                logger.info(f"AI personalized profile for {user_id}")

        except Exception as e:
            logger.error(f"AI personalization failed for {user_id}: {e}")
            profile.personalization_notes = "AI personalization unavailable - using standard profile."

    def _create_enhanced_recommendations_embed(
        self,
        symbol: str,
        recommendations: Dict[str, Any],
        user: discord.User,
        profile: UserRiskProfile
    ) -> discord.Embed:
        """Create comprehensive recommendations embed."""
        signal = recommendations.get("signal", "neutral")
        confidence = recommendations.get("confidence", 0)
        color = discord.Color.green() if signal == "buy" else discord.Color.red() if signal == "sell" else discord.Color.gold()

        embed = discord.Embed(
            title=f"AI-Personalized Recommendations: ${symbol}",
            description=f"Tailored for your {profile.risk_level.title()} profile ({confidence:.0f}% confidence)",
            color=color,
            timestamp=datetime.utcnow()
        )

        # Signal and rationale
        signal_emoji = "🟢" if signal == "buy" else "🔴" if signal == "sell" else "⚪"
        rationale = recommendations.get("rationale", "Based on technical and fundamental analysis.")
        embed.add_field(
            name=f"{signal_emoji} {signal.title()} Signal",
            value=rationale,
            inline=False
        )

        # Entry/Exit with profile-adjusted levels
        entry_points = recommendations.get("entry_points", [])
        if entry_points:
            entry_text = "\n".join([f"• ${p['price']:.2f} ({p['type'].title()}: {p.get('notes', '')})" for p in entry_points[:3]])
            embed.add_field(name="📥 Entry Points", value=entry_text, inline=True)

        exit_points = recommendations.get("exit_points", [])
        if exit_points:
            exit_text = "\n".join([f"• ${p['price']:.2f} ({p['type'].title()}: {p.get('notes', '')})" for p in exit_points[:3]])
            embed.add_field(name="📤 Exit Points", value=exit_text, inline=True)

        # Risk Management tailored to profile
        position_size = recommendations.get("position_size", profile.max_position_size)
        sl = recommendations.get("stop_loss", profile.stop_loss_pct)
        tp = recommendations.get("take_profit", profile.take_profit_pct)
        rr = recommendations.get("risk_reward_ratio", 0)

        embed.add_field(
            name="⚖️ Risk Management",
            value=f"**Position Size**: {position_size:.1f}% (profile: {profile.max_position_size:.1f}%)\n"
                  f"**Stop Loss**: {sl:.1f}% (profile: {profile.stop_loss_pct:.1f}%)\n"
                  f"**Take Profit**: {tp:.1f}% (profile: {profile.take_profit_pct:.1f}%)\n"
                  f"**Risk/Reward**: 1:{rr:.2f}",
            inline=False
        )

        # Backtesting if included
        backtest = recommendations.get("backtesting", {})
        if backtest:
            bt_text = f"Win Rate: {backtest.get('win_rate', 0):.1f}%\nProfit Factor: {backtest.get('profit_factor', 0):.2f}\n"
            bt_text += f"Max Drawdown: {backtest.get('max_drawdown', 0):.1f}%\nSharpe Ratio: {backtest.get('sharpe_ratio', 0):.2f}"
            embed.add_field(name="🧪 Backtesting Results", value=bt_text, inline=False)

        # AI Insights if available
        if "ai_insights" in recommendations:
            insights = recommendations["ai_insights"][:500]
            embed.add_field(name="🤖 AI Insights", value=insights, inline=False)

        # Profile alignment
        alignment_score = self.recommendation_engine.calculate_profile_alignment(recommendations, profile)
        embed.add_field(
            name="🎯 Profile Alignment",
            value=f"Score: {alignment_score:.0f}/100\n(How well this fits your risk profile)",
            inline=True
        )

        embed.set_author(name=f"For {user.display_name}", icon_url=user.display_avatar.url)
        embed.set_footer(text="Recommendations use multi-model AI and real-time data. Not financial advice.")

        return embed


async def setup(bot):
    """Setup enhanced recommendations."""
    await bot.add_cog(EnhancedRecommendationsCommands(bot))
    logger.info("✅ Enhanced Recommendations cog loaded with AI and backtesting")