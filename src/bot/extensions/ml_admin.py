"""
ML Administration Commands - Enhanced Version

Provides admin commands for managing ML model training and status with improved error handling,
logging, and integration with new core components.
"""
import discord
from discord.ext import commands
from discord import app_commands
import asyncio
import logging
from typing import Optional
from datetime import datetime

from src.shared.error_handling.logging import get_logger
from src.core.error_handling.fallback import handle_error_with_fallback
from src.analysis.ai.ml_training_service import ml_training_service
from src.analysis.ai.ml_models import trading_ml_model
from src.bot.core.error_handler import log_and_notify_error
from src.bot.permissions import PermissionLevel

logger = get_logger(__name__)


class MLAdminCog(commands.Cog):
    """ML Administration commands for managing model training with enhanced error handling."""

    def __init__(self, bot):
        self.bot = bot
        logger.info("✅ Enhanced ML Admin extension loaded")

    @app_commands.command(name="ml_status", description="Check ML model training status")
    @app_commands.describe(
        detailed="Include detailed performance metrics"
    )
    async def ml_status(self, interaction: discord.Interaction, detailed: Optional[bool] = False):
        """Check the status of ML models with improved error handling."""
        try:
            await interaction.response.defer(ephemeral=True)  # Ephemeral for admin commands

            # Get training status with fallback
            status = handle_error_with_fallback(
                ml_training_service.get_training_status,
                default={"is_trained": False, "training_in_progress": False, "last_training_time": "Never"}
            )
            model_summary = handle_error_with_fallback(
                trading_ml_model.get_model_summary,
                default={"model_type": "Unknown"}
            )

            # Create embed
            color = discord.Color.green() if status.get('is_trained', False) else discord.Color.orange()
            embed = discord.Embed(
                title="🤖 ML Model Status",
                color=color,
                timestamp=datetime.utcnow()
            )

            # Basic status
            embed.add_field(
                name="📊 Training Status",
                value=f"**Trained**: {'✅ Yes' if status.get('is_trained') else '❌ No'}\n"
                      f"**In Progress**: {'🔄 Yes' if status.get('training_in_progress') else '✅ No'}\n"
                      f"**Last Training**: {status.get('last_training_time', 'Never')}",
                inline=False
            )

            # Model details
            if status.get('is_trained'):
                models = ', '.join(status.get('models_available', []))
                embed.add_field(
                    name="🧠 Available Models",
                    value=f"**Models**: {models}\n"
                          f"**Type**: {model_summary.get('model_type', 'Unknown')}",
                    inline=False
                )

                # Performance metrics
                if detailed and status.get('performance_metrics'):
                    perf_text = ""
                    for model_name, metrics in status['performance_metrics'].items():
                        r2 = metrics.get('r2_score', 0)
                        acc = metrics.get('accuracy', 0)
                        perf_text += f"**{model_name}**: R²={r2:.3f}, Acc={acc:.3f}\n"

                    if perf_text:
                        embed.add_field(
                            name="📈 Performance Metrics",
                            value=perf_text,
                            inline=False
                        )

            # Recent training history
            if status.get('training_history'):
                history_text = ""
                for entry in status['training_history'][-3:]:  # Last 3 entries
                    timestamp = entry['timestamp'][:16]  # Just date and time
                    success = "✅" if entry['success'] else "❌"
                    duration = entry['duration_seconds']
                    samples = entry['training_samples']
                    history_text += f"{success} {timestamp} ({duration:.1f}s, {samples} samples)\n"

                if history_text:
                    embed.add_field(
                        name="📋 Recent Training History",
                        value=history_text,
                        inline=False
                    )

            embed.set_footer(text="Use /ml_train to manually train models (Admin only)")
            await interaction.followup.send(embed=embed)

        except Exception as e:
            await log_and_notify_error(
                e, interaction,
                "Error in ml_status command",
                fallback_message="❌ Error checking ML status. Please check logs.",
                ephemeral=True
            )

    @app_commands.command(name="ml_train", description="Train ML models now (Admin only)")
    async def ml_train(self, interaction: discord.Interaction):
        """Manually trigger ML model training with enhanced validation."""
        try:
            # Check if user has admin permissions with improved check
            if not any(role.name.lower() in ['admin', 'moderator', 'mod'] for role in interaction.user.roles):
                await interaction.response.send_message(
                    "❌ This command requires admin permissions.",
                    ephemeral=True
                )
                return

            await interaction.response.defer(ephemeral=True)

            # Check if training is already in progress with fallback
            status = handle_error_with_fallback(
                ml_training_service.get_training_status,
                default={"training_in_progress": False}
            )
            if status.get('training_in_progress', False):
                await interaction.followup.send(
                    "⏳ ML training is already in progress. Please wait for it to complete.",
                    ephemeral=True
                )
                return

            # Send starting message
            embed = discord.Embed(
                title="🚀 Starting ML Training",
                description="Training ML models with latest market data...",
                color=discord.Color.blue()
            )
            await interaction.followup.send(embed=embed)

            # Train the models with timeout and fallback
            training_result = await asyncio.wait_for(
                ml_training_service.train_models_now(),
                timeout=300.0  # 5 minute timeout
            )

            # Create result embed
            if training_result.get('success', False):
                result_embed = discord.Embed(
                    title="✅ ML Training Completed Successfully",
                    color=discord.Color.green()
                )

                duration = training_result.get('duration_seconds', 0)
                samples = training_result.get('training_samples', 0)
                models = ', '.join(training_result.get('models_trained', []))

                result_embed.add_field(
                    name="📊 Training Results",
                    value=f"**Duration**: {duration:.2f} seconds\n"
                          f"**Samples Used**: {samples:,}\n"
                          f"**Models Trained**: {models}",
                    inline=False
                )

                # Enhanced performance metrics
                if training_result.get('performance'):
                    perf_text = ""
                    for model_name, metrics in training_result['performance'].items():
                        r2 = metrics.get('r2_score', 0)
                        acc = metrics.get('accuracy', 0)
                        mse = metrics.get('mse', 0)
                        perf_text += f"**{model_name}**: R²={r2:.3f}, Acc={acc:.3f}, MSE={mse:.4f}\n"

                    if perf_text:
                        result_embed.add_field(
                            name="📈 Model Performance",
                            value=perf_text,
                            inline=False
                        )

                # Log success
                logger.info(f"ML training completed successfully by {interaction.user.display_name}")

            else:
                error_msg = training_result.get('error', 'Unknown error occurred')
                result_embed = discord.Embed(
                    title="❌ ML Training Failed",
                    description=f"Training failed: {error_msg}",
                    color=discord.Color.red()
                )
                logger.error(f"ML training failed: {error_msg}")

            await interaction.edit_original_response(embed=result_embed)

        except asyncio.TimeoutError:
            await interaction.followup.send(
                "⏰ ML training timed out after 5 minutes. Please check the training service.",
                ephemeral=True
            )
            logger.error("ML training timed out")
        except Exception as e:
            await log_and_notify_error(
                e, interaction,
                "Error during ML training",
                fallback_message="❌ Error during ML training. Please check logs.",
                ephemeral=True
            )

    @app_commands.command(name="ml_predict", description="Test ML prediction on a symbol (Enhanced)")
    @app_commands.describe(
        symbol="Stock symbol for prediction",
        horizon="Prediction horizon (1D, 1W, 1M)"
    )
    async def ml_predict(self, interaction: discord.Interaction, symbol: str, horizon: Optional[str] = "1W"):
        """Test ML prediction on a specific symbol with enhanced data fetching."""
        try:
            await interaction.response.defer(ephemeral=True)

            # Validate model is trained
            if not handle_error_with_fallback(trading_ml_model.is_trained, default=False):
                await interaction.followup.send(
                    "❌ ML models are not trained yet. Use `/ml_train` to train them first.",
                    ephemeral=True
                )
                return

            # Sanitize symbol
            from src.bot.utils.input_sanitizer import InputSanitizer
            sanitized, is_valid, error_msg = InputSanitizer.sanitize_symbol(symbol)
            if not is_valid:
                await interaction.followup.send(f"❌ Invalid symbol: {error_msg}", ephemeral=True)
                return

            # Get market data with fallback
            from src.shared.data_providers.aggregator import DataProviderAggregator
            data_manager = DataProviderAggregator()
            market_data = await handle_error_with_fallback(
                lambda: data_manager.get_ticker(sanitized),
                default={}
            )

            if not market_data or market_data.get("error"):
                await interaction.followup.send(
                    f"❌ Could not fetch market data for {sanitized}. Please try again.",
                    ephemeral=True
                )
                return

            # Prepare historical data with enhanced features
            current_price = market_data.get('current_price', 0)
            historical_data = {
                'close': current_price,
                'volume': market_data.get('volume', 0),
                'high': market_data.get('high', current_price),
                'low': market_data.get('low', current_price),
                'open': market_data.get('open', current_price)
            }

            # Get technical indicators if available
            technical_indicators = {}
            if 'technical_analysis' in market_data:
                ta = market_data['technical_analysis']
                technical_indicators = {
                    'rsi': ta.get('rsi', 50),
                    'macd': ta.get('macd', 0),
                    'sma_20': ta.get('sma_20', current_price),
                    'sma_50': ta.get('sma_50', current_price),
                    'volatility': ta.get('volatility', 'medium')
                }

            # Make prediction with error handling
            prediction = await handle_error_with_fallback(
                lambda: trading_ml_model.predict(
                    historical_data=historical_data,
                    technical_indicators=technical_indicators,
                    prediction_horizon=horizon
                ),
                default=None
            )

            if prediction is None:
                await interaction.followup.send(
                    "❌ Failed to generate prediction. Model may need retraining.",
                    ephemeral=True
                )
                return

            # Create enhanced result embed
            embed = discord.Embed(
                title=f"🤖 ML Prediction for {sanitized}",
                color=discord.Color.blue(),
                timestamp=datetime.utcnow()
            )

            # Prediction details
            direction_emoji = "🟢" if prediction.direction == "bullish" else "🔴" if prediction.direction == "bearish" else "⚪"
            embed.add_field(
                name="📊 Prediction Summary",
                value=f"**Direction**: {direction_emoji} {prediction.direction.title()}\n"
                      f"**Confidence**: {prediction.confidence:.1f}%\n"
                      f"**Probability**: {prediction.probability:.1%}",
                inline=False
            )

            # Price forecast
            predicted_price = prediction.predicted_price
            expected_return = ((predicted_price / current_price) - 1) * 100 if current_price > 0 else 0
            return_emoji = "🟢" if expected_return > 0 else "🔴" if expected_return < 0 else "⚪"

            embed.add_field(
                name="💰 Price Forecast",
                value=f"**Current Price**: ${current_price:.2f}\n"
                      f"**Predicted Price**: ${predicted_price:.2f}\n"
                      f"**Expected Return**: {return_emoji} {expected_return:+.2f}% ({horizon})",
                inline=False
            )

            # Model and features info
            embed.add_field(
                name="🔧 Model Details",
                value=f"**Model Used**: {prediction.model_used}\n"
                      f"**Horizon**: {prediction.prediction_horizon}\n"
                      f"**Features Used**: {len(prediction.features_used)} ({', '.join(prediction.features_used[:3])}{'...' if len(prediction.features_used) > 3 else ''})",
                inline=False
            )

            # Risk assessment
            risk_level = "Low" if prediction.confidence > 80 else "Medium" if prediction.confidence > 60 else "High"
            embed.add_field(
                name="⚠️ Risk Assessment",
                value=f"**Risk Level**: {risk_level}\n"
                      f"**Volatility Impact**: {prediction.volatility_impact or 'Medium'}\n"
                      f"**Market Conditions**: {prediction.market_conditions or 'Neutral'}",
                inline=False
            )

            embed.set_footer(text="Predictions are based on historical data and technical indicators. Not financial advice.")
            await interaction.followup.send(embed=embed)

            # Log prediction request
            logger.info(f"ML prediction requested for {sanitized} by {interaction.user.display_name}: {prediction.direction} ({prediction.confidence:.1f}%)")

        except Exception as e:
            await log_and_notify_error(
                e, interaction,
                f"Error making ML prediction for {sanitized}",
                fallback_message="❌ Error making ML prediction. Please check logs.",
                ephemeral=True
            )


async def setup(bot):
    """Add the enhanced ML admin commands to the bot."""
    await bot.add_cog(MLAdminCog(bot))
    logger.info("✅ Enhanced ML Admin cog loaded")