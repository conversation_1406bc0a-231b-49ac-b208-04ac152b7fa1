"""
Batch Analyze Command Module - Enhanced Version

Implements the /batch_analyze command for multi-symbol processing in the analyze pipeline.
Enhanced with new analysis orchestration, database caching for results, improved parallelism
using asyncio.gather with limits, and integration with core pipeline engine for better
modularity, error isolation per symbol, and performance tracking.
"""
import discord
from discord import app_commands
from discord.ext import commands
import asyncio
import logging
from typing import Optional, List, Dict, Any
from datetime import datetime

from src.shared.error_handling.logging import get_logger
from src.shared.error_handling.fallback import handle_error_with_fallback
from src.bot.core.error_handler import log_and_notify_error
from src.bot.permissions import PermissionLevel
from src.bot.utils.input_sanitizer import InputSanitizer
from src.analysis.orchestration.analysis_orchestrator import AnalysisOrchestrator  # New orchestration
from src.core.pipeline_engine import PipelineEngine  # New core pipeline
from src.core.formatting.response_templates import ResponseGenerator
from src.bot.utils.disclaimer_manager import add_disclaimer
from src.database.unified_db import get_db_session  # For caching results
from src.shared.cache.cache_service import get_cache  # For temporary results

logger = get_logger(__name__)


class BatchAnalyzeCommands(commands.Cog):
    """Enhanced batch analyze commands for multi-symbol processing with orchestration."""

    def __init__(self, bot):
        self.bot = bot
        self.permission_checker = getattr(bot, 'permission_checker', None)
        self.response_generator = ResponseGenerator()
        self.analysis_orchestrator = AnalysisOrchestrator()  # New orchestrator
        self.pipeline_engine = PipelineEngine()  # New core engine
        self.db_cache = get_cache("batch_analysis_cache")  # Cache for batch results

        # Enhanced limits with config
        self.max_symbols = 10  # Increased with better parallelism
        self.max_concurrent = 3  # Limit concurrency to avoid overload
        self.timeout_per_symbol = 45.0  # Per symbol timeout

    @app_commands.command(name="batch_analyze", description="Analyze multiple stocks at once with enhanced orchestration")
    @app_commands.describe(
        symbols="Comma-separated list of stock symbols to analyze (max 10)",
        timeframe="Timeframe for analysis (e.g., 1d, 1w, 1m)",
        indicators="Comma-separated list of indicators to focus on (e.g., rsi,macd,sma)",
        save_results="Save analysis results to database for later reference"
    )
    @app_commands.choices(
        timeframe=[
            app_commands.Choice(name="1 Day", value="1d"),
            app_commands.Choice(name="1 Week", value="1w"),
            app_commands.Choice(name="1 Month", value="1m"),
            app_commands.Choice(name="3 Months", value="3m"),
            app_commands.Choice(name="6 Months", value="6m"),
            app_commands.Choice(name="1 Year", value="1y")
        ]
    )
    async def batch_analyze_command(
        self,
        interaction: discord.Interaction,
        symbols: str,
        timeframe: Optional[str] = "1d",
        indicators: Optional[str] = None,
        save_results: Optional[bool] = False
    ):
        """Enhanced batch analysis with orchestration and DB persistence."""
        try:
            # Permission check
            has_permission, reason = handle_error_with_fallback(
                lambda: self.permission_checker.has_permission(
                    interaction.user, PermissionLevel.PAID, None, str(interaction.guild_id) if interaction.guild_id else None
                ),
                default=(False, "Permission checker unavailable")
            )

            if not has_permission:
                await interaction.response.send_message(
                    f"❌ This command requires paid tier access: {reason}",
                    ephemeral=True
                )
                return

            # Parse and validate symbols
            symbol_list = [s.strip().upper() for s in symbols.split(',') if s.strip()]
            if not symbol_list:
                await interaction.response.send_message(
                    "❌ Please provide at least one valid symbol.",
                    ephemeral=True
                )
                return

            if len(symbol_list) > self.max_symbols:
                await interaction.response.send_message(
                    f"❌ Maximum of {self.max_symbols} symbols allowed. You provided {len(symbol_list)}.",
                    ephemeral=True
                )
                return

            # Sanitize symbols with enhanced validation
            sanitized_symbols = []
            invalid_symbols = []
            for symbol in symbol_list:
                sanitized, is_valid, error_msg = InputSanitizer.sanitize_symbol(symbol)
                if is_valid:
                    sanitized_symbols.append(sanitized)
                else:
                    invalid_symbols.append(f"{symbol} ({error_msg})")

            if invalid_symbols:
                await interaction.response.send_message(
                    f"❌ Invalid symbols: {', '.join(invalid_symbols)}",
                    ephemeral=True
                )
                return

            # Parse indicators
            indicator_list = []
            if indicators:
                indicator_list = [i.strip().lower() for i in indicators.split(',') if i.strip()]

            # Defer and send progress
            await interaction.response.defer()
            progress_msg = await interaction.followup.send(
                f"🔍 Starting batch analysis for {len(sanitized_symbols)} symbols: {', '.join(sanitized_symbols[:3])}{'...' if len(sanitized_symbols) > 3 else ''}\n"
                f"Timeframe: {timeframe} | Indicators: {', '.join(indicator_list) or 'All'}\n"
                f"This may take a few moments due to parallel processing."
            )

            user_id = str(interaction.user.id)
            guild_id = str(interaction.guild_id) if interaction.guild_id else None
            batch_id = f"batch_{user_id}_{datetime.utcnow().timestamp()}"

            try:
                # Enhanced parallel processing with semaphore and error isolation
                semaphore = asyncio.Semaphore(self.max_concurrent)
                tasks = []

                async def analyze_symbol(symbol):
                    async with semaphore:
                        try:
                            # Use new pipeline engine
                            context = self.pipeline_engine.create_context(
                                symbol=symbol,
                                timeframe=timeframe,
                                indicators=indicator_list,
                                user_id=user_id,
                                guild_id=guild_id,
                                batch_id=batch_id
                            )
                            result = await asyncio.wait_for(
                                self.analysis_orchestrator.execute_analysis_pipeline(context),
                                timeout=self.timeout_per_symbol
                            )
                            return symbol, result
                        except asyncio.TimeoutError:
                            logger.warning(f"Timeout analyzing {symbol}")
                            return symbol, self._create_failed_context(symbol, "Timeout")
                        except Exception as e:
                            logger.error(f"Error analyzing {symbol}: {e}")
                            return symbol, self._create_failed_context(symbol, str(e))

                # Create tasks
                for symbol in sanitized_symbols:
                    tasks.append(analyze_symbol(symbol))

                # Gather results with timeout for entire batch
                results = await asyncio.wait_for(
                    asyncio.gather(*tasks, return_exceptions=True),
                    timeout=len(sanitized_symbols) * self.timeout_per_symbol + 30
                )

                # Process results
                success_results = {}
                failed_symbols = []
                for result in results:
                    if isinstance(result, Exception):
                        logger.error(f"Unexpected error in batch: {result}")
                        continue
                    symbol, context = result
                    if context.status == "completed":  # Use new status enum if available
                        success_results[symbol] = context
                    else:
                        failed_symbols.append(symbol)

                # Generate enhanced comparison report
                comparison_report = await self._generate_enhanced_comparison_report(
                    success_results, indicator_list, timeframe, batch_id
                )

                # Save results to DB if requested
                if save_results and success_results:
                    await self._save_batch_results_to_db(user_id, batch_id, success_results, timeframe)

                # Add disclaimer and send
                final_report = add_disclaimer(
                    comparison_report,
                    {'command': 'batch_analyze', 'symbols': sanitized_symbols, 'timeframe': timeframe}
                )

                # Update progress message
                await progress_msg.edit_original_response(
                    content=f"✅ Batch analysis complete for {len(success_results)}/{len(sanitized_symbols)} symbols.\n"
                            f"Failed: {len(failed_symbols)}" if failed_symbols else ""
                )

                # Send report (split if too long)
                if len(final_report) > 2000:
                    # Split into chunks
                    chunks = [final_report[i:i+1900] for i in range(0, len(final_report), 1900)]
                    for chunk in chunks:
                        await interaction.followup.send(chunk)
                else:
                    await interaction.followup.send(final_report)

                if failed_symbols:
                    await interaction.followup.send(
                        f"⚠️ Failed to analyze: {', '.join(failed_symbols)}\n"
                        "Common issues: Data unavailable, timeout, or API limits."
                    )

                # Log batch completion
                logger.info(f"Batch analysis completed for {user_id}: {len(success_results)} successful, {len(failed_symbols)} failed")

            except asyncio.TimeoutError:
                await progress_msg.edit_original_response(
                    content="⏰ Batch analysis timed out. Some results may be incomplete."
                )
                logger.warning(f"Batch analysis timeout for {user_id}")
            except Exception as e:
                await log_and_notify_error(
                    e, interaction,
                    "Error in batch analysis",
                    fallback_message="❌ Batch analysis failed. Please try with fewer symbols."
                )

        except Exception as e:
            await log_and_notify_error(
                e, interaction,
                "Error initiating batch analysis",
                fallback_message="❌ Failed to start batch analysis. Please check your input.",
                ephemeral=True
            )

    def _create_failed_context(self, symbol: str, error: str):
        """Create a failed context for error isolation."""
        from src.core.pipeline_engine import PipelineContext  # Assuming new context class
        context = PipelineContext()
        context.status = "failed"
        context.error_log = [{"error_message": error, "error_type": "BatchAnalysisError", "stage": "analysis"}]
        context.processing_results = {"symbol": symbol, "error": error}
        return context

    async def _generate_enhanced_comparison_report(
        self, results: Dict[str, Any], focus_indicators: List[str], timeframe: str, batch_id: str
    ) -> str:
        """Generate enhanced comparison report with new formatting and metrics."""
        if not results:
            return "❌ No successful analysis results available."

        # Extract data with new orchestrator helpers
        analysis_data = {}
        for symbol, context in results.items():
            if hasattr(context, 'processing_results') and context.status == "completed":
                # Use orchestrator to extract key metrics
                ta_data = self.analysis_orchestrator.extract_technical_summary(context.processing_results)
                analysis_data[symbol] = ta_data

        if not analysis_data:
            return "❌ Technical analysis failed for all symbols. Check data availability."

        # Enhanced report structure
        report = ["# 📊 Enhanced Multi-Symbol Analysis Report\n"]
        report.append(f"**Batch ID**: {batch_id}\n")
        report.append(f"**Timeframe**: {timeframe}\n")
        report.append(f"**Generated**: {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')}\n\n")

        # Summary table with enhanced metrics
        report.append("## 📈 Executive Summary\n")
        report.append("| Symbol | Price | Change | RSI | Trend | Signal | Score |")
        report.append("|--------|-------|--------|-----|-------|--------|-------|")

        for symbol, data in analysis_data.items():
            # Enhanced extraction
            price = data.get('current_price', 'N/A')
            if isinstance(price, (int, float)):
                price = f"${price:.2f}"

            change = data.get('price_change_percent', 'N/A')
            if isinstance(change, (int, float)):
                change_str = f"{change:+.2f}%"
            else:
                change_str = "N/A"

            rsi = data.get('rsi', 'N/A')
            if isinstance(rsi, (int, float)):
                rsi_status = " (🔴 Overbought)" if rsi > 70 else " (🟢 Oversold)" if rsi < 30 else ""
                rsi_str = f"{rsi:.1f}{rsi_status}"
            else:
                rsi_str = "N/A"

            trend = data.get('trend', 'N/A')
            trend_emoji = "🟢" if "bullish" in trend.lower() else "🔴" if "bearish" in trend.lower() else "⚪"
            trend_str = f"{trend_emoji} {trend.title()}"

            signal = data.get('signal', 'N/A')
            signal_emoji = "🟢" if signal == "buy" else "🔴" if signal == "sell" else "⚪"
            signal_str = f"{signal_emoji} {signal.title()}"

            # Overall score using new scoring system
            score = self.analysis_orchestrator.calculate_overall_score(data)
            score_emoji = "🟢" if score > 7 else "🟡" if score > 4 else "🔴"

            report.append(f"| ${symbol} | {price} | {change_str} | {rsi_str} | {trend_str} | {signal_str} | {score_emoji} {score}/10 |")

        report.append("\n")

        # Detailed indicator comparisons if focused
        if focus_indicators:
            report.append("## 🔍 Focused Indicator Analysis\n")
            for indicator in focus_indicators:
                if indicator in ['rsi', 'relative_strength_index']:
                    report.append(self._generate_rsi_comparison(analysis_data))
                elif indicator in ['macd', 'moving_average_convergence_divergence']:
                    report.append(self._generate_macd_comparison(analysis_data))
                elif indicator in ['sma', 'simple_moving_average']:
                    report.append(self._generate_sma_comparison(analysis_data))
                # Add more as needed
                report.append("\n")
        else:
            # Default comprehensive analysis
            report.append("## 📊 Comprehensive Technical Analysis\n")
            report.append(self._generate_rsi_comparison(analysis_data))
            report.append(self._generate_macd_comparison(analysis_data))
            report.append(self._generate_sma_comparison(analysis_data))
            report.append("\n")

        # Enhanced support/resistance with confluence
        report.append("## 🛡️ Support & Resistance Confluence\n")
        report.append("| Symbol | Key Support | Key Resistance | Distance to Support | Distance to Resistance |")
        report.append("|--------|-------------|----------------|---------------------|------------------------|")

        for symbol, data in analysis_data.items():
            support = data.get('support_levels', [0])[0] if data.get('support_levels') else 'N/A'
            resistance = data.get('resistance_levels', [0])[0] if data.get('resistance_levels') else 'N/A'
            current = data.get('current_price', 0)

            support_str = f"${support:.2f}" if support != 'N/A' else 'N/A'
            resistance_str = f"${resistance:.2f}" if resistance != 'N/A' else 'N/A'

            dist_support = f"{((current - support) / current * 100):.1f}%" if support != 'N/A' and current > support else 'N/A'
            dist_resistance = f"{((resistance - current) / current * 100):.1f}%" if resistance != 'N/A' and current < resistance else 'N/A'

            report.append(f"| ${symbol} | {support_str} | {resistance_str} | {dist_support} | {dist_resistance} |")

        # Risk assessment summary
        report.append("\n## ⚠️ Batch Risk Assessment\n")
        high_risk = [s for s, d in analysis_data.items() if d.get('volatility', 'low') == 'high']
        low_vol = [s for s, d in analysis_data.items() if d.get('volatility', 'high') == 'low']
        report.append(f"- High Volatility Symbols: {', '.join(high_risk) or 'None'}\n")
        report.append(f"- Low Volatility Opportunities: {', '.join(low_vol) or 'None'}\n")
        report.append(f"- Recommended Diversification: {len(analysis_data)} symbols across {len(set(d.get('sector', 'Unknown') for d in analysis_data.values()))} sectors\n")

        # Performance notes
        report.append("\n## 📝 Analysis Notes\n")
        report.append("- Analysis uses real-time data from multiple providers\n")
        report.append("- Scores are calculated using weighted technical indicators\n")
        report.append("- Confluence across timeframes increases reliability\n")
        report.append(f"- Generated for {len(analysis_data)} symbols in batch {batch_id}")

        return "\n".join(report)

    def _generate_rsi_comparison(self, analysis_data: Dict[str, Any]) -> str:
        """Enhanced RSI comparison with percentile ranking."""
        report = "### RSI Analysis (Relative Strength Index)\n"
        report += "| Symbol | RSI | Status | Percentile |\n"
        report += "|--------|-----|--------|------------|\n"

        rsi_values = [(s, d.get('rsi', 50)) for s, d in analysis_data.items() if isinstance(d.get('rsi'), (int, float))]
        rsi_values.sort(key=lambda x: x[1])

        for i, (symbol, rsi) in enumerate(rsi_values):
            percentile = ((i + 1) / len(rsi_values)) * 100
            status = "🔴 Overbought" if rsi > 70 else "🟢 Oversold" if rsi < 30 else "🟡 Neutral"
            report += f"| ${symbol} | {rsi:.1f} | {status} | {percentile:.0f}th |\n"

        # Overall RSI insight
        avg_rsi = sum(r[1] for r in rsi_values) / len(rsi_values) if rsi_values else 50
        report += f"\n**Batch Average RSI**: {avg_rsi:.1f} - {'Oversold Opportunity' if avg_rsi < 40 else 'Overbought Caution' if avg_rsi > 60 else 'Neutral Market'}"

        return report

    def _generate_macd_comparison(self, analysis_data: Dict[str, Any]) -> str:
        """Enhanced MACD with signal line crossover detection."""
        report = "### MACD Analysis\n"
        report += "| Symbol | MACD | Signal | Histogram | Crossover |\n"
        report += "|--------|------|--------|-----------|-----------|\n"

        for symbol, data in analysis_data.items():
            indicators = data.get('indicators', {})
            macd = indicators.get('macd', 0)
            signal = indicators.get('macd_signal', 0)
            histogram = macd - signal

            crossover = "🟢 Bullish" if macd > signal else "🔴 Bearish" if macd < signal else "⚪ Neutral"

            report += f"| ${symbol} | {macd:.3f} | {signal:.3f} | {histogram:+.3f} | {crossover} |\n"

        return report

    def _generate_sma_comparison(self, analysis_data: Dict[str, Any]) -> str:
        """Enhanced SMA with golden/death cross detection."""
        report = "### SMA Analysis (Simple Moving Averages)\n"
        report += "| Symbol | Price | SMA20 | SMA50 | SMA200 | Cross Signal |\n"
        report += "|--------|-------|-------|-------|--------|--------------|\n"

        for symbol, data in analysis_data.items():
            indicators = data.get('indicators', {})
            price = indicators.get('current_price', 0)
            sma20 = indicators.get('sma_20', price)
            sma50 = indicators.get('sma_50', price)
            sma200 = indicators.get('sma_200', price)

            # Detect crosses
            cross_signal = "🟢 Golden Cross" if sma50 > sma200 else "🔴 Death Cross" if sma20 < sma50 else "⚪ No Cross"

            report += f"| ${symbol} | ${price:.2f} | ${sma20:.2f} | ${sma50:.2f} | ${sma200:.2f} | {cross_signal} |\n"

        return report

    async def _save_batch_results_to_db(self, user_id: str, batch_id: str, results: Dict[str, Any], timeframe: str):
        """Save batch results to database for persistence."""
        try:
            async with get_db_session() as session:
                for symbol, context in results.items():
                    # Assuming new BatchAnalysisResult model
                    from src.database.models.analysis import BatchAnalysisResult
                    result_model = BatchAnalysisResult(
                        user_id=user_id,
                        batch_id=batch_id,
                        symbol=symbol,
                        timeframe=timeframe,
                        results=context.processing_results,  # JSON serialize
                        created_at=datetime.utcnow(),
                        status="completed"
                    )
                    session.add(result_model)
                await session.commit()
                logger.info(f"Saved batch results {batch_id} for {user_id}")
        except Exception as e:
            logger.error(f"Failed to save batch results: {e}")


async def setup(bot):
    """Add the enhanced batch analyze commands to the bot."""
    await bot.add_cog(BatchAnalyzeCommands(bot))
    logger.info("✅ Enhanced Batch Analyze cog loaded with orchestration")