"""
Enhanced Zones Command Module - Improved Version

Implements enhanced support/resistance zones analysis with multi-timeframe capabilities
and AI-powered probability scoring. Enhanced with new technical_analysis unified calculator,
database persistence for user-saved zones, integration with core pipeline_engine for
orchestrated analysis, and AI insights via unified_ai_processor for zone validation.
Includes confluence detection across timeframes and export functionality.
"""
import discord
from discord import app_commands
from discord.ext import commands
import asyncio
import logging
from typing import Optional, Dict, Any, List
from datetime import datetime

from src.shared.error_handling.logging import get_logger
from src.core.error_handling.fallback import handle_error_with_fallback
from src.bot.core.error_handler import log_and_notify_error
from src.bot.permissions import PermissionLevel
from src.bot.utils.input_sanitizer import InputSanitizer
from src.core.pipeline_engine import PipelineEngine  # New core
from src.analysis.orchestration.analysis_orchestrator import AnalysisOrchestrator  # Orchestration
from src.shared.technical_analysis.unified_calculator import UnifiedTechnicalCalculator  # Unified TA
from src.shared.ai_services.unified_ai_processor import UnifiedAIProcessor  # AI for scoring
from src.database.models.analysis import ZoneAnalysisModel  # New model for zones
from src.database.unified_db import get_db_session
from src.shared.cache.cache_service import get_cache  # Caching zones
from src.bot.utils.disclaimer_manager import add_disclaimer

logger = get_logger(__name__)

# Enhanced timeframes with descriptions
TIMEFRAMES = {
    "1m": {"name": "1 Minute", "description": "Ultra-short term intraday zones"},
    "5m": {"name": "5 Minutes", "description": "Short-term intraday zones"},
    "15m": {"name": "15 Minutes", "description": "Intraday swing zones"},
    "1h": {"name": "1 Hour", "description": "Hourly zones for day trading"},
    "4h": {"name": "4 Hours", "description": "Short-term swing zones"},
    "1d": {"name": "1 Day", "description": "Daily zones for swing trading"},
    "1w": {"name": "1 Week", "description": "Weekly zones for position trading"},
    "1M": {"name": "1 Month", "description": "Monthly zones for long-term analysis"}
}


class EnhancedZonesCommands(commands.Cog):
    """Enhanced zones analysis with AI and multi-timeframe confluence."""

    def __init__(self, bot):
        self.bot = bot
        self.permission_checker = getattr(bot, 'permission_checker', None)
        self.pipeline_engine = PipelineEngine()
        self.analysis_orchestrator = AnalysisOrchestrator()
        self.ta_calculator = UnifiedTechnicalCalculator()
        self.ai_processor = UnifiedAIProcessor()
        self.zone_cache = get_cache("user_zones")
        self.max_concurrent = 3
        self.semaphore = asyncio.Semaphore(self.max_concurrent)

    def _get_ai_model_for_job(self, job: str) -> str:
        """Get the appropriate AI model for a specific job from configuration."""
        try:
            from src.shared.ai_services.simple_model_config import get_model_id_for_job
            return get_model_id_for_job(job)
        except Exception as e:
            logger.warning(f"Failed to get model for job {job}: {e}, using fallback")
            return "gpt-4o-mini"  # Safe fallback

    @app_commands.command(name="zones", description="AI-enhanced support/resistance zones analysis")
    @app_commands.describe(
        symbol="Stock symbol (e.g., AAPL, TSLA)",
        timeframe="Primary timeframe for analysis",
        multi_timeframe="Analyze across multiple timeframes for confluence",
        save_zones="Save zones to your profile for tracking",
        ai_scoring="Use AI to score zone strength and provide insights"
    )
    @app_commands.choices(
        timeframe=[
            app_commands.Choice(name="1 Minute", value="1m"),
            app_commands.Choice(name="5 Minutes", value="5m"),
            app_commands.Choice(name="15 Minutes", value="15m"),
            app_commands.Choice(name="1 Hour", value="1h"),
            app_commands.Choice(name="4 Hours", value="4h"),
            app_commands.Choice(name="1 Day", value="1d"),
            app_commands.Choice(name="1 Week", value="1w"),
            app_commands.Choice(name="1 Month", value="1M")
        ]
    )
    async def zones_command(
        self,
        interaction: discord.Interaction,
        symbol: str,
        timeframe: Optional[str] = "1d",
        multi_timeframe: Optional[bool] = True,
        save_zones: Optional[bool] = False,
        ai_scoring: Optional[bool] = True
    ):
        """Enhanced zones with AI scoring and confluence."""
        try:
            # Permission
            has_permission, reason = handle_error_with_fallback(
                lambda: self.permission_checker.has_permission(
                    interaction.user, PermissionLevel.PAID, None, str(interaction.guild_id) if interaction.guild_id else None
                ),
                default=(False, "Unavailable")
            )

            if not has_permission:
                await interaction.response.send_message(f"❌ Paid access required: {reason}", ephemeral=True)
                return

            # Sanitize
            sanitized_symbol, is_valid, error_msg = InputSanizer.sanitize_symbol(symbol)
            if not is_valid:
                await interaction.response.send_message(f"❌ {error_msg}", ephemeral=True)
                return

            if timeframe not in TIMEFRAMES:
                await interaction.response.send_message(
                    f"❌ Invalid timeframe. Use: {', '.join(TIMEFRAMES.keys())}",
                    ephemeral=True
                )
                return

            await interaction.response.defer()

            # Progress
            progress_msg = await interaction.followup.send(
                f"🔍 Analyzing zones for ${sanitized_symbol} ({TIMEFRAMES[timeframe]['name']})\n"
                f"Multi-timeframe: {'Yes' if multi_timeframe else 'No'} | AI Scoring: {'Yes' if ai_scoring else 'No'}"
            )

            user_id = str(interaction.user.id)
            guild_id = str(interaction.guild_id) if interaction.guild_id else None

            async with self.semaphore:
                # Orchestrated analysis
                context = self.pipeline_engine.create_context(
                    symbol=sanitized_symbol,
                    timeframe=timeframe,
                    user_id=user_id,
                    guild_id=guild_id,
                    analysis_type="zones",
                    multi_timeframe=multi_timeframe
                )

                analysis_result = await asyncio.wait_for(
                    self.analysis_orchestrator.execute_analysis_pipeline(context),
                    timeout=90.0 if multi_timeframe else 60.0
                )

                # Extract and enhance zones
                zones_data = await self._extract_and_enhance_zones(
                    analysis_result, timeframe, multi_timeframe, ai_scoring, user_id
                )

                # Save if requested
                if save_zones:
                    await self._save_user_zones(user_id, sanitized_symbol, zones_data, timeframe)

                # Create embed
                embed = self._create_enhanced_zones_embed(
                    sanitized_symbol, zones_data, interaction.user, timeframe, multi_timeframe, ai_scoring
                )

                # Disclaimer
                embed.set_footer(text=add_disclaimer("Zones Analysis", {'symbol': sanitized_symbol, 'timeframe': timeframe}))

                # Update progress
                await progress_msg.edit_original_response(
                    content=f"✅ Zones analysis complete for ${sanitized_symbol}."
                )

                await interaction.followup.send(embed=embed)

                logger.info(f"Zones analyzed for {user_id}: {sanitized_symbol} ({timeframe})")

        except asyncio.TimeoutError:
            await interaction.followup.send("⏰ Analysis timed out. Try a single timeframe.", ephemeral=True)
        except Exception as e:
            await log_and_notify_error(
                e, interaction,
                f"Zones analysis for {sanitized_symbol}",
                fallback_message="❌ Zones analysis failed. Data may be unavailable.",
                ephemeral=True
            )

    async def _extract_and_enhance_zones(
        self, analysis_result, timeframe: str, multi_timeframe: bool, ai_scoring: bool, user_id: str
    ) -> Dict[str, Any]:
        """Extract zones and enhance with AI/multi-timeframe."""
        zones_data = {
            "timeframe": timeframe,
            "support_levels": [],
            "resistance_levels": [],
            "current_price": 0,
            "trend": "neutral",
            "confluence_score": 0,
            "ai_insights": None,
            "multi_timeframe_data": {} if multi_timeframe else None
        }

        try:
            # Basic extraction from unified calculator
            if hasattr(analysis_result, 'processing_results'):
                market_data = analysis_result.processing_results.get("market_data", {})
                zones_data["current_price"] = market_data.get("current_price", 0)

                ta_data = analysis_result.processing_results.get("technical_analysis", {})
                zones_data["trend"] = ta_data.get("trend", "neutral")

                # Use unified calculator for precise zones
                support, resistance = self.ta_calculator.calculate_support_resistance(
                    ta_data.get("ohlc_data", []), timeframe
                )
                zones_data["support_levels"] = support[:5]  # Top 5
                zones_data["resistance_levels"] = resistance[:5]

                if multi_timeframe:
                    # Multi-timeframe confluence
                    mt_data = await self._analyze_multi_timeframes(analysis_result, user_id)
                    zones_data["multi_timeframe_data"] = mt_data
                    zones_data["confluence_score"] = self._calculate_confluence_score(mt_data)

                # AI scoring if enabled
                if ai_scoring and zones_data["current_price"] > 0:
                    zones_data["ai_insights"] = await self._ai_enhance_zones(
                        zones_data, analysis_result.processing_results
                    )

        except Exception as e:
            logger.error(f"Error enhancing zones: {e}")

        return zones_data

    async def _analyze_multi_timeframes(self, analysis_result, user_id: str) -> Dict:
        """Analyze multiple timeframes for confluence."""
        timeframes = ["1h", "4h", "1d", "1w"] if analysis_result.timeframe != "1m" else ["1m", "5m", "15m", "1h"]
        mt_results = {}

        for tf in timeframes:
            try:
                # Reuse pipeline for each TF
                context_tf = analysis_result.copy()  # Shallow copy
                context_tf.timeframe = tf
                tf_result = await self.analysis_orchestrator.execute_zones_pipeline(context_tf)
                mt_results[tf] = {
                    "support": tf_result.get("support_levels", []),
                    "resistance": tf_result.get("resistance_levels", []),
                    "strength": self._calculate_timeframe_strength(tf_result)
                }
            except Exception as e:
                logger.warning(f"Multi-TF analysis failed for {tf}: {e}")
                mt_results[tf] = {"support": [], "resistance": [], "strength": 0}

        return mt_results

    def _calculate_confluence_score(self, mt_data: Dict) -> float:
        """Calculate overall confluence across timeframes."""
        if not mt_data:
            return 0.0

        # Weight higher timeframes more
        weights = {"1m": 0.5, "5m": 0.7, "15m": 0.8, "1h": 1.0, "4h": 1.2, "1d": 1.5, "1w": 2.0, "1M": 2.5}
        total_weight = sum(weights.get(tf, 1.0) for tf in mt_data)
        weighted_strength = sum(mt_data[tf]["strength"] * weights.get(tf, 1.0) for tf in mt_data)

        return round((weighted_strength / total_weight) * 10, 1)  # 0-10 scale

    def _calculate_timeframe_strength(self, tf_result) -> float:
        """Calculate strength for a single timeframe."""
        support = tf_result.get("support_levels", [])
        resistance = tf_result.get("resistance_levels", [])
        confluence_count = len(support) + len(resistance)
        return min(10, confluence_count * 1.5)  # Simple scoring

    async def _ai_enhance_zones(self, zones_data: Dict, analysis_data: Dict) -> str:
        """Use AI to provide insights on zones."""
        try:
            prompt = """
            Analyze these support/resistance zones for {symbol}:
            Current Price: ${current_price}
            Trend: {trend}
            Support Levels: {support}
            Resistance Levels: {resistance}
            Confluence Score: {confluence}/10

            Provide:
            1. Key insights on zone strength
            2. Trading implications (buy/sell/hold)
            3. Risk factors
            4. Timeframe validity

            Keep response concise (under 300 words).
            """
            prompt_formatted = prompt.format(
                symbol=zones_data.get("symbol", "Unknown"),
                current_price=zones_data["current_price"],
                trend=zones_data["trend"],
                support=", ".join([f"${s:.2f}" for s in zones_data["support_levels"]]),
                resistance=", ".join([f"${r:.2f}" for r in zones_data["resistance_levels"]]),
                confluence=zones_data.get("confluence_score", 0)
            )

            insights = await self.ai_processor.generate_text(
                analysis_data,  # Context
                prompt_formatted,
                model=self._get_ai_model_for_job("technical_analysis")
            )
            return insights[:500]  # Truncate

        except Exception as e:
            logger.error(f"AI zone enhancement failed: {e}")
            return "AI analysis unavailable - zones based on technical calculation only."

    async def _save_user_zones(self, user_id: str, symbol: str, zones_data: Dict, timeframe: str):
        """Save zones to user profile."""
        try:
            async with get_db_session() as session:
                # Assuming ZoneAnalysisModel
                zone_model = ZoneAnalysisModel(
                    user_id=user_id,
                    symbol=symbol,
                    timeframe=timeframe,
                    support_levels=zones_data["support_levels"],
                    resistance_levels=zones_data["resistance_levels"],
                    current_price=zones_data["current_price"],
                    trend=zones_data["trend"],
                    confluence_score=zones_data.get("confluence_score", 0),
                    ai_insights=zones_data.get("ai_insights"),
                    created_at=datetime.utcnow()
                )
                session.add(zone_model)
                await session.commit()

                # Cache
                cache_key = f"zones_{user_id}_{symbol}"
                self.zone_cache.set(cache_key, zones_data, ttl=3600)

                logger.debug(f"Saved zones for {user_id}: {symbol} ({timeframe})")

        except Exception as e:
            logger.error(f"Failed to save zones: {e}")

    def _create_enhanced_zones_embed(
        self, symbol: str, zones_data: Dict, user: discord.User, timeframe: str,
        multi_timeframe: bool, ai_scoring: bool
    ) -> discord.Embed:
        """Create comprehensive zones embed."""
        current_price = zones_data.get("current_price", 0)
        support = zones_data.get("support_levels", [])
        resistance = zones_data.get("resistance_levels", [])
        trend = zones_data.get("trend", "neutral")
        confluence = zones_data.get("confluence_score", 0)
        ai_insights = zones_data.get("ai_insights")
        mt_data = zones_data.get("multi_timeframe_data")

        color = discord.Color.blue() if trend == "bullish" else discord.Color.red() if trend == "bearish" else discord.Color.gray()

        embed = discord.Embed(
            title=f"🛡️ Enhanced Zones Analysis: ${symbol}",
            description=f"{TIMEFRAMES[timeframe]['description']} | Confluence: {confluence}/10 | Multi-TF: {'Yes' if multi_timeframe else 'No'}",
            color=color,
            timestamp=datetime.utcnow()
        )

        # Current price and trend
        price_emoji = "🟢" if trend == "bullish" else "🔴" if trend == "bearish" else "⚪"
        embed.add_field(
            name=f"{price_emoji} Current Price & Trend",
            value=f"${current_price:.2f}\nTrend: {trend.title()}",
            inline=True
        )

        # Support levels
        if support:
            support_text = "\n".join([f"S{i+1}: ${s:.2f}" for i, s in enumerate(support[:4])])
            embed.add_field(name="📉 Support Levels", value=support_text, inline=True)

        # Resistance levels
        if resistance:
            resistance_text = "\n".join([f"R{i+1}: ${r:.2f}" for i, r in enumerate(resistance[:4])])
            embed.add_field(name="📈 Resistance Levels", value=resistance_text, inline=True)

        # Confluence if multi-TF
        if multi_timeframe and mt_data:
            conf_text = ""
            for tf, data in list(mt_data.items())[:3]:
                strength = data["strength"]
                conf_emoji = "🟢" if strength > 7 else "🟡" if strength > 4 else "🔴"
                conf_text += f"{conf_emoji} {TIMEFRAMES[tf]['name'][:10]}: {strength}/10\n"
            embed.add_field(name="🔗 Multi-Timeframe Confluence", value=conf_text, inline=False)

        # AI Insights
        if ai_scoring and ai_insights:
            embed.add_field(name="🤖 AI Zone Insights", value=ai_insights, inline=False)

        # Key implications
        if current_price > 0 and support and resistance:
            closest_support = min([s for s in support if s < current_price] or [0])
            closest_resistance = min([r for r in resistance if r > current_price] or [float('inf')])
            risk = current_price - closest_support if closest_support > 0 else 0
            reward = closest_resistance - current_price if closest_resistance < float('inf') else 0
            rr_ratio = reward / risk if risk > 0 else 0

            embed.add_field(
                name="⚖️ Risk/Reward Analysis",
                value=f"Closest Support: ${closest_support:.2f}\n"
                      f"Closest Resistance: ${closest_resistance:.2f}\n"
                      f"Risk/Reward Ratio: 1:{rr_ratio:.2f}",
                inline=False
            )

        embed.set_author(name=f"Analysis for {user.display_name}", icon_url=user.display_avatar.url)
        embed.set_footer(text=f"Generated {datetime.utcnow().strftime('%H:%M UTC')} | Zones auto-update daily")

        return embed

    @app_commands.command(name="multizones", description="Multi-timeframe zones comparison with confluence mapping")
    @app_commands.describe(
        symbol="Stock symbol",
        ai_confluence="Use AI to map confluence and provide trading strategy"
    )
    async def multizones_command(
        self,
        interaction: discord.Interaction,
        symbol: str,
        ai_confluence: Optional[bool] = True
    ):
        """Enhanced multi-timeframe zones."""
        try:
            # Similar permission/sanitize as above
            has_permission, _ = handle_error_with_fallback(
                lambda: self.permission_checker.has_permission(
                    interaction.user, PermissionLevel.PAID, None, str(interaction.guild_id) if interaction.guild_id else None
                ),
                default=(False, "")
            )

            if not has_permission:
                await interaction.response.send_message("❌ Paid access required.", ephemeral=True)
                return

            sanitized_symbol, is_valid, _ = InputSanitizer.sanitize_symbol(symbol)
            if not is_valid:
                await interaction.response.send_message("❌ Invalid symbol.", ephemeral=True)
                return

            await interaction.response.defer()

            progress_msg = await interaction.followup.send(f"🔍 Multi-timeframe zones for ${sanitized_symbol}...")

            user_id = str(interaction.user.id)

            async with self.semaphore:
                # Orchestrate multi-TF
                context = self.pipeline_engine.create_context(
                    symbol=sanitized_symbol,
                    timeframe="1d",  # Base
                    user_id=user_id,
                    analysis_type="multi_zones",
                    multi_timeframe=True
                )

                analysis_result = await asyncio.wait_for(
                    self.analysis_orchestrator.execute_analysis_pipeline(context),
                    timeout=180.0  # Longer for multi
                )

                # Enhance
                mt_zones = await self._extract_and_enhance_zones(
                    analysis_result, "multi", True, ai_confluence, user_id
                )

                # Embed
                embed = self._create_multizones_embed(sanitized_symbol, mt_zones, interaction.user, ai_confluence)

                await progress_msg.edit_original_response(content=f"✅ Multi-timeframe analysis complete.")
                await interaction.followup.send(embed=embed)

        except Exception as e:
            await log_and_notify_error(
                e, interaction,
                f"Multi-zones for {sanitized_symbol}",
                fallback_message="❌ Multi-timeframe analysis failed.",
                ephemeral=True
            )

    def _create_multizones_embed(self, symbol: str, zones_data: Dict, user: discord.User, ai_confluence: bool) -> discord.Embed:
        """Create multi-zones embed."""
        embed = discord.Embed(
            title=f"🌐 Multi-Timeframe Zones: ${symbol}",
            description="Confluence analysis across timeframes",
            color=discord.Color.purple(),
            timestamp=datetime.utcnow()
        )

        mt_data = zones_data.get("multi_timeframe_data", {})
        current_price = zones_data.get("current_price", 0)

        # Per timeframe summary
        for tf, data in mt_data.items():
            support = data.get("support", [])
            resistance = data.get("resistance", [])
            strength = data.get("strength", 0)

            strength_emoji = "🟢" if strength > 7 else "🟡" if strength > 4 else "🔴"
            tf_summary = f"Support: {len(support)} | Resistance: {len(resistance)}\n"
            if support:
                tf_summary += f"Key S: ${support[0]:.2f}"
            if resistance:
                tf_summary += f" | Key R: ${resistance[0]:.2f}"

            embed.add_field(
                name=f"{TIMEFRAMES[tf]['name']} {strength_emoji} ({strength}/10)",
                value=tf_summary,
                inline=True
            )

        # Overall confluence
        conf_score = zones_data.get("confluence_score", 0)
        conf_emoji = "🟢" if conf_score > 7 else "🟡" if conf_score > 4 else "🔴"
        embed.add_field(
            name=f"{conf_emoji} Overall Confluence Score",
            value=f"{conf_score}/10\nStrong zones where multiple TFs align.",
            inline=False
        )

        # AI Confluence Strategy
        if ai_confluence and zones_data.get("ai_insights"):
            embed.add_field(
                name="🤖 AI Trading Strategy",
                value=zones_data["ai_insights"][:400],
                inline=False
            )

        # Price positioning
        if current_price > 0:
            embed.add_field(
                name="📍 Price Position",
                value=f"${current_price:.2f}\n"
                      f"Strongest Zone Nearby: {self._find_nearest_zone(current_price, mt_data):.2f}",
                inline=True
            )

        embed.set_author(name=f"Multi-TF Analysis for {user.display_name}", icon_url=user.display_avatar.url)
        embed.set_footer(text="Confluence increases reliability. Monitor for breaks.")

        return embed

    def _find_nearest_zone(self, price: float, mt_data: Dict) -> float:
        """Find nearest zone across all TFs."""
        all_zones = []
        for tf, data in mt_data.items():
            all_zones.extend(data.get("support", []))
            all_zones.extend(data.get("resistance", []))
        if all_zones:
            return min(all_zones, key=lambda z: abs(z - price))
        return price

    @app_commands.command(name="myzones", description="View your saved zones analyses")
    async def my_zones_command(self, interaction: discord.Interaction):
        """View saved zones."""
        try:
            if not self.permission_checker.has_permission(interaction.user, PermissionLevel.PAID):
                await interaction.response.send_message("❌ Paid access.", ephemeral=True)
                return

            await interaction.response.defer(ephemeral=True)

            user_id = str(interaction.user.id)

            # Get from cache/DB
            cache_key = f"my_zones_{user_id}"
            cached = self.zone_cache.get(cache_key)
            if cached:
                zones_list = cached
            else:
                async with get_db_session() as session:
                    query = await session.execute(
                        "SELECT symbol, timeframe, confluence_score, created_at FROM user_zone_analyses "
                        "WHERE user_id = :user_id ORDER BY created_at DESC LIMIT 10",
                        {"user_id": user_id}
                    )
                    zones_list = query.fetchall()
                    self.zone_cache.set(cache_key, zones_list, ttl=300)

            if not zones_list:
                await interaction.followup.send("No saved zones found. Use /zones save_zones=true to save.", ephemeral=True)
                return

            embed = discord.Embed(
                title="💾 Your Saved Zones",
                description="Recent zones analyses",
                color=discord.Color.blue()
            )

            for zone in zones_list[:5]:  # Top 5
                embed.add_field(
                    name=f"${zone.symbol} ({zone.timeframe})",
                    value=f"Confluence: {zone.confluence_score}/10\n"
                          f"Date: {zone.created_at.strftime('%Y-%m-%d %H:%M')}",
                    inline=True
                )

            embed.set_footer(text="Use /zones to create new analyses.")
            await interaction.followup.send(embed=embed)

        except Exception as e:
            await log_and_notify_error(
                e, interaction,
                "Error loading saved zones",
                fallback_message="❌ Failed to load zones.",
                ephemeral=True
            )


async def setup(bot):
    """Setup enhanced zones."""
    await bot.add_cog(EnhancedZonesCommands(bot))
    logger.info("✅ Enhanced Zones cog loaded with AI and multi-TF")