"""
Discord Bot Performance Monitor Extension - Enhanced Version

Provides performance monitoring commands and real-time optimization status.
Enhanced with integration to new monitoring_pkg, database logging for metrics,
improved alerting via core scheduler, and modular configuration using core config_manager.
Includes historical trend analysis and automated optimization triggers.
"""
import asyncio
import time
from typing import Dict, Any, Optional
from datetime import datetime, timedelta
import discord
from discord.ext import commands, tasks
from discord import app_commands

from src.shared.error_handling.logging import get_logger
from src.core.error_handling.fallback import handle_error_with_fallback
from src.bot.core.error_handler import log_and_notify_error
from src.core.monitoring_pkg.performance_tracker import PerformanceTracker  # New monitoring
from src.core.monitoring_pkg.bot_monitor import BotMonitor  # New bot monitor
from src.core.config_manager import get_config  # New config
from src.shared.services.enhanced_performance_optimizer import (
    performance_optimizer,
    get_performance_stats,
    OptimizationConfig
)
from src.database.unified_db import get_db_session  # For metrics persistence
from src.shared.cache.cache_service import get_cache  # For trend caching
from src.bot.permissions import PermissionLevel

logger = get_logger(__name__)


class PerformanceMonitor(commands.Cog):
    """Enhanced performance monitoring with database persistence and automated alerts."""

    def __init__(self, bot):
        self.bot = bot
        self.last_performance_check = datetime.now()
        self.performance_tracker = PerformanceTracker()  # New tracker
        self.bot_monitor = BotMonitor(bot)  # New bot monitor
        self.config = get_config("performance_monitoring")  # New config integration
        self.db_cache = get_cache("performance_trends")  # Cache for trends

        # Enhanced thresholds from config
        self.alert_thresholds = self.config.get("alert_thresholds", {
            "response_time": 5.0,  # seconds
            "memory_usage": 80.0,  # percent
            "cpu_usage": 85.0,     # percent
            "cache_hit_rate": 60.0  # percent (minimum)
        })
        self.performance_alerts_enabled = self.config.get("alerts_enabled", True)

        # Historical data storage
        self.performance_history = []  # In-memory, synced to DB periodically

    async def cog_load(self):
        """Initialize with DB sync and start loops."""
        logger.info("Enhanced Performance Monitor loading...")
        await self._load_historical_data()
        self.performance_check_loop.start()
        self.optimization_loop.start()
        logger.info("✅ Enhanced Performance Monitor loaded")

    async def cog_unload(self):
        """Save history and cleanup."""
        await self._save_historical_data()
        self.performance_check_loop.cancel()
        self.optimization_loop.cancel()
        logger.info("Enhanced Performance Monitor unloaded")

    @app_commands.command(name="performance", description="Show enhanced system performance dashboard")
    @app_commands.describe(
        timeframe="Historical timeframe (1h, 1d, 7d)",
        detailed="Include detailed metrics and trends"
    )
    async def performance_status(self, interaction: discord.Interaction, timeframe: Optional[str] = "1h", detailed: Optional[bool] = False):
        """Enhanced dashboard with historical trends and DB metrics."""
        await interaction.response.defer(ephemeral=True)  # Ephemeral for sensitive info

        try:
            # Get current stats with new monitor
            stats = await handle_error_with_fallback(
                self.bot_monitor.get_comprehensive_stats,
                default={"status": "no_data"}
            )

            if stats.get("status") == "no_data":
                embed = self._create_error_embed(
                    title="⚠️ Performance Data Unavailable",
                    description="System may be initializing. Data collection starting now."
                )
                await interaction.followup.send(embed=embed)
                return

            # Get historical data for timeframe
            historical = await self._get_historical_trends(timeframe)

            # Create enhanced embed
            embed = discord.Embed(
                title="🚀 Enhanced Performance Dashboard",
                description="Real-time and historical system metrics",
                color=self._get_status_color(stats.get("success_rate", 0)),
                timestamp=datetime.utcnow()
            )

            # System Health Overview
            success_rate = stats.get("success_rate", 0)
            status_emoji = self._get_emoji_for_value(success_rate, 95, 85)
            embed.add_field(
                name=f"{status_emoji} System Health",
                value=f"**Success Rate**: {success_rate:.1f}%\n"
                      f"**Operations (24h)**: {stats.get('total_operations_24h', 0):,}\n"
                      f"**Failures (24h)**: {stats.get('failed_operations_24h', 0)}\n"
                      f"**Uptime**: {stats.get('uptime_percentage', 0):.2f}%",
                inline=True
            )

            # Response Performance with trends
            avg_time = stats.get("avg_execution_time", 0)
            time_emoji = self._get_emoji_for_value(avg_time, 2, 5, invert=True)
            avg_time_trend = historical.get("avg_time_trend", "stable") if historical else "N/A"
            embed.add_field(
                name=f"{time_emoji} Response Performance",
                value=f"**Avg Response Time**: {avg_time:.2f}s ({avg_time_trend})\n"
                      f"**P95 Response Time**: {stats.get('p95_execution_time', 0):.2f}s\n"
                      f"**Cache Hit Rate**: {stats.get('cache_hit_rate', 0):.1f}%\n"
                      f"**Optimizations Applied**: {stats.get('optimizations_applied', 0):,}",
                inline=True
            )

            # Resource Usage with alerts
            resource_usage = stats.get("resource_usage", {})
            cpu_percent = resource_usage.get("cpu_percent", 0)
            memory_percent = resource_usage.get("memory_percent", 0)
            cpu_emoji = self._get_emoji_for_value(cpu_percent, 70, 85, invert=True)
            memory_emoji = self._get_emoji_for_value(memory_percent, 70, 85, invert=True)

            alerts_active = len([a for a in stats.get("active_alerts", []) if a["severity"] == "high"])
            embed.add_field(
                name=f"💻 Resource Usage {f'(⚠️ {alerts_active} alerts)' if alerts_active else ''}",
                value=f"{cpu_emoji} **CPU**: {cpu_percent:.1f}% (Cores: {resource_usage.get('cpu_count', 0)})\n"
                      f"{memory_emoji} **Memory**: {memory_percent:.1f}% ({resource_usage.get('memory_used_mb', 0):.0f}/{resource_usage.get('memory_total_mb', 0):.0f}MB)\n"
                      f"**Disk Usage**: {resource_usage.get('disk_percent', 0):.1f}%\n"
                      f"**Network I/O**: {resource_usage.get('network_in_mb', 0):.1f}MB in / {resource_usage.get('network_out_mb', 0):.1f}MB out",
                inline=True
            )

            # Optimization and Efficiency
            opt_stats = stats.get("optimization_stats", {})
            efficiency_score = self.performance_tracker.calculate_efficiency_score(stats)
            embed.add_field(
                name=f"⚡ Efficiency Score: {efficiency_score:.1f}/100",
                value=f"**Total Optimizations**: {opt_stats.get('total_optimizations', 0):,}\n"
                      f"**Cache Efficiency**: {opt_stats.get('cache_efficiency', 0):.1f}%\n"
                      f"**DB Query Efficiency**: {stats.get('db_query_efficiency', 0):.1f}%\n"
                      f"**API Rate Limit Usage**: {stats.get('api_rate_limit_usage', 0):.1f}%",
                inline=True
            )

            # Historical Trends if detailed
            if detailed and historical:
                embed.add_field(
                    name="📈 24h Trends",
                    value=f"**Response Time**: {historical.get('response_time_trend', 'N/A')}\n"
                          f"**Memory Usage**: {historical.get('memory_trend', 'N/A')}\n"
                          f"**Success Rate**: {historical.get('success_trend', 'N/A')}\n"
                          f"**Peak Load**: {historical.get('peak_load_time', 'N/A')}",
                    inline=False
                )

            # Recommendations based on analysis
            recommendations = self._generate_recommendations(stats, historical)
            if recommendations:
                embed.add_field(
                    name="🔧 Action Recommendations",
                    value=recommendations,
                    inline=False
                )

            embed.set_footer(text=f"Data refreshed: <t:{int(datetime.utcnow().timestamp())}:R> | Detailed view enabled")
            await interaction.followup.send(embed=embed)

        except Exception as e:
            await log_and_notify_error(
                e, interaction,
                "Error generating performance dashboard",
                fallback_message="❌ Failed to generate performance dashboard. Basic stats unavailable.",
                ephemeral=True
            )

    def _get_status_color(self, success_rate: float) -> discord.Color:
        """Get embed color based on success rate."""
        if success_rate > 95:
            return discord.Color.green()
        elif success_rate > 85:
            return discord.Color.gold()
        else:
            return discord.Color.red()

    def _get_emoji_for_value(self, value: float, good_threshold: float, warning_threshold: float, invert: bool = False) -> str:
        """Get emoji based on value thresholds."""
        if invert:
            if value < good_threshold:
                return "🟢"
            elif value < warning_threshold:
                return "🟡"
            else:
                return "🔴"
        else:
            if value > good_threshold:
                return "🟢"
            elif value > warning_threshold:
                return "🟡"
            else:
                return "🔴"

    def _create_error_embed(self, title: str, description: str) -> discord.Embed:
        """Create standardized error embed."""
        return discord.Embed(
            title=title,
            description=description,
            color=discord.Color.red(),
            timestamp=datetime.utcnow()
        )

    async def _load_historical_data(self):
        """Load historical performance data from DB."""
        try:
            async with get_db_session() as session:
                # Assuming PerformanceMetric model
                from src.database.models.monitoring import PerformanceMetric
                query = await session.execute(
                    "SELECT * FROM performance_metrics WHERE created_at > NOW() - INTERVAL '7 days' ORDER BY created_at DESC LIMIT 100"
                )
                rows = query.fetchall()
                self.performance_history = [row.to_dict() for row in rows]  # Assuming to_dict method
                logger.info(f"Loaded {len(self.performance_history)} historical records")
        except Exception as e:
            logger.error(f"Failed to load historical data: {e}")

    async def _save_historical_data(self):
        """Save current history to DB."""
        if not self.performance_history:
            return
        try:
            async with get_db_session() as session:
                from src.database.models.monitoring import PerformanceMetric
                for record in self.performance_history[-10:]:  # Save last 10
                    metric = PerformanceMetric(**record)
                    session.add(metric)
                await session.commit()
                logger.info("Saved performance history to DB")
        except Exception as e:
            logger.error(f"Failed to save historical data: {e}")

    async def _get_historical_trends(self, timeframe: str) -> Dict[str, str]:
        """Get trends for specified timeframe."""
        cache_key = f"trends_{timeframe}"
        cached = self.db_cache.get(cache_key)
        if cached:
            return cached

        # Calculate trends from history
        trends = self.performance_tracker.calculate_trends(self.performance_history, timeframe)
        self.db_cache.set(cache_key, trends, ttl=300)  # 5 min cache
        return trends

    def _generate_recommendations(self, stats: Dict, historical: Optional[Dict]) -> str:
        """Generate actionable recommendations."""
        recs = []
        avg_time = stats.get("avg_execution_time", 0)
        if avg_time > self.alert_thresholds["response_time"]:
            recs.append("• Optimize slow endpoints (check logs for bottlenecks)")
        memory = stats.get("resource_usage", {}).get("memory_percent", 0)
        if memory > self.alert_thresholds["memory_usage"]:
            recs.append("• Review memory leaks in long-running tasks")
        if historical and historical.get("success_trend") == "decreasing":
            recs.append("• Investigate recent failures in error logs")
        if stats.get("cache_hit_rate", 0) < 70:
            recs.append("• Increase cache size or adjust eviction policy")
        return "\n".join(recs) if recs else "• System performing optimally\n• Continue monitoring key metrics"

    @app_commands.command(name="optimize", description="Trigger automated performance optimizations (Admin)")
    @app_commands.describe(
        optimization_type="Type of optimization (auto, cache, db, memory)",
        aggressive="Use aggressive optimization (may cause brief downtime)"
    )
    async def trigger_optimization(self, interaction: discord.Interaction, optimization_type: Optional[str] = "auto", aggressive: Optional[bool] = False):
        """Enhanced optimization with confirmation and logging."""
        # Admin check
        if not any(role.name.lower() in ['admin', 'moderator'] for role in interaction.user.roles):
            await interaction.response.send_message("❌ Admin only.", ephemeral=True)
            return

        await interaction.response.defer(ephemeral=True)

        try:
            # Confirmation for aggressive
            if aggressive:
                confirm_embed = discord.Embed(
                    title="⚠️ Aggressive Optimization Confirmation",
                    description="This may cause brief service interruptions. Confirm?",
                    color=discord.Color.orange()
                )
                confirm_msg = await interaction.followup.send(embed=confirm_embed)
                # Simple confirmation (in production, use buttons)
                await asyncio.sleep(2)  # Simulate wait

            # Trigger optimization using new optimizer
            opt_config = OptimizationConfig(
                type=optimization_type,
                aggressive=aggressive,
                auto_approve=not aggressive
            )
            result = await performance_optimizer.optimize_system(opt_config)

            embed = discord.Embed(
                title="⚡ Optimization Complete",
                color=discord.Color.green() if result.get("success") else discord.Color.red(),
                timestamp=datetime.utcnow()
            )

            if result.get("success"):
                embed.description = f"{optimization_type.title()} optimization applied successfully."
                embed.add_field(
                    name="Improvements",
                    value=f"• Time Saved: {result.get('time_saved', 0):.1f}s\n"
                          f"• Resources Freed: {result.get('memory_freed_mb', 0):.0f}MB\n"
                          f"• Queries Optimized: {result.get('queries_optimized', 0):,}",
                    inline=False
                )
                logger.info(f"Optimization successful: {optimization_type} by {interaction.user.display_name}")
            else:
                embed.description = f"Optimization failed: {result.get('error', 'Unknown error')}"
                logger.error(f"Optimization failed: {result.get('error')}")

            await interaction.edit_original_response(embed=embed)

        except Exception as e:
            await log_and_notify_error(
                e, interaction,
                "Error triggering optimization",
                fallback_message="❌ Optimization failed. Manual intervention required.",
                ephemeral=True
            )

    @app_commands.command(name="performance-config", description="Configure performance monitoring (Admin)")
    @app_commands.describe(
        alerts_enabled="Enable/disable performance alerts",
        response_threshold="Response time alert threshold (seconds)",
        memory_threshold="Memory usage alert threshold (%)",
        cpu_threshold="CPU usage alert threshold (%)",
        cache_threshold="Minimum cache hit rate threshold (%)"
    )
    async def configure_performance(self, interaction: discord.Interaction,
                                   alerts_enabled: Optional[bool] = None,
                                   response_threshold: Optional[float] = None,
                                   memory_threshold: Optional[float] = None,
                                   cpu_threshold: Optional[float] = None,
                                   cache_threshold: Optional[float] = None):
        """Enhanced configuration with DB persistence."""
        # Admin check
        if not any(role.name.lower() in ['admin', 'moderator'] for role in interaction.user.roles):
            await interaction.response.send_message("❌ Admin only.", ephemeral=True)
            return

        await interaction.response.defer(ephemeral=True)

        try:
            changes = []
            updated = False

            if alerts_enabled is not None:
                self.performance_alerts_enabled = alerts_enabled
                self.config.set("alerts_enabled", alerts_enabled)
                changes.append(f"Alerts: {'Enabled' if alerts_enabled else 'Disabled'}")
                updated = True

            if response_threshold is not None:
                self.alert_thresholds["response_time"] = response_threshold
                self.config.set("alert_thresholds.response_time", response_threshold)
                changes.append(f"Response threshold: {response_threshold}s")
                updated = True

            if memory_threshold is not None:
                self.alert_thresholds["memory_usage"] = memory_threshold
                self.config.set("alert_thresholds.memory_usage", memory_threshold)
                changes.append(f"Memory threshold: {memory_threshold}%")
                updated = True

            if cpu_threshold is not None:
                self.alert_thresholds["cpu_usage"] = cpu_threshold
                self.config.set("alert_thresholds.cpu_usage", cpu_threshold)
                changes.append(f"CPU threshold: {cpu_threshold}%")
                updated = True

            if cache_threshold is not None:
                self.alert_thresholds["cache_hit_rate"] = cache_threshold
                self.config.set("alert_thresholds.cache_hit_rate", cache_threshold)
                changes.append(f"Cache threshold: {cache_threshold}%")
                updated = True

            # Save config to DB
            if updated:
                await self.config.save_to_db()

            embed = discord.Embed(
                title="⚙️ Performance Configuration Updated",
                description="Settings have been persisted and will apply immediately.",
                color=discord.Color.blue(),
                timestamp=datetime.utcnow()
            )

            if changes:
                embed.add_field(
                    name="📝 Changes Applied",
                    value="\n".join(f"• {change}" for change in changes),
                    inline=False
                )

            # Current configuration
            embed.add_field(
                name="🔧 Active Configuration",
                value=f"**Alerts**: {self.performance_alerts_enabled}\n"
                      f"**Response Time**: {self.alert_thresholds['response_time']}s\n"
                      f"**Memory**: {self.alert_thresholds['memory_usage']}%\n"
                      f"**CPU**: {self.alert_thresholds['cpu_usage']}%\n"
                      f"**Cache Hit Rate**: {self.alert_thresholds['cache_hit_rate']}%",
                inline=False
            )

            embed.set_footer(text="Configuration saved to database and cached for fast access.")
            await interaction.followup.send(embed=embed)

        except Exception as e:
            await log_and_notify_error(
                e, interaction,
                "Error updating configuration",
                fallback_message="❌ Configuration update failed. Reverting changes.",
                ephemeral=True
            )

    @tasks.loop(minutes=5)
    async def performance_check_loop(self):
        """Enhanced periodic check with DB logging and alerting."""
        try:
            self.last_performance_check = datetime.now()

            if not self.performance_alerts_enabled:
                return

            # Get comprehensive stats
            stats = await self.bot_monitor.get_comprehensive_stats()

            if stats.get("status") == "no_data":
                return

            # Log to DB
            await self._log_performance_metrics(stats)

            # Enhanced alerting
            alerts = self._check_alert_conditions(stats)
            if alerts:
                await self._send_enhanced_alerts(alerts, stats)

            # Update history
            self.performance_history.append(stats)
            if len(self.performance_history) > 1000:  # Keep last 1000
                self.performance_history = self.performance_history[-1000:]

        except Exception as e:
            logger.error(f"Performance check error: {e}")

    @tasks.loop(hours=1)
    async def optimization_loop(self):
        """Automated optimization every hour based on trends."""
        try:
            if self.performance_tracker.needs_optimization(self.performance_history):
                logger.info("Automated optimization triggered by trend analysis")
                opt_config = OptimizationConfig(type="auto", auto_approve=True)
                await performance_optimizer.optimize_system(opt_config)
        except Exception as e:
            logger.error(f"Automated optimization error: {e}")

    async def _log_performance_metrics(self, stats: Dict):
        """Log metrics to database."""
        try:
            async with get_db_session() as session:
                from src.database.models.monitoring import PerformanceMetric
                metric = PerformanceMetric(
                    timestamp=datetime.utcnow(),
                    success_rate=stats.get("success_rate"),
                    avg_response_time=stats.get("avg_execution_time"),
                    cpu_usage=stats.get("resource_usage", {}).get("cpu_percent"),
                    memory_usage=stats.get("resource_usage", {}).get("memory_percent"),
                    cache_hit_rate=stats.get("cache_hit_rate"),
                    total_operations=stats.get("total_operations"),
                    raw_data=stats  # JSON
                )
                session.add(metric)
                await session.commit()
        except Exception as e:
            logger.error(f"Failed to log metrics: {e}")

    def _check_alert_conditions(self, stats: Dict) -> List[Dict]:
        """Check conditions and return alerts."""
        alerts = []

        # Response time
        avg_time = stats.get("avg_execution_time", 0)
        if avg_time > self.alert_thresholds["response_time"]:
            alerts.append({
                "type": "response_time",
                "severity": "high" if avg_time > self.alert_thresholds["response_time"] * 2 else "medium",
                "message": f"High response time: {avg_time:.2f}s (threshold: {self.alert_thresholds['response_time']}s)"
            })

        # Memory
        memory = stats.get("resource_usage", {}).get("memory_percent", 0)
        if memory > self.alert_thresholds["memory_usage"]:
            alerts.append({
                "type": "memory",
                "severity": "critical" if memory > 90 else "high",
                "message": f"High memory usage: {memory:.1f}% (threshold: {self.alert_thresholds['memory_usage']}%)"
            })

        # CPU
        cpu = stats.get("resource_usage", {}).get("cpu_percent", 0)
        if cpu > self.alert_thresholds["cpu_usage"]:
            alerts.append({
                "type": "cpu",
                "severity": "high",
                "message": f"High CPU usage: {cpu:.1f}% (threshold: {self.alert_thresholds['cpu_usage']}%)"
            })

        # Cache
        cache_rate = stats.get("cache_hit_rate", 100)
        if cache_rate < self.alert_thresholds["cache_hit_rate"]:
            alerts.append({
                "type": "cache",
                "severity": "medium",
                "message": f"Low cache hit rate: {cache_rate:.1f}% (threshold: {self.alert_thresholds['cache_hit_rate']}%)"
            })

        return alerts

    async def _send_enhanced_alerts(self, alerts: List[Dict], stats: Dict):
        """Send alerts to admin channels with embed."""
        try:
            # Get admin channel from config
            admin_channel_id = self.config.get("admin_alert_channel")
            if not admin_channel_id:
                logger.warning("No admin channel configured for alerts")
                return

            channel = self.bot.get_channel(int(admin_channel_id))
            if not channel:
                logger.warning(f"Admin channel {admin_channel_id} not found")
                return

            embed = discord.Embed(
                title="🚨 Performance Alert",
                description="System performance thresholds exceeded",
                color=discord.Color.red(),
                timestamp=datetime.utcnow()
            )

            for alert in alerts:
                embed.add_field(
                    name=f"{alert['severity'].title()} Alert: {alert['type']}",
                    value=alert['message'],
                    inline=False
                )

            # Add current stats summary
            embed.add_field(
                name="Current Stats",
                value=f"Success Rate: {stats.get('success_rate', 0):.1f}%\n"
                      f"Avg Response: {stats.get('avg_execution_time', 0):.2f}s",
                inline=True
            )

            embed.set_footer(text="Automated alert from Performance Monitor")
            await channel.send(embed=embed)

            logger.warning(f"Sent {len(alerts)} performance alerts")

        except Exception as e:
            logger.error(f"Failed to send alerts: {e}")


async def setup(bot):
    """Setup enhanced performance monitor."""
    await bot.add_cog(PerformanceMonitor(bot))
    logger.info("✅ Enhanced Performance Monitor cog loaded with DB and trends")