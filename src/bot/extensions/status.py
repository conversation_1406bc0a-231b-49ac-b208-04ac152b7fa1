"""
Status Command Extension - Enhanced Version

Bot health and system status with comprehensive monitoring integration.
Enhanced with new bot_monitor for detailed uptime, service health, user activity,
database connection status, and performance snapshots. Includes historical uptime
and alert status from core monitoring.
"""
import discord
from discord.ext import commands
from discord import app_commands
from datetime import datetime, timedelta
import asyncio

from src.shared.error_handling.logging import get_logger
from src.core.error_handling.fallback import handle_error_with_fallback
from src.bot.core.error_handler import log_and_notify_error
from src.bot.permissions import Permission<PERSON><PERSON><PERSON>, DiscordPermissionChecker
from src.core.monitoring_pkg.bot_monitor import BotMonitor  # New monitor
from src.core.monitoring.health_monitor import HealthMonitor  # System health
from src.database.unified_db import get_db_connection_status  # DB status
from src.shared.services.performance_monitor import get_current_performance  # Perf snapshot

logger = get_logger(__name__)


class StatusCommand(commands.Cog):
    """Enhanced status command for comprehensive bot health monitoring."""

    def __init__(self, bot: commands.Bot):
        self.bot = bot
        self.permission_checker = DiscordPermissionChecker()
        self.bot_monitor = BotMonitor(bot)  # New integration
        self.health_monitor = HealthMonitor()
        logger.info("✅ Enhanced Status extension initialized")

    @app_commands.command(name="status", description="Comprehensive bot and system status check")
    @app_commands.describe(
        detailed="Include detailed service health and performance metrics",
        uptime_period="Uptime period (24h, 7d, 30d)"
    )
    async def status_command(self, interaction: discord.Interaction, detailed: Optional[bool] = False, uptime_period: Optional[str] = "24h"):
        """Enhanced status with monitoring integration."""
        try:
            # Permission check (admin only for detailed)
            if detailed and not self.permission_checker.has_permission(interaction.user, PermissionLevel.ADMIN):
                await interaction.response.send_message(
                    "❌ Detailed status requires admin access.",
                    ephemeral=True
                )
                return

            await interaction.response.defer(ephemeral=detailed)  # Ephemeral for detailed

            # Basic bot stats
            latency = round(self.bot.latency * 1000, 2)
            guild_count = len(self.bot.guilds)
            user_count = sum(guild.member_count for guild in self.bot.guilds if guild.member_count is not None)
            active_guilds = len([g for g in self.bot.guilds if g.member_count and g.member_count > 0])

            # Enhanced with monitor
            monitor_stats = await handle_error_with_fallback(
                self.bot_monitor.get_status_metrics,
                default={"uptime": "Unknown", "active_users": 0, "message_rate": 0}
            )

            # System health
            system_health = await self.health_monitor.get_system_health()
            db_status = await get_db_connection_status()

            # Uptime calculation
            uptime_str = await self._calculate_uptime(uptime_period)

            # Create embed
            color = discord.Color.green() if system_health.get("overall_status") == "healthy" else discord.Color.orange()
            embed = discord.Embed(
                title="🤖 Enhanced Bot Status Dashboard",
                description="Comprehensive health and performance overview",
                color=color,
                timestamp=datetime.utcnow()
            )

            # Basic Bot Info
            embed.add_field(
                name="🏓 Connectivity",
                value=f"**Websocket Latency**: {latency}ms\n"
                      f"**API Response**: {monitor_stats.get('api_latency', 'N/A')}ms",
                inline=True
            )

            embed.add_field(
                name="🌐 Presence",
                value=f"**Servers**: {guild_count} ({active_guilds} active)\n"
                      f"**Users**: {user_count:,}\n"
                      f"**Active Users (1h)**: {monitor_stats.get('active_users', 0):,}",
                inline=True
            )

            # Uptime
            embed.add_field(
                name="⏱️ Uptime",
                value=uptime_str,
                inline=True
            )

            # Service Health
            services_status = self._get_enhanced_service_status(detailed)
            embed.add_field(
                name="🔧 Services Health",
                value=services_status,
                inline=False
            )

            # Database
            db_emoji = "🟢" if db_status.get("connected") else "🔴"
            embed.add_field(
                name=f"{db_emoji} Database",
                value=f"**Status**: {db_status.get('status', 'Unknown')}\n"
                      f"**Connections**: {db_status.get('active_connections', 0)}/{db_status.get('max_connections', 0)}\n"
                      f"**Last Query**: {db_status.get('last_query_time', 'N/A')}",
                inline=True
            )

            # Performance Snapshot if detailed
            if detailed:
                perf_snapshot = await get_current_performance()
                perf_emoji = "🟢" if perf_snapshot.get("health_score", 0) > 80 else "🟡"
                embed.add_field(
                    name=f"{perf_emoji} Performance Snapshot",
                    value=f"**Health Score**: {perf_snapshot.get('health_score', 0)}/100\n"
                          f"**Load Average**: {perf_snapshot.get('load_avg', 'N/A')}\n"
                          f"**Memory**: {perf_snapshot.get('memory_usage', 0):.1f}%\n"
                          f"**Recent Errors**: {perf_snapshot.get('error_rate_5min', 0):.2f}%",
                    inline=False
                )

                # Active alerts
                active_alerts = await self.bot_monitor.get_active_alerts()
                alert_count = len(active_alerts)
                alert_status = f"{alert_count} active alerts" if alert_count > 0 else "No active alerts"
                embed.add_field(
                    name="🚨 Monitoring Alerts",
                    value=alert_status,
                    inline=True
                )

            # Message rate and activity
            msg_rate = monitor_stats.get("message_rate", 0)
            embed.add_field(
                name="💬 Activity",
                value=f"**Messages/min**: {msg_rate}\n"
                      f"**Commands/min**: {monitor_stats.get('command_rate', 0)}\n"
                      f"**Error Rate**: {monitor_stats.get('error_rate', 0):.2f}%",
                inline=True
            )

            embed.set_footer(text="Status generated with real-time monitoring | Detailed view enabled" if detailed else "Basic status | Use /status detailed for more info")
            await interaction.followup.send(embed=embed)

        except Exception as e:
            await log_and_notify_error(
                e, interaction,
                "Error generating status",
                fallback_message="❌ Status check failed. Bot is online but services may be impacted.",
                ephemeral=detailed
            )

    async def _calculate_uptime(self, period: str) -> str:
        """Calculate uptime for period."""
        uptime_data = await handle_error_with_fallback(
            self.bot_monitor.get_uptime,
            default={"uptime_hours": 0, "downtime_events": 0}
        )

        if period == "24h":
            uptime_pct = uptime_data.get("uptime_24h", 100)
            downtime = uptime_data.get("downtime_24h_minutes", 0)
        elif period == "7d":
            uptime_pct = uptime_data.get("uptime_7d", 100)
            downtime = uptime_data.get("downtime_7d_hours", 0)
        elif period == "30d":
            uptime_pct = uptime_data.get("uptime_30d", 100)
            downtime = uptime_data.get("downtime_30d_days", 0)
        else:
            uptime_pct = 100
            downtime = 0

        uptime_emoji = "🟢" if uptime_pct > 99 else "🟡" if uptime_pct > 95 else "🔴"
        return f"{uptime_emoji} **{uptime_pct:.2f}%** uptime\nDowntime: {downtime} {'min' if period=='24h' else 'h' if period=='7d' else 'd'}"

    def _get_enhanced_service_status(self, detailed: bool) -> str:
        """Get service status with new monitor."""
        if not detailed:
            return "🟢 All core services operational (detailed view for breakdown)"

        # Use bot_monitor for detailed
        service_statuses = self.bot_monitor.get_service_statuses()
        status_lines = []
        for service, status in service_statuses.items():
            emoji = "🟢" if status.get("healthy") else "🟡" if status.get("degraded") else "🔴"
            details = status.get("details", "OK")
            status_lines.append(f"{emoji} **{service.replace('_', ' ').title()}**: {details}")

        return "\n".join(status_lines) if status_lines else "No service details available"


async def setup(bot: commands.Bot):
    """Setup enhanced status extension."""
    await bot.add_cog(StatusCommand(bot))
    logger.info("✅ Enhanced Status cog loaded with monitoring integration")