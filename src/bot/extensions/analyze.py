"""
Async Analyze Command Module

Implements an enhanced /analyze command with asynchronous data fetching
for improved performance and responsiveness.
"""

import discord
from discord import app_commands
from discord.ext import commands
import asyncio
import logging
from typing import Optional, Dict, Any, List
from datetime import datetime
import hashlib

from src.shared.error_handling.logging import get_logger
from src.bot.permissions import PermissionLevel, require_permission
from src.bot.utils.input_sanitizer import InputSanitizer
from src.bot.pipeline.commands.analyze.parallel_pipeline import execute_parallel_analyze_pipeline
from src.bot.utils.disclaimer_manager import add_disclaimer
from src.shared.ai_services.smart_model_router import router

logger = get_logger(__name__)

class AsyncAnalyzeCommands(commands.Cog):
    """Enhanced analyze commands with async data fetching"""
    
    def __init__(self, bot):
        self.bot = bot
        self.permission_checker = getattr(bot, 'permission_checker', None)

        # Maximum number of concurrent analyses
        self.max_concurrent_analyses = 3
        self.semaphore = asyncio.Semaphore(self.max_concurrent_analyses)

        # Smart caching for analysis results
        self.cache = {}
        self.cache_timestamps = {}

    def _get_cache_key(self, symbol: str, timeframe: str) -> str:
        """Generate cache key for analysis results"""
        return hashlib.md5(f"{symbol}:{timeframe}".encode()).hexdigest()

    def _is_cache_valid(self, cache_key: str, intent: str = "technical_analysis") -> bool:
        """Check if cached result is still valid using smart caching policy"""
        if cache_key not in self.cache_timestamps:
            return False

        # Get smart cache configuration (only pass intent parameter)
        cache_config = router.get_cache_config_for_intent(intent)
        ttl_seconds = cache_config.get('ttl_seconds', 180)  # Default 3 minutes for analysis

        import time
        age = time.time() - self.cache_timestamps[cache_key]
        return age < ttl_seconds

    def _cache_response(self, cache_key: str, response: str, intent: str = "technical_analysis"):
        """Cache response using smart caching policy"""
        cache_config = router.get_cache_config_for_intent(intent)
        should_cache = cache_config.get('enabled', True) and not cache_config.get('bypass', False)

        if should_cache:
            import time
            self.cache[cache_key] = response
            self.cache_timestamps[cache_key] = time.time()
            logger.info(f"Cached analysis result with TTL {cache_config.get('ttl_seconds', 180)}s")
    
    @app_commands.command(name="analyze", description="Get comprehensive multi-timeframe analysis for a stock with deep technical insights")
    @app_commands.describe(
        symbol="Stock symbol to analyze (e.g., AAPL, MSFT, TSLA)"
    )
    async def analyze_command(
        self,
        interaction: discord.Interaction,
        symbol: str
    ):
        """
        Get comprehensive multi-timeframe analysis for a stock with deep technical insights

        This command automatically performs:
        - Multi-timeframe technical analysis (1d, 1w, 1m, 3m, 6m, 1y)
        - Comprehensive indicator analysis (RSI, MACD, Bollinger Bands, etc.)
        - Support/resistance levels across timeframes
        - Volume analysis and momentum indicators
        - Risk assessment and volatility analysis
        - Price targets and trend analysis

        Parameters:
        -----------
        symbol: str
            Stock symbol to analyze
        """
        # Check if user has paid access
        has_permission, reason = self.permission_checker.has_permission(
            interaction.user, PermissionLevel.PAID, None, str(interaction.guild_id) if interaction.guild_id else None
        )
        
        if not has_permission:
            await interaction.response.send_message(
                f"❌ This command requires paid tier access: {reason}"
            )
            return
        
        # Sanitize symbol using unified approach
        from src.shared.utils.symbol_extraction import validate_symbol_format
        sanitized_symbol, is_valid, error_message = validate_symbol_format(symbol)
        if not is_valid:
            from src.shared.utils.discord_helpers import DiscordMessageHelper
            await DiscordMessageHelper.safe_send_message(
                interaction, f"❌ Invalid symbol: {error_message}", ephemeral=True
            )
            return
        
        # Defer the response to avoid timeout
        await interaction.response.defer(thinking=True)
        
        # Check cache first (using multi-timeframe cache key)
        cache_key = self._get_cache_key(sanitized_symbol, "multi_timeframe")
        if self._is_cache_valid(cache_key):
            cached_response = self.cache.get(cache_key)
            if cached_response:
                logger.info(f"Returning cached multi-timeframe analysis for {sanitized_symbol}")
                from src.shared.utils.discord_helpers import DiscordMessageHelper
                await interaction.followup.send(cached_response)
                return

        # Use semaphore to limit concurrent analyses
        async with self.semaphore:
            try:
                logger.info(f"Starting comprehensive multi-timeframe analysis for {sanitized_symbol} requested by {interaction.user}")

                # Send initial response
                await interaction.followup.send(
                    f"🔍 **Comprehensive Analysis for {sanitized_symbol}**\n"
                    f"📊 Running multi-timeframe analysis across 6 timeframes...\n"
                    f"🔬 Performing deep technical analysis with all indicators...\n"
                    f"📈 Calculating support/resistance levels and price targets...\n\n"
                    f"⏳ This comprehensive analysis may take 30-60 seconds. Please wait..."
                )
                
                # Log pipeline execution start
                logger.info(f"Executing analysis pipeline for {sanitized_symbol}")
                
                # Execute the enhanced multi-timeframe analysis pipeline
                pipeline_kwargs = {
                    'ticker': sanitized_symbol,
                    'analysis_type': 'comprehensive_multi_timeframe',
                    'timeframes': ['1d', '1w', '1m', '3m', '6m', '1y'],
                    'include_all_indicators': True,
                    'include_support_resistance': True,
                    'include_volume_analysis': True,
                    'include_risk_assessment': True,
                    'include_price_targets': True,
                    'user_id': str(interaction.user.id),
                    'guild_id': str(interaction.guild.id) if interaction.guild else None,
                }
                
                # Execute the pipeline
                try:
                    context = await execute_parallel_analyze_pipeline(**pipeline_kwargs)
                except Exception as pipeline_error:
                    logger.error(f"Pipeline execution failed for {sanitized_symbol}: {pipeline_error}", exc_info=True)
                    raise
                
                # Log pipeline completion
                logger.info(f"Analysis pipeline completed for {sanitized_symbol}")
                
                # Get the response from the context (handle both dict and object types)
                if hasattr(context, 'processing_results'):
                    response = context.processing_results.get('response', '')
                elif isinstance(context, dict):
                    response = context.get('response', 'Analysis completed successfully.')
                else:
                    response = 'Analysis completed successfully.'

                # Log response details
                logger.info(f"Generated response for {sanitized_symbol} (length: {len(response)} chars)")
                
                # Add disclaimer
                response = add_disclaimer(response, {
                    'command': 'analyze',
                    'symbol': sanitized_symbol
                })

                # Cache the response using smart caching policy
                self._cache_response(cache_key, response)

                # Send the response with length enforcement
                from src.shared.utils.discord_helpers import DiscordMessageHelper
                await interaction.followup.send(response)
            except asyncio.TimeoutError:
                error_msg = "⏰ The analysis took longer than expected. Please try again later."
                logger.error(f"Timeout in analyze command for {sanitized_symbol}: {error_msg}")
                await interaction.followup.send(error_msg)
            except Exception as e:
                import traceback
                error_traceback = traceback.format_exc()
                logger.error(f"Error in analyze command for {sanitized_symbol}: {e}\n{error_traceback}")
                
                # Log additional context
                logger.error(f"User: {interaction.user} (ID: {interaction.user.id})")
                logger.error(f"Guild: {interaction.guild.name if interaction.guild else 'DM'} (ID: {interaction.guild.id if interaction.guild else 'N/A'})")
                logger.error(f"Time: {datetime.utcnow().isoformat()}")
                
                # Send a more detailed error message to the user
                error_msg = (
                    f"❌ An error occurred while processing your request.\n"
                    f"Error: {str(e)}\n\n"
                    "Please try again later or contact support if the issue persists."
                )
                
                try:
                    await interaction.followup.send(error_msg)
                except Exception as send_error:
                    logger.error(f"Failed to send error message to user: {send_error}")
                
                # Re-raise the exception to ensure it's caught by Discord's error handler
                raise


class MultiSymbolAnalyzeCommands(commands.Cog):
    """Multi-symbol analyze commands for comparing multiple stocks"""
    
    def __init__(self, bot):
        self.bot = bot
        self.permission_checker = getattr(bot, 'permission_checker', None)
        
        # Maximum number of symbols to analyze at once
        self.max_symbols = 5
        
        # Maximum number of concurrent analyses
        self.max_concurrent_analyses = 3
        self.semaphore = asyncio.Semaphore(self.max_concurrent_analyses)
    
    @app_commands.command(name="compare", description="Compare technical analysis for multiple stocks")
    @app_commands.describe(
        symbols="Comma-separated list of stock symbols to compare (max 5)",
        timeframe="Timeframe for analysis (e.g., 1d, 1w, 1m)"
    )
    @app_commands.choices(
        timeframe=[
            app_commands.Choice(name="1 Day", value="1d"),
            app_commands.Choice(name="1 Week", value="1w"),
            app_commands.Choice(name="1 Month", value="1m"),
            app_commands.Choice(name="3 Months", value="3m")
        ]
    )
    async def compare_command(
        self, 
        interaction: discord.Interaction, 
        symbols: str,
        timeframe: Optional[str] = "1d"
    ):
        """
        Compare technical analysis for multiple stocks
        
        Parameters:
        -----------
        symbols: str
            Comma-separated list of stock symbols to compare (max 5)
        timeframe: str, optional
            Timeframe for analysis (e.g., 1d, 1w, 1m)
        """
        # Check if user has paid access
        has_permission, reason = self.permission_checker.has_permission(
            interaction.user, PermissionLevel.PAID, None, str(interaction.guild_id) if interaction.guild_id else None
        )
        
        if not has_permission:
            await interaction.response.send_message(
                f"❌ This command requires paid tier access: {reason}"
            )
            return
        
        # Parse and validate symbols
        symbol_list = [s.strip() for s in symbols.split(',') if s.strip()]
        
        if not symbol_list:
            await interaction.response.send_message(
                "❌ Please provide at least one valid symbol."
            )
            return
        
        if len(symbol_list) > self.max_symbols:
            await interaction.response.send_message(
                f"❌ You can compare a maximum of {self.max_symbols} symbols at once. "
                f"You provided {len(symbol_list)} symbols."
            )
            return
        
        # Sanitize symbols
        sanitized_symbols = []
        invalid_symbols = []
        
        for symbol in symbol_list:
            sanitized, is_valid, error_message = InputSanitizer.sanitize_symbol(symbol)
            if is_valid:
                sanitized_symbols.append(sanitized)
            else:
                invalid_symbols.append(f"{symbol} ({error_message})")
        
        if invalid_symbols:
            await interaction.response.send_message(
                f"❌ The following symbols are invalid: {', '.join(invalid_symbols)}"
            )
            return
        
        # Defer response to allow for processing time
        await interaction.response.defer(thinking=True)
        
        try:
            # Send initial message
            await interaction.followup.send(
                f"🔍 Comparing {len(sanitized_symbols)} symbols: {', '.join(sanitized_symbols)}..."
            )
            
            # Process each symbol in parallel with concurrency control
            tasks = []
            
            for symbol in sanitized_symbols:
                # Create a task for analyzing this symbol
                task = asyncio.create_task(
                    self._analyze_symbol(
                        symbol,
                        str(interaction.user.id),
                        str(interaction.guild_id) if interaction.guild_id else None,
                        timeframe
                    )
                )
                tasks.append((symbol, task))
            
            # Wait for all tasks to complete
            results = {}
            for symbol, task in tasks:
                try:
                    # Wait for task with timeout
                    context = await asyncio.wait_for(task, timeout=60.0)
                    results[symbol] = {
                        "status": "completed",
                        "context": context
                    }
                except asyncio.TimeoutError:
                    logger.warning(f"Analysis timed out for {symbol}")
                    results[symbol] = {
                        "status": "timeout",
                        "error": "Analysis timed out"
                    }
                except Exception as e:
                    logger.error(f"Error analyzing {symbol}: {e}")
                    results[symbol] = {
                        "status": "error",
                        "error": str(e)
                    }
            
            # Ensure timeframe is not None
            if timeframe is None:
                timeframe = "1d"  # Default to 1 day if not specified
                
            # Generate comparison report
            comparison_report = self._generate_comparison_report(sanitized_symbols, results, timeframe)
            
            # Add disclaimer
            comparison_report = add_disclaimer(comparison_report, {
                'command': 'compare',
                'symbols': sanitized_symbols
            })
            
            # Send the response
            await interaction.followup.send(comparison_report)
            
        except Exception as e:
            logger.error(f"Error in compare command: {e}")
            await interaction.followup.send(
                "❌ I encountered an error while comparing these symbols. Please try again later."
            )
    
    async def _analyze_symbol(
        self,
        symbol: str,
        user_id: str,
        guild_id: Optional[str],
        timeframe: str
    ):
        """
        Analyze a single symbol with concurrency control
        
        Args:
            symbol: The symbol to analyze
            user_id: Discord user ID
            guild_id: Discord guild ID
            timeframe: Timeframe for analysis
            
        Returns:
            PipelineContext with analysis results
        """
        async with self.semaphore:
            logger.info(f"Analyzing {symbol} with timeframe {timeframe}")
            
            # Execute parallel analyze pipeline
            return await execute_parallel_analyze_pipeline(
                ticker=symbol,
                user_id=user_id,
                guild_id=guild_id,
                correlation_id=f"compare_{symbol}_{datetime.now().timestamp()}"
            )
    
    def _generate_comparison_report(
        self,
        symbols: List[str],
        results: Dict[str, Dict[str, Any]],
        timeframe: str
    ) -> str:
        """
        Generate a comparison report for multiple symbols
        
        Args:
            symbols: List of symbols
            results: Analysis results for each symbol
            timeframe: Timeframe used for analysis
            
        Returns:
            Comparison report text
        """
        # Create header
        header = f"# Technical Analysis Comparison ({timeframe})\n\n"
        
        # Add summary table
        header += "## Summary\n\n"
        header += "| Symbol | Price | Change % | RSI | Trend | Signal |\n"
        header += "|--------|-------|----------|-----|-------|--------|\n"
        
        for symbol in symbols:
            result = results.get(symbol, {})
            
            if result.get("status") == "completed":
                context = result.get("context")
                
                if context and hasattr(context, "processing_results"):
                    # Extract data from context
                    market_data = context.processing_results.get("market_data", {})
                    technical_analysis = context.processing_results.get("technical_analysis", {})
                    
                    # Extract key metrics
                    price = market_data.get('current_price', 'N/A')
                    if isinstance(price, (int, float)):
                        price = f"${price:.2f}"
                        
                    change = market_data.get('change_percent', 'N/A')
                    if isinstance(change, (int, float)):
                        change_str = f"{change:.2f}%"
                        if change > 0:
                            change_str = f"🟢 +{change_str}"
                        elif change < 0:
                            change_str = f"🔴 {change_str}"
                    else:
                        change_str = "N/A"
                        
                    rsi = technical_analysis.get('rsi', 'N/A')
                    if isinstance(rsi, (int, float)):
                        rsi_str = f"{rsi:.1f}"
                    else:
                        rsi_str = "N/A"
                        
                    trend = technical_analysis.get('trend', 'N/A')
                    signal = technical_analysis.get('signal', 'N/A')
                    
                    # Add row to table
                    header += f"| ${symbol} | {price} | {change_str} | {rsi_str} | {trend} | {signal} |\n"
                else:
                    header += f"| ${symbol} | N/A | N/A | N/A | N/A | N/A |\n"
            else:
                error = result.get("error", "Unknown error")
                header += f"| ${symbol} | ❌ Error | {error} | - | - | - |\n"
        
        # Add individual analysis sections
        body = "\n## Individual Analysis\n\n"
        
        for symbol in symbols:
            result = results.get(symbol, {})
            
            if result.get("status") == "completed":
                context = result.get("context")
                
                if context and hasattr(context, "processing_results"):
                    # Extract key data
                    technical_analysis = context.processing_results.get("technical_analysis", {})
                    price_targets = context.processing_results.get("price_targets", {})
                    enhanced_analysis = context.processing_results.get("enhanced_analysis", {})
                    
                    body += f"### ${symbol}\n\n"
                    
                    # Technical indicators
                    body += "**Technical Indicators:**\n"
                    body += f"- RSI: {technical_analysis.get('rsi', 'N/A')}\n"
                    body += f"- Trend: {technical_analysis.get('trend', 'N/A')}\n"
                    body += f"- MACD: {technical_analysis.get('macd', 'N/A')}\n"
                    body += f"- Volatility: {technical_analysis.get('volatility', 'N/A')}\n\n"
                    
                    # Price targets
                    body += "**Price Targets:**\n"
                    body += f"- Short-term: ${price_targets.get('short_term', 'N/A')}\n"
                    body += f"- Medium-term: ${price_targets.get('medium_term', 'N/A')}\n"
                    body += f"- Long-term: ${price_targets.get('long_term', 'N/A')}\n\n"
                    
                    # Support/Resistance
                    support_levels = technical_analysis.get('support_levels', [])
                    resistance_levels = technical_analysis.get('resistance_levels', [])
                    
                    if support_levels:
                        body += f"**Support Levels:** {', '.join([f'${level:.2f}' for level in support_levels[:3]])}\n"
                    
                    if resistance_levels:
                        body += f"**Resistance Levels:** {', '.join([f'${level:.2f}' for level in resistance_levels[:3]])}\n"
                    
                    body += "\n"
            else:
                error = result.get("error", "Unknown error")
                body += f"### ${symbol}\n\n"
                body += f"❌ Error: {error}\n\n"
        
        # Add comparison insights
        insights = "\n## Comparison Insights\n\n"
        
        # Count trends
        trends = {}
        for symbol in symbols:
            result = results.get(symbol, {})
            if result.get("status") == "completed":
                context = result.get("context")
                if context and hasattr(context, "processing_results"):
                    technical_analysis = context.processing_results.get("technical_analysis", {})
                    trend = technical_analysis.get('trend', 'unknown')
                    trends[trend] = trends.get(trend, 0) + 1
        
        # Add trend summary
        insights += "**Trend Distribution:**\n"
        for trend, count in trends.items():
            insights += f"- {trend.capitalize()}: {count} symbol(s)\n"
        
        insights += "\n"
        
        # Add RSI comparison
        insights += "**RSI Comparison:**\n"
        rsi_values = {}
        for symbol in symbols:
            result = results.get(symbol, {})
            if result.get("status") == "completed":
                context = result.get("context")
                if context and hasattr(context, "processing_results"):
                    technical_analysis = context.processing_results.get("technical_analysis", {})
                    rsi = technical_analysis.get('rsi')
                    if isinstance(rsi, (int, float)):
                        rsi_values[symbol] = rsi
        
        if rsi_values:
            # Sort by RSI
            sorted_rsi = sorted(rsi_values.items(), key=lambda x: x[1])
            
            # Add RSI comparison
            for symbol, rsi in sorted_rsi:
                status = "Oversold" if rsi < 30 else "Overbought" if rsi > 70 else "Neutral"
                insights += f"- ${symbol}: {rsi:.1f} ({status})\n"
        
        # Combine all sections
        return header + body + insights


async def setup(bot):
    """Add the async analyze commands to the bot"""
    await bot.add_cog(AsyncAnalyzeCommands(bot))
    await bot.add_cog(MultiSymbolAnalyzeCommands(bot))
