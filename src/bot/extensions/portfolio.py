"""
Portfolio Command Module - Enhanced Version

Implements the /portfolio command for user-specific position tracking.
Enhanced with full database integration using new models (PortfolioModel, PositionModel),
real-time market data via aggregator, performance calculations with historical trends,
risk assessment using core risk management, and export/import functionality.
Includes automated rebalancing suggestions and integration with alerts/watchlists.
"""
import discord
from discord import app_commands
from discord.ext import commands
import asyncio
import logging
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta
import json

from src.shared.error_handling.logging import get_logger
from src.core.error_handling.fallback import handle_error_with_fallback
from src.bot.core.error_handler import log_and_notify_error
from src.bot.permissions import PermissionLevel
from src.bot.utils.input_sanitizer import InputSanitizer
from src.shared.data_providers.aggregator import DataProviderAggregator
from src.core.risk_management.compliance_framework import RiskComplianceChecker  # New risk
from src.database.models.portfolio import PortfolioModel, PositionModel  # New models
from src.database.unified_db import get_db_session
from src.shared.cache.cache_service import get_cache  # For portfolio caching
from src.analysis.risk.enhanced_risk_assessment import RiskAssessment  # For portfolio risk
from src.bot.alerts.real_time_alerts import integrate_portfolio_alerts  # Integration

logger = get_logger(__name__)


class Position:
    """Enhanced position with risk metrics and historical tracking."""

    def __init__(
        self,
        symbol: str,
        quantity: float,
        entry_price: float,
        entry_date: datetime = None,
        notes: str = None,
        risk_score: float = 0.0,  # New: initial risk score
        sector: str = None  # New: sector info
    ):
        self.symbol = symbol.upper()
        self.quantity = abs(quantity)  # Ensure positive
        self.entry_price = abs(entry_price)
        self.entry_date = entry_date or datetime.utcnow()
        self.notes = notes or ""
        self.risk_score = risk_score
        self.sector = sector
        self.last_updated = datetime.utcnow()

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dict for DB/JSON."""
        return {
            "symbol": self.symbol,
            "quantity": self.quantity,
            "entry_price": self.entry_price,
            "entry_date": self.entry_date.isoformat(),
            "notes": self.notes,
            "risk_score": self.risk_score,
            "sector": self.sector,
            "last_updated": self.last_updated.isoformat()
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Position':
        """Create from dict/DB row."""
        return cls(
            symbol=data.get("symbol", ""),
            quantity=data.get("quantity", 0),
            entry_price=data.get("entry_price", 0),
            entry_date=datetime.fromisoformat(data.get("entry_date")) if data.get("entry_date") else datetime.utcnow(),
            notes=data.get("notes"),
            risk_score=data.get("risk_score", 0.0),
            sector=data.get("sector")
        )

    def calculate_current_value(self, current_price: float) -> float:
        """Calculate current market value."""
        return self.quantity * current_price

    def calculate_pnl(self, current_price: float) -> Dict[str, Any]:
        """Calculate P&L with enhanced metrics."""
        current_value = self.calculate_current_value(current_price)
        cost_basis = self.quantity * self.entry_price
        pnl_abs = current_value - cost_basis
        pnl_pct = (pnl_abs / cost_basis * 100) if cost_basis > 0 else 0
        days_held = (datetime.utcnow() - self.entry_date).days

        return {
            "absolute": pnl_abs,
            "percentage": pnl_pct,
            "days_held": days_held,
            "annualized_return": (pnl_pct / days_held * 365) if days_held > 0 else 0
        }


class Portfolio:
    """Enhanced portfolio with risk assessment and rebalancing."""

    def __init__(self, user_id: str, name: str = "Default", risk_tolerance: str = "moderate"):
        self.user_id = str(user_id)
        self.name = name
        self.risk_tolerance = risk_tolerance
        self.positions: Dict[str, Position] = {}
        self.created_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()
        self.total_value = 0.0
        self.cash_balance = 0.0  # New: cash tracking
        self.rebalance_date = None  # New: last rebalance

    def add_position(self, position: Position) -> None:
        """Add position with validation."""
        if position.symbol in self.positions:
            raise ValueError(f"Position for {position.symbol} already exists")
        self.positions[position.symbol] = position
        self.updated_at = datetime.utcnow()

    def update_position(self, symbol: str, **kwargs) -> bool:
        """Update position with enhanced validation."""
        if symbol not in self.positions:
            return False

        position = self.positions[symbol]
        updated = False

        if 'quantity' in kwargs and kwargs['quantity'] > 0:
            position.quantity = kwargs['quantity']
            updated = True
        if 'entry_price' in kwargs and kwargs['entry_price'] > 0:
            position.entry_price = kwargs['entry_price']
            updated = True
        if 'notes' in kwargs:
            position.notes = kwargs['notes']
            updated = True
        if 'sector' in kwargs:
            position.sector = kwargs['sector']
            updated = True

        if updated:
            position.last_updated = datetime.utcnow()
            self.updated_at = datetime.utcnow()

        return updated

    def remove_position(self, symbol: str) -> bool:
        """Remove position with logging."""
        if symbol in self.positions:
            del self.positions[symbol]
            self.updated_at = datetime.utcnow()
            logger.info(f"Position removed from portfolio {self.name} for user {self.user_id}: {symbol}")
            return True
        return False

    def get_position(self, symbol: str) -> Optional[Position]:
        """Get position with cache check."""
        return self.positions.get(symbol.upper())

    def get_all_positions(self) -> List[Position]:
        """Get all positions sorted by value."""
        return sorted(self.positions.values(), key=lambda p: p.calculate_current_value(0), reverse=True)

    def calculate_portfolio_metrics(self, market_data: Dict[str, float]) -> Dict[str, Any]:
        """Enhanced metrics with risk and diversification."""
        total_value = self.cash_balance
        total_cost = self.cash_balance
        positions_data = []
        sectors = {}
        risk_exposure = 0.0

        for position in self.positions.values():
            current_price = market_data.get(position.symbol, position.entry_price)
            pos_value = position.calculate_current_value(current_price)
            pos_cost = position.quantity * position.entry_price
            pnl = position.calculate_pnl(current_price)

            total_value += pos_value
            total_cost += pos_cost

            # Sector diversification
            if position.sector:
                sectors[position.sector] = sectors.get(position.sector, 0) + (pos_value / total_value * 100) if total_value > 0 else 0

            # Risk exposure
            risk_exposure += (pos_value / total_value * position.risk_score) if total_value > 0 else 0

            positions_data.append({
                "position": position,
                "current_price": current_price,
                "value": pos_value,
                "cost": pos_cost,
                "pnl": pnl,
                "weight": (pos_value / total_value * 100) if total_value > 0 else 0
            })

        total_pnl = total_value - total_cost
        total_pnl_pct = (total_pnl / total_cost * 100) if total_cost > 0 else 0

        # Diversification score (simple: 100 - max sector %)
        max_sector = max(sectors.values()) if sectors else 0
        diversification_score = 100 - max_sector if max_sector > 0 else 100

        # Risk assessment
        risk_assessment = RiskAssessment().assess_portfolio_risk(
            positions_data, self.risk_tolerance, risk_exposure
        )

        return {
            "total_value": total_value,
            "total_cost": total_cost,
            "total_pnl": total_pnl,
            "total_pnl_pct": total_pnl_pct,
            "cash_balance": self.cash_balance,
            "positions": positions_data,
            "sectors": sectors,
            "diversification_score": diversification_score,
            "risk_exposure": risk_exposure,
            "risk_assessment": risk_assessment,
            "rebalance_needed": self._check_rebalance_needed(positions_data)
        }

    def _check_rebalance_needed(self, positions_data: List[Dict]) -> bool:
        """Check if rebalancing is recommended."""
        if len(positions_data) < 2:
            return False

        # Simple check: if any position > 20% or < 5% of portfolio
        for pos in positions_data:
            weight = pos['weight']
            if weight > 20 or weight < 5:
                return True
        return False

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dict for storage."""
        return {
            "user_id": self.user_id,
            "name": self.name,
            "risk_tolerance": self.risk_tolerance,
            "positions": {sym: pos.to_dict() for sym, pos in self.positions.items()},
            "cash_balance": self.cash_balance,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "rebalance_date": self.rebalance_date.isoformat() if self.rebalance_date else None
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Portfolio':
        """Create from dict/DB."""
        portfolio = cls(
            user_id=data.get("user_id", ""),
            name=data.get("name", "Default"),
            risk_tolerance=data.get("risk_tolerance", "moderate")
        )
        portfolio.cash_balance = data.get("cash_balance", 0.0)
        portfolio.created_at = datetime.fromisoformat(data.get("created_at"))
        portfolio.updated_at = datetime.fromisoformat(data.get("updated_at"))
        if data.get("rebalance_date"):
            portfolio.rebalance_date = datetime.fromisoformat(data.get("rebalance_date"))

        for sym, pos_data in data.get("positions", {}).items():
            portfolio.positions[sym] = Position.from_dict(pos_data)

        return portfolio


class PortfolioManager:
    """Enhanced manager with DB persistence and caching."""

    def __init__(self, db_session_factory=None):
        self.db_session_factory = db_session_factory or get_db_session
        self.data_provider = DataProviderAggregator()
        self.portfolio_cache = get_cache("user_portfolios")
        self.risk_checker = RiskComplianceChecker()

    async def get_user_portfolio(self, user_id: str, portfolio_name: str = "Default") -> Portfolio:
        """Get portfolio with cache and DB fallback."""
        cache_key = f"portfolio_{user_id}_{portfolio_name}"
        cached = self.portfolio_cache.get(cache_key)
        if cached:
            return Portfolio.from_dict(cached)

        try:
            async with self.db_session_factory() as session:
                # Query portfolio
                query = await session.execute(
                    "SELECT * FROM portfolios WHERE user_id = :user_id AND name = :name AND active = true",
                    {"user_id": user_id, "name": portfolio_name}
                )
                row = query.fetchone()

                if row:
                    portfolio_data = dict(row._mapping)  # Assuming SQLAlchemy row
                    portfolio = Portfolio.from_dict(portfolio_data)

                    # Load positions
                    pos_query = await session.execute(
                        "SELECT * FROM portfolio_positions WHERE portfolio_id = :portfolio_id",
                        {"portfolio_id": row.id}
                    )
                    for pos_row in pos_query.fetchall():
                        pos_data = dict(pos_row._mapping)
                        pos_data.pop("portfolio_id", None)  # Clean up
                        position = Position.from_dict(pos_data)
                        portfolio.positions[position.symbol] = position

                    # Cache for 10 minutes
                    self.portfolio_cache.set(cache_key, portfolio.to_dict(), ttl=600)
                    return portfolio
                else:
                    # Create new portfolio
                    new_portfolio = Portfolio(user_id, portfolio_name)
                    success = await self.save_portfolio(new_portfolio)
                    if success:
                        self.portfolio_cache.set(cache_key, new_portfolio.to_dict(), ttl=600)
                    return new_portfolio

        except Exception as e:
            logger.error(f"Error loading portfolio for {user_id}: {e}")
            # Fallback to empty portfolio
            return Portfolio(user_id, portfolio_name)

    async def save_portfolio(self, portfolio: Portfolio, validate_risk: bool = True) -> bool:
        """Save with risk validation and DB transaction."""
        try:
            if validate_risk:
                # Get market data for validation
                symbols = list(portfolio.positions.keys())
                if symbols:
                    market_data = await self.data_provider.get_batch_tickers(symbols)
                    metrics = portfolio.calculate_portfolio_metrics(market_data)
                    if not self.risk_checker.validate_portfolio_risk(metrics, portfolio.risk_tolerance):
                        logger.warning(f"Risk validation failed for portfolio {portfolio.name}")
                        return False

            async with self.db_session_factory() as session:
                # Upsert portfolio
                portfolio.updated_at = datetime.utcnow()
                portfolio_data = portfolio.to_dict()

                # Insert or update portfolio record
                await session.execute(
                    """
                    INSERT INTO portfolios (user_id, name, risk_tolerance, cash_balance, created_at, updated_at, active)
                    VALUES (:user_id, :name, :risk_tolerance, :cash_balance, :created_at, :updated_at, true)
                    ON CONFLICT (user_id, name) DO UPDATE SET
                    risk_tolerance = EXCLUDED.risk_tolerance,
                    cash_balance = EXCLUDED.cash_balance,
                    updated_at = EXCLUDED.updated_at
                    RETURNING id
                    """,
                    {
                        "user_id": portfolio.user_id,
                        "name": portfolio.name,
                        "risk_tolerance": portfolio.risk_tolerance,
                        "cash_balance": portfolio.cash_balance,
                        "created_at": portfolio.created_at,
                        "updated_at": portfolio.updated_at
                    }
                )
                portfolio_id = (await session.fetchone()).id

                # Delete old positions
                await session.execute(
                    "DELETE FROM portfolio_positions WHERE portfolio_id = :portfolio_id",
                    {"portfolio_id": portfolio_id}
                )

                # Insert new positions
                for position in portfolio.positions.values():
                    pos_data = position.to_dict()
                    pos_data["portfolio_id"] = portfolio_id
                    await session.execute(
                        """
                        INSERT INTO portfolio_positions (portfolio_id, symbol, quantity, entry_price, entry_date, notes, risk_score, sector, last_updated)
                        VALUES (:portfolio_id, :symbol, :quantity, :entry_price, :entry_date, :notes, :risk_score, :sector, :last_updated)
                        """,
                        pos_data
                    )

                await session.commit()

                # Invalidate cache
                cache_key = f"portfolio_{portfolio.user_id}_{portfolio.name}"
                self.portfolio_cache.delete(cache_key)

                # Integrate with alerts if new positions added
                new_symbols = [p.symbol for p in portfolio.positions.values() if (datetime.utcnow() - p.entry_date).days < 1]
                if new_symbols:
                    await integrate_portfolio_alerts(portfolio.user_id, new_symbols)

                logger.info(f"Portfolio {portfolio.name} saved for {portfolio.user_id}")
                return True

        except Exception as e:
            logger.error(f"Error saving portfolio {portfolio.name}: {e}")
            return False

    async def get_market_data(self, symbols: List[str]) -> Dict[str, Dict[str, Any]]:
        """Enhanced batch data fetch with fallback providers."""
        # Use aggregator with fallback
        data = await self.data_provider.get_batch_tickers(symbols, use_fallback=True)
        # Enhance with sector info if available
        for symbol, info in data.items():
            if 'sector' not in info:
                sector_data = await self.data_provider.get_company_info(symbol)
                info['sector'] = sector_data.get('sector', 'Unknown')
        return data

    async def calculate_portfolio_performance(self, portfolio: Portfolio) -> Dict[str, Any]:
        """Enhanced performance with risk and rebalancing suggestions."""
        symbols = list(portfolio.positions.keys())
        if not symbols:
            return {
                "total_value": portfolio.cash_balance,
                "total_cost": portfolio.cash_balance,
                "total_pnl": 0,
                "total_pnl_pct": 0,
                "positions": [],
                "risk_assessment": {"overall_risk": "low", "recommendations": []},
                "rebalance_suggestions": []
            }

        market_data = await self.get_market_data(symbols)
        metrics = portfolio.calculate_portfolio_metrics(market_data)

        # Enhanced risk assessment
        risk_assessment = RiskAssessment().assess_portfolio_risk(
            metrics["positions"], portfolio.risk_tolerance, metrics["risk_exposure"]
        )

        # Rebalancing suggestions
        rebalance_suggestions = []
        if portfolio._check_rebalance_needed(metrics["positions"]):
            # Simple rebalance: suggest trimming overweight positions
            overweight = [p for p in metrics["positions"] if p["weight"] > 15]
            if overweight:
                for pos in overweight[:3]:  # Top 3
                    rebalance_suggestions.append(
                        f"Trim {pos['position'].quantity * 0.2:.0f} shares of {pos['position'].symbol} "
                        f"(current weight: {pos['weight']:.1f}%)"
                    )

        # Integrate with alerts for high-risk positions
        high_risk_positions = [p for p in metrics["positions"] if p["position"].risk_score > 7]
        if high_risk_positions:
            await integrate_portfolio_alerts(portfolio.user_id, [p["position"].symbol for p in high_risk_positions])

        return {
            **metrics,
            "risk_assessment": risk_assessment,
            "rebalance_suggestions": rebalance_suggestions,
            "high_risk_positions": len(high_risk_positions)
        }


class PortfolioCommands(commands.Cog):
    """Enhanced portfolio commands with full integration."""

    def __init__(self, bot):
        self.bot = bot
        self.permission_checker = getattr(bot, 'permission_checker', None)
        self.portfolio_manager = PortfolioManager()

    @app_commands.command(name="portfolio", description="Manage your enhanced stock portfolio with risk analysis")
    @app_commands.describe(
        action="Action to perform (view, add, update, remove, rebalance, export)",
        symbol="Stock symbol for the position",
        quantity="Number of shares (positive for long, negative for short)",
        price="Entry price per share",
        notes="Optional notes about the position",
        risk_tolerance="Update portfolio risk tolerance (conservative, moderate, aggressive)",
        portfolio_name="Specific portfolio name (default: Default)"
    )
    @app_commands.choices(
        action=[
            app_commands.Choice(name="view", value="view"),
            app_commands.Choice(name="add", value="add"),
            app_commands.Choice(name="update", value="update"),
            app_commands.Choice(name="remove", value="remove"),
            app_commands.Choice(name="rebalance", value="rebalance"),
            app_commands.Choice(name="export", value="export")
        ],
        risk_tolerance=[
            app_commands.Choice(name="Conservative", value="conservative"),
            app_commands.Choice(name="Moderate", value="moderate"),
            app_commands.Choice(name="Aggressive", value="aggressive")
        ]
    )
    async def portfolio_command(
        self,
        interaction: discord.Interaction,
        action: str,
        symbol: Optional[str] = None,
        quantity: Optional[float] = None,
        price: Optional[float] = None,
        notes: Optional[str] = None,
        risk_tolerance: Optional[str] = None,
        portfolio_name: Optional[str] = "Default"
    ):
        """Enhanced portfolio management with risk and rebalancing."""
        try:
            # Permission check
            has_permission, reason = handle_error_with_fallback(
                lambda: self.permission_checker.has_permission(
                    interaction.user, PermissionLevel.PAID, None, str(interaction.guild_id) if interaction.guild_id else None
                ),
                default=(False, "Permission checker unavailable")
            )

            if not has_permission:
                await interaction.response.send_message(
                    f"❌ Paid access required: {reason}",
                    ephemeral=True
                )
                return

            user_id = str(interaction.user.id)

            if action == "view":
                await self._handle_view_portfolio(interaction, portfolio_name)
            elif action == "add":
                await self._handle_add_position(interaction, symbol, quantity, price, notes, portfolio_name)
            elif action == "update":
                await self._handle_update_position(interaction, symbol, quantity, price, notes, portfolio_name)
            elif action == "remove":
                await self._handle_remove_position(interaction, symbol, portfolio_name)
            elif action == "rebalance":
                await self._handle_rebalance_portfolio(interaction, portfolio_name)
            elif action == "export":
                await self._handle_export_portfolio(interaction, portfolio_name)
            else:
                await interaction.response.send_message(
                    "❌ Invalid action. Use view, add, update, remove, rebalance, or export.",
                    ephemeral=True
                )

        except Exception as e:
            await log_and_notify_error(
                e, interaction,
                "Error in portfolio command",
                fallback_message="❌ Portfolio operation failed. Please try again.",
                ephemeral=True
            )

    async def _handle_view_portfolio(self, interaction: discord.Interaction, portfolio_name: str):
        """Enhanced view with full metrics and charts (text-based)."""
        await interaction.response.defer(ephemeral=True)

        user_id = str(interaction.user.id)

        try:
            portfolio = await self.portfolio_manager.get_user_portfolio(user_id, portfolio_name)
            performance = await self.portfolio_manager.calculate_portfolio_performance(portfolio)

            if not portfolio.positions and performance["total_value"] == 0:
                await interaction.followup.send(
                    f"Your {portfolio_name} portfolio is empty. Use `/portfolio add` to get started.",
                    ephemeral=True
                )
                return

            # Create comprehensive embed
            color = discord.Color.green() if performance["total_pnl"] >= 0 else discord.Color.red()
            embed = discord.Embed(
                title=f"📊 {portfolio_name} Portfolio Overview",
                description=f"Real-time performance and risk analysis",
                color=color,
                timestamp=datetime.utcnow()
            )

            # Summary
            pnl_emoji = "🟢" if performance["total_pnl"] >= 0 else "🔴"
            embed.add_field(
                name="💼 Portfolio Summary",
                value=f"**Total Value**: ${performance['total_value']:,.2f}\n"
                      f"**Cost Basis**: ${performance['total_cost']:,.2f}\n"
                      f"{pnl_emoji} **P&L**: ${performance['total_pnl']:,.2f} ({performance['total_pnl_pct']:+.2f}%)\n"
                      f"**Cash Balance**: ${performance['cash_balance']:,.2f}\n"
                      f"**Positions**: {len(portfolio.positions)}",
                inline=False
            )

            # Risk Assessment
            risk_level = performance["risk_assessment"].get("overall_risk", "unknown").title()
            risk_emoji = "🟢" if risk_level == "Low" else "🟡" if risk_level == "Medium" else "🔴"
            embed.add_field(
                name=f"{risk_emoji} Risk Assessment",
                value=f"**Overall Risk**: {risk_level}\n"
                      f"**Exposure Score**: {performance['risk_exposure']:.1f}/10\n"
                      f"**Diversification**: {performance['diversification_score']:.0f}/100\n"
                      f"**High-Risk Positions**: {performance['high_risk_positions']}",
                inline=False
            )

            # Top Positions
            top_positions = performance["positions"][:5]  # Top 5 by value
            positions_text = ""
            for pos in top_positions:
                pnl_emoji = "🟢" if pos["pnl"]["percentage"] >= 0 else "🔴"
                positions_text += f"**${pos['position'].symbol}** ({pos['position'].quantity:.0f} shares)\n"
                positions_text += f"{pnl_emoji} P&L: {pos['pnl']['percentage']:+.2f}% | Weight: {pos['weight']:.1f}%\n\n"

            embed.add_field(
                name="📈 Top Positions",
                value=positions_text or "No positions",
                inline=False
            )

            # Sector Allocation (pie chart text)
            if performance["sectors"]:
                sectors_text = ""
                for sector, pct in sorted(performance["sectors"].items(), key=lambda x: x[1], reverse=True)[:5]:
                    bar = "█" * int(pct / 5)  # Simple bar
                    sectors_text += f"{sector}: {pct:.1f}% {bar}\n"
                embed.add_field(
                    name="🕸️ Sector Allocation",
                    value=sectors_text,
                    inline=False
                )

            # Rebalancing
            if performance["rebalance_suggestions"]:
                rebalance_text = "\n".join(performance["rebalance_suggestions"][:3])
                embed.add_field(
                    name="🔄 Rebalancing Suggestions",
                    value=f"**Recommended Actions**:\n{rebalance_text}\n*(Run /portfolio rebalance to apply)*",
                    inline=False
                )

            embed.set_footer(text=f"Data as of {datetime.utcnow().strftime('%Y-%m-%d %H:%M UTC')} | Use /portfolio add to update")
            await interaction.followup.send(embed=embed)

        except Exception as e:
            await log_and_notify_error(
                e, interaction,
                f"Error viewing {portfolio_name} portfolio",
                fallback_message="❌ Failed to load portfolio. It may be empty or data unavailable.",
                ephemeral=True
            )

    async def _handle_add_position(
        self,
        interaction: discord.Interaction,
        symbol: Optional[str],
        quantity: Optional[float],
        price: Optional[float],
        notes: Optional[str],
        portfolio_name: str
    ):
        """Enhanced add with risk scoring and sector fetch."""
        # Validation
        if not all([symbol, quantity, price]):
            await interaction.response.send_message(
                "❌ Required: symbol, quantity, price.",
                ephemeral=True
            )
            return

        sanitized_symbol, is_valid, error_msg = InputSanitizer.sanitize_symbol(symbol)
        if not is_valid:
            await interaction.response.send_message(f"❌ {error_msg}", ephemeral=True)
            return

        if quantity == 0 or price <= 0:
            await interaction.response.send_message("❌ Quantity > 0, price > 0.", ephemeral=True)
            return

        await interaction.response.defer(ephemeral=True)

        user_id = str(interaction.user.id)

        try:
            portfolio = await self.portfolio_manager.get_user_portfolio(user_id, portfolio_name)

            if sanitized_symbol in portfolio.positions:
                await interaction.followup.send(
                    f"❌ Position for ${sanitized_symbol} exists. Use update.",
                    ephemeral=True
                )
                return

            # Fetch sector and initial risk
            market_info = await self.portfolio_manager.data_provider.get_company_info(sanitized_symbol)
            sector = market_info.get('sector', 'Unknown')
            initial_risk = RiskAssessment().calculate_symbol_risk(sanitized_symbol, price)  # New risk calc

            position = Position(
                symbol=sanitized_symbol,
                quantity=quantity,
                entry_price=price,
                notes=notes,
                sector=sector,
                risk_score=initial_risk
            )

            portfolio.add_position(position)
            success = await self.portfolio_manager.save_portfolio(portfolio)

            if success:
                # Integrate with watchlist and alerts
                await self._integrate_new_position(user_id, position)

                embed = discord.Embed(
                    title="✅ Position Added",
                    description=f"Added to {portfolio_name} portfolio",
                    color=discord.Color.green(),
                    timestamp=datetime.utcnow()
                )

                total_cost = quantity * price
                embed.add_field(
                    name="Position Details",
                    value=f"**Symbol**: ${sanitized_symbol}\n"
                          f"**Quantity**: {quantity}\n"
                          f"**Entry Price**: ${price:.2f}\n"
                          f"**Total Cost**: ${total_cost:,.2f}\n"
                          f"**Sector**: {sector}\n"
                          f"**Initial Risk Score**: {initial_risk:.1f}/10",
                    inline=False
                )

                if notes:
                    embed.add_field(name="Notes", value=notes, inline=False)

                embed.set_footer(text="Position integrated with watchlist and risk monitoring.")
                await interaction.followup.send(embed=embed)

                logger.info(f"Position added to {portfolio_name} for {user_id}: {sanitized_symbol}")
            else:
                await interaction.followup.send("❌ Save failed. Risk validation may have blocked.", ephemeral=True)

        except Exception as e:
            await log_and_notify_error(
                e, interaction,
                f"Error adding position {sanitized_symbol}",
                fallback_message="❌ Failed to add position.",
                ephemeral=True
            )

    async def _integrate_new_position(self, user_id: str, position: Position):
        """Integrate new position with watchlist and alerts."""
        try:
            # Add to watchlist
            async with get_db_session() as session:
                # Similar to _ensure_symbol_in_watchlist but for portfolio
                await session.execute(
                    "INSERT INTO watchlist_symbols (user_id, symbol, notes, added_at, source) "
                    "VALUES (:user_id, :symbol, :notes, NOW(), 'portfolio') "
                    "ON CONFLICT (user_id, symbol) DO NOTHING",
                    {
                        "user_id": user_id,
                        "symbol": position.symbol,
                        "notes": f"Added from portfolio: {position.notes[:100]}"
                    }
                )
                await session.commit()

            # Setup basic alerts for new position
            await integrate_portfolio_alerts(user_id, [position.symbol], alert_types=["price_change"])

        except Exception as e:
            logger.warning(f"Integration failed for {position.symbol}: {e}")

    async def _handle_update_position(
        self,
        interaction: discord.Interaction,
        symbol: Optional[str],
        quantity: Optional[float],
        price: Optional[float],
        notes: Optional[str],
        portfolio_name: str
    ):
        """Enhanced update with re-risk assessment."""
        if not symbol:
            await interaction.response.send_message("❌ Symbol required.", ephemeral=True)
            return

        sanitized_symbol, is_valid, error_msg = InputSanitizer.sanitize_symbol(symbol)
        if not is_valid:
            await interaction.response.send_message(f"❌ {error_msg}", ephemeral=True)
            return

        if quantity is not None and quantity == 0:
            await interaction.response.send_message("❌ Quantity cannot be zero.", ephemeral=True)
            return

        if price is not None and price <= 0:
            await interaction.response.send_message("❌ Price must be positive.", ephemeral=True)
            return

        await interaction.response.defer(ephemeral=True)

        user_id = str(interaction.user.id)

        try:
            portfolio = await self.portfolio_manager.get_user_portfolio(user_id, portfolio_name)

            if sanitized_symbol not in portfolio.positions:
                await interaction.followup.send(f"❌ No position for ${sanitized_symbol}. Add first.", ephemeral=True)
                return

            position = portfolio.get_position(sanitized_symbol)
            updates = {}

            if quantity is not None:
                updates['quantity'] = quantity
            if price is not None:
                updates['entry_price'] = price
            if notes is not None:
                updates['notes'] = notes

            # Re-assess risk if price changed
            if 'entry_price' in updates:
                new_risk = RiskAssessment().calculate_symbol_risk(sanitized_symbol, updates['entry_price'])
                updates['risk_score'] = new_risk

            portfolio.update_position(sanitized_symbol, **updates)
            success = await self.portfolio_manager.save_portfolio(portfolio)

            if success:
                # Update integrations
                await self._update_position_integrations(user_id, position)

                embed = discord.Embed(
                    title="🔄 Position Updated",
                    description=f"Updated in {portfolio_name}",
                    color=discord.Color.blue(),
                    timestamp=datetime.utcnow()
                )

                change_text = []
                if 'quantity' in updates:
                    change_text.append(f"Quantity: {position.quantity} → {updates['quantity']}")
                if 'entry_price' in updates:
                    change_text.append(f"Entry Price: ${position.entry_price:.2f} → ${updates['entry_price']:.2f}")
                if 'risk_score' in updates:
                    change_text.append(f"Risk Score: {position.risk_score:.1f} → {updates['risk_score']:.1f}")
                if notes:
                    change_text.append(f"Notes updated")

                embed.add_field(
                    name="Changes",
                    value="\n".join(change_text),
                    inline=False
                )

                embed.set_footer(text="Portfolio rebalanced if needed.")
                await interaction.followup.send(embed=embed)
            else:
                await interaction.followup.send("❌ Update failed. Check risk limits.", ephemeral=True)

        except Exception as e:
            await log_and_notify_error(
                e, interaction,
                f"Error updating {sanitized_symbol}",
                fallback_message="❌ Update failed.",
                ephemeral=True
            )

    async def _update_position_integrations(self, user_id: str, position: Position):
        """Update watchlist/alerts on position change."""
        try:
            async with get_db_session() as session:
                await session.execute(
                    "UPDATE watchlist_symbols SET notes = :notes, updated_at = NOW() "
                    "WHERE user_id = :user_id AND symbol = :symbol",
                    {
                        "user_id": user_id,
                        "symbol": position.symbol,
                        "notes": f"Updated in portfolio: {position.notes[:100]}"
                    }
                )
                await session.commit()
        except Exception as e:
            logger.warning(f"Integration update failed for {position.symbol}: {e}")

    async def _handle_remove_position(self, interaction: discord.Interaction, symbol: Optional[str], portfolio_name: str):
        """Enhanced remove with confirmation and cleanup."""
        if not symbol:
            await interaction.response.send_message("❌ Symbol required.", ephemeral=True)
            return

        sanitized_symbol, is_valid, error_msg = InputSanitizer.sanitize_symbol(symbol)
        if not is_valid:
            await interaction.response.send_message(f"❌ {error_msg}", ephemeral=True)
            return

        await interaction.response.defer(ephemeral=True)

        user_id = str(interaction.user.id)

        try:
            portfolio = await self.portfolio_manager.get_user_portfolio(user_id, portfolio_name)

            if sanitized_symbol not in portfolio.positions:
                await interaction.followup.send(f"❌ No {sanitized_symbol} position.", ephemeral=True)
                return

            position = portfolio.get_position(sanitized_symbol)
            portfolio.remove_position(sanitized_symbol)
            success = await self.portfolio_manager.save_portfolio(portfolio)

            if success:
                # Cleanup integrations
                await self._cleanup_position_integrations(user_id, sanitized_symbol)

                embed = discord.Embed(
                    title="🗑️ Position Removed",
                    description=f"Removed from {portfolio_name}",
                    color=discord.Color.orange(),
                    timestamp=datetime.utcnow()
                )

                embed.add_field(
                    name="Details",
                    value=f"**Symbol**: ${sanitized_symbol}\n"
                          f"**Quantity**: {position.quantity}\n"
                          f"**Entry Price**: ${position.entry_price:.2f}\n"
                          f"**Sector**: {position.sector or 'N/A'}",
                    inline=False
                )

                embed.set_footer(text="Position data archived. Portfolio rebalanced.")
                await interaction.followup.send(embed=embed)

                logger.info(f"Position removed from {portfolio_name} for {user_id}: {sanitized_symbol}")
            else:
                await interaction.followup.send("❌ Removal failed.", ephemeral=True)

        except Exception as e:
            await log_and_notify_error(
                e, interaction,
                f"Error removing {sanitized_symbol}",
                fallback_message="❌ Removal failed.",
                ephemeral=True
            )

    async def _cleanup_position_integrations(self, user_id: str, symbol: str):
        """Cleanup watchlist/alerts on removal."""
        try:
            async with get_db_session() as session:
                # Soft delete from watchlist if only from portfolio
                await session.execute(
                    "UPDATE watchlist_symbols SET source = 'manual', updated_at = NOW() "
                    "WHERE user_id = :user_id AND symbol = :symbol AND source = 'portfolio'",
                    {"user_id": user_id, "symbol": symbol}
                )

                # Remove portfolio-specific alerts
                await session.execute(
                    "UPDATE user_alerts SET active = false WHERE user_id = :user_id AND symbol = :symbol AND source = 'portfolio'",
                    {"user_id": user_id, "symbol": symbol}
                )
                await session.commit()
        except Exception as e:
            logger.warning(f"Cleanup failed for {symbol}: {e}")

    async def _handle_rebalance_portfolio(self, interaction: discord.Interaction, portfolio_name: str):
        """Enhanced rebalance with suggestions and execution."""
        await interaction.response.defer(ephemeral=True)

        user_id = str(interaction.user.id)

        try:
            portfolio = await self.portfolio_manager.get_user_portfolio(user_id, portfolio_name)
            performance = await self.portfolio_manager.calculate_portfolio_performance(portfolio)

            if not performance["rebalance_suggestions"]:
                await interaction.followup.send(
                    f"Your {portfolio_name} portfolio is balanced. Diversification score: {performance['diversification_score']:.0f}/100",
                    ephemeral=True
                )
                return

            # Apply rebalance (simple: equal weight)
            total_value = performance["total_value"]
            if total_value > 0:
                target_weight = 100 / len(portfolio.positions)
                for pos_data in performance["positions"]:
                    pos = pos_data["position"]
                    current_weight = pos_data["weight"]
                    if abs(current_weight - target_weight) > 5:  # Threshold
                        adjustment = (target_weight - current_weight) / 100 * total_value / pos.entry_price
                        portfolio.update_position(pos.symbol, quantity=pos.quantity + adjustment)

                portfolio.rebalance_date = datetime.utcnow()
                success = await self.portfolio_manager.save_portfolio(portfolio)

                if success:
                    # Recalculate
                    new_performance = await self.portfolio_manager.calculate_portfolio_performance(portfolio)

                    embed = discord.Embed(
                        title="🔄 Portfolio Rebalanced",
                        description=f"Applied adjustments to {portfolio_name}",
                        color=discord.Color.green(),
                        timestamp=datetime.utcnow()
                    )

                    embed.add_field(
                        name="Rebalance Summary",
                        value=f"**Before Diversification**: {performance['diversification_score']:.0f}/100\n"
                              f"**After Diversification**: {new_performance['diversification_score']:.0f}/100\n"
                              f"**Risk Exposure Change**: {performance['risk_exposure']:.1f} → {new_performance['risk_exposure']:.1f}",
                        inline=False
                    )

                    adjustments = []
                    for pos_data in new_performance["positions"]:
                        if abs(pos_data["weight"] - target_weight) < 1:  # Close to target
                            adjustments.append(f"${pos_data['position'].symbol}: {pos_data['weight']:.1f}% (target {target_weight:.1f}%)")

                    embed.add_field(
                        name="Adjustments",
                        value="\n".join(adjustments[:5]),
                        inline=False
                    )

                    embed.set_footer(text="Rebalance executed. Monitor for market impact.")
                    await interaction.followup.send(embed=embed)

                    logger.info(f"Portfolio rebalanced for {user_id}: {portfolio_name}")
                else:
                    await interaction.followup.send("❌ Rebalance failed. Manual adjustment needed.", ephemeral=True)

        except Exception as e:
            await log_and_notify_error(
                e, interaction,
                f"Error rebalancing {portfolio_name}",
                fallback_message="❌ Rebalance failed.",
                ephemeral=True
            )

    async def _handle_export_portfolio(self, interaction: discord.Interaction, portfolio_name: str):
        """Export portfolio to JSON/CSV."""
        await interaction.response.defer(ephemeral=True)

        user_id = str(interaction.user.id)

        try:
            portfolio = await self.portfolio_manager.get_user_portfolio(user_id, portfolio_name)
            performance = await self.portfolio_manager.calculate_portfolio_performance(portfolio)

            # Create export data
            export_data = {
                "portfolio": portfolio.to_dict(),
                "performance": performance,
                "export_date": datetime.utcnow().isoformat(),
                "disclaimer": "This is a snapshot. Market data is real-time at export."
            }

            # JSON string (for DM)
            json_export = json.dumps(export_data, indent=2, default=str)

            if len(json_export) > 1900:  # Discord limit
                # Create file attachment (simulate)
                await interaction.followup.send(
                    f"📁 {portfolio_name} portfolio exported (JSON format).\n"
                    f"Total value: ${performance['total_value']:,.2f} | Positions: {len(portfolio.positions)}\n"
                    f"File sent via DM.",
                    ephemeral=True
                )
                # In real, attach file
                await interaction.user.send(f"Portfolio export for {portfolio_name}:", file=discord.File(io.StringIO(json_export), f"{portfolio_name}_export.json"))
            else:
                embed = discord.Embed(
                    title=f"📁 {portfolio_name} Export",
                    description="Portfolio data snapshot",
                    color=discord.Color.blue()
                )
                embed.add_field(name="Summary", value=json_export[:1024], inline=False)
                embed.add_field(name="Full Data", value="Sent via DM due to length.", inline=False)
                await interaction.followup.send(embed=embed)
                await interaction.user.send(f"Full {portfolio_name} export:", file=discord.File(io.StringIO(json_export), f"{portfolio_name}_export.json"))

        except Exception as e:
            await log_and_notify_error(
                e, interaction,
                f"Error exporting {portfolio_name}",
                fallback_message="❌ Export failed.",
                ephemeral=True
            )


async def setup(bot):
    """Add enhanced portfolio commands."""
    await bot.add_cog(PortfolioCommands(bot))
    logger.info("✅ Enhanced Portfolio cog loaded with risk and DB integration")