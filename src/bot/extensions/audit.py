"""
Audit Extension - Discord commands for audit system management.

This extension provides Discord slash commands for managing the audit system,
including configuration, testing, and monitoring capabilities.
"""

import discord
from discord.ext import commands
from discord import app_commands
from typing import Optional
import asyncio

from src.shared.error_handling.logging import get_logger
from ..audit.audit_service import audit_service, AuditLevel
from ..audit.request_visualizer import request_visualizer
from ..audit.session_manager import audit_session_manager

logger = get_logger(__name__)

class AuditCommands(commands.Cog):
    """Discord commands for audit system management"""
    
    def __init__(self, bot):
        self.bot = bot
    
    @app_commands.command(name="audit_config", description="Configure the audit system (Admin only)")
    @app_commands.describe(
        enabled="Enable or disable the audit system",
        channel_id="Set a Discord channel ID for audit logs",
        use_webhook="Enable or disable webhook logging"
    )
    async def audit_config_command(
        self,
        interaction: discord.Interaction,
        enabled: Optional[bool] = None,
        channel_id: Optional[str] = None,
        use_webhook: Optional[bool] = None
    ):
        """Configure the audit system"""
        # Check if user is an admin
        if not interaction.user.guild_permissions.administrator:
            await interaction.response.send_message(
                "❌ You don't have permission to use this command.",
                ephemeral=True
            )
            return
        
        # Update configuration
        if enabled is not None:
            audit_service.enabled = enabled
            audit_service.log_to_console = enabled
            audit_service.log_to_channel = enabled and audit_service.dev_channel_id is not None
            audit_service.log_to_webhook = enabled and audit_service.webhook_url is not None
            
            request_visualizer.enabled = enabled
            request_visualizer.log_to_console = enabled
            request_visualizer.log_to_channel = enabled and request_visualizer.dev_channel_id is not None
            request_visualizer.log_to_webhook = enabled and request_visualizer.webhook_url is not None
        
        if channel_id:
            try:
                new_channel_id = int(channel_id)
                audit_service.dev_channel_id = new_channel_id
                audit_service.log_to_channel = audit_service.enabled and new_channel_id is not None
                
                request_visualizer.dev_channel_id = new_channel_id
                request_visualizer.log_to_channel = request_visualizer.enabled and new_channel_id is not None
            except ValueError:
                await interaction.response.send_message(
                    "❌ Invalid channel ID. Please provide a valid integer.",
                    ephemeral=True
                )
                return
                
        # Toggle webhook usage
        if use_webhook is not None:
            audit_service.log_to_webhook = use_webhook and audit_service.enabled
            request_visualizer.log_to_webhook = use_webhook and request_visualizer.enabled
            
            # Initialize session if needed
            if audit_service.log_to_webhook and not audit_service._session:
                import aiohttp
                audit_service._session = aiohttp.ClientSession()
        
        # Send confirmation
        await interaction.response.send_message(
            f"✅ Audit system configuration updated:\n"
            f"• Enabled: {audit_service.enabled}\n"
            f"• Developer Channel ID: {audit_service.dev_channel_id or 'Not set'}\n"
            f"• Webhook Logging: {'Enabled' if audit_service.log_to_webhook else 'Disabled'}",
            ephemeral=True
        )
        
        # Log configuration change
        await audit_service.log_audit_event(
            level=AuditLevel.INFO,
            message=f"Audit system configuration updated by {interaction.user.name}",
            data={
                "enabled": audit_service.enabled,
                "dev_channel_id": audit_service.dev_channel_id
            }
        )
    
    @app_commands.command(name="toggle_webhook", description="Toggle webhook logging for dev logs (Admin only)")
    async def toggle_webhook_command(self, interaction: discord.Interaction):
        """Toggle webhook logging for dev logs"""
        # Check if user is an admin
        if not interaction.user.guild_permissions.administrator:
            await interaction.response.send_message(
                "❌ You don't have permission to use this command.",
                ephemeral=True
            )
            return
            
        # Toggle webhook logging
        audit_service.log_to_webhook = not audit_service.log_to_webhook
        request_visualizer.log_to_webhook = not request_visualizer.log_to_webhook
        
        # Initialize session if needed
        if audit_service.log_to_webhook and not audit_service._session:
            import aiohttp
            audit_service._session = aiohttp.ClientSession()
        
        # Send confirmation
        status = "enabled" if audit_service.log_to_webhook else "disabled"
        await interaction.response.send_message(
            f"✅ Webhook logging is now {status}.",
            ephemeral=True
        )
        
        # Log configuration change
        await audit_service.log_audit_event(
            level=AuditLevel.INFO,
            message=f"Webhook logging {status} by {interaction.user.name}",
            data={
                "webhook_enabled": audit_service.log_to_webhook
            }
        )
    
    @app_commands.command(name="test_audit", description="Test the audit system (Admin only)")
    async def test_audit_command(self, interaction: discord.Interaction):
        """Test the audit system"""
        # Check if user is an admin
        if not interaction.user.guild_permissions.administrator:
            await interaction.response.send_message(
                "❌ You don't have permission to use this command.",
                ephemeral=True
            )
            return
        
        # Send test message
        await interaction.response.send_message(
            "🧪 Testing audit system...",
            ephemeral=True
        )
        
        # Log test events at different levels
        for level in AuditLevel:
            await audit_service.log_audit_event(
                level=level,
                message=f"Test audit event at {level.value} level",
                command_name="test_audit",
                data={
                    "test": True,
                    "level": level.value,
                    "timestamp": discord.utils.utcnow().isoformat()
                }
            )
        
        # Send test request visualization
        await request_visualizer.visualize_request(
            interaction=interaction,
            command_name="test_audit",
            args={"test": True},
            correlation_id="test-correlation-id"
        )
        
        # Send test response visualization
        await request_visualizer.visualize_response(
            interaction=interaction,
            command_name="test_audit",
            response_data="Test response data",
            execution_time=0.5,
            correlation_id="test-correlation-id",
            success=True
        )
        
        # Send confirmation
        await interaction.followup.send(
            "✅ Audit system test completed. Check the developer channel for results.",
            ephemeral=True
        )
    
    @app_commands.command(name="audit_status", description="Get audit system status (Admin only)")
    async def audit_status_command(self, interaction: discord.Interaction):
        """Get audit system status"""
        # Check if user is an admin
        if not interaction.user.guild_permissions.administrator:
            await interaction.response.send_message(
                "❌ You don't have permission to use this command.",
                ephemeral=True
            )
            return
        
        # Get session stats
        session_stats = await audit_session_manager.get_session_stats()
        
        # Create status embed
        embed = discord.Embed(
            title="🔍 Audit System Status",
            color=0x00ff00 if audit_service.enabled else 0xff0000,
            timestamp=discord.utils.utcnow()
        )
        
        embed.add_field(
            name="System Status",
            value="✅ Enabled" if audit_service.enabled else "❌ Disabled",
            inline=True
        )
        
        embed.add_field(
            name="Console Logging",
            value="✅ Enabled" if audit_service.log_to_console else "❌ Disabled",
            inline=True
        )
        
        embed.add_field(
            name="Channel Logging",
            value="✅ Enabled" if audit_service.log_to_channel else "❌ Disabled",
            inline=True
        )
        
        embed.add_field(
            name="Webhook Logging",
            value="✅ Enabled" if audit_service.log_to_webhook else "❌ Disabled",
            inline=True
        )
        
        embed.add_field(
            name="Developer Channel",
            value=f"<#{audit_service.dev_channel_id}>" if audit_service.dev_channel_id else "Not set",
            inline=True
        )
        
        embed.add_field(
            name="Webhook URL",
            value="✅ Configured" if audit_service.webhook_url else "❌ Not configured",
            inline=True
        )
        
        embed.add_field(
            name="Active Sessions",
            value=str(session_stats.get("active_sessions", 0)),
            inline=True
        )
        
        embed.add_field(
            name="Total Records",
            value=str(session_stats.get("total_records", 0)),
            inline=True
        )
        
        embed.add_field(
            name="Data Directory",
            value=session_stats.get("data_directory", "Not set"),
            inline=False
        )
        
        await interaction.response.send_message(embed=embed, ephemeral=True)
    
    @app_commands.command(name="audit_cleanup", description="Clean up old audit data (Admin only)")
    async def audit_cleanup_command(self, interaction: discord.Interaction):
        """Clean up old audit data"""
        # Check if user is an admin
        if not interaction.user.guild_permissions.administrator:
            await interaction.response.send_message(
                "❌ You don't have permission to use this command.",
                ephemeral=True
            )
            return
        
        await interaction.response.send_message(
            "🧹 Starting audit data cleanup...",
            ephemeral=True
        )
        
        try:
            # Trigger cleanup
            await audit_session_manager._cleanup_old_sessions()
            
            # Get updated stats
            session_stats = await audit_session_manager.get_session_stats()
            
            await interaction.followup.send(
                f"✅ Audit data cleanup completed.\n"
                f"• Active sessions: {session_stats.get('active_sessions', 0)}\n"
                f"• Total records: {session_stats.get('total_records', 0)}",
                ephemeral=True
            )
            
        except Exception as e:
            await interaction.followup.send(
                f"❌ Error during cleanup: {str(e)}",
                ephemeral=True
            )

async def setup(bot):
    """Setup the audit extension"""
    await bot.add_cog(AuditCommands(bot))
    logger.info("✅ Audit extension loaded")
