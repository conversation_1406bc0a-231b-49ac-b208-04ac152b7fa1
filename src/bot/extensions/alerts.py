"""
Alerts Command Module - Enhanced Version

Implements the /alerts command for real-time notifications via WebSocket integration.
Enhanced with database persistence using new models, improved error handling, and integration
with core alert system for better modularity and reliability.
"""
import discord
from discord import app_commands
from discord.ext import commands
import asyncio
import logging
from typing import Optional, List, Dict, Any
from datetime import datetime

from src.shared.error_handling.logging import get_logger
from src.core.error_handling.fallback import handle_error_with_fallback
from src.bot.core.error_handler import log_and_notify_error
from src.bot.permissions import PermissionLevel
from src.bot.utils.input_sanitizer import InputSanitizer
from src.bot.alerts.real_time_alerts import AlertType, AlertManager  # New core alert system
from src.database.models.alerts import AlertModel  # New database model
from src.database.unified_db import get_db_session  # New DB integration
from src.shared.cache.cache_service import get_cache  # For caching alerts

logger = get_logger(__name__)


class AlertsCommands(commands.Cog):
    """Enhanced alerts commands for real-time market notifications with DB persistence."""

    def __init__(self, bot):
        self.bot = bot
        self.permission_checker = getattr(bot, 'permission_checker', None)
        self.alert_manager = AlertManager()  # Use new core alert manager
        self.db_cache = get_cache("alerts_cache")  # Cache for user alerts

        # Enhanced alert types with descriptions
        self.alert_types = {
            "Price Change": AlertType.PRICE_CHANGE,
            "Volume Spike": AlertType.VOLUME_SPIKE,
            "Technical Signal": AlertType.TECHNICAL_SIGNAL,
            "Support/Resistance": AlertType.SUPPORT_RESISTANCE,
            "Volatility": AlertType.VOLATILITY,
            "Earnings": AlertType.EARNINGS,
            "News": AlertType.NEWS
        }

        # Direction and timeframe choices (unchanged but validated)
        self.directions = ["up", "down", "both"]
        self.timeframes = ["1m", "5m", "15m", "30m", "1h", "4h", "1d", "1w"]

    @app_commands.command(name="alerts", description="Manage price and technical alerts for your watchlist")
    @app_commands.describe(
        action="Action to perform (list, create, update, delete, test)",
        symbol="Stock symbol to set alert for",
        alert_type="Type of alert to create",
        threshold="Alert threshold value (e.g., 5 for 5% price change)",
        direction="Alert trigger direction (up, down, both)",
        timeframe="Timeframe for the alert (e.g., 1d for daily)",
        notify_channel="Discord channel for notifications (optional)"
    )
    @app_commands.choices(
        action=[
            app_commands.Choice(name="list", value="list"),
            app_commands.Choice(name="create", value="create"),
            app_commands.Choice(name="update", value="update"),
            app_commands.Choice(name="delete", value="delete"),
            app_commands.Choice(name="test", value="test")
        ],
        alert_type=[
            app_commands.Choice(name="Price Change", value="price_change"),
            app_commands.Choice(name="Volume Spike", value="volume_spike"),
            app_commands.Choice(name="Technical Signal", value="technical_signal"),
            app_commands.Choice(name="Support/Resistance", value="support_resistance"),
            app_commands.Choice(name="Volatility", value="volatility"),
            app_commands.Choice(name="Earnings", value="earnings"),
            app_commands.Choice(name="News", value="news")
        ],
        direction=[
            app_commands.Choice(name="Up", value="up"),
            app_commands.Choice(name="Down", value="down"),
            app_commands.Choice(name="Both", value="both")
        ],
        timeframe=[
            app_commands.Choice(name="1 Minute", value="1m"),
            app_commands.Choice(name="5 Minutes", value="5m"),
            app_commands.Choice(name="15 Minutes", value="15m"),
            app_commands.Choice(name="30 Minutes", value="30m"),
            app_commands.Choice(name="1 Hour", value="1h"),
            app_commands.Choice(name="4 Hours", value="4h"),
            app_commands.Choice(name="1 Day", value="1d"),
            app_commands.Choice(name="1 Week", value="1w")
        ]
    )
    async def alerts_command(
        self,
        interaction: discord.Interaction,
        action: str,
        symbol: Optional[str] = None,
        alert_type: Optional[str] = None,
        threshold: Optional[float] = None,
        direction: Optional[str] = None,
        timeframe: Optional[str] = None,
        notify_channel: Optional[str] = None  # New: optional channel override
    ):
        """Enhanced alert management with DB persistence and channel notifications."""
        try:
            # Permission check with fallback
            has_permission, reason = handle_error_with_fallback(
                lambda: self.permission_checker.has_permission(
                    interaction.user, PermissionLevel.PAID, None, str(interaction.guild_id) if interaction.guild_id else None
                ),
                default=(False, "Permission checker unavailable")
            )

            if not has_permission:
                await interaction.response.send_message(
                    f"❌ This command requires paid tier access: {reason}",
                    ephemeral=True
                )
                return

            # Validate alert manager availability
            if not self.alert_manager:
                await interaction.response.send_message(
                    "❌ Alert system is not available. Please contact an administrator.",
                    ephemeral=True
                )
                return

            # Handle actions with enhanced validation
            if action == "list":
                await self._handle_list_alerts(interaction, notify_channel)
            elif action == "create":
                await self._handle_create_alert(interaction, symbol, alert_type, threshold, direction, timeframe, notify_channel)
            elif action == "update":
                await self._handle_update_alert(interaction, symbol, alert_type, threshold, direction, timeframe, notify_channel)
            elif action == "delete":
                await self._handle_delete_alert(interaction, symbol, alert_type)
            elif action == "test":
                await self._handle_test_alert(interaction, symbol)
            else:
                await interaction.response.send_message(
                    "❌ Invalid action. Please use list, create, update, delete, or test.",
                    ephemeral=True
                )

        except Exception as e:
            await log_and_notify_error(
                e, interaction,
                "Error in alerts command",
                fallback_message="❌ An error occurred while managing alerts. Please try again.",
                ephemeral=True
            )

    async def _handle_list_alerts(self, interaction: discord.Interaction, notify_channel: Optional[str] = None):
        """Enhanced listing with DB query and caching."""
        await interaction.response.defer(ephemeral=True)

        user_id = str(interaction.user.id)

        try:
            # Get alerts from DB with cache fallback
            alerts = await handle_error_with_fallback(
                lambda: self._get_user_alerts_from_db(user_id),
                default={}
            )

            if not alerts:
                await interaction.followup.send(
                    "You don't have any alerts set up yet. Use `/alerts create` to create one.",
                    ephemeral=True
                )
                return

            # Create enhanced embed
            embed = discord.Embed(
                title="🔔 Your Active Alerts",
                description="Current alert settings with status",
                color=discord.Color.blue(),
                timestamp=datetime.utcnow()
            )

            alert_count = 0
            for symbol, alert_prefs in alerts.items():
                alert_details = []
                for alert_key, settings in alert_prefs.items():
                    if isinstance(settings, dict) and settings.get('enabled', False):
                        base_type = alert_key.replace("_threshold", "")
                        threshold = settings.get('threshold', 0)
                        direction = settings.get('direction', 'both')
                        tf = settings.get('timeframe', '1d')
                        enabled = settings.get('enabled', True)
                        last_triggered = settings.get('last_triggered', 'Never')

                        status = "✅ Active" if enabled else "❌ Disabled"
                        alert_details.append(
                            f"**{base_type.replace('_', ' ').title()}**: {threshold}% ({direction}, {tf}) - {status}\n"
                            f"Last Triggered: {last_triggered}"
                        )
                        alert_count += 1

                if alert_details:
                    embed.add_field(
                        name=f"${symbol}",
                        value="\n".join(alert_details),
                        inline=False
                    )

            embed.add_field(
                name="Total Alerts",
                value=f"{alert_count} active alerts",
                inline=True
            )

            if notify_channel:
                embed.add_field(
                    name="Notification Channel",
                    value=notify_channel,
                    inline=True
                )

            embed.set_footer(text="Use /alerts create to add new alerts | Alerts persist across restarts")
            await interaction.followup.send(embed=embed)

        except Exception as e:
            await log_and_notify_error(
                e, interaction,
                "Error listing alerts",
                fallback_message="❌ Error retrieving alerts. Please try again.",
                ephemeral=True
            )

    async def _get_user_alerts_from_db(self, user_id: str) -> Dict:
        """Fetch user alerts from database with caching."""
        cache_key = f"user_alerts_{user_id}"
        cached = self.db_cache.get(cache_key)
        if cached:
            return cached

        async with get_db_session() as session:
            alerts_query = await session.execute(
                "SELECT symbol, alert_data FROM user_alerts WHERE user_id = :user_id AND active = true",
                {"user_id": user_id}
            )
            db_alerts = alerts_query.fetchall()

            alerts_dict = {}
            for row in db_alerts:
                symbol = row.symbol
                alert_data = row.alert_data  # Assuming JSON stored
                alerts_dict[symbol] = alert_data

            # Cache for 5 minutes
            self.db_cache.set(cache_key, alerts_dict, ttl=300)
            return alerts_dict

    async def _handle_create_alert(
        self,
        interaction: discord.Interaction,
        symbol: Optional[str],
        alert_type: Optional[str],
        threshold: Optional[float],
        direction: Optional[str],
        timeframe: Optional[str],
        notify_channel: Optional[str] = None
    ):
        """Enhanced creation with DB persistence and validation."""
        # Enhanced validation
        if not all([symbol, alert_type, threshold]):
            await interaction.response.send_message(
                "❌ Missing required parameters: symbol, alert_type, and threshold are required.",
                ephemeral=True
            )
            return

        # Sanitize and validate symbol
        sanitized_symbol, is_valid, error_message = InputSanitizer.sanitize_symbol(symbol)
        if not is_valid:
            await interaction.response.send_message(f"❌ Invalid symbol: {error_message}", ephemeral=True)
            return

        # Validate threshold
        if threshold <= 0 or threshold > 100:
            await interaction.response.send_message("❌ Threshold must be between 0 and 100.", ephemeral=True)
            return

        # Set defaults
        direction = direction or "both"
        timeframe = timeframe or "1d"
        if direction not in self.directions:
            direction = "both"
        if timeframe not in self.timeframes:
            timeframe = "1d"

        await interaction.response.defer(ephemeral=True)

        user_id = str(interaction.user.id)
        guild_id = str(interaction.guild_id) if interaction.guild_id else None

        try:
            # Check/add to watchlist with new DB integration
            await self._ensure_symbol_in_watchlist(user_id, sanitized_symbol)

            # Create alert model
            alert_model = AlertModel(
                user_id=user_id,
                guild_id=guild_id,
                symbol=sanitized_symbol,
                alert_type=alert_type,
                threshold=threshold,
                direction=direction,
                timeframe=timeframe,
                notify_channel=notify_channel,
                enabled=True,
                created_at=datetime.utcnow()
            )

            # Save to DB
            async with get_db_session() as session:
                session.add(alert_model)
                await session.commit()

            # Register with core alert manager
            await self.alert_manager.register_alert(alert_model)

            # Cache update
            await self._invalidate_user_alerts_cache(user_id)

            # Enhanced success embed
            embed = discord.Embed(
                title="🔔 Alert Created Successfully",
                description=f"Real-time alert for ${sanitized_symbol} is now active",
                color=discord.Color.green(),
                timestamp=datetime.utcnow()
            )

            embed.add_field(
                name="Alert Configuration",
                value=f"**Type**: {alert_type.replace('_', ' ').title()}\n"
                      f"**Threshold**: {threshold}%\n"
                      f"**Direction**: {direction.title()}\n"
                      f"**Timeframe**: {timeframe}\n"
                      f"**Channel**: {notify_channel or 'Default (DM)'}",
                inline=False
            )

            embed.add_field(
                name="Status",
                value="✅ Active and monitoring\n"
                      "🚀 Integrated with real-time data streams\n"
                      "💾 Persisted in database",
                inline=False
            )

            embed.set_footer(text="You will receive notifications when conditions are met. Alerts auto-restart on bot reboot.")
            await interaction.followup.send(embed=embed)

            logger.info(f"Alert created for user {user_id}: {sanitized_symbol} - {alert_type}")

        except Exception as e:
            await log_and_notify_error(
                e, interaction,
                f"Error creating alert for {sanitized_symbol}",
                fallback_message="❌ Failed to create alert. Please try again.",
                ephemeral=True
            )

    async def _ensure_symbol_in_watchlist(self, user_id: str, symbol: str):
        """Ensure symbol is in user's watchlist using new DB models."""
        try:
            async with get_db_session() as session:
                # Check if symbol exists in watchlist
                watchlist_check = await session.execute(
                    "SELECT id FROM watchlist_symbols WHERE user_id = :user_id AND symbol = :symbol",
                    {"user_id": user_id, "symbol": symbol.upper()}
                )
                if not watchlist_check.fetchone():
                    # Add to default watchlist
                    default_watchlist = await session.execute(
                        "SELECT id FROM user_watchlists WHERE user_id = :user_id AND name = 'Default' LIMIT 1",
                        {"user_id": user_id}
                    )
                    watchlist_id = default_watchlist.fetchone()
                    if not watchlist_id:
                        # Create default watchlist
                        await session.execute(
                            "INSERT INTO user_watchlists (user_id, name) VALUES (:user_id, 'Default')",
                            {"user_id": user_id}
                        )
                        watchlist_result = await session.execute(
                            "SELECT id FROM user_watchlists WHERE user_id = :user_id AND name = 'Default'",
                            {"user_id": user_id}
                        )
                        watchlist_id = watchlist_result.fetchone().id
                    else:
                        watchlist_id = watchlist_id.id

                    await session.execute(
                        "INSERT INTO watchlist_symbols (watchlist_id, symbol, notes, added_at) "
                        "VALUES (:watchlist_id, :symbol, :notes, NOW())",
                        {
                            "watchlist_id": watchlist_id,
                            "symbol": symbol.upper(),
                            "notes": f"Added via alerts on {datetime.utcnow().strftime('%Y-%m-%d')}"
                        }
                    )
                    await session.commit()
        except Exception as e:
            logger.warning(f"Could not add {symbol} to watchlist for {user_id}: {e}")

    async def _invalidate_user_alerts_cache(self, user_id: str):
        """Invalidate cache for user alerts."""
        cache_key = f"user_alerts_{user_id}"
        self.db_cache.delete(cache_key)

    async def _handle_update_alert(
        self,
        interaction: discord.Interaction,
        symbol: Optional[str],
        alert_type: Optional[str],
        threshold: Optional[float],
        direction: Optional[str],
        timeframe: Optional[str],
        notify_channel: Optional[str] = None
    ):
        """Enhanced update with DB update and validation."""
        if not symbol or not alert_type:
            await interaction.response.send_message(
                "❌ Missing required parameters: symbol and alert_type are required.",
                ephemeral=True
            )
            return

        sanitized_symbol, is_valid, error_message = InputSanitizer.sanitize_symbol(symbol)
        if not is_valid:
            await interaction.response.send_message(f"❌ Invalid symbol: {error_message}", ephemeral=True)
            return

        # Validate threshold if provided
        if threshold is not None and (threshold <= 0 or threshold > 100):
            await interaction.response.send_message("❌ Threshold must be between 0 and 100.", ephemeral=True)
            return

        await interaction.response.defer(ephemeral=True)

        user_id = str(interaction.user.id)

        try:
            # Get existing alert from DB
            async with get_db_session() as session:
                alert_query = await session.execute(
                    "SELECT * FROM user_alerts WHERE user_id = :user_id AND symbol = :symbol AND alert_type = :alert_type",
                    {"user_id": user_id, "symbol": sanitized_symbol, "alert_type": alert_type}
                )
                existing_alert = alert_query.fetchone()

                if not existing_alert:
                    await interaction.followup.send(
                        f"❌ No existing alert found for ${sanitized_symbol} with type {alert_type}.",
                        ephemeral=True
                    )
                    return

                # Update alert
                update_data = {"updated_at": datetime.utcnow()}
                if threshold is not None:
                    update_data["threshold"] = threshold
                if direction:
                    update_data["direction"] = direction
                if timeframe:
                    update_data["timeframe"] = timeframe
                if notify_channel:
                    update_data["notify_channel"] = notify_channel

                await session.execute(
                    "UPDATE user_alerts SET " + ", ".join([f"{k} = :{k}" for k in update_data.keys()]) + 
                    " WHERE id = :id",
                    {**update_data, "id": existing_alert.id}
                )
                await session.commit()

                # Update in core manager
                await self.alert_manager.update_alert(existing_alert.id, **update_data)

                # Invalidate cache
                await self._invalidate_user_alerts_cache(user_id)

            # Success embed
            embed = discord.Embed(
                title="🔄 Alert Updated Successfully",
                description=f"Updated alert for ${sanitized_symbol}",
                color=discord.Color.blue(),
                timestamp=datetime.utcnow()
            )

            updates = []
            if threshold is not None:
                updates.append(f"Threshold: {threshold}%")
            if direction:
                updates.append(f"Direction: {direction.title()}")
            if timeframe:
                updates.append(f"Timeframe: {timeframe}")
            if notify_channel:
                updates.append(f"Channel: {notify_channel}")

            embed.add_field(
                name="Updated Settings",
                value="\n".join(updates),
                inline=False
            )

            embed.set_footer(text="Alert updated and will use new settings immediately.")
            await interaction.followup.send(embed=embed)

        except Exception as e:
            await log_and_notify_error(
                e, interaction,
                f"Error updating alert for {sanitized_symbol}",
                fallback_message="❌ Failed to update alert. Please try again.",
                ephemeral=True
            )

    async def _handle_delete_alert(
        self,
        interaction: discord.Interaction,
        symbol: Optional[str],
        alert_type: Optional[str]
    ):
        """Enhanced deletion with DB soft delete and manager unregistration."""
        if not symbol or not alert_type:
            await interaction.response.send_message(
                "❌ Missing required parameters: symbol and alert_type are required.",
                ephemeral=True
            )
            return

        sanitized_symbol, is_valid, error_message = InputSanitizer.sanitize_symbol(symbol)
        if not is_valid:
            await interaction.response.send_message(f"❌ Invalid symbol: {error_message}", ephemeral=True)
            return

        await interaction.response.defer(ephemeral=True)

        user_id = str(interaction.user.id)

        try:
            async with get_db_session() as session:
                # Soft delete (set active=false)
                await session.execute(
                    "UPDATE user_alerts SET active = false, deleted_at = NOW() WHERE "
                    "user_id = :user_id AND symbol = :symbol AND alert_type = :alert_type",
                    {"user_id": user_id, "symbol": sanitized_symbol, "alert_type": alert_type}
                )
                result = await session.execute(
                    "SELECT COUNT(*) as count FROM user_alerts WHERE "
                    "user_id = :user_id AND symbol = :symbol AND alert_type = :alert_type AND active = false",
                    {"user_id": user_id, "symbol": sanitized_symbol, "alert_type": alert_type}
                )
                deleted_count = result.fetchone().count
                await session.commit()

                if deleted_count == 0:
                    await interaction.followup.send(
                        f"❌ No alert found for ${sanitized_symbol} with type {alert_type}.",
                        ephemeral=True
                    )
                    return

                # Unregister from core manager
                await self.alert_manager.unregister_alert_by_user_symbol_type(user_id, sanitized_symbol, alert_type)

                # Invalidate cache
                await self._invalidate_user_alerts_cache(user_id)

            # Success embed
            embed = discord.Embed(
                title="🗑️ Alert Deleted Successfully",
                description=f"Deleted alert for ${sanitized_symbol} ({alert_type})",
                color=discord.Color.red(),
                timestamp=datetime.utcnow()
            )

            embed.add_field(
                name="Details",
                value=f"**Type**: {alert_type.replace('_', ' ').title()}\n"
                      f"**Status**: Removed from monitoring and database",
                inline=False
            )

            embed.set_footer(text="Alert has been permanently removed.")
            await interaction.followup.send(embed=embed)

        except Exception as e:
            await log_and_notify_error(
                e, interaction,
                f"Error deleting alert for {sanitized_symbol}",
                fallback_message="❌ Failed to delete alert. Please try again.",
                ephemeral=True
            )

    async def _handle_test_alert(self, interaction: discord.Interaction, symbol: Optional[str]):
        """Enhanced test with simulated alert trigger."""
        if not symbol:
            await interaction.response.send_message(
                "❌ Missing required parameter: symbol.",
                ephemeral=True
            )
            return

        sanitized_symbol, is_valid, error_message = InputSanitizer.sanitize_symbol(symbol)
        if not is_valid:
            await interaction.response.send_message(f"❌ Invalid symbol: {error_message}", ephemeral=True)
            return

        await interaction.response.defer(ephemeral=True)

        try:
            # Simulate alert trigger using core manager
            test_alert = self.alert_manager.create_test_alert(sanitized_symbol, interaction.user.id)
            await self.alert_manager.trigger_test_notification(test_alert, interaction)

            embed = discord.Embed(
                title="🔔 Test Alert Sent",
                description=f"Test alert for ${sanitized_symbol} has been simulated and sent.",
                color=discord.Color.gold(),
                timestamp=datetime.utcnow()
            )

            embed.add_field(
                name="Simulation Details",
                value="• Used real-time data streams\n"
                      "• Simulated price movement of 5.2%\n"
                      "• Delivered via configured channel/DM",
                inline=False
            )

            embed.set_footer(text="This confirms your alert system is fully operational.")
            await interaction.followup.send(embed=embed)

        except Exception as e:
            await log_and_notify_error(
                e, interaction,
                f"Error testing alert for {sanitized_symbol}",
                fallback_message="❌ Failed to test alert. Please check configuration.",
                ephemeral=True
            )


async def setup(bot):
    """Add the enhanced alerts commands to the bot."""
    await bot.add_cog(AlertsCommands(bot))
    logger.info("✅ Enhanced Alerts cog loaded with DB integration")