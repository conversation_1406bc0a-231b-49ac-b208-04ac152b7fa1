"""
Utility Commands Extension - Enhanced Version

Basic bot utility commands with expanded functionality.
Enhanced with integration to core monitoring for ping/response testing,
database health checks, cache status, and configuration verification.
Includes user diagnostics and system info for troubleshooting.
"""
import discord
from discord.ext import commands
from discord import app_commands
import asyncio
from datetime import datetime
from typing import Optional

from src.shared.error_handling.logging import get_logger
from src.shared.error_handling.fallback import handle_error_with_fallback
from src.bot.core.error_handler import log_and_notify_error
from src.bot.permissions import PermissionLevel
from src.core.monitoring_pkg.bot_monitor import BotMonitor  # New monitor
from src.database.unified_db import get_db_connection_status  # DB check
from src.shared.cache.cache_service import get_cache_stats  # Cache info

logger = get_logger(__name__)


class UtilityCommands(commands.Cog):
    """Enhanced utility commands for diagnostics and basic operations."""

    def __init__(self, bot: commands.Bot):
        self.bot = bot
        self.bot_monitor = BotMonitor(bot)  # Integration
        logger.info("✅ Enhanced Utility commands loaded")

    @app_commands.command(name="ping", description="Check bot latency and API responsiveness")
    @app_commands.describe(
        detailed="Include API and database ping times"
    )
    async def ping_command(self, interaction: discord.Interaction, detailed: Optional[bool] = False):
        """Enhanced ping with multiple latency checks."""
        try:
            # Basic websocket ping
            ws_latency = round(self.bot.latency * 1000, 2)

            # API ping
            api_start = datetime.utcnow()
            api_latency = await handle_error_with_fallback(
                lambda: self.bot_monitor.ping_api(),
                default=999
            )
            api_end = datetime.utcnow()
            api_duration = (api_end - api_start).total_seconds() * 1000

            # DB ping if detailed
            db_latency = "N/A"
            if detailed:
                db_start = datetime.utcnow()
                db_status = await get_db_connection_status()
                db_end = datetime.utcnow()
                db_latency = round((db_end - db_start).total_seconds() * 1000, 2)
                db_healthy = db_status.get("connected", False)

            # Embed response
            color = discord.Color.green() if ws_latency < 100 else discord.Color.orange()
            embed = discord.Embed(
                title="🏓 Pong! Latency Check",
                color=color,
                timestamp=datetime.utcnow()
            )

            embed.add_field(
                name="🌐 Websocket Latency",
                value=f"{ws_latency}ms",
                inline=True
            )

            embed.add_field(
                name="🔌 API Latency",
                value=f"{api_latency}ms (internal: {api_duration:.0f}ms)",
                inline=True
            )

            if detailed:
                db_emoji = "🟢" if db_healthy else "🔴"
                embed.add_field(
                    name=f"{db_emoji} Database Latency",
                    value=f"{db_latency}ms ({'Healthy' if db_healthy else 'Issue'})",
                    inline=True
                )

                # Cache status
                cache_stats = get_cache_stats()
                embed.add_field(
                    name="💾 Cache Status",
                    value=f"Hit Rate: {cache_stats.get('hit_rate', 0):.1f}% | Size: {cache_stats.get('size', 0):,} items",
                    inline=False
                )

            embed.set_footer(text="Low latency indicates optimal performance")
            await interaction.response.send_message(embed=embed)

            logger.debug(f"Ping requested by {interaction.user.display_name}: WS={ws_latency}ms, API={api_latency}ms")

        except Exception as e:
            await log_and_notify_error(
                e, interaction,
                "Error in ping command",
                fallback_message="🏓 Pong! (Basic check only - detailed failed)"
            )

    @app_commands.command(name="test", description="Run comprehensive bot functionality test")
    @app_commands.describe(
        full="Run full system test including DB, API, and services"
    )
    async def test_command(self, interaction: discord.Interaction, full: Optional[bool] = False):
        """Enhanced test with system checks."""
        try:
            await interaction.response.defer(ephemeral=full)

            # Basic test
            basic_embed = discord.Embed(
                title="✅ Basic Test Passed",
                description="Bot is online and responsive.",
                color=discord.Color.green()
            )
            basic_embed.add_field(name="Websocket", value="Connected", inline=True)
            basic_embed.add_field(name="Commands", value="Registered", inline=True)
            basic_embed.add_field(name="User", value=f"{interaction.user.display_name}", inline=True)

            if not full:
                await interaction.followup.send(embed=basic_embed)
                return

            # Full test
            test_results = await asyncio.gather(
                self._test_database(),
                self._test_api_connectivity(),
                self._test_cache(),
                self._test_services(),
                return_exceptions=True
            )

            # Results
            embed = discord.Embed(title="🔍 Full System Test Results", color=discord.Color.blue())
            tests = ["Database", "API Connectivity", "Cache", "Services"]
            for i, result in enumerate(test_results):
                status = "🟢 PASS" if not isinstance(result, Exception) else "🔴 FAIL"
                details = str(result)[:100] if not isinstance(result, Exception) else str(result)[:100]
                embed.add_field(name=f"{tests[i]}: {status}", value=details, inline=True)

            overall_pass = all(not isinstance(r, Exception) for r in test_results)
            embed.color = discord.Color.green() if overall_pass else discord.Color.red()
            embed.set_footer(text="Full test completed" if overall_pass else "Issues detected - check logs")

            await interaction.followup.send(embed=embed)

            logger.info(f"Full test run by {interaction.user.display_name}: {'PASS' if overall_pass else 'FAIL'}")

        except Exception as e:
            await log_and_notify_error(
                e, interaction,
                "Error in test command",
                fallback_message="❌ Test failed - basic functionality may be impaired."
            )

    async def _test_database(self):
        """Test DB connection."""
        status = await get_db_connection_status()
        if status.get("connected"):
            return f"Connected: {status.get('version', 'N/A')}"
        raise Exception(f"DB Issue: {status.get('error', 'Unknown')}")

    async def _test_api_connectivity(self):
        """Test external API."""
        from src.shared.data_providers.aggregator import DataProviderAggregator
        aggregator = DataProviderAggregator()
        test_data = await aggregator.get_ticker("AAPL")
        if test_data and not test_data.get("error"):
            return f"API OK: Fetched {len(test_data)} keys"
        raise Exception("API fetch failed")

    async def _test_cache(self):
        """Test cache operations."""
        test_cache = get_cache("test_cache")
        test_key = f"test_{int(datetime.utcnow().timestamp())}"
        test_cache.set(test_key, "value", ttl=10)
        value = test_cache.get(test_key)
        test_cache.delete(test_key)
        if value == "value":
            stats = get_cache_stats()
            return f"Cache OK: {stats.get('size', 0)} items"
        raise Exception("Cache operation failed")

    async def _test_services(self):
        """Test core services."""
        monitor_stats = await self.bot_monitor.get_service_statuses()
        healthy_count = sum(1 for s in monitor_stats.values() if s.get("healthy"))
        if healthy_count == len(monitor_stats):
            return f"All {healthy_count} services healthy"
        raise Exception(f"{healthy_count}/{len(monitor_stats)} services healthy")

    @app_commands.command(name="diagnostics", description="Run user-specific diagnostics (Admin)")
    async def diagnostics_command(self, interaction: discord.Interaction):
        """User diagnostics for troubleshooting."""
        if not self.permission_checker.has_permission(interaction.user, PermissionLevel.ADMIN):
            await interaction.response.send_message("❌ Admin only.", ephemeral=True)
            return

        await interaction.response.defer(ephemeral=True)

        try:
            user_id = str(interaction.user.id)
            guild_id = str(interaction.guild_id) if interaction.guild_id else None

            # Gather diagnostics
            diag = await self.bot_monitor.get_user_diagnostics(user_id, guild_id)

            embed = discord.Embed(
                title=f"🔍 Diagnostics for {interaction.user.display_name}",
                color=discord.Color.blue(),
                timestamp=datetime.utcnow()
            )

            embed.add_field(
                name="User Activity",
                value=f"Commands Used: {diag.get('command_count', 0)}\n"
                      f"Last Active: {diag.get('last_activity', 'Never')}\n"
                      f"Alerts Active: {diag.get('active_alerts', 0)}",
                inline=True
            )

            embed.add_field(
                name="Permissions",
                value=f"Level: {diag.get('permission_level', 'Unknown')}\n"
                      f"Roles: {', '.join(diag.get('roles', [])) or 'None'}\n"
                      f"Guild: {diag.get('guild_name', 'DM')}",
                inline=True
            )

            embed.add_field(
                name="Data Usage",
                value=f"Watchlist Size: {diag.get('watchlist_size', 0)} symbols\n"
                      f"Portfolio Value: ${diag.get('portfolio_value', 0):,.2f}\n"
                      f"API Calls (24h): {diag.get('api_calls_24h', 0)}",
                inline=True
            )

            # Issues
            issues = diag.get('issues', [])
            if issues:
                issues_text = "\n".join([f"• {issue}" for issue in issues[:5]])
                embed.add_field(name="⚠️ Potential Issues", value=issues_text, inline=False)

            embed.set_footer(text="Run /status detailed for system-wide diagnostics")
            await interaction.followup.send(embed=embed)

        except Exception as e:
            await log_and_notify_error(
                e, interaction,
                "Error in diagnostics",
                fallback_message="❌ Diagnostics unavailable.",
                ephemeral=True
            )


async def setup(bot: commands.Bot):
    """Setup enhanced utilities."""
    await bot.add_cog(UtilityCommands(bot))
    logger.info("✅ Enhanced Utility cog loaded with diagnostics")