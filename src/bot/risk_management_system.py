"""
Risk Management System for Trading Bot

This module provides comprehensive risk management functionality
including position sizing, stop losses, risk limits, and portfolio risk controls.
"""
import asyncio
import logging
from datetime import datetime
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from enum import Enum
import math
from src.shared.error_handling.logging import get_logger

logger = get_logger(__name__)


class RiskLevel(Enum):
    """Risk levels for positions and portfolios."""
    CONSERVATIVE = "conservative"  # Low risk
    MODERATE = "moderate"          # Medium risk
    AGGRESSIVE = "aggressive"      # High risk
    EXCESSIVE = "excessive"        # Too risky


class RiskType(Enum):
    """Types of risks to manage."""
    POSITION_SIZE = "position_size"
    PORTFOLIO_CONCENTRATION = "portfolio_concentration"
    MAX_DRAW_DOWN = "max_draw_down"
    DAILY_LOSS_LIMIT = "daily_loss_limit"
    VOLATILITY = "volatility"


@dataclass
class RiskParameters:
    """Configuration parameters for risk management."""
    max_position_size_percent: float = 2.0  # Max 2% of portfolio per position
    max_portfolio_concentration: float = 10.0  # Max 10% in one sector
    max_daily_loss_percent: float = 5.0  # Max 5% daily loss
    max_drawdown_percent: float = 15.0  # Max 15% drawdown
    volatility_threshold: float = 2.0  # Volatility multiplier threshold
    risk_free_rate: float = 0.02  # Annual risk-free rate for Sharpe ratio


@dataclass
class PositionRisk:
    """Risk assessment for a single position."""
    symbol: str
    position_size: float
    portfolio_percentage: float
    volatility: float
    risk_score: float  # 0.0 to 1.0
    risk_level: RiskLevel
    max_loss_at_stake: float
    stop_loss_price: Optional[float] = None


@dataclass
class PortfolioRisk:
    """Risk assessment for the entire portfolio."""
    total_value: float
    cash_percentage: float
    concentration_score: float  # 0.0 to 1.0
    volatility_score: float  # 0.0 to 1.0
    drawdown_risk: float  # 0.0 to 1.0
    risk_score: float  # Overall 0.0 to 1.0
    risk_level: RiskLevel
    max_loss_at_stake: float


class RiskManagementSystem:
    """Manages risk across trading positions and portfolio."""
    
    def __init__(self, risk_params: Optional[RiskParameters] = None):
        self.risk_params = risk_params or RiskParameters()
        self._logger = get_logger("risk_management")
        self._portfolio_positions: Dict[str, Dict[str, Any]] = {}
        self._daily_losses: Dict[str, float] = {}
        self._portfolio_value_history: List[Dict[str, Any]] = []
    
    def set_risk_parameters(self, risk_params: RiskParameters):
        """Update risk management parameters."""
        self.risk_params = risk_params
        self._logger.info("Risk parameters updated")
    
    def update_portfolio_position(self, symbol: str, quantity: float, avg_price: float, current_price: float):
        """Update portfolio position for risk assessment."""
        position_value = quantity * current_price
        self._portfolio_positions[symbol] = {
            'quantity': quantity,
            'avg_price': avg_price,
            'current_price': current_price,
            'value': position_value,
            'timestamp': datetime.now()
        }
    
    def calculate_position_risk(self, symbol: str, quantity: float, entry_price: float, 
                               current_price: Optional[float] = None) -> PositionRisk:
        """Calculate risk metrics for a potential or existing position."""
        if current_price is None:
            # If not provided, use the position price from portfolio if available
            pos_data = self._portfolio_positions.get(symbol, {})
            current_price = pos_data.get('current_price', entry_price)
        
        position_value = quantity * entry_price
        portfolio_value = self._get_portfolio_total_value()
        
        # Calculate portfolio percentage
        portfolio_percentage = (position_value / portfolio_value * 100) if portfolio_value > 0 else 0
        
        # Calculate volatility (simplified)
        price_change = abs(current_price - entry_price) / entry_price
        volatility = price_change * 100  # As percentage
        
        # Calculate risk score based on position size vs limits
        risk_score = portfolio_percentage / self.risk_params.max_position_size_percent
        
        # Determine risk level
        if risk_score <= 0.5:
            risk_level = RiskLevel.CONSERVATIVE
        elif risk_score <= 1.0:
            risk_level = RiskLevel.MODERATE
        elif risk_score <= 1.5:
            risk_level = RiskLevel.AGGRESSIVE
        else:
            risk_level = RiskLevel.EXCESSIVE
        
        # Calculate max loss at stake (assuming stop loss at 10% below entry)
        stop_loss_price = entry_price * 0.9  # Default stop loss at 10%
        max_loss = quantity * (entry_price - stop_loss_price)
        
        return PositionRisk(
            symbol=symbol,
            position_size=position_value,
            portfolio_percentage=portfolio_percentage,
            volatility=volatility,
            risk_score=min(risk_score, 1.0),  # Cap at 1.0
            risk_level=risk_level,
            max_loss_at_stake=max_loss,
            stop_loss_price=stop_loss_price
        )
    
    def calculate_portfolio_risk(self) -> PortfolioRisk:
        """Calculate overall portfolio risk."""
        total_value = self._get_portfolio_total_value()
        
        if total_value <= 0:
            return PortfolioRisk(
                total_value=0,
                cash_percentage=100.0,
                concentration_score=0.0,
                volatility_score=0.0,
                drawdown_risk=0.0,
                risk_score=0.0,
                risk_level=RiskLevel.CONSERVATIVE,
                max_loss_at_stake=0.0
            )
        
        # Calculate cash percentage
        total_stock_value = sum(
            pos['value'] for pos in self._portfolio_positions.values()
        )
        cash_percentage = max(0, (total_value - total_stock_value) / total_value * 100)
        
        # Calculate concentration score (simplified - based on largest position)
        if self._portfolio_positions:
            largest_position_value = max(
                pos['value'] for pos in self._portfolio_positions.values()
            )
            concentration_score = largest_position_value / total_value
        else:
            concentration_score = 0.0
        
        # Calculate volatility score (simplified average of position volatilities)
        if self._portfolio_positions:
            volatilities = []
            for symbol, pos in self._portfolio_positions.items():
                entry_price = pos['avg_price']
                current_price = pos['current_price']
                if entry_price > 0:
                    price_volatility = abs(current_price - entry_price) / entry_price
                    volatilities.append(price_volatility)
            
            volatility_score = sum(volatilities) / len(volatilities) if volatilities else 0.0
        else:
            volatility_score = 0.0
        
        # Calculate drawdown risk (simplified)
        drawdown_risk = self._calculate_drawdown_risk()
        
        # Overall risk score (weighted combination)
        risk_score = (
            concentration_score * 0.3 +
            volatility_score * 0.4 +
            drawdown_risk * 0.3
        )
        
        # Determine risk level
        if risk_score <= 0.2:
            risk_level = RiskLevel.CONSERVATIVE
        elif risk_score <= 0.4:
            risk_level = RiskLevel.MODERATE
        elif risk_score <= 0.7:
            risk_level = RiskLevel.AGGRESSIVE
        else:
            risk_level = RiskLevel.EXCESSIVE
        
        # Calculate max potential loss
        max_loss_at_stake = sum(
            abs(pos['quantity'] * (pos['avg_price'] - pos['current_price']))
            for pos in self._portfolio_positions.values()
        )
        
        return PortfolioRisk(
            total_value=total_value,
            cash_percentage=cash_percentage,
            concentration_score=concentration_score,
            volatility_score=volatility_score,
            drawdown_risk=drawdown_risk,
            risk_score=risk_score,
            risk_level=risk_level,
            max_loss_at_stake=max_loss_at_stake
        )
    
    def _get_portfolio_total_value(self) -> float:
        """Get the total value of the portfolio."""
        total_stock_value = sum(
            pos['value'] for pos in self._portfolio_positions.values()
        )
        # For simplicity, assuming cash value is part of the total from another source
        # In a real system, we'd have the cash balance
        return total_stock_value  # This would include cash in a real implementation
    
    def _calculate_drawdown_risk(self) -> float:
        """Calculate drawdown risk based on portfolio history."""
        # Simplified: calculate drawdown risk based on recent volatility
        if len(self._portfolio_value_history) < 2:
            return 0.0
        
        # Calculate volatility of recent portfolio values
        values = [entry['value'] for entry in self._portfolio_value_history[-10:]]  # Last 10 entries
        
        if len(values) < 2:
            return 0.0
        
        # Calculate standard deviation as a measure of volatility/drawdown risk
        mean_val = sum(values) / len(values)
        variance = sum((x - mean_val) ** 2 for x in values) / len(values)
        std_dev = math.sqrt(variance)
        
        # Normalize to 0-1 scale based on mean value
        return min(1.0, std_dev / mean_val if mean_val > 0 else 0)
    
    def is_position_acceptable(self, risk: PositionRisk) -> bool:
        """Check if a position meets risk criteria."""
        checks = {
            'position_size': risk.portfolio_percentage <= self.risk_params.max_position_size_percent,
            'risk_level': risk.risk_level != RiskLevel.EXCESSIVE,
        }
        
        all_passed = all(checks.values())
        
        if not all_passed:
            failed_checks = [check for check, passed in checks.items() if not passed]
            self._logger.warning(f"Position risk check failed for {risk.symbol}: {failed_checks}")
        
        return all_passed
    
    def is_portfolio_risk_acceptable(self, risk: PortfolioRisk) -> bool:
        """Check if portfolio risk is acceptable."""
        checks = {
            'concentration': risk.concentration_score <= self.risk_params.max_portfolio_concentration / 100,
            'overall_risk': risk.risk_score <= 0.8,  # 80% threshold
        }
        
        all_passed = all(checks.values())
        
        if not all_passed:
            failed_checks = [check for check, passed in checks.items() if not passed]
            self._logger.warning(f"Portfolio risk check failed: {failed_checks}")
        
        return all_passed
    
    def calculate_stop_loss(self, entry_price: float, symbol: str, risk_percentage: float = 8.0) -> float:
        """Calculate stop loss price based on risk percentage."""
        return entry_price * (1 - risk_percentage / 100)
    
    def calculate_position_size(self, portfolio_value: float, risk_per_trade: float = 1.0, 
                               entry_price: float = 0.0, stop_loss_price: float = 0.0) -> float:
        """Calculate position size based on risk parameters."""
        if entry_price <= 0 or stop_loss_price <= 0:
            return 0.0
        
        # Calculate risk per share
        risk_per_share = entry_price - stop_loss_price
        
        # Calculate risk amount based on portfolio value and risk percentage
        risk_amount = portfolio_value * (risk_per_trade / 100)
        
        # Calculate position size
        if risk_per_share > 0:
            return risk_amount / risk_per_share
        else:
            return 0.0
    
    def get_risk_recommendation(self, symbol: str, action: str, 
                                quantity: float, price: float) -> Dict[str, Any]:
        """Get risk recommendation for a trade."""
        position_risk = self.calculate_position_risk(symbol, quantity, price)
        portfolio_risk = self.calculate_portfolio_risk()
        
        recommendation = {
            'symbol': symbol,
            'action': action,
            'quantity': quantity,
            'price': price,
            'position_risk': {
                'risk_score': position_risk.risk_score,
                'risk_level': position_risk.risk_level.value,
                'portfolio_percentage': position_risk.portfolio_percentage,
                'max_loss_at_stake': position_risk.max_loss_at_stake
            },
            'portfolio_risk': {
                'risk_score': portfolio_risk.risk_score,
                'risk_level': portfolio_risk.risk_level.value,
                'total_value': portfolio_risk.total_value
            },
            'is_acceptable': (
                self.is_position_acceptable(position_risk) and 
                self.is_portfolio_risk_acceptable(portfolio_risk)
            ),
            'suggested_quantity': quantity,  # May be adjusted based on risk
            'recommendation': 'proceed' if (
                self.is_position_acceptable(position_risk) and 
                self.is_portfolio_risk_acceptable(portfolio_risk)
            ) else 'review'
        }
        
        return recommendation
    
    def calculate_var(self, confidence_level: float = 0.95, lookback_days: int = 30) -> float:
        """Calculate Value at Risk (VaR)."""
        # Simplified VaR calculation
        if len(self._portfolio_value_history) < lookback_days:
            return 0.0
        
        # Get recent returns
        recent_values = self._portfolio_value_history[-lookback_days:]
        returns = []
        
        for i in range(1, len(recent_values)):
            if recent_values[i-1]['value'] > 0:
                return_val = (recent_values[i]['value'] - recent_values[i-1]['value']) / recent_values[i-1]['value']
                returns.append(return_val)
        
        if not returns:
            return 0.0
        
        # Sort returns to find VaR
        returns_sorted = sorted(returns)
        var_index = int((1 - confidence_level) * len(returns_sorted))
        var_index = max(0, min(var_index, len(returns_sorted) - 1))
        
        # Get current portfolio value
        current_value = self._get_portfolio_total_value()
        var_loss = abs(returns_sorted[var_index]) * current_value
        
        return var_loss


# Global risk management system instance
risk_management_system = RiskManagementSystem()


def get_risk_recommendation(symbol: str, action: str, quantity: float, price: float) -> Dict[str, Any]:
    """Convenience function to get risk recommendation."""
    return risk_management_system.get_risk_recommendation(symbol, action, quantity, price)


def calculate_position_risk(symbol: str, quantity: float, entry_price: float, 
                          current_price: Optional[float] = None) -> PositionRisk:
    """Convenience function to calculate position risk."""
    return risk_management_system.calculate_position_risk(symbol, quantity, entry_price, current_price)