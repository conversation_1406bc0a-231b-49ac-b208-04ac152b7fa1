"""
AI Context Aware Processor for Trading Bot

This module provides context-aware processing for AI responses,
incorporating user history, market conditions, and conversation context
to generate more relevant and personalized responses.
"""
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from enum import Enum
import json
from src.shared.error_handling.logging import get_logger

logger = get_logger(__name__)


class ContextType(Enum):
    """Types of context that can be incorporated."""
    USER_HISTORY = "user_history"
    MARKET_CONDITIONS = "market_conditions"
    CONVERSATION_HISTORY = "conversation_history"
    TRADING_POSITIONS = "trading_positions"
    PREFERENCES = "preferences"
    RISK_PROFILE = "risk_profile"


@dataclass
class ContextData:
    """Data structure for context information."""
    context_type: ContextType
    data: Dict[str, Any]
    timestamp: datetime
    confidence: float = 1.0
    importance: float = 0.5  # 0.0 to 1.0


@dataclass
class ProcessingResult:
    """Result of context-aware processing."""
    original_query: str
    enhanced_query: str
    context_incorporated: List[ContextType]
    processing_time: float
    confidence_score: float
    metadata: Dict[str, Any]


class ContextAwareProcessor:
    """Processes queries with awareness of various contextual factors."""
    
    def __init__(self):
        self._logger = get_logger("context_aware_processor")
        self._user_contexts: Dict[str, List[ContextData]] = {}
        self._conversation_history: Dict[str, List[Dict[str, Any]]] = {}
        self._market_context = {}
        self._context_weighting = {
            ContextType.USER_HISTORY: 0.3,
            ContextType.MARKET_CONDITIONS: 0.25,
            ContextType.CONVERSATION_HISTORY: 0.2,
            ContextType.TRADING_POSITIONS: 0.15,
            ContextType.PREFERENCES: 0.05,
            ContextType.RISK_PROFILE: 0.05
        }
    
    async def process_query_with_context(
        self, 
        query: str, 
        user_id: str,
        additional_context: Optional[Dict[str, Any]] = None
    ) -> ProcessingResult:
        """Process a query with contextual awareness."""
        start_time = datetime.now()
        
        self._logger.info(f"Processing query with context for user {user_id}: {query[:100]}...")
        
        try:
            # Gather all relevant context
            contexts = await self._gather_context(user_id, query, additional_context)
            
            # Enhance query with context
            enhanced_query = await self._enhance_query_with_context(query, contexts)
            
            # Calculate confidence based on context availability
            confidence_score = await self._calculate_confidence_score(contexts)
            
            # Record the context used
            context_types_used = [ctx.context_type for ctx in contexts]
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            result = ProcessingResult(
                original_query=query,
                enhanced_query=enhanced_query,
                context_incorporated=context_types_used,
                processing_time=processing_time,
                confidence_score=confidence_score,
                metadata={
                    'user_id': user_id,
                    'timestamp': datetime.now().isoformat(),
                    'context_count': len(contexts)
                }
            )
            
            self._logger.debug(f"Query processing completed in {processing_time:.3f}s with confidence {confidence_score:.2f}")
            
            return result
            
        except Exception as e:
            self._logger.error(f"Error processing query with context: {e}", exc_info=True)
            # Return original query if processing fails
            return ProcessingResult(
                original_query=query,
                enhanced_query=query,  # Fall back to original
                context_incorporated=[],
                processing_time=(datetime.now() - start_time).total_seconds(),
                confidence_score=0.1,  # Low confidence on error
                metadata={
                    'user_id': user_id,
                    'timestamp': datetime.now().isoformat(),
                    'error': str(e)
                }
            )
    
    async def _gather_context(
        self, 
        user_id: str, 
        query: str, 
        additional_context: Optional[Dict[str, Any]]
    ) -> List[ContextData]:
        """Gather all relevant context for the query."""
        contexts = []
        
        # Add user history context
        user_history = await self._get_user_history_context(user_id)
        if user_history:
            contexts.append(user_history)
        
        # Add conversation history context
        conversation_context = await self._get_conversation_context(user_id, query)
        if conversation_context:
            contexts.append(conversation_context)
        
        # Add market conditions context
        market_context = await self._get_market_context(query)
        if market_context:
            contexts.append(market_context)
        
        # Add trading positions context (if applicable)
        positions_context = await self._get_trading_positions_context(user_id)
        if positions_context:
            contexts.append(positions_context)
        
        # Add user preferences context
        preferences_context = await self._get_user_preferences_context(user_id)
        if preferences_context:
            contexts.append(preferences_context)
        
        # Add risk profile context
        risk_context = await self._get_risk_profile_context(user_id)
        if risk_context:
            contexts.append(risk_context)
        
        # Add any additional provided context
        if additional_context:
            additional_data = ContextData(
                context_type=ContextType.USER_HISTORY,  # Use as general context
                data=additional_context,
                timestamp=datetime.now(),
                confidence=0.9,
                importance=0.3
            )
            contexts.append(additional_data)
        
        return contexts
    
    async def _get_user_history_context(self, user_id: str) -> Optional[ContextData]:
        """Get user's historical interaction context."""
        if user_id in self._user_contexts:
            user_history = self._user_contexts[user_id][-10:]  # Last 10 interactions
            if user_history:
                return ContextData(
                    context_type=ContextType.USER_HISTORY,
                    data={
                        'recent_interactions': [
                            {
                                'query': ctx.data.get('original_query', ''),
                                'response_type': ctx.data.get('response_type', ''),
                                'timestamp': ctx.timestamp.isoformat()
                            }
                            for ctx in user_history
                        ],
                        'interaction_count': len(user_history)
                    },
                    timestamp=datetime.now(),
                    confidence=0.8,
                    importance=0.7
                )
        return None
    
    async def _get_conversation_context(self, user_id: str, current_query: str) -> Optional[ContextData]:
        """Get recent conversation history context."""
        if user_id in self._conversation_history:
            conversation = self._conversation_history[user_id][-5:]  # Last 5 exchanges
            if conversation:
                return ContextData(
                    context_type=ContextType.CONVERSATION_HISTORY,
                    data={
                        'recent_exchanges': conversation,
                        'conversation_length': len(conversation)
                    },
                    timestamp=datetime.now(),
                    confidence=0.7,
                    importance=0.6
                )
        return None
    
    async def _get_market_context(self, query: str) -> Optional[ContextData]:
        """Get current market conditions context."""
        # In a real implementation, this would fetch current market data
        # For now, we'll simulate based on query content
        
        import random
        market_data = {
            'market_trend': 'bullish' if random.random() > 0.5 else 'bearish',
            'volatility_level': random.choice(['low', 'medium', 'high']),
            'trading_volume': random.uniform(0.8, 1.2),  # Multiplier
            'sentiment': random.uniform(-1, 1),  # -1 to 1 scale
            'affected_symbols': self._extract_symbols_from_query(query)
        }
        
        return ContextData(
            context_type=ContextType.MARKET_CONDITIONS,
            data=market_data,
            timestamp=datetime.now(),
            confidence=0.9,
            importance=0.8
        )
    
    async def _get_trading_positions_context(self, user_id: str) -> Optional[ContextData]:
        """Get user's current trading positions."""
        # Simulate user's trading positions
        # In a real implementation, this would come from trading platform
        positions_data = {
            'positions': [
                {
                    'symbol': 'AAPL',
                    'quantity': 10,
                    'avg_price': 150.0,
                    'current_price': 155.0,
                    'direction': 'long',
                    'pnl': 50.0
                }
            ],
            'total_value': 1550.0,
            'cash_balance': 5000.0
        }
        
        return ContextData(
            context_type=ContextType.TRADING_POSITIONS,
            data=positions_data,
            timestamp=datetime.now() - timedelta(minutes=5),  # Slightly outdated
            confidence=0.7,
            importance=0.8
        )
    
    async def _get_user_preferences_context(self, user_id: str) -> Optional[ContextData]:
        """Get user's preferences context."""
        # Simulate user preferences
        preferences_data = {
            'risk_tolerance': 'moderate',
            'preferred_analysis': ['technical', 'fundamental'],
            'favorite_symbols': ['AAPL', 'MSFT', 'GOOGL'],
            'notification_preferences': {'email': True, 'sms': False},
            'trading_style': 'swing'
        }
        
        return ContextData(
            context_type=ContextType.PREFERENCES,
            data=preferences_data,
            timestamp=datetime.now(),
            confidence=1.0,
            importance=0.6
        )
    
    async def _get_risk_profile_context(self, user_id: str) -> Optional[ContextData]:
        """Get user's risk profile context."""
        # Simulate user's risk profile
        risk_profile = {
            'risk_level': 'moderate',
            'max_position_size': 2.0,  # Percent of portfolio
            'max_daily_loss': 5.0,    # Percent
            'trading_experience': 'intermediate',
            'investment_horizon': 'medium_term'
        }
        
        return ContextData(
            context_type=ContextType.RISK_PROFILE,
            data=risk_profile,
            timestamp=datetime.now(),
            confidence=1.0,
            importance=0.7
        )
    
    async def _enhance_query_with_context(self, query: str, contexts: List[ContextData]) -> str:
        """Enhance the original query with contextual information."""
        # Start with the original query
        enhanced_parts = [f"Original query: {query}"]
        
        # Add context-specific enhancements
        for context in contexts:
            if context.context_type == ContextType.USER_HISTORY:
                recent_queries = [item['query'] for item in context.data.get('recent_interactions', [])]
                if recent_queries:
                    enhanced_parts.append(f"User recently asked about: {', '.join(recent_queries[-2:])}")
            
            elif context.context_type == ContextType.MARKET_CONDITIONS:
                market_trend = context.data.get('market_trend', 'neutral')
                volatility = context.data.get('volatility_level', 'moderate')
                enhanced_parts.append(f"Current market is {market_trend} with {volatility} volatility")
                
                affected_symbols = context.data.get('affected_symbols', [])
                if affected_symbols:
                    enhanced_parts.append(f"Query involves symbols: {', '.join(affected_symbols)}")
            
            elif context.context_type == ContextType.TRADING_POSITIONS:
                positions = context.data.get('positions', [])
                if positions:
                    symbols = [pos['symbol'] for pos in positions]
                    enhanced_parts.append(f"User currently holds positions in: {', '.join(symbols)}")
            
            elif context.context_type == ContextType.RISK_PROFILE:
                risk_level = context.data.get('risk_level', 'moderate')
                enhanced_parts.append(f"User has {risk_level} risk tolerance")
        
        # Combine all enhanced parts
        enhanced_query = " | ".join(enhanced_parts)
        
        # Add a summary of available context
        context_summary = {
            'context_types': [ctx.context_type.value for ctx in contexts],
            'total_contexts': len(contexts),
            'timestamp': datetime.now().isoformat()
        }
        
        # Append context summary as metadata
        enhanced_query += f" | CONTEXT_SUMMARY: {json.dumps(context_summary)}"
        
        return enhanced_query
    
    async def _calculate_confidence_score(self, contexts: List[ContextData]) -> float:
        """Calculate confidence score based on available context."""
        if not contexts:
            return 0.2  # Low confidence if no context
        
        # Calculate weighted confidence based on context types and quality
        total_weighted_score = 0.0
        total_weight = 0.0
        
        for context in contexts:
            weight = self._context_weighting.get(context.context_type, 0.1)
            score = context.confidence * context.importance
            total_weighted_score += score * weight
            total_weight += weight
        
        if total_weight > 0:
            avg_confidence = total_weighted_score / total_weight
        else:
            avg_confidence = 0.5
        
        # Return bounded score between 0.1 and 0.9
        return max(0.1, min(0.9, avg_confidence))
    
    def _extract_symbols_from_query(self, query: str) -> List[str]:
        """Extract potential stock symbols from query."""
        # Simple symbol extraction - would be more sophisticated in real implementation
        import re
        # Look for potential ticker symbols (1-5 uppercase letters)
        symbols = re.findall(r'\b[A-Z]{1,5}\b', query.upper())
        # Filter for common valid symbols (in reality, would check against a symbol list)
        valid_symbols = [s for s in symbols if len(s) >= 2]  # At least 2 chars
        return list(set(valid_symbols))  # Remove duplicates
    
    def update_user_context(self, user_id: str, context_data: Dict[str, Any]):
        """Update user's context information."""
        if user_id not in self._user_contexts:
            self._user_contexts[user_id] = []
        
        context = ContextData(
            context_type=ContextType.USER_HISTORY,
            data=context_data,
            timestamp=datetime.now(),
            confidence=0.9,
            importance=0.7
        )
        
        self._user_contexts[user_id].append(context)
        
        # Keep only recent contexts (last 50 for each user)
        if len(self._user_contexts[user_id]) > 50:
            self._user_contexts[user_id] = self._user_contexts[user_id][-50:]
    
    def update_conversation_history(self, user_id: str, user_message: str, bot_response: str):
        """Update conversation history for a user."""
        if user_id not in self._conversation_history:
            self._conversation_history[user_id] = []
        
        exchange = {
            'user': user_message,
            'bot': bot_response,
            'timestamp': datetime.now().isoformat()
        }
        
        self._conversation_history[user_id].append(exchange)
        
        # Keep only recent conversation history (last 20 exchanges)
        if len(self._conversation_history[user_id]) > 20:
            self._conversation_history[user_id] = self._conversation_history[user_id][-20:]
    
    def get_user_context_summary(self, user_id: str) -> Dict[str, Any]:
        """Get a summary of context available for a user."""
        context_summary = {
            'user_id': user_id,
            'has_user_history': user_id in self._user_contexts,
            'has_conversation_history': user_id in self._conversation_history,
            'user_context_count': len(self._user_contexts.get(user_id, [])),
            'conversation_count': len(self._conversation_history.get(user_id, [])),
            'last_activity': None
        }
        
        # Determine last activity time
        last_user_context = (
            self._user_contexts[user_id][-1].timestamp.isoformat()
            if user_id in self._user_contexts and self._user_contexts[user_id]
            else None
        )
        
        last_conversation = (
            self._conversation_history[user_id][-1]['timestamp']
            if user_id in self._conversation_history and self._conversation_history[user_id]
            else None
        )
        
        # Take the more recent of the two
        if last_user_context and last_conversation:
            context_summary['last_activity'] = max(last_user_context, last_conversation)
        else:
            context_summary['last_activity'] = last_user_context or last_conversation
        
        return context_summary


# Global context aware processor instance
context_aware_processor = ContextAwareProcessor()


async def process_query_with_context(
    query: str, 
    user_id: str,
    additional_context: Optional[Dict[str, Any]] = None
) -> ProcessingResult:
    """Convenience function to process a query with context."""
    return await context_aware_processor.process_query_with_context(query, user_id, additional_context)


def update_user_context(user_id: str, context_data: Dict[str, Any]):
    """Convenience function to update user context."""
    context_aware_processor.update_user_context(user_id, context_data)


def update_conversation_history(user_id: str, user_message: str, bot_response: str):
    """Convenience function to update conversation history."""
    context_aware_processor.update_conversation_history(user_id, user_message, bot_response)