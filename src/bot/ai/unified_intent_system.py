"""
Context-Aware Processing System

This module provides context-aware processing that maintains conversation state
and understands user intent across multiple interactions, enabling natural
conversational flow with the trading bot.
"""

import asyncio
import json
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timed<PERSON><PERSON>
from enum import Enum

from src.shared.error_handling.logging import get_logger

logger = get_logger(__name__)

class UnifiedIntent(Enum):
    """Unified intent types for all bot interactions"""
    # Core Trading Intents
    PRICE_CHECK = "price_check"
    STOCK_ANALYSIS = "stock_analysis"
    TECHNICAL_ANALYSIS = "technical_analysis"
    FUNDAMENTAL_ANALYSIS = "fundamental_analysis"
    RECOMMENDATION = "recommendation"
    RISK_ASSESSMENT = "risk_assessment"
    
    # Portfolio & Management
    PORTFOLIO_ANALYSIS = "portfolio_analysis"
    WATCHLIST_MANAGEMENT = "watchlist_management"
    ALERT_MANAGEMENT = "alert_management"
    
    # Comparative & Research
    COMPARISON = "comparison"
    MARKET_NEWS = "market_news"
    SECTOR_ANALYSIS = "sector_analysis"
    
    # Advanced Trading
    OPTIONS_ANALYSIS = "options_analysis"
    ZONES_ANALYSIS = "zones_analysis"
    BATCH_ANALYSIS = "batch_analysis"
    
    # Utility & Support
    HELP_REQUEST = "help_request"
    STATUS_CHECK = "status_check"
    GENERAL_QUESTION = "general_question"
    GREETING = "greeting"
    
    # Meta Intents
    CONTEXT_CONTINUATION = "context_continuation"
    CLARIFICATION_REQUEST = "clarification_request"
    UNKNOWN = "unknown"

class ProcessingPipeline(Enum):
    """Available processing pipelines"""
    ASK_PIPELINE = "ask_pipeline"
    ANALYZE_PIPELINE = "analyze_pipeline"
    ZONES_PIPELINE = "zones_pipeline"
    BATCH_PIPELINE = "batch_pipeline"
    PORTFOLIO_PIPELINE = "portfolio_pipeline"
    WATCHLIST_PIPELINE = "watchlist_pipeline"
    ALERT_PIPELINE = "alert_pipeline"
    UTILITY_PIPELINE = "utility_pipeline"
    HELP_PIPELINE = "help_pipeline"

class ParameterType(Enum):
    """Types of parameters that can be extracted"""
    SYMBOL = "symbol"
    SYMBOLS_LIST = "symbols_list"
    TIMEFRAME = "timeframe"
    INDICATORS = "indicators"
    PRICE_TARGET = "price_target"
    PERCENTAGE = "percentage"
    DATE = "date"
    AMOUNT = "amount"
    TEXT = "text"
    BOOLEAN = "boolean"

@dataclass
class ExtractedParameter:
    """Extracted parameter from natural language"""
    name: str
    value: Any
    type: ParameterType
    confidence: float
    source_text: str
    alternatives: List[Any] = field(default_factory=list)

@dataclass
class ConversationContext:
    """Context for ongoing conversation"""
    user_id: str
    last_intent: Optional[UnifiedIntent] = None
    last_symbols: List[str] = field(default_factory=list)
    last_timeframe: Optional[str] = None
    last_parameters: Dict[str, Any] = field(default_factory=dict)
    conversation_history: List[Dict[str, Any]] = field(default_factory=list)
    session_start: datetime = field(default_factory=datetime.now)
    preferences: Dict[str, Any] = field(default_factory=dict)

@dataclass
class IntentAnalysisResult:
    """Comprehensive intent analysis result"""
    # Core Intent Information
    primary_intent: UnifiedIntent
    secondary_intents: List[UnifiedIntent]
    confidence: float
    
    # Processing Information
    recommended_pipeline: ProcessingPipeline
    processing_priority: int  # 1-10, higher = more urgent
    
    # Extracted Parameters
    parameters: Dict[str, ExtractedParameter]
    missing_parameters: List[str]
    
    # Context Information
    requires_context: bool
    context_continuation: bool
    references_previous: bool
    
    # Response Guidance
    response_style: str  # "concise", "detailed", "technical", etc.
    urgency_level: str   # "low", "medium", "high", "urgent"
    
    # Metadata
    processing_time: float
    method: str  # "ai", "hybrid", "fallback"
    reasoning: str
    suggestions: List[str] = field(default_factory=list)

class ContextType(Enum):
    """Types of context that can be maintained"""
    CONVERSATION = "conversation"
    SESSION = "session"
    USER_PREFERENCES = "user_preferences"
    MARKET_STATE = "market_state"
    ANALYSIS_HISTORY = "analysis_history"

class ContextPriority(Enum):
    """Priority levels for context information"""
    CRITICAL = "critical"      # Must be preserved (user preferences, active positions)
    HIGH = "high"             # Important for continuity (recent symbols, timeframes)
    MEDIUM = "medium"         # Helpful for UX (conversation flow, suggestions)
    LOW = "low"              # Nice to have (general history, patterns)

@dataclass
class ContextItem:
    """Individual context item with metadata"""
    key: str
    value: Any
    context_type: ContextType
    priority: ContextPriority
    created_at: datetime
    last_accessed: datetime
    access_count: int = 0
    expires_at: Optional[datetime] = None

@dataclass
class ProcessingContext:
    """Enhanced processing context for AI operations"""
    # User Information
    user_id: str
    session_id: str
    
    # Current Request
    current_query: str
    current_intent: UnifiedIntent
    current_parameters: Dict[str, ExtractedParameter]
    
    # Historical Context
    conversation_context: ConversationContext
    recent_analyses: List[Dict[str, Any]]
    user_preferences: Dict[str, Any]
    
    # Market Context
    market_session: str  # "pre_market", "market_hours", "after_hours", "closed"
    market_conditions: Dict[str, Any]
    
    # Processing Metadata
    context_confidence: float
    context_sources: List[str]
    processing_hints: List[str]

class ContextAwareProcessor:
    """
    Context-aware processing system that maintains conversation state
    and provides intelligent context for AI operations.
    """
    
    def __init__(self):
        self.intent_detector = UnifiedIntentDetector()
        self.context_store: Dict[str, Dict[str, ContextItem]] = {}
        self.session_contexts: Dict[str, ProcessingContext] = {}
        
        # Context retention policies
        self.retention_policies = {
            ContextPriority.CRITICAL: timedelta(days=30),
            ContextPriority.HIGH: timedelta(days=7),
            ContextPriority.MEDIUM: timedelta(days=1),
            ContextPriority.LOW: timedelta(hours=6)
        }
        
        # Context relevance scoring
        self.relevance_weights = {
            'recency': 0.3,      # How recent is the context
            'frequency': 0.2,    # How often it's accessed
            'similarity': 0.3,   # How similar to current query
            'importance': 0.2    # Inherent importance level
        }
        
        logger.info("✅ Context-Aware Processor initialized")
    
    async def process_with_context(
        self, 
        query: str, 
        user_id: str,
        session_id: Optional[str] = None,
        interaction_context: Optional[Dict[str, Any]] = None
    ) -> Tuple[IntentAnalysisResult, ProcessingContext]:
        """
        Process user query with full context awareness
        
        Args:
            query: User input query
            user_id: Unique user identifier
            session_id: Session identifier (optional)
            interaction_context: Additional context from interaction
            
        Returns:
            Tuple of (IntentAnalysisResult, ProcessingContext)
        """
        session_id = session_id or f"{user_id}_{datetime.now().strftime('%Y%m%d_%H')}"
        
        try:
            # Clean up expired context
            await self._cleanup_expired_context()
            
            # Get or create processing context
            processing_context = await self._build_processing_context(
                query, user_id, session_id, interaction_context
            )
            
            # Enhance query with context
            enhanced_query = await self._enhance_query_with_context(query, processing_context)
            
            # Perform intent analysis with context
            intent_result = await self.intent_detector.analyze_intent(
                enhanced_query, user_id, interaction_context
            )
            
            # Update processing context with results
            processing_context.current_intent = intent_result.primary_intent
            processing_context.current_parameters = intent_result.parameters
            
            # Store context for future use
            await self._store_context(user_id, session_id, processing_context, intent_result)
            
            # Generate context-aware processing hints
            processing_context.processing_hints = self._generate_processing_hints(
                intent_result, processing_context
            )
            
            logger.info(f"🧠 Context-aware processing complete: {intent_result.primary_intent.value} "
                       f"(context confidence: {processing_context.context_confidence:.2f})")
            
            return intent_result, processing_context
            
        except Exception as e:
            logger.error(f"❌ Context-aware processing failed: {e}")
            # Fallback to basic intent analysis
            intent_result = await self.intent_detector.analyze_intent(query, user_id)
            basic_context = ProcessingContext(
                user_id=user_id,
                session_id=session_id,
                current_query=query,
                current_intent=intent_result.primary_intent,
                current_parameters=intent_result.parameters,
                conversation_context=self.intent_detector._get_conversation_context(user_id),
                recent_analyses=[],
                user_preferences={},
                market_session="unknown",
                market_conditions={},
                context_confidence=0.5,
                context_sources=["fallback"],
                processing_hints=[]
            )
            return intent_result, basic_context
    
    async def _build_processing_context(
        self, 
        query: str, 
        user_id: str, 
        session_id: str,
        interaction_context: Optional[Dict[str, Any]]
    ) -> ProcessingContext:
        """Build comprehensive processing context"""
        
        # Get conversation context
        conversation_context = self.intent_detector._get_conversation_context(user_id)
        
        # Get user context
        user_context = self._get_user_context(user_id)
        
        # Get recent analyses
        recent_analyses = self._get_recent_analyses(user_id, limit=5)
        
        # Determine market session
        market_session = self._get_market_session()
        
        # Calculate context confidence
        context_confidence = self._calculate_context_confidence(
            conversation_context, user_context, recent_analyses
        )
        
        # Identify context sources
        context_sources = []
        if conversation_context.conversation_history:
            context_sources.append("conversation_history")
        if user_context:
            context_sources.append("user_preferences")
        if recent_analyses:
            context_sources.append("analysis_history")
        if interaction_context:
            context_sources.append("interaction_context")
        
        return ProcessingContext(
            user_id=user_id,
            session_id=session_id,
            current_query=query,
            current_intent=UnifiedIntent.UNKNOWN,  # Will be set after analysis
            current_parameters={},  # Will be set after analysis
            conversation_context=conversation_context,
            recent_analyses=recent_analyses,
            user_preferences=user_context.get('preferences', {}),
            market_session=market_session,
            market_conditions=self._get_market_conditions(),
            context_confidence=context_confidence,
            context_sources=context_sources,
            processing_hints=[]
        )
    
    async def _enhance_query_with_context(
        self, 
        query: str, 
        context: ProcessingContext
    ) -> str:
        """Enhance query with relevant context information"""
        
        # If query is very short or uses pronouns, add context
        if len(query.split()) < 3 or any(word in query.lower() for word in ['it', 'that', 'this', 'them']):
            
            # Add recent symbols if available
            if context.conversation_context.last_symbols:
                symbols_context = f" (referring to {', '.join(context.conversation_context.last_symbols)})"
                query += symbols_context
            
            # Add recent intent context
            if context.conversation_context.last_intent:
                intent_context = f" (previous context: {context.conversation_context.last_intent.value})"
                query += intent_context
        
        return query
    
    def _get_user_context(self, user_id: str) -> Dict[str, Any]:
        """Get stored user context"""
        user_contexts = self.context_store.get(user_id, {})
        
        # Filter for user preference contexts
        preferences = {}
        for key, context_item in user_contexts.items():
            if context_item.context_type == ContextType.USER_PREFERENCES:
                preferences[key] = context_item.value
                context_item.last_accessed = datetime.now()
                context_item.access_count += 1
        
        return {'preferences': preferences}
    
    def _get_recent_analyses(self, user_id: str, limit: int = 5) -> List[Dict[str, Any]]:
        """Get recent analysis history for user"""
        user_contexts = self.context_store.get(user_id, {})
        
        analyses = []
        for key, context_item in user_contexts.items():
            if context_item.context_type == ContextType.ANALYSIS_HISTORY:
                analyses.append({
                    'timestamp': context_item.created_at,
                    'data': context_item.value
                })
        
        # Sort by timestamp and return most recent
        analyses.sort(key=lambda x: x['timestamp'], reverse=True)
        return analyses[:limit]
    
    def _get_market_session(self) -> str:
        """Determine current market session"""
        now = datetime.now()
        hour = now.hour
        
        # US market hours (EST/EDT)
        if 9 <= hour < 16:
            return "market_hours"
        elif 4 <= hour < 9:
            return "pre_market"
        elif 16 <= hour < 20:
            return "after_hours"
        else:
            return "closed"
    
    def _get_market_conditions(self) -> Dict[str, Any]:
        """Get current market conditions (placeholder)"""
        return {
            'volatility': 'medium',
            'trend': 'neutral',
            'volume': 'normal'
        }
    
    def _calculate_context_confidence(
        self, 
        conversation_context: ConversationContext,
        user_context: Dict[str, Any],
        recent_analyses: List[Dict[str, Any]]
    ) -> float:
        """Calculate confidence in available context"""
        confidence = 0.0
        
        # Conversation history contributes to confidence
        if conversation_context.conversation_history:
            confidence += 0.3 * min(1.0, len(conversation_context.conversation_history) / 5)
        
        # Recent symbols and parameters
        if conversation_context.last_symbols:
            confidence += 0.2
        if conversation_context.last_parameters:
            confidence += 0.2
        
        # User preferences
        if user_context.get('preferences'):
            confidence += 0.15
        
        # Recent analyses
        if recent_analyses:
            confidence += 0.15 * min(1.0, len(recent_analyses) / 3)
        
        return min(1.0, confidence)

    async def _cleanup_expired_context(self):
        """Clean up expired context items"""
        current_time = datetime.now()

        for user_id in list(self.context_store.keys()):
            user_contexts = self.context_store[user_id]
            expired_keys = []

            for key, context_item in user_contexts.items():
                # Check if item has expired
                if context_item.expires_at and current_time > context_item.expires_at:
                    expired_keys.append(key)
                    continue

                # Check retention policy
                retention_period = self.retention_policies.get(
                    context_item.priority,
                    timedelta(hours=1)
                )
                if current_time - context_item.created_at > retention_period:
                    expired_keys.append(key)

            # Remove expired items
            for key in expired_keys:
                del user_contexts[key]

            # Remove user if no contexts remain
            if not user_contexts:
                del self.context_store[user_id]

    async def _store_context(
        self,
        user_id: str,
        session_id: str,
        processing_context: ProcessingContext,
        intent_result: IntentAnalysisResult
    ):
        """Store context information for future use"""
        if user_id not in self.context_store:
            self.context_store[user_id] = {}

        user_contexts = self.context_store[user_id]
        current_time = datetime.now()

        # Store current analysis
        analysis_key = f"analysis_{current_time.strftime('%Y%m%d_%H%M%S')}"
        user_contexts[analysis_key] = ContextItem(
            key=analysis_key,
            value={
                'query': processing_context.current_query,
                'intent': intent_result.primary_intent.value,
                'parameters': {name: param.value for name, param in intent_result.parameters.items()},
                'confidence': intent_result.confidence,
                'pipeline': intent_result.recommended_pipeline.value
            },
            context_type=ContextType.ANALYSIS_HISTORY,
            priority=ContextPriority.MEDIUM,
            created_at=current_time,
            last_accessed=current_time,
            expires_at=current_time + timedelta(days=1)
        )

        # Store session information
        session_key = f"session_{session_id}"
        user_contexts[session_key] = ContextItem(
            key=session_key,
            value={
                'session_id': session_id,
                'start_time': current_time,
                'query_count': len(processing_context.conversation_context.conversation_history),
                'primary_intents': [intent_result.primary_intent.value]
            },
            context_type=ContextType.SESSION,
            priority=ContextPriority.HIGH,
            created_at=current_time,
            last_accessed=current_time,
            expires_at=current_time + timedelta(hours=6)
        )

        # Update user preferences based on usage patterns
        await self._update_user_preferences(user_id, intent_result, processing_context)

    async def _update_user_preferences(
        self,
        user_id: str,
        intent_result: IntentAnalysisResult,
        processing_context: ProcessingContext
    ):
        """Update user preferences based on usage patterns"""
        user_contexts = self.context_store[user_id]

        # Track preferred response style
        style_key = "preferred_response_style"
        if style_key not in user_contexts:
            user_contexts[style_key] = ContextItem(
                key=style_key,
                value=intent_result.response_style,
                context_type=ContextType.USER_PREFERENCES,
                priority=ContextPriority.CRITICAL,
                created_at=datetime.now(),
                last_accessed=datetime.now()
            )
        else:
            # Update based on frequency
            user_contexts[style_key].value = intent_result.response_style
            user_contexts[style_key].last_accessed = datetime.now()

        # Track frequently used symbols
        if intent_result.parameters and 'symbols' in intent_result.parameters:
            symbols = intent_result.parameters['symbols'].value
            for symbol in symbols:
                symbol_key = f"frequent_symbol_{symbol}"
                if symbol_key not in user_contexts:
                    user_contexts[symbol_key] = ContextItem(
                        key=symbol_key,
                        value={'symbol': symbol, 'count': 1},
                        context_type=ContextType.USER_PREFERENCES,
                        priority=ContextPriority.HIGH,
                        created_at=datetime.now(),
                        last_accessed=datetime.now()
                    )
                else:
                    user_contexts[symbol_key].value['count'] += 1
                    user_contexts[symbol_key].last_accessed = datetime.now()

    def _generate_processing_hints(
        self,
        intent_result: IntentAnalysisResult,
        processing_context: ProcessingContext
    ) -> List[str]:
        """Generate processing hints based on context"""
        hints = []

        # Context-based hints
        if processing_context.context_confidence > 0.8:
            hints.append("high_context_confidence")

        if processing_context.conversation_context.last_symbols:
            hints.append("has_symbol_context")

        if processing_context.recent_analyses:
            hints.append("has_analysis_history")

        # Intent-specific hints
        if intent_result.primary_intent in [UnifiedIntent.TECHNICAL_ANALYSIS, UnifiedIntent.STOCK_ANALYSIS]:
            hints.append("fetch_market_data")
            hints.append("calculate_indicators")

        if intent_result.primary_intent == UnifiedIntent.COMPARISON:
            hints.append("multi_symbol_analysis")
            hints.append("comparative_metrics")

        # Market session hints
        if processing_context.market_session == "after_hours":
            hints.append("after_hours_context")
        elif processing_context.market_session == "pre_market":
            hints.append("pre_market_context")

        # Missing parameter hints
        if intent_result.missing_parameters:
            hints.append("request_missing_parameters")

        return hints

class UnifiedIntentDetector:
    """
    Unified AI-powered intent detection system that replaces rigid command parsing
    with intelligent natural language understanding and context-aware processing.
    """
    
    def __init__(self):
        self.conversation_contexts: Dict[str, ConversationContext] = {}
        self.context_timeout = timedelta(hours=2)  # Context expires after 2 hours
        
        # Intent to pipeline mapping
        self.intent_pipeline_mapping = {
            UnifiedIntent.PRICE_CHECK: ProcessingPipeline.ASK_PIPELINE,
            UnifiedIntent.STOCK_ANALYSIS: ProcessingPipeline.ANALYZE_PIPELINE,
            UnifiedIntent.TECHNICAL_ANALYSIS: ProcessingPipeline.ANALYZE_PIPELINE,
            UnifiedIntent.FUNDAMENTAL_ANALYSIS: ProcessingPipeline.ANALYZE_PIPELINE,
            UnifiedIntent.RECOMMENDATION: ProcessingPipeline.ASK_PIPELINE,
            UnifiedIntent.RISK_ASSESSMENT: ProcessingPipeline.ANALYZE_PIPELINE,
            UnifiedIntent.PORTFOLIO_ANALYSIS: ProcessingPipeline.PORTFOLIO_PIPELINE,
            UnifiedIntent.WATCHLIST_MANAGEMENT: ProcessingPipeline.WATCHLIST_PIPELINE,
            UnifiedIntent.ALERT_MANAGEMENT: ProcessingPipeline.ALERT_PIPELINE,
            UnifiedIntent.COMPARISON: ProcessingPipeline.BATCH_PIPELINE,
            UnifiedIntent.ZONES_ANALYSIS: ProcessingPipeline.ZONES_PIPELINE,
            UnifiedIntent.BATCH_ANALYSIS: ProcessingPipeline.BATCH_PIPELINE,
            UnifiedIntent.OPTIONS_ANALYSIS: ProcessingPipeline.ANALYZE_PIPELINE,
            UnifiedIntent.HELP_REQUEST: ProcessingPipeline.HELP_PIPELINE,
            UnifiedIntent.STATUS_CHECK: ProcessingPipeline.UTILITY_PIPELINE,
            UnifiedIntent.GENERAL_QUESTION: ProcessingPipeline.ASK_PIPELINE,
        }
        
        logger.info("✅ Unified Intent Detection System initialized")
    
    async def analyze_intent(
        self, 
        text: str, 
        user_id: str,
        interaction_context: Optional[Dict[str, Any]] = None
    ) -> IntentAnalysisResult:
        """
        Analyze user intent with comprehensive natural language understanding
        
        Args:
            text: User input text
            user_id: Unique user identifier
            interaction_context: Additional context from Discord interaction
            
        Returns:
            IntentAnalysisResult with comprehensive analysis
        """
        start_time = time.time()
        
        try:
            # Get or create conversation context
            context = self._get_conversation_context(user_id)
            
            # Simple intent analysis (placeholder for AI integration)
            unified_intent = self._analyze_intent_simple(text)
            secondary_intents = []
            
            # Determine processing pipeline
            pipeline = self.intent_pipeline_mapping.get(unified_intent, ProcessingPipeline.ASK_PIPELINE)
            
            # Extract parameters using simple regex
            parameters = self._extract_parameters_simple(text, unified_intent, context)
            
            # Analyze context requirements
            context_analysis = self._analyze_context_requirements(text, context)
            
            # Update conversation context
            self._update_conversation_context(context, unified_intent, parameters, text)
            
            # Generate processing recommendations
            suggestions = self._generate_suggestions(unified_intent, parameters, context)
            
            processing_time = time.time() - start_time
            
            result = IntentAnalysisResult(
                primary_intent=unified_intent,
                secondary_intents=secondary_intents,
                confidence=0.8,  # Placeholder confidence
                recommended_pipeline=pipeline,
                processing_priority=self._calculate_priority(unified_intent),
                parameters=parameters,
                missing_parameters=self._identify_missing_parameters(unified_intent, parameters),
                requires_context=context_analysis['requires_context'],
                context_continuation=context_analysis['context_continuation'],
                references_previous=context_analysis['references_previous'],
                response_style="detailed",
                urgency_level="medium",
                processing_time=processing_time,
                method="simple",
                reasoning="Simple pattern matching analysis",
                suggestions=suggestions
            )
            
            logger.info(f"🎯 Intent analysis complete: {unified_intent.value} "
                       f"(confidence: 0.8, pipeline: {pipeline.value}, time: {processing_time:.3f}s)")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Intent analysis failed: {e}")
            return self._create_fallback_result(text, user_id, start_time)
    
    def _analyze_intent_simple(self, text: str) -> UnifiedIntent:
        """Simple intent analysis using keyword matching"""
        text_lower = text.lower()
        
        # Price check patterns
        if any(word in text_lower for word in ['price', 'cost', 'value', 'worth', 'trading at']):
            return UnifiedIntent.PRICE_CHECK
        
        # Technical analysis patterns
        if any(word in text_lower for word in ['analyze', 'analysis', 'technical', 'chart', 'indicator']):
            return UnifiedIntent.TECHNICAL_ANALYSIS
        
        # Stock analysis patterns
        if any(word in text_lower for word in ['stock', 'shares', 'equity', 'company']):
            return UnifiedIntent.STOCK_ANALYSIS
        
        # Comparison patterns
        if any(word in text_lower for word in ['compare', 'vs', 'versus', 'better', 'worse']):
            return UnifiedIntent.COMPARISON
        
        # Help patterns
        if any(word in text_lower for word in ['help', 'how', 'what', 'explain']):
            return UnifiedIntent.HELP_REQUEST
        
        # Greeting patterns
        if any(word in text_lower for word in ['hello', 'hi', 'hey', 'good morning', 'good afternoon']):
            return UnifiedIntent.GREETING
        
        return UnifiedIntent.GENERAL_QUESTION
    
    def _extract_parameters_simple(
        self,
        text: str,
        intent: UnifiedIntent,
        context: ConversationContext
    ) -> Dict[str, ExtractedParameter]:
        """Simple parameter extraction using regex"""
        parameters = {}
        
        # Extract symbols (simple pattern)
        import re
        symbols = re.findall(r'\b[A-Z]{1,5}\b', text.upper())
        if symbols:
            parameters['symbols'] = ExtractedParameter(
                name='symbols',
                value=symbols,
                type=ParameterType.SYMBOLS_LIST,
                confidence=0.9,
                source_text=text
            )
        elif context.last_symbols:
            # Use context symbols if none found
            parameters['symbols'] = ExtractedParameter(
                name='symbols',
                value=context.last_symbols,
                type=ParameterType.SYMBOLS_LIST,
                confidence=0.6,
                source_text="from context"
            )
        
        return parameters
    
    def _analyze_context_requirements(
        self,
        text: str,
        context: ConversationContext
    ) -> Dict[str, bool]:
        """Analyze if the query requires or references context"""
        text_lower = text.lower()
        
        # Check for context continuation words
        continuation_words = ['also', 'too', 'and', 'what about', 'how about', 'compare']
        context_continuation = any(word in text_lower for word in continuation_words)
        
        # Check for references to previous queries
        reference_words = ['it', 'that', 'this', 'them', 'those', 'same']
        references_previous = any(word in text_lower for word in reference_words)
        
        # Check if query is incomplete without context
        requires_context = (
            context_continuation or
            references_previous or
            (len(text.split()) < 3 and not any(char in text for char in ['?', '!']))
        )
        
        return {
            'requires_context': requires_context,
            'context_continuation': context_continuation,
            'references_previous': references_previous
        }
    
    def _update_conversation_context(
        self,
        context: ConversationContext,
        intent: UnifiedIntent,
        parameters: Dict[str, ExtractedParameter],
        text: str
    ):
        """Update conversation context with new information"""
        context.last_intent = intent
        
        # Update symbols
        if 'symbols' in parameters:
            context.last_symbols = parameters['symbols'].value
        elif 'symbol' in parameters:
            context.last_symbols = [parameters['symbol'].value]
        
        # Add to conversation history
        context.conversation_history.append({
            'timestamp': datetime.now().isoformat(),
            'text': text,
            'intent': intent.value,
            'parameters': {name: param.value for name, param in parameters.items()}
        })
        
        # Keep only last 10 interactions
        if len(context.conversation_history) > 10:
            context.conversation_history = context.conversation_history[-10:]
    
    def _calculate_priority(self, intent: UnifiedIntent) -> int:
        """Calculate processing priority (1-10, higher = more urgent)"""
        base_priority = {
            UnifiedIntent.PRICE_CHECK: 8,  # High priority for quick responses
            UnifiedIntent.STOCK_ANALYSIS: 7,
            UnifiedIntent.TECHNICAL_ANALYSIS: 6,
            UnifiedIntent.FUNDAMENTAL_ANALYSIS: 5,
            UnifiedIntent.RECOMMENDATION: 7,
            UnifiedIntent.RISK_ASSESSMENT: 6,
            UnifiedIntent.PORTFOLIO_ANALYSIS: 5,
            UnifiedIntent.WATCHLIST_MANAGEMENT: 4,
            UnifiedIntent.ALERT_MANAGEMENT: 6,
            UnifiedIntent.COMPARISON: 5,
            UnifiedIntent.ZONES_ANALYSIS: 5,
            UnifiedIntent.BATCH_ANALYSIS: 4,
            UnifiedIntent.OPTIONS_ANALYSIS: 6,
            UnifiedIntent.HELP_REQUEST: 3,
            UnifiedIntent.STATUS_CHECK: 2,
            UnifiedIntent.GENERAL_QUESTION: 3,
            UnifiedIntent.GREETING: 1,
        }.get(intent, 3)
        
        return base_priority
    
    def _identify_missing_parameters(
        self,
        intent: UnifiedIntent,
        parameters: Dict[str, ExtractedParameter]
    ) -> List[str]:
        """Identify missing required parameters for the intent"""
        required_params = {
            UnifiedIntent.PRICE_CHECK: ['symbols'],
            UnifiedIntent.STOCK_ANALYSIS: ['symbols'],
            UnifiedIntent.TECHNICAL_ANALYSIS: ['symbols'],
            UnifiedIntent.FUNDAMENTAL_ANALYSIS: ['symbols'],
            UnifiedIntent.COMPARISON: ['symbols'],  # Should have multiple symbols
            UnifiedIntent.ZONES_ANALYSIS: ['symbols'],
            UnifiedIntent.BATCH_ANALYSIS: ['symbols'],
            UnifiedIntent.OPTIONS_ANALYSIS: ['symbols'],
        }.get(intent, [])
        
        missing = []
        for param in required_params:
            if param not in parameters:
                missing.append(param)
            elif param == 'symbols' and intent == UnifiedIntent.COMPARISON:
                # Comparison needs multiple symbols
                symbols = parameters.get('symbols')
                if symbols and len(symbols.value) < 2:
                    missing.append('additional_symbols')
        
        return missing
    
    def _generate_suggestions(
        self,
        intent: UnifiedIntent,
        parameters: Dict[str, ExtractedParameter],
        context: ConversationContext
    ) -> List[str]:
        """Generate helpful suggestions for the user"""
        suggestions = []
        
        # Intent-specific suggestions
        if intent == UnifiedIntent.TECHNICAL_ANALYSIS:
            if 'indicators' not in parameters:
                suggestions.append("Consider specifying indicators like RSI, MACD, or moving averages")
            if 'timeframe' not in parameters:
                suggestions.append("You might want to specify a timeframe (e.g., 1d, 1w, 1m)")
        
        elif intent == UnifiedIntent.COMPARISON:
            symbols = parameters.get('symbols')
            if not symbols or len(symbols.value) < 2:
                suggestions.append("For comparison, please specify at least two stocks")
        
        elif intent == UnifiedIntent.PORTFOLIO_ANALYSIS:
            suggestions.append("I can help analyze your portfolio performance and risk metrics")
        
        elif intent == UnifiedIntent.ZONES_ANALYSIS:
            if 'timeframe' not in parameters:
                suggestions.append("Specify a timeframe for more accurate support/resistance zones")
        
        # Context-based suggestions
        if context.last_symbols and 'symbols' not in parameters:
            suggestions.append(f"Continue with {', '.join(context.last_symbols[:2])}?")
        
        return suggestions
    
    def _get_conversation_context(self, user_id: str) -> ConversationContext:
        """Get or create conversation context for user"""
        # Clean up expired contexts
        current_time = datetime.now()
        expired_users = [
            uid for uid, ctx in self.conversation_contexts.items()
            if current_time - ctx.session_start > self.context_timeout
        ]
        for uid in expired_users:
            del self.conversation_contexts[uid]
        
        # Get or create context
        if user_id not in self.conversation_contexts:
            self.conversation_contexts[user_id] = ConversationContext(user_id=user_id)
        
        return self.conversation_contexts[user_id]
    
    def _create_fallback_result(self, text: str, user_id: str, start_time: float) -> IntentAnalysisResult:
        """Create fallback result when analysis fails"""
        processing_time = time.time() - start_time
        
        return IntentAnalysisResult(
            primary_intent=UnifiedIntent.GENERAL_QUESTION,
            secondary_intents=[],
            confidence=0.5,
            recommended_pipeline=ProcessingPipeline.ASK_PIPELINE,
            processing_priority=3,
            parameters={},
            missing_parameters=[],
            requires_context=False,
            context_continuation=False,
            references_previous=False,
            response_style="detailed",
            urgency_level="medium",
            processing_time=processing_time,
            method="fallback",
            reasoning="Simple analysis failed, using fallback classification",
            suggestions=["Please try rephrasing your question for better understanding"]
        )

# Global instance for easy access
unified_intent_detector = UnifiedIntentDetector()

# Convenience functions
async def analyze_user_intent(text: str, user_id: str, context: Optional[Dict[str, Any]] = None) -> IntentAnalysisResult:
    """Analyze user intent using the unified system"""
    return await unified_intent_detector.analyze_intent(text, user_id, context)

async def get_processing_pipeline(text: str, user_id: str) -> ProcessingPipeline:
    """Get recommended processing pipeline for user input"""
    result = await unified_intent_detector.analyze_intent(text, user_id)
    return result.recommended_pipeline

# Global instance for easy access
context_aware_processor = ContextAwareProcessor()

# Convenience functions
async def process_with_context(
    query: str,
    user_id: str,
    session_id: Optional[str] = None,
    context: Optional[Dict[str, Any]] = None
) -> Tuple[IntentAnalysisResult, ProcessingContext]:
    """Process query with full context awareness"""
    return await context_aware_processor.process_with_context(query, user_id, session_id, context)