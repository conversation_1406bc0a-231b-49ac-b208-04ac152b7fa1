#!/usr/bin/env python3
"""
Startup script for TradingView Automation Bot
"""

import os
import sys
import asyncio
import logging
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def start_bot():
    """Start the TradingView Automation Bot"""
    try:
        logger.info("🚀 Starting TradingView Automation Bot...")
        
        # Import bot components from new location
        from src.bot.client import create_bot
        
        # Create bot instance
        bot = create_bot()
        if not bot:
            logger.error("❌ Failed to create bot instance")
            return 1
            
        logger.info("✅ Bot instance created successfully")
        
        # Get token from environment
        token = os.getenv('DISCORD_BOT_TOKEN')
        if not token:
            logger.error("❌ DISCORD_BOT_TOKEN not set in environment variables")
            return 1
            
        logger.info("🔐 Discord token loaded successfully")
        
        # Start bot
        logger.info("🔄 Starting bot...")
        await bot.start_bot(token)
        
        return 0
        
    except KeyboardInterrupt:
        logger.info("🛑 Bot shutdown requested by user")
        return 0
    except Exception as e:
        logger.error(f"❌ Bot failed to start: {e}", exc_info=True)
        return 1

if __name__ == "__main__":
    # Change to project directory
    os.chdir(project_root)
    
    # Run the bot
    exit_code = asyncio.run(start_bot())
    sys.exit(exit_code)