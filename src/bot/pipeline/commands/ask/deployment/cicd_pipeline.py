"""
CI/CD Pipeline for ASK - Simplified Refactored Version

Basic pipeline config for build/test/deploy.
Uses core config; no full execution, just config generation.
For dev/deployment use.
"""
from typing import Dict, Any, List
from enum import Enum

from src.core.config_manager import ConfigManager
from src.shared.error_handling.logging import get_logger

logger = get_logger(__name__)


class PipelineStage(str, Enum):
    BUILD = "build"
    TEST = "test"
    DEPLOY = "deploy"


class SimpleCICDPipeline:
    """
    Simple CI/CD config using core config.
    Generates workflow config; no execution.
    """
    def __init__(self, config_manager: ConfigManager):
        self.config = config_manager

    def generate_workflow(self) -> Dict[str, Any]:
        """Generate simple workflow."""
        workflow = {
            "name": "ask-pipeline-ci",
            "on": ["push", "pull_request"],
            "jobs": {
                "build": {
                    "runs-on": "ubuntu-latest",
                    "steps": [
                        {"uses": "actions/checkout@v4"},
                        {"run": "pip install -r requirements.txt"},
                        {"run": "pytest tests/"}
                    ]
                },
                "deploy": {
                    "runs-on": "ubuntu-latest",
                    "steps": [
                        {"run": "docker build -t ask-pipeline ."},
                        {"run": "docker push ask-pipeline"}
                    ]
                }
            }
        }
        logger.info("CI/CD workflow generated")
        return workflow


# Global
_pipeline = None


def get_cicd_pipeline(config_manager: ConfigManager) -> SimpleCICDPipeline:
    global _pipeline
    if _pipeline is None:
        _pipeline = SimpleCICDPipeline(config_manager)
    return _pipeline