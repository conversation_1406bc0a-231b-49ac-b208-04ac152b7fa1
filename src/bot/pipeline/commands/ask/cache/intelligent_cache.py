"""
Intelligent Cache System for ASK Pipeline

Advanced caching with query-based hashing, TTL management, 
invalidation strategies, and memory optimization.
"""

import time
import hashlib
import json
import asyncio
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import threading
from collections import OrderedDict

from src.shared.error_handling.logging import get_logger

logger = get_logger(__name__)

@dataclass
class CacheEntry:
    """Represents a cached entry with metadata"""
    key: str
    data: Any
    created_at: float
    last_accessed: float
    access_count: int
    ttl: float
    size_bytes: int
    query_hash: str
    intent: str
    
    def is_expired(self) -> bool:
        """Check if cache entry has expired"""
        return time.time() - self.created_at > self.ttl
    
    def is_stale(self, staleness_threshold: float = 300) -> bool:
        """Check if entry is stale (not accessed recently)"""
        return time.time() - self.last_accessed > staleness_threshold
    
    def update_access(self):
        """Update access metadata"""
        self.last_accessed = time.time()
        self.access_count += 1

@dataclass
class CacheStats:
    """Cache performance statistics"""
    total_requests: int = 0
    cache_hits: int = 0
    cache_misses: int = 0
    evictions: int = 0
    memory_usage_bytes: int = 0
    entries_count: int = 0
    
    @property
    def hit_rate(self) -> float:
        """Calculate cache hit rate"""
        if self.total_requests == 0:
            return 0.0
        return (self.cache_hits / self.total_requests) * 100

class IntelligentCache:
    """
    Advanced caching system with intelligent features:
    
    - Query-based hashing for semantic similarity
    - TTL management with different expiration times
    - LRU eviction with memory optimization
    - Intent-based cache segmentation
    - Performance monitoring and statistics
    - Automatic cleanup and maintenance
    """
    
    def __init__(
        self,
        max_memory_mb: int = 100,
        default_ttl: float = 300,  # 5 minutes
        max_entries: int = 1000,
        cleanup_interval: float = 60  # 1 minute
    ):
        self.max_memory_bytes = max_memory_mb * 1024 * 1024
        self.default_ttl = default_ttl
        self.max_entries = max_entries
        self.cleanup_interval = cleanup_interval
        
        # Cache storage (thread-safe)
        self._cache: OrderedDict[str, CacheEntry] = OrderedDict()
        self._lock = threading.RLock()
        
        # Statistics
        self.stats = CacheStats()
        
        # TTL configurations for different content types
        self.ttl_config = {
            'casual': 3600,      # 1 hour for casual responses
            'data_needed': 300,  # 5 minutes for market data
            'price': 60,         # 1 minute for price data
            'news': 900,         # 15 minutes for news
            'analysis': 1800     # 30 minutes for analysis
        }
        
        # Start background cleanup task
        self._cleanup_task = None
        self._start_cleanup_task()
        
        logger.info(f"✅ Intelligent cache initialized (max: {max_memory_mb}MB, {max_entries} entries)")
    
    def _start_cleanup_task(self):
        """Start background cleanup task"""
        def cleanup_loop():
            while True:
                try:
                    self._cleanup_expired()
                    time.sleep(self.cleanup_interval)
                except Exception as e:
                    logger.error(f"Cache cleanup error: {e}")
                    time.sleep(self.cleanup_interval)
        
        self._cleanup_task = threading.Thread(target=cleanup_loop, daemon=True)
        self._cleanup_task.start()
    
    def _generate_cache_key(self, query: str, intent: str, context: Dict[str, Any] = None) -> str:
        """Generate intelligent cache key based on query semantics"""
        # Normalize query for better cache hits
        normalized_query = self._normalize_query(query)
        
        # Create cache key components
        key_components = {
            'query': normalized_query,
            'intent': intent,
            'context': context or {}
        }
        
        # Generate hash
        key_string = json.dumps(key_components, sort_keys=True)
        return hashlib.sha256(key_string.encode()).hexdigest()[:16]
    
    def _normalize_query(self, query: str) -> str:
        """Normalize query for better cache matching"""
        # Convert to lowercase and remove extra whitespace
        normalized = ' '.join(query.lower().split())
        
        # Remove common variations that don't affect meaning
        replacements = {
            "what's": "what is",
            "what're": "what are", 
            "can you": "",
            "please": "",
            "current": "",
            "latest": "",
            "right now": "",
            "today": ""
        }
        
        for old, new in replacements.items():
            normalized = normalized.replace(old, new)
        
        return normalized.strip()
    
    def _calculate_size(self, data: Any) -> int:
        """Estimate memory size of data"""
        try:
            return len(json.dumps(data, default=str).encode('utf-8'))
        except:
            return len(str(data).encode('utf-8'))
    
    def _determine_ttl(self, intent: str, query: str) -> float:
        """Determine appropriate TTL based on content type"""
        query_lower = query.lower()
        
        # Price queries expire quickly
        if any(word in query_lower for word in ['price', 'quote', 'current']):
            return self.ttl_config.get('price', 60)
        
        # News queries have medium expiration
        if any(word in query_lower for word in ['news', 'latest', 'update']):
            return self.ttl_config.get('news', 900)
        
        # Analysis queries have longer expiration
        if any(word in query_lower for word in ['analysis', 'technical', 'chart']):
            return self.ttl_config.get('analysis', 1800)
        
        # Use intent-based TTL
        return self.ttl_config.get(intent, self.default_ttl)
    
    async def get(
        self, 
        query: str, 
        intent: str, 
        context: Dict[str, Any] = None
    ) -> Optional[Any]:
        """Get cached result for query"""
        with self._lock:
            self.stats.total_requests += 1
            
            cache_key = self._generate_cache_key(query, intent, context)
            
            if cache_key in self._cache:
                entry = self._cache[cache_key]
                
                # Check if expired
                if entry.is_expired():
                    logger.debug(f"Cache entry expired: {cache_key}")
                    del self._cache[cache_key]
                    self.stats.cache_misses += 1
                    return None
                
                # Update access and move to end (LRU)
                entry.update_access()
                self._cache.move_to_end(cache_key)
                
                self.stats.cache_hits += 1
                logger.debug(f"Cache hit: {cache_key} (accessed {entry.access_count} times)")
                return entry.data
            
            self.stats.cache_misses += 1
            logger.debug(f"Cache miss: {cache_key}")
            return None
    
    async def set(
        self, 
        query: str, 
        intent: str, 
        data: Any, 
        context: Dict[str, Any] = None,
        custom_ttl: Optional[float] = None
    ) -> bool:
        """Cache result for query"""
        with self._lock:
            cache_key = self._generate_cache_key(query, intent, context)
            
            # Calculate size and TTL
            size_bytes = self._calculate_size(data)
            ttl = custom_ttl or self._determine_ttl(intent, query)
            
            # Check memory limits
            if size_bytes > self.max_memory_bytes:
                logger.warning(f"Data too large to cache: {size_bytes} bytes")
                return False
            
            # Ensure we have space
            self._ensure_space(size_bytes)
            
            # Create cache entry
            entry = CacheEntry(
                key=cache_key,
                data=data,
                created_at=time.time(),
                last_accessed=time.time(),
                access_count=1,
                ttl=ttl,
                size_bytes=size_bytes,
                query_hash=hashlib.md5(query.encode()).hexdigest()[:8],
                intent=intent
            )
            
            # Store in cache
            self._cache[cache_key] = entry
            self._update_stats()
            
            logger.debug(f"Cached: {cache_key} (size: {size_bytes} bytes, TTL: {ttl}s)")
            return True
    
    def _ensure_space(self, required_bytes: int):
        """Ensure sufficient cache space by evicting entries"""
        current_size = sum(entry.size_bytes for entry in self._cache.values())
        
        # Evict if we exceed memory or entry limits
        while (
            (current_size + required_bytes > self.max_memory_bytes) or
            (len(self._cache) >= self.max_entries)
        ) and self._cache:
            
            # Remove least recently used entry
            oldest_key, oldest_entry = self._cache.popitem(last=False)
            current_size -= oldest_entry.size_bytes
            self.stats.evictions += 1
            
            logger.debug(f"Evicted LRU entry: {oldest_key}")
    
    def _cleanup_expired(self):
        """Remove expired entries"""
        with self._lock:
            current_time = time.time()
            expired_keys = []
            
            for key, entry in self._cache.items():
                if entry.is_expired():
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self._cache[key]
                logger.debug(f"Cleaned up expired entry: {key}")
            
            if expired_keys:
                self._update_stats()
                logger.info(f"Cleaned up {len(expired_keys)} expired cache entries")
    
    def _update_stats(self):
        """Update cache statistics"""
        self.stats.entries_count = len(self._cache)
        self.stats.memory_usage_bytes = sum(entry.size_bytes for entry in self._cache.values())
    
    def get_stats(self) -> Dict[str, Any]:
        """Get comprehensive cache statistics"""
        with self._lock:
            self._update_stats()
            
            # Calculate additional metrics
            if self._cache:
                avg_access_count = sum(entry.access_count for entry in self._cache.values()) / len(self._cache)
                avg_age = sum(time.time() - entry.created_at for entry in self._cache.values()) / len(self._cache)
            else:
                avg_access_count = 0
                avg_age = 0
            
            return {
                **asdict(self.stats),
                'hit_rate_percent': self.stats.hit_rate,
                'memory_usage_mb': self.stats.memory_usage_bytes / (1024 * 1024),
                'memory_limit_mb': self.max_memory_bytes / (1024 * 1024),
                'memory_utilization_percent': (self.stats.memory_usage_bytes / self.max_memory_bytes) * 100,
                'avg_access_count': avg_access_count,
                'avg_entry_age_seconds': avg_age,
                'ttl_config': self.ttl_config
            }
    
    def clear(self):
        """Clear all cache entries"""
        with self._lock:
            self._cache.clear()
            self.stats = CacheStats()
            logger.info("Cache cleared")
    
    def invalidate_pattern(self, pattern: str):
        """Invalidate cache entries matching a pattern"""
        with self._lock:
            keys_to_remove = []
            
            for key, entry in self._cache.items():
                if pattern.lower() in entry.query_hash.lower():
                    keys_to_remove.append(key)
            
            for key in keys_to_remove:
                del self._cache[key]
            
            if keys_to_remove:
                self._update_stats()
                logger.info(f"Invalidated {len(keys_to_remove)} cache entries matching pattern: {pattern}")
    
    def __del__(self):
        """Cleanup when cache is destroyed"""
        if hasattr(self, '_cleanup_task') and self._cleanup_task:
            # Note: Can't join daemon thread in __del__
            pass