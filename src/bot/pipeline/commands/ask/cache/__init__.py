"""
ASK Pipeline Cache Module
Provides caching functionality for the ASK pipeline.
"""

try:
    from .unified_cache import (
        UnifiedCacheManager as CacheManager,
        UnifiedCacheConfig as CacheConfig,
        UnifiedCacheManager,
        UnifiedCacheConfig
    )
    print("✅ Successfully imported UnifiedCacheManager")
except ImportError as e:
    # Fallback if unified_cache doesn't have UnifiedCacheManager
    print(f"⚠️ Failed to import UnifiedCacheManager: {e}")
    from src.shared.cache.cache_service import cache_service

    # Create a simple CacheConfig fallback
    class CacheConfig:
        def __init__(self, enabled=True, key_prefix="ask_pipeline", default_ttl=300, **kwargs):
            self.enabled = enabled
            self.key_prefix = key_prefix
            self.default_ttl = default_ttl
            # Accept any additional kwargs to be compatible
            for key, value in kwargs.items():
                setattr(self, key, value)

    # Create a wrapper class for the cache service
    class CacheManager:
        def __init__(self, config=None):
            self.config = config or CacheConfig()
            self.cache_service = cache_service

        def __getattr__(self, name):
            # Delegate to the cache service
            return getattr(self.cache_service, name)

        # Add missing cache methods as no-ops for fallback
        async def get_intent_cache(self, query: str, correlation_id: str = ""):
            return None

        async def set_intent_cache(self, query: str, data, correlation_id: str = ""):
            return False

        async def get_tool_cache(self, query: str, intent_result=None, correlation_id: str = ""):
            return None

        async def set_tool_cache(self, query: str, intent_result, data, correlation_id: str = ""):
            return False

    # Aliases for compatibility
    UnifiedCacheManager = CacheManager
    UnifiedCacheConfig = CacheConfig

try:
    from .intelligent_cache import IntelligentCache
except ImportError:
    IntelligentCache = None

__all__ = ['CacheManager', 'CacheConfig', 'UnifiedCacheManager', 'UnifiedCacheConfig', 'IntelligentCache']
