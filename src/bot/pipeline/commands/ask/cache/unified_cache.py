"""
Unified Cache for ASK Pipeline - Refactored Version

Simplified wrapper around core cache_service for pipeline stages.
Uses Redis via cache_service; no memory fallback (core handles).
Stage-specific get/set for intent/tools/responses with prefixed keys, TTLs from config.
Merged intelligent features: query normalization, TTL determination.
Deleted overkill like warming, compression, full metrics - use core logging/stats.
Integrates with pipeline: cache keys include correlation_id for isolation.
"""
import asyncio
import hashlib
import json
from typing import Dict, Any, Optional, List
from datetime import datetime

from src.shared.cache.cache_service import get_cache, CacheService  # Core cache
from src.core.config_manager import ConfigManager  # For TTLs
from src.shared.error_handling.logging import get_logger

logger = get_logger(__name__)


class UnifiedCacheConfig:
    """Simple config for ask cache."""
    def __init__(self, enabled=True, use_redis=True, fallback_to_memory=True,
                 max_memory_mb=50, default_ttl=300, key_prefix="ask_pipeline",
                 max_entries=500, cache_intent=True, cache_tools=True,
                 cache_responses=True, async_cache_writes=True,
                 intelligent_ttl=True, cache_analytics=True):
        self.enabled = enabled
        self.use_redis = use_redis
        self.fallback_to_memory = fallback_to_memory
        self.max_memory_mb = max_memory_mb
        self.default_ttl = default_ttl
        self.key_prefix = key_prefix
        self.max_entries = max_entries
        self.cache_intent = cache_intent
        self.cache_tools = cache_tools
        self.cache_responses = cache_responses
        self.async_cache_writes = async_cache_writes
        self.intelligent_ttl = intelligent_ttl
        self.cache_analytics = cache_analytics
        self.ttls = {
            "intent": 600,  # 10 min
            "tools": 180,   # 3 min
            "response": 300 # 5 min
        }


class UnifiedCacheManager:
    """
    Unified cache for ASK pipeline stages using core cache_service.
    Prefix keys with 'ask_pipeline:<stage>:<hash>'; TTLs from config.
    Normalize query for keys; log hits/misses.
    """
    def __init__(self, config: UnifiedCacheConfig = None):
        self.config = config or UnifiedCacheConfig()
        if not self.config.enabled:
            logger.info("Unified cache disabled")
            self.cache = None
            return
        
        self.cache = get_cache()
        self.config_manager = ConfigManager()
        # Override TTLs from core config if available
        self.config.ttls.update(self.config_manager.get_section("cache.ttls", default={}))
        logger.info("Unified cache manager initialized with core cache_service")

    def _normalize_query(self, query: str) -> str:
        """Normalize for key hashing."""
        normalized = ' '.join(query.lower().split())
        replacements = {
            "what's": "what is", "what're": "what are", "can you": "",
            "please": "", "current": "", "latest": "", "right now": "", "today": ""
        }
        for old, new in replacements.items():
            normalized = normalized.replace(old, new)
        return normalized.strip()

    def _generate_key(self, stage: str, query: str, **kwargs) -> str:
        """Generate prefixed key."""
        normalized = self._normalize_query(query)
        key_data = {"stage": stage, "query": normalized, **kwargs}
        key_string = json.dumps(key_data, sort_keys=True)
        key_hash = hashlib.md5(key_string.encode()).hexdigest()[:16]
        return f"{self.config.key_prefix}:{stage}:{key_hash}"

    def _get_ttl(self, stage: str, query: str) -> float:
        """Get TTL for stage/query."""
        query_lower = query.lower()
        if stage == "tools" and any(word in query_lower for word in ['price', 'quote']):
            return 60  # Real-time
        if stage == "tools" and any(word in query_lower for word in ['rsi', 'macd']):
            return 180  # TA
        return self.config.ttls.get(stage, self.config.default_ttl)

    async def get(self, stage: str, query: str, correlation_id: str = "", **kwargs) -> Optional[Dict[str, Any]]:
        """Get from cache for stage."""
        if not self.cache:
            return None
        
        key = self._generate_key(stage, query, correlation_id=correlation_id, **kwargs)
        ttl = self._get_ttl(stage, query)
        
        try:
            data = await self.cache.get(key)
            if data:
                logger.debug(f"Cache hit for {stage}", extra={'key': key[:20], 'correlation_id': correlation_id})
                return data
            logger.debug(f"Cache miss for {stage}", extra={'key': key[:20], 'correlation_id': correlation_id})
            return None
        except Exception as e:
            logger.warning(f"Cache get error for {stage}: {e}", extra={'correlation_id': correlation_id})
            return None

    async def set(self, stage: str, query: str, data: Dict[str, Any], correlation_id: str = "", **kwargs) -> bool:
        """Set in cache for stage."""
        if not self.cache:
            return False
        
        key = self._generate_key(stage, query, correlation_id=correlation_id, **kwargs)
        ttl = self._get_ttl(stage, query)
        
        try:
            success = await self.cache.set(key, data, ttl=ttl)
            if success:
                logger.debug(f"Cached {stage} result", extra={'key': key[:20], 'ttl': ttl, 'correlation_id': correlation_id})
            return success
        except Exception as e:
            logger.warning(f"Cache set error for {stage}: {e}", extra={'correlation_id': correlation_id})
            return False

    async def invalidate(self, stage: Optional[str] = None, correlation_id: str = "") -> int:
        """Invalidate keys (simple prefix delete)."""
        if not self.cache:
            return 0
        
        prefix = f"{self.config.key_prefix}:{stage}:" if stage else f"{self.config.key_prefix}:"
        if correlation_id:
            prefix += f"*{correlation_id}*"  # Approximate
        
        try:
            # Use scan/delete for non-blocking
            count = 0
            cursor = 0
            while True:
                cursor, keys = await self.cache.scan(cursor=cursor, match=prefix, count=100)
                if keys:
                    deleted = await self.cache.delete(*keys)
                    count += deleted
                if cursor == 0:
                    break
            logger.info(f"Invalidated {count} cache keys for {stage or 'all'}")
            return count
        except Exception as e:
            logger.warning(f"Cache invalidate error: {e}")
            return 0

    async def get_stats(self) -> Dict[str, Any]:
        """Get basic stats from core cache."""
        if not self.cache:
            return {"enabled": False}
        
        try:
            stats = await self.cache.get_stats()  # Assume core has stats
            stats["ask_specific"] = {
                "ttls": self.config.ttls,
                "prefix": self.config.key_prefix
            }
            return stats
        except Exception:
            return {"error": "Stats unavailable"}

    # Stage-specific cache methods
    async def get_intent_cache(self, query: str, correlation_id: str = "") -> Optional[Any]:
        """Get cached intent result"""
        return await self.get("intent", query, correlation_id)
    
    async def set_intent_cache(self, query: str, data: Any, correlation_id: str = "") -> bool:
        """Set cached intent result"""
        return await self.set("intent", query, data, correlation_id)
    
    async def get_tools_cache(self, query: str, correlation_id: str = "") -> Optional[Any]:
        """Get cached tools result"""
        return await self.get("tools", query, correlation_id)

    async def set_tools_cache(self, query: str, data: Any, correlation_id: str = "") -> bool:
        """Set cached tools result"""
        return await self.set("tools", query, data, correlation_id)

    # Alias methods for backward compatibility
    async def get_tool_cache(self, query: str, intent_result: Any, correlation_id: str = "") -> Optional[Any]:
        """Get cached tool result (alias for get_tools_cache)"""
        return await self.get_tools_cache(query, correlation_id)

    async def set_tool_cache(self, query: str, intent_result: Any, data: Any, correlation_id: str = "") -> bool:
        """Set cached tool result (alias for set_tools_cache)"""
        return await self.set_tools_cache(query, data, correlation_id)
    
    async def get_response_cache(self, query: str, correlation_id: str = "") -> Optional[Any]:
        """Get cached response result"""
        return await self.get("response", query, correlation_id)
    
    async def set_response_cache(self, query: str, data: Any, correlation_id: str = "") -> bool:
        """Set cached response result"""
        return await self.set("response", query, data, correlation_id)


# Global manager
_unified_cache: Optional[UnifiedCacheManager] = None


def get_unified_cache(config: UnifiedCacheConfig = None) -> UnifiedCacheManager:
    global _unified_cache
    if _unified_cache is None:
        _unified_cache = UnifiedCacheManager(config)
    return _unified_cache


async def setup_unified_cache(config_manager: ConfigManager):
    """Setup cache with core config."""
    config = UnifiedCacheConfig()
    # Load from core
    cache_section = config_manager.get_section("ask_cache", default={})
    config.enabled = cache_section.get("enabled", True)
    config.ttls = cache_section.get("ttls", config.ttls)
    
    manager = get_unified_cache(config)
    logger.info("Unified cache setup complete")
    return manager