"""
Core Pipeline Models
Shared data models for the ASK pipeline to avoid circular imports.
"""

from dataclasses import dataclass
from typing import Optional, Dict, Any, List
from datetime import datetime

@dataclass
class PipelineResult:
    """Result of pipeline execution"""
    success: bool
    response: Optional[str] = None
    embed: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    execution_time: float = 0.0
    correlation_id: str = ""
    intent: Optional[str] = None
    tools_used: list = None
    cache_hit: bool = False
    confidence: float = 0.0
    disclaimer_added: bool = False
    cache_used: bool = False
    fallback_used: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'success': self.success,
            'response': self.response,
            'embed': self.embed,
            'error': self.error,
            'metadata': self.metadata,
            'execution_time': self.execution_time,
            'correlation_id': self.correlation_id
        }

@dataclass
class StageResult:
    """Result from a pipeline stage"""
    stage_name: str
    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    execution_time: Optional[float] = None
    metadata: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'stage_name': self.stage_name,
            'success': self.success,
            'data': self.data,
            'error': self.error,
            'execution_time': self.execution_time,
            'metadata': self.metadata
        }

@dataclass
class ErrorResult:
    """Standardized result from the error coordinator."""
    pipeline_result: PipelineResult
    error_type: str
    error_message: str
    is_retriable: bool = False
    severity: str = "high"
    retry_count: int = 0
    fallback_used: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'pipeline_result': self.pipeline_result.to_dict(),
            'error_type': self.error_type,
            'error_message': self.error_message,
            'is_retriable': self.is_retriable,
            'retry_count': self.retry_count,
            'fallback_used': self.fallback_used
        }
