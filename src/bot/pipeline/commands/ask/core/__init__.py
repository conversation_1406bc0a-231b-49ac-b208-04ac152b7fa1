"""
Core Pipeline Components

This module contains the core components of the refactored ASK pipeline:
- Controller: Main orchestrator
- StageManager: Stage coordination
- ErrorCoordinator: Error handling

These components replace the monolithic pipeline.py with a clean,
modular architecture that follows single responsibility principle.
"""

from .models import PipelineResult, StageResult, ErrorResult
from .controller import AskPipelineController
from .stage_manager import StageManager
from .stage_executor import StageExecutor
from .error_coordinator import ErrorCoordinator

__all__ = [
    'AskPipelineController',
    'PipelineResult',
    'StageManager',
    'StageResult',
    'StageExecutor',
    'ErrorCoordinator',
    'ErrorResult'
]
