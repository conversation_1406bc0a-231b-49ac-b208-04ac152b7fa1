"""
ASK Pipeline Error Coordinator

Centralized error handling for the ASK pipeline. This component is responsible
for catching exceptions from any stage, logging them with comprehensive context,
and generating a user-friendly error response.

Design Principles:
- **Centralization**: All pipeline errors are funneled through this coordinator.
- **User-Centric Responses**: Provide helpful, non-technical error messages.
- **Rich Logging**: Capture detailed context for debugging and analysis.
- **Graceful Degradation**: Attempt to provide a fallback response if possible.
- **Decoupling**: Error handling logic is decoupled from pipeline stages.
"""

import asyncio
import traceback
import time
from typing import Optional, Any, Dict, List, Union
from dataclasses import dataclass
from enum import Enum

from src.core.exceptions import (
    TradingBotBaseException,
    DataProviderError,
    AIServiceError,
    PipelineError,
    ConfigurationError,
    ValidationError
)
from src.shared.error_handling.logging import get_logger
from ..config import AskConfig
from .models import PipelineResult, ErrorResult

logger = get_logger(__name__)

class ErrorType(Enum):
    """Types of errors that can occur in the pipeline"""
    AI_SERVICE_UNAVAILABLE = "ai_service_unavailable"
    AI_RATE_LIMITED = "ai_rate_limited"
    AI_TIMEOUT = "ai_timeout"
    TOOL_EXECUTION_FAILED = "tool_execution_failed"
    NETWORK_ERROR = "network_error"
    CACHE_ERROR = "cache_error"
    VALIDATION_ERROR = "validation_error"
    UNKNOWN_ERROR = "unknown_error"

class ErrorSeverity(Enum):
    """Severity levels for errors"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

# ErrorResult is now imported from models.py

class ErrorCoordinator:
    """Handles all errors occurring within the ASK pipeline."""

    def __init__(self, config: AskConfig):
        """
        Initialize the error coordinator with advanced error handling features.

        Args:
            config: The pipeline configuration.
        """
        self.config = config

        # Enhanced error statistics and monitoring
        self.error_stats = {
            'total_errors': 0,
            'errors_by_type': {},
            'errors_by_severity': {},
            'recovery_success_rate': 0.0,
            'circuit_breaker_trips': 0
        }

        # Circuit breaker states
        self.circuit_breakers = {}
        self.circuit_breaker_config = {
            'failure_threshold': 5,
            'recovery_timeout': 300,  # 5 minutes
            'half_open_max_calls': 3
        }

        # Enhanced error messages with severity levels
        self.error_messages = {
            ErrorType.AI_SERVICE_UNAVAILABLE: {
                ErrorSeverity.LOW: "I'm having a small hiccup with my AI services. Let me try a different approach.",
                ErrorSeverity.MEDIUM: "My AI services are temporarily unavailable, but I can still help with general information.",
                ErrorSeverity.HIGH: "I'm experiencing significant AI connectivity issues. Please try again in a few minutes.",
                ErrorSeverity.CRITICAL: "My AI services are currently down. Please try again later."
            },
            ErrorType.AI_RATE_LIMITED: {
                ErrorSeverity.LOW: "I'm being rate limited. Let me try again in a moment.",
                ErrorSeverity.MEDIUM: "I've hit my rate limit. Please wait a moment before trying again.",
                ErrorSeverity.HIGH: "I'm currently rate limited. Please try again in a few minutes.",
                ErrorSeverity.CRITICAL: "I'm severely rate limited. Please try again later."
            },
            ErrorType.AI_TIMEOUT: {
                ErrorSeverity.LOW: "That took longer than expected. Let me try a faster approach.",
                ErrorSeverity.MEDIUM: "I'm experiencing some delays. Let me try a different method.",
                ErrorSeverity.HIGH: "I'm having timeout issues. Please try again with a simpler request.",
                ErrorSeverity.CRITICAL: "I'm experiencing severe timeouts. Please try again later."
            },
            ErrorType.TOOL_EXECUTION_FAILED: {
                ErrorSeverity.LOW: "One of my tools had an issue, but I can still help you.",
                ErrorSeverity.MEDIUM: "I'm having trouble with some of my tools. Let me try a different approach.",
                ErrorSeverity.HIGH: "Several of my tools are having issues. I'll do my best with what's available.",
                ErrorSeverity.CRITICAL: "My tools are currently unavailable. I can only provide general assistance."
            },
            ErrorType.NETWORK_ERROR: {
                ErrorSeverity.LOW: "I had a small network hiccup. Let me try again.",
                ErrorSeverity.MEDIUM: "I'm experiencing network connectivity issues. Retrying...",
                ErrorSeverity.HIGH: "I'm having significant network problems. Please try again shortly.",
                ErrorSeverity.CRITICAL: "I'm experiencing severe network issues. Please try again later."
            },
            ErrorType.CACHE_ERROR: {
                ErrorSeverity.LOW: "I had a small cache issue, but I can still help you.",
                ErrorSeverity.MEDIUM: "My cache is having issues, but I can work without it.",
                ErrorSeverity.HIGH: "I'm having cache problems, which may slow down my responses.",
                ErrorSeverity.CRITICAL: "My cache system is down, but I can still assist you."
            },
            ErrorType.VALIDATION_ERROR: {
                ErrorSeverity.LOW: "I didn't quite understand that. Could you rephrase your question?",
                ErrorSeverity.MEDIUM: "I'm having trouble understanding your request. Could you be more specific?",
                ErrorSeverity.HIGH: "I can't process that request. Please try rephrasing it differently.",
                ErrorSeverity.CRITICAL: "I can't understand that request format. Please try a different approach."
            },
            ErrorType.UNKNOWN_ERROR: {
                ErrorSeverity.LOW: "I encountered a small issue, but let me try to help anyway.",
                ErrorSeverity.MEDIUM: "I ran into an unexpected problem. Let me try a different approach.",
                ErrorSeverity.HIGH: "I encountered an unexpected error. Please try again.",
                ErrorSeverity.CRITICAL: "I'm experiencing unexpected technical difficulties. Please try again later."
            }
        }

        # Error classification patterns
        self.error_patterns = {
            'rate_limit': ['rate limit', 'quota exceeded', 'too many requests'],
            'timeout': ['timeout', 'timed out', 'connection timeout'],
            'unavailable': ['service unavailable', 'connection refused', 'not available'],
            'network': ['network error', 'connection error', 'dns resolution'],
            'validation': ['validation error', 'invalid input', 'malformed request']
        }

        logger.info("✅ Enhanced error coordinator initialized with circuit breaker and advanced error handling")

    async def handle_error(
        self,
        exception: Exception,
        stage: str,
        query: str,
        user_id: Optional[str] = None,
        correlation_id: str = "",
        execution_time: float = 0.0,
        audit_logger: Optional[Any] = None,
        retry_count: int = 0
    ) -> PipelineResult:
        """
        Central entry point for handling any exception from the pipeline with advanced features.

        Args:
            exception: The exception that was raised.
            stage: The name of the pipeline stage where the error occurred.
            query: The original user query.
            user_id: The ID of the user who made the request.
            correlation_id: The unique ID for the request.
            execution_time: The time elapsed before the error occurred.
            audit_logger: The audit logger instance for this request.
            retry_count: Number of times this operation has been retried.

        Returns:
            A PipelineResult object containing a user-friendly error response.
        """
        start_time = time.time()

        # Update error statistics
        self.error_stats['total_errors'] += 1

        # Classify the error using advanced classification
        error_type = self._classify_error(exception, stage, retry_count)
        severity = self._determine_severity(error_type, exception, stage, retry_count, execution_time)

        # Update error statistics by type and severity
        self.error_stats['errors_by_type'][error_type.value] = \
            self.error_stats['errors_by_type'].get(error_type.value, 0) + 1
        self.error_stats['errors_by_severity'][severity.value] = \
            self.error_stats['errors_by_severity'].get(severity.value, 0) + 1

        # Check circuit breaker
        circuit_breaker_key = f"{stage}_{error_type.value}"
        if self._should_circuit_break(circuit_breaker_key, severity):
            return await self._handle_circuit_break(circuit_breaker_key, stage, query, correlation_id)

        # Determine if retry is recommended
        retry_recommended = self._should_retry(error_type, severity, retry_count)

        # Generate user-friendly response
        user_response = self._generate_user_message(error_type, severity, retry_count)

        # Log the error with detailed context
        log_context = {
            'correlation_id': correlation_id,
            'user_id': user_id,
            'stage': stage,
            'error_type': error_type.value,
            'error_message': str(exception),
            'severity': severity.value,
            'retry_count': retry_count,
            'execution_time': execution_time,
            'query': query,
            'retry_recommended': retry_recommended
        }
        self._log_error(exception, error_type, severity, log_context)

        # Audit the error
        if audit_logger:
            try:
                audit_logger.log_error(
                    exception,
                    context={
                        "stage": stage,
                        "query": query,
                        "user_id": user_id,
                        "error_type": error_type.value,
                        "severity": severity.value,
                        "retry_recommended": retry_recommended
                    }
                )
                audit_logger.log_decision(
                    decision_point="error_handling_strategy",
                    options=["generate_user_message", "circuit_break", "retry"],
                    chosen_option="generate_user_message" if not self._should_circuit_break(circuit_breaker_key, severity) else "circuit_break",
                    reasoning=f"Handled {error_type.value} error in stage '{stage}' with severity {severity.value}",
                    confidence=1.0
                )
            except Exception as audit_err:
                logger.error(f"Failed to write to audit log during error handling: {audit_err}", extra=log_context)

        # Create the final pipeline result
        result = PipelineResult(
            success=False,
            response=user_response,
            error=f"{error_type.value}: {str(exception)}",
            execution_time=time.time() - start_time,
            correlation_id=correlation_id,
            intent="error"
        )

        # Trigger alerts for high-severity errors
        if severity in [ErrorSeverity.CRITICAL, ErrorSeverity.HIGH]:
            await self._trigger_alert(log_context)

        return result

    def _classify_error(self, exception: Exception, stage: str, retry_count: int) -> ErrorType:
        """Classify an error based on exception type, message, and context"""
        exception_str = str(exception).lower()
        exception_type = type(exception).__name__.lower()

        # Check for specific patterns in error message
        for pattern_type, patterns in self.error_patterns.items():
            if any(pattern in exception_str for pattern in patterns):
                if pattern_type == 'rate_limit':
                    return ErrorType.AI_RATE_LIMITED
                elif pattern_type == 'timeout':
                    return ErrorType.AI_TIMEOUT
                elif pattern_type == 'unavailable':
                    return ErrorType.AI_SERVICE_UNAVAILABLE
                elif pattern_type == 'network':
                    return ErrorType.NETWORK_ERROR
                elif pattern_type == 'validation':
                    return ErrorType.VALIDATION_ERROR

        # Check exception types and stage context
        if 'timeout' in exception_type:
            return ErrorType.AI_TIMEOUT
        elif 'connection' in exception_type or 'network' in exception_type:
            return ErrorType.NETWORK_ERROR
        elif 'validation' in exception_type or 'value' in exception_type:
            return ErrorType.VALIDATION_ERROR
        elif 'cache' in exception_type:
            return ErrorType.CACHE_ERROR
        elif stage == 'tools':
            return ErrorType.TOOL_EXECUTION_FAILED
        elif stage in ['intent', 'response']:
            return ErrorType.AI_SERVICE_UNAVAILABLE

        return ErrorType.UNKNOWN_ERROR

    def _determine_severity(
        self,
        error_type: ErrorType,
        exception: Exception,
        stage: str,
        retry_count: int,
        execution_time: float
    ) -> ErrorSeverity:
        """Determine error severity based on type, context, and retry count"""
        # High retry count increases severity
        if retry_count >= 3:
            return ErrorSeverity.CRITICAL
        elif retry_count >= 2:
            return ErrorSeverity.HIGH

        # Base severity by error type
        base_severity = {
            ErrorType.AI_SERVICE_UNAVAILABLE: ErrorSeverity.HIGH,
            ErrorType.AI_RATE_LIMITED: ErrorSeverity.MEDIUM,
            ErrorType.AI_TIMEOUT: ErrorSeverity.MEDIUM,
            ErrorType.TOOL_EXECUTION_FAILED: ErrorSeverity.LOW,
            ErrorType.NETWORK_ERROR: ErrorSeverity.MEDIUM,
            ErrorType.CACHE_ERROR: ErrorSeverity.LOW,
            ErrorType.VALIDATION_ERROR: ErrorSeverity.LOW,
            ErrorType.UNKNOWN_ERROR: ErrorSeverity.MEDIUM
        }.get(error_type, ErrorSeverity.MEDIUM)

        # Adjust based on execution time (longer = more severe)
        if execution_time > 30:
            if base_severity == ErrorSeverity.LOW:
                return ErrorSeverity.MEDIUM
            elif base_severity == ErrorSeverity.MEDIUM:
                return ErrorSeverity.HIGH

        return base_severity

    def _should_retry(self, error_type: ErrorType, severity: ErrorSeverity, retry_count: int) -> bool:
        """Determine if retry is recommended"""
        # Don't retry if we've already tried too many times
        if retry_count >= 3:
            return False

        # Don't retry critical errors
        if severity == ErrorSeverity.CRITICAL:
            return False

        # Don't retry validation errors
        if error_type == ErrorType.VALIDATION_ERROR:
            return False

        # Retry eligible error types
        retry_eligible = {
            ErrorType.AI_TIMEOUT,
            ErrorType.NETWORK_ERROR,
            ErrorType.AI_RATE_LIMITED,
            ErrorType.TOOL_EXECUTION_FAILED,
            ErrorType.AI_SERVICE_UNAVAILABLE
        }

        return error_type in retry_eligible

    def _should_circuit_break(self, circuit_breaker_key: str, severity: ErrorSeverity) -> bool:
        """Check if circuit breaker should trip"""
        if circuit_breaker_key not in self.circuit_breakers:
            self.circuit_breakers[circuit_breaker_key] = {
                'failure_count': 0,
                'last_failure_time': 0,
                'state': 'closed',  # closed, open, half_open
                'half_open_attempts': 0
            }

        breaker = self.circuit_breakers[circuit_breaker_key]
        current_time = time.time()

        # Update failure count for high/critical errors
        if severity in [ErrorSeverity.HIGH, ErrorSeverity.CRITICAL]:
            breaker['failure_count'] += 1
            breaker['last_failure_time'] = current_time

        # Check if we should trip the breaker
        if (breaker['state'] == 'closed' and
            breaker['failure_count'] >= self.circuit_breaker_config['failure_threshold']):
            breaker['state'] = 'open'
            self.error_stats['circuit_breaker_trips'] += 1
            logger.warning(f"Circuit breaker tripped for {circuit_breaker_key}")
            return True

        # Check if we should move from open to half-open
        if (breaker['state'] == 'open' and
            current_time - breaker['last_failure_time'] > self.circuit_breaker_config['recovery_timeout']):
            breaker['state'] = 'half_open'
            breaker['half_open_attempts'] = 0
            logger.info(f"Circuit breaker moving to half-open for {circuit_breaker_key}")

        # Check if we should close the breaker from half-open
        if breaker['state'] == 'half_open':
            breaker['half_open_attempts'] += 1
            if breaker['half_open_attempts'] >= self.circuit_breaker_config['half_open_max_calls']:
                if severity in [ErrorSeverity.HIGH, ErrorSeverity.CRITICAL]:
                    # Still failing, go back to open
                    breaker['state'] = 'open'
                    breaker['last_failure_time'] = current_time
                    return True
                else:
                    # Success, close the breaker
                    breaker['state'] = 'closed'
                    breaker['failure_count'] = 0
                    logger.info(f"Circuit breaker closed for {circuit_breaker_key}")

        return breaker['state'] == 'open'

    async def _handle_circuit_break(
        self,
        circuit_breaker_key: str,
        stage: str,
        query: str,
        correlation_id: str
    ) -> PipelineResult:
        """Handle circuit breaker trip"""
        logger.warning(f"Circuit breaker active for {circuit_breaker_key}",
                      extra={'correlation_id': correlation_id, 'stage': stage})

        return PipelineResult(
            success=False,
            response="I'm temporarily limiting requests to this service due to repeated failures. Please try again in a few minutes.",
            error="circuit_breaker_open",
            execution_time=0.0,
            correlation_id=correlation_id,
            intent="error"
        )

    def _generate_user_message(self, error_type: ErrorType, severity: ErrorSeverity, retry_count: int) -> str:
        """Generate user-friendly error message"""
        try:
            # Get base message for error type and severity
            base_message = self.error_messages.get(error_type, {}).get(
                severity,
                "I encountered an unexpected issue. Let me try to help you anyway."
            )

            # Add context-specific information
            if retry_count > 0:
                if retry_count == 1:
                    base_message += " Let me try that again..."
                elif retry_count == 2:
                    base_message += " Still having issues. Trying a different approach..."
                else:
                    base_message += " I'm having persistent difficulties. Let me try one more time..."

            return base_message

        except Exception as e:
            logger.warning(f"Error generating user message: {e}")
            return "I encountered an issue while processing your request. Please try again."

    def _log_error(self, exception: Exception, error_type: ErrorType, severity: ErrorSeverity, log_context: Dict[str, Any]):
        """Log error with appropriate level based on severity"""
        if severity == ErrorSeverity.CRITICAL:
            logger.critical(f"Critical error in {log_context['stage']}: {exception}", extra=log_context)
        elif severity == ErrorSeverity.HIGH:
            logger.error(f"High severity error in {log_context['stage']}: {exception}", extra=log_context)
        elif severity == ErrorSeverity.MEDIUM:
            logger.warning(f"Medium severity error in {log_context['stage']}: {exception}", extra=log_context)
        else:
            logger.info(f"Low severity error in {log_context['stage']}: {exception}", extra=log_context)

    async def handle_stage_error(
        self,
        exception: Exception,
        stage: str,
        correlation_id: str,
        query: str = "",
        **kwargs
    ) -> PipelineResult:
        """
        Handles errors that occur within a specific pipeline stage.
        This is a convenience wrapper around `handle_error`.
        """
        return await self.handle_error(
            exception=exception,
            stage=stage,
            query=query,
            correlation_id=correlation_id,
            **kwargs
        )

    async def handle_pipeline_error(
        self,
        exception: Exception,
        query: str,
        user_id: Optional[str] = None,
        correlation_id: str = "",
        execution_time: float = 0.0,
        audit_logger: Optional[Any] = None
    ) -> PipelineResult:
        """
        Handles errors that occur at the controller level (outside a specific stage).
        """
        return await self.handle_error(
            exception=exception,
            stage="pipeline_controller",
            query=query,
            user_id=user_id,
            correlation_id=correlation_id,
            execution_time=execution_time,
            audit_logger=audit_logger
        )

    async def handle_stage_timeout(
        self,
        stage: str,
        timeout_seconds: float,
        correlation_id: str,
        **kwargs
    ) -> PipelineResult:
        """
        Handles timeout errors for a specific stage.
        """
        timeout_exception = asyncio.TimeoutError(f"Stage '{stage}' timed out after {timeout_seconds} seconds.")
        return await self.handle_error(
            exception=timeout_exception,
            stage=stage,
            correlation_id=correlation_id,
            **kwargs
        )

    def _get_user_facing_error_message(self, exception: Exception) -> str:
        """
        Legacy method for backward compatibility.
        Now delegates to the enhanced error message generation.
        """
        error_type = self._classify_error(exception, "unknown", 0)
        severity = self._determine_severity(error_type, exception, "unknown", 0, 0.0)
        return self._generate_user_message(error_type, severity, 0)

    def _is_retriable(self, exception: Exception) -> bool:
        """

        Determines if an error is transient and the operation could be retried.

        Args:
            exception: The exception to assess.

        Returns:
            True if the error is retriable, False otherwise.
        """
        # Timeouts are often transient
        if isinstance(exception, asyncio.TimeoutError):
            return True
        
        # Data provider issues can be temporary
        if isinstance(exception, DataProviderError):
            # Specific provider errors might indicate retriable conditions
            # e.g., if the error message contains "503 Service Unavailable"
            if "unavailable" in str(exception).lower() or "503" in str(exception):
                return True
            return False

        # AI service issues can also be temporary
        if isinstance(exception, AIServiceError):
            if "overloaded" in str(exception).lower() or "rate limit" in str(exception).lower():
                return True
            return False

        # Validation and configuration errors are not retriable
        if isinstance(exception, (ValidationError, ConfigurationError, PipelineError)):
            return False

        return False

    def _assess_severity(self, exception: Exception) -> str:
        """
        Legacy method for backward compatibility.
        Now delegates to the enhanced severity determination.
        """
        error_type = self._classify_error(exception, "unknown", 0)
        severity = self._determine_severity(error_type, exception, "unknown", 0, 0.0)
        return severity.value

    async def _trigger_alert(self, log_context: Dict[str, Any]):
        """
        Triggers an alert for high-severity errors.
        (Placeholder for integration with a monitoring/alerting system like PagerDuty, Sentry, etc.)

        Args:
            log_context: The detailed context of the error.
        """
        severity = log_context.get('severity', 'high')
        error_type = log_context.get('error_type', 'UnknownError')
        stage = log_context.get('stage', 'unknown')
        
        alert_message = (
            f"🚨 **{severity.upper()} Severity Alert** 🚨\n"
            f"**Error**: `{error_type}` in stage `{stage}`\n"
            f"**Message**: {log_context.get('error_message')}\n"
            f"**Correlation ID**: `{log_context.get('correlation_id')}`"
        )
        
        # In a real system, this would call an external service.
        # For now, we just log it with critical level.
        logger.critical(f"ALERT_TRIGGERED: {alert_message}", extra=log_context)

        # Example of how you might integrate with Sentry
        # try:
        #     import sentry_sdk
        #     with sentry_sdk.push_scope() as scope:
        #         for key, value in log_context.items():
        #             scope.set_tag(key, value)
        #         sentry_sdk.capture_message(alert_message, level='error')
        # except ImportError:
        #     pass # Sentry not installed
        # except Exception as e:
        #     logger.error(f"Failed to send alert to Sentry: {e}")

    def get_error_summary(self, exception: Exception, stage: str) -> Dict[str, Any]:
        """
        Provides a structured summary of an error without handling it.

        Args:
            exception: The exception that occurred.
            stage: The stage where the error happened.

        Returns:
            A dictionary containing a summary of the error.
        """
        return {
            "error_type": type(exception).__name__,
            "error_message": str(exception),
            "stage": stage,
            "is_retriable": self._is_retriable(exception),
            "severity": self._assess_severity(exception),
            "traceback": traceback.format_exc()
        }

    def classify_error(self, exception: Exception) -> str:
        """
        Classifies an exception into a high-level category.

        Args:
            exception: The exception to classify.

        Returns:
            A string representing the error category.
        """
        if isinstance(exception, ConfigurationError):
            return "CONFIGURATION"
        if isinstance(exception, DataProviderError):
            return "DATA_SOURCE"
        if isinstance(exception, AIServiceError):
            return "AI_SERVICE"
        if isinstance(exception, ValidationError):
            return "USER_INPUT"
        if isinstance(exception, asyncio.TimeoutError):
            return "TIMEOUT"
        if isinstance(exception, PipelineError):
            return "PIPELINE_LOGIC"
        
        return "UNHANDLED_EXCEPTION"

    async def generate_error_report(self, exception: Exception, context: Dict[str, Any]) -> Dict[str, Any]:
        """
g
        Generates a detailed, structured report for a given error, suitable for
        logging or sending to an external monitoring service.

        Args:
            exception: The exception that occurred.
            context: A dictionary containing the context of the error (e.g., query, user_id).

        Returns:
            A dictionary containing the detailed error report.
        """
        report = {
            "timestamp": asyncio.get_event_loop().time(),
            "correlation_id": context.get("correlation_id", "N/A"),
            "user_id": context.get("user_id"),
            "stage": context.get("stage", "unknown"),
            "query": context.get("query"),
            "error_classification": self.classify_error(exception),
            "error_details": {
                "type": type(exception).__name__,
                "message": str(exception),
                "is_retriable": self._is_retriable(exception),
                "severity": self._assess_severity(exception),
            },
            "traceback": traceback.format_exc().splitlines(),
            "execution_context": {
                "execution_time": context.get("execution_time"),
                # Add other relevant context here, e.g., cache hits, tools used before failure
            }
        }
        return report

    def get_error_stats(self) -> Dict[str, Any]:
        """Get current error statistics"""
        return self.error_stats.copy()

    def reset_circuit_breaker(self, circuit_breaker_key: str) -> bool:
        """Manually reset a circuit breaker"""
        if circuit_breaker_key in self.circuit_breakers:
            self.circuit_breakers[circuit_breaker_key] = {
                'failure_count': 0,
                'last_failure_time': 0,
                'state': 'closed',
                'half_open_attempts': 0
            }
            logger.info(f"Circuit breaker manually reset for {circuit_breaker_key}")
            return True
        return False

    def get_circuit_breaker_status(self) -> Dict[str, Any]:
        """Get status of all circuit breakers"""
        return {
            key: {
                'state': breaker['state'],
                'failure_count': breaker['failure_count'],
                'last_failure_time': breaker['last_failure_time'],
                'half_open_attempts': breaker.get('half_open_attempts', 0)
            }
            for key, breaker in self.circuit_breakers.items()
        }

    def reset_error_stats(self):
        """Reset error statistics"""
        self.error_stats = {
            'total_errors': 0,
            'errors_by_type': {},
            'errors_by_severity': {},
            'recovery_success_rate': 0.0,
            'circuit_breaker_trips': 0
        }
        logger.info("Error statistics reset")

