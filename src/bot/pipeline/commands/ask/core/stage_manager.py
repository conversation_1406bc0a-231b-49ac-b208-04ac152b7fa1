"""
Stage Manager

Coordinates the execution of all pipeline stages in the correct order.
Handles stage transitions, data flow, and stage-specific error handling.

Design Principles:
- Single responsibility: Stage coordination only
- Clean interfaces: Well-defined stage contracts
- Error delegation: Passes errors to error coordinator
- Caching integration: Manages stage-level caching
"""

from typing import Optional, Any, TYPE_CHECKING
from dataclasses import dataclass

from src.shared.error_handling.logging import get_logger
from .error_coordinator import ErrorCoordinator
from .stage_executor import StageExecutor
from ..cache import CacheManager
from ..config import AskConfig

if TYPE_CHECKING:
    from .controller import PipelineResult

logger = get_logger(__name__)

@dataclass
class StageResult:
    """Result from a pipeline stage"""
    success: bool
    data: Any = None
    error: Optional[str] = None
    execution_time: float = 0.0
    cache_hit: bool = False

class StageManager:
    """
    Manages the execution of pipeline stages
    
    Coordinates the flow between:
    1. Intent Detection
    2. Tool Orchestration  
    3. Response Generation
    4. Discord Formatting
    """

    def __init__(self, 
                 config: AskConfig,
                 cache_manager: <PERSON><PERSON><PERSON><PERSON><PERSON>,
                 error_coordinator: <PERSON>rrorCoordinator):
        """
        Initialize stage manager with dependencies
        
        Args:
            config: Pipeline configuration
            cache_manager: Cache management component
            error_coordinator: Error handling component
        """
        self.config = config
        self.cache_manager = cache_manager
        self.error_coordinator = error_coordinator

        # Initialize stage executor
        self.stage_executor = StageExecutor(config, cache_manager, error_coordinator)

    async def execute_pipeline(
        self,
        query: str,
        user_id: Optional[str] = None,
        correlation_id: str = "",
        audit_logger: Optional[Any] = None
    ) -> 'PipelineResult':
        """
        Execute the complete pipeline through all stages
        
        Args:
            query: User query
            user_id: Discord user ID
            correlation_id: Request correlation ID
            audit_logger: Audit logging component
            
        Returns:
            PipelineResult with final response
        """
        # Stage 1: Intent Detection
        intent_result = await self.stage_executor.execute_intent_detection(
            query, correlation_id, audit_logger
        )

        # Stage 2: Tool Orchestration
        tool_result = await self.stage_executor.execute_tool_orchestration(
            query, intent_result, correlation_id, audit_logger
        )

        # Stage 3: Response Generation
        response_result = await self.stage_executor.execute_response_generation(
            query, intent_result, tool_result, correlation_id, audit_logger
        )

        # Stage 4: Discord Formatting
        formatted_response = await self.stage_executor.execute_discord_formatting(
            response_result, correlation_id, audit_logger
        )
        
        # Build final result
        from .controller import PipelineResult
        return PipelineResult(
            success=True,
            response=getattr(formatted_response, 'text', None) or (
                str(formatted_response.embed) if hasattr(formatted_response, 'embed') and formatted_response.embed 
                else "Response generated successfully"
            ),
            embed=getattr(formatted_response, 'embed', None),
            intent=intent_result.intent,
            tools_used=getattr(tool_result, 'tools_used', []) if tool_result else [],
            cache_hit=getattr(tool_result, 'cache_hit', False) if tool_result else False,
            confidence=getattr(tool_result, 'confidence', 0.0) if tool_result else 0.0
        )
