"""
ASK Pipeline Observability Module
Provides monitoring, logging, and health checking for the ASK pipeline.
"""

# Import structured logger from core monitoring
from src.core.monitoring.logger import get_structured_logger, set_correlation_id, clear_correlation_id, LogContext, LogCategory

# Import actual implementations from core monitoring
try:
    from src.core.monitoring.tracer import get_tracer, SpanStatus
    # Create SpanKind as alias for SpanStatus for compatibility
    SpanKind = SpanStatus
except ImportError:
    def get_tracer():
        """Fallback tracer"""
        return None

    class SpanKind:
        """Fallback span kind"""
        pass
except Exception:
    def get_tracer():
        """Fallback tracer"""
        return None

    class SpanKind:
        """Fallback span kind"""
        pass

try:
    from src.core.monitoring.metrics import get_metrics_collector
except ImportError:
    def get_metrics_collector():
        """Fallback metrics collector"""
        return None

try:
    from src.core.monitoring.health_monitor import get_health_checker
except ImportError:
    def get_health_checker():
        """Fallback health checker"""
        return None

try:
    from .health_checker import HealthChecker
except ImportError:
    HealthChecker = None

try:
    from .log_analyzer import LogAnalyzer
except ImportError:
    LogAnalyzer = None

def get_performance_monitor():
    """Placeholder performance monitor"""
    return None

# All classes are now imported from core monitoring modules

__all__ = [
    'get_structured_logger', 'HealthChecker', 'LogAnalyzer',
    'get_metrics_collector', 'get_performance_monitor', 'get_health_checker',
    'get_tracer', 'LogContext', 'LogCategory', 'SpanKind',
    'set_correlation_id', 'clear_correlation_id'
]