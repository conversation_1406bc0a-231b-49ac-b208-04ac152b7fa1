"""
Log Aggregation and Analysis Utilities for ASK Pipeline

Provides comprehensive log analysis capabilities:
- Log parsing and aggregation from multiple sources
- Performance metrics extraction
- Error pattern detection
- User behavior analysis
- Audit trail analysis
- Automated reporting and alerting
"""

import json
import re
import gzip
import logging
from typing import Dict, List, Any, Optional, Tuple, Iterator
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from pathlib import Path
from collections import defaultdict, Counter
import statistics
from enum import Enum

logger = logging.getLogger(__name__)

class AnalysisTimeframe(Enum):
    """Time frames for log analysis"""
    HOUR = "1h"
    DAY = "1d"
    WEEK = "7d"
    MONTH = "30d"

class AlertSeverity(Enum):
    """Alert severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class LogEntry:
    """Parsed log entry"""
    timestamp: datetime
    level: str
    category: str
    logger: str
    correlation_id: str
    message: str
    context: Dict[str, Any] = field(default_factory=dict)
    extra: Dict[str, Any] = field(default_factory=dict)

@dataclass
class LogMetrics:
    """Log analysis metrics"""
    timeframe: AnalysisTimeframe
    total_entries: int
    error_count: int
    warning_count: int
    info_count: int
    debug_count: int
    unique_users: int
    unique_correlation_ids: int
    average_response_time: float
    error_rate: float
    top_errors: List[Tuple[str, int]] = field(default_factory=list)
    top_users: List[Tuple[str, int]] = field(default_factory=list)
    performance_issues: List[str] = field(default_factory=list)

@dataclass
class Alert:
    """Log analysis alert"""
    severity: AlertSeverity
    category: str
    message: str
    timestamp: datetime
    count: int
    pattern: str
    recommendations: List[str] = field(default_factory=list)

class LogAnalyzer:
    """Comprehensive log analysis system"""
    
    def __init__(self, log_directory: str = "logs"):
        self.log_directory = Path(log_directory)
        self.log_entries: List[LogEntry] = []
        self.metrics: Dict[AnalysisTimeframe, LogMetrics] = {}
        self.alerts: List[Alert] = []
        
        # Log parsing patterns
        self.log_patterns = {
            'structured': re.compile(r'(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d+Z)\s+(\w+)\s+(\w+)\s+(\w+)\s+(\w+)\s+(.+)'),
            'simple': re.compile(r'(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})\s+(\w+)\s+(.+)'),
            'json': re.compile(r'\{.*\}')
        }
        
        # Error patterns
        self.error_patterns = [
            r'ERROR|CRITICAL|FATAL',
            r'Exception|Error|Failed',
            r'Timeout|Connection.*failed',
            r'Rate.*limit|Throttle',
            r'Memory.*error|OutOfMemory'
        ]
        
        # Performance patterns
        self.performance_patterns = [
            r'response_time.*(\d+\.\d+)',
            r'execution_time.*(\d+\.\d+)',
            r'duration.*(\d+\.\d+)',
            r'latency.*(\d+\.\d+)'
        ]
    
    def load_logs(self, timeframe: AnalysisTimeframe = AnalysisTimeframe.DAY) -> int:
        """Load logs from the specified timeframe"""
        end_time = datetime.utcnow()
        start_time = self._get_start_time(end_time, timeframe)
        
        loaded_entries = 0
        
        # Load from various log sources
        for log_file in self._get_log_files():
            entries = self._parse_log_file(log_file, start_time, end_time)
            self.log_entries.extend(entries)
            loaded_entries += len(entries)
        
        # Sort by timestamp
        self.log_entries.sort(key=lambda x: x.timestamp)
        
        logger.debug(f"Loaded {loaded_entries} log entries from {timeframe.value}")
        return loaded_entries
    
    def _get_start_time(self, end_time: datetime, timeframe: AnalysisTimeframe) -> datetime:
        """Get start time based on timeframe"""
        if timeframe == AnalysisTimeframe.HOUR:
            return end_time - timedelta(hours=1)
        elif timeframe == AnalysisTimeframe.DAY:
            return end_time - timedelta(days=1)
        elif timeframe == AnalysisTimeframe.WEEK:
            return end_time - timedelta(weeks=1)
        elif timeframe == AnalysisTimeframe.MONTH:
            return end_time - timedelta(days=30)
        else:
            return end_time - timedelta(days=1)
    
    def _get_log_files(self) -> List[Path]:
        """Get list of log files to analyze"""
        log_files = []
        
        if self.log_directory.exists():
            # Find all log files
            for pattern in ['*.log', '*.log.gz', '*.json']:
                log_files.extend(self.log_directory.rglob(pattern))
        
        return sorted(log_files, key=lambda x: x.stat().st_mtime, reverse=True)
    
    def _parse_log_file(self, log_file: Path, start_time: datetime, end_time: datetime) -> List[LogEntry]:
        """Parse a single log file"""
        entries = []
        
        try:
            # Handle compressed files
            if log_file.suffix == '.gz':
                with gzip.open(log_file, 'rt', encoding='utf-8') as f:
                    content = f.read()
            else:
                with open(log_file, 'r', encoding='utf-8') as f:
                    content = f.read()
            
            lines = content.split('\n')
            
            for line in lines:
                if not line.strip():
                    continue
                
                entry = self._parse_log_line(line)
                if entry and start_time <= entry.timestamp <= end_time:
                    entries.append(entry)
        
        except Exception as e:
            logger.error(f"Error parsing log file {log_file}: {e}")
        
        return entries
    
    def _parse_log_line(self, line: str) -> Optional[LogEntry]:
        """Parse a single log line"""
        try:
            # Try structured log format first
            match = self.log_patterns['structured'].match(line)
            if match:
                timestamp_str, level, category, logger, correlation_id, message = match.groups()
                timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                
                return LogEntry(
                    timestamp=timestamp,
                    level=level,
                    category=category,
                    logger=logger,
                    correlation_id=correlation_id,
                    message=message
                )
            
            # Try simple format
            match = self.log_patterns['simple'].match(line)
            if match:
                timestamp_str, level, message = match.groups()
                timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
                
                return LogEntry(
                    timestamp=timestamp,
                    level=level,
                    category='general',
                    logger='unknown',
                    correlation_id='unknown',
                    message=message
                )
            
            # Try JSON format
            if self.log_patterns['json'].search(line):
                try:
                    data = json.loads(line)
                    return LogEntry(
                        timestamp=datetime.fromisoformat(data.get('timestamp', '1970-01-01T00:00:00Z').replace('Z', '+00:00')),
                        level=data.get('level', 'INFO'),
                        category=data.get('category', 'general'),
                        logger=data.get('logger', 'unknown'),
                        correlation_id=data.get('correlation_id', 'unknown'),
                        message=data.get('message', ''),
                        context=data.get('context', {}),
                        extra=data.get('extra', {})
                    )
                except json.JSONDecodeError:
                    pass
        
        except Exception as e:
            # Skip malformed lines
            pass
        
        return None
    
    def analyze_logs(self, timeframe: AnalysisTimeframe = AnalysisTimeframe.DAY) -> LogMetrics:
        """Analyze loaded logs and generate metrics"""
        if not self.log_entries:
            self.load_logs(timeframe)
        
        # Count log levels
        level_counts = Counter(entry.level for entry in self.log_entries)
        
        # Count unique users and correlation IDs
        unique_users = len(set(entry.context.get('user_id', '') for entry in self.log_entries if entry.context.get('user_id')))
        unique_correlation_ids = len(set(entry.correlation_id for entry in self.log_entries))
        
        # Calculate response times
        response_times = []
        for entry in self.log_entries:
            if 'response_time' in entry.context:
                try:
                    response_times.append(float(entry.context['response_time']))
                except (ValueError, TypeError):
                    pass
        
        average_response_time = statistics.mean(response_times) if response_times else 0.0
        
        # Calculate error rate
        total_entries = len(self.log_entries)
        error_count = level_counts.get('ERROR', 0) + level_counts.get('CRITICAL', 0) + level_counts.get('FATAL', 0)
        error_rate = (error_count / total_entries * 100) if total_entries > 0 else 0.0
        
        # Find top errors
        error_messages = [entry.message for entry in self.log_entries if entry.level in ['ERROR', 'CRITICAL', 'FATAL']]
        top_errors = Counter(error_messages).most_common(10)
        
        # Find top users
        user_counts = Counter(entry.context.get('user_id', '') for entry in self.log_entries if entry.context.get('user_id'))
        top_users = user_counts.most_common(10)
        
        # Find performance issues
        performance_issues = self._identify_performance_issues()
        
        metrics = LogMetrics(
            timeframe=timeframe,
            total_entries=total_entries,
            error_count=error_count,
            warning_count=level_counts.get('WARNING', 0),
            info_count=level_counts.get('INFO', 0),
            debug_count=level_counts.get('DEBUG', 0),
            unique_users=unique_users,
            unique_correlation_ids=unique_correlation_ids,
            average_response_time=average_response_time,
            error_rate=error_rate,
            top_errors=top_errors,
            top_users=top_users,
            performance_issues=performance_issues
        )
        
        self.metrics[timeframe] = metrics
        return metrics
    
    def _identify_performance_issues(self) -> List[str]:
        """Identify performance issues from logs"""
        issues = []
        
        # Check for high response times
        high_response_times = [entry for entry in self.log_entries 
                             if entry.context.get('response_time', 0) > 5.0]
        if high_response_times:
            issues.append(f"High response times detected: {len(high_response_times)} entries > 5s")
        
        # Check for memory issues
        memory_errors = [entry for entry in self.log_entries 
                        if 'memory' in entry.message.lower() and 'error' in entry.message.lower()]
        if memory_errors:
            issues.append(f"Memory issues detected: {len(memory_errors)} entries")
        
        # Check for timeout errors
        timeout_errors = [entry for entry in self.log_entries 
                         if 'timeout' in entry.message.lower()]
        if timeout_errors:
            issues.append(f"Timeout errors detected: {len(timeout_errors)} entries")
        
        return issues
    
    def generate_alerts(self) -> List[Alert]:
        """Generate alerts based on log analysis"""
        alerts = []
        
        if not self.metrics:
            return alerts
        
        # Get latest metrics
        latest_metrics = max(self.metrics.values(), key=lambda x: x.total_entries)
        
        # High error rate alert
        if latest_metrics.error_rate > 5.0:
            alerts.append(Alert(
                severity=AlertSeverity.HIGH,
                category='error_rate',
                message=f"High error rate detected: {latest_metrics.error_rate:.2f}%",
                timestamp=datetime.utcnow(),
                count=int(latest_metrics.error_count),
                pattern='error_rate_high',
                recommendations=[
                    'Investigate top error messages',
                    'Check system health',
                    'Review recent deployments'
                ]
            ))
        
        # Performance issues alert
        if latest_metrics.average_response_time > 2.0:
            alerts.append(Alert(
                severity=AlertSeverity.MEDIUM,
                category='performance',
                message=f"High average response time: {latest_metrics.average_response_time:.2f}s",
                timestamp=datetime.utcnow(),
                count=1,
                pattern='response_time_high',
                recommendations=[
                    'Optimize slow queries',
                    'Check resource utilization',
                    'Review caching strategy'
                ]
            ))
        
        # Memory issues alert
        memory_issues = [issue for issue in latest_metrics.performance_issues if 'memory' in issue.lower()]
        if memory_issues:
            alerts.append(Alert(
                severity=AlertSeverity.HIGH,
                category='memory',
                message=f"Memory issues detected: {len(memory_issues)} issues",
                timestamp=datetime.utcnow(),
                count=len(memory_issues),
                pattern='memory_issues',
                recommendations=[
                    'Check memory usage',
                    'Review garbage collection',
                    'Consider memory optimization'
                ]
            ))
        
        self.alerts = alerts
        return alerts
    
    def export_report(self, output_file: str = None) -> str:
        """Export analysis report to JSON file"""
        if output_file is None:
            output_file = f"log_analysis_report_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}.json"
        
        report_data = {
            'analysis_timestamp': datetime.utcnow().isoformat(),
            'metrics': {
                timeframe.value: {
                    'total_entries': metrics.total_entries,
                    'error_count': metrics.error_count,
                    'warning_count': metrics.warning_count,
                    'info_count': metrics.info_count,
                    'debug_count': metrics.debug_count,
                    'unique_users': metrics.unique_users,
                    'unique_correlation_ids': metrics.unique_correlation_ids,
                    'average_response_time': metrics.average_response_time,
                    'error_rate': metrics.error_rate,
                    'top_errors': metrics.top_errors,
                    'top_users': metrics.top_users,
                    'performance_issues': metrics.performance_issues
                } for timeframe, metrics in self.metrics.items()
            },
            'alerts': [
                {
                    'severity': alert.severity.value,
                    'category': alert.category,
                    'message': alert.message,
                    'timestamp': alert.timestamp.isoformat(),
                    'count': alert.count,
                    'pattern': alert.pattern,
                    'recommendations': alert.recommendations
                } for alert in self.alerts
            ]
        }
        
        with open(output_file, 'w') as f:
            json.dump(report_data, f, indent=2, default=str)
        
        logger.info(f"Log analysis report exported to {output_file}")
        return output_file

# Global analyzer instance
_log_analyzer: Optional[LogAnalyzer] = None

def get_log_analyzer(log_directory: str = "logs") -> LogAnalyzer:
    """Get global log analyzer instance"""
    global _log_analyzer
    if _log_analyzer is None:
        _log_analyzer = LogAnalyzer(log_directory)
    return _log_analyzer

def cleanup_log_analyzer():
    """Cleanup global analyzer instance"""
    global _log_analyzer
    _log_analyzer = None
