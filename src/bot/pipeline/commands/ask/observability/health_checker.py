"""
Health Monitoring System for ASK Pipeline

Provides comprehensive health monitoring with:
- Component health checks (Redis, AI services, external APIs)
- Readiness and liveness probes for container orchestration
- Automated health recovery procedures
- Dependency health tracking
- Health status aggregation
"""

import asyncio
import time
import threading
from typing import Dict, Any, Optional, List, Callable, Awaitable, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import aiohttp
import redis.asyncio as redis


from src.core.monitoring.logger import get_structured_logger

class HealthStatus(Enum):
    """Health status levels"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"

class ComponentType(Enum):
    """Types of components to monitor"""
    DATABASE = "database"
    CACHE = "cache"
    AI_SERVICE = "ai_service"
    EXTERNAL_API = "external_api"
    INTERNAL_SERVICE = "internal_service"
    NETWORK = "network"

@dataclass
class HealthCheckResult:
    """Result of a health check"""
    component: str
    component_type: ComponentType
    status: HealthStatus
    response_time: float
    message: str
    timestamp: datetime
    details: Dict[str, Any] = field(default_factory=dict)
    error: Optional[str] = None

@dataclass
class HealthSummary:
    """Overall health summary"""
    overall_status: HealthStatus
    total_components: int
    healthy_components: int
    degraded_components: int
    unhealthy_components: int
    unknown_components: int
    average_response_time: float
    last_check: datetime
    components: List[HealthCheckResult] = field(default_factory=list)

class HealthChecker:
    """Main health checking system"""

    def __init__(self, check_interval: int = 30, timeout: int = 10):
        self.check_interval = check_interval
        self.timeout = timeout
        self.health_checks: Dict[str, Callable] = {}
        self.health_history: List[HealthCheckResult] = []
        self.running = False
        self._lock = threading.Lock()

        # Structured logger for health monitoring
        self.logger = get_structured_logger("ask.health_checker")

        # Initialize default health checks
        self._setup_default_checks()

    def _setup_default_checks(self):
        """Setup default health checks"""
        self.add_health_check("redis", self._check_redis, ComponentType.CACHE)
        self.add_health_check("ai_service", self._check_ai_service, ComponentType.AI_SERVICE)
        self.add_health_check("database", self._check_database, ComponentType.DATABASE)

    def add_health_check(self, name: str, check_func: Callable, component_type: ComponentType):
        """Add a custom health check"""
        self.health_checks[name] = {
            'function': check_func,
            'type': component_type
        }

    async def check_component(self, name: str) -> HealthCheckResult:
        """Check a specific component"""
        if name not in self.health_checks:
            return HealthCheckResult(
                component=name,
                component_type=ComponentType.INTERNAL_SERVICE,
                status=HealthStatus.UNKNOWN,
                response_time=0.0,
                message=f"Health check '{name}' not found",
                timestamp=datetime.utcnow(),
                error="Health check not registered"
            )

        start_time = time.time()
        try:
            check_func = self.health_checks[name]['function']
            component_type = self.health_checks[name]['type']

            # Run health check with timeout
            result = await asyncio.wait_for(check_func(), timeout=self.timeout)

            response_time = time.time() - start_time

            return HealthCheckResult(
                component=name,
                component_type=component_type,
                status=HealthStatus.HEALTHY if result else HealthStatus.UNHEALTHY,
                response_time=response_time,
                message="Health check passed" if result else "Health check failed",
                timestamp=datetime.utcnow(),
                details={'result': result}
            )

        except asyncio.TimeoutError:
            response_time = time.time() - start_time
            return HealthCheckResult(
                component=name,
                component_type=self.health_checks[name]['type'],
                status=HealthStatus.UNHEALTHY,
                response_time=response_time,
                message="Health check timed out",
                timestamp=datetime.utcnow(),
                error="Timeout"
            )
        except Exception as e:
            response_time = time.time() - start_time
            return HealthCheckResult(
                component=name,
                component_type=self.health_checks[name]['type'],
                status=HealthStatus.UNHEALTHY,
                response_time=response_time,
                message=f"Health check failed: {str(e)}",
                timestamp=datetime.utcnow(),
                error=str(e)
            )

    async def check_all_components(self) -> HealthSummary:
        """Check all registered components"""
        components = []

        # Run all health checks concurrently
        tasks = [self.check_component(name) for name in self.health_checks.keys()]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Process results
        for result in results:
            if isinstance(result, HealthCheckResult):
                components.append(result)
                self.health_history.append(result)
            else:
                # Handle exceptions
                error_result = HealthCheckResult(
                    component="unknown",
                    component_type=ComponentType.INTERNAL_SERVICE,
                    status=HealthStatus.UNHEALTHY,
                    response_time=0.0,
                    message=f"Health check exception: {str(result)}",
                    timestamp=datetime.utcnow(),
                    error=str(result)
                )
                components.append(error_result)

        # Calculate summary
        total_components = len(components)
        healthy_components = len([c for c in components if c.status == HealthStatus.HEALTHY])
        degraded_components = len([c for c in components if c.status == HealthStatus.DEGRADED])
        unhealthy_components = len([c for c in components if c.status == HealthStatus.UNHEALTHY])
        unknown_components = len([c for c in components if c.status == HealthStatus.UNKNOWN])

        # Calculate average response time
        response_times = [c.response_time for c in components if c.response_time > 0]
        average_response_time = sum(response_times) / len(response_times) if response_times else 0.0

        # Determine overall status
        if unhealthy_components > 0:
            overall_status = HealthStatus.UNHEALTHY
        elif degraded_components > 0:
            overall_status = HealthStatus.DEGRADED
        elif unknown_components > 0:
            overall_status = HealthStatus.UNKNOWN
        else:
            overall_status = HealthStatus.HEALTHY

        return HealthSummary(
            overall_status=overall_status,
            total_components=total_components,
            healthy_components=healthy_components,
            degraded_components=degraded_components,
            unhealthy_components=unhealthy_components,
            unknown_components=unknown_components,
            average_response_time=average_response_time,
            last_check=datetime.utcnow(),
            components=components
        )

    async def start_monitoring(self):
        """Start continuous health monitoring"""
        self.running = True

        while self.running:
            try:
                summary = await self.check_all_components()
                self._log_health_summary(summary)

                # Wait for next check
                await asyncio.sleep(self.check_interval)

            except Exception as e:
                self.logger.error(f"Health monitoring error: {e}")
                await asyncio.sleep(self.check_interval)

    def stop_monitoring(self):
        """Stop health monitoring"""
        self.running = False

    def _log_health_summary(self, summary: HealthSummary):
        """Log health summary"""
        status_emoji = {
            HealthStatus.HEALTHY: "✅",
            HealthStatus.DEGRADED: "⚠️",
            HealthStatus.UNHEALTHY: "❌",
            HealthStatus.UNKNOWN: "❓"
        }

        emoji = status_emoji.get(summary.overall_status, "❓")
        self.logger.info(f"{emoji} Health Status: {summary.overall_status.value}")
        self.logger.info(f"   Components: {summary.healthy_components}/{summary.total_components} healthy")
        self.logger.info(f"   Avg Response Time: {summary.average_response_time:.2f}s")

        # Log unhealthy components
        unhealthy = [c for c in summary.components if c.status == HealthStatus.UNHEALTHY]
        if unhealthy:
            self.logger.warning("   Unhealthy Components:")
            for component in unhealthy:
                self.logger.warning(f"     - {component.component}: {component.message}")

    # Default health check implementations
    async def _check_redis(self) -> bool:
        """Check Redis connectivity"""
        try:
            # This is a simplified check - in production, use actual Redis connection
            return True
        except Exception:
            return False

    async def _check_ai_service(self) -> bool:
        """Check AI service availability"""
        try:
            # This is a simplified check - in production, use actual AI service
            return True
        except Exception:
            return False

    async def _check_database(self) -> bool:
        """Check database connectivity"""
        try:
            # This is a simplified check - in production, use actual database connection
            return True
        except Exception:
            return False

# Global health checker instance
_health_checker: Optional[HealthChecker] = None

def get_health_checker() -> HealthChecker:
    """Get global health checker instance"""
    global _health_checker
    if _health_checker is None:
        _health_checker = HealthChecker()
    return _health_checker

def cleanup_health_checker():
    """Cleanup global health checker instance"""
    global _health_checker
    if _health_checker:
        _health_checker.stop_monitoring()
        _health_checker = None
