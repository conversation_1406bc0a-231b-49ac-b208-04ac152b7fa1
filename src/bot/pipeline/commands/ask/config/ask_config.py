"""
Enhanced ASK Pipeline Configuration with Full Core Integration

Advanced configuration system with:
- Deep core integration for centralized management
- Environment-specific overrides with validation
- Feature flags with A/B testing support
- Dynamic configuration reloading with zero downtime
- User-specific configuration for premium features
- Comprehensive validation and error handling
- Integration with observability and monitoring systems
"""
import time
import os
import json
import yaml
import asyncio
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field, asdict
from pathlib import Path
from enum import Enum
from datetime import datetime, timedelta

from src.core.config_manager import CoreConfigManager as ConfigManager, Environment  # Enhanced core config
from src.core.validation.financial_validator import validate_config_values  # Validation
from src.shared.error_handling.logging import get_logger
# from src.database.unified_db import get_db_session  # For persistence - not implemented yet
from src.shared.cache.local_cache_manager import CacheManager  # For config caching
from src.shared.monitoring.observability import observability_manager, MetricType  # For config change tracking
from src.bot.pipeline.commands.ask.audit import get_audit_logger  # For configuration changes

logger = get_logger(__name__)


class ConfigValidationError(Exception):
    """Custom error for config validation."""
    pass


class ConfigEnvironment(Enum):
    """Configuration environment types"""
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"
    TESTING = "testing"


@dataclass
class IntentDetectionConfig:
    """Enhanced configuration for intent detection with AI model selection and core integration."""
    model: str = "unified_intent_model"  # Use new unified
    timeout: float = 10.0
    cache_ttl: int = 300  # 5 minutes
    confidence_threshold: float = 0.7
    use_ai_fallback: bool = True  # Fallback to core AI if needed
    enable_context_awareness: bool = True  # Use conversation context
    max_context_length: int = 4096  # Token limit for context
    adaptive_threshold: bool = True  # Adjust threshold based on performance


@dataclass
class ToolsConfig:
    """Enhanced tools config with MCP integration, concurrency limits, and core resource management."""
    mcp_enabled: bool = True
    parallel_execution: bool = True
    timeout: float = 60.0
    max_concurrent: int = 5  # Increased with semaphore
    rate_limit_per_minute: int = 60
    use_pipeline_engine: bool = True  # Integrate with core pipeline
    resource_pool_size: int = 10  # Number of available tool resources
    tool_priority_system: bool = True  # Prioritize critical tools
    fallback_tools_enabled: bool = True  # Enable fallback tool execution


@dataclass
class ResponseConfig:
    """Enhanced response config with prompt integration and core AI optimization."""
    model: str = "gpt-4o-mini"  # Efficient default - will be overridden by config
    max_tokens: int = 1500  # Increased for better responses
    temperature: float = 0.3
    timeout: float = 30.0  # Longer for synthesis
    use_prompt_manager: bool = True  # Use core prompts
    response_quality_threshold: float = 0.85  # Minimum quality score
    enable_synthesis: bool = True  # Use AI response synthesis
    fallback_response_enabled: bool = True  # Enable fallback responses


@dataclass
class CacheConfig:
    """Enhanced cache config with Redis integration and intelligent management."""
    enabled: bool = True
    ttl: int = 600  # 10 minutes
    max_size: int = 5000  # Larger for pipeline
    redis_url: Optional[str] = None  # From env
    eviction_policy: str = "LRU"  # Least Recently Used
    intelligent_ttl: bool = True  # Use adaptive TTL
    cache_warming_enabled: bool = True  # Preload common data
    cache_metrics_enabled: bool = True  # Track cache performance


@dataclass
class MonitoringConfig:
    """Enhanced monitoring with core integration and observability."""
    log_level: str = "INFO"
    metrics_enabled: bool = True
    correlation_tracking: bool = True
    performance_tracking: bool = True
    integrate_with_core: bool = True  # Use core monitoring_pkg
    observability_level: str = "full"  # basic, extended, full
    alert_thresholds: Dict[str, float] = field(default_factory=lambda: {
        "error_rate": 0.05,
        "response_time_p95": 3.0,
        "success_rate": 0.95
    })


@dataclass
class SecurityConfig:
    """Security configuration for the ASK pipeline."""
    rate_limiting_enabled: bool = True
    max_requests_per_minute: int = 100
    suspicious_activity_threshold: int = 5
    input_validation_enabled: bool = True
    output_sanitization_enabled: bool = True
    audit_logging_enabled: bool = True
    data_encryption_enabled: bool = True


@dataclass
class AskConfig:
    """Main enhanced ASK pipeline configuration with full core integration."""
    intent_detection: IntentDetectionConfig = field(default_factory=IntentDetectionConfig)
    tools: ToolsConfig = field(default_factory=ToolsConfig)
    response: ResponseConfig = field(default_factory=ResponseConfig)
    cache: CacheConfig = field(default_factory=CacheConfig)
    monitoring: MonitoringConfig = field(default_factory=MonitoringConfig)
    security: SecurityConfig = field(default_factory=SecurityConfig)

    # Performance targets with validation
    max_response_time: float = 3.0  # Stricter
    target_success_rate: float = 0.99

    # Core integration settings
    environment: ConfigEnvironment = ConfigEnvironment.DEVELOPMENT
    core_integration_level: str = "full"  # basic, extended, full
    database_persistence_enabled: bool = True
    distributed_tracing_enabled: bool = True

    # Feature flags with A/B testing support
    feature_flags: Dict[str, bool] = field(default_factory=lambda: {
        "use_ai_synthesis": True,
        "parallel_tools": True,
        "enhanced_logging": True,
        "adaptive_caching": True,
        "context_aware_responses": True,
        "advanced_error_handling": True
    })

    # A/B testing configuration
    ab_testing: Dict[str, Any] = field(default_factory=lambda: {
        "enabled": False,
        "variants": {},
        "traffic_allocation": {}
    })

    def __post_init__(self):
        """Post-init validation and core integration setup."""
        self.validate()
        self._setup_core_integration()

    def _setup_core_integration(self):
        """Setup core integration components."""
        # Initialize core manager connection
        self.core_manager = ConfigManager()
        
        # Setup observability tracking for config changes
        observability_manager.register_health_check("config_service", self._health_check)
        
        # Setup audit logging for configuration changes
        self.audit_logger = get_audit_logger()
        
        logger.info(f"ASK Config initialized with core integration level: {self.core_integration_level}")

    def validate(self) -> bool:
        """Enhanced validation using core validator with core integration checks."""
        errors = validate_config_values(asdict(self), {
            "max_response_time": {"type": "float", "min": 0.1, "max": 10.0},
            "target_success_rate": {"type": "float", "min": 0.0, "max": 1.0},
            "max_concurrent": {"type": "int", "min": 1, "max": 20},
            "cache_ttl": {"type": "int", "min": 60, "max": 3600},
            "max_tokens": {"type": "int", "min": 100, "max": 4000},
            "max_requests_per_minute": {"type": "int", "min": 10, "max": 1000},
            "confidence_threshold": {"type": "float", "min": 0.0, "max": 1.0}
        })

        # Additional core integration validation
        if self.core_integration_level not in ["basic", "extended", "full"]:
            errors.append("Invalid core_integration_level. Must be 'basic', 'extended', or 'full'")

        if errors:
            raise ConfigValidationError(f"Validation errors: {errors}")
        return True

    @classmethod
    def from_core_manager(cls, section: str = "ask_pipeline", environment: ConfigEnvironment = ConfigEnvironment.DEVELOPMENT) -> 'AskConfig':
        """Load from core config manager with DB fallback and environment support."""
        config_manager = ConfigManager()
        config_dict = config_manager.get_section(section, default={})
        
        # Load environment-specific overrides
        env_section = f"{section}_{environment.value}"
        env_overrides = config_manager.get_section(env_section, default={})
        config_dict.update(env_overrides)

        # Create instance from dict
        config = cls()
        config._load_from_dict(config_dict)
        config.environment = environment
        config.validate()
        
        # Log configuration load
        logger.info(f"Loaded ASK config from core manager for environment: {environment.value}")
        
        return config

    def _load_from_dict(self, data: Dict[str, Any]):
        """Load config from dict, supporting nested structures and core integration."""
        for key, value in data.items():
            if hasattr(self, key) and not key.startswith('_'):
                if isinstance(value, dict) and hasattr(getattr(self, key), '_load_from_dict'):
                    getattr(self, key)._load_from_dict(value)
                else:
                    setattr(self, key, value)

    @classmethod
    def from_env(cls, environment: ConfigEnvironment = ConfigEnvironment.DEVELOPMENT) -> 'AskConfig':
        """Load from environment with overrides and core integration."""
        config = cls(environment=environment)

        # Core environment setup
        config.environment = environment

        # Intent Detection
        config.intent_detection.timeout = float(os.getenv('ASK_INTENT_TIMEOUT', '10.0'))
        config.intent_detection.cache_ttl = int(os.getenv('ASK_INTENT_CACHE_TTL', '300'))
        config.intent_detection.confidence_threshold = float(os.getenv('ASK_INTENT_CONFIDENCE', '0.7'))
        config.intent_detection.enable_context_awareness = os.getenv('ASK_INTENT_CONTEXT_AWARE', 'true').lower() == 'true'
        config.intent_detection.max_context_length = int(os.getenv('ASK_INTENT_MAX_CONTEXT', '4096'))
        config.intent_detection.adaptive_threshold = os.getenv('ASK_INTENT_ADAPTIVE_THRESHOLD', 'true').lower() == 'true'

        # Tools
        config.tools.mcp_enabled = os.getenv('ASK_MCP_ENABLED', 'true').lower() == 'true'
        config.tools.timeout = float(os.getenv('ASK_TOOLS_TIMEOUT', '60.0'))
        config.tools.max_concurrent = int(os.getenv('ASK_MAX_CONCURRENT_TOOLS', '5'))
        config.tools.resource_pool_size = int(os.getenv('ASK_TOOL_RESOURCE_POOL', '10'))
        config.tools.tool_priority_system = os.getenv('ASK_TOOL_PRIORITY_SYSTEM', 'true').lower() == 'true'
        config.tools.fallback_tools_enabled = os.getenv('ASK_FALLBACK_TOOLS_ENABLED', 'true').lower() == 'true'

        # Response
        config.response.model = os.getenv('ASK_RESPONSE_MODEL', 'gpt-4o-mini')
        config.response.max_tokens = int(os.getenv('ASK_MAX_TOKENS', '1500'))
        config.response.temperature = float(os.getenv('ASK_TEMPERATURE', '0.3'))
        config.response.timeout = float(os.getenv('ASK_RESPONSE_TIMEOUT', '30.0'))
        config.response.response_quality_threshold = float(os.getenv('ASK_RESPONSE_QUALITY_THRESHOLD', '0.85'))
        config.response.enable_synthesis = os.getenv('ASK_RESPONSE_SYNTHESIS', 'true').lower() == 'true'
        config.response.fallback_response_enabled = os.getenv('ASK_FALLBACK_RESPONSE_ENABLED', 'true').lower() == 'true'

        # Cache
        config.cache.enabled = os.getenv('ASK_CACHE_ENABLED', 'true').lower() == 'true'
        config.cache.ttl = int(os.getenv('ASK_CACHE_TTL', '600'))
        config.cache.redis_url = os.getenv('REDIS_URL')
        config.cache.eviction_policy = os.getenv('ASK_CACHE_POLICY', 'LRU')
        config.cache.intelligent_ttl = os.getenv('ASK_CACHE_INTELLIGENT_TTL', 'true').lower() == 'true'
        config.cache.cache_warming_enabled = os.getenv('ASK_CACHE_WARMING_ENABLED', 'true').lower() == 'true'
        config.cache.cache_metrics_enabled = os.getenv('ASK_CACHE_METRICS_ENABLED', 'true').lower() == 'true'

        # Monitoring
        config.monitoring.log_level = os.getenv('ASK_LOG_LEVEL', 'INFO')
        config.monitoring.metrics_enabled = os.getenv('ASK_METRICS_ENABLED', 'true').lower() == 'true'
        config.monitoring.correlation_tracking = os.getenv('ASK_CORRELATION_TRACKING', 'true').lower() == 'true'
        config.monitoring.performance_tracking = os.getenv('ASK_PERFORMANCE_TRACKING', 'true').lower() == 'true'
        config.monitoring.integrate_with_core = os.getenv('ASK_CORE_MONITORING', 'true').lower() == 'true'
        config.monitoring.observability_level = os.getenv('ASK_OBSERVABILITY_LEVEL', 'full')
        config.monitoring.alert_thresholds = {
            "error_rate": float(os.getenv('ASK_ALERT_ERROR_RATE', '0.05')),
            "response_time_p95": float(os.getenv('ASK_ALERT_RESPONSE_TIME_P95', '3.0')),
            "success_rate": float(os.getenv('ASK_ALERT_SUCCESS_RATE', '0.95'))
        }

        # Security
        config.security.rate_limiting_enabled = os.getenv('ASK_RATE_LIMITING_ENABLED', 'true').lower() == 'true'
        config.security.max_requests_per_minute = int(os.getenv('ASK_MAX_REQUESTS_PER_MINUTE', '100'))
        config.security.suspicious_activity_threshold = int(os.getenv('ASK_SUSPICIOUS_ACTIVITY_THRESHOLD', '5'))
        config.security.input_validation_enabled = os.getenv('ASK_INPUT_VALIDATION_ENABLED', 'true').lower() == 'true'
        config.security.output_sanitization_enabled = os.getenv('ASK_OUTPUT_SANITIZATION_ENABLED', 'true').lower() == 'true'
        config.security.audit_logging_enabled = os.getenv('ASK_AUDIT_LOGGING_ENABLED', 'true').lower() == 'true'
        config.security.data_encryption_enabled = os.getenv('ASK_DATA_ENCRYPTION_ENABLED', 'true').lower() == 'true'

        # Core Integration
        config.core_integration_level = os.getenv('ASK_CORE_INTEGRATION_LEVEL', 'full')
        config.database_persistence_enabled = os.getenv('ASK_DATABASE_PERSISTENCE_ENABLED', 'true').lower() == 'true'
        config.distributed_tracing_enabled = os.getenv('ASK_DISTRIBUTED_TRACING_ENABLED', 'true').lower() == 'true'

        # Performance
        config.max_response_time = float(os.getenv('ASK_MAX_RESPONSE_TIME', '3.0'))
        config.target_success_rate = float(os.getenv('ASK_TARGET_SUCCESS_RATE', '0.99'))

        # A/B Testing
        config.ab_testing["enabled"] = os.getenv('ASK_AB_TESTING_ENABLED', 'false').lower() == 'true'
        config.ab_testing["variants"] = json.loads(os.getenv('ASK_AB_TESTING_VARIANTS', '{}'))
        config.ab_testing["traffic_allocation"] = json.loads(os.getenv('ASK_AB_TRAFFIC_ALLOCATION', '{}'))

        # Feature flags
        config.feature_flags["use_ai_synthesis"] = os.getenv('ASK_USE_AI_SYNTHESIS', 'true').lower() == 'true'
        config.feature_flags["parallel_tools"] = os.getenv('ASK_PARALLEL_TOOLS', 'true').lower() == 'true'
        config.feature_flags["enhanced_logging"] = os.getenv('ASK_ENHANCED_LOGGING', 'true').lower() == 'true'
        config.feature_flags["adaptive_caching"] = os.getenv('ASK_ADAPTIVE_CACHING', 'true').lower() == 'true'
        config.feature_flags["context_aware_responses"] = os.getenv('ASK_CONTEXT_AWARE_RESPONSES', 'true').lower() == 'true'
        config.feature_flags["advanced_error_handling"] = os.getenv('ASK_ADVANCED_ERROR_HANDLING', 'true').lower() == 'true'

        config.validate()
        return config

    @classmethod
    def from_yaml(cls, config_path: str, environment: ConfigEnvironment = ConfigEnvironment.DEVELOPMENT) -> 'AskConfig':
        """Load from YAML with core integration and environment support."""
        path = Path(config_path)
        if not path.exists():
            logger.warning(f"Config file not found: {config_path}. Using environment defaults for {environment.value}.")
            return cls.from_env(environment)

        with open(path, 'r') as f:
            yaml_data = yaml.safe_load(f)

        # Load base configuration
        config_dict = yaml_data.get('ask_pipeline', {})
        
        # Load environment-specific overrides
        env_section = f"ask_pipeline_{environment.value}"
        if env_section in yaml_data:
            env_overrides = yaml_data[env_section]
            config_dict.update(env_overrides)
            logger.info(f"Applied environment-specific overrides for {environment.value}")

        config = cls(environment=environment)
        config._load_from_dict(config_dict)
        config.validate()
        return config

    def save_to_db(self, user_id: Optional[str] = None, environment: ConfigEnvironment = ConfigEnvironment.DEVELOPMENT):
        """Persist to DB via core manager with environment context."""
        config_manager = ConfigManager()
        section = f"ask_pipeline_{environment.value}_{user_id}" if user_id else f"ask_pipeline_{environment.value}"
        config_data = asdict(self)
        config_data['environment'] = environment.value  # Include environment in saved config
        
        config_manager.set_section(section, config_data)
        config_manager.save_to_db()
        
        # Audit configuration change
        if self.audit_logger:
            self.audit_logger.log_configuration_change(
                user_id=user_id or "system",
                description=f"Saved ASK configuration for environment {environment.value}",
                metadata={
                    'environment': environment.value,
                    'user_id': user_id,
                    'section': section
                }
            )

    def reload(self, config_path: Optional[str] = None, force_env: bool = False, environment: ConfigEnvironment = ConfigEnvironment.DEVELOPMENT):
        """Hot-reload config with core integration and observability tracking."""
        start_time = time.time()
        
        if force_env:
            new_config = self.from_env(environment)
        elif config_path:
            new_config = self.from_yaml(config_path, environment)
        else:
            # Reload from core with current environment
            new_config = self.from_core_manager(environment=environment)

        # Update configuration
        self.__dict__.update(new_config.__dict__)
        self.validate()
        
        # Track configuration reload
        reload_duration = time.time() - start_time
        observability_manager.record_metric(
            name="config_reload_duration",
            value=reload_duration,
            metric_type=MetricType.TIMER,
            tags={'environment': environment.value}
        )
        
        logger.info(f"ASK config reloaded in {reload_duration:.3f}s for environment {environment.value}")
        
        # Audit log the reload
        if self.audit_logger:
            self.audit_logger.log_configuration_change(
                user_id="system",
                description=f"Hot-reloaded ASK configuration for environment {environment.value}",
                metadata={
                    'environment': environment.value,
                    'source': 'reload',
                    'duration': reload_duration
                }
            )

    def get_feature_flag(self, flag: str, user_id: Optional[str] = None, environment: ConfigEnvironment = ConfigEnvironment.DEVELOPMENT) -> bool:
        """Get feature flag value with A/B testing support."""
        base_flag = self.feature_flags.get(flag, False)
        
        # Check for user-specific override
        if user_id:
            user_section = f"ask_pipeline_{environment.value}_{user_id}"
            user_config = self.core_manager.get_section(user_section, default={})
            user_flag = user_config.get('feature_flags', {}).get(flag, base_flag)
            if user_flag != base_flag:
                logger.debug(f"User-specific feature flag override: {flag} = {user_flag} for user {user_id}")
                return user_flag

        # Check for A/B testing variant
        if self.ab_testing["enabled"]:
            variant_key = f"{flag}_{user_id or 'default'}"
            variant = self.ab_testing["variants"].get(flag, {}).get(variant_key, base_flag)
            if variant != base_flag:
                logger.debug(f"A/B testing variant for {flag}: {variant}")
                return variant

        return base_flag

    def _health_check(self) -> bool:
        """Health check for configuration service."""
        try:
            # Validate current configuration
            self.validate()
            
            # Check core manager connectivity
            if self.core_manager:
                # Basic connectivity check
                test_key = "health_check"
                self.core_manager.set(test_key, "ok")
                if self.core_manager.get(test_key) == "ok":
                    self.core_manager.set(test_key, None)  # Cleanup
                    return True
            
            return True
        except Exception as e:
            logger.error(f"Configuration health check failed: {e}")
            return False


# Global config instance with enhanced caching and core integration
_config_cache = CacheManager()
_global_configs: Dict[ConfigEnvironment, Optional[AskConfig]] = {}


def get_ask_config(user_id: Optional[str] = None, environment: ConfigEnvironment = ConfigEnvironment.DEVELOPMENT) -> AskConfig:
    """Get global or user-specific config with environment support."""
    global _global_configs

    # Get environment-specific global config
    if environment not in _global_configs:
        _global_configs[environment] = None

    if _global_configs[environment] is None:
        # Try cache first
        cache_key = f"global_ask_config_{environment.value}"
        cached = _config_cache.get(cache_key)
        if cached:
            config = AskConfig(environment=environment)
            config._load_from_dict(cached)
        else:
            config = AskConfig.from_env(environment)
            _config_cache.set(cache_key, asdict(config), ttl=600)  # 10 min
            _global_configs[environment] = config

        _global_configs[environment] = config

    global_config = _global_configs[environment]

    # Override with user-specific config if provided
    if user_id:
        cache_key = f"ask_config_{environment.value}_{user_id}"
        cached = _config_cache.get(cache_key)
        if cached:
            user_config = AskConfig(environment=environment)
            user_config._load_from_dict(cached)
            user_config._load_from_dict(asdict(global_config))  # Merge with global
        else:
            user_config = AskConfig.from_core_manager(f"ask_pipeline_{environment.value}_{user_id}", environment)
            _config_cache.set(cache_key, asdict(user_config), ttl=300)  # 5 min
        return user_config

    return global_config


def reload_ask_config(config_path: Optional[str] = None, user_id: Optional[str] = None, 
                     environment: ConfigEnvironment = ConfigEnvironment.DEVELOPMENT, force_env: bool = False):
    """Reload and invalidate cache with environment support."""
    global _global_configs
    
    # Clear environment-specific cache
    cache_key = f"global_ask_config_{environment.value}"
    _config_cache.delete(cache_key)
    
    if user_id:
        user_cache_key = f"ask_config_{environment.value}_{user_id}"
        _config_cache.delete(user_cache_key)
    
    # Reload global config for environment
    _global_configs[environment] = None
    get_ask_config(environment=environment)
    
    # Reload user config if specified
    if user_id:
        get_ask_config(user_id, environment)
    
    # Reload from YAML if path provided
    if config_path:
        config = AskConfig.from_yaml(config_path, environment)
        if user_id:
            cache_key = f"ask_config_{environment.value}_{user_id}"
        else:
            cache_key = f"global_ask_config_{environment.value}"
        _config_cache.set(cache_key, asdict(config), ttl=600)
        if not user_id:
            _global_configs[environment] = config
        config.reload(config_path, environment=environment)
    
    logger.info(f"ASK config reloaded for environment {environment.value}, user: {'user' if user_id else 'global'}")


def cleanup_ask_config(environment: ConfigEnvironment = ConfigEnvironment.DEVELOPMENT):
    """Cleanup cache for specific environment."""
    global _global_configs
    
    # Clear cache
    cache_key = f"global_ask_config_{environment.value}"
    _config_cache.delete(cache_key)
    
    # Clear global config instance
    if environment in _global_configs:
        _global_configs[environment] = None
    
    logger.info(f"ASK config cache cleaned up for environment {environment.value}")