"""
Environment Profiles for ASK Pipeline

Provides environment-specific configuration:
- Development, staging, and production configurations
- Environment-specific feature toggles and limits
- Environment validation and health checks
- Environment migration and deployment scripts
- Environment monitoring and alerting
"""

import os
import json
import logging
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, field, asdict
from datetime import datetime, <PERSON><PERSON><PERSON>
from enum import Enum
from pathlib import Path
import asyncio
import subprocess
import threading

logger = logging.getLogger(__name__)

class EnvironmentType(Enum):
    """Environment types"""
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"
    TESTING = "testing"

class HealthStatus(Enum):
    """Health status"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"

@dataclass
class EnvironmentConfig:
    """Environment-specific configuration"""
    name: str
    environment_type: EnvironmentType
    description: str
    base_url: str
    database_url: str
    redis_url: str
    log_level: str
    debug: bool
    features: Dict[str, bool] = field(default_factory=dict)
    limits: Dict[str, Any] = field(default_factory=dict)
    monitoring: Dict[str, Any] = field(default_factory=dict)
    security: Dict[str, Any] = field(default_factory=dict)
    performance: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)

@dataclass
class HealthCheck:
    """Health check configuration"""
    name: str
    check_type: str
    endpoint: Optional[str] = None
    command: Optional[str] = None
    timeout: int = 30
    interval: int = 60
    retries: int = 3
    critical: bool = True
    enabled: bool = True

@dataclass
class HealthCheckResult:
    """Health check result"""
    check_name: str
    status: HealthStatus
    message: str
    response_time: float
    checked_at: datetime
    error: Optional[str] = None

@dataclass
class EnvironmentHealth:
    """Environment health status"""
    environment: str
    overall_status: HealthStatus
    checks: List[HealthCheckResult]
    last_checked: datetime
    uptime: float
    version: str

class EnvironmentProfileManager:
    """Environment profile management system"""
    
    def __init__(self, profiles_file: str = "environment_profiles.json"):
        self.profiles_file = profiles_file
        self.profiles: Dict[str, EnvironmentConfig] = {}
        self.health_checks: Dict[str, List[HealthCheck]] = {}
        self.health_results: Dict[str, List[HealthCheckResult]] = {}
        self._lock = threading.RLock()
        self._health_monitor_running = False
        self._health_monitor_task = None
        
        # Load existing profiles
        self._load_profiles()
        self._load_health_checks()
    
    def _load_profiles(self):
        """Load environment profiles from file"""
        try:
            config_path = Path(self.profiles_file)
            if not config_path.exists():
                logger.info("No environment profiles file found, creating default profiles")
                self._create_default_profiles()
                return
            
            with open(config_path, 'r') as f:
                data = json.load(f)
            
            for profile_data in data.get('profiles', []):
                try:
                    profile = EnvironmentConfig(
                        name=profile_data['name'],
                        environment_type=EnvironmentType(profile_data['environment_type']),
                        description=profile_data['description'],
                        base_url=profile_data['base_url'],
                        database_url=profile_data['database_url'],
                        redis_url=profile_data['redis_url'],
                        log_level=profile_data['log_level'],
                        debug=profile_data['debug'],
                        features=profile_data.get('features', {}),
                        limits=profile_data.get('limits', {}),
                        monitoring=profile_data.get('monitoring', {}),
                        security=profile_data.get('security', {}),
                        performance=profile_data.get('performance', {}),
                        created_at=datetime.fromisoformat(profile_data['created_at']),
                        updated_at=datetime.fromisoformat(profile_data['updated_at'])
                    )
                    self.profiles[profile.name] = profile
                except Exception as e:
                    logger.error(f"Failed to load environment profile {profile_data.get('name', 'unknown')}: {e}")
            
            logger.info(f"Loaded {len(self.profiles)} environment profiles from {self.profiles_file}")
            
        except Exception as e:
            logger.error(f"Failed to load environment profiles: {e}")
    
    def _create_default_profiles(self):
        """Create default environment profiles"""
        default_profiles = [
            EnvironmentConfig(
                name="development",
                environment_type=EnvironmentType.DEVELOPMENT,
                description="Development environment",
                base_url="http://localhost:8000",
                database_url="postgresql://postgres:password@localhost:5432/ask_dev",
                redis_url="redis://localhost:6379/0",
                log_level="DEBUG",
                debug=True,
                features={
                    "hot_reload": True,
                    "debug_mode": True,
                    "mock_apis": True,
                    "detailed_logging": True
                },
                limits={
                    "max_requests_per_minute": 1000,
                    "max_concurrent_requests": 100,
                    "max_memory_usage_mb": 512,
                    "max_cpu_usage_percent": 80
                },
                monitoring={
                    "enabled": True,
                    "metrics_interval": 10,
                    "health_check_interval": 30,
                    "alert_thresholds": {
                        "response_time_ms": 1000,
                        "error_rate_percent": 5,
                        "memory_usage_percent": 90
                    }
                },
                security={
                    "rate_limiting": True,
                    "input_validation": True,
                    "pii_detection": True,
                    "encryption": False
                },
                performance={
                    "caching": True,
                    "connection_pooling": True,
                    "async_processing": True,
                    "compression": False
                }
            ),
            EnvironmentConfig(
                name="staging",
                environment_type=EnvironmentType.STAGING,
                description="Staging environment",
                base_url="https://staging.ask-pipeline.com",
                database_url="**********************************************/ask_staging",
                redis_url="redis://staging-redis:6379/0",
                log_level="INFO",
                debug=False,
                features={
                    "hot_reload": False,
                    "debug_mode": False,
                    "mock_apis": False,
                    "detailed_logging": True
                },
                limits={
                    "max_requests_per_minute": 5000,
                    "max_concurrent_requests": 500,
                    "max_memory_usage_mb": 2048,
                    "max_cpu_usage_percent": 85
                },
                monitoring={
                    "enabled": True,
                    "metrics_interval": 30,
                    "health_check_interval": 60,
                    "alert_thresholds": {
                        "response_time_ms": 2000,
                        "error_rate_percent": 3,
                        "memory_usage_percent": 85
                    }
                },
                security={
                    "rate_limiting": True,
                    "input_validation": True,
                    "pii_detection": True,
                    "encryption": True
                },
                performance={
                    "caching": True,
                    "connection_pooling": True,
                    "async_processing": True,
                    "compression": True
                }
            ),
            EnvironmentConfig(
                name="production",
                environment_type=EnvironmentType.PRODUCTION,
                description="Production environment",
                base_url="https://ask-pipeline.com",
                database_url="*******************************************/ask_prod",
                redis_url="redis://prod-redis:6379/0",
                log_level="WARNING",
                debug=False,
                features={
                    "hot_reload": False,
                    "debug_mode": False,
                    "mock_apis": False,
                    "detailed_logging": False
                },
                limits={
                    "max_requests_per_minute": 10000,
                    "max_concurrent_requests": 1000,
                    "max_memory_usage_mb": 4096,
                    "max_cpu_usage_percent": 90
                },
                monitoring={
                    "enabled": True,
                    "metrics_interval": 60,
                    "health_check_interval": 120,
                    "alert_thresholds": {
                        "response_time_ms": 5000,
                        "error_rate_percent": 1,
                        "memory_usage_percent": 80
                    }
                },
                security={
                    "rate_limiting": True,
                    "input_validation": True,
                    "pii_detection": True,
                    "encryption": True
                },
                performance={
                    "caching": True,
                    "connection_pooling": True,
                    "async_processing": True,
                    "compression": True
                }
            )
        ]
        
        for profile in default_profiles:
            self.profiles[profile.name] = profile
        
        self._save_profiles()
    
    def _load_health_checks(self):
        """Load health checks from file"""
        try:
            config_path = Path(self.profiles_file)
            if not config_path.exists():
                self._create_default_health_checks()
                return
            
            with open(config_path, 'r') as f:
                data = json.load(f)
            
            for env_name, checks_data in data.get('health_checks', {}).items():
                checks = []
                for check_data in checks_data:
                    try:
                        check = HealthCheck(
                            name=check_data['name'],
                            check_type=check_data['check_type'],
                            endpoint=check_data.get('endpoint'),
                            command=check_data.get('command'),
                            timeout=check_data.get('timeout', 30),
                            interval=check_data.get('interval', 60),
                            retries=check_data.get('retries', 3),
                            critical=check_data.get('critical', True),
                            enabled=check_data.get('enabled', True)
                        )
                        checks.append(check)
                    except Exception as e:
                        logger.error(f"Failed to load health check {check_data.get('name', 'unknown')}: {e}")
                
                self.health_checks[env_name] = checks
            
            logger.info(f"Loaded health checks for {len(self.health_checks)} environments")
            
        except Exception as e:
            logger.error(f"Failed to load health checks: {e}")
    
    def _create_default_health_checks(self):
        """Create default health checks"""
        default_checks = {
            "development": [
                HealthCheck("database", "database", command="pg_isready -h localhost -p 5432", timeout=10),
                HealthCheck("redis", "redis", command="redis-cli ping", timeout=10),
                HealthCheck("api", "http", endpoint="http://localhost:8000/health", timeout=5)
            ],
            "staging": [
                HealthCheck("database", "database", command="pg_isready -h staging-db -p 5432", timeout=15),
                HealthCheck("redis", "redis", command="redis-cli -h staging-redis ping", timeout=15),
                HealthCheck("api", "http", endpoint="https://staging.ask-pipeline.com/health", timeout=10)
            ],
            "production": [
                HealthCheck("database", "database", command="pg_isready -h prod-db -p 5432", timeout=30),
                HealthCheck("redis", "redis", command="redis-cli -h prod-redis ping", timeout=30),
                HealthCheck("api", "http", endpoint="https://ask-pipeline.com/health", timeout=15)
            ]
        }
        
        for env_name, checks in default_checks.items():
            self.health_checks[env_name] = checks
        
        self._save_health_checks()
    
    def _save_profiles(self):
        """Save environment profiles to file"""
        try:
            with self._lock:
                profiles_data = []
                for profile in self.profiles.values():
                    profile_data = asdict(profile)
                    profile_data['created_at'] = profile.created_at.isoformat()
                    profile_data['updated_at'] = profile.updated_at.isoformat()
                    profiles_data.append(profile_data)
                
                data = {
                    'profiles': profiles_data,
                    'last_updated': datetime.utcnow().isoformat()
                }
                
                with open(self.profiles_file, 'w') as f:
                    json.dump(data, f, indent=2)
                
                logger.debug("Environment profiles saved to file")
                
        except Exception as e:
            logger.error(f"Failed to save environment profiles: {e}")
    
    def _save_health_checks(self):
        """Save health checks to file"""
        try:
            with self._lock:
                health_checks_data = {}
                for env_name, checks in self.health_checks.items():
                    checks_data = []
                    for check in checks:
                        check_data = asdict(check)
                        checks_data.append(check_data)
                    health_checks_data[env_name] = checks_data
                
                # Load existing data and update health checks
                config_path = Path(self.profiles_file)
                data = {}
                if config_path.exists():
                    with open(config_path, 'r') as f:
                        data = json.load(f)
                
                data['health_checks'] = health_checks_data
                data['last_updated'] = datetime.utcnow().isoformat()
                
                with open(self.profiles_file, 'w') as f:
                    json.dump(data, f, indent=2)
                
                logger.debug("Health checks saved to file")
                
        except Exception as e:
            logger.error(f"Failed to save health checks: {e}")
    
    def get_profile(self, name: str) -> Optional[EnvironmentConfig]:
        """Get environment profile"""
        with self._lock:
            return self.profiles.get(name)
    
    def list_profiles(self) -> List[EnvironmentConfig]:
        """List all environment profiles"""
        with self._lock:
            return list(self.profiles.values())
    
    def create_profile(self, profile: EnvironmentConfig) -> bool:
        """Create new environment profile"""
        try:
            with self._lock:
                if profile.name in self.profiles:
                    logger.warning(f"Environment profile {profile.name} already exists")
                    return False
                
                self.profiles[profile.name] = profile
                self._save_profiles()
                
                logger.info(f"Environment profile created: {profile.name}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to create environment profile {profile.name}: {e}")
            return False
    
    def update_profile(self, name: str, **kwargs) -> bool:
        """Update environment profile"""
        try:
            with self._lock:
                if name not in self.profiles:
                    logger.warning(f"Environment profile {name} not found")
                    return False
                
                profile = self.profiles[name]
                
                # Update allowed fields
                allowed_fields = {
                    'description', 'base_url', 'database_url', 'redis_url',
                    'log_level', 'debug', 'features', 'limits', 'monitoring',
                    'security', 'performance'
                }
                
                for key, value in kwargs.items():
                    if key in allowed_fields:
                        setattr(profile, key, value)
                
                profile.updated_at = datetime.utcnow()
                self._save_profiles()
                
                logger.info(f"Environment profile updated: {name}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to update environment profile {name}: {e}")
            return False
    
    def delete_profile(self, name: str) -> bool:
        """Delete environment profile"""
        try:
            with self._lock:
                if name not in self.profiles:
                    logger.warning(f"Environment profile {name} not found")
                    return False
                
                del self.profiles[name]
                if name in self.health_checks:
                    del self.health_checks[name]
                
                self._save_profiles()
                
                logger.info(f"Environment profile deleted: {name}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to delete environment profile {name}: {e}")
            return False
    
    async def run_health_check(self, check: HealthCheck) -> HealthCheckResult:
        """Run a single health check"""
        start_time = datetime.utcnow()
        
        try:
            if check.check_type == "http":
                result = await self._check_http_endpoint(check)
            elif check.check_type == "database":
                result = await self._check_database(check)
            elif check.check_type == "redis":
                result = await self._check_redis(check)
            elif check.check_type == "command":
                result = await self._check_command(check)
            else:
                result = HealthCheckResult(
                    check_name=check.name,
                    status=HealthStatus.UNKNOWN,
                    message=f"Unknown check type: {check.check_type}",
                    response_time=0.0,
                    checked_at=start_time,
                    error="Unknown check type"
                )
            
            return result
            
        except Exception as e:
            response_time = (datetime.utcnow() - start_time).total_seconds()
            return HealthCheckResult(
                check_name=check.name,
                status=HealthStatus.UNHEALTHY,
                message=f"Health check failed: {e}",
                response_time=response_time,
                checked_at=start_time,
                error=str(e)
            )
    
    async def _check_http_endpoint(self, check: HealthCheck) -> HealthCheckResult:
        """Check HTTP endpoint"""
        import aiohttp
        
        start_time = datetime.utcnow()
        
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=check.timeout)) as session:
                async with session.get(check.endpoint) as response:
                    response_time = (datetime.utcnow() - start_time).total_seconds()
                    
                    if response.status == 200:
                        status = HealthStatus.HEALTHY
                        message = "HTTP endpoint is healthy"
                    else:
                        status = HealthStatus.UNHEALTHY
                        message = f"HTTP endpoint returned status {response.status}"
                    
                    return HealthCheckResult(
                        check_name=check.name,
                        status=status,
                        message=message,
                        response_time=response_time,
                        checked_at=start_time
                    )
                    
        except asyncio.TimeoutError:
            response_time = (datetime.utcnow() - start_time).total_seconds()
            return HealthCheckResult(
                check_name=check.name,
                status=HealthStatus.UNHEALTHY,
                message="HTTP endpoint timeout",
                response_time=response_time,
                checked_at=start_time,
                error="Timeout"
            )
        except Exception as e:
            response_time = (datetime.utcnow() - start_time).total_seconds()
            return HealthCheckResult(
                check_name=check.name,
                status=HealthStatus.UNHEALTHY,
                message=f"HTTP endpoint error: {e}",
                response_time=response_time,
                checked_at=start_time,
                error=str(e)
            )
    
    async def _check_database(self, check: HealthCheck) -> HealthCheckResult:
        """Check database connection"""
        start_time = datetime.utcnow()
        
        try:
            result = await asyncio.create_subprocess_exec(
                *check.command.split(),
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await asyncio.wait_for(
                result.communicate(), 
                timeout=check.timeout
            )
            
            response_time = (datetime.utcnow() - start_time).total_seconds()
            
            if result.returncode == 0:
                status = HealthStatus.HEALTHY
                message = "Database is healthy"
            else:
                status = HealthStatus.UNHEALTHY
                message = f"Database check failed: {stderr.decode()}"
            
            return HealthCheckResult(
                check_name=check.name,
                status=status,
                message=message,
                response_time=response_time,
                checked_at=start_time
            )
            
        except asyncio.TimeoutError:
            response_time = (datetime.utcnow() - start_time).total_seconds()
            return HealthCheckResult(
                check_name=check.name,
                status=HealthStatus.UNHEALTHY,
                message="Database check timeout",
                response_time=response_time,
                checked_at=start_time,
                error="Timeout"
            )
        except Exception as e:
            response_time = (datetime.utcnow() - start_time).total_seconds()
            return HealthCheckResult(
                check_name=check.name,
                status=HealthStatus.UNHEALTHY,
                message=f"Database check error: {e}",
                response_time=response_time,
                checked_at=start_time,
                error=str(e)
            )
    
    async def _check_redis(self, check: HealthCheck) -> HealthCheckResult:
        """Check Redis connection"""
        start_time = datetime.utcnow()
        
        try:
            result = await asyncio.create_subprocess_exec(
                *check.command.split(),
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await asyncio.wait_for(
                result.communicate(), 
                timeout=check.timeout
            )
            
            response_time = (datetime.utcnow() - start_time).total_seconds()
            
            if result.returncode == 0 and b"PONG" in stdout:
                status = HealthStatus.HEALTHY
                message = "Redis is healthy"
            else:
                status = HealthStatus.UNHEALTHY
                message = f"Redis check failed: {stderr.decode()}"
            
            return HealthCheckResult(
                check_name=check.name,
                status=status,
                message=message,
                response_time=response_time,
                checked_at=start_time
            )
            
        except asyncio.TimeoutError:
            response_time = (datetime.utcnow() - start_time).total_seconds()
            return HealthCheckResult(
                check_name=check.name,
                status=HealthStatus.UNHEALTHY,
                message="Redis check timeout",
                response_time=response_time,
                checked_at=start_time,
                error="Timeout"
            )
        except Exception as e:
            response_time = (datetime.utcnow() - start_time).total_seconds()
            return HealthCheckResult(
                check_name=check.name,
                status=HealthStatus.UNHEALTHY,
                message=f"Redis check error: {e}",
                response_time=response_time,
                checked_at=start_time,
                error=str(e)
            )
    
    async def _check_command(self, check: HealthCheck) -> HealthCheckResult:
        """Check custom command"""
        start_time = datetime.utcnow()
        
        try:
            result = await asyncio.create_subprocess_exec(
                *check.command.split(),
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await asyncio.wait_for(
                result.communicate(), 
                timeout=check.timeout
            )
            
            response_time = (datetime.utcnow() - start_time).total_seconds()
            
            if result.returncode == 0:
                status = HealthStatus.HEALTHY
                message = "Command check passed"
            else:
                status = HealthStatus.UNHEALTHY
                message = f"Command check failed: {stderr.decode()}"
            
            return HealthCheckResult(
                check_name=check.name,
                status=status,
                message=message,
                response_time=response_time,
                checked_at=start_time
            )
            
        except asyncio.TimeoutError:
            response_time = (datetime.utcnow() - start_time).total_seconds()
            return HealthCheckResult(
                check_name=check.name,
                status=HealthStatus.UNHEALTHY,
                message="Command check timeout",
                response_time=response_time,
                checked_at=start_time,
                error="Timeout"
            )
        except Exception as e:
            response_time = (datetime.utcnow() - start_time).total_seconds()
            return HealthCheckResult(
                check_name=check.name,
                status=HealthStatus.UNHEALTHY,
                message=f"Command check error: {e}",
                response_time=response_time,
                checked_at=start_time,
                error=str(e)
            )
    
    async def check_environment_health(self, environment: str) -> EnvironmentHealth:
        """Check environment health"""
        checks = self.health_checks.get(environment, [])
        if not checks:
            return EnvironmentHealth(
                environment=environment,
                overall_status=HealthStatus.UNKNOWN,
                checks=[],
                last_checked=datetime.utcnow(),
                uptime=0.0,
                version="unknown"
            )
        
        # Run all health checks
        check_results = []
        for check in checks:
            if check.enabled:
                result = await self.run_health_check(check)
                check_results.append(result)
        
        # Determine overall status
        critical_failed = any(
            result.status == HealthStatus.UNHEALTHY and 
            any(c.critical for c in checks if c.name == result.check_name)
            for result in check_results
        )
        
        if critical_failed:
            overall_status = HealthStatus.UNHEALTHY
        elif any(result.status == HealthStatus.UNHEALTHY for result in check_results):
            overall_status = HealthStatus.DEGRADED
        elif all(result.status == HealthStatus.HEALTHY for result in check_results):
            overall_status = HealthStatus.HEALTHY
        else:
            overall_status = HealthStatus.UNKNOWN
        
        return EnvironmentHealth(
            environment=environment,
            overall_status=overall_status,
            checks=check_results,
            last_checked=datetime.utcnow(),
            uptime=0.0,  # This would be calculated from actual uptime
            version="1.0.0"  # This would come from actual version
        )
    
    def start_health_monitoring(self, environment: str, interval: int = 60):
        """Start health monitoring for environment"""
        if self._health_monitor_running:
            logger.warning("Health monitoring is already running")
            return
        
        self._health_monitor_running = True
        self._health_monitor_task = asyncio.create_task(
            self._health_monitor_loop(environment, interval)
        )
        
        logger.info(f"Health monitoring started for {environment}")
    
    def stop_health_monitoring(self):
        """Stop health monitoring"""
        if self._health_monitor_task:
            self._health_monitor_task.cancel()
            self._health_monitor_task = None
        
        self._health_monitor_running = False
        logger.info("Health monitoring stopped")
    
    async def _health_monitor_loop(self, environment: str, interval: int):
        """Health monitoring loop"""
        while self._health_monitor_running:
            try:
                health = await self.check_environment_health(environment)
                
                # Store results
                with self._lock:
                    if environment not in self.health_results:
                        self.health_results[environment] = []
                    
                    self.health_results[environment].extend(health.checks)
                    
                    # Keep only last 1000 results
                    if len(self.health_results[environment]) > 1000:
                        self.health_results[environment] = self.health_results[environment][-1000:]
                
                # Log health status
                if health.overall_status == HealthStatus.UNHEALTHY:
                    logger.error(f"Environment {environment} is unhealthy")
                elif health.overall_status == HealthStatus.DEGRADED:
                    logger.warning(f"Environment {environment} is degraded")
                else:
                    logger.debug(f"Environment {environment} is healthy")
                
                await asyncio.sleep(interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Health monitoring error: {e}")
                await asyncio.sleep(interval)
    
    def get_health_history(self, environment: str, hours: int = 24) -> List[HealthCheckResult]:
        """Get health check history"""
        with self._lock:
            if environment not in self.health_results:
                return []
            
            cutoff_time = datetime.utcnow() - timedelta(hours=hours)
            return [
                result for result in self.health_results[environment]
                if result.checked_at > cutoff_time
            ]
    
    def get_environment_summary(self) -> Dict[str, Any]:
        """Get environment summary"""
        with self._lock:
            return {
                "total_environments": len(self.profiles),
                "environments": list(self.profiles.keys()),
                "health_monitoring_running": self._health_monitor_running,
                "total_health_checks": sum(len(checks) for checks in self.health_checks.values()),
                "health_results_count": sum(len(results) for results in self.health_results.values())
            }

# Global environment profile manager
_environment_manager: Optional[EnvironmentProfileManager] = None

def get_environment_manager() -> EnvironmentProfileManager:
    """Get global environment profile manager"""
    global _environment_manager
    if _environment_manager is None:
        _environment_manager = EnvironmentProfileManager()
    return _environment_manager

def cleanup_environment_manager():
    """Cleanup global environment profile manager"""
    global _environment_manager
    if _environment_manager:
        _environment_manager.stop_health_monitoring()
        _environment_manager = None
