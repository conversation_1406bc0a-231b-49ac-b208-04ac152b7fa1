"""
Configuration Management for ASK Pipeline

This module provides comprehensive configuration management:

1. Configuration Manager - Centralized configuration with validation and hot-reloading
2. Secrets Manager - Secure secrets management with encryption and rotation
3. Feature Flags - Feature flag system for gradual rollouts and A/B testing
4. Environment Profiles - Environment-specific configuration and health monitoring

Features:
- Environment-specific configuration (dev/staging/prod)
- Feature flag system for gradual rollouts
- Configuration hot-reloading without service restart
- Secure secrets management with encryption
- Environment health monitoring and validation
- Configuration validation and documentation tools
"""

from .config_manager import (
    ConfigurationManager,
    Environment,
    ConfigSource,
    ConfigValue,
    FeatureFlag,
    ConfigValidationRule,
    get_config_manager,
    cleanup_config_manager
)

from .ask_config import (
    AskConfig,
    ConfigEnvironment,
    IntentDetectionConfig,
    ToolsConfig,
    ResponseConfig,
    CacheConfig,
    MonitoringConfig,
    SecurityConfig,
    get_ask_config,
    reload_ask_config,
    cleanup_ask_config
)

from .secrets_manager import (
    SecretsManager,
    SecretType,
    SecretStatus,
    SecretMetadata,
    SecretAccess,
    get_secrets_manager,
    cleanup_secrets_manager
)

from .feature_flags import (
    SimpleFeatureFlagManager as FeatureFlagManager,
    FeatureFlagStatus,
    TargetingType,
    FeatureFlagEvaluation,
    FeatureFlagAnalytics,
    get_feature_flag_manager,
    cleanup_feature_flag_manager
)

from .environment_profiles import (
    EnvironmentProfileManager,
    EnvironmentType,
    HealthStatus,
    EnvironmentConfig,
    HealthCheck,
    HealthCheckResult,
    EnvironmentHealth,
    get_environment_manager,
    cleanup_environment_manager
)

# Convenience function for getting config
def get_config():
    """Get the global configuration manager instance"""
    return get_config_manager()

__all__ = [
    # Configuration Manager
    'ConfigurationManager',
    'Environment',
    'ConfigSource',
    'ConfigValue',
    'FeatureFlag',
    'ConfigValidationRule',
    'get_config_manager',
    'get_config',
    'cleanup_config_manager',
    
    # Ask Config
    'AskConfig',
    'ConfigEnvironment',
    'IntentDetectionConfig',
    'ToolsConfig',
    'ResponseConfig',
    'CacheConfig',
    'MonitoringConfig',
    'SecurityConfig',
    'get_ask_config',
    'reload_ask_config',
    'cleanup_ask_config',
    
    # Secrets Manager
    'SecretsManager',
    'SecretType',
    'SecretStatus',
    'SecretMetadata',
    'SecretAccess',
    'get_secrets_manager',
    'cleanup_secrets_manager',
    
    # Feature Flags
    'FeatureFlagManager',
    'FeatureFlagStatus',
    'TargetingType',
    'FeatureFlagEvaluation',
    'FeatureFlagAnalytics',
    'get_feature_flag_manager',
    'cleanup_feature_flag_manager',
    
    # Environment Profiles
    'EnvironmentProfileManager',
    'EnvironmentType',
    'HealthStatus',
    'EnvironmentConfig',
    'HealthCheck',
    'HealthCheckResult',
    'EnvironmentHealth',
    'get_environment_manager',
    'cleanup_environment_manager'
]