"""
Configuration Management System for ASK Pipeline

Provides centralized configuration management:
- Environment-specific configuration with validation
- Feature flag system for gradual rollouts
- Configuration hot-reloading without service restart
- Configuration documentation and validation tools
- Runtime configuration updates
"""

import os
import yaml
import json
import asyncio
import logging
from typing import Dict, Any, Optional, List, Union, Callable, Type
from dataclasses import dataclass, field, asdict
from datetime import datetime, timedelta
from enum import Enum
from pathlib import Path
import threading
import hashlib
import time
from contextlib import asynccontextmanager

logger = logging.getLogger(__name__)

class Environment(Enum):
    """Environment types"""
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"
    TESTING = "testing"

class ConfigSource(Enum):
    """Configuration sources"""
    FILE = "file"
    ENVIRONMENT = "environment"
    DATABASE = "database"
    API = "api"
    DEFAULT = "default"

@dataclass
class ConfigValue:
    """Configuration value with metadata"""
    value: Any
    source: ConfigSource
    timestamp: datetime
    version: int = 1
    validated: bool = False
    error: Optional[str] = None

@dataclass
class FeatureFlag:
    """Feature flag configuration"""
    name: str
    enabled: bool
    rollout_percentage: float = 100.0
    target_users: List[str] = field(default_factory=list)
    target_guilds: List[str] = field(default_factory=list)
    conditions: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.utcnow)
    expires_at: Optional[datetime] = None

@dataclass
class ConfigValidationRule:
    """Configuration validation rule"""
    key: str
    required: bool = True
    data_type: Type = str
    min_value: Optional[Union[int, float]] = None
    max_value: Optional[Union[int, float]] = None
    allowed_values: Optional[List[Any]] = None
    pattern: Optional[str] = None
    custom_validator: Optional[Callable] = None

class ConfigurationManager:
    """Centralized configuration management system"""
    
    def __init__(self, environment: Environment = Environment.DEVELOPMENT):
        self.environment = environment
        self.config: Dict[str, ConfigValue] = {}
        self.feature_flags: Dict[str, FeatureFlag] = {}
        self.validation_rules: Dict[str, ConfigValidationRule] = {}
        self.watchers: List[Callable] = []
        self._lock = threading.RLock()
        self._file_watcher = None
        self._hot_reload_enabled = True
        self._config_hash = None
        
        # Initialize default configuration
        self._load_default_config()
        self._load_validation_rules()
    
    def _load_default_config(self):
        """Load default configuration values"""
        default_config = {
            # Application settings
            "app.name": "ASK Pipeline",
            "app.version": "1.0.0",
            "app.environment": self.environment.value,
            "app.debug": self.environment == Environment.DEVELOPMENT,
            
            # Server settings
            "server.host": "0.0.0.0",
            "server.port": 8000,
            "server.workers": 1,
            "server.timeout": 30,
            
            # Database settings
            "database.host": "localhost",
            "database.port": 5432,
            "database.name": "ask_pipeline",
            "database.user": "postgres",
            "database.password": "",
            "database.pool_size": 10,
            "database.timeout": 30,
            
            # Redis settings
            "redis.host": "localhost",
            "redis.port": 6379,
            "redis.db": 0,
            "redis.password": "",
            "redis.pool_size": 10,
            "redis.timeout": 5,
            
            # AI settings
            "ai.provider": "openai",
            "ai.model": "gpt-4o-mini",
            "ai.max_tokens": 2000,
            "ai.temperature": 0.7,
            "ai.timeout": 30,
            
            # Security settings
            "security.rate_limit.enabled": True,
            "security.rate_limit.requests_per_minute": 10,
            "security.rate_limit.requests_per_hour": 100,
            "security.input_validation.enabled": True,
            "security.pii_detection.enabled": True,
            
            # Performance settings
            "performance.cache.enabled": True,
            "performance.cache.ttl": 300,
            "performance.cache.max_size": 1000,
            "performance.connection_pool.enabled": True,
            "performance.connection_pool.max_connections": 100,
            
            # Logging settings
            "logging.level": "INFO",
            "logging.format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            "logging.file.enabled": True,
            "logging.file.path": "logs/ask_pipeline.log",
            "logging.file.max_size": 10485760,  # 10MB
            "logging.file.backup_count": 5,
            
            # Monitoring settings
            "monitoring.enabled": True,
            "monitoring.metrics.enabled": True,
            "monitoring.health_check.enabled": True,
            "monitoring.health_check.interval": 30,
        }
        
        for key, value in default_config.items():
            self.config[key] = ConfigValue(
                value=value,
                source=ConfigSource.DEFAULT,
                timestamp=datetime.utcnow(),
                validated=True
            )
    
    def _load_validation_rules(self):
        """Load configuration validation rules"""
        validation_rules = [
            ConfigValidationRule("app.name", required=True, data_type=str),
            ConfigValidationRule("app.version", required=True, data_type=str),
            ConfigValidationRule("app.environment", required=True, data_type=str, 
                               allowed_values=[env.value for env in Environment]),
            ConfigValidationRule("app.debug", required=True, data_type=bool),
            
            ConfigValidationRule("server.port", required=True, data_type=int, 
                               min_value=1, max_value=65535),
            ConfigValidationRule("server.workers", required=True, data_type=int, 
                               min_value=1, max_value=32),
            ConfigValidationRule("server.timeout", required=True, data_type=int, 
                               min_value=1, max_value=300),
            
            ConfigValidationRule("database.port", required=True, data_type=int, 
                               min_value=1, max_value=65535),
            ConfigValidationRule("database.pool_size", required=True, data_type=int, 
                               min_value=1, max_value=100),
            
            ConfigValidationRule("redis.port", required=True, data_type=int, 
                               min_value=1, max_value=65535),
            ConfigValidationRule("redis.db", required=True, data_type=int, 
                               min_value=0, max_value=15),
            
            ConfigValidationRule("ai.max_tokens", required=True, data_type=int, 
                               min_value=1, max_value=8000),
            ConfigValidationRule("ai.temperature", required=True, data_type=float, 
                               min_value=0.0, max_value=2.0),
            
            ConfigValidationRule("security.rate_limit.requests_per_minute", required=True, 
                               data_type=int, min_value=1, max_value=1000),
            ConfigValidationRule("security.rate_limit.requests_per_hour", required=True, 
                               data_type=int, min_value=1, max_value=10000),
            
            ConfigValidationRule("performance.cache.ttl", required=True, data_type=int, 
                               min_value=1, max_value=86400),
            ConfigValidationRule("performance.cache.max_size", required=True, data_type=int, 
                               min_value=1, max_value=100000),
            
            ConfigValidationRule("logging.level", required=True, data_type=str, 
                               allowed_values=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]),
        ]
        
        for rule in validation_rules:
            self.validation_rules[rule.key] = rule
    
    def load_from_file(self, file_path: str) -> bool:
        """Load configuration from file"""
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                logger.warning(f"Configuration file not found: {file_path}")
                return False
            
            with open(file_path, 'r') as f:
                if file_path.suffix.lower() in ['.yaml', '.yml']:
                    config_data = yaml.safe_load(f)
                elif file_path.suffix.lower() == '.json':
                    config_data = json.load(f)
                else:
                    logger.error(f"Unsupported configuration file format: {file_path.suffix}")
                    return False
            
            # Update configuration
            for key, value in config_data.items():
                self.set_config(key, value, ConfigSource.FILE)
            
            # Start file watcher if hot reload is enabled
            if self._hot_reload_enabled:
                self._start_file_watcher(file_path)
            
            logger.info(f"Configuration loaded from file: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to load configuration from file {file_path}: {e}")
            return False
    
    def load_from_environment(self, prefix: str = "ASK_") -> bool:
        """Load configuration from environment variables"""
        try:
            env_config = {}
            for key, value in os.environ.items():
                if key.startswith(prefix):
                    # Convert environment variable name to config key
                    config_key = key[len(prefix):].lower().replace('_', '.')
                    env_config[config_key] = value
            
            # Update configuration
            for key, value in env_config.items():
                self.set_config(key, value, ConfigSource.ENVIRONMENT)
            
            logger.info(f"Configuration loaded from environment variables with prefix: {prefix}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to load configuration from environment: {e}")
            return False
    
    def set_config(self, key: str, value: Any, source: ConfigSource = ConfigSource.API) -> bool:
        """Set configuration value"""
        try:
            with self._lock:
                # Validate value
                validation_result = self._validate_config_value(key, value)
                if not validation_result["valid"]:
                    logger.error(f"Configuration validation failed for {key}: {validation_result['error']}")
                    return False
                
                # Convert value to appropriate type
                converted_value = self._convert_value(key, value)
                
                # Update configuration
                self.config[key] = ConfigValue(
                    value=converted_value,
                    source=source,
                    timestamp=datetime.utcnow(),
                    validated=True
                )
                
                # Notify watchers
                self._notify_watchers(key, converted_value)
                
                logger.debug(f"Configuration updated: {key} = {converted_value}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to set configuration {key}: {e}")
            return False
    
    def get_config(self, key: str, default: Any = None) -> Any:
        """Get configuration value"""
        with self._lock:
            if key in self.config:
                return self.config[key].value
            return default
    
    def get_config_with_metadata(self, key: str) -> Optional[ConfigValue]:
        """Get configuration value with metadata"""
        with self._lock:
            return self.config.get(key)
    
    def _validate_config_value(self, key: str, value: Any) -> Dict[str, Any]:
        """Validate configuration value"""
        if key not in self.validation_rules:
            return {"valid": True, "error": None}
        
        rule = self.validation_rules[key]
        
        # Check required
        if rule.required and value is None:
            return {"valid": False, "error": f"Required configuration {key} is missing"}
        
        # Check data type
        if rule.data_type and not isinstance(value, rule.data_type):
            try:
                converted = rule.data_type(value)
            except (ValueError, TypeError):
                return {"valid": False, "error": f"Configuration {key} must be of type {rule.data_type.__name__}"}
        
        # Check min/max values
        if rule.min_value is not None and value < rule.min_value:
            return {"valid": False, "error": f"Configuration {key} must be >= {rule.min_value}"}
        if rule.max_value is not None and value > rule.max_value:
            return {"valid": False, "error": f"Configuration {key} must be <= {rule.max_value}"}
        
        # Check allowed values
        if rule.allowed_values and value not in rule.allowed_values:
            return {"valid": False, "error": f"Configuration {key} must be one of {rule.allowed_values}"}
        
        # Check pattern
        if rule.pattern:
            import re
            if not re.match(rule.pattern, str(value)):
                return {"valid": False, "error": f"Configuration {key} must match pattern {rule.pattern}"}
        
        # Check custom validator
        if rule.custom_validator:
            try:
                if not rule.custom_validator(value):
                    return {"valid": False, "error": f"Configuration {key} failed custom validation"}
            except Exception as e:
                return {"valid": False, "error": f"Configuration {key} custom validation error: {e}"}
        
        return {"valid": True, "error": None}
    
    def _convert_value(self, key: str, value: Any) -> Any:
        """Convert value to appropriate type"""
        if key not in self.validation_rules:
            return value
        
        rule = self.validation_rules[key]
        if rule.data_type:
            try:
                return rule.data_type(value)
            except (ValueError, TypeError):
                return value
        
        return value
    
    def _notify_watchers(self, key: str, value: Any):
        """Notify configuration watchers"""
        for watcher in self.watchers:
            try:
                watcher(key, value)
            except Exception as e:
                logger.error(f"Configuration watcher error: {e}")
    
    def add_watcher(self, watcher: Callable[[str, Any], None]):
        """Add configuration watcher"""
        self.watchers.append(watcher)
    
    def remove_watcher(self, watcher: Callable[[str, Any], None]):
        """Remove configuration watcher"""
        if watcher in self.watchers:
            self.watchers.remove(watcher)
    
    def _start_file_watcher(self, file_path: Path):
        """Start file watcher for hot reloading"""
        if self._file_watcher:
            self._file_watcher.stop()
        
        try:
            import watchdog
            from watchdog.observers import Observer
            from watchdog.events import FileSystemEventHandler
            
            class ConfigFileHandler(FileSystemEventHandler):
                def __init__(self, config_manager):
                    self.config_manager = config_manager
                    self.last_modified = 0
                
                def on_modified(self, event):
                    if event.is_directory:
                        return
                    
                    if event.src_path == str(file_path):
                        current_time = time.time()
                        if current_time - self.last_modified > 1:  # Debounce
                            self.last_modified = current_time
                            asyncio.create_task(self._reload_config())
                
                async def _reload_config(self):
                    try:
                        await asyncio.sleep(0.5)  # Wait for file to be written
                        self.config_manager.load_from_file(str(file_path))
                        logger.info("Configuration reloaded from file")
                    except Exception as e:
                        logger.error(f"Failed to reload configuration: {e}")
            
            self._file_watcher = Observer()
            handler = ConfigFileHandler(self)
            self._file_watcher.schedule(handler, str(file_path.parent), recursive=False)
            self._file_watcher.start()
            
        except ImportError:
            logger.warning("watchdog not available, file watching disabled")
        except Exception as e:
            logger.error(f"Failed to start file watcher: {e}")
    
    def enable_hot_reload(self, enabled: bool = True):
        """Enable or disable hot reloading"""
        self._hot_reload_enabled = enabled
        if not enabled and self._file_watcher:
            self._file_watcher.stop()
            self._file_watcher = None
    
    def get_all_config(self) -> Dict[str, Any]:
        """Get all configuration values"""
        with self._lock:
            return {key: config_value.value for key, config_value in self.config.items()}
    
    def get_config_by_source(self, source: ConfigSource) -> Dict[str, Any]:
        """Get configuration values by source"""
        with self._lock:
            return {
                key: config_value.value 
                for key, config_value in self.config.items() 
                if config_value.source == source
            }
    
    def export_config(self, file_path: str, format: str = "yaml") -> bool:
        """Export configuration to file"""
        try:
            config_data = self.get_all_config()
            
            with open(file_path, 'w') as f:
                if format.lower() == 'yaml':
                    yaml.dump(config_data, f, default_flow_style=False, indent=2)
                elif format.lower() == 'json':
                    json.dump(config_data, f, indent=2)
                else:
                    logger.error(f"Unsupported export format: {format}")
                    return False
            
            logger.info(f"Configuration exported to {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to export configuration: {e}")
            return False
    
    def validate_all_config(self) -> Dict[str, List[str]]:
        """Validate all configuration values"""
        errors = {}
        
        with self._lock:
            for key, config_value in self.config.items():
                validation_result = self._validate_config_value(key, config_value.value)
                if not validation_result["valid"]:
                    if key not in errors:
                        errors[key] = []
                    errors[key].append(validation_result["error"])
        
        return errors
    
    def get_config_summary(self) -> Dict[str, Any]:
        """Get configuration summary"""
        with self._lock:
            total_configs = len(self.config)
            validated_configs = sum(1 for cv in self.config.values() if cv.validated)
            sources = {}
            
            for config_value in self.config.values():
                source = config_value.source.value
                sources[source] = sources.get(source, 0) + 1
            
            return {
                "total_configurations": total_configs,
                "validated_configurations": validated_configs,
                "validation_rate": validated_configs / total_configs if total_configs > 0 else 0,
                "sources": sources,
                "environment": self.environment.value,
                "hot_reload_enabled": self._hot_reload_enabled
            }
    
    def cleanup(self):
        """Cleanup configuration manager"""
        if self._file_watcher:
            self._file_watcher.stop()
            self._file_watcher = None
        
        with self._lock:
            self.watchers.clear()
            self.config.clear()
            self.feature_flags.clear()

# Global configuration manager
_config_manager: Optional[ConfigurationManager] = None

def get_config_manager(environment: Environment = Environment.DEVELOPMENT) -> ConfigurationManager:
    """Get global configuration manager"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigurationManager(environment)
    return _config_manager

def cleanup_config_manager():
    """Cleanup global configuration manager"""
    global _config_manager
    if _config_manager:
        _config_manager.cleanup()
        _config_manager = None