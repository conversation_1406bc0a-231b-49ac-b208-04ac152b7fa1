"""
Feature Flags for ASK Pipeline - Simplified Refactored Version

Simple feature flag system using core ConfigManager.
Flags stored as config keys; evaluate directly from config.
No advanced targeting; simple enabled/disabled.
"""
from typing import Dict, Any, Optional
from enum import Enum

from src.core.config_manager import CoreConfigManager as ConfigManager
from src.shared.error_handling.logging import get_logger

logger = get_logger(__name__)


class FeatureFlagStatus(str, Enum):
    """Flag status."""
    ENABLED = "enabled"
    DISABLED = "disabled"


class SimpleFeatureFlagManager:
    """
    Simple feature flag manager using core config.
    Flags as "ask.feature_flags.<name>": true/false
    """
    def __init__(self, config_manager: ConfigManager):
        self.config = config_manager

    def is_enabled(self, flag_name: str, default: bool = False) -> bool:
        """Check if flag enabled."""
        key = f"ask.feature_flags.{flag_name}"
        return self.config.get(key, default)

    def set_flag(self, flag_name: str, enabled: bool):
        """Set flag."""
        key = f"ask.feature_flags.{flag_name}"
        self.config.set(key, enabled)
        logger.info(f"Feature flag {flag_name} set to {enabled}")

    def get_flags(self) -> Dict[str, bool]:
        """Get all flags."""
        flags = {}
        for key in self.config.get_all_keys():
            if key.startswith("ask.feature_flags."):
                flag = key.split(".", 2)[-1]
                flags[flag] = self.config.get(key, False)
        return flags


# Global
_manager = None


def get_feature_flag_manager(config_manager: ConfigManager) -> SimpleFeatureFlagManager:
    global _manager
    if _manager is None:
        _manager = SimpleFeatureFlagManager(config_manager)
    return _manager

# Backward compatibility aliases
FeatureFlagManager = SimpleFeatureFlagManager

# Stub classes for compatibility (minimal implementation)
class TargetingType(Enum):
    USER = "user"
    PERCENTAGE = "percentage"
    ALL = "all"

class FeatureFlagEvaluation:
    def __init__(self, flag_name: str, enabled: bool, reason: str = ""):
        self.flag_name = flag_name
        self.enabled = enabled
        self.reason = reason

class FeatureFlagAnalytics:
    def __init__(self):
        self.impressions = 0
        self.conversions = 0

def cleanup_feature_flag_manager():
    global _manager
    _manager = None