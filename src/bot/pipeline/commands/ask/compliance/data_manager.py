"""
Data Manager for ASK Pipeline Compliance - Simplified Refactored Version

Minimal data logging for compliance/audit; integrates with core audit_logger and unified_db.
Tracks data access, anonymization for queries; no full GDPR system.
Merged with core: use AuditLogModel for persistence, basic anonymization.
Deleted overkill like full rights management, reports - handle via audit.
Focus on bot: log query data access, anonymize user info if needed.
"""
import asyncio
import hashlib
from typing import Dict, Any, Optional
from datetime import datetime

from src.shared.error_handling.logging import get_logger
from src.database.unified_db import get_db_session, DataAccessLogModel  # Core DB for logs
from src.core.prompts.utils.validation import validate_query_safety

logger = get_logger(__name__)


class DataClassification(str, Enum):
    """Simple data classification."""
    PUBLIC = "public"
    INTERNAL = "internal"
    SENSITIVE = "sensitive"


class SimpleDataManager:
    """
    Basic data manager for compliance - logs access, anonymizes if sensitive.
    Uses core DB for access logs; no retention policies (use audit cleanup).
    """
    def __init__(self):
        self.db_session = get_db_session()

    async def log_data_access(self, query: str, user_id: str, correlation_id: str, classification: DataClassification = DataClassification.INTERNAL):
        """Log data access for audit."""
        # Basic anonymization if sensitive
        anonymized_query = query
        if classification == DataClassification.SENSITIVE:
            anonymized_query = hashlib.sha256(query.encode()).hexdigest()[:16]
        
        # Validate safety
        is_safe = validate_query_safety(query)
        if not is_safe:
            logger.warning(f"Unsafe query logged: {anonymized_query}", extra={'correlation_id': correlation_id})
        
        async with self.db_session() as session:
            log = DataAccessLogModel(
                correlation_id=correlation_id,
                user_id=user_id,
                query_hash=hashlib.md5(query.encode()).hexdigest(),
                anonymized_query=anonymized_query,
                classification=classification.value,
                access_time=datetime.utcnow(),
                is_safe=is_safe
            )
            session.add(log)
            await session.commit()
        
        logger.debug(f"Data access logged for {classification.value}", extra={'correlation_id': correlation_id})

    def anonymize_query(self, query: str) -> str:
        """Simple anonymization."""
        if '@' in query:  # Email-like
            local, domain = query.split('@', 1)
            return f"{local[0]}***@{domain}"
        return hashlib.sha256(query.encode()).hexdigest()[:20]

    async def check_retention(self, record_id: str) -> bool:
        """Check if record should be retained (simple)."""
        async with self.db_session() as session:
            log = await session.get(DataAccessLogModel, record_id)
            if log and log.access_time < datetime.utcnow() - timedelta(days=30):  # Example policy
                return False
            return True


# Global manager
_data_manager: Optional[SimpleDataManager] = None


def get_data_manager() -> SimpleDataManager:
    global _data_manager
    if _data_manager is None:
        _data_manager = SimpleDataManager()
    return _data_manager


async def setup_data_manager():
    """Setup compliance data manager."""
    manager = get_data_manager()
    logger.info("Simple data manager for compliance initialized")
    return manager