"""
Security Manager for ASK Pipeline

Provides comprehensive security features:
- Input validation and sanitization
- Rate limiting and throttling
- Suspicious activity detection
- Security event logging and alerting
- Data encryption and protection
"""

import asyncio
import logging
import time
import hashlib
import uuid
from typing import Dict, Any, Optional, List, Union, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import re
from collections import defaultdict

from src.shared.error_handling.logging import get_logger
from src.shared.cache.cache_service import cache_service
from src.bot.pipeline.commands.ask.audit import get_audit_logger

logger = get_logger(__name__)

class SecurityThreat(Enum):
    """Security threat types"""
    SQL_INJECTION = "sql_injection"
    XSS = "xss"
    COMMAND_INJECTION = "command_injection"
    RATE_LIMIT_EXCEEDED = "rate_limit_exceeded"
    SUSPICIOUS_ACTIVITY = "suspicious_activity"
    MALFORMED_INPUT = "malformed_input"
    UNAUTHORIZED_ACCESS = "unauthorized_access"

class ValidationRule(Enum):
    """Input validation rules"""
    LENGTH = "length"
    PATTERN = "pattern"
    TYPE = "type"
    SANITIZATION = "sanitization"
    WHITELIST = "whitelist"
    BLACKLIST = "blacklist"

@dataclass
class SecurityEvent:
    """Security event record"""
    event_id: str
    threat_type: SecurityThreat
    severity: str  # low, medium, high, critical
    timestamp: datetime
    user_id: Optional[str] = None
    ip_address: Optional[str] = None
    session_id: Optional[str] = None
    input_data: Dict[str, Any] = field(default_factory=dict)
    action_taken: str = "logged"
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class RateLimitConfig:
    """Rate limiting configuration"""
    max_requests_per_minute: int = 100
    max_requests_per_hour: int = 1000
    burst_limit: int = 5
    window_size: int = 60  # seconds
    enabled: bool = True

class SecurityManager:
    """Comprehensive security manager for ASK pipeline"""

    def __init__(self):
        self.rate_limit_configs: Dict[str, RateLimitConfig] = {}
        self.suspicious_patterns = {
            'sql_injection': [r'select.*from', r'insert into', r'update.*set', r'drop table', r'--', r';--', r'@@version', r'sysobjects'],
            'xss': [r'<script>', r'javascript:', r'onload*=', r'onerror*=', r'onclick*=', r'alert\('],
            'command_injection': [r';', r'&&', r'\\|', r'\\|', r'\\$', r'\\(', r'\\)', r'\\<', r'\\>'],
            'malformed': [r'[<>\'";]', r'\\x00', r'%00', r'null[\s]*=', r'javascript[\s]*:'],
        }
        self.threat_thresholds = {
            'suspicious_patterns': 3,  # Number of suspicious patterns to flag
            'rapid_requests': 10,  # Requests in 10 seconds
            'unusual_patterns': 5  # Unusual input patterns
        }
        self.security_events: List[SecurityEvent] = []
        self.blocked_ips: set = set()
        self.suspicious_users: Dict[str, int] = defaultdict(int)

        # Initialize rate limiting
        self._initialize_rate_limits()

        logger.info("✅ Security manager initialized")

    def _initialize_rate_limits(self):
        """Initialize rate limiting configurations"""
        self.rate_limit_configs = {
            'global': RateLimitConfig(max_requests_per_minute=100, burst_limit=5),
            'user': RateLimitConfig(max_requests_per_minute=50, burst_limit=3),
            'ip': RateLimitConfig(max_requests_per_minute=200, burst_limit=10),
            'api': RateLimitConfig(max_requests_per_minute=1000, burst_limit=50)
        }

    async def validate_input(self, input_data: Dict[str, Any], validation_rules: Dict[str, ValidationRule] = None) -> Tuple[bool, List[str]]:
        """Validate input data against security rules"""
        errors = []

        if validation_rules is None:
            validation_rules = {
                'query': [ValidationRule.LENGTH, ValidationRule.PATTERN, ValidationRule.SANITIZATION],
                'user_id': [ValidationRule.TYPE, ValidationRule.LENGTH],
                'session_id': [ValidationRule.TYPE, ValidationRule.PATTERN]
            }

        for field, rules in validation_rules.items():
            if field not in input_data:
                continue

            value = input_data[field]
            field_errors = []

            for rule in rules:
                if rule == ValidationRule.LENGTH:
                    if isinstance(value, str) and len(value) > 1000:
                        field_errors.append(f"{field}: Length exceeds 1000 characters")
                    elif isinstance(value, list) and len(value) > 100:
                        field_errors.append(f"{field}: List exceeds 100 items")

                elif rule == ValidationRule.PATTERN:
                    if isinstance(value, str):
                        suspicious_matches = self._check_suspicious_patterns(value)
                        if suspicious_matches:
                            field_errors.append(f"{field}: Suspicious patterns detected: {suspicious_matches}")

                elif rule == ValidationRule.TYPE:
                    if not self._validate_type(field, value):
                        field_errors.append(f"{field}: Invalid data type")

                elif rule == ValidationRule.SANITIZATION:
                    sanitized = self._sanitize_input(value)
                    if sanitized != value:
                        input_data[field] = sanitized
                        field_errors.append(f"{field}: Input was sanitized")

                elif rule == ValidationRule.WHITELIST:
                    if not self._validate_whitelist(field, value):
                        field_errors.append(f"{field}: Value not in whitelist")

                elif rule == ValidationRule.BLACKLIST:
                    if self._check_blacklist(field, value):
                        field_errors.append(f"{field}: Value matches blacklist")

            if field_errors:
                errors.extend(field_errors)

        return len(errors) == 0, errors

    def _check_suspicious_patterns(self, value: str) -> List[str]:
        """Check for suspicious patterns in input"""
        suspicious = []
        value_lower = value.lower()

        for threat_type, patterns in self.suspicious_patterns.items():
            for pattern in patterns:
                if re.search(pattern, value_lower):
                    suspicious.append(threat_type)  # threat_type is already a string
                    break

        return list(set(suspicious))

    def _validate_type(self, field: str, value: Any) -> bool:
        """Validate field type"""
        type_rules = {
            'user_id': str,
            'session_id': str,
            'query': str,
            'timestamp': (int, float),
            'count': int
        }

        expected_type = type_rules.get(field)
        if expected_type is None:
            return True

        if isinstance(expected_type, tuple):
            return isinstance(value, expected_type)
        return isinstance(value, expected_type)

    def _sanitize_input(self, value: Any) -> Any:
        """Sanitize input data"""
        if isinstance(value, str):
            # Basic HTML/JS sanitization
            value = re.sub(r'<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>', '', value, flags=re.IGNORECASE)
            value = re.sub(r'on\w+\s*=', '', value, flags=re.IGNORECASE)
            value = re.sub(r'javascript\s*:', '', value, flags=re.IGNORECASE)
            value = re.sub(r'vbscript\s*:', '', value, flags=re.IGNORECASE)
            # Remove null bytes
            value = value.replace('\x00', '')
        elif isinstance(value, list):
            value = [self._sanitize_input(item) for item in value]
        elif isinstance(value, dict):
            value = {k: self._sanitize_input(v) for k, v in value.items()}

        return value

    def _validate_whitelist(self, field: str, value: Any) -> bool:
        """Validate against whitelist"""
        whitelists = {
            'user_id': r'^[a-zA-Z0-9_-]+$',
            'session_id': r'^[a-f0-9-]{36}$',  # UUID format
        }

        pattern = whitelists.get(field)
        if pattern and isinstance(value, str):
            return bool(re.match(pattern, value))

        return True

    def _check_blacklist(self, field: str, value: Any) -> bool:
        """Check against blacklist"""
        blacklists = {
            'query': [r'drop table', r'delete from', r'update.*set', r'grant ', r'revoke '],
            'user_id': [r'admin', r'root', r'system', r'dbadmin']
        }

        patterns = blacklists.get(field, [])
        if patterns and isinstance(value, str):
            for pattern in patterns:
                if re.search(pattern, value, re.IGNORECASE):
                    return True

        return False

    async def check_rate_limit(self, identifier: str, limit_type: str = "global") -> bool:
        """Check rate limit for identifier"""
        if not self.rate_limit_configs.get(limit_type, {}).enabled:
            return True

        config = self.rate_limit_configs[limit_type]
        cache_key = f"rate_limit:{limit_type}:{identifier}"
        request_times = await cache_service.get(cache_key) or []

        current_time = time.time()
        window_start = current_time - config.window_size

        # Filter requests within time window
        valid_requests = [t for t in request_times if t > window_start]
        
        # Check burst limit
        if len(valid_requests) >= config.burst_limit:
            logger.warning(f"Burst limit exceeded for {identifier} ({limit_type})")
            return False

        # Check rate limit
        if len(valid_requests) >= config.max_requests_per_minute:
            logger.warning(f"Rate limit exceeded for {identifier} ({limit_type})")
            await self._record_security_event(
                threat_type=SecurityThreat.RATE_LIMIT_EXCEEDED,
                severity="medium",
                identifier=identifier,
                metadata={'limit_type': limit_type, 'requests': len(valid_requests)}
            )
            return False

        # Add current request
        valid_requests.append(current_time)
        await cache_service.set(cache_key, valid_requests, ttl=config.window_size)

        return True

    async def _record_security_event(self, threat_type: SecurityThreat, severity: str,
                                   identifier: str, metadata: Dict[str, Any] = None):
        """Record security event"""
        event_id = str(uuid.uuid4())
        event = SecurityEvent(
            event_id=event_id,
            threat_type=threat_type,
            severity=severity,
            timestamp=datetime.now(),
            metadata=metadata or {}
        )

        self.security_events.append(event)

        # Log security event
        logger.warning(f"Security event: {threat_type.value} for {identifier} (severity: {severity})")

        # Audit log
        audit_logger = get_audit_logger()
        audit_logger.log_security_event(
            event_description=f"{threat_type.value} detected for {identifier}",
            severity="high" if severity in ["high", "critical"] else "medium",
            metadata={
                'security_threat': threat_type.value,
                'severity': severity,
                'identifier': identifier[:8] if identifier else None  # Truncate for privacy
            }
        )

        # Block if critical
        if severity == "critical":
            self.blocked_ips.add(identifier)
            logger.critical(f"CRITICAL security event: {threat_type.value} - {identifier} blocked")

    def is_blocked(self, identifier: str) -> bool:
        """Check if identifier is blocked"""
        return identifier in self.blocked_ips

    def unblock(self, identifier: str) -> bool:
        """Unblock identifier"""
        if identifier in self.blocked_ips:
            self.blocked_ips.remove(identifier)
            logger.info(f"Unblocked {identifier}")
            return True
        return False

    async def scan_for_threats(self, input_data: Dict[str, Any]) -> List[SecurityThreat]:
        """Scan input data for security threats"""
        threats = []

        for field, value in input_data.items():
            if isinstance(value, str):
                suspicious = self._check_suspicious_patterns(value)
                if len(suspicious) >= self.threat_thresholds['suspicious_patterns']:
                    threats.append(SecurityThreat.SUSPICIOUS_ACTIVITY)
                    await self._record_security_event(
                        threat_type=SecurityThreat.SUSPICIOUS_ACTIVITY,
                        severity="high",
                        identifier=field,
                        metadata={'field': field, 'suspicious_patterns': suspicious}
                    )

        return threats

    def get_security_summary(self) -> Dict[str, Any]:
        """Get security summary"""
        return {
            "total_events": len(self.security_events),
            "events_by_threat": {},
            "blocked_ips_count": len(self.blocked_ips),
            "suspicious_users_count": len([u for u, count in self.suspicious_users.items() if count >= self.threat_thresholds['unusual_patterns']]),
            "recent_events": len([e for e in self.security_events if e.timestamp > datetime.now() - timedelta(hours=24)])
        }

# Global security manager instance
security_manager = SecurityManager()

def validate_request_input(input_data: Dict[str, Any]) -> Tuple[bool, List[str]]:
    """Validate request input for security"""
    return security_manager.validate_input(input_data)

async def check_rate_limit(identifier: str, limit_type: str = "global") -> bool:
    """Check rate limit for request"""
    return await security_manager.check_rate_limit(identifier, limit_type)

def scan_input_for_threats(input_data: Dict[str, Any]) -> List[SecurityThreat]:
    """Scan input for security threats"""
    return security_manager.scan_for_threats(input_data)

def get_security_summary() -> Dict[str, Any]:
    """Get current security summary"""
    return security_manager.get_security_summary()