"""
Simplified Dependency Management for ASK Pipeline

Provides basic dependency management:
- Generate lock file using pip freeze
- Run basic safety vulnerability check
- Log placeholders for updates and validation
"""

import subprocess
import sys
import json
import logging
from typing import Dict, Any, List, Optional
from pathlib import Path
from datetime import datetime

logger = logging.getLogger(__name__)

class DependencyStatus:
    """Simple dependency status"""
    UP_TO_DATE = "up_to_date"
    OUTDATED = "outdated"
    VULNERABLE = "vulnerable"
    UNKNOWN = "unknown"

class DependencyManager:
    """Simplified dependency manager"""
    
    def __init__(self, requirements_file: str = "requirements.txt", lock_file: str = "requirements.lock"):
        self.requirements_file = Path(requirements_file)
        self.lock_file = Path(lock_file)
        self.dependencies: Dict[str, Dict[str, Any]] = {}
        self.vulnerabilities: List[Dict[str, Any]] = []
    
    def scan_dependencies(self) -> Dict[str, Any]:
        """Basic dependency scan using pip and safety"""
        logger.info("Starting simplified dependency scan")
        
        try:
            # Load dependencies from requirements
            self._load_dependencies()
            
            # Check for vulnerabilities with safety
            self._run_safety_scan()
            
            # Check for outdated packages
            self._check_outdated()
            
            scan_results = {
                "scan_timestamp": datetime.utcnow().isoformat(),
                "total_dependencies": len(self.dependencies),
                "vulnerable_dependencies": len(self.vulnerabilities),
                "outdated_dependencies": len([d for d in self.dependencies.values() if d.get("status") == DependencyStatus.OUTDATED]),
                "dependencies": self.dependencies,
                "vulnerabilities": self.vulnerabilities
            }
            
            logger.info("Simplified dependency scan completed")
            return scan_results
            
        except Exception as e:
            logger.error(f"Dependency scan failed: {e}")
            return {"error": str(e)}
    
    def _load_dependencies(self):
        """Load dependencies from requirements file"""
        if not self.requirements_file.exists():
            logger.warning(f"Requirements file {self.requirements_file} not found")
            return
        
        try:
            with open(self.requirements_file, 'r') as f:
                requirements = f.read().strip().split('\n')
            
            for req in requirements:
                if req.strip() and not req.startswith('#'):
                    # Simple parse: assume package==version or package
                    if '==' in req:
                        package, version = req.split('==', 1)
                    else:
                        package, version = req.strip(), "unknown"
                    
                    self.dependencies[package] = {
                        "name": package,
                        "current_version": version,
                        "latest_version": "unknown",
                        "status": DependencyStatus.UNKNOWN
                    }
            
            logger.info(f"Loaded {len(self.dependencies)} dependencies")
            
        except Exception as e:
            logger.error(f"Failed to load dependencies: {e}")
    
    def _run_safety_scan(self):
        """Run safety vulnerability scan"""
        try:
            # Check if safety is available
            result = subprocess.run([sys.executable, "-m", "safety", "--version"], 
                                  capture_output=True, text=True)
            if result.returncode != 0:
                logger.warning("Safety not available, skipping vulnerability scan")
                return
            
            # Run safety check
            result = subprocess.run([
                sys.executable, "-m", "safety", "check", 
                "--json", "--file", str(self.requirements_file)
            ], capture_output=True, text=True)
            
            if result.returncode == 0 and result.stdout:
                try:
                    data = json.loads(result.stdout)
                    for vuln_data in data:
                        vuln = {
                            "cve_id": vuln_data.get("cve_id", "unknown"),
                            "severity": vuln_data.get("severity", "unknown"),
                            "description": vuln_data.get("description", ""),
                            "package": vuln_data.get("package_name", "unknown")
                        }
                        self.vulnerabilities.append(vuln)
                        
                        # Update dependency status
                        package = vuln["package"]
                        if package in self.dependencies:
                            self.dependencies[package]["status"] = DependencyStatus.VULNERABLE
                            if "vulnerabilities" not in self.dependencies[package]:
                                self.dependencies[package]["vulnerabilities"] = []
                            self.dependencies[package]["vulnerabilities"].append(vuln)
                except json.JSONDecodeError:
                    logger.warning("Failed to parse safety output")
            else:
                logger.warning(f"Safety scan output: {result.stderr}")
                
        except Exception as e:
            logger.error(f"Safety scan error: {e}")
    
    def _check_outdated(self):
        """Check for outdated packages using pip"""
        try:
            result = subprocess.run([
                sys.executable, "-m", "pip", "list", "--outdated", "--format=json"
            ], capture_output=True, text=True)
            
            if result.returncode == 0 and result.stdout:
                outdated = json.loads(result.stdout)
                for package_data in outdated:
                    package_name = package_data["name"]
                    if package_name in self.dependencies:
                        self.dependencies[package_name]["latest_version"] = package_data["latest_version"]
                        self.dependencies[package_name]["status"] = DependencyStatus.OUTDATED
                        logger.info(f"Outdated: {package_name} ({package_data['version']} -> {package_data['latest_version']})")
            
        except Exception as e:
            logger.error(f"Outdated check failed: {e}")
    
    def generate_lock_file(self) -> bool:
        """Generate lock file with pip freeze"""
        try:
            result = subprocess.run([
                sys.executable, "-m", "pip", "freeze"
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                with open(self.lock_file, 'w') as f:
                    f.write(result.stdout)
                logger.info(f"Generated lock file: {self.lock_file}")
                return True
            else:
                logger.error(f"Failed to generate lock file: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"Error generating lock file: {e}")
            return False
    
    def update_dependencies_placeholder(self, packages: Optional[List[str]] = None) -> Dict[str, Any]:
        """Placeholder for updating dependencies (log only)"""
        if not packages:
            packages = list(self.dependencies.keys())
        
        logger.info(f"Placeholder: Would update packages {packages} using pip install --upgrade")
        return {
            "updated": packages,
            "note": "In production, implement actual pip upgrade with testing"
        }

# Global function
def get_dependency_manager() -> DependencyManager:
    """Get simplified dependency manager"""
    return DependencyManager()