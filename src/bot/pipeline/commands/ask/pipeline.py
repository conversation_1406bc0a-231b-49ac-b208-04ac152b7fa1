"""
ASK Pipeline - Legacy Wrapper

This file now serves as a legacy wrapper around the refactored core components.
The actual pipeline logic has been moved to the core module for better organization.

This maintains backward compatibility while using the new modular architecture.
"""

from .core import AskPipelineController, PipelineResult

# Hotfix for compatibility issue where a ProcessingResult object is treated like a dict.
# This adds a .get() method to the PipelineResult class to mimic dict behavior,
# resolving the AttributeError until the core components can be updated to use
# direct attribute access.
if not hasattr(PipelineResult, 'get'):
    def get_attr_compat(self, key, default=None):
        """Provides dict-like .get() compatibility for attribute access."""
        return getattr(self, key, default)
    PipelineResult.get = get_attr_compat


# Legacy compatibility - redirect to new controller
class AskPipeline(AskPipelineController):
    """
    Legacy ASK Pipeline wrapper
    
    This class maintains backward compatibility by inheriting from the new
    AskPipelineController. All functionality is now handled by the core components.
    """
    pass
