"""
Intent Detector for ASK Pipeline - Enhanced Version

AI-powered intent classification using unified_ai_processor for fast, accurate
classification. Enhanced with core prompts for better intent detection, caching
via cache_service, and integration with pipeline_engine for context-aware
detection. Supports entity extraction and confidence scoring with fallback
to keyword-based for reliability.
"""
import time
import json
import asyncio
from typing import Optional
from dataclasses import dataclass

from src.shared.error_handling.logging import get_logger
from src.shared.ai_services.unified_ai_processor import UnifiedAIProcessor  # New unified AI
from src.core.prompts.prompt_manager import PromptManager  # Core prompts
from src.shared.cache.cache_service import cache_service  # Caching
from src.bot.utils.input_sanitizer import InputSanitizer  # For query cleaning
from src.services.ai.enhanced_intent_detector import enhanced_intent_detector, IntentAnalysis
from enum import Enum
from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional, Tuple

logger = get_logger(__name__)


@dataclass
class IntentResult:
    """Enhanced intent result with entities and context."""
    intent: str  # "casual" or "data_needed"
    confidence: float  # 0.0 to 1.0
    reasoning: str
    execution_time: float = 0.0
    entities: dict = None  # Extracted entities (symbols, timeframes, etc.)
    method_used: str = "ai"  # ai or keywords
    # Enhanced fields from src(old)
    primary_intent: Optional[str] = None
    secondary_intents: List[str] = field(default_factory=list)
    urgency_level: str = "medium"
    response_style: str = "detailed"
    context_clues: List[str] = field(default_factory=list)
    processing_time: float = 0.0
    # Compatibility field expected by older tests
    metadata: dict = field(default_factory=dict)


class IntentDetector:
    """
    Enhanced AI-Powered Intent Detection Stage

    Uses unified_ai_processor for classification, core prompts for accuracy,
    and caching for performance. Fallback to keyword-based ensures reliability.
    Now enhanced with advanced AI intent detection from src(old).
    """

    def __init__(self):
        self.ai_processor = UnifiedAIProcessor()  # New unified
        self.prompt_manager = PromptManager()
        self.intent_cache = cache_service
        self.cache_ttl = 300  # 5 minutes
        self.enhanced_detector = enhanced_intent_detector  # Advanced AI detector
        self.max_retries = 3
        self.retry_delay = 1.0

        # Enhanced keywords for fallback
        self.data_keywords = [
            'price', 'stock', 'ticker', 'chart', 'analysis', 'buy', 'sell',
            'market', 'trading', 'volume', 'earnings', 'news', 'forecast',
            'trend', 'support', 'resistance', 'technical', 'fundamental',
            'options', 'futures', 'crypto', 'bitcoin', 'ethereum', 'portfolio'
        ]
        self.casual_keywords = [
            'hello', 'hi', 'thanks', 'thank you', 'help', 'how are you',
            'what can you do', 'explain', 'learn', 'tutorial', 'guide', 'joke'
        ]

    async def detect(self, query: str, correlation_id: str = "") -> IntentResult:
        """
        Detect intent with enhanced AI and fallback.
        Order: AI -> Enhanced AI -> Keyword fallback (to satisfy legacy tests).
        """
        start_time = time.time()

        # Sanitize query
        sanitized_query, is_valid, error_msg = await InputSanitizer.sanitize_query(query)

        # Treat empty-after-trim as invalid as well (legacy tests expectation)
        if not is_valid or not (sanitized_query or "").strip():
            logger.warning(f"Invalid query detected: {error_msg}")
            # Legacy behavior: default to casual for empty/invalid queries
            return IntentResult(
                intent="casual",
                confidence=0.2,
                reasoning="keyword-based fallback: empty or invalid query",
                execution_time=time.time() - start_time,
                metadata={"error": error_msg, "original_query": query}
            )

        # Cache check
        cache_key = self._get_cache_key(sanitized_query)
        cached = await self.intent_cache.get(cache_key)
        if cached:
            logger.debug(f"Cached intent for {correlation_id}")
            cached.execution_time = time.time() - start_time
            return cached

        # First try unified AI processor; on error, fall back to keywords (per tests)
        try:
            result = await self._detect_with_ai(sanitized_query, correlation_id)
            if result:
                await self.intent_cache.set(cache_key, result, ttl=self.cache_ttl)
                result.execution_time = time.time() - start_time
                return result
        except Exception as e:
            logger.warning(f"AI detection failed for {correlation_id}: {e}")
            # Per legacy tests, on AI failure go straight to keyword fallback
            result = await self._detect_with_keywords(sanitized_query, correlation_id)
            result.execution_time = time.time() - start_time
            result.method_used = "keywords"
            logger.info(f"Intent detected for {correlation_id}: {result.intent} ({result.confidence:.2f}) via {result.method_used}")
            return result

        # If AI returns None, try enhanced AI; if that fails, use keywords
        enhanced_result = await self._detect_with_enhanced_ai(sanitized_query, correlation_id)
        if enhanced_result:
            await self.intent_cache.set(cache_key, enhanced_result, ttl=self.cache_ttl)
            enhanced_result.execution_time = time.time() - start_time
            return enhanced_result

        # Final fallback to keywords
        result = await self._detect_with_keywords(sanitized_query, correlation_id)
        result.execution_time = time.time() - start_time
        result.method_used = "keywords"

        logger.info(f"Intent detected for {correlation_id}: {result.intent} ({result.confidence:.2f}) via {result.method_used}")
        return result

    async def _detect_with_enhanced_ai(self, query: str, correlation_id: str) -> Optional[IntentResult]:
        """
        Use enhanced AI intent detector with comprehensive analysis and retry logic.
        """
        try:
            # Check if enhanced detector is available
            if not hasattr(self, 'enhanced_detector') or not self.enhanced_detector:
                logger.debug("Enhanced detector not available, skipping")
                return None

            for attempt in range(self.max_retries):
                try:
                    # Use the enhanced intent detector
                    analysis = await self.enhanced_detector.analyze_intent(query)

                    # Convert to IntentResult format
                    primary_intent_value = analysis.primary_intent.value if hasattr(analysis.primary_intent, 'value') else str(analysis.primary_intent)
                    intent = "data_needed" if primary_intent_value != "general_question" else "casual"

                    result = IntentResult(
                        intent=intent,
                        confidence=analysis.confidence,
                        reasoning=analysis.reasoning,
                        entities=analysis.entities,
                        method_used="enhanced_ai",
                        primary_intent=primary_intent_value,
                        secondary_intents=[secondary_intent.value if hasattr(secondary_intent, 'value') else str(secondary_intent) for secondary_intent in analysis.secondary_intents],
                        urgency_level=analysis.urgency_level.value if hasattr(analysis.urgency_level, 'value') else str(analysis.urgency_level),
                        response_style=analysis.response_style.value if hasattr(analysis.response_style, 'value') else str(analysis.response_style),
                        context_clues=analysis.context_clues,
                        processing_time=analysis.processing_time
                    )

                    logger.info(f"Enhanced AI intent analysis successful on attempt {attempt + 1}")
                    return result

                except Exception as e:
                    logger.warning(f"Enhanced AI intent analysis failed (attempt {attempt + 1}): {e}")
                    if attempt < self.max_retries - 1:
                        await asyncio.sleep(self.retry_delay * (attempt + 1))

            logger.warning(f"Enhanced AI intent analysis failed after {self.max_retries} attempts")
            return None

        except Exception as e:
            logger.error(f"Enhanced AI intent detector error: {e}")
            return None

    async def _detect_with_ai(self, query: str, correlation_id: str) -> Optional[IntentResult]:
        """AI detection using unified processor and core prompts."""
        prompt = self.prompt_manager.get_prompt("intent_detection")  # From core
        if not prompt:
            prompt = self._create_default_prompt(query)

        try:
            # Use unified AI
            response = await self.ai_processor.generate_text(
                context={"query": query},
                prompt=prompt,
                model="fast-classifier"  # Efficient model
            )

            if not response:
                return None

            # Parse response
            data = json.loads(response.strip()) if isinstance(response, str) else response
            intent = data.get('intent', 'data_needed')  # Default to data_needed
            confidence = float(data.get('confidence', 0.5))
            reasoning = data.get('reasoning', 'AI classification')
            entities = data.get('entities', {})

            # Validate
            if intent not in ['casual', 'data_needed']:
                intent = 'data_needed'
                confidence = 0.5
                reasoning = "Invalid AI response, defaulting to data_needed"

            return IntentResult(
                intent=intent,
                confidence=min(1.0, max(0.0, confidence)),
                reasoning=reasoning,
                entities=entities
            )

        except json.JSONDecodeError:
            logger.warning(f"JSON parse error in AI response for {correlation_id}")
            return None
        except Exception as e:
            logger.error(f"AI detection error for {correlation_id}: {e}")
            return None

    def _create_default_prompt(self, query: str) -> str:
        """Default prompt if core not available."""
        return f"""Classify this query as "casual" (general chat) or "data_needed" (requires market data):

Query: "{query}"

Respond with JSON: {{"intent": "casual" or "data_needed", "confidence": 0.0-1.0, "reasoning": "brief", "entities": {{"symbols": [], "timeframes": []}}}}"""

    async def _detect_with_keywords(self, query: str, correlation_id: str) -> IntentResult:
        """Enhanced keyword fallback with entity extraction."""
        query_lower = query.lower()

        # Score keywords
        data_score = sum(1 for kw in self.data_keywords if kw in query_lower)
        casual_score = sum(1 for kw in self.casual_keywords if kw in query_lower)

        # Determine intent
        if data_score > casual_score * 1.5:  # Bias towards data
            intent = "data_needed"
            confidence = min(0.9, 0.4 + (data_score * 0.1))
            reasoning = f"Data keywords: {data_score}"
        elif casual_score > 0:
            intent = "casual"
            confidence = min(0.8, 0.4 + (casual_score * 0.1))
            reasoning = f"Casual keywords: {casual_score}"
        else:
            intent = "data_needed"  # Safe default
            confidence = 0.6
            reasoning = "Default to data_needed"

        # Simple entity extraction
        entities = self._extract_entities(query_lower)

        return IntentResult(
            intent=intent,
            confidence=confidence,
            reasoning=reasoning,
            entities=entities
        )

    def _extract_entities(self, query_lower: str) -> dict:
        """Extract basic entities."""
        import re
        symbols = re.findall(r'\b[A-Z]{2,5}\b', query_lower)  # Stock symbols
        timeframes = re.findall(r'\b(1m|5m|15m|1h|4h|1d|1w|1M)\b', query_lower)
        indicators = re.findall(r'\b(rsi|macd|sma|ema|bb|stoch)\b', query_lower)
        return {
            "symbols": symbols,
            "timeframes": timeframes,
            "indicators": indicators
        }

    def _get_cache_key(self, query: str) -> str:
        """Cache key."""
        import hashlib
        return hashlib.md5(query.encode()).hexdigest()

    def get_supported_intents(self) -> list:
        """Supported intents."""
        return ["casual", "data_needed"]

    def get_cache_stats(self) -> dict:
        """Cache stats."""
        return {
            'cache_size': self.intent_cache.size if hasattr(self.intent_cache, 'size') else len(self.intent_cache),
            'cache_ttl': self.cache_ttl,
            'ai_available': self.ai_processor.is_available(),
            'enhanced_ai_available': True,  # Enhanced detector is always available
            'max_retries': self.max_retries,
            'retry_delay': self.retry_delay
        }

    async def analyze_intent_comprehensive(self, query: str, correlation_id: str = "") -> Optional[IntentAnalysis]:
        """
        Get comprehensive intent analysis using enhanced detector.
        Returns the full IntentAnalysis object from enhanced detector.
        """
        try:
            return await self.enhanced_detector.analyze_intent(query)
        except Exception as e:
            logger.error(f"Comprehensive intent analysis failed for {correlation_id}: {e}")
            return None

    async def detect_intent_with_confidence(self, query: str, min_confidence: float = 0.7, correlation_id: str = "") -> Tuple[str, float]:
        """
        Detect intent with confidence threshold.
        Returns (intent, confidence) tuple.
        """
        result = await self.detect(query, correlation_id)
        if result.confidence >= min_confidence:
            return result.intent, result.confidence
        return "unknown", result.confidence

    def get_supported_enhanced_intents(self) -> List[str]:
        """Get list of supported enhanced intent types."""
        try:
            from src.services.ai.enhanced_intent_detector import IntentType
            return [intent.value for intent in IntentType]
        except ImportError:
            return []

    # Compatibility helpers expected by some tests
    def _classify_by_keywords(self, query: str) -> str:
        q = (query or "").lower()
        data_score = sum(1 for kw in self.data_keywords if kw in q)
        casual_score = sum(1 for kw in self.casual_keywords if kw in q)
        if data_score > casual_score * 1.5:
            return "data_needed"
        if casual_score > 0:
            return "casual"
        return "data_needed"

    def _extract_symbols(self, query: str) -> List[str]:
        import re
        return re.findall(r"\b[A-Z]{1,5}\b", query or "")

    def _extract_timeframes(self, query: str) -> List[str]:
        import re
        return re.findall(r"\b(1m|5m|15m|1h|4h|daily|weekly|1d|1w)\b", (query or "").lower())

