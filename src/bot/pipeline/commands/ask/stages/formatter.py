"""
Discord Formatter for ASK Pipeline - Enhanced Refactored Version

Formats AI responses for Discord with embeds, character limits, and enhanced styling.
Refactored for modularity: integrates core error handling, improved field extraction
with regex/symbol detection, dynamic color schemes based on intent, and merged
footer metrics from response_result. Handles Discord limits robustly with truncation
and fallbacks. No major merges needed as distinct from response generation.
"""
import time
import re
from typing import Dict, Any, Optional, List
from dataclasses import dataclass

from src.shared.error_handling.logging import get_logger
from src.shared.error_handling.fallback import with_fallback
from src.bot.core.error_handler import log_and_notify_error
from .response_generator import ResponseResult

logger = get_logger(__name__)


@dataclass
class FormattedResponse:
    """Enhanced Formatted response for Discord with additional metadata."""
    text: Optional[str] = None
    embed: Optional[Dict[str, Any]] = None
    execution_time: float = 0.0
    format_type: str = "text"  # 'text' or 'embed'
    truncated: bool = False


class DiscordFormatter:
    """
    Enhanced Discord Response Formatter for ASK Pipeline

    Refactored to use core error handling, async formatting, and improved content analysis
    for smarter embeds (e.g., symbol extraction, intent-based colors). Handles Discord
    limits (2000 text, 4096 desc, 1024 fields, 25 fields max) with intelligent truncation.
    Features:
    - Dynamic embed creation with footer metrics from response_result
    - Regex-based field/symbol extraction
    - Color schemes tied to trading intents
    - Fallback to plain text on errors
    - Logging integration for debugging
    """

    def __init__(self):
        self.max_text_length = 2000
        self.max_embed_description = 4096
        self.max_embed_field_value = 1024
        self.max_fields = 25
        
        # Enhanced Discord color scheme with trading intents
        self.colors = {
            'success': 0x00ff00,      # Green
            'info': 0x0099ff,         # Blue
            'warning': 0xffaa00,      # Orange
            'error': 0xff0000,        # Red
            'trading': 0x1f8b4c,      # Dark green for market data
            'risk': 0xff6b6b,         # Reddish for risks
            'technical': 0x4ecdc4,    # Teal for TA
            'casual': 0x7289da         # Discord blurple
        }

    async def format(self, response_result: ResponseResult, correlation_id: str = "") -> FormattedResponse:
        """
        Async format response for Discord with core fallback handling.
        """
        start_time = time.time()

        try:
            logger.info(f"Formatting response for Discord", extra={
                'correlation_id': correlation_id,
                'response_length': len(response_result.response),
                'has_disclaimer': response_result.disclaimer_added,
                'confidence': response_result.confidence
            })

            # Determine format type
            use_embed = self._should_use_embed(response_result.response)
            logger.debug(f"Selected format: {'embed' if use_embed else 'text'}", extra={'correlation_id': correlation_id})

            if use_embed:
                embed = await self._create_embed(response_result, correlation_id)
                formatted = FormattedResponse(
                    embed=embed,
                    execution_time=time.time() - start_time,
                    format_type='embed',
                    truncated=embed.get('truncated', False)
                )
            else:
                text = await self._format_text(response_result.response, correlation_id)
                formatted = FormattedResponse(
                    text=text,
                    execution_time=time.time() - start_time,
                    format_type='text',
                    truncated=len(response_result.response) > self.max_text_length
                )

            logger.info(f"Formatting completed successfully", extra={
                'correlation_id': correlation_id,
                'format_type': formatted.format_type,
                'execution_time': formatted.execution_time,
                'truncated': formatted.truncated
            })

            return formatted

        except Exception as e:
            logger.error(f"Discord formatting failed: {e}", extra={'correlation_id': correlation_id})
            # Fallback to plain text
            fallback_text = response_result.response[:self.max_text_length] + "\n\n*[Formatting error - response truncated]*"
            return FormattedResponse(
                text=fallback_text,
                execution_time=time.time() - start_time,
                format_type='text',
                truncated=True
            )

    def _should_use_embed(self, response: str) -> bool:
        """Enhanced decision for embed usage based on content structure."""
        if len(response) > 500:
            return True
        
        if response.count('\n\n') >= 2:
            return True
        
        # Check for markdown elements or lists
        if any(marker in response for marker in ['**', '•', '#', '##', '📊', '📈']):
            return True
        
        # Symbol presence suggests structured data
        symbols = re.findall(r'\b[A-Z]{1,5}\b', response)
        if symbols and any(word in response.lower() for word in ['price', 'analysis', 'rsi', 'macd']):
            return True
        
        return False

    async def _create_embed(self, response_result: ResponseResult, correlation_id: str) -> Dict[str, Any]:
        """Create enhanced Discord embed with symbol extraction and intent colors."""
        response = response_result.response
        
        # Determine color based on content and confidence
        color = self._get_embed_color(response, response_result.confidence)
        
        # Split for title/description
        title, description = self._split_response_for_embed(response)
        
        # Base embed
        embed = {
            'title': title,
            'description': description,
            'color': color,
            'timestamp': time.strftime('%Y-%m-%dT%H:%M:%S.000Z'),
            'footer': {
                'text': self._create_footer_text(response_result)
            },
            'author': {
                'name': 'Trading Assistant AI',
                'icon_url': 'https://cdn.discordapp.com/emojis/📈.png'  # Enhanced placeholder
            }
        }
        
        # Extract fields with improved regex for sections and symbols
        fields = self._extract_fields_from_response(response)
        if fields:
            embed['fields'] = fields[:self.max_fields]
        
        # Handle disclaimer separately
        if response_result.disclaimer_added:
            disclaimer = self._extract_disclaimer(response)
            if disclaimer:
                embed['fields'] = embed.get('fields', [])
                embed['fields'].append({
                    'name': '⚠️ Important Disclaimer',
                    'value': disclaimer,
                    'inline': False
                })
                # Remove from description to avoid duplication
                if '⚠️' in description:
                    embed['description'] = description.split('⚠️')[0].strip()
        
        # Truncate to limits
        embed = self._truncate_embed(embed)
        embed['truncated'] = len(response) > self.max_embed_description or bool(fields)
        
        logger.debug(f"Embed created with {len(embed.get('fields', []))} fields", extra={'correlation_id': correlation_id})
        
        return embed

    async def _format_text(self, response: str, correlation_id: str) -> str:
        """Format plain text with truncation and markdown preservation."""
        if len(response) > self.max_text_length:
            # Intelligent truncation at sentence boundary
            truncated = response[:self.max_text_length - 100]
            last_period = truncated.rfind('. ')
            if last_period > self.max_text_length - 300:
                truncated = truncated[:last_period + 1]
            
            response = truncated + "\n\n**Note:** Response truncated due to Discord limits. Use /ask for full details."
            logger.warning(f"Text truncated for {correlation_id}", extra={'original_length': len(response), 'truncated_length': len(response)})
        
        return response

    def _get_embed_color(self, response: str, confidence: float) -> int:
        """Enhanced color selection based on content, intent, and confidence."""
        response_lower = response.lower()
        
        if confidence < 0.6:
            return self.colors['error']
        elif any(word in response_lower for word in ['error', 'failed', 'unavailable']):
            return self.colors['error']
        elif any(word in response_lower for word in ['warning', 'risk', 'volatility', 'caution']):
            return self.colors['risk']
        elif any(word in response_lower for word in ['technical', 'chart', 'indicator', 'rsi', 'macd']):
            return self.colors['technical']
        elif any(word in response_lower for word in ['trading', 'stock', 'market', 'price', 'buy', 'sell']):
            return self.colors['trading']
        elif any(word in response_lower for word in ['hello', 'help', 'welcome', 'casual']):
            return self.colors['casual']
        else:
            return self.colors['info']

    def _split_response_for_embed(self, response: str) -> tuple[str, str]:
        """Improved split with symbol-aware title generation."""
        lines = response.split('\n')
        
        if lines and len(lines[0]) < 100 and not lines[0].startswith('•'):
            title = lines[0].strip()
            description = '\n'.join(lines[1:]).strip()
        else:
            title = self._generate_smart_title(response)
            description = response
        
        # Truncate title to 256 chars
        if len(title) > 256:
            title = title[:253] + "..."
        
        return title, description

    def _generate_smart_title(self, response: str) -> str:
        """Enhanced title generation with regex symbol extraction."""
        response_lower = response.lower()
        symbols = re.findall(r'\b[A-Z]{1,5}\b', response)
        
        if symbols:
            symbol = symbols[0]
            if 'price' in response_lower:
                return f"📈 {symbol} Price & Analysis"
            elif any(word in response_lower for word in ['technical', 'chart', 'rsi', 'macd']):
                return f"📊 {symbol} Technical Breakdown"
            elif 'risk' in response_lower:
                return f"⚠️ {symbol} Risk Evaluation"
            elif 'news' in response_lower:
                return f"📰 {symbol} News Impact"
            else:
                return f"💹 {symbol} Market Insights"
        
        # Fallback content-based
        if any(word in response_lower for word in ['option', 'call', 'put']):
            return "🎯 Options Strategy Analysis"
        elif 'portfolio' in response_lower:
            return "📋 Portfolio Review"
        elif any(word in response_lower for word in ['risk', 'volatility']):
            return "⚠️ Risk & Volatility Assessment"
        elif 'market' in response_lower and any(word in response_lower for word in ['outlook', 'trend']):
            return "🔮 Market Trends & Outlook"
        elif any(word in response_lower for word in ['learn', 'explain', 'tutorial']):
            return "🎓 Trading Education"
        elif any(word in response_lower for word in ['hello', 'hi', 'help']):
            return "👋 Trading Assistant Response"
        elif 'data' in response_lower and 'unavailable' in response_lower:
            return "⚠️ Data Access Issue"
        else:
            return "💡 Trading & Market Analysis"

    def _extract_fields_from_response(self, response: str) -> List[Dict[str, Any]]:
        """Enhanced field extraction with regex for sections and bullet points."""
        fields = []
        
        # Extract **Header**: content sections
        sections = re.findall(r'\*\*(.*?)\*\*:\s*(.*?)(?=\*\*|$)', response, re.DOTALL | re.IGNORECASE)
        for name, value in sections:
            value = value.strip()
            if len(value) > 0:
                fields.append({
                    'name': name.strip(),
                    'value': value[:self.max_embed_field_value],
                    'inline': len(value) < 200
                })
        
        # Extract bullet points as fields if no sections
        if not fields and '•' in response:
            bullets = re.findall(r'•\s*(.*?)(?=\n•|\n\n|$)', response, re.DOTALL)
            for i, bullet in enumerate(bullets[:self.max_fields]):
                fields.append({
                    'name': f"Key Point {i+1}",
                    'value': bullet.strip()[:self.max_embed_field_value],
                    'inline': True
                })
        
        # Symbol-specific fields if detected
        symbols = re.findall(r'\b[A-Z]{1,5}\b', response)
        if symbols and len(fields) < self.max_fields:
            for symbol in set(symbols[:3]):  # Limit to 3 unique
                # Simple extraction, could enhance with tool results if passed
                fields.append({
                    'name': f"{symbol} Quick Stats",
                    'value': "Symbol detected in analysis. See description for details.",
                    'inline': True
                })
        
        return fields

    def _extract_disclaimer(self, response: str) -> str:
        """Extract and clean disclaimer."""
        if '⚠️ **Disclaimer:**' in response:
            disclaimer_match = re.search(r'⚠️ \*\*Disclaimer:\*\* (.*)', response, re.DOTALL)
            if disclaimer_match:
                disclaimer = disclaimer_match.group(1).strip()
                return disclaimer[:self.max_embed_field_value]
        return ''

    def _truncate_embed(self, embed: Dict[str, Any]) -> Dict[str, Any]:
        """Robust truncation for all embed parts."""
        if 'description' in embed and len(embed['description']) > self.max_embed_description:
            embed['description'] = embed['description'][:self.max_embed_description - 50] + "\n\n*[Truncated for Discord limits]*"
        
        if 'fields' in embed:
            for field in embed['fields']:
                if len(field['value']) > self.max_embed_field_value:
                    field['value'] = field['value'][:self.max_embed_field_value - 20] + "\n*[Field truncated]*"
        
        # Truncate title if needed (though checked earlier)
        if 'title' in embed and len(embed['title']) > 256:
            embed['title'] = embed['title'][:253] + "..."
        
        return embed

    def _create_footer_text(self, response_result: ResponseResult) -> str:
        """Enhanced footer with confidence, model, fallback, and cache indicators."""
        footer_parts = []
        
        # Execution time
        footer_parts.append(f"⚡ {response_result.execution_time:.2f}s")
        
        # Confidence with emoji
        if response_result.confidence:
            if response_result.confidence > 0.8:
                conf_emoji = "🟢"
            elif response_result.confidence > 0.6:
                conf_emoji = "🟡"
            else:
                conf_emoji = "🔴"
            footer_parts.append(f"{conf_emoji} {response_result.confidence:.0%}")
        
        # Model/Fallback
        if response_result.fallback_used:
            footer_parts.append("🔄 Fallback Mode")
        else:
            footer_parts.append(f"🤖 {response_result.model_used}")
        
        # Cache
        if response_result.cache_used:
            footer_parts.append("💾 Cached")
        
        # Disclaimer indicator
        if response_result.disclaimer_added:
            footer_parts.append("⚠️ Disclaimer Applied")
        
        return " • ".join(footer_parts)


async def setup(bot):
    """Setup for formatter - no-op as stateless."""
    logger.info("Enhanced Discord Formatter ready with core integration")