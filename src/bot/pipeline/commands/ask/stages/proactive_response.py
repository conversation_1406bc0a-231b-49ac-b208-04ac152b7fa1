"""
Minimal compatibility shim for proactive response generation used in tests.
Provides ProactiveResponseGenerator with generate_proactive_response() returning
an object that has immediate_value_provided=True.
"""
from dataclasses import dataclass
from typing import Any, Optional

@dataclass
class ProactiveResult:
    immediate_value_provided: bool = True
    content: Optional[str] = None

class ProactiveResponseGenerator:
    async def generate_proactive_response(self, query: str, intent_result: Any, tool_result: Any) -> ProactiveResult:
        # Provide immediate value flag for tests
        return ProactiveResult(immediate_value_provided=True, content=None)

