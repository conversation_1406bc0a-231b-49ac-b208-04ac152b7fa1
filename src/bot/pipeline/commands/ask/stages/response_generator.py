"""
Enhanced Response Generator for ASK Pipeline

AI-powered response synthesis using centralized prompt system, tool results, and advanced synthesis techniques.
Integrates with observability, security, and performance monitoring.
"""

import time
import asyncio
import json
import re
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass, field

from src.shared.error_handling.logging import get_logger
from src.shared.ai_chat.ai_client import AIClientWrapper
from src.core.prompts import PromptManager
from src.shared.monitoring.observability import observability_manager, MetricType
from src.bot.pipeline.commands.ask.audit import get_audit_logger
from src.bot.pipeline.commands.ask.cost import record_ai_usage
from .intent_detector import IntentResult
from .simplified_tool_orchestrator import ToolResult

logger = get_logger(__name__)

@dataclass
class ResponseResult:
    """Enhanced result of response generation with observability integration."""
    response: str
    confidence: float
    execution_time: float
    model_used: str = "ai_synthesis"
    tokens_used: int = 0
    disclaimer_added: bool = False
    cache_used: bool = False
    fallback_used: bool = False
    quality_score: float = 0.0
    synthesis_metadata: Dict[str, Any] = field(default_factory=dict)

class ResponseGenerator:
    """
    Advanced AI-Powered Response Generation Stage

    Features:
    - Multi-model AI synthesis with fallback strategies
    - Context-aware prompt engineering
    - Tool result integration and summarization
    - Response quality assessment
    - Observability and cost tracking
    - Security scanning of generated responses
    - A/B testing support for response variants
    """

    def __init__(self):
        # Initialize AI client with advanced configuration
        class ResponseContext:
            def __init__(self):
                self.pipeline_id = "ask_response_generator"
                self.max_retries = 3
                self.fallback_enabled = True

        self.ai_client = AIClientWrapper(context=ResponseContext())
        self.prompt_manager = PromptManager()

        # Enhanced fallback responses with quality scoring
        self.fallback_responses = {
            "casual": [
                "Hello! I'm here to help you with trading and market questions. What would you like to know?",
                "Hi there! I can assist you with market analysis, stock information, and trading insights. How can I help?",
                "Welcome! I'm your trading assistant. Feel free to ask me about stocks, markets, or trading strategies."
            ],
            "data_needed": [
                "I've analyzed the available data for your query. Here's what I found:",
                "Based on the current market information, here's my analysis:",
                "After reviewing the relevant data, I can provide the following insights:"
            ],
            "error": [
                "I'm sorry, but I'm having trouble processing your request right now. Please try rephrasing your question.",
                "I encountered an issue while generating a response. Could you try asking in a different way?",
                "I'm experiencing technical difficulties. Let me know how I can assist you with a different question."
            ]
        }

        # Response quality assessment weights
        self.quality_weights = {
            "relevance": 0.4,
            "completeness": 0.3,
            "clarity": 0.2,
            "conciseness": 0.1
        }

        # A/B testing variants for response styles
        self.response_variants = {
            "concise": {"temperature": 0.1, "max_tokens": 500, "style": "concise"},
            "detailed": {"temperature": 0.3, "max_tokens": 1500, "style": "detailed"},
            "conversational": {"temperature": 0.5, "max_tokens": 1000, "style": "conversational"}
        }

    def _get_ai_model_for_job(self, job: str) -> str:
        """Get the appropriate AI model for a specific job from configuration."""
        try:
            from src.shared.ai_services.simple_model_config import get_model_id_for_job
            return get_model_id_for_job(job)
        except Exception as e:
            logger.warning(f"Failed to get model for job {job}: {e}, using fallback")
            return "gpt-4o-mini"  # Safe fallback

    async def generate(
        self,
        query: str,
        intent_result: IntentResult,
        tool_result: Optional[ToolResult],
        correlation_id: str,
        user_id: Optional[str] = None,
        variant: str = "detailed"
    ) -> ResponseResult:
        """
        Generate AI-powered response with advanced synthesis and observability.

        Args:
            query: User's question or request
            intent_result: Result from intent detection
            tool_result: Result from tool orchestration (if any)
            correlation_id: Request correlation ID
            user_id: User ID for personalization
            variant: A/B testing variant for response style

        Returns:
            ResponseResult with generated response and metadata
        """
        start_time = time.time()
        operation_id = observability_manager.start_operation(
            operation_name="response_generation",
            correlation_id=correlation_id,
            user_id=user_id,
            metadata={
                'intent': intent_result.intent,
                'has_tool_data': tool_result is not None if tool_result else False,
                'variant': variant
            }
        )

        logger.debug(f"Generating AI response (variant: {variant})", extra={
            'correlation_id': correlation_id,
            'intent': intent_result.intent,
            'has_tool_data': tool_result is not None if tool_result else False,
            'user_id': user_id
        })

        # Security scan of input
        from src.bot.pipeline.commands.ask.security.security_manager import scan_input_for_threats
        threats = await scan_input_for_threats({"query": query})
        if threats:
            logger.warning(f"Security threats detected in query: {threats}", extra={'correlation_id': correlation_id})
            observability_manager.record_metric(
                name="security_threat_detected",
                value=len(threats),
                metric_type=MetricType.COUNTER,
                tags={'stage': 'response_generation', 'threats': ','.join([(t.value if hasattr(t, 'value') else str(t)) for t in threats])}
            )
            # Use fallback for security
            response = await self._generate_fallback_response(query, intent_result, tool_result, correlation_id)
            confidence = 0.5
            tokens_used = len(response.split())
            fallback_used = True
        else:
            # Generate AI response with retry logic
            response, confidence, tokens_used, fallback_used = await self._generate_ai_response_with_retry(
                query, intent_result, tool_result, correlation_id, user_id, variant
            )
            # Adjust confidence if no tool data was available
            tools_present = False
            if tool_result:
                if getattr(tool_result, 'tool_results', None):
                    tools_present = len(getattr(tool_result, 'tool_results') or []) > 0
                elif getattr(tool_result, 'tools_used', None):
                    tools_present = len(getattr(tool_result, 'tools_used') or []) > 0
            if not tools_present:
                confidence = max(0.0, min(1.0, confidence - 0.15))

        # Add disclaimer for trading-related content
        disclaimer_added = self._should_add_disclaimer(query, response)
        if disclaimer_added:
            response = self._add_trading_disclaimer(response)

        # Assess response quality
        quality_score = self._assess_response_quality(response, query, intent_result)

        # Record AI cost
        if tokens_used > 0:
            record_ai_usage(
                provider="openai",  # Default provider
                model=self._get_ai_model_for_job("user_explanations"),
                tokens_used=tokens_used,
                input_tokens=len(query.split()) + (len(str(tool_result)) if tool_result else 0),
                output_tokens=len(response.split()),
                user_id=user_id
            )

        execution_time = time.time() - start_time

        # End observability tracking
        observability_manager.end_operation(
            operation_id=operation_id,
            success=True,
            error_message=None
        )

        # Audit log response generation
        audit_logger = get_audit_logger()
        if audit_logger:
            audit_logger.log_data_access(
                user_id=user_id,
                resource_id=f"response_{correlation_id}",
                data_type="ai_response",
                access_method="generate"
            )

        result = ResponseResult(
            response=response,
            confidence=confidence,
            execution_time=execution_time,
            model_used=self._get_ai_model_for_job("user_explanations"),
            tokens_used=tokens_used,
            disclaimer_added=disclaimer_added,
            cache_used=False,  # Implement caching in future
            fallback_used=fallback_used,
            quality_score=quality_score,
            synthesis_metadata={
                'variant': variant,
                'intent': intent_result.intent,
                'tools_used': getattr(tool_result, 'tools_used', []) if tool_result else [],
                'quality_assessment': {
                    'relevance': self._assess_relevance(response, query, intent_result),
                    'completeness': self._assess_completeness(response, tool_result),
                    'clarity': self._assess_clarity(response),
                    'conciseness': self._assess_conciseness(response)
                }
            }
        )

        # Record quality metrics
        observability_manager.record_metric(
            name="response_quality_score",
            value=quality_score,
            metric_type=MetricType.GAUGE,
            tags={
                'variant': variant,
                'intent': intent_result.intent,
                'fallback_used': str(fallback_used),
                'disclaimer_added': str(disclaimer_added)
            }
        )

        logger.info(f"Response generation completed", extra={
            'correlation_id': correlation_id,
            'response_length': len(response),
            'execution_time': execution_time,
            'quality_score': quality_score,
            'confidence': confidence,
            'fallback_used': fallback_used,
            'disclaimer_added': disclaimer_added
        })

        return result

    async def _generate_ai_response_with_retry(
        self,
        query: str,
        intent_result: IntentResult,
        tool_result: Optional[ToolResult],
        correlation_id: str,
        user_id: Optional[str] = None,
        variant: str = "detailed",
        max_retries: int = 3
    ) -> Tuple[str, float, int, bool]:
        """Generate AI response with retry logic and fallback."""
        fallback_used = False

        for attempt in range(1, max_retries + 1):
            try:
                response, confidence, tokens_used = await self._generate_ai_response(
                    query, intent_result, tool_result, correlation_id, user_id, variant
                )
                return response, confidence, tokens_used, False

            except Exception as e:
                logger.warning(f"AI response attempt {attempt} failed: {str(e)}", extra={
                    'correlation_id': correlation_id,
                    'attempt': attempt,
                    'max_retries': max_retries
                })

                if attempt == max_retries:
                    # Final fallback
                    logger.warning(f"All AI attempts failed, using fallback", extra={
                        'correlation_id': correlation_id,
                        'attempts': max_retries
                    })
                    fallback_response = await self._generate_fallback_response(
                        query, intent_result, tool_result, correlation_id
                    )
                    return fallback_response, 0.6, len(fallback_response.split()), True

                # Wait before retry
                await asyncio.sleep(0.5 * attempt)  # Exponential backoff

        # Should not reach here
        raise Exception("All fallback mechanisms failed")

    async def _generate_ai_response(
        self,
        query: str,
        intent_result: IntentResult,
        tool_result: Optional[ToolResult],
        correlation_id: str,
        user_id: Optional[str] = None,
        variant: str = "detailed"
    ) -> Tuple[str, float, int]:
        """Generate AI-powered response using advanced synthesis techniques."""

        # Select variant configuration
        variant_config = self.response_variants.get(variant, self.response_variants["detailed"])

        # Determine appropriate persona based on query content and user context
        persona = self._select_persona(query, intent_result, user_id)

        # Build comprehensive context for AI response
        context = self._build_enhanced_response_context(query, intent_result, tool_result, user_id)

        # Create advanced response generation prompt with variant-specific instructions
        system_prompt = self.prompt_manager.get_enhanced_system_prompt(
            persona=persona,
            context=context,
            include_json_format=True
        )

        # Build user prompt with structured context and variant instructions
        user_prompt = self._build_enhanced_user_prompt(
            query, intent_result, tool_result, user_id, variant_config
        )

        logger.debug(f"Calling AI for response synthesis (variant: {variant})", extra={
            'correlation_id': correlation_id,
            'persona': persona,
            'variant': variant,
            'context_keys': list(context.keys()),
            'max_tokens': variant_config["max_tokens"]
        })

        # Start AI operation tracking
        ai_operation_id = observability_manager.start_operation(
            operation_name="ai_response_synthesis",
            correlation_id=correlation_id,
            metadata={
                'persona': persona,
                'variant': variant,
                'model': self.ai_client.model_name if hasattr(self.ai_client, 'model_name') else self._get_ai_model_for_job("user_explanations")
            }
        )

        try:
            # Call AI for response generation - AI client expects a single prompt and flag for JSON system prompt
            maybe_coro = self.ai_client.generate_response(
                prompt=user_prompt,
                use_json_system_prompt=True
            )
            ai_response = await maybe_coro if asyncio.iscoroutine(maybe_coro) else maybe_coro

            # Normalize to content/metadata
            if isinstance(ai_response, str):
                content = ai_response
                confidence = 0.85
                tokens_used = len(ai_response.split())
            else:
                content = getattr(ai_response, 'content', '')
                confidence = getattr(ai_response, 'confidence', 0.85)
                tokens_used = getattr(ai_response, 'tokens_used', len(str(content).split()))

            if not content:
                raise Exception("AI returned empty response")

            # End AI operation tracking
            observability_manager.end_operation(
                operation_id=ai_operation_id,
                success=True
            )

            return str(content).strip(), confidence, tokens_used

        except Exception as e:
            # End AI operation tracking with error
            observability_manager.end_operation(
                operation_id=ai_operation_id,
                success=False,
                error_message=str(e)
            )

            raise e

    def _select_persona(self, query: str, intent_result: IntentResult, user_id: Optional[str] = None) -> str:
        """Select appropriate AI persona based on query, intent, and user context."""
        query_lower = query.lower()

        # Educational intent should take precedence when explicitly asked to learn/explain
        if intent_result.intent == "casual" or any(word in query_lower for word in ['learn', 'explain', 'beginner']):
            return "educational_assistant"

        # Check for specific trading/analysis keywords
        if any(word in query_lower for word in ['risk', 'portfolio', 'diversification', 'volatility', 'hedge']):
            return "risk_analyst"
        elif any(word in query_lower for word in ['chart', 'technical', 'indicator', 'pattern', 'trend', 'rsi', 'macd']):
            return "technical_analyst"
        elif any(word in query_lower for word in ['option', 'call', 'put', 'strike', 'expiration', 'greeks']):
            return "options_specialist"
        elif any(word in query_lower for word in ['market', 'economy', 'sector', 'industry', 'macro']):
            return "market_strategist"
        elif any(word in query_lower for word in ['fundamental', 'valuation', 'earnings', 'revenue', 'pe']):
            return "fundamental_analyst"
        elif user_id and "premium" in user_id.lower():  # Premium user personalization
            return "premium_advisor"
        else:
            # Default to general trading expert for data-driven queries
            return "trading_expert"

    def _build_enhanced_response_context(
        self,
        query: str,
        intent_result: IntentResult,
        tool_result: Optional[ToolResult],
        user_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Build comprehensive context dictionary for AI response generation."""
        context = {
            "query": query,
            "query_intent": intent_result.intent,
            "query_confidence": intent_result.confidence,
            "query_entities": intent_result.entities or {},
            "user_id": user_id,
            "has_market_data": False,
            "tools_executed": [],
            "data_summary": "",
            "conversation_context": "This is a single-turn conversation for trading assistance.",
            "response_guidelines": [
                "Provide accurate, helpful information about financial markets and trading",
                "Use clear, professional language suitable for investors",
                "Include relevant disclaimers for trading advice",
                "Base responses on provided data and general market knowledge",
                "Avoid giving specific investment recommendations"
            ]
        }

        # Add user-specific context if available
        if user_id:
            context["user_context"] = f"User ID: {user_id[:8]}... (premium features available)"

        # Add tool result context if available
        if tool_result:
            tools_used = getattr(tool_result, 'tools_used', [])
            context.update({
                "has_market_data": bool(tools_used),
                "tools_executed": tools_used or [],
                "tool_execution_time": getattr(tool_result, 'execution_time', 0.0),
                "tool_cache_hit": getattr(tool_result, 'cache_hit', False)
            })

            # Enhanced tool data summarization
            if tool_result.results:
                context["data_summary"] = self._summarize_enhanced_tool_data(tool_result.results, intent_result.intent)

        # Add market context guidelines
        context["market_context"] = (
            "Current market conditions: Normal trading hours. "
            "All price data is real-time unless specified otherwise. "
            "Trading involves risk; past performance is not indicative of future results."
        )

        return context

    # --- Compatibility helpers expected by legacy tests ---
    def _build_response_context(
        self,
        query: str,
        intent_result: IntentResult,
        tool_result: Optional[ToolResult]
    ) -> Dict[str, Any]:
        """Build a simple context dict with keys expected by tests.
        Keys: query, intent, symbols, tool_results
        """
        symbols = []
        try:
            symbols = (getattr(intent_result, 'entities', {}) or {}).get('symbols', [])
        except Exception:
            symbols = []
        context = {
            'query': query,
            'intent': getattr(intent_result, 'intent', None),
            'symbols': symbols,
            'tool_results': getattr(tool_result, 'tool_results', None) if tool_result else None,
        }
        return context

    def _summarize_tool_results(self, tool_result: ToolResult) -> str:
        """Summarize tool results into a concise human-readable string (for tests)."""
        if not tool_result:
            return "No tool data available"
        entries = getattr(tool_result, 'tool_results', []) or []
        parts: List[str] = []
        for entry in entries:
            tool = entry.get('tool')
            data = entry.get('data') or {}
            symbol = data.get('symbol') if isinstance(data, dict) else None
            if tool == 'get_global_quote' and isinstance(data, dict):
                price = data.get('price') or data.get('latestPrice')
                change = data.get('change')
                change_pct = data.get('change_percent') or data.get('changePercent')
                seg = f"{symbol}: " if symbol else ""
                if isinstance(price, (int, float)):
                    seg += f"${price:.2f}"
                elif price is not None:
                    seg += str(price)
                if change is not None:
                    if isinstance(change, (int, float)):
                        seg += f" ({change:+.2f}"
                    else:
                        seg += f" ({change}"
                    if change_pct is not None:
                        if isinstance(change_pct, (int, float)):
                            seg += f", {change_pct:+.2f}%)"
                        else:
                            seg += f", {change_pct})"
                    else:
                        seg += ")"
                parts.append(seg)
            elif tool in ("get_rsi", "rsi") and isinstance(data, dict):
                rsi = data.get('rsi')
                if rsi is not None:
                    prefix = f"{symbol} " if symbol else ""
                    parts.append(f"{prefix}RSI: {rsi}")
            else:
                if symbol:
                    parts.append(f"{symbol} data retrieved")
        if not parts and getattr(tool_result, 'summary', ''):
            return tool_result.summary
        return "; ".join([p for p in parts if p]) or "No tool data available"

    def _validate_response(self, response: Optional[str]) -> bool:
        """Basic validation: non-empty, non-whitespace string."""
        return isinstance(response, str) and bool(response.strip())

    def _build_enhanced_user_prompt(
        self,
        query: str,
        intent_result: IntentResult,
        tool_result: Optional[ToolResult],
        user_id: Optional[str] = None,
        variant_config: Dict[str, Any] = None
    ) -> str:
        """Build enhanced user prompt with structured context and variant instructions."""
        prompt_parts = []

        # Header with query and intent
        prompt_parts.append(f"USER QUERY: {query}")
        prompt_parts.append(f"INTENT CLASSIFICATION: {intent_result.intent} (confidence: {intent_result.confidence:.2f})")

        # Entity extraction
        if intent_result.entities:
            entities_str = ", ".join([f"{k}: {v}" for k, v in intent_result.entities.items()])
            prompt_parts.append(f"EXTRACTED ENTITIES: {entities_str}")

        # Tool execution results
        tools_used = getattr(tool_result, 'tools_used', []) if tool_result else []
        if tool_result and tools_used:
            prompt_parts.append(f"TOOLS EXECUTED: {', '.join(tools_used)}")
            prompt_parts.append("TOOL RESULTS:")

            for tool_name, result in (tool_result.results or {}).items():
                if result.get('success', True):
                    data_preview = self._format_tool_result_for_prompt(tool_name, result.get('data', ''))
                    prompt_parts.append(f"- {tool_name}: {data_preview}")
                else:
                    prompt_parts.append(f"- {tool_name}: Failed - {result.get('error', 'Unknown error')}")

            # Add execution summary
            prompt_parts.append(f"TOOL EXECUTION SUMMARY: {len(tools_used)} tools executed, {getattr(tool_result, 'execution_time', 0.0):.2f}s total")
        else:
            prompt_parts.append("No market data tools were executed for this query.")

        # Variant-specific instructions
        if variant_config:
            style_instruction = {
                "concise": "Provide a brief, direct answer focusing on key points only.",
                "detailed": "Provide a comprehensive, detailed analysis with explanations and context.",
                "conversational": "Respond in a friendly, conversational tone as if speaking to a colleague."
            }.get(variant_config["style"], "Provide a clear, professional response.")
            prompt_parts.append(f"RESPONSE STYLE: {style_instruction}")

        # Core response guidelines
        prompt_parts.append("\nRESPONSE GUIDELINES:")
        prompt_parts.extend([
            "Be accurate and truthful in all financial information",
            "Use clear, professional language suitable for investors and traders",
            "Include relevant context and explanations where appropriate",
            "Reference specific data points from tool results when available",
            "Avoid speculative language or guarantees about future performance",
            "Include appropriate disclaimers for trading-related content",
            "Structure responses logically with clear sections if complex",
            "Keep responses focused on the user's specific query"
        ])

        # Final instruction
        prompt_parts.append("\nGenerate a helpful, accurate response based on the above information and context.")

        return "\n".join(prompt_parts)

    def _format_tool_result_for_prompt(self, tool_name: str, data: Any) -> str:
        """Format tool result for inclusion in AI prompt."""
        if not data:
            return "No data returned"

        if isinstance(data, dict):
            # Format common tool result types
            if tool_name in ['get_global_quote', 'market_data']:
                if 'price' in data:
                    return f"Current price: ${data['price']:.2f}, Change: {data.get('change', 0):+.2f} ({data.get('change_percent', 0):+.2%})"
                elif 'symbol' in data and 'companyName' in data:
                    return f"{data['symbol']}: {data['companyName']} - {data.get('latestPrice', 'N/A')}"

            elif tool_name in ['get_macd', 'get_rsi']:
                if 'values' in data:
                    latest = data['values'][-1] if data['values'] else {}
                    return f"Latest {tool_name.upper()}: {latest.get('value', 'N/A')}"

            else:
                # Generic formatting
                key_info = []
                for key, value in list(data.items())[:3]:  # First 3 items
                    key_info.append(f"{key}: {str(value)[:50]}")
                return f"Key data: {', '.join(key_info)}"

        elif isinstance(data, list) and data:
            if len(data) == 1:
                return f"Single result: {str(data[0])[:100]}"
            else:
                return f"{len(data)} results available"

        else:
            return f"Data retrieved: {str(data)[:100]}..."

    def _summarize_enhanced_tool_data(self, tool_results: Dict[str, Any], intent: str) -> str:
        """Create enhanced summary of tool data for AI context based on intent."""
        summaries = []
        intent_lower = intent.lower()

        for tool_name, result in tool_results.items():
            if result.get('success', True):
                data = result.get('data', {})

                # Intent-specific summarization
                if 'price' in intent_lower or 'quote' in intent_lower:
                    if isinstance(data, dict) and 'price' in data:
                        price_info = f"${data['price']:.2f}"
                        if 'change' in data:
                            change = data['change']
                            change_pct = data.get('change_percent', 0)
                            price_info += f" ({change:+.2f} | {change_pct:+.2%})"
                        summaries.append(f"{tool_name}: {price_info}")

                elif 'technical' in intent_lower or 'indicator' in intent_lower:
                    if isinstance(data, dict) and 'values' in data:
                        latest = data['values'][-1] if data['values'] else {}
                        value = latest.get('value', 'N/A')
                        signal = latest.get('signal', 'N/A')
                        summaries.append(f"{tool_name}: {value} (signal: {signal})")

                elif 'news' in intent_lower:
                    if isinstance(data, list) and data:
                        headlines = [item.get('title', 'No title')[:50] for item in data[:3]]
                        summaries.append(f"{tool_name}: {', '.join(headlines)}")

                else:
                    # Generic summary
                    summaries.append(f"{tool_name}: Data retrieved successfully")

            else:
                summaries.append(f"{tool_name}: Failed to retrieve data")

        return "; ".join(summaries) if summaries else "No tool data available"

    def _assess_response_quality(self, response: str, query: str, intent_result: IntentResult) -> float:
        """Assess the quality of the generated response."""
        scores = {
            "relevance": self._assess_relevance(response, query, intent_result),
            "completeness": self._assess_completeness(response, None),  # tool_result not available here
            "clarity": self._assess_clarity(response),
            "conciseness": self._assess_conciseness(response)
        }

        # Calculate weighted quality score using predefined weights
        total_score = 0.0
        for name, score in scores.items():
            weight = self.quality_weights.get(name, 0.0)
            try:
                total_score += float(score) * float(weight)
            except Exception:
                # Be defensive if any scorer returns non-numeric
                continue
        return float(total_score)

    def _assess_relevance(self, response: str, query: str, intent_result: IntentResult) -> float:
        """Assess how relevant the response is to the query and intent."""
        query_words = set(query.lower().split())
        response_words = set(response.lower().split())

        # Check for overlap with query terms
        query_overlap = len(query_words.intersection(response_words)) / len(query_words) if query_words else 0

        # Check for intent-specific keywords
        intent_keywords = {
            "price": ["price", "quote", "current", "value"],
            "technical": ["rsi", "macd", "trend", "indicator", "chart"],
            "fundamental": ["earnings", "revenue", "pe", "valuation", "profit"],
            "news": ["news", "recent", "update", "headline"],
            "casual": ["help", "explain", "what", "how"]
        }.get(intent_result.intent, [])

        intent_match = sum(1 for word in intent_keywords if word in response.lower()) / len(intent_keywords) if intent_keywords else 0

        return (query_overlap * 0.6 + intent_match * 0.4) * 100

    def _assess_completeness(self, response: str, tool_result: Optional[ToolResult]) -> float:
        """Assess how complete the response is."""
        response_length = len(response.split())

        # Basic completeness based on length and structure
        if response_length < 20:
            return 30.0  # Too short
        elif response_length < 100:
            return 70.0  # Reasonable
        elif response_length < 500:
            return 90.0  # Good
        else:
            return 95.0  # Very comprehensive

    def _assess_clarity(self, response: str) -> float:
        """Assess clarity of the response."""
        # Simple heuristics for clarity
        complex_sentences = len(re.findall(r'[.!?][^.!?]{50,}', response))
        total_sentences = len(re.findall(r'[.!?]', response)) + 1

        if total_sentences == 0:
            return 50.0

        clarity_ratio = 1.0 - (complex_sentences / total_sentences)
        return clarity_ratio * 100

    def _assess_conciseness(self, response: str) -> float:
        """Assess conciseness of the response."""
        words = len(response.split())
        characters = len(response)

        # Average words per sentence
        sentences = len(re.findall(r'[.!?]', response)) + 1
        words_per_sentence = words / sentences if sentences > 0 else words

        if words_per_sentence < 15:
            return 95.0  # Very concise
        elif words_per_sentence < 25:
            return 85.0  # Good
        elif words_per_sentence < 40:
            return 70.0  # Reasonable
        else:
            return 50.0  # Wordy

    def _should_add_disclaimer(self, query: str, response: str) -> bool:
        """Determine if trading disclaimer should be added."""
        trading_keywords = [
            'buy', 'sell', 'invest', 'trade', 'stock', 'price', 'recommendation',
            'advice', 'strategy', 'profit', 'loss', 'risk', 'return', 'portfolio',
            'market', 'analysis', 'prediction', 'forecast', 'bullish', 'bearish'
        ]

        combined_text = (query + " " + response).lower()
        return any(keyword in combined_text for keyword in trading_keywords)

    def _add_trading_disclaimer(self, response: str) -> str:
        """Add comprehensive trading disclaimer to response."""
        disclaimer = (
            "\n\n⚠️ **Important Trading Disclaimer:** "
            "This information is for educational and informational purposes only and should not be considered as financial advice, "
            "investment recommendations, or guarantees of future performance. All investments involve risk, including the possible "
            "loss of principal. Market data is provided by third-party sources and may not be real-time or completely accurate. "
            "Always conduct your own research, consider your financial situation, and consult with qualified financial advisors "
            "before making any investment decisions. Past performance is not indicative of future results. "
            "Trading and investing in financial markets carries significant risk and is not suitable for all investors."
        )

        return response + disclaimer

    async def _generate_fallback_response(
        self,
        query: str,
        intent_result: IntentResult,
        tool_result: Optional[ToolResult],
        correlation_id: str
    ) -> str:
        """Generate intelligent fallback response when AI synthesis fails."""
        import random

        # Select fallback based on intent and available data
        if intent_result.intent == "casual":
            fallback = random.choice(self.fallback_responses["casual"])
        elif intent_result.intent in ["price", "quote"]:
            tools_used = getattr(tool_result, 'tools_used', []) if tool_result else []
            if tool_result and tools_used:
                fallback = f"I've retrieved the latest market data for your query. Here's a summary of the key information:"
            else:
                fallback = "I can help you get current stock prices and market data. Please specify a stock symbol or market index."
        elif intent_result.intent in ["technical", "analysis"]:
            fallback = "Technical analysis involves studying price charts and indicators to identify trading opportunities. Common indicators include RSI, MACD, and moving averages."
        else:
            fallback = random.choice(self.fallback_responses["data_needed"])

        # Add tool data summary if available
        tools_used = getattr(tool_result, 'tools_used', []) if tool_result else []
        if tool_result and tools_used:
            tool_summary = f"\n\n**Data Sources:** {', '.join(tools_used)}"
            fallback += tool_summary

        # Log fallback usage
        observability_manager.record_metric(
            name="fallback_response_used",
            value=1,
            metric_type=MetricType.COUNTER,
            tags={'intent': intent_result.intent, 'reason': 'ai_failure'}
        )

        return fallback