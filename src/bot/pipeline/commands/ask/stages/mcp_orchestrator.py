"""
Advanced MCP Orchestrator for ASK Pipeline

Integrates with MCP (Modular Component Platform) for tool execution, resource management, and fallback strategies.
Provides comprehensive tool orchestration with performance monitoring and error handling.
"""

import asyncio
import time
import json
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass

from src.shared.error_handling.logging import get_logger
from src.shared.monitoring.observability import observability_manager
from src.bot.pipeline.commands.ask.audit import get_audit_logger
from src.bot.pipeline.commands.ask.cost import record_ai_usage, record_data_usage
from src.bot.pipeline.commands.ask.security.security_manager import (
    validate_request_input, check_rate_limit, scan_input_for_threats
)
from src.bot.pipeline.commands.ask.config.ask_config import get_ask_config

logger = get_logger(__name__)

@dataclass
class MCPSession:
    """MCP session context"""
    session_id: str
    user_id: str
    correlation_id: str
    max_concurrent_tools: int = 5
    timeout: float = 60.0
    fallback_enabled: bool = True

class MCPOrchestrator:
    """
    Advanced MCP Orchestrator with comprehensive integration

    Features:
    - MCP tool execution with resource management
    - Parallel tool execution with semaphore control
    - Comprehensive error handling and fallbacks
    - Observability and cost tracking
    - Security validation and rate limiting
    - Performance monitoring and grading
    """

    def __init__(self):
        self.config = get_ask_config()
        self.semaphore = asyncio.Semaphore(self.config.tools.max_concurrent)
        self.active_sessions: Dict[str, MCPSession] = {}
        self.tool_registry: Dict[str, callable] = self._register_tools()
        self.fallback_handler = self._create_fallback_handler()

    def _register_tools(self) -> Dict[str, callable]:
        """Register available MCP tools"""
        tools = {
            'get_stock_price': self._get_stock_price,
            'get_technical_indicators': self._get_technical_indicators,
            'get_company_overview': self._get_company_overview,
            'search_news': self._search_news,
            'analyze_sentiment': self._analyze_sentiment,
            'get_market_data': self._get_market_data
        }
        logger.info(f"Registered {len(tools)} MCP tools")
        return tools

    def _create_fallback_handler(self):
        """Create fallback handler for tool failures"""
        class FallbackHandler:
            async def handle_tool_failure(self, tool_name: str, error: Exception, context: Dict[str, Any]) -> Dict[str, Any]:
                logger.warning(f"Tool {tool_name} failed: {str(error)}", extra=context)
                
                # Record cost for failed tool
                record_data_usage(
                    provider="mcp_orchestrator",
                    data_type=f"tool_{tool_name}",
                    request_count=1,
                    user_id=context.get('user_id')
                )
                
                # Return fallback data
                return {
                    'success': False,
                    'error': str(error),
                    'data': None,
                    'fallback_used': True,
                    'tools_used': [tool_name]
                }

        return FallbackHandler()

    async def create_session(self, user_id: str, correlation_id: str) -> str:
        """Create MCP session"""
        session_id = f"mcp_{user_id}_{correlation_id}_{int(time.time())}"
        
        session = MCPSession(
            session_id=session_id,
            user_id=user_id,
            correlation_id=correlation_id,
            max_concurrent_tools=self.config.tools.max_concurrent,
            timeout=self.config.tools.timeout,
            fallback_enabled=self.config.tools.fallback_tools_enabled
        )
        
        self.active_sessions[session_id] = session
        
        # Start observability tracking
        observability_manager.start_operation(
            operation_name="mcp_session",
            correlation_id=correlation_id,
            user_id=user_id,
            metadata={'session_id': session_id}
        )
        
        logger.info(f"MCP session created: {session_id}")
        return session_id

    async def execute_tools(
        self,
        session_id: str,
        tools_to_execute: List[Dict[str, Any]],
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Execute multiple tools with parallelization and error handling"""
        if session_id not in self.active_sessions:
            raise ValueError(f"Invalid session ID: {session_id}")

        session = self.active_sessions[session_id]
        user_id = session.user_id
        correlation_id = session.correlation_id

        # Security validation
        validation_result, issues = validate_request_input(context)
        if not validation_result:
            logger.warning(f"Input validation failed: {issues}", extra={'correlation_id': correlation_id})
            return {
                'success': False,
                'error': f"Input validation failed: {issues}",
                'tools_used': [],
                'results': {}
            }

        # Rate limiting check
        rate_ok = await check_rate_limit(user_id, "user")
        if not rate_ok:
            logger.warning("Rate limit exceeded", extra={'correlation_id': correlation_id})
            return {
                'success': False,
                'error': "Rate limit exceeded. Please try again later.",
                'tools_used': [],
                'results': {}
            }

        # Security scan
        threats = scan_input_for_threats(context)
        if threats:
            logger.warning(f"Security threats detected: {threats}", extra={'correlation_id': correlation_id})
            return {
                'success': False,
                'error': "Security scan failed. Request blocked.",
                'tools_used': [],
                'results': {}
            }

        # Start observability tracking
        operation_id = observability_manager.start_operation(
            operation_name="mcp_tool_execution",
            correlation_id=correlation_id,
            user_id=user_id,
            metadata={
                'session_id': session_id,
                'tools_count': len(tools_to_execute),
                'max_concurrent': session.max_concurrent_tools
            }
        )

        # Audit log tool execution start
        audit_logger = get_audit_logger()
        if audit_logger:
            audit_logger.log_data_access(
                user_id=user_id,
                resource_id=f"mcp_tools_{correlation_id}",
                data_type="tool_execution",
                access_method="execute"
            )

        try:
            # Execute tools in parallel with semaphore
            tasks = []
            for tool_config in tools_to_execute:
                tool_name = tool_config['name']
                tool_args = tool_config.get('args', {})
                
                if tool_name in self.tool_registry:
                    task = self._execute_single_tool(
                        tool_name,
                        tool_args,
                        session_id,
                        correlation_id,
                        user_id
                    )
                    tasks.append(task)
                else:
                    logger.warning(f"Unknown tool: {tool_name}", extra={'correlation_id': correlation_id})
                    tasks.append(asyncio.create_task(
                        self._create_failed_tool_result(tool_name, "Unknown tool", correlation_id)
                    ))

            # Execute with semaphore control
            semaphore_tasks = []
            for task in tasks:
                async with self.semaphore:
                    semaphore_tasks.append(task)
                
                # Wait for some tasks to complete if too many are running
                if len(asyncio.all_tasks()) - 1 > session.max_concurrent_tools:
                    await asyncio.sleep(0.1)

            results = await asyncio.gather(*semaphore_tasks, return_exceptions=True)

            # Process results
            tool_results = {}
            tools_used = []
            
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    tool_name = tools_to_execute[i]['name']
                    tool_results[tool_name] = await self.fallback_handler.handle_tool_failure(
                        tool_name, result, {'correlation_id': correlation_id, 'user_id': user_id}
                    )
                else:
                    tool_result = result
                    tool_name = tools_to_execute[i]['name']
                    tool_results[tool_name] = tool_result
                    tools_used.append(tool_name)

            # Record costs for successful tools
            for tool_name, result in tool_results.items():
                if result.get('success'):
                    record_data_usage(
                        provider="mcp_orchestrator",
                        data_type=f"tool_{tool_name}",
                        request_count=1,
                        user_id=user_id
                    )

            # End observability tracking
            observability_manager.end_operation(
                operation_id=operation_id,
                success=True,
                error_message=None
            )

            # Audit log completion
            if audit_logger:
                audit_logger.log_data_access(
                    user_id=user_id,
                    resource_id=f"mcp_tools_{correlation_id}",
                    data_type="tool_execution",
                    access_method="complete",
                    metadata={'tools_executed': len(tools_used), 'success_rate': len([r for r in results if not isinstance(r, Exception)]) / len(results)}
                )

            return {
                'success': True,
                'tools_used': tools_used,
                'results': tool_results,
                'execution_time': time.time() - start_time,
                'session_id': session_id
            }

        except Exception as e:
            logger.error(f"MCP orchestration failed: {str(e)}", extra={'correlation_id': correlation_id})
            
            # End observability with error
            observability_manager.end_operation(
                operation_id=operation_id,
                success=False,
                error_message=str(e)
            )

            return {
                'success': False,
                'error': str(e),
                'tools_used': [],
                'results': {},
                'session_id': session_id
            }

    async def _execute_single_tool(self, tool_name: str, args: Dict[str, Any],
                                 session_id: str, correlation_id: str, user_id: str) -> Dict[str, Any]:
        """Execute a single tool with timeout and error handling"""
        start_time = time.time()
        tool_operation_id = observability_manager.start_operation(
            operation_name=f"mcp_tool_{tool_name}",
            correlation_id=correlation_id,
            user_id=user_id,
            metadata={
                'session_id': session_id,
                'tool_name': tool_name,
                'args': args
            }
        )

        try:
            # Execute tool with timeout
            tool_func = self.tool_registry[tool_name]
            result = await asyncio.wait_for(
                tool_func(args, correlation_id, user_id),
                timeout=self.config.tools.timeout
            )

            execution_time = time.time() - start_time

            observability_manager.end_operation(
                operation_id=tool_operation_id,
                success=True,
                error_message=None
            )

            return {
                'success': True,
                'tool_name': tool_name,
                'data': result,
                'execution_time': execution_time,
                'cache_hit': False  # Implement caching
            }

        except asyncio.TimeoutError:
            logger.error(f"Tool {tool_name} timed out after {self.config.tools.timeout}s", extra={'correlation_id': correlation_id})
            
            observability_manager.end_operation(
                operation_id=tool_operation_id,
                success=False,
                error_message=f"Timeout after {self.config.tools.timeout}s"
            )

            return await self.fallback_handler.handle_tool_failure(
                tool_name, Exception("Timeout"), {'correlation_id': correlation_id, 'user_id': user_id}
            )

        except Exception as e:
            logger.error(f"Tool {tool_name} execution error: {str(e)}", extra={'correlation_id': correlation_id})
            
            observability_manager.end_operation(
                operation_id=tool_operation_id,
                success=False,
                error_message=str(e)
            )

            return await self.fallback_handler.handle_tool_failure(
                tool_name, e, {'correlation_id': correlation_id, 'user_id': user_id}
            )

    async def _create_failed_tool_result(self, tool_name: str, error_msg: str, correlation_id: str) -> Dict[str, Any]:
        """Create failed tool result"""
        return {
            'success': False,
            'tool_name': tool_name,
            'error': error_msg,
            'data': None,
            'execution_time': 0.0,
            'cache_hit': False
        }

    # Tool implementations (mocked for demonstration)
    async def _get_stock_price(self, args: Dict[str, Any], correlation_id: str, user_id: str) -> Dict[str, Any]:
        """Get stock price tool"""
        symbol = args.get('symbol', 'AAPL')
        
        # Simulate API call
        await asyncio.sleep(0.5)
        
        return {
            'symbol': symbol,
            'price': 150.25 + (time.time() % 10),  # Mock price
            'change': 2.15,
            'change_percent': 1.45,
            'volume': 12345678,
            'timestamp': datetime.now().isoformat()
        }

    async def _get_technical_indicators(self, args: Dict[str, Any], correlation_id: str, user_id: str) -> Dict[str, Any]:
        """Get technical indicators tool"""
        symbol = args.get('symbol', 'AAPL')
        indicators = args.get('indicators', ['rsi', 'macd'])
        
        # Simulate calculation
        await asyncio.sleep(1.0)
        
        result = {
            'symbol': symbol,
            'indicators': {}
        }
        
        for indicator in indicators:
            if indicator == 'rsi':
                result['indicators']['rsi'] = {
                    'period': 14,
                    'value': 65.2,
                    'signal': 'overbought' if 65.2 > 70 else 'neutral'
                }
            elif indicator == 'macd':
                result['indicators']['macd'] = {
                    'macd': 1.25,
                    'signal': 0.85,
                    'histogram': 0.40,
                    'signal_line': 'bullish'
                }
        
        return result

    async def _get_company_overview(self, args: Dict[str, Any], correlation_id: str, user_id: str) -> Dict[str, Any]:
        """Get company overview tool"""
        symbol = args.get('symbol', 'AAPL')
        
        # Simulate API call
        await asyncio.sleep(0.8)
        
        return {
            'symbol': symbol,
            'companyName': 'Apple Inc.',
            'exchange': 'NASDAQ',
            'industry': 'Technology',
            'sector': 'Consumer Electronics',
            'marketCap': 2500000000000,
            'peRatio': 28.5,
            'dividendYield': 0.005,
            'description': 'Apple Inc. designs, manufactures, and markets smartphones, personal computers, tablets, and wearable digital devices worldwide.'
        }

    async def _search_news(self, args: Dict[str, Any], correlation_id: str, user_id: str) -> Dict[str, Any]:
        """Search news tool"""
        query = args.get('query', 'market news')
        count = args.get('count', 5)
        
        # Simulate news search
        await asyncio.sleep(1.2)
        
        return {
            'query': query,
            'count': count,
            'articles': [
                {
                    'title': f"Market News Headline {i}",
                    'source': 'Financial News',
                    'published': datetime.now().isoformat(),
                    'summary': f"Summary of market news article {i} related to {query}",
                    'url': f"https://news.example.com/article/{i}"
                }
                for i in range(1, count + 1)
            ]
        }

    async def _analyze_sentiment(self, args: Dict[str, Any], correlation_id: str, user_id: str) -> Dict[str, Any]:
        """Analyze sentiment tool"""
        text = args.get('text', 'Sample market text')
        
        # Simulate sentiment analysis
        await asyncio.sleep(0.7)
        
        return {
            'text': text[:100] + '...' if len(text) > 100 else text,
            'sentiment': 'positive',
            'confidence': 0.85,
            'score': 0.72,
            'magnitude': 0.65
        }

    async def _get_market_data(self, args: Dict[str, Any], correlation_id: str, user_id: str) -> Dict[str, Any]:
        """Get comprehensive market data tool"""
        symbols = args.get('symbols', ['AAPL', 'GOOGL', 'MSFT'])
        
        # Simulate market data retrieval
        await asyncio.sleep(1.5)
        
        return {
            'symbols': symbols,
            'market_data': {
                symbol: {
                    'price': 150.25 + (hash(symbol) % 10),
                    'change': 1.5 + (hash(symbol) % 5),
                    'volume': 10000000 + (hash(symbol) % 1000000)
                }
                for symbol in symbols
            },
            'market_status': 'open',
            'timestamp': datetime.now().isoformat()
        }

    async def close_session(self, session_id: str) -> bool:
        """Close MCP session"""
        if session_id in self.active_sessions:
            del self.active_sessions[session_id]
            
            # End observability tracking for session
            observability_manager.end_operation(
                operation_name="mcp_session",
                correlation_id=self.active_sessions[session_id].correlation_id,
                success=True
            )
            
            logger.info(f"MCP session closed: {session_id}")
            return True
        return False

# Global orchestrator instance
mcp_orchestrator = MCPOrchestrator()

def get_mcp_orchestrator() -> MCPOrchestrator:
    """Get global MCP orchestrator"""
    return mcp_orchestrator