"""
Fallback Handler for ASK Pipeline
Handles fallback responses when primary processing fails.
"""

import logging
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from enum import Enum

from src.core.monitoring.logger import get_structured_logger
from src.bot.pipeline.commands.ask.core.models import PipelineResult

logger = get_structured_logger(__name__)

class FallbackType(Enum):
    """Types of fallback responses"""
    GENERIC = "generic"
    ERROR_SPECIFIC = "error_specific"
    CONTEXT_AWARE = "context_aware"
    CACHED = "cached"

@dataclass
class FallbackResponse:
    """Structured fallback response"""
    message: str
    fallback_type: FallbackType
    confidence: float = 0.1
    metadata: Optional[Dict[str, Any]] = None

class FallbackHandler:
    """
    Handles fallback responses when primary ASK pipeline processing fails.
    Provides context-aware fallback messages based on error type and user query.
    """
    
    def __init__(self):
        self.logger = get_structured_logger(__name__)
        self.fallback_templates = {
            FallbackType.GENERIC: [
                "I'm having trouble processing your request right now. Please try again in a moment.",
                "I'm experiencing some technical difficulties. Could you rephrase your question?",
                "I'm unable to provide a complete response at the moment. Please try again."
            ],
            FallbackType.ERROR_SPECIFIC: {
                "timeout": "Your request is taking longer than expected. Please try a simpler query.",
                "rate_limit": "I'm receiving too many requests. Please wait a moment and try again.",
                "api_error": "I'm having trouble connecting to external services. Please try again shortly.",
                "parsing_error": "I'm having trouble understanding your request. Could you rephrase it?",
                "validation_error": "There seems to be an issue with your request format. Please check and try again."
            },
            FallbackType.CONTEXT_AWARE: {
                "market_data": "I'm unable to fetch current market data. Please check your symbols and try again.",
                "analysis": "I can't perform the requested analysis right now. Please try a different approach.",
                "trading": "Trading-related requests are temporarily unavailable. Please try again later.",
                "general": "I'm having trouble with your request. Could you be more specific?"
            }
        }
        
    def get_fallback_response(
        self, 
        query: str, 
        error_type: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> FallbackResponse:
        """
        Generate appropriate fallback response based on query and error context.
        
        Args:
            query: Original user query
            error_type: Type of error that occurred
            context: Additional context about the failure
            
        Returns:
            FallbackResponse with appropriate message and metadata
        """
        try:
            # Determine fallback type based on error and context
            fallback_type = self._determine_fallback_type(query, error_type, context)
            
            # Generate appropriate message
            message = self._generate_fallback_message(fallback_type, error_type, query, context)
            
            # Calculate confidence based on fallback quality
            confidence = self._calculate_fallback_confidence(fallback_type, error_type)
            
            response = FallbackResponse(
                message=message,
                fallback_type=fallback_type,
                confidence=confidence,
                metadata={
                    "original_query": query,
                    "error_type": error_type,
                    "context": context,
                    "timestamp": self._get_timestamp()
                }
            )
            
            self.logger.info(f"Generated fallback response: {fallback_type.value}")
            return response
            
        except Exception as e:
            self.logger.error(f"Error generating fallback response: {e}")
            return self._get_emergency_fallback()
    
    def _determine_fallback_type(
        self, 
        query: str, 
        error_type: Optional[str], 
        context: Optional[Dict[str, Any]]
    ) -> FallbackType:
        """Determine the most appropriate fallback type"""
        
        if error_type and error_type in self.fallback_templates[FallbackType.ERROR_SPECIFIC]:
            return FallbackType.ERROR_SPECIFIC
            
        # Check for context-aware fallbacks
        if context:
            query_lower = query.lower()
            if any(term in query_lower for term in ["price", "stock", "ticker", "market"]):
                return FallbackType.CONTEXT_AWARE
            elif any(term in query_lower for term in ["analyze", "analysis", "trend"]):
                return FallbackType.CONTEXT_AWARE
            elif any(term in query_lower for term in ["buy", "sell", "trade", "position"]):
                return FallbackType.CONTEXT_AWARE
        
        return FallbackType.GENERIC
    
    def _generate_fallback_message(
        self, 
        fallback_type: FallbackType, 
        error_type: Optional[str],
        query: str,
        context: Optional[Dict[str, Any]]
    ) -> str:
        """Generate the actual fallback message"""
        
        if fallback_type == FallbackType.ERROR_SPECIFIC and error_type:
            return self.fallback_templates[FallbackType.ERROR_SPECIFIC].get(
                error_type, 
                self.fallback_templates[FallbackType.GENERIC][0]
            )
        
        elif fallback_type == FallbackType.CONTEXT_AWARE:
            query_lower = query.lower()
            if any(term in query_lower for term in ["price", "stock", "ticker", "market"]):
                return self.fallback_templates[FallbackType.CONTEXT_AWARE]["market_data"]
            elif any(term in query_lower for term in ["analyze", "analysis", "trend"]):
                return self.fallback_templates[FallbackType.CONTEXT_AWARE]["analysis"]
            elif any(term in query_lower for term in ["buy", "sell", "trade", "position"]):
                return self.fallback_templates[FallbackType.CONTEXT_AWARE]["trading"]
            else:
                return self.fallback_templates[FallbackType.CONTEXT_AWARE]["general"]
        
        # Default to generic fallback
        import random
        return random.choice(self.fallback_templates[FallbackType.GENERIC])
    
    def _calculate_fallback_confidence(self, fallback_type: FallbackType, error_type: Optional[str]) -> float:
        """Calculate confidence score for fallback response"""
        base_confidence = {
            FallbackType.CONTEXT_AWARE: 0.3,
            FallbackType.ERROR_SPECIFIC: 0.2,
            FallbackType.CACHED: 0.4,
            FallbackType.GENERIC: 0.1
        }
        return base_confidence.get(fallback_type, 0.1)
    
    def _get_emergency_fallback(self) -> FallbackResponse:
        """Emergency fallback when even fallback generation fails"""
        return FallbackResponse(
            message="I'm experiencing technical difficulties. Please try again later.",
            fallback_type=FallbackType.GENERIC,
            confidence=0.05,
            metadata={"emergency": True, "timestamp": self._get_timestamp()}
        )
    
    def _get_timestamp(self) -> str:
        """Get current timestamp"""
        from datetime import datetime
        return datetime.utcnow().isoformat()
    
    def create_fallback_pipeline_result(
        self, 
        query: str, 
        error_type: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None,
        correlation_id: str = ""
    ) -> PipelineResult:
        """
        Create a complete PipelineResult with fallback response.
        
        Args:
            query: Original user query
            error_type: Type of error that occurred
            context: Additional context about the failure
            correlation_id: Request correlation ID
            
        Returns:
            PipelineResult with fallback response
        """
        fallback_response = self.get_fallback_response(query, error_type, context)
        
        return PipelineResult(
            success=False,  # Fallback indicates failure of primary processing
            response=fallback_response.message,
            error=f"Primary processing failed: {error_type or 'unknown error'}",
            execution_time=0.1,  # Minimal time for fallback
            correlation_id=correlation_id,
            confidence=fallback_response.confidence,
            cache_hit=False,
            tools_used=["fallback_handler"],
            intent="fallback"
        )
    
    def handle_error(self, error: Exception, context: Optional[Dict[str, Any]] = None) -> FallbackResponse:
        """
        Handle an error by generating an appropriate fallback response
        
        Args:
            error: The exception that occurred
            context: Additional context about the error
            
        Returns:
            FallbackResponse with appropriate fallback message
        """
        error_type = type(error).__name__.lower()
        error_message = str(error).lower()
        
        # Determine fallback type based on error
        if 'timeout' in error_message:
            fallback_type = FallbackType.ERROR_SPECIFIC
            error_key = "timeout"
        elif 'rate' in error_message and 'limit' in error_message:
            fallback_type = FallbackType.ERROR_SPECIFIC
            error_key = "rate_limit"
        elif 'api' in error_message or 'service' in error_message:
            fallback_type = FallbackType.ERROR_SPECIFIC
            error_key = "api_error"
        elif 'validation' in error_message or 'parse' in error_message:
            fallback_type = FallbackType.ERROR_SPECIFIC
            error_key = "validation_error"
        else:
            fallback_type = FallbackType.GENERIC
            error_key = None
        
        # Get fallback message
        if fallback_type == FallbackType.ERROR_SPECIFIC and error_key:
            message = self.fallback_templates[fallback_type].get(error_key, "I encountered an unexpected error.")
        else:
            messages = self.fallback_templates[fallback_type]
            if isinstance(messages, list):
                message = messages[0]  # Use first generic message
            else:
                message = "I encountered an unexpected error."
        
        return FallbackResponse(
            message=message,
            fallback_type=fallback_type,
            metadata={
                "error_type": error_type,
                "error_message": str(error),
                "context": context or {}
            }
        )

# Global instance
fallback_handler = FallbackHandler()
