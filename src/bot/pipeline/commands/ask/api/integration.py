"""
Integration Patterns for ASK Pipeline

Provides integration capabilities:
- Event-driven architecture for component communication
- Message queuing for asynchronous operations
- Integration adapters for external services
- Retry logic and circuit breaker patterns
- Integration monitoring and failure detection
"""

import asyncio
import logging
import time
from typing import Dict, Any, Optional, List, Callable, Awaitable, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import json
import uuid
from abc import ABC, abstractmethod
from contextlib import asynccontextmanager

logger = logging.getLogger(__name__)

class EventType(Enum):
    """Event type enumeration"""
    REQUEST_RECEIVED = "request_received"
    REQUEST_PROCESSED = "request_processed"
    REQUEST_FAILED = "request_failed"
    COMPONENT_STARTED = "component_started"
    COMPONENT_STOPPED = "component_stopped"
    HEALTH_CHECK = "health_check"
    METRICS_UPDATE = "metrics_update"
    ERROR_OCCURRED = "error_occurred"

class CircuitState(Enum):
    """Circuit breaker state"""
    CLOSED = "closed"
    OPEN = "open"
    HALF_OPEN = "half_open"

class RetryStrategy(Enum):
    """Retry strategy enumeration"""
    FIXED = "fixed"
    EXPONENTIAL = "exponential"
    LINEAR = "linear"
    CUSTOM = "custom"

@dataclass
class Event:
    """Event data structure"""
    event_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    event_type: EventType = EventType.REQUEST_RECEIVED
    timestamp: datetime = field(default_factory=datetime.utcnow)
    source: str = "system"
    data: Dict[str, Any] = field(default_factory=dict)
    correlation_id: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class RetryConfig:
    """Retry configuration"""
    max_attempts: int = 3
    base_delay: float = 1.0
    max_delay: float = 60.0
    strategy: RetryStrategy = RetryStrategy.EXPONENTIAL
    backoff_multiplier: float = 2.0
    jitter: bool = True
    retryable_exceptions: List[type] = field(default_factory=list)

@dataclass
class CircuitBreakerConfig:
    """Circuit breaker configuration"""
    failure_threshold: int = 5
    recovery_timeout: float = 60.0
    success_threshold: int = 3
    timeout: float = 30.0

class EventBus:
    """Event bus for component communication"""
    
    def __init__(self):
        self.subscribers: Dict[EventType, List[Callable]] = {}
        self.event_history: List[Event] = []
        self._lock = asyncio.Lock()
    
    async def publish(self, event: Event):
        """Publish an event"""
        async with self._lock:
            # Store event in history
            self.event_history.append(event)
            
            # Keep only last 1000 events
            if len(self.event_history) > 1000:
                self.event_history = self.event_history[-1000:]
            
            # Notify subscribers
            subscribers = self.subscribers.get(event.event_type, [])
            for subscriber in subscribers:
                try:
                    if asyncio.iscoroutinefunction(subscriber):
                        await subscriber(event)
                    else:
                        subscriber(event)
                except Exception as e:
                    logger.error(f"Event subscriber error: {e}")
    
    def subscribe(self, event_type: EventType, handler: Callable):
        """Subscribe to an event type"""
        if event_type not in self.subscribers:
            self.subscribers[event_type] = []
        
        self.subscribers[event_type].append(handler)
        logger.debug(f"Subscribed to {event_type.value}")
    
    def unsubscribe(self, event_type: EventType, handler: Callable):
        """Unsubscribe from an event type"""
        if event_type in self.subscribers:
            if handler in self.subscribers[event_type]:
                self.subscribers[event_type].remove(handler)
                logger.debug(f"Unsubscribed from {event_type.value}")
    
    def get_event_history(self, event_type: Optional[EventType] = None, limit: int = 100) -> List[Event]:
        """Get event history"""
        events = self.event_history
        if event_type:
            events = [e for e in events if e.event_type == event_type]
        
        return events[-limit:]

class MessageQueue:
    """Message queue for asynchronous operations"""
    
    def __init__(self, max_size: int = 10000):
        self.queue: asyncio.Queue = asyncio.Queue(maxsize=max_size)
        self.processing = False
        self.workers: List[asyncio.Task] = []
        self._lock = asyncio.Lock()
    
    async def enqueue(self, message: Dict[str, Any], priority: int = 1) -> bool:
        """Enqueue a message"""
        try:
            message_data = {
                "id": str(uuid.uuid4()),
                "timestamp": datetime.utcnow().isoformat(),
                "priority": priority,
                "data": message
            }
            
            await self.queue.put(message_data)
            logger.debug(f"Message enqueued: {message_data['id']}")
            return True
            
        except asyncio.QueueFull:
            logger.warning("Message queue is full")
            return False
    
    async def dequeue(self) -> Optional[Dict[str, Any]]:
        """Dequeue a message"""
        try:
            message = await asyncio.wait_for(self.queue.get(), timeout=1.0)
            return message
        except asyncio.TimeoutError:
            return None
    
    async def start_workers(self, num_workers: int = 5, handler: Callable = None):
        """Start message queue workers"""
        if self.processing:
            return
        
        self.processing = True
        
        for i in range(num_workers):
            worker = asyncio.create_task(self._worker(f"worker-{i}", handler))
            self.workers.append(worker)
        
        logger.info(f"Started {num_workers} message queue workers")
    
    async def stop_workers(self):
        """Stop message queue workers"""
        self.processing = False
        
        for worker in self.workers:
            worker.cancel()
        
        await asyncio.gather(*self.workers, return_exceptions=True)
        self.workers.clear()
        
        logger.info("Stopped message queue workers")
    
    async def _worker(self, worker_id: str, handler: Callable):
        """Message queue worker"""
        logger.debug(f"Worker {worker_id} started")
        
        while self.processing:
            try:
                message = await self.dequeue()
                if message and handler:
                    await handler(message)
                elif message:
                    logger.debug(f"Worker {worker_id} processed message: {message['id']}")
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Worker {worker_id} error: {e}")
        
        logger.debug(f"Worker {worker_id} stopped")

class RetryManager:
    """Retry logic manager"""
    
    def __init__(self, config: RetryConfig):
        self.config = config
    
    async def execute_with_retry(self, func: Callable, *args, **kwargs) -> Any:
        """Execute function with retry logic"""
        last_exception = None
        
        for attempt in range(self.config.max_attempts):
            try:
                if asyncio.iscoroutinefunction(func):
                    return await func(*args, **kwargs)
                else:
                    return func(*args, **kwargs)
                    
            except Exception as e:
                last_exception = e
                
                # Check if exception is retryable
                if not self._is_retryable(e):
                    raise e
                
                # Check if we've exhausted retries
                if attempt == self.config.max_attempts - 1:
                    break
                
                # Calculate delay
                delay = self._calculate_delay(attempt)
                
                logger.warning(f"Attempt {attempt + 1} failed, retrying in {delay}s: {e}")
                await asyncio.sleep(delay)
        
        # All retries exhausted
        raise last_exception
    
    def _is_retryable(self, exception: Exception) -> bool:
        """Check if exception is retryable"""
        if self.config.retryable_exceptions:
            return isinstance(exception, tuple(self.config.retryable_exceptions))
        
        # Default retryable exceptions
        retryable_exceptions = (
            ConnectionError,
            TimeoutError,
            asyncio.TimeoutError,
            OSError
        )
        
        return isinstance(exception, retryable_exceptions)
    
    def _calculate_delay(self, attempt: int) -> float:
        """Calculate retry delay"""
        if self.config.strategy == RetryStrategy.FIXED:
            delay = self.config.base_delay
        elif self.config.strategy == RetryStrategy.EXPONENTIAL:
            delay = self.config.base_delay * (self.config.backoff_multiplier ** attempt)
        elif self.config.strategy == RetryStrategy.LINEAR:
            delay = self.config.base_delay * (attempt + 1)
        else:
            delay = self.config.base_delay
        
        # Apply jitter
        if self.config.jitter:
            import random
            delay *= (0.5 + random.random() * 0.5)
        
        # Cap at max delay
        return min(delay, self.config.max_delay)

class CircuitBreaker:
    """Circuit breaker pattern implementation"""
    
    def __init__(self, config: CircuitBreakerConfig):
        self.config = config
        self.state = CircuitState.CLOSED
        self.failure_count = 0
        self.success_count = 0
        self.last_failure_time = None
        self._lock = asyncio.Lock()
    
    async def execute(self, func: Callable, *args, **kwargs) -> Any:
        """Execute function with circuit breaker"""
        async with self._lock:
            if self.state == CircuitState.OPEN:
                if self._should_attempt_reset():
                    self.state = CircuitState.HALF_OPEN
                    self.success_count = 0
                else:
                    raise Exception("Circuit breaker is OPEN")
        
        try:
            # Execute function with timeout
            result = await asyncio.wait_for(
                func(*args, **kwargs) if asyncio.iscoroutinefunction(func) else func(*args, **kwargs),
                timeout=self.config.timeout
            )
            
            await self._on_success()
            return result
            
        except Exception as e:
            await self._on_failure()
            raise e
    
    def _should_attempt_reset(self) -> bool:
        """Check if circuit breaker should attempt reset"""
        if self.last_failure_time is None:
            return True
        
        return (datetime.utcnow() - self.last_failure_time).total_seconds() >= self.config.recovery_timeout
    
    async def _on_success(self):
        """Handle successful execution"""
        async with self._lock:
            if self.state == CircuitState.HALF_OPEN:
                self.success_count += 1
                if self.success_count >= self.config.success_threshold:
                    self.state = CircuitState.CLOSED
                    self.failure_count = 0
                    logger.info("Circuit breaker reset to CLOSED")
            elif self.state == CircuitState.CLOSED:
                self.failure_count = 0
    
    async def _on_failure(self):
        """Handle failed execution"""
        async with self._lock:
            self.failure_count += 1
            self.last_failure_time = datetime.utcnow()
            
            if self.state == CircuitState.HALF_OPEN:
                self.state = CircuitState.OPEN
                logger.warning("Circuit breaker opened from HALF_OPEN")
            elif self.state == CircuitState.CLOSED and self.failure_count >= self.config.failure_threshold:
                self.state = CircuitState.OPEN
                logger.warning("Circuit breaker opened")
    
    def get_state(self) -> CircuitState:
        """Get current circuit breaker state"""
        return self.state
    
    def reset(self):
        """Reset circuit breaker"""
        with asyncio.Lock():
            self.state = CircuitState.CLOSED
            self.failure_count = 0
            self.success_count = 0
            self.last_failure_time = None

class IntegrationAdapter(ABC):
    """Base class for integration adapters"""
    
    def __init__(self, name: str):
        self.name = name
        self.connected = False
        self.retry_manager = RetryManager(RetryConfig())
        self.circuit_breaker = CircuitBreaker(CircuitBreakerConfig())
    
    @abstractmethod
    async def connect(self) -> bool:
        """Connect to external service"""
        pass
    
    @abstractmethod
    async def disconnect(self) -> bool:
        """Disconnect from external service"""
        pass
    
    @abstractmethod
    async def health_check(self) -> bool:
        """Check service health"""
        pass
    
    async def execute_with_resilience(self, func: Callable, *args, **kwargs) -> Any:
        """Execute function with retry and circuit breaker"""
        return await self.circuit_breaker.execute(
            self.retry_manager.execute_with_retry,
            func,
            *args,
            **kwargs
        )

class ExternalServiceAdapter(IntegrationAdapter):
    """Adapter for external services"""
    
    def __init__(self, name: str, base_url: str, timeout: float = 30.0):
        super().__init__(name)
        self.base_url = base_url
        self.timeout = timeout
        self.session = None
    
    async def connect(self) -> bool:
        """Connect to external service"""
        try:
            import aiohttp
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=self.timeout)
            )
            self.connected = True
            logger.info(f"Connected to {self.name}")
            return True
        except Exception as e:
            logger.error(f"Failed to connect to {self.name}: {e}")
            return False
    
    async def disconnect(self) -> bool:
        """Disconnect from external service"""
        try:
            if self.session:
                await self.session.close()
                self.session = None
            self.connected = False
            logger.info(f"Disconnected from {self.name}")
            return True
        except Exception as e:
            logger.error(f"Failed to disconnect from {self.name}: {e}")
            return False
    
    async def health_check(self) -> bool:
        """Check service health"""
        try:
            if not self.session:
                return False
            
            async with self.session.get(f"{self.base_url}/health") as response:
                return response.status == 200
        except Exception:
            return False
    
    async def make_request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """Make HTTP request to external service"""
        if not self.connected:
            raise Exception(f"Not connected to {self.name}")
        
        url = f"{self.base_url}{endpoint}"
        
        async with self.session.request(method, url, **kwargs) as response:
            response.raise_for_status()
            return await response.json()

class IntegrationMonitor:
    """Integration monitoring and failure detection"""
    
    def __init__(self):
        self.adapters: Dict[str, IntegrationAdapter] = {}
        self.metrics: Dict[str, Dict[str, Any]] = {}
        self.alerts: List[Dict[str, Any]] = []
        self.monitoring = False
        self.monitor_task = None
    
    def register_adapter(self, adapter: IntegrationAdapter):
        """Register integration adapter"""
        self.adapters[adapter.name] = adapter
        self.metrics[adapter.name] = {
            "requests": 0,
            "successes": 0,
            "failures": 0,
            "avg_response_time": 0.0,
            "last_check": None,
            "status": "unknown"
        }
        logger.info(f"Registered adapter: {adapter.name}")
    
    def unregister_adapter(self, name: str):
        """Unregister integration adapter"""
        if name in self.adapters:
            del self.adapters[name]
            del self.metrics[name]
            logger.info(f"Unregistered adapter: {name}")
    
    async def start_monitoring(self, interval: float = 60.0):
        """Start integration monitoring"""
        if self.monitoring:
            return
        
        self.monitoring = True
        self.monitor_task = asyncio.create_task(self._monitor_loop(interval))
        logger.info("Integration monitoring started")
    
    async def stop_monitoring(self):
        """Stop integration monitoring"""
        self.monitoring = False
        if self.monitor_task:
            self.monitor_task.cancel()
            await self.monitor_task
        logger.info("Integration monitoring stopped")
    
    async def _monitor_loop(self, interval: float):
        """Monitoring loop"""
        while self.monitoring:
            try:
                await self._check_all_adapters()
                await asyncio.sleep(interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Monitoring error: {e}")
                await asyncio.sleep(interval)
    
    async def _check_all_adapters(self):
        """Check all registered adapters"""
        for name, adapter in self.adapters.items():
            try:
                start_time = time.time()
                is_healthy = await adapter.health_check()
                response_time = time.time() - start_time
                
                # Update metrics
                metrics = self.metrics[name]
                metrics["last_check"] = datetime.utcnow()
                metrics["avg_response_time"] = (
                    (metrics["avg_response_time"] * metrics["requests"] + response_time) /
                    (metrics["requests"] + 1)
                )
                metrics["requests"] += 1
                
                if is_healthy:
                    metrics["successes"] += 1
                    metrics["status"] = "healthy"
                else:
                    metrics["failures"] += 1
                    metrics["status"] = "unhealthy"
                    await self._create_alert(name, "Health check failed")
                
            except Exception as e:
                logger.error(f"Health check failed for {name}: {e}")
                self.metrics[name]["failures"] += 1
                self.metrics[name]["status"] = "error"
                await self._create_alert(name, f"Health check error: {e}")
    
    async def _create_alert(self, adapter_name: str, message: str):
        """Create monitoring alert"""
        alert = {
            "id": str(uuid.uuid4()),
            "timestamp": datetime.utcnow(),
            "adapter": adapter_name,
            "message": message,
            "severity": "warning"
        }
        
        self.alerts.append(alert)
        
        # Keep only last 1000 alerts
        if len(self.alerts) > 1000:
            self.alerts = self.alerts[-1000:]
        
        logger.warning(f"Alert: {adapter_name} - {message}")
    
    def get_metrics(self, adapter_name: Optional[str] = None) -> Dict[str, Any]:
        """Get integration metrics"""
        if adapter_name:
            return self.metrics.get(adapter_name, {})
        return self.metrics
    
    def get_alerts(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Get recent alerts"""
        return self.alerts[-limit:]

# Global instances
_event_bus = EventBus()
_message_queue = MessageQueue()
_integration_monitor = IntegrationMonitor()

def get_event_bus() -> EventBus:
    """Get global event bus"""
    return _event_bus

def get_message_queue() -> MessageQueue:
    """Get global message queue"""
    return _message_queue

def get_integration_monitor() -> IntegrationMonitor:
    """Get global integration monitor"""
    return _integration_monitor

async def cleanup_integration():
    """Cleanup integration components"""
    await _message_queue.stop_workers()
    await _integration_monitor.stop_monitoring()
    
    # Disconnect all adapters
    for adapter in _integration_monitor.adapters.values():
        await adapter.disconnect()