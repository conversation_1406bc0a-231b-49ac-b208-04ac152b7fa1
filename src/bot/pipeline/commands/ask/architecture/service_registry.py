"""
Service Registry for ASK Pipeline Architecture - Simplified Refactored Version

Minimal registry for pipeline components (stages/services).
Integrated with core config_manager for registration/health; no distributed features.
Merged: "services" are stages like intent_detector; health is config flag.
Deleted overkill like load balancing, routes, HTTP checks - use pipeline_engine for orchestration.
Focus on bot: register/unregister stages, get healthy components, simple summary.
"""
import asyncio
from typing import Dict, Any, Optional, List
from enum import Enum
from dataclasses import dataclass, field
from datetime import datetime

from src.core.config_manager import ConfigManager  # Core for health/config
from src.shared.error_handling.logging import get_logger

logger = get_logger(__name__)


class ServiceStatus(str, Enum):
    """Component status."""
    HEALTHY = "healthy"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"


class ServiceType(str, Enum):
    """Pipeline component types."""
    STAGE = "stage"
    TOOL = "tool"
    VALIDATOR = "validator"
    ANALYZER = "analyzer"


@dataclass
class ServiceInstance:
    """Simplified component instance."""
    instance_id: str
    service_name: str  # e.g., "intent_detector"
    service_type: ServiceType
    status: ServiceStatus = ServiceStatus.UNKNOWN
    version: str = "1.0"
    last_check: datetime = field(default_factory=datetime.utcnow)
    metadata: Dict[str, Any] = field(default_factory=dict)


class ArchitectureServiceRegistry:
    """
    Simple registry for ASK pipeline components.
    Registers stages/tools; "health" via config flag (e.g., enabled=True).
    No monitoring loop/HTTP; check on demand via config.
    Integrates with pipeline_engine for dynamic loading.
    """
    def __init__(self, config_manager: ConfigManager):
        self.config = config_manager
        self.services: Dict[str, List[ServiceInstance]] = {}  # service_name -> instances
        self.instances: Dict[str, ServiceInstance] = {}  # instance_id -> instance
        logger.info("Architecture service registry initialized")

    def register_service(self, service_name: str, service_type: ServiceType, version: str = "1.0", metadata: Dict[str, Any] = None) -> str:
        """Register pipeline component."""
        instance_id = f"{service_name}_{int(datetime.utcnow().timestamp())}"
        
        instance = ServiceInstance(
            instance_id=instance_id,
            service_name=service_name,
            service_type=service_type,
            version=version,
            metadata=metadata or {}
        )
        
        # Check initial health via config
        self._update_health(instance)
        
        if service_name not in self.services:
            self.services[service_name] = []
        self.services[service_name].append(instance)
        self.instances[instance_id] = instance
        
        logger.info(f"Registered service: {service_name} ({service_type.value}) - {instance_id}")
        return instance_id

    def unregister_service(self, instance_id: str) -> bool:
        """Unregister component."""
        if instance_id not in self.instances:
            return False
        
        instance = self.instances[instance_id]
        service_name = instance.service_name
        
        if service_name in self.services:
            self.services[service_name] = [i for i in self.services[service_name] if i.instance_id != instance_id]
            if not self.services[service_name]:
                del self.services[service_name]
        
        del self.instances[instance_id]
        logger.info(f"Unregistered service: {service_name} - {instance_id}")
        return True

    def get_service_instances(self, service_name: str, healthy_only: bool = True) -> List[ServiceInstance]:
        """Get instances for service, filter healthy."""
        instances = self.services.get(service_name, [])
        if healthy_only:
            instances = [i for i in instances if i.status == ServiceStatus.HEALTHY]
        return instances

    def get_service_instance(self, service_name: str) -> Optional[ServiceInstance]:
        """Get first healthy instance."""
        healthy = self.get_service_instances(service_name, healthy_only=True)
        return healthy[0] if healthy else None

    def _update_health(self, instance: ServiceInstance):
        """Update health from config (e.g., enabled flag)."""
        config_key = f"services.{instance.service_name}.enabled"
        enabled = self.config.get(config_key, default=True)
        instance.status = ServiceStatus.HEALTHY if enabled else ServiceStatus.UNHEALTHY
        instance.last_check = datetime.utcnow()
        logger.debug(f"Health check for {instance.service_name}: {instance.status.value}")

    async def check_health(self, service_name: str):
        """Async health check - update all instances."""
        if service_name not in self.services:
            return
        
        for instance in self.services[service_name]:
            self._update_health(instance)
        
        logger.debug(f"Health checked for {service_name}")

    def get_health_summary(self) -> Dict[str, Any]:
        """Simple summary."""
        total_services = len(self.services)
        healthy_services = sum(1 for instances in self.services.values() if any(i.status == ServiceStatus.HEALTHY for i in instances))
        
        return {
            "total_services": total_services,
            "healthy_services": healthy_services,
            "services": {
                name: {
                    "instances": len(instances),
                    "healthy": sum(1 for i in instances if i.status == ServiceStatus.HEALTHY)
                }
                for name, instances in self.services.items()
            }
        }


# Global registry
_service_registry: Optional[ArchitectureServiceRegistry] = None


def get_architecture_service_registry(config_manager: ConfigManager) -> ArchitectureServiceRegistry:
    global _service_registry
    if _service_registry is None:
        _service_registry = ArchitectureServiceRegistry(config_manager)
    return _service_registry


async def setup_architecture_service_registry(config_manager: ConfigManager, pipeline_engine):
    """Setup registry - register core stages."""
    registry = get_architecture_service_registry(config_manager)
    
    # Register example stages
    registry.register_service("intent_detector", ServiceType.STAGE, metadata={"stage": "intent"})
    registry.register_service("tool_orchestrator", ServiceType.STAGE, metadata={"stage": "tools"})
    registry.register_service("response_generator", ServiceType.STAGE, metadata={"stage": "response"})
    registry.register_service("formatter", ServiceType.STAGE, metadata={"stage": "format"})
    
    # Health check all
    for name in registry.services:
        await registry.check_health(name)
    
    logger.info("Architecture service registry setup complete")
    return registry