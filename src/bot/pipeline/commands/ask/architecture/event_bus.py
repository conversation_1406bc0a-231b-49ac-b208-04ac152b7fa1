"""
Event Bus for ASK Pipeline Architecture - Simplified Refactored Version

Lightweight event bus integrated with pipeline_engine for component communication.
Merged with core: uses pipeline_engine for event handling, simple subscribers.
Deleted overkill like full queue, persistence, replay - use core logging/DB for history.
Focus on bot: publish pipeline events (intent, tool, response), subscribe stages.
No CQRS/sourcing; simple pub-sub for orchestration.
"""
import asyncio
from typing import Dict, Any, Optional, List, Callable
from enum import Enum
from dataclasses import dataclass, field
from datetime import datetime
import uuid

from src.core.pipeline_engine import PipelineEngine  # Core integration
from src.shared.error_handling.logging import get_logger
from src.bot.core.error_handler import log_and_notify_error

logger = get_logger(__name__)


class EventType(str, Enum):
    """Pipeline-specific events."""
    QUERY_RECEIVED = "query_received"
    INTENT_DETECTED = "intent_detected"
    TOOL_EXECUTED = "tool_executed"
    RESPONSE_GENERATED = "response_generated"
    ERROR_OCCURRED = "error_occurred"
    PIPELINE_COMPLETED = "pipeline_completed"


@dataclass
class Event:
    """Simple event structure."""
    event_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    event_type: EventType
    timestamp: datetime = field(default_factory=datetime.utcnow)
    correlation_id: Optional[str] = None
    data: Dict[str, Any] = field(default_factory=dict)
    source: str = "pipeline"


class ArchitectureEventBus:
    """
    Simplified event bus for ASK architecture - pub-sub via pipeline_engine.
    Subscribers are stages/tools; events trigger orchestration steps.
    No workers/queue; async publish to engine handlers.
    """
    def __init__(self, pipeline_engine: PipelineEngine):
        self.engine = pipeline_engine
        self.subscribers: Dict[EventType, List[Callable]] = {}
        logger.info("Architecture event bus initialized")

    async def publish(self, event: Event):
        """Publish event to subscribers and engine."""
        logger.debug(f"Publishing {event.event_type.value}", extra={'correlation_id': event.correlation_id})
        
        # Notify subscribers
        for subscriber in self.subscribers.get(event.event_type, []):
            try:
                if asyncio.iscoroutinefunction(subscriber):
                    await subscriber(event)
                else:
                    subscriber(event)
            except Exception as e:
                log_and_notify_error(e, f"Event subscriber failed: {event.event_type}", event.correlation_id, logger)
        
        # Route through engine for orchestration
        await self.engine.handle_architecture_event(event)

    def subscribe(self, event_type: EventType, handler: Callable):
        """Subscribe handler to event type."""
        if event_type not in self.subscribers:
            self.subscribers[event_type] = []
        self.subscribers[event_type].append(handler)
        logger.debug(f"Subscribed to {event_type.value}")

    def unsubscribe(self, event_type: EventType, handler: Callable):
        """Unsubscribe handler."""
        if event_type in self.subscribers and handler in self.subscribers[event_type]:
            self.subscribers[event_type].remove(handler)
            logger.debug(f"Unsubscribed from {event_type.value}")

    async def publish_query_received(self, query: str, correlation_id: str) -> str:
        """Convenience: publish query received."""
        event = Event(event_type=EventType.QUERY_RECEIVED, correlation_id=correlation_id, data={"query": query})
        await self.publish(event)
        return event.event_id

    async def publish_intent_detected(self, intent: str, confidence: float, correlation_id: str):
        """Publish intent detection."""
        event = Event(
            event_type=EventType.INTENT_DETECTED,
            correlation_id=correlation_id,
            data={"intent": intent, "confidence": confidence}
        )
        await self.publish(event)

    async def publish_tool_executed(self, tool_name: str, success: bool, correlation_id: str):
        """Publish tool execution."""
        event = Event(
            event_type=EventType.TOOL_EXECUTED,
            correlation_id=correlation_id,
            data={"tool": tool_name, "success": success}
        )
        await self.publish(event)

    async def publish_response_generated(self, response: str, correlation_id: str):
        """Publish response generation."""
        event = Event(
            event_type=EventType.RESPONSE_GENERATED,
            correlation_id=correlation_id,
            data={"response": response[:100]}  # Truncate for log
        )
        await self.publish(event)

    async def publish_error(self, error: str, correlation_id: str):
        """Publish error event."""
        event = Event(
            event_type=EventType.ERROR_OCCURRED,
            correlation_id=correlation_id,
            data={"error": error}
        )
        await self.publish(event)

    async def publish_pipeline_completed(self, success: bool, correlation_id: str):
        """Publish pipeline completion."""
        event = Event(
            event_type=EventType.PIPELINE_COMPLETED,
            correlation_id=correlation_id,
            data={"success": success}
        )
        await self.publish(event)


# Global bus - init with engine
_event_bus: Optional[ArchitectureEventBus] = None


def get_architecture_event_bus(pipeline_engine: PipelineEngine) -> ArchitectureEventBus:
    global _event_bus
    if _event_bus is None:
        _event_bus = ArchitectureEventBus(pipeline_engine)
    return _event_bus


async def setup_architecture_event_bus(pipeline_engine):
    """Setup event bus."""
    bus = get_architecture_event_bus(pipeline_engine)
    # Example subscribers for stages
    from .stages.intent_detector import handle_intent_event  # Assume exists
    bus.subscribe(EventType.QUERY_RECEIVED, handle_intent_event)
    
    from .stages.simplified_tool_orchestrator import handle_tool_event
    bus.subscribe(EventType.INTENT_DETECTED, handle_tool_event)
    
    logger.info("Architecture event bus setup complete")
    return bus