"""
Consolidated Fallback Strategy for ASK Pipeline - Refactored Version

Implements basic fallback and recovery: cached (placeholder), static responses, retry backoff.
Integrated with core logging; logs fallbacks to AuditLogModel.
Simplified: no advanced AI/tools fallbacks, focus on static/retry.
"""
import asyncio
import time
from typing import Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum

from src.shared.error_handling.logging import get_logger
# Optional DB logging; provide no-op fallbacks if unavailable
try:
    from src.database.unified_db import get_db_session, AuditLogModel  # Core DB
except Exception:
    def get_db_session():
        class _DummySession:
            async def __aenter__(self):
                return self
            async def __aexit__(self, exc_type, exc, tb):
                pass
            def add(self, obj):
                pass
            async def commit(self):
                pass
        return _DummySession()
    class AuditLogModel:  # type: ignore
        def __init__(self, **kwargs):
            self.__dict__.update(kwargs)
from .error_manager import ErrorType, ErrorSeverity  # Local

logger = get_logger(__name__)


class FallbackType(Enum):
    """Types of fallback strategies"""
    CACHED_RESPONSE = "cached_response"
    STATIC_RESPONSE = "static_response"
    RETRY_WITH_BACKOFF = "retry_with_backoff"
    EMERGENCY_RESPONSE = "emergency_response"


@dataclass
class FallbackResult:
    """Result of fallback execution"""
    success: bool
    response: str
    fallback_type: str
    execution_time: float
    confidence: float
    data: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.data is None:
            self.data = {}


class SimpleFallbackStrategy:
    """
    Simplified fallback manager.
    Features: Basic chains, static responses, exponential backoff retry.
    """
    def __init__(self):
        # Simplified fallback chains
        self.fallback_chains = {
            ErrorType.AI_SERVICE_UNAVAILABLE: [
                FallbackType.CACHED_RESPONSE,
                FallbackType.STATIC_RESPONSE,
                FallbackType.EMERGENCY_RESPONSE
            ],
            ErrorType.AI_RATE_LIMITED: [
                FallbackType.RETRY_WITH_BACKOFF,
                FallbackType.STATIC_RESPONSE
            ],
            ErrorType.AI_TIMEOUT: [
                FallbackType.CACHED_RESPONSE,
                FallbackType.STATIC_RESPONSE
            ],
            ErrorType.TOOL_EXECUTION_FAILED: [
                FallbackType.STATIC_RESPONSE
            ],
            ErrorType.NETWORK_ERROR: [
                FallbackType.RETRY_WITH_BACKOFF,
                FallbackType.STATIC_RESPONSE
            ],
            ErrorType.CACHE_ERROR: [
                FallbackType.STATIC_RESPONSE
            ],
            ErrorType.VALIDATION_ERROR: [
                FallbackType.STATIC_RESPONSE
            ],
            ErrorType.UNKNOWN_ERROR: [
                FallbackType.STATIC_RESPONSE,
                FallbackType.EMERGENCY_RESPONSE
            ]
        }
        
        # Basic static responses
        self.static_responses = {
            'general_help': "I can help with market analysis and stock info. Try asking about a specific stock.",
            'market_data': "Unable to fetch real-time data now. Ask about general market concepts.",
            'ai_unavailable': "AI services unavailable. Providing basic information.",
            'tool_failure': "Tools unavailable. General assistance only.",
            'rate_limited': "Rate limited. Please wait and try again.",
            'timeout': "Request timed out. Try a simpler query.",
            'validation': "Didn't understand. Rephrase, e.g., ask about 'AAPL'.",
            'emergency': "Technical difficulties. Try again later."
        }
        
        # Retry config
        self.retry_config = {
            'max_retries': 3,
            'base_delay': 1.0,
            'max_delay': 10.0,
            'exponential_base': 2.0
        }

        # Store callable to acquire a session lazily
        self.db_session = get_db_session
        logger.info("Simple fallback strategy initialized")

    async def execute_fallback(
        self,
        error_type: ErrorType,
        severity: ErrorSeverity,
        query: str,
        correlation_id: str,
        context: Optional[Dict[str, Any]] = None
    ) -> FallbackResult:
        """Execute fallback chain."""
        start_time = time.time()
        context = context or {}
        
        try:
            fallback_chain = self.fallback_chains.get(error_type, [FallbackType.EMERGENCY_RESPONSE])
            
            for fallback_type in fallback_chain:
                try:
                    result = await self._execute_single_fallback(
                        fallback_type, query, correlation_id, context
                    )
                    
                    if result.success:
                        result.execution_time = time.time() - start_time
                        await self._log_fallback_to_db(fallback_type, result, correlation_id, context)
                        logger.info(f"Fallback successful: {fallback_type.value}", extra={'correlation_id': correlation_id})
                        return result
                        
                except Exception as e:
                    logger.warning(f"Fallback {fallback_type.value} failed: {e}", extra={'correlation_id': correlation_id})
                    continue
            
            # Emergency fallback
            return FallbackResult(
                success=True,
                response=self.static_responses['emergency'],
                fallback_type=FallbackType.EMERGENCY_RESPONSE.value,
                execution_time=time.time() - start_time,
                confidence=0.1
            )
            
        except Exception as e:
            logger.error(f"Fallback execution error: {e}", extra={'correlation_id': correlation_id})
            return FallbackResult(
                success=True,
                response="Unexpected issue. Please try again.",
                fallback_type="error_fallback",
                execution_time=time.time() - start_time,
                confidence=0.1
            )

    async def _execute_single_fallback(
        self,
        fallback_type: FallbackType,
        query: str,
        correlation_id: str,
        context: Dict[str, Any]
    ) -> FallbackResult:
        """Execute single fallback."""
        start_time = time.time()
        
        if fallback_type == FallbackType.CACHED_RESPONSE:
            # Placeholder: assume no cache, fail
            return FallbackResult(
                success=False,
                response="No cached response available",
                fallback_type=fallback_type.value,
                execution_time=time.time() - start_time,
                confidence=0.0
            )
        
        elif fallback_type == FallbackType.STATIC_RESPONSE:
            return await self._get_static_response(query, correlation_id, context)
        
        elif fallback_type == FallbackType.RETRY_WITH_BACKOFF:
            return await self._retry_with_backoff(query, correlation_id, context)
        
        elif fallback_type == FallbackType.EMERGENCY_RESPONSE:
            return FallbackResult(
                success=True,
                response=self.static_responses['emergency'],
                fallback_type=fallback_type.value,
                execution_time=time.time() - start_time,
                confidence=0.1
            )
        
        else:
            return FallbackResult(
                success=False,
                response="Unknown fallback",
                fallback_type=fallback_type.value,
                execution_time=time.time() - start_time,
                confidence=0.0
            )

    async def _get_static_response(
        self, 
        query: str, 
        correlation_id: str, 
        context: Dict[str, Any]
    ) -> FallbackResult:
        """Get static response based on query."""
        query_lower = query.lower()
        
        if any(word in query_lower for word in ['price', 'quote']):
            response = self.static_responses['market_data']
        elif any(word in query_lower for word in ['help', 'what']):
            response = self.static_responses['general_help']
        elif any(word in query_lower for word in ['analysis']):
            response = self.static_responses['tool_failure']
        else:
            response = self.static_responses['general_help']
        
        return FallbackResult(
            success=True,
            response=response,
            fallback_type=FallbackType.STATIC_RESPONSE.value,
            execution_time=0.05,
            confidence=0.6
        )

    async def _retry_with_backoff(
        self, 
        query: str, 
        correlation_id: str, 
        context: Dict[str, Any]
    ) -> FallbackResult:
        """Retry with backoff."""
        retry_count = context.get('retry_count', 0)
        
        if retry_count >= self.retry_config['max_retries']:
            return FallbackResult(
                success=False,
                response="Max retries exceeded",
                fallback_type=FallbackType.RETRY_WITH_BACKOFF.value,
                execution_time=0.1,
                confidence=0.0
            )
        
        delay = min(
            self.retry_config['base_delay'] * (self.retry_config['exponential_base'] ** retry_count),
            self.retry_config['max_delay']
        )
        
        logger.info(f"Retrying with backoff: {delay}s (attempt {retry_count + 1})", extra={'correlation_id': correlation_id})
        await asyncio.sleep(delay)
        
        # Placeholder: indicate retry
        return FallbackResult(
            success=True,
            response=f"Retrying request (attempt {retry_count + 1})...",
            fallback_type=FallbackType.RETRY_WITH_BACKOFF.value,
            execution_time=delay,
            confidence=0.7,
            data={'retry_count': retry_count + 1, 'delay': delay}
        )

    async def _log_fallback_to_db(self, fallback_type: FallbackType, result: FallbackResult, correlation_id: str, context: Dict[str, Any]):
        """Log fallback to DB."""
        async with self.db_session() as session:
            log = AuditLogModel(
                stage="fallback",
                action=f"fallback_{fallback_type.value}",
                user_id=context.get('user_id'),
                details=f"Fallback result: success={result.success}, confidence={result.confidence}",
                status="info",
                metadata=str({
                    'correlation_id': correlation_id,
                    'fallback_type': fallback_type.value,
                    'response_length': len(result.response)
                }),
                timestamp=time.time()
            )
            session.add(log)
            await session.commit()


# Global singleton
_strategy: Optional[SimpleFallbackStrategy] = None


def get_fallback_strategy() -> SimpleFallbackStrategy:
    global _strategy
    if _strategy is None:
        _strategy = SimpleFallbackStrategy()
    return _strategy