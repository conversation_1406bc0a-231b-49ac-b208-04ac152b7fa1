"""
Consolidated Error Manager for ASK Pipeline - Refactored Version

Centralizes error classification, user messaging, and basic circuit breaker.
Integrated with core logging; simplified stats (no DB for stats).
Uses AuditLogModel for error persistence.
"""
import time
from typing import Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum

from src.shared.error_handling.logging import get_logger
# Optional DB logging; provide no-op fallbacks if unavailable
try:
    from src.database.unified_db import get_db_session, AuditLogModel  # Core DB for error logs
except Exception:
    def get_db_session():
        class _DummySession:
            async def __aenter__(self):
                return self
            async def __aexit__(self, exc_type, exc, tb):
                pass
            def add(self, obj):
                pass
            async def commit(self):
                pass
        return _DummySession()
    class AuditLogModel:  # type: ignore
        def __init__(self, **kwargs):
            self.__dict__.update(kwargs)

logger = get_logger(__name__)


class ErrorType(Enum):
    """Types of errors that can occur in the pipeline"""
    AI_SERVICE_UNAVAILABLE = "ai_service_unavailable"
    AI_RATE_LIMITED = "ai_rate_limited"
    AI_TIMEOUT = "ai_timeout"
    TOOL_EXECUTION_FAILED = "tool_execution_failed"
    NETWORK_ERROR = "network_error"
    CACHE_ERROR = "cache_error"
    VALIDATION_ERROR = "validation_error"
    UNKNOWN_ERROR = "unknown_error"


class ErrorSeverity(Enum):
    """Severity levels for errors"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class ErrorContext:
    """Context information for error handling"""
    stage: str
    user_id: Optional[str] = None
    query: str = ""
    retry_count: int = 0
    execution_time: float = 0.0
    additional_data: Optional[Dict[str, Any]] = None


@dataclass
class ErrorResult:
    """Result of error handling"""
    success: bool
    response: str
    error_type: str
    severity: ErrorSeverity
    execution_time: float
    correlation_id: str = ""
    retry_recommended: bool = False
    fallback_used: Optional[str] = None


class SimpleErrorManager:
    """
    Simplified error manager for ASK pipeline.
    Features: Basic classification, user messages, simple circuit breaker, DB logging.
    """
    def __init__(self):
        self.error_stats = {
            'total_errors': 0,
            'errors_by_type': {},
            'errors_by_severity': {},
        }
        
        # Simple circuit breaker (in-memory, per type)
        self.circuit_breakers = {}  # {key: {'failures': int, 'state': str}}
        self.cb_config = {
            'failure_threshold': 3,
            'recovery_timeout': 60,  # 1 minute
        }
        
        # Basic error patterns
        self.error_patterns = {
            'rate_limit': ['rate limit', 'quota exceeded'],
            'timeout': ['timeout', 'timed out'],
            'unavailable': ['service unavailable', 'connection refused'],
            'network': ['network error', 'connection error'],
            'validation': ['validation error', 'invalid input'],
        }
        
        # Simplified user messages (reduce options)
        self.error_messages = {
            ErrorType.AI_SERVICE_UNAVAILABLE: "I'm having trouble with my analysis tools. Trying a different approach.",
            ErrorType.AI_RATE_LIMITED: "Hit rate limit. Retrying shortly.",
            ErrorType.AI_TIMEOUT: "Request timed out. Simplifying query.",
            ErrorType.TOOL_EXECUTION_FAILED: "Tool failed. Using basic analysis.",
            ErrorType.NETWORK_ERROR: "Network connectivity issue. Retrying connection.",
            ErrorType.CACHE_ERROR: "Cache issue. Proceeding without cache.",
            ErrorType.VALIDATION_ERROR: "I don't understand that request. Please rephrase your question.",
            ErrorType.UNKNOWN_ERROR: "Unexpected error. Please try again.",
        }

        # Store callable to acquire a session lazily
        self.db_session = get_db_session
        logger.info("Simple error manager initialized")

    async def handle_error(
        self,
        exception: Exception,
        context: ErrorContext,
        correlation_id: str = ""
    ) -> ErrorResult:
        """Handle error with classification and logging."""
        start_time = time.time()
        
        try:
            self.error_stats['total_errors'] += 1
            
            error_type = self._classify_error(exception, context)
            severity = self._determine_severity(error_type, context)
            
            # Update stats
            self.error_stats['errors_by_type'][error_type.value] = self.error_stats['errors_by_type'].get(error_type.value, 0) + 1
            self.error_stats['errors_by_severity'][severity.value] = self.error_stats['errors_by_severity'].get(severity.value, 0) + 1
            
            # Simple circuit breaker
            cb_key = f"{context.stage}_{error_type.value}"
            if self._should_circuit_break(cb_key, severity):
                return await self._handle_circuit_break(cb_key, context, correlation_id)
            
            # User message
            user_message = self.error_messages.get(error_type, self.error_messages[ErrorType.UNKNOWN_ERROR])
            if context.retry_count > 0:
                user_message += f" (Retry {context.retry_count})"
            
            retry_recommended = self._should_retry(error_type, severity, context)
            
            execution_time = time.time() - start_time
            result = ErrorResult(
                success=False,
                response=user_message,
                error_type=error_type.value,
                severity=severity,
                execution_time=execution_time,
                correlation_id=correlation_id,
                retry_recommended=retry_recommended
            )
            
            # Log to DB
            await self._log_error_to_db(exception, error_type, severity, context, correlation_id)
            
            # Console log
            self._log_error_console(exception, error_type, severity, context, correlation_id)
            
            return result
            
        except Exception as e:
            logger.error(f"Error in error handler: {e}", extra={'correlation_id': correlation_id})
            return ErrorResult(
                success=False,
                response="Unexpected technical issue. Please try again.",
                error_type=ErrorType.UNKNOWN_ERROR.value,
                severity=ErrorSeverity.HIGH,
                execution_time=time.time() - start_time,
                correlation_id=correlation_id,
                retry_recommended=True
            )

    def _classify_error(self, exception: Exception, context: ErrorContext) -> ErrorType:
        """Classify error based on message and type."""
        exc_str = str(exception).lower()
        exc_type = type(exception).__name__.lower()
        
        for pattern_type, patterns in self.error_patterns.items():
            if any(p in exc_str for p in patterns):
                if pattern_type == 'rate_limit': return ErrorType.AI_RATE_LIMITED
                elif pattern_type == 'timeout': return ErrorType.AI_TIMEOUT
                elif pattern_type == 'unavailable': return ErrorType.AI_SERVICE_UNAVAILABLE
                elif pattern_type == 'network': return ErrorType.NETWORK_ERROR
                elif pattern_type == 'validation': return ErrorType.VALIDATION_ERROR
        
        if 'timeout' in exc_type: return ErrorType.AI_TIMEOUT
        if 'connection' in exc_type or 'network' in exc_type: return ErrorType.NETWORK_ERROR
        if 'validation' in exc_type: return ErrorType.VALIDATION_ERROR
        if 'cache' in exc_type: return ErrorType.CACHE_ERROR
        if context.stage == 'tools': return ErrorType.TOOL_EXECUTION_FAILED
        if context.stage in ['intent', 'response']: return ErrorType.AI_SERVICE_UNAVAILABLE
        
        return ErrorType.UNKNOWN_ERROR

    def _determine_severity(self, error_type: ErrorType, context: ErrorContext) -> ErrorSeverity:
        """Determine severity."""
        if context.retry_count >= 3: return ErrorSeverity.CRITICAL
        if context.retry_count >= 2: return ErrorSeverity.HIGH
        
        base_severity = {
            ErrorType.AI_SERVICE_UNAVAILABLE: ErrorSeverity.HIGH,
            ErrorType.AI_RATE_LIMITED: ErrorSeverity.MEDIUM,
            ErrorType.AI_TIMEOUT: ErrorSeverity.MEDIUM,
            ErrorType.TOOL_EXECUTION_FAILED: ErrorSeverity.LOW,
            ErrorType.NETWORK_ERROR: ErrorSeverity.HIGH,
            ErrorType.CACHE_ERROR: ErrorSeverity.LOW,
            ErrorType.VALIDATION_ERROR: ErrorSeverity.LOW,
            ErrorType.UNKNOWN_ERROR: ErrorSeverity.MEDIUM
        }.get(error_type, ErrorSeverity.MEDIUM)
        
        if context.execution_time > 30:
            if base_severity == ErrorSeverity.LOW: return ErrorSeverity.MEDIUM
            if base_severity == ErrorSeverity.MEDIUM: return ErrorSeverity.HIGH
        
        return base_severity

    def _should_retry(self, error_type: ErrorType, severity: ErrorSeverity, context: ErrorContext) -> bool:
        """Determine if retry recommended."""
        if context.retry_count >= 3 or severity == ErrorSeverity.CRITICAL: return False
        if error_type == ErrorType.VALIDATION_ERROR: return False
        
        retry_eligible = {
            ErrorType.AI_TIMEOUT, ErrorType.NETWORK_ERROR, ErrorType.AI_RATE_LIMITED,
            ErrorType.TOOL_EXECUTION_FAILED, ErrorType.AI_SERVICE_UNAVAILABLE
        }
        return error_type in retry_eligible

    def _should_circuit_break(self, cb_key: str, severity: ErrorSeverity) -> bool:
        """Simple circuit breaker check."""
        if cb_key not in self.circuit_breakers:
            self.circuit_breakers[cb_key] = {'failures': 0, 'last_failure': time.time(), 'state': 'closed'}
        
        breaker = self.circuit_breakers[cb_key]
        now = time.time()
        
        if severity in [ErrorSeverity.HIGH, ErrorSeverity.CRITICAL]:
            breaker['failures'] += 1
            breaker['last_failure'] = now
        
        if breaker['state'] == 'closed' and breaker['failures'] >= self.cb_config['failure_threshold']:
            breaker['state'] = 'open'
            logger.warning(f"Circuit breaker tripped for {cb_key}")
            return True
        
        if breaker['state'] == 'open' and now - breaker['last_failure'] > self.cb_config['recovery_timeout']:
            breaker['state'] = 'closed'
            breaker['failures'] = 0
            logger.info(f"Circuit breaker recovered for {cb_key}")
        
        return breaker['state'] == 'open'

    async def _handle_circuit_break(self, cb_key: str, context: ErrorContext, correlation_id: str) -> ErrorResult:
        """Handle circuit breaker."""
        logger.warning(f"Circuit breaker active for {cb_key}", extra={'correlation_id': correlation_id})
        return ErrorResult(
            success=False,
            response="Service temporarily limited due to errors. Try again soon.",
            error_type="circuit_breaker",
            severity=ErrorSeverity.HIGH,
            execution_time=0.0,
            correlation_id=correlation_id,
            retry_recommended=False,
            fallback_used="circuit_breaker"
        )

    async def _log_error_to_db(self, exception: Exception, error_type: ErrorType, severity: ErrorSeverity, context: ErrorContext, correlation_id: str):
        """Log error to DB using AuditLogModel."""
        async with self.db_session() as session:
            log = AuditLogModel(
                stage=context.stage,
                action="error_log",
                user_id=context.user_id,
                details=f"Error: {str(exception)} | Type: {error_type.value} | Severity: {severity.value}",
                status="error",
                metadata=str({
                    'correlation_id': correlation_id,
                    'retry_count': context.retry_count,
                    'execution_time': context.execution_time
                }),
                timestamp=time.time()
            )
            session.add(log)
            await session.commit()

    def _log_error_console(self, exception: Exception, error_type: ErrorType, severity: ErrorSeverity, context: ErrorContext, correlation_id: str):
        """Console log based on severity."""
        log_data = {
            'correlation_id': correlation_id,
            'error_type': error_type.value,
            'severity': severity.value,
            'stage': context.stage,
            'retry_count': context.retry_count
        }
        
        if severity == ErrorSeverity.CRITICAL:
            logger.critical(f"Critical error: {exception}", extra=log_data)
        elif severity == ErrorSeverity.HIGH:
            logger.error(f"High error: {exception}", extra=log_data)
        elif severity == ErrorSeverity.MEDIUM:
            logger.warning(f"Medium error: {exception}", extra=log_data)
        else:
            logger.info(f"Low error: {exception}", extra=log_data)

    def get_error_stats(self) -> Dict[str, Any]:
        """Get error statistics."""
        return self.error_stats.copy()


# Global singleton
_manager: Optional[SimpleErrorManager] = None


def get_error_manager() -> SimpleErrorManager:
    global _manager
    if _manager is None:
        _manager = SimpleErrorManager()
    return _manager