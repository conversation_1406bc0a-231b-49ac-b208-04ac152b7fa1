"""
Consolidated Error Handling System for ASK Pipeline

This module provides unified error handling with two focused components:
- ErrorManager: Centralized error classification, handling, and circuit breaking
- FallbackStrategy: Intelligent fallback and recovery strategies

This replaces the previous multi-module error handling architecture with
a cleaner, more maintainable approach.
"""

from .error_manager import (
    SimpleErrorManager as ErrorManager,
    ErrorType,
    ErrorSeverity,
    ErrorContext,
    ErrorResult
)

from .fallback_strategy import (
    SimpleFallbackStrategy as FallbackStrategy,
    FallbackType,
    FallbackResult
)

__all__ = [
    # Error Management
    'ErrorManager',
    'ErrorType',
    'ErrorSeverity', 
    'ErrorContext',
    'ErrorResult',
    
    # Fallback Strategies
    'FallbackStrategy',
    'FallbackType',
    'FallbackResult'
]