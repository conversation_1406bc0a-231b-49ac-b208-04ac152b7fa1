"""
Resource Optimizer for ASK Pipeline - Simplified Refactored Version

Basic resource monitoring using psutil; logs metrics.
No auto-scaling; just track CPU/memory for optimization.
Integrated with core logging.
"""
import asyncio
import psutil
from typing import Dict, Any
from datetime import datetime
from enum import Enum

from src.shared.error_handling.logging import get_logger

logger = get_logger(__name__)


class ResourceType(str, Enum):
    CPU = "cpu"
    MEMORY = "memory"


class SimpleResourceOptimizer:
    """
    Simple resource optimizer - monitors CPU/memory, logs to DB.
    No auto-scaling; manual review via logs.
    """
    def __init__(self):
        self.db_session = get_db_session()

    async def log_metric(self, resource_type: ResourceType, value: float, metadata: Dict[str, Any] = None):
        """Log resource metric."""
        async with self.db_session() as session:
            metric = ResourceMetricModel(
                resource_type=resource_type.value,
                value=value,
                timestamp=datetime.utcnow(),
                metadata=str(metadata or {})
            )
            session.add(metric)
            await session.commit()
        
        logger.debug(f"Resource metric: {resource_type.value} = {value}", extra=metadata or {})

    async def get_average_usage(self, resource_type: ResourceType, hours: int = 24) -> float:
        """Get average usage."""
        async with self.db_session() as session:
            from_date = datetime.utcnow() - timedelta(hours=hours)
            result = await session.execute(
                "SELECT AVG(value) FROM resource_metrics WHERE resource_type = :type AND timestamp > :date",
                {"type": resource_type.value, "date": from_date}
            )
            return result.scalar() or 0.0

    async def monitor(self):
        """Monitor loop - run periodically."""
        while True:
            cpu = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory().percent
            
            await self.log_metric(ResourceType.CPU, cpu, {"source": "psutil"})
            await self.log_metric(ResourceType.MEMORY, memory, {"source": "psutil"})
            
            logger.info(f"Resources: CPU {cpu}%, Memory {memory}%")
            await asyncio.sleep(60)  # Every minute


# Global
_optimizer = None


def get_resource_optimizer() -> SimpleResourceOptimizer:
    global _optimizer
    if _optimizer is None:
        _optimizer = SimpleResourceOptimizer()
    return _optimizer


async def setup_resource_optimizer():
    """Setup optimizer."""
    optimizer = get_resource_optimizer()
    asyncio.create_task(optimizer.monitor())
    logger.info("Resource optimizer initialized")