"""
Cost Tracking and Resource Optimization for ASK Pipeline

Provides comprehensive cost monitoring and resource optimization:
- Track AI API costs, data provider costs, and infrastructure costs
- Implement cost optimization strategies
- Add cost alerting and budget management
- Create cost reporting and forecasting
- Integrate with billing and accounting systems
"""

import asyncio
import logging
import time
import uuid
from typing import Dict, Any, Optional, List, Union, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta, timezone
from enum import Enum
from collections import defaultdict
import json
import os
from pathlib import Path

from src.shared.error_handling.logging import get_logger
from src.shared.cache.cache_service import cache_service

logger = get_logger(__name__)

class CostCategory(Enum):
    """Cost categories"""
    AI_API = "ai_api"
    DATA_PROVIDER = "data_provider"
    INFRASTRUCTURE = "infrastructure"
    DATABASE = "database"
    STORAGE = "storage"
    NETWORK = "network"
    COMPUTE = "compute"

class CostType(Enum):
    """Cost types"""
    FIXED = "fixed"
    VARIABLE = "variable"
    USAGE_BASED = "usage_based"
    SUBSCRIPTION = "subscription"

@dataclass
class CostRecord:
    """Individual cost record"""
    record_id: str
    category: CostCategory
    cost_type: CostType
    description: str
    amount: float  # Cost in USD
    timestamp: datetime
    currency: str = "USD"
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    resource_id: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    tags: List[str] = field(default_factory=list)

@dataclass
class CostThreshold:
    """Cost threshold configuration"""
    category: CostCategory
    daily_threshold: float
    monthly_threshold: float
    alert_threshold: float
    enabled: bool = True

@dataclass
class CostAlert:
    """Cost alert"""
    alert_id: str
    category: CostCategory
    threshold_type: str  # daily, monthly
    current_cost: float
    threshold: float
    alert_level: str  # warning, critical
    timestamp: datetime
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class CostReport:
    """Cost report"""
    report_id: str
    period_start: datetime
    period_end: datetime
    total_cost: float
    costs_by_category: Dict[str, float] = field(default_factory=dict)
    costs_by_type: Dict[str, float] = field(default_factory=dict)
    alerts: List[CostAlert] = field(default_factory=list)
    budget_remaining: float = 0.0
    cost_efficiency_score: float = 0.0
    recommendations: List[str] = field(default_factory=list)

class CostTracker:
    """Cost tracking and optimization system"""

    def __init__(self):
        self.cost_records: List[CostRecord] = []
        self.thresholds: Dict[CostCategory, CostThreshold] = {}
        self.alerts: List[CostAlert] = []
        self.budgets: Dict[str, float] = {}  # budget_name: budget_amount
        self.current_period_costs: Dict[CostCategory, float] = defaultdict(float)
        self.daily_costs: Dict[CostCategory, float] = defaultdict(float)
        self.monthly_costs: Dict[CostCategory, float] = defaultdict(float)

        # Initialize default thresholds
        self._initialize_thresholds()

        # Flag to track if monitoring has started
        self._monitoring_started = False

        # Note: Cost monitoring will be started when the event loop is available

    def ensure_monitoring_started(self):
        """Ensure cost monitoring is started if event loop is available"""
        if not self._monitoring_started:
            try:
                loop = asyncio.get_running_loop()
                if loop and not loop.is_closed():
                    asyncio.create_task(self.start_cost_monitoring())
                    self._monitoring_started = True
            except RuntimeError:
                # No event loop running, monitoring will start later
                pass

    def _initialize_thresholds(self):
        """Initialize default cost thresholds"""
        thresholds = [
            CostThreshold(
                category=CostCategory.AI_API,
                daily_threshold=50.0,
                monthly_threshold=1000.0,
                alert_threshold=0.8  # 80% of budget
            ),
            CostThreshold(
                category=CostCategory.DATA_PROVIDER,
                daily_threshold=20.0,
                monthly_threshold=500.0,
                alert_threshold=0.8
            ),
            CostThreshold(
                category=CostCategory.INFRASTRUCTURE,
                daily_threshold=30.0,
                monthly_threshold=800.0,
                alert_threshold=0.9
            )
        ]

        for threshold in thresholds:
            self.thresholds[threshold.category] = threshold

    async def start_cost_monitoring(self):
        """Start cost monitoring"""
        if hasattr(self, '_monitoring_task'):
            return

        self._monitoring_task = asyncio.create_task(self._cost_monitoring_loop())
        logger.info("Cost monitoring started")

    async def stop_cost_monitoring(self):
        """Stop cost monitoring"""
        if hasattr(self, '_monitoring_task'):
            self._monitoring_task.cancel()
            self._monitoring_task = None
        logger.info("Cost monitoring stopped")

    async def _cost_monitoring_loop(self):
        """Cost monitoring loop"""
        while True:
            try:
                await self._check_cost_thresholds()
                await self._aggregate_costs()
                await asyncio.sleep(300)  # Check every 5 minutes
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Cost monitoring error: {e}")
                await asyncio.sleep(60)

    async def _check_cost_thresholds(self):
        """Check if costs exceed thresholds"""
        now = datetime.utcnow()

        # Check daily thresholds
        for category, threshold in self.thresholds.items():
            if threshold.enabled:
                daily_cost = self.daily_costs[category]
                if daily_cost > threshold.daily_threshold:
                    await self._trigger_cost_alert(
                        category=category,
                        threshold_type="daily",
                        current_cost=daily_cost,
                        threshold=threshold.daily_threshold,
                        alert_level="critical"
                    )

                monthly_cost = self.monthly_costs[category]
                if monthly_cost > threshold.monthly_threshold:
                    await self._trigger_cost_alert(
                        category=category,
                        threshold_type="monthly",
                        current_cost=monthly_cost,
                        threshold=threshold.monthly_threshold,
                        alert_level="critical"
                    )

    async def _aggregate_costs(self):
        """Aggregate costs by period"""
        now = datetime.utcnow()
        today = now.replace(hour=0, minute=0, second=0, microsecond=0)
        month_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)

        # Reset daily costs
        self.daily_costs = defaultdict(float)
        self.monthly_costs = defaultdict(float)

        # Aggregate from records
        for record in self.cost_records:
            if record.timestamp.date() == today.date():
                self.daily_costs[record.category] += record.amount

            if record.timestamp >= month_start:
                self.monthly_costs[record.category] += record.amount

    async def _trigger_cost_alert(self, category: CostCategory, threshold_type: str,
                                 current_cost: float, threshold: float, alert_level: str):
        """Trigger cost alert"""
        alert_id = str(uuid.uuid4())

        alert = CostAlert(
            alert_id=alert_id,
            category=category,
            threshold_type=threshold_type,
            current_cost=current_cost,
            threshold=threshold,
            alert_level=alert_level,
            timestamp=datetime.utcnow(),
            metadata={
                "exceeded_by": current_cost - threshold,
                "percentage_over": ((current_cost - threshold) / threshold) * 100
            }
        )

        self.alerts.append(alert)

        # Log alert
        logger.warning(f"COST ALERT: {category.value} {threshold_type} cost exceeded threshold. Current: ${current_cost:.2f}, Threshold: ${threshold:.2f}")

        # In production, this would trigger external alerts (Slack, email, etc.)

    def record_cost(self, category: CostCategory, description: str, amount: float,
                   cost_type: CostType = CostType.USAGE_BASED, user_id: Optional[str] = None,
                   session_id: Optional[str] = None, resource_id: Optional[str] = None,
                   metadata: Dict[str, Any] = None, tags: List[str] = None) -> str:
        """Record a cost event"""
        # Ensure monitoring is started
        self.ensure_monitoring_started()

        record_id = str(uuid.uuid4())

        record = CostRecord(
            record_id=record_id,
            category=category,
            cost_type=cost_type,
            description=description,
            amount=amount,
            timestamp=datetime.utcnow(),
            user_id=user_id,
            session_id=session_id,
            resource_id=resource_id,
            metadata=metadata or {},
            tags=tags or []
        )

        self.cost_records.append(record)

        # Update current period costs
        self.current_period_costs[category] += amount

        # Log the cost
        logger.info(f"Cost recorded: {category.value} - ${amount:.4f} - {description}")

        return record_id

    def record_ai_cost(self, provider: str, model: str, tokens_used: int, 
                      input_tokens: int, output_tokens: int, user_id: Optional[str] = None) -> str:
        """Record AI API cost"""
        # Calculate cost based on provider and token usage
        cost_per_1k_input = {
            'openai': 0.0005,  # $0.50 per 1M tokens
            'anthropic': 0.0008,
            'google': 0.0002,
            'default': 0.0003
        }

        cost_per_1k_output = {
            'openai': 0.0015,
            'anthropic': 0.0025,
            'google': 0.0006,
            'default': 0.0010
        }

        input_cost = (input_tokens / 1000) * cost_per_1k_input.get(provider, cost_per_1k_input['default'])
        output_cost = (output_tokens / 1000) * cost_per_1k_output.get(provider, cost_per_1k_output['default'])
        total_cost = input_cost + output_cost

        description = f"AI API call: {provider}/{model} - {input_tokens} in, {output_tokens} out"

        return self.record_cost(
            category=CostCategory.AI_API,
            description=description,
            amount=total_cost,
            user_id=user_id,
            metadata={
                'provider': provider,
                'model': model,
                'tokens': {
                    'input': input_tokens,
                    'output': output_tokens,
                    'total': input_tokens + output_tokens
                },
                'cost_breakdown': {
                    'input_cost': input_cost,
                    'output_cost': output_cost
                }
            }
        )

    def record_data_provider_cost(self, provider: str, data_type: str, 
                                 request_count: int, user_id: Optional[str] = None) -> str:
        """Record data provider cost"""
        # Cost per request for different providers
        cost_per_request = {
            'alpha_vantage': 0.001,  # $0.001 per request
            'yahoo_finance': 0.0005,
            'polygon': 0.002,
            'default': 0.001
        }

        total_cost = request_count * cost_per_request.get(provider, cost_per_request['default'])

        description = f"Data provider: {provider} - {data_type} ({request_count} requests)"

        return self.record_cost(
            category=CostCategory.DATA_PROVIDER,
            description=description,
            amount=total_cost,
            user_id=user_id,
            metadata={
                'provider': provider,
                'data_type': data_type,
                'request_count': request_count,
                'cost_per_request': cost_per_request.get(provider, 0.001)
            }
        )

    def record_infrastructure_cost(self, resource_type: str, usage: float,
                                  unit_cost: float, user_id: Optional[str] = None) -> str:
        """Record infrastructure cost"""
        total_cost = usage * unit_cost

        description = f"Infrastructure: {resource_type} usage ({usage} units)"

        return self.record_cost(
            category=CostCategory.INFRASTRUCTURE,
            description=description,
            amount=total_cost,
            user_id=user_id,
            metadata={
                'resource_type': resource_type,
                'usage': usage,
                'unit_cost': unit_cost
            }
        )

    def set_budget(self, budget_name: str, amount: float, period: str = "monthly") -> bool:
        """Set budget for cost tracking"""
        self.budgets[budget_name] = {
            'amount': amount,
            'period': period,
            'current_spent': 0.0,
            'start_date': datetime.utcnow()
        }
        logger.info(f"Budget set: {budget_name} - ${amount} {period}")
        return True

    def get_budget_status(self, budget_name: str) -> Optional[Dict[str, Any]]:
        """Get budget status"""
        budget = self.budgets.get(budget_name)
        if not budget:
            return None

        period_end = self._get_period_end(budget['period'], budget['start_date'])
        remaining = budget['amount'] - budget['current_spent']

        return {
            'budget_name': budget_name,
            'amount': budget['amount'],
            'period': budget['period'],
            'current_spent': budget['current_spent'],
            'remaining': remaining,
            'percentage_used': (budget['current_spent'] / budget['amount']) * 100,
            'period_end': period_end,
            'over_budget': remaining < 0
        }

    def _get_period_end(self, period: str, start_date: datetime) -> datetime:
        """Get period end date"""
        if period == "daily":
            return start_date + timedelta(days=1)
        elif period == "monthly":
            return start_date.replace(day=1) + timedelta(days=32)
        elif period == "yearly":
            return start_date.replace(year=start_date.year + 1)
        return start_date + timedelta(days=30)  # Default monthly

    def generate_cost_report(self, period_start: Optional[datetime] = None,
                           period_end: Optional[datetime] = None) -> CostReport:
        """Generate cost report for period"""
        if period_start is None:
            period_start = datetime.utcnow() - timedelta(days=30)
        if period_end is None:
            period_end = datetime.utcnow()

        # Filter records for period
        period_records = [
            record for record in self.cost_records
            if period_start <= record.timestamp <= period_end
        ]

        total_cost = sum(record.amount for record in period_records)

        costs_by_category = defaultdict(float)
        costs_by_type = defaultdict(float)

        for record in period_records:
            costs_by_category[record.category.value] += record.amount
            costs_by_type[record.cost_type.value] += record.amount

        # Calculate cost efficiency score (placeholder - would be more sophisticated)
        efficiency_score = 100.0 - min(50.0, (total_cost / 1000.0) * 10)  # Simple calculation

        report = CostReport(
            report_id=str(uuid.uuid4()),
            period_start=period_start,
            period_end=period_end,
            total_cost=total_cost,
            costs_by_category=dict(costs_by_category),
            costs_by_type=dict(costs_by_type),
            alerts=self.alerts[-10:],  # Last 10 alerts
            budget_remaining=sum(budget['amount'] - budget['current_spent'] for budget in self.budgets.values()),
            cost_efficiency_score=efficiency_score,
            recommendations=self._generate_cost_recommendations(period_records)
        )

        logger.info(f"Generated cost report: ${total_cost:.2f} for period {period_start} to {period_end}")
        return report

    def _generate_cost_recommendations(self, records: List[CostRecord]) -> List[str]:
        """Generate cost optimization recommendations"""
        recommendations = []

        # AI API cost optimization
        ai_costs = sum(record.amount for record in records if record.category == CostCategory.AI_API)
        if ai_costs > 100:  # High AI costs
            recommendations.append("Review AI model selection - consider using cheaper models for simple tasks")
            recommendations.append("Implement AI response caching to reduce API calls")
            recommendations.append("Optimize prompt engineering to reduce token usage")

        # Data provider cost optimization
        data_costs = sum(record.amount for record in records if record.category == CostCategory.DATA_PROVIDER)
        if data_costs > 50:
            recommendations.append("Consolidate data provider requests to reduce API calls")
            recommendations.append("Implement data caching for frequently accessed market data")
            recommendations.append("Review data request frequency and batch where possible")

        # Infrastructure cost optimization
        infra_costs = sum(record.amount for record in records if record.category == CostCategory.INFRASTRUCTURE)
        if infra_costs > 200:
            recommendations.append("Review resource allocation and downsize unused capacity")
            recommendations.append("Implement auto-scaling policies based on actual usage")
            recommendations.append("Optimize database queries and indexing")

        return recommendations

    def export_cost_report(self, report: CostReport, output_file: str) -> bool:
        """Export cost report to JSON file"""
        try:
            report_data = {
                "report_id": report.report_id,
                "period_start": report.period_start.isoformat(),
                "period_end": report.period_end.isoformat(),
                "total_cost": report.total_cost,
                "costs_by_category": report.costs_by_category,
                "costs_by_type": report.costs_by_type,
                "alerts": [
                    {
                        "alert_id": alert.alert_id,
                        "category": alert.category.value,
                        "threshold_type": alert.threshold_type,
                        "current_cost": alert.current_cost,
                        "threshold": alert.threshold,
                        "alert_level": alert.alert_level,
                        "timestamp": alert.timestamp.isoformat(),
                        "metadata": alert.metadata
                    }
                    for alert in report.alerts
                ],
                "budget_remaining": report.budget_remaining,
                "cost_efficiency_score": report.cost_efficiency_score,
                "recommendations": report.recommendations
            }

            with open(output_file, 'w') as f:
                json.dump(report_data, f, indent=2)

            logger.info(f"Cost report exported: {output_file}")
            return True

        except Exception as e:
            logger.error(f"Error exporting cost report: {e}")
            return False

    def get_cost_summary(self, category: Optional[CostCategory] = None) -> Dict[str, Any]:
        """Get current cost summary"""
        summary = {
            "total_cost_today": sum(self.daily_costs.values()),
            "total_cost_month": sum(self.monthly_costs.values()),
            "costs_by_category": dict(self.current_period_costs),
            "alerts_count": len(self.alerts),
            "budgets": {}
        }

        for budget_name, budget_data in self.budgets.items():
            status = self.get_budget_status(budget_name)
            if status:
                summary["budgets"][budget_name] = status

        if category:
            summary["category_cost"] = self.current_period_costs.get(category, 0.0)

        return summary

    def start_monitoring(self):
        """Start cost monitoring when event loop is available"""
        try:
            loop = asyncio.get_running_loop()
            loop.create_task(self.start_cost_monitoring())
        except RuntimeError:
            # No event loop running, will be started later
            pass

# Global cost tracker instance
cost_tracker = CostTracker()

def get_cost_tracker() -> CostTracker:
    """Get the global cost tracker instance"""
    return cost_tracker

def record_ai_usage(provider: str, model: str, tokens_used: int, 
                   input_tokens: int, output_tokens: int, user_id: Optional[str] = None) -> str:
    """Record AI usage for cost tracking"""
    return cost_tracker.record_ai_cost(provider, model, tokens_used, input_tokens, output_tokens, user_id)

def record_data_usage(provider: str, data_type: str, request_count: int, 
                     user_id: Optional[str] = None) -> str:
    """Record data provider usage for cost tracking"""
    return cost_tracker.record_data_provider_cost(provider, data_type, request_count, user_id)

def record_infrastructure_usage(resource_type: str, usage: float, unit_cost: float,
                               user_id: Optional[str] = None) -> str:
    """Record infrastructure usage for cost tracking"""
    return cost_tracker.record_infrastructure_cost(resource_type, usage, unit_cost, user_id)

def get_cost_summary(category: Optional[CostCategory] = None) -> Dict[str, Any]:
    """Get current cost summary"""
    return cost_tracker.get_cost_summary(category)