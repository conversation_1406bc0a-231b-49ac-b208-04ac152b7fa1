"""
Legacy Component Archiver for ASK Pipeline

Provides tools for archiving legacy components:
- Move deprecated components to archive directory with documentation
- Create migration guide from old to new implementations
- Add deprecation warnings and sunset timeline
- Implement legacy component monitoring and usage tracking
- Create final cleanup and removal procedures

NOTE: This tool only ANALYZES and PREPARES - it does NOT move or delete anything
"""

import os
import re
import logging
from typing import Dict, Any, Optional, List, Set
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from pathlib import Path
import json

logger = logging.getLogger(__name__)

@dataclass
class LegacyComponent:
    """Legacy component information"""
    name: str
    file_path: str
    component_type: str  # function, class, module, file
    deprecated_since: datetime
    sunset_date: Optional[datetime] = None
    replacement: Optional[str] = None
    usage_count: int = 0
    last_used: Optional[datetime] = None
    deprecation_warnings: List[str] = field(default_factory=list)
    migration_notes: str = ""

@dataclass
class ArchivePlan:
    """Archive plan for legacy components"""
    component: LegacyComponent
    archive_path: str
    documentation_path: str
    migration_guide_path: str
    monitoring_config: Dict[str, Any] = field(default_factory=dict)

class LegacyArchiver:
    """Legacy component archiving system"""
    
    def __init__(self, project_root: str = ".", archive_dir: str = "archive"):
        self.project_root = Path(project_root)
        self.archive_dir = self.project_root / archive_dir
        self.legacy_components: List[LegacyComponent] = []
        self.archive_plans: List[ArchivePlan] = []
        
        # Create archive directory if it doesn't exist
        self.archive_dir.mkdir(exist_ok=True)
        
        # Legacy component patterns
        self.legacy_patterns = {
            'deprecated_functions': r'@deprecated|# DEPRECATED|# TODO: Remove',
            'legacy_classes': r'class\s+Legacy\w+|class\s+Old\w+',
            'unused_imports': r'import\s+\w+.*# UNUSED|from\s+\w+\s+import.*# UNUSED',
            'commented_code': r'#\s*(def\s+\w+|class\s+\w+)',
            'temp_files': r'temp_|tmp_|_old|_backup'
        }
        
        logger.info(f"LegacyArchiver initialized with archive directory: {self.archive_dir}")
    
    def scan_for_legacy_components(self, directory: Path = None) -> List[LegacyComponent]:
        """Scan directory for legacy components"""
        if directory is None:
            directory = self.project_root / "src"
        
        legacy_components = []
        
        for py_file in directory.rglob("*.py"):
            if py_file.name.startswith('__'):
                continue
            
            components = self._analyze_file_for_legacy_components(py_file)
            legacy_components.extend(components)
        
        self.legacy_components = legacy_components
        logger.info(f"Found {len(legacy_components)} legacy components")
        return legacy_components
    
    def _analyze_file_for_legacy_components(self, file_path: Path) -> List[LegacyComponent]:
        """Analyze a file for legacy components"""
        components = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            lines = content.split('\n')
            
            for i, line in enumerate(lines, 1):
                # Check for deprecated functions
                if re.search(self.legacy_patterns['deprecated_functions'], line, re.IGNORECASE):
                    component = self._create_legacy_component(
                        file_path, line, i, 'function', 'deprecated'
                    )
                    if component:
                        components.append(component)
                
                # Check for legacy classes
                elif re.search(self.legacy_patterns['legacy_classes'], line):
                    component = self._create_legacy_component(
                        file_path, line, i, 'class', 'legacy'
                    )
                    if component:
                        components.append(component)
                
                # Check for unused imports
                elif re.search(self.legacy_patterns['unused_imports'], line):
                    component = self._create_legacy_component(
                        file_path, line, i, 'import', 'unused'
                    )
                    if component:
                        components.append(component)
                
                # Check for commented code
                elif re.search(self.legacy_patterns['commented_code'], line):
                    component = self._create_legacy_component(
                        file_path, line, i, 'code', 'commented'
                    )
                    if component:
                        components.append(component)
            
            # Check for temp files
            if re.search(self.legacy_patterns['temp_files'], file_path.name, re.IGNORECASE):
                component = LegacyComponent(
                    name=file_path.stem,
                    file_path=str(file_path),
                    component_type='file',
                    deprecated_since=datetime.fromtimestamp(file_path.stat().st_mtime),
                    migration_notes="Temporary file - consider removal"
                )
                components.append(component)
        
        except Exception as e:
            logger.error(f"Error analyzing {file_path} for legacy components: {e}")
        
        return components
    
    def _create_legacy_component(self, file_path: Path, line: str, line_num: int, 
                               component_type: str, reason: str) -> Optional[LegacyComponent]:
        """Create a legacy component from analysis"""
        try:
            # Extract component name from line
            if component_type == 'function':
                match = re.search(r'def\s+(\w+)', line)
                name = match.group(1) if match else f"function_at_line_{line_num}"
            elif component_type == 'class':
                match = re.search(r'class\s+(\w+)', line)
                name = match.group(1) if match else f"class_at_line_{line_num}"
            else:
                name = f"{component_type}_at_line_{line_num}"
            
            return LegacyComponent(
                name=name,
                file_path=str(file_path),
                component_type=component_type,
                deprecated_since=datetime.now(),
                migration_notes=f"Identified as {reason} at line {line_num}"
            )
        
        except Exception as e:
            logger.error(f"Error creating legacy component: {e}")
            return None
    
    def create_archive_plans(self) -> List[ArchivePlan]:
        """Create archive plans for legacy components"""
        plans = []
        
        for component in self.legacy_components:
            plan = self._create_archive_plan(component)
            if plan:
                plans.append(plan)
        
        self.archive_plans = plans
        logger.info(f"Created {len(plans)} archive plans")
        return plans
    
    def _create_archive_plan(self, component: LegacyComponent) -> Optional[ArchivePlan]:
        """Create an archive plan for a component"""
        try:
            # Create archive paths
            relative_path = Path(component.file_path).relative_to(self.project_root)
            archive_path = self.archive_dir / "legacy" / relative_path
            documentation_path = archive_path.with_suffix('.md')
            migration_guide_path = archive_path.with_suffix('.migration.md')
            
            # Create monitoring config
            monitoring_config = {
                'component_name': component.name,
                'component_type': component.component_type,
                'deprecated_since': component.deprecated_since.isoformat(),
                'sunset_date': component.sunset_date.isoformat() if component.sunset_date else None,
                'replacement': component.replacement,
                'monitoring_enabled': True,
                'usage_tracking': True,
                'deprecation_warnings': component.deprecation_warnings
            }
            
            return ArchivePlan(
                component=component,
                archive_path=str(archive_path),
                documentation_path=str(documentation_path),
                migration_guide_path=str(migration_guide_path),
                monitoring_config=monitoring_config
            )
        
        except Exception as e:
            logger.error(f"Error creating archive plan for {component.name}: {e}")
            return None
    
    def generate_migration_guide(self, component: LegacyComponent) -> str:
        """Generate migration guide for a legacy component"""
        guide = f"""# Migration Guide: {component.name}

## Component Information
- **Type**: {component.component_type}
- **File**: {component.file_path}
- **Deprecated Since**: {component.deprecated_since.strftime('%Y-%m-%d')}
- **Sunset Date**: {component.sunset_date.strftime('%Y-%m-%d') if component.sunset_date else 'TBD'}

## Replacement
{component.replacement or 'No direct replacement available'}

## Migration Steps
1. **Identify Usage**: Find all references to this component
2. **Update Imports**: Replace imports with new component
3. **Update Function Calls**: Update function signatures if changed
4. **Test Changes**: Ensure functionality remains the same
5. **Remove Old Code**: Delete the legacy component

## Breaking Changes
- [ ] Function signature changes
- [ ] Return value changes
- [ ] Dependencies changed
- [ ] Configuration changes

## Testing Checklist
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] Performance tests pass
- [ ] Manual testing completed

## Notes
{component.migration_notes}

## Support
For questions about this migration, contact the development team.
"""
        return guide
    
    def generate_deprecation_warning(self, component: LegacyComponent) -> str:
        """Generate deprecation warning for a component"""
        warning = f"""
# Deprecation Warning

**{component.name}** ({component.component_type}) is deprecated.

- **Deprecated Since**: {component.deprecated_since.strftime('%Y-%m-%d')}
- **Sunset Date**: {component.sunset_date.strftime('%Y-%m-%d') if component.sunset_date else 'TBD'}
- **Replacement**: {component.replacement or 'No direct replacement'}

Please migrate to the new implementation as soon as possible.

## Migration Guide
See the migration guide for detailed instructions on how to update your code.

## Timeline
- **Phase 1** (Current): Deprecation warnings active
- **Phase 2** ({component.sunset_date.strftime('%Y-%m-%d') if component.sunset_date else 'TBD'}): Component will be removed
"""
        return warning
    
    def generate_archive_report(self) -> Dict[str, Any]:
        """Generate comprehensive archive report"""
        return {
            'analysis_timestamp': datetime.utcnow().isoformat(),
            'total_legacy_components': len(self.legacy_components),
            'components_by_type': self._group_components_by_type(),
            'archive_plans': [
                {
                    'component_name': plan.component.name,
                    'component_type': plan.component.component_type,
                    'file_path': plan.component.file_path,
                    'archive_path': plan.archive_path,
                    'documentation_path': plan.documentation_path,
                    'migration_guide_path': plan.migration_guide_path
                } for plan in self.archive_plans
            ],
            'recommendations': self._generate_archive_recommendations(),
            'timeline': self._generate_archive_timeline()
        }
    
    def _group_components_by_type(self) -> Dict[str, int]:
        """Group legacy components by type"""
        type_counts = {}
        for component in self.legacy_components:
            type_counts[component.component_type] = type_counts.get(component.component_type, 0) + 1
        return type_counts
    
    def _generate_archive_recommendations(self) -> List[str]:
        """Generate archive recommendations"""
        recommendations = []
        
        # Check for high-priority components
        high_priority = [c for c in self.legacy_components if c.usage_count > 10]
        if high_priority:
            recommendations.append(f"Prioritize migration of {len(high_priority)} high-usage components")
        
        # Check for old components
        old_components = [c for c in self.legacy_components 
                         if (datetime.now() - c.deprecated_since).days > 90]
        if old_components:
            recommendations.append(f"Consider sunsetting {len(old_components)} old deprecated components")
        
        # Check for unused components
        unused_components = [c for c in self.legacy_components if c.usage_count == 0]
        if unused_components:
            recommendations.append(f"Remove {len(unused_components)} unused legacy components")
        
        return recommendations
    
    def _generate_archive_timeline(self) -> Dict[str, Any]:
        """Generate archive timeline"""
        now = datetime.now()
        
        # Group components by sunset date
        timeline = {
            'immediate': [],  # Past due
            'next_month': [],
            'next_quarter': [],
            'future': []
        }
        
        for component in self.legacy_components:
            if component.sunset_date:
                if component.sunset_date < now:
                    timeline['immediate'].append(component.name)
                elif component.sunset_date < now + timedelta(days=30):
                    timeline['next_month'].append(component.name)
                elif component.sunset_date < now + timedelta(days=90):
                    timeline['next_quarter'].append(component.name)
                else:
                    timeline['future'].append(component.name)
            else:
                timeline['future'].append(component.name)
        
        return timeline
    
    def export_report(self, output_file: str = None) -> str:
        """Export archive report to JSON file"""
        if output_file is None:
            output_file = f"legacy_archive_report_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}.json"
        
        report_data = self.generate_archive_report()
        
        with open(output_file, 'w') as f:
            json.dump(report_data, f, indent=2, default=str)
        
        logger.info(f"Legacy archive report exported to {output_file}")
        return output_file

# Global archiver instance
_legacy_archiver: Optional[LegacyArchiver] = None

def get_legacy_archiver(project_root: str = None, archive_dir: str = "archive") -> LegacyArchiver:
    """Get global legacy archiver instance"""
    global _legacy_archiver
    if _legacy_archiver is None:
        _legacy_archiver = LegacyArchiver(project_root, archive_dir)
    return _legacy_archiver

def cleanup_legacy_archiver():
    """Cleanup global archiver instance"""
    global _legacy_archiver
    _legacy_archiver = None