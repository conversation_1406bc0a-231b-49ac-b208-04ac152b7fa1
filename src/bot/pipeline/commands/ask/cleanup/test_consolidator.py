"""
Test Consolidator for ASK Pipeline

Provides tools for analyzing and consolidating test files:
- Analyze 15+ overlapping test files and identify duplicates
- Identify duplicate test scenarios and outdated test data
- Standardize test naming conventions and organization
- Create test suite documentation and execution guides
- Implement test maintenance and update procedures

NOTE: This tool only ANALYZES and REPORTS - it does NOT delete or modify anything
"""

import os
import re
import logging
from typing import Dict, Any, Optional, List, Set, Tuple
from dataclasses import dataclass, field
from datetime import datetime
from pathlib import Path
import json
import ast

logger = logging.getLogger(__name__)

@dataclass
class FileInfo:
    """Test file information"""
    file_path: str
    test_functions: List[str] = field(default_factory=list)
    test_classes: List[str] = field(default_factory=list)
    imports: List[str] = field(default_factory=list)
    fixtures: List[str] = field(default_factory=list)
    test_data: List[str] = field(default_factory=list)
    dependencies: List[str] = field(default_factory=list)
    file_size: int = 0
    line_count: int = 0
    last_modified: datetime = field(default_factory=datetime.utcnow)

@dataclass
class ScenarioInfo:
    """Test scenario information"""
    name: str
    file_path: str
    function_name: str
    description: str
    test_type: str  # unit, integration, e2e, performance
    dependencies: List[str] = field(default_factory=list)
    data_requirements: List[str] = field(default_factory=list)
    expected_duration: Optional[float] = None

@dataclass
class DuplicateTest:
    """Duplicate test information"""
    test_name: str
    files: List[str]
    similarity_score: float
    differences: List[str] = field(default_factory=list)
    recommended_action: str = ""

@dataclass
class ConsolidationReport:
    """Test consolidation analysis report"""
    total_test_files: int
    total_test_functions: int
    duplicate_tests: List[DuplicateTest] = field(default_factory=list)
    outdated_tests: List[str] = field(default_factory=list)
    missing_tests: List[str] = field(default_factory=list)
    consolidation_recommendations: List[str] = field(default_factory=list)
    test_organization_plan: Dict[str, Any] = field(default_factory=dict)
    analysis_timestamp: datetime = field(default_factory=datetime.utcnow)

class Consolidator:
    """Analyzes and consolidates test files for better organization and maintenance"""
    
    def __init__(self, project_root: str = None):
        self.project_root = Path(project_root) if project_root else Path.cwd()
        self.test_directories = [
            self.project_root / "tests",
            self.project_root / "src" / "bot" / "pipeline" / "commands" / "ask" / "tests",
            self.project_root / "src" / "tests"
        ]
        self.test_files: List[FileInfo] = []
        self.test_scenarios: List[ScenarioInfo] = []
        
        # Test patterns
        self.test_function_pattern = re.compile(r'def\s+(test_\w+)')
        self.test_class_pattern = re.compile(r'class\s+(Test\w+)')
        self.fixture_pattern = re.compile(r'@pytest\.fixture|@fixture')
        self.import_pattern = re.compile(r'^import\s+(\w+)|^from\s+(\w+)\s+import')
        
        logger.info("TestConsolidator initialized")
    
    def analyze_test_files(self) -> List[FileInfo]:
        """Analyze all test files in the project"""
        test_files = []
        
        for test_dir in self.test_directories:
            if test_dir.exists():
                for py_file in test_dir.rglob("test_*.py"):
                    test_file = self._analyze_test_file(py_file)
                    if test_file:
                        test_files.append(test_file)
        
        self.test_files = test_files
        logger.info(f"Analyzed {len(test_files)} test files")
        return test_files
    
    def _analyze_test_file(self, file_path: Path) -> Optional[FileInfo]:
        """Analyze a single test file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Parse AST
            tree = ast.parse(content, filename=str(file_path))
            
            # Extract test functions
            test_functions = []
            test_classes = []
            fixtures = []
            imports = []
            
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    if node.name.startswith('test_'):
                        test_functions.append(node.name)
                elif isinstance(node, ast.ClassDef):
                    if node.name.startswith('Test'):
                        test_classes.append(node.name)
                elif isinstance(node, ast.Import):
                    for alias in node.names:
                        imports.append(alias.name)
                elif isinstance(node, ast.ImportFrom):
                    if node.module:
                        imports.append(node.module)
            
            # Find fixtures using regex (more reliable than AST for decorators)
            fixture_matches = self.fixture_pattern.findall(content)
            fixtures = [match for match in fixture_matches if match]
            
            # Get file stats
            file_size = file_path.stat().st_size
            line_count = len(content.split('\n'))
            last_modified = datetime.fromtimestamp(file_path.stat().st_mtime)
            
            return FileInfo(
                file_path=str(file_path),
                test_functions=test_functions,
                test_classes=test_classes,
                imports=imports,
                fixtures=fixtures,
                file_size=file_size,
                line_count=line_count,
                last_modified=last_modified
            )
            
        except Exception as e:
            logger.error(f"Error analyzing test file {file_path}: {e}")
            return None
    
    def find_duplicate_tests(self) -> List[DuplicateTest]:
        """Find duplicate test functions across files"""
        duplicates = []
        test_function_map = {}
        
        # Group test functions by name
        for test_file in self.test_files:
            for test_func in test_file.test_functions:
                if test_func not in test_function_map:
                    test_function_map[test_func] = []
                test_function_map[test_func].append(test_file.file_path)
        
        # Find duplicates
        for test_name, files in test_function_map.items():
            if len(files) > 1:
                # Calculate similarity score (simplified)
                similarity_score = self._calculate_test_similarity(test_name, files)
                
                duplicate = DuplicateTest(
                    test_name=test_name,
                    files=files,
                    similarity_score=similarity_score,
                    recommended_action=self._get_consolidation_recommendation(test_name, files)
                )
                duplicates.append(duplicate)
        
        logger.info(f"Found {len(duplicates)} duplicate test functions")
        return duplicates
    
    def _calculate_test_similarity(self, test_name: str, files: List[str]) -> float:
        """Calculate similarity score between duplicate tests"""
        # Simplified similarity calculation
        # In a real implementation, this would compare the actual test content
        if len(files) == 2:
            return 0.8  # Assume 80% similarity for now
        else:
            return 0.6  # Lower similarity for multiple duplicates
    
    def _get_consolidation_recommendation(self, test_name: str, files: List[str]) -> str:
        """Get recommendation for consolidating duplicate tests"""
        if len(files) == 2:
            return f"Merge duplicate test '{test_name}' from {len(files)} files"
        else:
            return f"Consolidate {len(files)} duplicate tests named '{test_name}'"
    
    def identify_outdated_tests(self) -> List[str]:
        """Identify potentially outdated test files"""
        outdated_tests = []
        current_time = datetime.utcnow()
        
        for test_file in self.test_files:
            # Check if file hasn't been modified in 6 months
            if (current_time - test_file.last_modified).days > 180:
                outdated_tests.append(test_file.file_path)
            
            # Check for empty test files
            if len(test_file.test_functions) == 0 and len(test_file.test_classes) == 0:
                outdated_tests.append(test_file.file_path)
        
        logger.info(f"Found {len(outdated_tests)} potentially outdated test files")
        return outdated_tests
    
    def generate_consolidation_plan(self) -> Dict[str, Any]:
        """Generate a test consolidation plan"""
        duplicates = self.find_duplicate_tests()
        outdated = self.identify_outdated_tests()
        
        # Group tests by functionality
        test_groups = self._group_tests_by_functionality()
        
        # Create consolidation plan
        plan = {
            'duplicate_consolidation': {
                'total_duplicates': len(duplicates),
                'high_priority': [d for d in duplicates if d.similarity_score > 0.8],
                'medium_priority': [d for d in duplicates if 0.5 < d.similarity_score <= 0.8],
                'low_priority': [d for d in duplicates if d.similarity_score <= 0.5]
            },
            'outdated_cleanup': {
                'total_outdated': len(outdated),
                'files_to_review': outdated
            },
            'test_organization': {
                'suggested_structure': self._suggest_test_structure(),
                'test_groups': test_groups
            },
            'recommendations': self._generate_consolidation_recommendations(duplicates, outdated)
        }
        
        return plan
    
    def _group_tests_by_functionality(self) -> Dict[str, List[str]]:
        """Group tests by functionality based on naming patterns"""
        groups = {
            'unit_tests': [],
            'integration_tests': [],
            'e2e_tests': [],
            'performance_tests': [],
            'api_tests': [],
            'other_tests': []
        }
        
        for test_file in self.test_files:
            file_name = Path(test_file.file_path).name.lower()
            
            if 'unit' in file_name or 'test_' in file_name:
                groups['unit_tests'].append(test_file.file_path)
            elif 'integration' in file_name:
                groups['integration_tests'].append(test_file.file_path)
            elif 'e2e' in file_name or 'end_to_end' in file_name:
                groups['e2e_tests'].append(test_file.file_path)
            elif 'performance' in file_name or 'perf' in file_name:
                groups['performance_tests'].append(test_file.file_path)
            elif 'api' in file_name:
                groups['api_tests'].append(test_file.file_path)
            else:
                groups['other_tests'].append(test_file.file_path)
        
        return groups
    
    def _suggest_test_structure(self) -> Dict[str, str]:
        """Suggest a standardized test directory structure"""
        return {
            'unit_tests': 'tests/unit/',
            'integration_tests': 'tests/integration/',
            'e2e_tests': 'tests/e2e/',
            'performance_tests': 'tests/performance/',
            'fixtures': 'tests/fixtures/',
            'test_data': 'tests/data/',
            'test_utilities': 'tests/utils/'
        }
    
    def _generate_consolidation_recommendations(self, duplicates: List[DuplicateTest], outdated: List[str]) -> List[str]:
        """Generate consolidation recommendations"""
        recommendations = []
        
        if duplicates:
            recommendations.append(f"Consolidate {len(duplicates)} duplicate test functions")
        
        if outdated:
            recommendations.append(f"Review and update {len(outdated)} outdated test files")
        
        # Check for test coverage gaps
        total_tests = sum(len(tf.test_functions) for tf in self.test_files)
        if total_tests < 50:
            recommendations.append("Consider adding more comprehensive test coverage")
        
        # Check for test organization
        if len(self.test_files) > 20:
            recommendations.append("Organize tests into logical directories by functionality")
        
        return recommendations
    
    def generate_consolidation_report(self) -> ConsolidationReport:
        """Generate comprehensive test consolidation report"""
        duplicates = self.find_duplicate_tests()
        outdated = self.identify_outdated_tests()
        total_test_functions = sum(len(tf.test_functions) for tf in self.test_files)
        
        return ConsolidationReport(
            total_test_files=len(self.test_files),
            total_test_functions=total_test_functions,
            duplicate_tests=duplicates,
            outdated_tests=outdated,
            consolidation_recommendations=self._generate_consolidation_recommendations(duplicates, outdated),
            test_organization_plan=self.generate_consolidation_plan()
        )
    
    def export_report(self, output_file: str = None) -> str:
        """Export consolidation report to JSON file"""
        if output_file is None:
            output_file = f"test_consolidation_report_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}.json"
        
        report = self.generate_consolidation_report()
        
        report_data = {
            'summary': {
                'total_test_files': report.total_test_files,
                'total_test_functions': report.total_test_functions,
                'duplicate_tests_count': len(report.duplicate_tests),
                'outdated_tests_count': len(report.outdated_tests),
                'analysis_timestamp': report.analysis_timestamp.isoformat()
            },
            'duplicate_tests': [
                {
                    'test_name': d.test_name,
                    'files': d.files,
                    'similarity_score': d.similarity_score,
                    'recommended_action': d.recommended_action
                } for d in report.duplicate_tests
            ],
            'outdated_tests': report.outdated_tests,
            'recommendations': report.consolidation_recommendations,
            'organization_plan': report.test_organization_plan
        }
        
        with open(output_file, 'w') as f:
            json.dump(report_data, f, indent=2, default=str)
        
        logger.info(f"Test consolidation report exported to {output_file}")
        return output_file

# Global consolidator instance
_test_consolidator: Optional[Consolidator] = None

def get_test_consolidator(project_root: str = None) -> Consolidator:
    """Get global test consolidator instance"""
    global _test_consolidator
    if _test_consolidator is None:
        _test_consolidator = Consolidator(project_root)
    return _test_consolidator

def cleanup_test_consolidator():
    """Cleanup global consolidator instance"""
    global _test_consolidator
    _test_consolidator = None