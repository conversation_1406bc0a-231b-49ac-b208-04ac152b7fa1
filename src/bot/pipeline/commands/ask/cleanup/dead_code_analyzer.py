"""
Dead Code Analyzer for ASK Pipeline

Provides analysis tools for identifying dead code:
- Identify and analyze unused imports and functions across all ask-related files
- Analyze obsolete test files and duplicate implementations
- Identify commented-out code and temporary debugging statements
- Analyze unused configuration options and environment variables
- Create code cleanup validation and prevention tools

NOTE: This tool only ANALYZES and REPORTS - it does NOT delete anything
"""

import ast
import os
import re
import logging
from typing import Dict, Any, Optional, List, Set, Tuple
from dataclasses import dataclass, field
from datetime import datetime
from pathlib import Path
import json

logger = logging.getLogger(__name__)

@dataclass
class DeadCodeReport:
    """Dead code analysis report"""
    file_path: str
    unused_imports: List[str] = field(default_factory=list)
    unused_functions: List[str] = field(default_factory=list)
    unused_classes: List[str] = field(default_factory=list)
    unused_variables: List[str] = field(default_factory=list)
    commented_code: List[Dict[str, Any]] = field(default_factory=list)
    debug_statements: List[Dict[str, Any]] = field(default_factory=list)
    todo_comments: List[Dict[str, Any]] = field(default_factory=list)
    duplicate_code: List[Dict[str, Any]] = field(default_factory=list)
    analysis_timestamp: datetime = field(default_factory=datetime.utcnow)

@dataclass
class CodeMetrics:
    """Code metrics for a file"""
    file_path: str
    total_lines: int
    code_lines: int
    comment_lines: int
    blank_lines: int
    function_count: int
    class_count: int
    import_count: int
    complexity_score: float
    maintainability_index: float

class DeadCodeAnalyzer:
    """Analyzes code for dead code patterns and provides cleanup recommendations"""
    
    def __init__(self, project_root: str = None):
        self.project_root = Path(project_root) if project_root else Path.cwd()
        self.ask_pipeline_path = self.project_root / "src" / "bot" / "pipeline" / "commands" / "ask"
        self.reports: List[DeadCodeReport] = []
        self.metrics: List[CodeMetrics] = []
        
        # Patterns for identifying problematic code
        self.debug_patterns = [
            r'print\s*\(',
            r'console\.log\s*\(',
            r'debugger\s*;',
            r'pdb\.set_trace\s*\(',
            r'breakpoint\s*\(',
            r'import\s+pdb',
            r'import\s+ipdb'
        ]
        
        self.todo_patterns = [
            r'#\s*TODO:',
            r'#\s*FIXME:',
            r'#\s*HACK:',
            r'#\s*XXX:',
            r'#\s*NOTE:',
            r'#\s*BUG:'
        ]
        
        logger.info("DeadCodeAnalyzer initialized")
    
    def analyze_file(self, file_path: Path) -> DeadCodeReport:
        """Analyze a single file for dead code patterns"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Parse AST
            tree = ast.parse(content, filename=str(file_path))
            
            # Initialize report
            report = DeadCodeReport(file_path=str(file_path))
            
            # Find unused imports
            report.unused_imports = self._find_unused_imports(tree, content)
            
            # Find unused functions and classes
            report.unused_functions = self._find_unused_functions(tree, content)
            report.unused_classes = self._find_unused_classes(tree, content)
            
            # Find commented code
            report.commented_code = self._find_commented_code(content)
            
            # Find debug statements
            report.debug_statements = self._find_debug_statements(content)
            
            # Find TODO comments
            report.todo_comments = self._find_todo_comments(content)
            
            # Calculate metrics
            metrics = self._calculate_metrics(file_path, content)
            self.metrics.append(metrics)
            
            self.reports.append(report)
            logger.debug(f"Analyzed {file_path}: {len(report.unused_imports)} unused imports, {len(report.unused_functions)} unused functions")
            
            return report
            
        except Exception as e:
            logger.error(f"Error analyzing {file_path}: {e}")
            return DeadCodeReport(file_path=str(file_path))
    
    def analyze_directory(self, directory: Path = None) -> List[DeadCodeReport]:
        """Analyze all Python files in a directory"""
        if directory is None:
            directory = self.ask_pipeline_path
        
        if not directory.exists():
            logger.warning(f"Directory {directory} does not exist")
            return []
        
        reports = []
        for py_file in directory.rglob("*.py"):
            if py_file.name.startswith('__'):
                continue
            report = self.analyze_file(py_file)
            reports.append(report)
        
        logger.info(f"Analyzed {len(reports)} files in {directory}")
        return reports
    
    def _find_unused_imports(self, tree: ast.AST, content: str) -> List[str]:
        """Find unused imports in the AST"""
        unused_imports = []
        
        # Get all import statements
        imports = []
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    imports.append(alias.name)
            elif isinstance(node, ast.ImportFrom):
                if node.module:
                    for alias in node.names:
                        imports.append(f"{node.module}.{alias.name}")
        
        # Check if imports are used
        for import_name in imports:
            # Simple check - look for the import name in the content
            # This is a basic implementation and could be improved
            if import_name not in content.replace(f"import {import_name}", ""):
                unused_imports.append(import_name)
        
        return unused_imports
    
    def _find_unused_functions(self, tree: ast.AST, content: str) -> List[str]:
        """Find unused function definitions"""
        unused_functions = []
        
        # Get all function definitions
        functions = []
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                functions.append(node.name)
        
        # Check if functions are called
        for func_name in functions:
            # Skip private functions (starting with _)
            if func_name.startswith('_'):
                continue
            
            # Look for function calls
            if f"{func_name}(" not in content:
                unused_functions.append(func_name)
        
        return unused_functions
    
    def _find_unused_classes(self, tree: ast.AST, content: str) -> List[str]:
        """Find unused class definitions"""
        unused_classes = []
        
        # Get all class definitions
        classes = []
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef):
                classes.append(node.name)
        
        # Check if classes are instantiated or inherited
        for class_name in classes:
            # Skip private classes (starting with _)
            if class_name.startswith('_'):
                continue
            
            # Look for class usage
            if f"{class_name}(" not in content and f"class " not in content:
                unused_classes.append(class_name)
        
        return unused_classes
    
    def _find_commented_code(self, content: str) -> List[Dict[str, Any]]:
        """Find commented-out code blocks"""
        commented_code = []
        lines = content.split('\n')
        
        for i, line in enumerate(lines, 1):
            stripped = line.strip()
            if stripped.startswith('#') and len(stripped) > 1:
                # Check if it looks like code (not just a comment)
                code_part = stripped[1:].strip()
                if any(keyword in code_part for keyword in ['def ', 'class ', 'if ', 'for ', 'while ', 'return ', '=']):
                    commented_code.append({
                        'line_number': i,
                        'content': code_part,
                        'original_line': line
                    })
        
        return commented_code
    
    def _find_debug_statements(self, content: str) -> List[Dict[str, Any]]:
        """Find debug statements and temporary code"""
        debug_statements = []
        lines = content.split('\n')
        
        for i, line in enumerate(lines, 1):
            for pattern in self.debug_patterns:
                if re.search(pattern, line):
                    debug_statements.append({
                        'line_number': i,
                        'content': line.strip(),
                        'pattern': pattern
                    })
                    break
        
        return debug_statements
    
    def _find_todo_comments(self, content: str) -> List[Dict[str, Any]]:
        """Find TODO and similar comments"""
        todo_comments = []
        lines = content.split('\n')
        
        for i, line in enumerate(lines, 1):
            for pattern in self.todo_patterns:
                if re.search(pattern, line, re.IGNORECASE):
                    todo_comments.append({
                        'line_number': i,
                        'content': line.strip(),
                        'type': pattern.split(':')[0].replace('#', '').strip()
                    })
                    break
        
        return todo_comments
    
    def _calculate_metrics(self, file_path: Path, content: str) -> CodeMetrics:
        """Calculate code metrics for a file"""
        lines = content.split('\n')
        total_lines = len(lines)
        
        code_lines = 0
        comment_lines = 0
        blank_lines = 0
        
        for line in lines:
            stripped = line.strip()
            if not stripped:
                blank_lines += 1
            elif stripped.startswith('#'):
                comment_lines += 1
            else:
                code_lines += 1
        
        # Parse AST for function and class counts
        try:
            tree = ast.parse(content)
            function_count = len([node for node in ast.walk(tree) if isinstance(node, ast.FunctionDef)])
            class_count = len([node for node in ast.walk(tree) if isinstance(node, ast.ClassDef)])
            import_count = len([node for node in ast.walk(tree) if isinstance(node, (ast.Import, ast.ImportFrom))])
        except:
            function_count = class_count = import_count = 0
        
        # Simple complexity calculation
        complexity_score = function_count + class_count * 2
        
        # Simple maintainability index
        maintainability_index = max(0, 100 - (complexity_score * 5) - (comment_lines / max(code_lines, 1) * 20))
        
        return CodeMetrics(
            file_path=str(file_path),
            total_lines=total_lines,
            code_lines=code_lines,
            comment_lines=comment_lines,
            blank_lines=blank_lines,
            function_count=function_count,
            class_count=class_count,
            import_count=import_count,
            complexity_score=complexity_score,
            maintainability_index=maintainability_index
        )
    
    def generate_summary_report(self) -> Dict[str, Any]:
        """Generate a summary report of all analyses"""
        total_files = len(self.reports)
        total_unused_imports = sum(len(r.unused_imports) for r in self.reports)
        total_unused_functions = sum(len(r.unused_functions) for r in self.reports)
        total_unused_classes = sum(len(r.unused_classes) for r in self.reports)
        total_debug_statements = sum(len(r.debug_statements) for r in self.reports)
        total_todo_comments = sum(len(r.todo_comments) for r in self.reports)
        
        # Calculate average metrics
        if self.metrics:
            avg_complexity = sum(m.complexity_score for m in self.metrics) / len(self.metrics)
            avg_maintainability = sum(m.maintainability_index for m in self.metrics) / len(self.metrics)
        else:
            avg_complexity = avg_maintainability = 0
        
        return {
            'analysis_timestamp': datetime.utcnow().isoformat(),
            'total_files_analyzed': total_files,
            'dead_code_summary': {
                'unused_imports': total_unused_imports,
                'unused_functions': total_unused_functions,
                'unused_classes': total_unused_classes,
                'debug_statements': total_debug_statements,
                'todo_comments': total_todo_comments
            },
            'code_quality_metrics': {
                'average_complexity_score': round(avg_complexity, 2),
                'average_maintainability_index': round(avg_maintainability, 2)
            },
            'recommendations': self._generate_recommendations()
        }
    
    def _generate_recommendations(self) -> List[str]:
        """Generate cleanup recommendations based on analysis"""
        recommendations = []
        
        total_unused_imports = sum(len(r.unused_imports) for r in self.reports)
        total_debug_statements = sum(len(r.debug_statements) for r in self.reports)
        total_todo_comments = sum(len(r.todo_comments) for r in self.reports)
        
        if total_unused_imports > 0:
            recommendations.append(f"Remove {total_unused_imports} unused imports to improve code clarity")
        
        if total_debug_statements > 0:
            recommendations.append(f"Remove {total_debug_statements} debug statements before production")
        
        if total_todo_comments > 0:
            recommendations.append(f"Address {total_todo_comments} TODO/FIXME comments")
        
        # Check for files with high complexity
        high_complexity_files = [m for m in self.metrics if m.complexity_score > 20]
        if high_complexity_files:
            recommendations.append(f"Refactor {len(high_complexity_files)} files with high complexity")
        
        return recommendations
    
    def export_report(self, output_file: str = None) -> str:
        """Export analysis report to JSON file"""
        if output_file is None:
            output_file = f"dead_code_report_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}.json"
        
        report_data = {
            'summary': self.generate_summary_report(),
            'detailed_reports': [
                {
                    'file_path': r.file_path,
                    'unused_imports': r.unused_imports,
                    'unused_functions': r.unused_functions,
                    'unused_classes': r.unused_classes,
                    'commented_code': r.commented_code,
                    'debug_statements': r.debug_statements,
                    'todo_comments': r.todo_comments
                } for r in self.reports
            ],
            'metrics': [
                {
                    'file_path': m.file_path,
                    'total_lines': m.total_lines,
                    'code_lines': m.code_lines,
                    'comment_lines': m.comment_lines,
                    'blank_lines': m.blank_lines,
                    'function_count': m.function_count,
                    'class_count': m.class_count,
                    'import_count': m.import_count,
                    'complexity_score': m.complexity_score,
                    'maintainability_index': m.maintainability_index
                } for m in self.metrics
            ]
        }
        
        with open(output_file, 'w') as f:
            json.dump(report_data, f, indent=2, default=str)
        
        logger.info(f"Dead code analysis report exported to {output_file}")
        return output_file

# Global analyzer instance
_dead_code_analyzer: Optional[DeadCodeAnalyzer] = None

def get_dead_code_analyzer(project_root: str = None) -> DeadCodeAnalyzer:
    """Get global dead code analyzer instance"""
    global _dead_code_analyzer
    if _dead_code_analyzer is None:
        _dead_code_analyzer = DeadCodeAnalyzer(project_root)
    return _dead_code_analyzer

def cleanup_dead_code_analyzer():
    """Cleanup global analyzer instance"""
    global _dead_code_analyzer
    _dead_code_analyzer = None