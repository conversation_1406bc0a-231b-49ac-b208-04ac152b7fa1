"""
Context manager for bot pipeline operations, including data quality assessment.
"""

from enum import Enum
from dataclasses import dataclass
from typing import Optional
from datetime import datetime

class DataQuality(Enum):
    """Data quality levels for pipeline context."""
    EXCELLENT = "excellent"
    GOOD = "good"
    FAIR = "fair"
    POOR = "poor"
    UNRELIABLE = "unreliable"

@dataclass
class QualityScore:
    """Quality score for data in pipeline context."""
    overall_score: float = 0.0
    freshness_score: float = 0.0
    consistency_score: float = 0.0
    completeness_score: float = 0.0
    source_reliability: float = 0.0
    quality_level: DataQuality = DataQuality.UNRELIABLE
    data_window_start: Optional[datetime] = None
    data_window_end: Optional[datetime] = None
    gaps: list = None
    gap_penalty: float = 0.0

    def to_dict(self) -> dict:
        """Convert to dictionary for serialization."""
        return {
            'overall_score': self.overall_score,
            'freshness_score': self.freshness_score,
            'consistency_score': self.consistency_score,
            'completeness_score': self.completeness_score,
            'source_reliability': self.source_reliability,
            'quality_level': self.quality_level.value,
            'data_window_start': self.data_window_start.isoformat() if self.data_window_start else None,
            'data_window_end': self.data_window_end.isoformat() if self.data_window_end else None,
            'gap_penalty': self.gap_penalty,
            'gaps': [gap.to_dict() if hasattr(gap, 'to_dict') else str(gap) for gap in (self.gaps or [])]
        }
    