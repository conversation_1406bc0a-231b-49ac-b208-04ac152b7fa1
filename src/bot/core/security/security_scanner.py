"""
Simplified Security Scanner for ASK Pipeline

Ported from security/security_scanner.py:
- Basic PII detection and redaction
- Integration with core logger and config
- XSS and format validation
"""

import re
from typing import List, Optional
from dataclasses import dataclass

from src.core.config_manager import ConfigManager
from src.core.monitoring.logger import get_structured_logger

logger = get_structured_logger(__name__)
config = ConfigManager()

@dataclass
class SecurityResult:
    """Security result"""
    is_secure: bool
    issues: List[str]
    redacted_content: Optional[str] = None
    risk_level: str = "low"

@dataclass
class PIIMatch:
    """PII match"""
    match: str
    pii_type: str
    confidence: float

class SecurityScanner:
    """Simplified security scanner"""
    
    def __init__(self):
        self.pii_patterns = {
            'email': re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'),
        }
        self.xss_patterns = [
            re.compile(r'<script[^>]*>.*?</script>', re.IGNORECASE | re.DOTALL),
            re.compile(r'javascript:', re.IGNORECASE),
        ]
    
    def scan_response(self, response: str, context: dict) -> SecurityResult:
        """Scan response"""
        issues = []
        pii_matches = []
        
        # PII detection
        for pii_type, pattern in self.pii_patterns.items():
            matches = pattern.findall(response)
            if matches:
                pii_matches.extend([PIIMatch(match=m, pii_type=pii_type, confidence=0.95) for m in matches])
                issues.append(f"{len(matches)} {pii_type} detected")
        
        # XSS check
        for pattern in self.xss_patterns:
            if pattern.search(response):
                issues.append("Potential XSS detected")
        
        # Redact PII
        redacted = response
        for match in pii_matches:
            redacted = re.sub(re.escape(match.match), f"[{match.pii_type.upper()}_REDACTED]", redacted)
        
        is_secure = len(issues) == 0
        risk = "high" if "XSS" in str(issues) else "medium" if pii_matches else "low"
        
        if issues:
            logger.error(f"Security issues: {issues}")
        
        return SecurityResult(
            is_secure=is_secure,
            issues=issues,
            redacted_content=redacted if pii_matches else response,
            risk_level=risk
        )

scanner = SecurityScanner()

def get_security_scanner() -> SecurityScanner:
    return scanner