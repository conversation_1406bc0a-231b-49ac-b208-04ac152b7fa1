"""
Simplified Rate Limiter for ASK Pipeline

Ported from security/rate_limiter.py:
- Basic Redis-backed rate limiting
- Integration with core config and logger
- User and guild limits
"""

import time
from typing import Optional, Dict, Any
from enum import Enum
from dataclasses import dataclass

from src.core.config_manager import ConfigManager
from src.core.monitoring.logger import get_structured_logger
from src.core.performance.connection_pool import get_redis_pool

logger = get_structured_logger(__name__)
config = ConfigManager()

@dataclass
class RateLimitResult:
    """Rate limit result"""
    allowed: bool
    remaining: int
    reset_time: float
    limit: int

class RateLimitType(Enum):
    """Limit types"""
    USER_MINUTE = "user_minute"

class RateLimiter:
    """Simplified rate limiter"""

    def __init__(self):
        self.limits = config.get_section("security.rate_limits", default={
            "user_per_minute": 10
        })
        self.redis = None
    
    async def check_limit(self, user_id: str, limit_type: RateLimitType = RateLimitType.USER_MINUTE) -> RateLimitResult:
        """Check rate limit"""
        key = f"rate_limit:{limit_type.value}:{user_id}"
        limit = self.limits.get(limit_type.value, 10)
        window = 60  # 1 minute

        try:
            # Get redis pool if not already initialized
            if self.redis is None:
                self.redis = await get_redis_pool()

            current_time = time.time()
            count = await self.redis.execute("INCR", key)
            if count == 1:
                await self.redis.execute("EXPIRE", key, window)

            if count > limit:
                await self.redis.execute("EXPIRE", key, window)
                reset_time = current_time + window
                return RateLimitResult(allowed=False, remaining=0, reset_time=reset_time, limit=limit)

            remaining = limit - count
            reset_time = current_time + window
            return RateLimitResult(allowed=True, remaining=remaining, reset_time=reset_time, limit=limit)

        except Exception as e:
            logger.error(f"Rate limit check failed: {e}")
            return RateLimitResult(allowed=True, remaining=limit, reset_time=time.time() + window, limit=limit)  # Fail open

limiter = RateLimiter()

def get_rate_limiter() -> RateLimiter:
    return limiter