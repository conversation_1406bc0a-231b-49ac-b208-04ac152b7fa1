"""
Simplified Security Module for ASK Pipeline

Ported from security/simplified_security.py:
- Combined security checks: validation, rate limiting, auth
- Integration with core components
- Basic security for pipeline stages
"""

from typing import Dict, Any, List
from dataclasses import dataclass

from src.core.config_manager import ConfigManager
from src.core.monitoring.logger import get_structured_logger
from .auth import get_auth_manager
from .input_validator import get_input_validator
from .rate_limiter import get_rate_limiter

logger = get_structured_logger(__name__)
config = ConfigManager()
auth = get_auth_manager()
validator = get_input_validator()
limiter = get_rate_limiter()

@dataclass
class SecurityResult:
    """Combined security result"""
    is_valid: bool
    risk_level: str = "low"
    violations: List[str] = None
    sanitized_input: str = ""
    auth_authorized: bool = True
    rate_limit_ok: bool = True

class SimplifiedSecurity:
    """Combined security module"""
    
    def __init__(self):
        self.max_query_length = config.get("security.max_query_length", 2000)
    
    def validate_request(self, query: str, user_id: str, command: str = "ask") -> SecurityResult:
        """Validate request with all checks"""
        violations = []
        
        # Auth check
        auth_result = auth.validate_permissions(user_id, command)
        if not auth_result.is_authorized:
            violations.append(f"Auth failed: {auth_result.reason}")
        
        # Input validation
        val_result = validator.validate(query)
        if not val_result.is_valid:
            violations.extend(val_result.issues)
            sanitized = val_result.sanitized_query
        else:
            sanitized = query
        
        # Rate limiting
        rate_result = limiter.check_limit(user_id)
        if not rate_result.allowed:
            violations.append("Rate limit exceeded")
        
        is_valid = len(violations) == 0
        risk = "high" if "injection" in str(violations) else "low"
        
        result = SecurityResult(
            is_valid=is_valid,
            risk_level=risk,
            violations=violations,
            sanitized_input=sanitized,
            auth_authorized=auth_result.is_authorized,
            rate_limit_ok=rate_result.allowed
        )
        
        logger.info(f"Security validation: valid={is_valid}, risk={risk}")
        return result

security = SimplifiedSecurity()

def get_simplified_security() -> SimplifiedSecurity:
    return security