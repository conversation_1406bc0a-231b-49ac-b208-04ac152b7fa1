"""
Simplified Input Validator for ASK Pipeline

Ported from security/input_validator.py:
- Basic query validation and sanitization
- Integration with core logger and config
- Prompt injection detection
"""

import re
from typing import List, Optional
from dataclasses import dataclass
from enum import Enum

from src.core.config_manager import ConfigManager
from src.core.monitoring.logger import get_structured_logger

logger = get_structured_logger(__name__)
config = ConfigManager()

@dataclass
class ValidationResult:
    """Validation result"""
    is_valid: bool
    sanitized_query: str
    risk_level: str = "low"
    issues: List[str] = None

class InputValidator:
    """Simplified input validator"""
    
    def __init__(self):
        self.max_length = config.get("security.max_query_length", 2000)
        self.injection_patterns = [
            re.compile(r'(?i)\b(ignore|forget|disregard)\s+(previous|all)\s+(instructions?)', re.IGNORECASE),
            re.compile(r'(?i)\b(act\s+as|pretend\s+to\s+be|roleplay\s+as)', re.IGNORECASE),
        ]
    
    def validate(self, query: str) -> ValidationResult:
        """Validate query"""
        issues = []
        
        if len(query) > self.max_length:
            issues.append("Query too long")
        
        for pattern in self.injection_patterns:
            if pattern.search(query):
                issues.append("Potential prompt injection")
                break
        
        sanitized = re.sub(r'[\x00-\x1F\x7F]', '', query)  # Remove control chars
        
        is_valid = len(issues) == 0
        risk = "high" if "injection" in str(issues) else "low"
        
        logger.info(f"Validated query: valid={is_valid}, risk={risk}")
        return ValidationResult(is_valid=is_valid, sanitized_query=sanitized, issues=issues, risk_level=risk)

validator = InputValidator()

def get_input_validator() -> InputValidator:
    return validator