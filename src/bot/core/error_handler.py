"""Error handler compatibility wrapper.

The original core error handler was migrated; expose a thin shim that
delegates to shared error handling utilities where applicable.
"""

from src.shared.error_handling.logging import log_error


def log_and_notify_error(error, context=None, logger=None):
    """Log and notify about an error with context."""
    import logging
    if logger is None:
        logger = logging.getLogger(__name__)

    try:
        log_error(logger, error, context)
    except Exception:
        # Fallback to basic logging
        logger.exception(f"Error occurred: {error}")


def setup_error_handler(bot):
    """Attach a minimal error handler to the bot that logs via shared utilities."""

    @bot.event
    async def on_command_error(ctx, error):
        # Use shared logging helper to capture the exception with context
        try:
            log_error(__name__, error, context={"command": getattr(ctx, 'command', None)})
        except Exception:
            # Fallback to built-in logging if shared logger fails
            import logging
            logging.getLogger(__name__).exception("Failed to log command error")
        # Let default behavior continue (extensions may handle reply)
        return
