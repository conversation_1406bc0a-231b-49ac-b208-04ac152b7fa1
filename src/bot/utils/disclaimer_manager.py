"""
Disclaimer Manager Module

Provides standardized financial disclaimers and ensures they are consistently
applied to all responses that contain financial information or recommendations.
"""

import re
from typing import Dict, Any, List, Optional, Tuple

class DisclaimerManager:
    """Manages financial disclaimers for bot responses"""
    
    # Standard disclaimer templates
    GENERAL_DISCLAIMER = (
        "\n\n*This information is for educational purposes only and does not constitute financial advice. "
        "Always do your own research and consult with a financial advisor before making investment decisions.*"
    )
    
    TRADING_DISCLAIMER = (
        "\n\n*TRADING DISCLAIMER: The information provided is for educational purposes only. "
        "Past performance is not indicative of future results. Trading involves substantial risk of loss. "
        "Consult with a financial advisor before making any investment decisions.*"
    )
    
    RECOMMENDATION_DISCLAIMER = (
        "\n\n*RECOMMENDATION DISCLAIMER: These suggestions are not personalized investment advice. "
        "They are algorithmic interpretations based on technical analysis only. "
        "Always conduct thorough research and consider your financial situation, risk tolerance, "
        "and investment objectives before acting on any information provided.*"
    )
    
    PRICE_TARGET_DISCLAIMER = (
        "\n\n*PRICE TARGET DISCLAIMER: Price targets are algorithmic projections based on technical analysis "
        "and historical patterns. They are not guarantees of future performance. Markets are unpredictable "
        "and subject to numerous factors beyond technical indicators.*"
    )
    
    @classmethod
    def add_appropriate_disclaimer(cls, response: str, context: Dict[str, Any]) -> str:
        """
        Add the appropriate disclaimer based on response content and context
        
        Args:
            response: The response text to add a disclaimer to
            context: Context information about the response
            
        Returns:
            Response with appropriate disclaimer added
        """
        # Check if response already has a disclaimer
        if cls._has_disclaimer(response):
            return response
            
        # Determine the appropriate disclaimer based on content and context
        disclaimer = cls._select_disclaimer(response, context)
        
        # Add the disclaimer to the response
        return response + disclaimer
    
    @classmethod
    def _has_disclaimer(cls, response: str) -> bool:
        """
        Check if the response already has a disclaimer
        
        Args:
            response: The response text to check
            
        Returns:
            True if a disclaimer is already present, False otherwise
        """
        disclaimer_patterns = [
            r"disclaimer",
            r"for educational purposes only",
            r"not financial advice",
            r"not investment advice",
            r"consult.*financial advisor",
            r"do your own research",
            r"past performance.*future results"
        ]
        
        # Check for disclaimer patterns
        for pattern in disclaimer_patterns:
            if re.search(pattern, response, re.IGNORECASE):
                return True
                
        return False
    
    @classmethod
    def _select_disclaimer(cls, response: str, context: Dict[str, Any]) -> str:
        """
        Select the appropriate disclaimer based on content and context
        
        Args:
            response: The response text
            context: Context information about the response
            
        Returns:
            The appropriate disclaimer text
        """
        # Check for recommendation content
        if cls._contains_recommendation(response):
            return cls.RECOMMENDATION_DISCLAIMER
            
        # Check for price targets
        if cls._contains_price_targets(response):
            return cls.PRICE_TARGET_DISCLAIMER
            
        # Check for trading advice
        if cls._contains_trading_advice(response):
            return cls.TRADING_DISCLAIMER
            
        # Default to general disclaimer
        return cls.GENERAL_DISCLAIMER
    
    @classmethod
    def _contains_recommendation(cls, text: str) -> bool:
        """Check if text contains investment recommendations"""
        recommendation_patterns = [
            r"recommend",
            r"should (buy|sell|hold)",
            r"(buy|sell|hold) signal",
            r"good (investment|opportunity)",
            r"(bullish|bearish) outlook"
        ]
        
        for pattern in recommendation_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                return True
                
        return False
    
    @classmethod
    def _contains_price_targets(cls, text: str) -> bool:
        """Check if text contains price targets"""
        price_target_patterns = [
            r"price target",
            r"target price",
            r"projected (price|value)",
            r"(short|medium|long)[\s\-]term target",
            r"support.*resistance",
            r"could reach",
            r"may hit"
        ]
        
        for pattern in price_target_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                return True
                
        return False
    
    @classmethod
    def _contains_trading_advice(cls, text: str) -> bool:
        """Check if text contains trading advice"""
        trading_advice_patterns = [
            r"trading (strategy|plan|advice)",
            r"(entry|exit) point",
            r"stop[\s\-]loss",
            r"take[\s\-]profit",
            r"risk management",
            r"position sizing",
            r"technical (analysis|indicator)"
        ]
        
        for pattern in trading_advice_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                return True
                
        return False

# Convenience function
def add_disclaimer(response: str, context: Optional[Dict[str, Any]] = None) -> str:
    """
    Add appropriate disclaimer to a response (convenience function)
    
    Args:
        response: The response text
        context: Optional context information
        
    Returns:
        Response with disclaimer added
    """
    context = context or {}
    return DisclaimerManager.add_appropriate_disclaimer(response, context)