"""
Unified Database Manager

Provides a centralized, asynchronous interface for all database operations,
ensuring consistent, high-performance access to PostgreSQL.

Key Features:
- **Async Operations**: Uses asyncpg for non-blocking database access.
- **Connection Pooling**: Manages a pool of connections for efficiency.
- **Centralized Configuration**: All database settings managed in one place.
- **Graceful Shutdown**: Ensures connections are closed properly.
- **Comprehensive Error Handling**: Catches and logs database errors.
- **Singleton Pattern**: Ensures a single instance manages all connections.
"""

import asyncio
import asyncpg
import logging
from typing import Optional, List, Dict, Any, Tuple
from contextlib import asynccontextmanager

from src.shared.config import get_config

logger = logging.getLogger(__name__)

class UnifiedDBManager:
    """Singleton class for managing the asyncpg connection pool."""

    _instance = None
    _pool: Optional[asyncpg.Pool] = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(UnifiedDBManager, cls).__new__(cls)
        return cls._instance

    async def initialize(self) -> bool:
        """
        Initializes the database connection pool.

        Returns:
            True if initialization was successful, False otherwise.
        """
        if self._pool and not self._pool._closed:
            logger.info("Database pool already initialized.")
            return True

        config = get_config()
        db_url = config.get('database', 'url')

        if not db_url:
            logger.error("Database URL is not configured. Cannot initialize pool.")
            return False

        try:
            self._pool = await asyncpg.create_pool(
                dsn=db_url,
                min_size=config.getint('database', 'min_pool_size', 1),
                max_size=config.getint('database', 'max_pool_size', 10),
                timeout=config.getint('database', 'connect_timeout', 30),
                command_timeout=config.getint('database', 'command_timeout', 60)
            )
            logger.info("Database connection pool initialized successfully.")
            return True
        except (asyncpg.PostgresError, OSError) as e:
            logger.error(f"Failed to initialize database pool: {e}", exc_info=True)
            self._pool = None
            return False

    async def close(self):
        """Closes the database connection pool."""
        if self._pool and not self._pool._closed:
            await self._pool.close()
            self._pool = None
            logger.info("Database connection pool closed.")

    def get_pool(self) -> Optional[asyncpg.Pool]:
        """
        Returns the connection pool.

        Raises:
            ConnectionError: If the pool is not initialized.

        Returns:
            The asyncpg.Pool instance.
        """
        if self._pool is None:
            raise ConnectionError("Database pool is not initialized. Call initialize() first.")
        return self._pool

    @asynccontextmanager
    async def get_connection(self):
        """
        Provides a connection from the pool within a context manager.

        Yields:
            An asyncpg.Connection object.
        """
        pool = self.get_pool()
        conn = await pool.acquire()
        try:
            yield conn
        finally:
            await pool.release(conn)

    @asynccontextmanager
    async def get_transaction(self):
        """
        Provides a connection with a transaction from the pool.

        Yields:
            An asyncpg.Connection object within a transaction.
        """
        pool = self.get_pool()
        conn = await pool.acquire()
        try:
            async with conn.transaction():
                yield conn
        finally:
            await pool.release(conn)

    async def execute(self, query: str, *args) -> int:
        """
        Execute a query that does not return rows (e.g., INSERT, UPDATE, DELETE).

        Returns:
            The number of rows affected.
        """
        async with self.get_connection() as conn:
            status = await conn.execute(query, *args)
            # 'DELETE 1' -> '1'
            return int(status.split()[-1]) if status.split() else 0

    async def fetch(self, query: str, *args) -> List[asyncpg.Record]:
        """Execute a query and fetch all results."""
        async with self.get_connection() as conn:
            return await conn.fetch(query, *args)

    async def fetchrow(self, query: str, *args) -> Optional[asyncpg.Record]:
        """Execute a query and fetch the first result."""
        async with self.get_connection() as conn:
            return await conn.fetchrow(query, *args)

    async def fetchval(self, query: str, *args) -> Any:
        """Execute a query and fetch a single scalar value."""
        async with self.get_connection() as conn:
            return await conn.fetchval(query, *args)

    async def executemany(self, command: str, args: List[Tuple]):
        """
        Execute a command for each set of parameters in a sequence.
        This is efficient for bulk inserts/updates.
        """
        async with self.get_connection() as conn:
            await conn.executemany(command, args)

# Global instance of the database manager
bot_db_manager = UnifiedDBManager()