"""
Token Validator for Trading Bot

This module provides functionality to validate various types of tokens
including API keys, authentication tokens, and rate limiting based on tokens.
"""
import asyncio
import logging
import secrets
import hashlib
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Set
from enum import Enum
import re
from src.shared.error_handling.logging import get_logger

logger = get_logger(__name__)


class TokenType(Enum):
    """Types of tokens that can be validated."""
    API_KEY = "api_key"
    BEARER_TOKEN = "bearer_token"
    SESSION_TOKEN = "session_token"
    CUSTOM = "custom"


class ValidationError(Exception):
    """Raised when a token validation fails."""
    pass


class TokenValidator:
    """Validates and manages tokens for the trading bot."""
    
    def __init__(self):
        self._valid_tokens: Set[str] = set()
        self._token_metadata: Dict[str, Dict[str, Any]] = {}
        self._rate_limits: Dict[str, Dict[str, int]] = {}
        self._logger = get_logger("token_validator")
        self._blocked_tokens: Set[str] = set()
        self._token_usage: Dict[str, Dict[str, Any]] = {}
    
    def add_valid_token(self, token: str, token_type: TokenType, metadata: Optional[Dict[str, Any]] = None) -> bool:
        """Add a valid token with associated metadata."""
        try:
            # Normalize the token for consistent handling
            normalized_token = token.strip()
            
            # Validate token format based on type
            if not self._validate_token_format(normalized_token, token_type):
                raise ValidationError(f"Invalid format for {token_type.value} token")
            
            self._valid_tokens.add(normalized_token)
            self._token_metadata[normalized_token] = {
                'type': token_type.value,
                'created_at': datetime.now(),
                'last_used': None,
                **(metadata or {})
            }
            
            self._logger.info(f"Added new {token_type.value} token")
            return True
            
        except Exception as e:
            self._logger.error(f"Failed to add token: {e}")
            return False
    
    def remove_token(self, token: str) -> bool:
        """Remove a token from the valid tokens set."""
        normalized_token = token.strip()
        if normalized_token in self._valid_tokens:
            self._valid_tokens.remove(normalized_token)
            if normalized_token in self._token_metadata:
                del self._token_metadata[normalized_token]
            if normalized_token in self._rate_limits:
                del self._rate_limits[normalized_token]
            self._logger.info(f"Removed token: {normalized_token}")
            return True
        return False
    
    def block_token(self, token: str, reason: str = "Unknown") -> bool:
        """Block a token from being used."""
        normalized_token = token.strip()
        if normalized_token in self._valid_tokens:
            self._blocked_tokens.add(normalized_token)
            self._logger.warning(f"Blocked token: {normalized_token}, reason: {reason}")
            return True
        return False
    
    def _validate_token_format(self, token: str, token_type: TokenType) -> bool:
        """Validate the format of a token based on its type."""
        if not token:
            return False
        
        if token_type == TokenType.API_KEY:
            # API keys typically have a specific format, often starting with 'sk-' or 'ak-'
            return re.match(r'^[a-zA-Z0-9_-]+', token) is not None and len(token) >= 16
        elif token_type == TokenType.BEARER_TOKEN:
            # Bearer tokens are often JWTs or random strings
            return len(token) >= 20  # Basic length check
        elif token_type == TokenType.SESSION_TOKEN:
            # Session tokens might have a specific format
            return len(token) >= 32 and re.match(r'^[a-zA-Z0-9]+', token) is not None
        elif token_type == TokenType.CUSTOM:
            # For custom tokens, just ensure it's not empty
            return len(token) > 0
        
        return False
    
    async def validate_token(self, token: str, requires_rate_limit: bool = False) -> Dict[str, Any]:
        """Validate a token and return its metadata."""
        normalized_token = token.strip()
        
        # Check if token is blocked
        if normalized_token in self._blocked_tokens:
            raise ValidationError("Token is blocked")
        
        # Check if token exists in valid tokens
        if normalized_token not in self._valid_tokens:
            raise ValidationError("Invalid token")
        
        # Get token metadata
        metadata = self._token_metadata[normalized_token].copy()
        
        # Update last used timestamp
        self._token_metadata[normalized_token]['last_used'] = datetime.now()
        
        # Check rate limits if required
        if requires_rate_limit:
            await self._check_rate_limit(normalized_token)
        
        # Update usage statistics
        self._update_usage_stats(normalized_token)
        
        self._logger.debug(f"Token validation successful: {normalized_token[:10]}...")
        return metadata
    
    async def _check_rate_limit(self, token: str) -> bool:
        """Check if the token is within rate limits."""
        # Get rate limit settings from metadata
        metadata = self._token_metadata.get(token, {})
        rate_limit_config = metadata.get('rate_limit', {
            'requests': 100,  # per hour
            'window': 3600    # seconds
        })
        
        current_time = time.time()
        window_start = current_time - rate_limit_config['window']
        
        # Get request counts for this token
        if token not in self._rate_limits:
            self._rate_limits[token] = {'timestamps': []}
        
        # Clean old timestamps
        self._rate_limits[token]['timestamps'] = [
            ts for ts in self._rate_limits[token]['timestamps'] 
            if ts > window_start
        ]
        
        # Check if within limits
        if len(self._rate_limits[token]['timestamps']) >= rate_limit_config['requests']:
            raise ValidationError(f"Rate limit exceeded: {rate_limit_config['requests']} requests per {rate_limit_config['window']} seconds")
        
        # Add current request timestamp
        self._rate_limits[token]['timestamps'].append(current_time)
        
        return True
    
    def _update_usage_stats(self, token: str):
        """Update usage statistics for a token."""
        if token not in self._token_usage:
            self._token_usage[token] = {
                'requests_count': 0,
                'first_used': datetime.now(),
                'last_used': datetime.now()
            }
        
        self._token_usage[token]['requests_count'] += 1
        self._token_usage[token]['last_used'] = datetime.now()
    
    def generate_api_key(self, prefix: str = "tkn_", length: int = 32) -> str:
        """Generate a new API key."""
        # Create a secure random token
        random_bytes = secrets.token_bytes(length)
        token = prefix + hashlib.sha256(random_bytes).hexdigest()[:length-len(prefix)]
        return token
    
    def generate_session_token(self, user_id: str) -> str:
        """Generate a session token for a user."""
        # Combine user ID with timestamp and random data
        timestamp = str(time.time())
        random_data = secrets.token_urlsafe(16)
        session_data = f"{user_id}:{timestamp}:{random_data}"
        return hashlib.sha256(session_data.encode()).hexdigest()
    
    def get_token_usage_stats(self, token: str) -> Optional[Dict[str, Any]]:
        """Get usage statistics for a specific token."""
        return self._token_usage.get(token)
    
    def get_all_token_usage_stats(self) -> Dict[str, Dict[str, Any]]:
        """Get usage statistics for all tokens."""
        return self._token_usage.copy()
    
    def is_valid_token(self, token: str) -> bool:
        """Quick check to see if a token is valid (without raising exception)."""
        try:
            self.validate_token(token)
            return True
        except ValidationError:
            return False
    
    async def validate_and_get_metadata(self, token: str, requires_rate_limit: bool = False) -> Optional[Dict[str, Any]]:
        """Validate a token and return its metadata, or None if invalid."""
        try:
            return await self.validate_token(token, requires_rate_limit)
        except ValidationError:
            return None
    
    def get_rate_limit_info(self, token: str) -> Dict[str, int]:
        """Get rate limit information for a token."""
        if token in self._rate_limits:
            timestamps = self._rate_limits[token]['timestamps']
            current_time = time.time()
            window_start = current_time - 3600  # Last hour
            
            recent_requests = [ts for ts in timestamps if ts > window_start]
            
            metadata = self._token_metadata.get(token, {})
            rate_limit_config = metadata.get('rate_limit', {
                'requests': 100,
                'window': 3600
            })
            
            return {
                'used': len(recent_requests),
                'limit': rate_limit_config['requests'],
                'remaining': max(0, rate_limit_config['requests'] - len(recent_requests)),
                'reset_time': int(window_start + rate_limit_config['window'])
            }
        else:
            return {
                'used': 0,
                'limit': 100,
                'remaining': 100,
                'reset_time': int(time.time() + 3600)
            }


# Global token validator instance
token_validator = TokenValidator()


async def validate_token(token: str, requires_rate_limit: bool = False) -> Dict[str, Any]:
    """Convenience function to validate a token."""
    return await token_validator.validate_token(token, requires_rate_limit)


def is_valid_token(token: str) -> bool:
    """Convenience function to check if a token is valid."""
    return token_validator.is_valid_token(token)


def generate_api_key(prefix: str = "tkn_") -> str:
    """Convenience function to generate an API key."""
    return token_validator.generate_api_key(prefix)


async def get_rate_limit_info(token: str) -> Dict[str, int]:
    """Convenience function to get rate limit info."""
    return token_validator.get_rate_limit_info(token)