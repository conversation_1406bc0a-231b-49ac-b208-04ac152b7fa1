"""
Permissions Management System

Provides a flexible and robust permissions system for controlling access
to bot commands and features based on user roles, subscription tiers,
and server-specific settings.
"""

import logging
from enum import Enum
from typing import Optional, Tuple, Dict, Any
from functools import wraps
import discord

from src.shared.error_handling.logging import get_logger
from src.bot.database_manager import bot_db_manager

logger = get_logger(__name__)

class PermissionLevel(Enum):
    """Defines different permission levels for bot commands."""
    ANYONE = 0
    SUBSCRIBER = 10
    PAID = 20
    VIP = 30
    MODERATOR = 40
    ADMIN = 50
    OWNER = 60

class DiscordPermissionChecker:
    """Checks user permissions for Discord commands."""

    def __init__(self):
        self.db_manager = bot_db_manager
        self.permission_cache: Dict[str, Tuple[PermissionLevel, float]] = {}
        self.cache_ttl = 300  # 5 minutes

    async def get_user_permission_level(self, user: discord.User, guild: Optional[discord.Guild] = None) -> PermissionLevel:
        """
        Determines the permission level of a user.

        Args:
            user: The Discord user.
            guild: The Discord guild (server), if applicable.

        Returns:
            The user's permission level.
        """
        import time
        user_id = str(user.id)
        cache_key = f"{user_id}:{guild.id if guild else 'dm'}"

        # Check cache first
        if cache_key in self.permission_cache:
            level, timestamp = self.permission_cache[cache_key]
            if time.time() - timestamp < self.cache_ttl:
                return level

        # Owner check
        if await self.is_owner(user):
            level = PermissionLevel.OWNER
        # Admin check (in a guild context)
        elif guild and user.guild_permissions.administrator:
            level = PermissionLevel.ADMIN
        # Moderator check (e.g., has 'manage_messages' permission)
        elif guild and user.guild_permissions.manage_messages:
            level = PermissionLevel.MODERATOR
        # Paid/VIP/Subscriber check (from database)
        elif await self.is_paid_user(user_id):
            # This could be more granular (e.g., check for VIP tier)
            level = PermissionLevel.PAID
        else:
            level = PermissionLevel.ANYONE

        # Cache the result
        self.permission_cache[cache_key] = (level, time.time())
        return level

    def has_permission(
        self,
        user: discord.User,
        required_level: PermissionLevel,
        guild: Optional[discord.Guild] = None,
        guild_id: Optional[str] = None
    ) -> Tuple[bool, str]:
        """
        Synchronous check if a user has the required permission level.
        Note: This is a simplified check and does not perform async operations.
        For full checks, use `check_permission`.
        """
        # This is a basic, non-async check suitable for some contexts.
        # It won't check the database for paid status.
        if guild and isinstance(user, discord.Member):
            if user.guild_permissions.administrator:
                user_level = PermissionLevel.ADMIN
            elif user.guild_permissions.manage_messages:
                user_level = PermissionLevel.MODERATOR
            else:
                user_level = PermissionLevel.ANYONE
        else:
            user_level = PermissionLevel.ANYONE

        if user_level.value >= required_level.value:
            return True, ""
        else:
            return False, f"You need `{required_level.name}` permission. You have `{user_level.name}`."

    async def check_permission(
        self,
        user: discord.User,
        required_level: PermissionLevel,
        guild: Optional[discord.Guild] = None
    ) -> Tuple[bool, str]:
        """
        Asynchronously checks if a user has the required permission level.

        Returns:
            A tuple of (bool, str) indicating if permission is granted and a reason if not.
        """
        user_level = await self.get_user_permission_level(user, guild)
        if user_level.value >= required_level.value:
            return True, ""
        else:
            return False, f"This command requires `{required_level.name}` permission. Your current level is `{user_level.name}`."

    async def is_owner(self, user: discord.User) -> bool:
        """Checks if a user is the bot owner."""
        # In a real application, this would be checked against a configured owner ID
        # For now, let's assume the bot object is accessible, which it isn't here.
        # This is a placeholder for a real implementation.
        # return await self.bot.is_owner(user)
        return False # Placeholder

    async def is_paid_user(self, user_id: str) -> bool:
        """Checks if a user has a paid subscription via the database."""
        if not self.db_manager or not self.db_manager._pool:
            logger.warning("Database manager not initialized, cannot check paid status.")
            return False
        try:
            query = "SELECT 1 FROM subscriptions WHERE user_id = $1 AND status = 'active' AND expires_at > NOW()"
            result = await self.db_manager.fetchval(query, user_id)
            return result is not None
        except Exception as e:
            logger.error(f"Database error checking paid status for user {user_id}: {e}")
            return False

def require_permission(required_level: PermissionLevel):
    """
    A decorator for discord.py commands to enforce permission levels.

    Example:
        @app_commands.command(name="vip_command")
        @require_permission(PermissionLevel.VIP)
        async def vip_command(self, interaction: discord.Interaction):
            ...
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(cog_instance, interaction: discord.Interaction, *args, **kwargs):
            # Assumes the cog has a 'permission_checker' attribute
            checker = getattr(cog_instance, 'permission_checker', None)
            if not checker:
                logger.error("Permission checker not found on cog instance.")
                await interaction.response.send_message(
                    "Permission system is currently unavailable.", ephemeral=True
                )
                return

            has_perm, reason = await checker.check_permission(
                interaction.user, required_level, interaction.guild
            )

            if has_perm:
                return await func(cog_instance, interaction, *args, **kwargs)
            else:
                await interaction.response.send_message(
                    f"❌ Access Denied: {reason}", ephemeral=True
                )
        return wrapper
    return decorator