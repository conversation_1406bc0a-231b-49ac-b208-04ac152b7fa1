"""
Bot Client Initialization Module (Placeholder)

Contains the factory function to create the main bot instance.
"""
"""Compatibility entrypoint for creating the TradingBot instance.

This module used to contain a placeholder implementation. Prefer the
well-maintained core implementation in `src.bot.core.bot` and expose a
compatibility factory for existing callers.
"""

from typing import Optional

from src.bot.core.bot import create_bot as create_core_bot


def create_bot() -> Optional[object]:
    """Create a TradingBot instance using the core implementation.

    Returns the higher-level TradingBot object from `src.bot.core.bot`.
    """
    return create_core_bot()
