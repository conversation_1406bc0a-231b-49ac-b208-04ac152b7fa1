"""
Metrics Collector for Trading Bot

This module provides functionality to collect, store, and report various metrics
related to bot performance, user interactions, and system health.
"""
import asyncio
import logging
import time
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from enum import Enum
from collections import defaultdict
import json
from src.shared.error_handling.logging import get_logger

logger = get_logger(__name__)


class MetricType(Enum):
    """Types of metrics that can be collected."""
    COUNTER = "counter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"
    SUMMARY = "summary"


@dataclass
class MetricData:
    """Data structure for a single metric."""
    name: str
    type: MetricType
    value: float
    labels: Dict[str, str]
    timestamp: datetime
    description: Optional[str] = None


class MetricsCollector:
    """Collects and manages various metrics for the trading bot."""
    
    def __init__(self):
        self._metrics: Dict[str, List[MetricData]] = defaultdict(list)
        self._gauges: Dict[str, float] = {}
        self._counters: Dict[str, float] = {}
        self._logger = get_logger("metrics")
        self._correlation_id = None
    
    def set_correlation_id(self, correlation_id: str):
        """Set correlation ID for tracking metrics across requests."""
        self._correlation_id = correlation_id
    
    def increment_counter(self, name: str, labels: Optional[Dict[str, str]] = None, value: float = 1.0):
        """Increment a counter metric."""
        labels = labels or {}
        if self._correlation_id:
            labels['correlation_id'] = self._correlation_id
            
        key = f"{name}_{str(sorted(labels.items()))}"
        self._counters[key] = self._counters.get(key, 0) + value
        
        metric = MetricData(
            name=name,
            type=MetricType.COUNTER,
            value=self._counters[key],
            labels=labels,
            timestamp=datetime.now(),
            description=f"Counter for {name} with value {self._counters[key]}"
        )
        self._metrics[name].append(metric)
        
        self._logger.debug(f"Counter {name} incremented to {self._counters[key]} with labels: {labels}")
    
    def set_gauge(self, name: str, value: float, labels: Optional[Dict[str, str]] = None):
        """Set a gauge metric."""
        labels = labels or {}
        if self._correlation_id:
            labels['correlation_id'] = self._correlation_id
            
        key = f"{name}_{str(sorted(labels.items()))}"
        self._gauges[key] = value
        
        metric = MetricData(
            name=name,
            type=MetricType.GAUGE,
            value=value,
            labels=labels,
            timestamp=datetime.now(),
            description=f"Gauge for {name} with value {value}"
        )
        self._metrics[name].append(metric)
        
        self._logger.debug(f"Gauge {name} set to {value} with labels: {labels}")
    
    def record_histogram(self, name: str, value: float, labels: Optional[Dict[str, str]] = None):
        """Record a value in a histogram metric."""
        labels = labels or {}
        if self._correlation_id:
            labels['correlation_id'] = self._correlation_id
            
        metric = MetricData(
            name=name,
            type=MetricType.HISTOGRAM,
            value=value,
            labels=labels,
            timestamp=datetime.now(),
            description=f"Histogram value for {name}: {value}"
        )
        self._metrics[name].append(metric)
        
        self._logger.debug(f"Histogram value {value} recorded for {name} with labels: {labels}")
    
    def get_counter_value(self, name: str, labels: Optional[Dict[str, str]] = None) -> float:
        """Get the current value of a counter."""
        labels = labels or {}
        if self._correlation_id:
            labels['correlation_id'] = self._correlation_id
            
        key = f"{name}_{str(sorted(labels.items()))}"
        return self._counters.get(key, 0)
    
    def get_gauge_value(self, name: str, labels: Optional[Dict[str, str]] = None) -> float:
        """Get the current value of a gauge."""
        labels = labels or {}
        if self._correlation_id:
            labels['correlation_id'] = self._correlation_id
            
        key = f"{name}_{str(sorted(labels.items()))}"
        return self._gauges.get(key, 0)
    
    def get_metrics_by_name(self, name: str) -> List[MetricData]:
        """Get all metrics with the given name."""
        return self._metrics.get(name, [])
    
    def get_all_metrics(self) -> Dict[str, List[MetricData]]:
        """Get all collected metrics."""
        return dict(self._metrics)
    
    def clear_metrics(self, name: Optional[str] = None):
        """Clear metrics, either all or for a specific name."""
        if name:
            if name in self._metrics:
                del self._metrics[name]
            # Also clear from internal storage
            keys_to_remove = [k for k in self._counters.keys() if k.startswith(name)]
            for key in keys_to_remove:
                del self._counters[key]
            keys_to_remove = [k for k in self._gauges.keys() if k.startswith(name)]
            for key in keys_to_remove:
                del self._gauges[key]
        else:
            self._metrics.clear()
            self._counters.clear()
            self._gauges.clear()
    
    async def record_pipeline_stage_duration(self, stage_name: str, duration: float, labels: Optional[Dict[str, str]] = None):
        """Record the duration of a pipeline stage."""
        labels = labels or {}
        labels['stage'] = stage_name
        
        self.record_histogram(
            "pipeline_stage_duration_seconds",
            duration,
            labels
        )
        self._logger.info(f"Pipeline stage {stage_name} took {duration:.3f}s")
    
    async def record_query_processing(self, query_type: str, duration: float, success: bool = True):
        """Record metrics for query processing."""
        labels = {'query_type': query_type, 'success': str(success)}
        
        # Record duration
        self.record_histogram(
            "query_processing_duration_seconds",
            duration,
            labels
        )
        
        # Increment counter
        status = 'success' if success else 'failure'
        self.increment_counter(
            "query_processed_total",
            {'query_type': query_type, 'status': status}
        )
    
    async def record_api_call(self, endpoint: str, duration: float, status_code: int):
        """Record metrics for API calls."""
        labels = {'endpoint': endpoint, 'status_code': str(status_code)}
        
        # Record duration
        self.record_histogram(
            "api_call_duration_seconds",
            duration,
            labels
        )
        
        # Increment counter
        self.increment_counter(
            "api_calls_total",
            labels
        )
    
    async def record_data_fetch(self, provider: str, symbol: str, success: bool = True):
        """Record metrics for data fetch operations."""
        labels = {'provider': provider, 'symbol': symbol, 'success': str(success)}
        
        # Increment counter
        self.increment_counter(
            "data_fetches_total",
            labels
        )
        
        self._logger.info(f"Data fetch recorded: provider={provider}, symbol={symbol}, success={success}")
    
    async def get_metrics_summary(self) -> Dict[str, Any]:
        """Get a summary of key metrics."""
        summary = {
            'timestamp': datetime.now().isoformat(),
            'counters': {},
            'gauges': {},
            'total_metrics': len([m for sublist in self._metrics.values() for m in sublist])
        }
        
        # Include non-zero counters
        for key, value in self._counters.items():
            if value > 0:
                summary['counters'][key] = value
        
        # Include non-zero gauges
        for key, value in self._gauges.items():
            if value != 0:
                summary['gauges'][key] = value
        
        return summary


# Global metrics collector instance
metrics_collector = MetricsCollector()


async def increment_counter(name: str, labels: Optional[Dict[str, str]] = None, value: float = 1.0):
    """Convenience function to increment a counter."""
    metrics_collector.increment_counter(name, labels, value)


async def set_gauge(name: str, value: float, labels: Optional[Dict[str, str]] = None):
    """Convenience function to set a gauge."""
    metrics_collector.set_gauge(name, value, labels)


async def record_histogram(name: str, value: float, labels: Optional[Dict[str, str]] = None):
    """Convenience function to record a histogram value."""
    metrics_collector.record_histogram(name, value, labels)


async def get_metrics_summary() -> Dict[str, Any]:
    """Convenience function to get metrics summary."""
    return await metrics_collector.get_metrics_summary()