"""
Compliance and Risk Disclaimer Management

This module manages all compliance-related content including risk disclaimers,
regulatory warnings, and educational disclaimers for different risk levels
and trading contexts.
"""

from typing import Dict, List, Optional
from pathlib import Path

class ComplianceManager:
    """Manages compliance disclaimers and risk warnings"""
    
    def __init__(self, templates_dir: Optional[Path] = None):
        """Initialize compliance manager"""
        self.templates_dir = templates_dir or Path(__file__).parent.parent / "templates" / "compliance"
        self.disclaimers = self._load_disclaimers()
    
    def _load_disclaimers(self) -> Dict[str, str]:
        """Load disclaimer templates"""
        disclaimers = {
            "standard": self._get_standard_disclaimer(),
            "high": self._get_high_risk_disclaimer(),
            "educational": self._get_educational_disclaimer(),
            "options": self._get_options_disclaimer(),
            "very_high": self._get_very_high_risk_disclaimer()
        }
        
        # Load custom disclaimers from files if they exist
        if self.templates_dir.exists():
            for disclaimer_file in self.templates_dir.glob("*.txt"):
                disclaimer_name = disclaimer_file.stem
                disclaimers[disclaimer_name] = disclaimer_file.read_text()
        
        return disclaimers
    
    def _get_standard_disclaimer(self) -> str:
        """Standard risk disclaimer for general trading content"""
        return """## Compliance Requirements:
Every response MUST include appropriate risk disclaimers:
- "This is educational content, not financial advice"
- "Past performance doesn't guarantee future results"  
- "Trading involves substantial risk of loss"
- "Consult a financial advisor for personalized advice"
- Position sizing recommendations (1-3% risk per trade maximum)

⚠️ IMPORTANT: This is educational content, not personalized financial advice. Trading involves substantial risk of loss. Past performance doesn't guarantee future results. Consult a qualified financial advisor for investment decisions."""
    
    def _get_high_risk_disclaimer(self) -> str:
        """High risk disclaimer for aggressive strategies"""
        return """## High Risk Compliance Requirements:
MANDATORY risk warnings for high-risk content:
- "HIGH RISK: This strategy involves significant risk of loss"
- "Only trade with capital you can afford to lose completely"
- "Use proper position sizing (1-3% account risk maximum)"
- "This is educational content, not financial advice"
- "Past performance doesn't guarantee future results"
- "Consult a financial advisor before implementing any strategy"

⚠️ HIGH RISK WARNING: This strategy involves significant risk of loss. Only trade with capital you can afford to lose completely. Use proper position sizing (1-3% account risk maximum). This is educational content, not personalized financial advice. Consult a qualified financial advisor before implementing any trading strategy."""
    
    def _get_educational_disclaimer(self) -> str:
        """Educational disclaimer for learning content"""
        return """## Educational Compliance:
Educational content requirements:
- "This is for educational purposes only"
- "Not personalized financial advice"
- "Practice with paper trading before using real money"
- "Understand the risks before trading"
- "Seek professional guidance for your specific situation"

📚 EDUCATIONAL PURPOSE: This analysis teaches trading concepts and methodologies. Always conduct your own research and risk assessment before making investment decisions. Practice with paper trading before using real money."""
    
    def _get_options_disclaimer(self) -> str:
        """Specific disclaimer for options trading content"""
        return """## Options Trading Compliance:
MANDATORY options-specific warnings:
- "Options trading involves substantial risk and is not suitable for all investors"
- "You may lose your entire investment in options"
- "Understand the risks before trading options"
- "Options strategies can result in unlimited losses"
- "This is educational content, not financial advice"
- "Consult a financial advisor experienced with options"

⚠️ OPTIONS RISK: Options trading involves substantial risk and is not suitable for all investors. You may lose your entire investment. Options strategies can result in unlimited losses. Understand the risks before trading options. This is educational content, not financial advice."""
    
    def _get_very_high_risk_disclaimer(self) -> str:
        """Very high risk disclaimer for extremely risky strategies"""
        return """## EXTREME RISK Compliance:
CRITICAL warnings for very high-risk content:
- "EXTREME RISK: This strategy can result in total loss of capital"
- "Not suitable for most investors"
- "Requires extensive experience and risk management"
- "Can result in losses exceeding initial investment"
- "This is educational content only - NOT investment advice"
- "Professional guidance strongly recommended"

🚨 EXTREME RISK: This strategy can result in total loss of capital and is not suitable for most investors. Requires extensive experience and sophisticated risk management. Can result in losses exceeding initial investment. Professional guidance strongly recommended."""
    
    def get_compliance_fragment(self, disclaimer_level: str = "standard") -> str:
        """Get compliance fragment for specified risk level"""
        return self.disclaimers.get(disclaimer_level, self.disclaimers["standard"])
    
    def get_intent_specific_compliance(self, intent: str) -> str:
        """Get compliance disclaimer specific to trading intent"""
        intent_compliance_mapping = {
            "price_check": "standard",
            "technical_analysis": "standard", 
            "fundamental_analysis": "standard",
            "options_strategy": "options",
            "market_overview": "standard",
            "risk_management": "educational",
            "educational": "educational"
        }
        
        disclaimer_level = intent_compliance_mapping.get(intent, "standard")
        return self.get_compliance_fragment(disclaimer_level)
    
    def get_risk_level_compliance(self, risk_level: str) -> str:
        """Get compliance disclaimer based on risk level"""
        risk_compliance_mapping = {
            "low": "educational",
            "medium": "standard",
            "high": "high", 
            "very_high": "very_high"
        }
        
        disclaimer_level = risk_compliance_mapping.get(risk_level, "standard")
        return self.get_compliance_fragment(disclaimer_level)
    
    def get_compliance_templates(self) -> Dict[str, str]:
        """Get all available compliance templates"""
        return {
            "standard_disclaimer": "⚠️ IMPORTANT: This is educational content, not personalized financial advice. Trading involves substantial risk of loss. Past performance doesn't guarantee future results. Consult a qualified financial advisor for investment decisions.",
            
            "options_warning": "⚠️ OPTIONS RISK: Options trading involves substantial risk and is not suitable for all investors. You may lose your entire investment. Understand the risks before trading options.",
            
            "high_risk_warning": "⚠️ HIGH RISK: This strategy involves significant risk of loss. Only trade with capital you can afford to lose completely. Use proper position sizing (1-3% account risk maximum).",
            
            "educational_disclaimer": "📚 EDUCATIONAL PURPOSE: This analysis teaches trading concepts and methodologies. Always conduct your own research and risk assessment before making investment decisions.",
            
            "position_sizing": "💡 POSITION SIZING: Never risk more than 1-3% of your account on any single trade. Use proper stop-losses and position sizing to manage risk.",
            
            "paper_trading": "📝 PRACTICE FIRST: Consider practicing these strategies with paper trading before using real money to gain experience and confidence.",
            
            "professional_advice": "👨‍💼 SEEK GUIDANCE: Consult with a qualified financial advisor who understands your specific financial situation and risk tolerance.",
            
            "market_volatility": "📊 MARKET RISK: Markets can be volatile and unpredictable. Past performance does not guarantee future results.",
            
            "regulatory_notice": "⚖️ REGULATORY: This content is for educational purposes only and does not constitute investment advice or recommendations.",
            
            "data_accuracy": "📈 DATA DISCLAIMER: Market data and analysis are based on available information and may not reflect real-time conditions."
        }
    
    def build_comprehensive_disclaimer(self, 
                                     intent: str,
                                     risk_level: str,
                                     includes_options: bool = False,
                                     includes_high_risk: bool = False) -> str:
        """Build a comprehensive disclaimer based on content characteristics"""
        disclaimers = []
        
        # Base disclaimer
        disclaimers.append(self.get_intent_specific_compliance(intent))
        
        # Risk level specific
        if risk_level in ["high", "very_high"]:
            disclaimers.append(self.get_risk_level_compliance(risk_level))
        
        # Options specific
        if includes_options or intent == "options_strategy":
            disclaimers.append(self.get_compliance_fragment("options"))
        
        # Additional high risk warning
        if includes_high_risk or risk_level == "very_high":
            disclaimers.append(self.get_compliance_fragment("very_high"))
        
        return "\n\n".join(set(disclaimers))  # Remove duplicates
    
    def validate_compliance(self, content: str) -> Dict[str, bool]:
        """Validate that content includes required compliance elements"""
        required_elements = [
            "educational content",
            "not financial advice", 
            "risk",
            "financial advisor"
        ]
        
        content_lower = content.lower()
        validation_results = {}
        
        for element in required_elements:
            validation_results[element] = element in content_lower
        
        return validation_results
