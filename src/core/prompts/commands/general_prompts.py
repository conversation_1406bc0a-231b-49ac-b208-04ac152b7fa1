"""
General Command Prompts

This module contains prompts for general bot commands and interactions
that don't fit into specific command categories.
"""

from typing import Dict, Any, Optional
from ..base.personas import <PERSON>a<PERSON>ana<PERSON>
from ..base.system_prompts import SystemPromptManager

class GeneralCommandPrompts:
    """Manages prompts for general bot commands"""
    
    def __init__(self):
        """Initialize general command prompts"""
        self.persona_manager = PersonaManager()
        self.system_prompt_manager = SystemPromptManager()
    
    def get_help_prompt(self) -> str:
        """Get prompt for help command responses"""
        return """You are a helpful trading bot assistant. Provide clear, concise help information about available commands and features.

Focus on:
- Available commands and their purposes
- How to format queries for best results
- Examples of effective questions
- Educational resources and learning paths

Keep responses organized, friendly, and educational."""
    
    def get_greeting_prompt(self) -> str:
        """Get prompt for greeting and welcome messages"""
        return """You are a professional trading assistant. Provide a warm, professional greeting that:

- Welcomes the user to the trading analysis platform
- Briefly explains your capabilities
- Suggests how to get started
- Maintains a professional but approachable tone

Keep it concise and encouraging."""
    
    def get_error_handling_prompt(self) -> str:
        """Get prompt for error handling and user guidance"""
        return """You are a helpful trading assistant handling an error or unclear request. Provide guidance that:

- Acknowledges the issue professionally
- Suggests specific ways to rephrase or clarify
- Offers alternative approaches
- Maintains a helpful, patient tone
- Includes examples of well-formed queries

Focus on helping the user succeed with their next attempt."""
    
    def get_educational_prompt(self, topic: str = "general") -> str:
        """Get prompt for educational content delivery"""
        return f"""You are an educational trading assistant explaining {topic}. Provide clear, educational content that:

- Explains concepts in accessible language
- Uses practical examples and analogies
- Builds from basic to advanced concepts
- Includes relevant warnings and considerations
- Encourages further learning and practice

Maintain an educational, patient tone while being thorough and accurate."""
