"""
Analyze Command Prompts

This module contains prompts specific to the /analyze command,
including comprehensive analysis prompts and configurations.
"""

from typing import Dict, Any, Optional
from ..base.personas import PersonaManager
from ..base.system_prompts import SystemPromptManager

class AnalyzeCommandPrompts:
    """Manages prompts specific to the /analyze command"""
    
    def __init__(self):
        """Initialize analyze command prompts"""
        self.persona_manager = PersonaManager()
        self.system_prompt_manager = SystemPromptManager()
    
    def get_system_prompt(self, 
                         analysis_type: str = "comprehensive",
                         context: Optional[Dict[str, Any]] = None) -> str:
        """
        Get system prompt for analyze command
        
        Args:
            analysis_type: Type of analysis (comprehensive, technical, fundamental)
            context: Additional context to inject
            
        Returns:
            System prompt for analyze command
        """
        # Select appropriate persona based on analysis type
        persona_mapping = {
            "comprehensive": "trading_expert",
            "technical": "trading_expert", 
            "fundamental": "market_analyst",
            "risk": "risk_analyst",
            "options": "options_specialist"
        }
        
        persona = persona_mapping.get(analysis_type, "trading_expert")
        
        return self.system_prompt_manager.get_system_prompt(
            persona=persona,
            context=context,
            include_json_format=False,  # Analyze command typically returns formatted text
            include_anti_fabrication=True
        )
    
    def get_analysis_configuration(self) -> Dict[str, Any]:
        """Get configuration for different analysis types"""
        return {
            "comprehensive": {
                "includes": ["technical", "fundamental", "sentiment", "risk"],
                "timeframes": ["short_term", "medium_term", "long_term"],
                "depth": "detailed",
                "persona": "trading_expert"
            },
            "technical": {
                "includes": ["indicators", "patterns", "support_resistance", "volume"],
                "timeframes": ["intraday", "short_term"],
                "depth": "technical_focused",
                "persona": "trading_expert"
            },
            "fundamental": {
                "includes": ["financials", "valuation", "growth", "sector_comparison"],
                "timeframes": ["medium_term", "long_term"],
                "depth": "fundamental_focused", 
                "persona": "market_analyst"
            },
            "risk": {
                "includes": ["volatility", "correlation", "drawdown", "position_sizing"],
                "timeframes": ["all"],
                "depth": "risk_focused",
                "persona": "risk_analyst"
            },
            "options": {
                "includes": ["volatility", "greeks", "strategies", "liquidity"],
                "timeframes": ["short_term"],
                "depth": "options_focused",
                "persona": "options_specialist"
            }
        }
