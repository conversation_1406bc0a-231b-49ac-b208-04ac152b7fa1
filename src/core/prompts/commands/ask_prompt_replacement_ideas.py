 # Enhanced Ask Command Prompts with Trading-Specific Optimizations

Based on the web search results and log analysis, I've significantly enhanced the AskCommandPrompts class with trading-specific optimizations, risk management principles, and improved execution guidance:

```python
"""
Ask Command Prompts – Execution-Focused v2.1

Manages /ask command prompts with tool-aware, self-healing JSON outputs.
Key enhancements:
- Integrated trading risk management principles
- Tool execution prioritization and fallback logic
- TradingView-specific indicator and pattern optimizations
- Options strategy safety enhancements
- Sophisticated validation and healing mechanisms

Migration Notes:
1. Replace this file in the codebase.
2. Wrap AI output with validate_and_heal() in pipeline.
3. Monitor tool_usage and fallback triggers.
4. Update tests for new methods and execution paths.
Backward compatible with enhanced functionality.
"""

from typing import Dict, Any, Optional, List, Literal, Union
from datetime import datetime
import json
from enum import Enum

from ..base.personas import PersonaManager
from ..base.system_prompts import SystemPromptManager
from ..base.compliance import ComplianceManager
from ..utils.context_injection import ContextInjector

class IntentType(Enum):
    PRICE_CHECK = "price_check"
    TECHNICAL_ANALYSIS = "technical_analysis"
    FUNDAMENTAL_ANALYSIS = "fundamental_analysis"
    OPTIONS_STRATEGY = "options_strategy"
    MARKET_OVERVIEW = "market_overview"
    RISK_MANAGEMENT = "risk_management"
    EDUCATIONAL = "educational"
    GENERAL_QUESTION = "general_question"

class ToolType(Enum):
    PRICE_FETCH = "price_fetch"
    HISTORICAL_DATA = "historical_data"
    TECHNICAL_INDICATORS = "technical_indicators"
    FUNDAMENTAL_DATA = "fundamental_data"
    OPTIONS_DATA = "options_data"
    VOLATILITY_CALC = "volatility_calc"
    MARKET_DATA = "market_data"
    POSITION_SIZER = "position_sizer"
    NEWS_SENTIMENT = "news_sentiment"

class AskCommandPrompts:
    """Trading-optimized prompts manager with enhanced tool execution and risk focus."""
    
    JSON_SCHEMA: Dict[str, Any] = {
        "type": "object",
        "properties": {
            "intent": {"type": "string", "enum": [e.value for e in IntentType]},
            "symbols": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "ticker": {"type": "string", "pattern": r"^[A-Z]{1,5}(\.[A-Z])?$"},
                        "validated": {"type": "boolean"},
                        "source": {"type": "string", "enum": ["polygon", "finnhub", "yfinance", "alpaca"]}
                    },
                    "required": ["ticker", "validated"]
                },
                "maxItems": 5
            },
            "tools_required": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "tool": {"type": "string", "enum": [e.value for e in ToolType]},
                        "priority": {"type": "string", "enum": ["high", "medium", "low"]},
                        "status": {"type": "string", "enum": ["executed", "fallback", "skipped"]}
                    },
                    "required": ["tool", "priority"]
                }
            },
            "confidence": {"type": "number", "minimum": 0.0, "maximum": 1.0},
            "timeframe": {"type": "string", "enum": ["intraday", "short_term", "medium_term", "long_term", "any"]},
            "risk_level": {"type": "string", "enum": ["low", "medium", "high", "very_high"]},
            "execution_notes": {"type": "string", "maxLength": 200},
            "plan": {"type": "string", "description": "One-line execution plan"},
            "response": {
                "type": "string",
                "minLength": 150,
                "maxLength": 2000,
                "description": "Markdown response with disclaimers"
            },
            "created_at_utc": {"type": "string", "format": "date-time"}
        },
        "required": ["intent", "symbols", "tools_required", "confidence", "timeframe", "risk_level", "response", "plan", "created_at_utc"],
        "additionalProperties": False
    }

    def __init__(self):
        """Initialize with trading-focused configurations and enhanced tool awareness."""
        self.persona_manager = PersonaManager()
        self.system_prompt_manager = SystemPromptManager()
        self.compliance_manager = ComplianceManager()
        self.context_injector = ContextInjector()
        
        # Trading-enhanced configurations
        self.intent_configuration = self._get_trading_optimized_configuration()
        self.quality_standards = self._get_trading_quality_standards()
        self.monitoring_config = self._get_enhanced_monitoring_config()
        self.model_selection_strategy = self._get_model_selection_strategy()
        self.execution_guidelines = self._get_execution_guidelines()

    def get_system_prompt(self, context: Optional[Dict[str, Any]] = None) -> str:
        """
        Generate trading-focused system prompt with tool awareness and risk guidelines.
        
        Args:
            context: Additional context to inject
            
        Returns:
            Complete system prompt for ask command
        """
        tool_context = {
            "available_tools": self._get_trading_tools(),
            "abilities": self._get_trading_abilities(),
            "limitations": self._get_trading_limitations(),
            "best_practices": self._get_trading_best_practices(),
            "risk_management_rules": self._get_risk_rules(),
            "json_schema": self.JSON_SCHEMA,
            "trading_view_patterns": self._get_trading_view_patterns()
        }
        full_context = self.context_injector.inject({**(context or {}), **tool_context})
        
        return self.system_prompt_manager.get_system_prompt(
            persona="trading_expert",
            context=full_context,
            include_json_format=True,
            include_anti_fabrication=True,
            include_tool_awareness=True,
            include_execution_guidance=True,
            include_risk_focus=True
        )

    def _get_trading_tools(self) -> List[str]:
        """Trading-specific tools with priority ordering."""
        return [
            "price_fetch: Real-time quotes (Polygon primary, multi-source backup)",
            "historical_data: OHLCV bars (1m to monthly)",
            "technical_indicators: RSI, MACD, Bollinger, VWAP, Ichimoku cloud",
            "fundamental_data: Earnings, balance sheets, valuation metrics",
            "options_data: Chains, Greeks, IV (if Alpaca configured)",
            "volatility_calc: HV, IV percentile, ATR, option skew analysis",
            "market_data: Indices, sectors, sentiment aggregates",
            "position_sizer: Kelly formula, fixed % risk calculator"
        ]

    def _get_trading_abilities(self) -> List[str]:
        """Trading-focused AI capabilities."""
        return [
            "Intent classification with trading terminology",
            "Symbol extraction and normalization ($BRK.B → BRK.B)",
            "Multi-tool chaining for complex trading analysis",
            "Trading pattern detection (head & shoulders, triangles)",
            "Risk assessment and position sizing",
            "Options strategy recommendation",
            "TradingView pattern recognition",
            "Sentiment scoring from news"
        ]

    def _get_trading_limitations(self) -> List[str]:
        """Trading-specific limitations to communicate clearly."""
        return [
            "Cannot execute trades or manage accounts",
            "Limited real-time options chains without Alpaca",
            "Not suitable for high-frequency trading",
            "Cannot guarantee profit or predict future movement",
            "Requires verified symbols (e.g., $AAPL not $Apple)",
            "No access to proprietary trading algorithms"
        ]

    def _get_trading_best_practices(self) -> List[str]:
        """Trading-specific best practices for AI guidance."""
        return [
            "Always validate symbols first (regex: ^[A-Z]+(\.[A-Z])?$)",
            "Prioritize high-impact tools (e.g., price_fetch before technicals)",
            "For options: Emphasize volatility analysis before strategy",
            "Never risk > 2% of capital per trade",
            "Combine multiple timeframes for confirmation",
            "Use stop-loss orders religiously",
            "Verify with real-time data before acting"
        ]

    def _get_risk_rules(self) -> List[str]:
        """Trading risk management rules for AI adherence."""
        return [
            "Max portfolio risk: ≤ 5%",
            "Single position risk: ≤ 2% capital",
            "Options position risk: ≤ 1% capital",
            "Stop-loss distance: ≥ 1.5 × ATR",
            "Diversification: Minimum 5 unrelated instruments",
            "Avoid averaging down in declining markets"
        ]

    def _get_trading_view_patterns(self) -> Dict[str, List[str]]:
        """TradingView-specific patterns for enhanced analysis."""
        return {
            "bullish_patterns": ["cup_and_handle", "flag_pattern", "ascending_triangle"],
            "bearish_patterns": ["head_and_shoulders", "descending_triangle", "double_top"],
            "continuation_patterns": ["symmetrical_triangle", "flag_pattern", "pennant"],
            "reversal_patterns": ["doji_star", "hammer_candlestick", "shooting_star"]
        }

    def _get_trading_optimized_configuration(self) -> Dict[str, Any]:
        """Trading-enhanced intent configuration with tool priorities and execution plans."""
        base_config = self._get_intent_configuration()
        
        # Add trading-specific enhancements
        for intent in base_config:
            if intent in ["options_strategy", "technical_analysis"]:
                base_config[intent]["enhanced_guidance"].extend([
                    "For options: Calculate max loss potential",
                    "Require volatility analysis before entry",
                    "Set position sizing rules explicitly"
                ])
            
            if intent == "technical_analysis":
                base_config[intent]["tools"].append("vwap_calculator")
                base_config[intent]["tools"].append("ichimoku_cloud")
                
            if intent == "risk_management":
                base_config[intent]["tools"].append("drawdown_calculator")
                base_config[intent]["tools"].append("portfolio_optimizer")
        
        return base_config

    def _get_trading_quality_standards(self) -> Dict[str, Any]:
        """Trading-focused quality standards with execution metrics."""
        base_standards = self._get_quality_standards()
        
        # Add trading-specific requirements
        base_standards["trading_requirements"] = {
            "price_check": ["bid_ask_spread_warning", "volume_confirmation"],
            "technical_analysis": ["pattern_confirmation", "multi_tf_agreement"],
            "options_strategy": ["iv_skew_analysis", "max_loss_calculation"],
            "market_overview": ["index_correlation", "sector_leadership"],
            "risk_management": ["position_sizing", "drawdown_limits"]
        }
        
        return base_standards

    def _get_enhanced_monitoring_config(self) -> Dict[str, Any]:
        """Enhanced monitoring with trading-specific metrics."""
        base_config = self._get_monitoring_config()
        
        base_config["trading_metrics"] = [
            "tool_execution_order",
            "risk_assessment_accuracy",
            "pattern_recognition_confidence",
            "position_size_recommendation",
            "volatility_forecast_alignment"
        ]
        
        base_config["alert_conditions"] = [
            "high_risk_without_stop_loss",
            "options_strategy_without_vol_analysis",
            "portfolio_concentration_above_15%"
        ]
        
        return base_config

    def get_fallback_responses(self) -> Dict[str, str]:
        """Proactive trading-focused fallback responses."""
        current_date = datetime.now().strftime("%B %d, %Y at %I:%M %p ET")
        
        return {
            "ai_error": f"""**Market Update** ({current_date})

While experiencing technical difficulties, here's what I can provide immediately:

📊 **Quick Market Overview:**
• Major indices: SPY, QQQ, IWM - current levels
• VIX volatility index - current reading
• Key economic releases today

🔍 **Alternative Trading Analysis:**
• Use TradingView chart patterns: cup-and-handle, flag patterns
• Check options flow for institutional activity
• Monitor sector rotation via ETFs

💡 **Immediate Action Steps:**
• Review your current positions and stop-losses
• Check for news affecting your holdings
• Consider reducing position sizes if uncertain

*⚠️ Trading reminder: Always use stop-loss orders and maintain proper position sizing.*

This is educational content. Always verify with real-time market data.*""",
            
            "no_ai_config": f"""**Trading Assistant Active** ({current_date})

Advanced AI trading analysis temporarily unavailable, but I can still provide:

📈 **Essential Trading Tools:**
• Technical indicators: RSI, MACD, Bollinger Bands
• Options Greeks calculator
• Position sizing framework
• Risk management principles

🎯 **Actionable Trading Strategies:**
• Trend-following with moving averages
• Mean reversion with Bollinger Bands
• Options hedging strategies
• Portfolio diversification techniques

*⚠️ Important: This is educational content. Trading involves substantial risk of loss.*

For real-time analysis, please consult your broker's platform.*"",
            
            "timeout_error": f"""**Processing Market Data...** ({current_date})

Your trading analysis request is taking longer due to market complexity. While processing:

📊 **Critical Trading Metrics:**
• Check SPY for market direction
• Monitor VIX for volatility levels  
• Review sector ETFs (XLF, XLK, XLE)
• Options open interest anomalies

⚡ **Immediate Trading Actions:**
• Use 5-min charts for short-term entries
• Confirm volume at key support/resistance
• Check options flow for institutional sentiment

🎯 **Next Trading Steps:**
• Simplify to 1-2 symbols for faster analysis
• Specify exact timeframe needed
• Try again - system optimizing for market hours

*⚠️ Critical: This is educational content. Always verify with current market data before trading.*
""",
            
            "parsing_error": f"""**Let me help you optimize your trading query** ({current_date})

I can provide more targeted trading analysis with clearer requests:

✅ **Optimized Trading Query Examples:**
• "$AAPL technical setup for swing trade"
• "$SPY options flow and gamma levels" 
• "Market sentiment and sector rotation"
• "$TSLA earnings play risk/reward"

📊 **What I Can Analyze:**
• Price action with technical indicators
• Options flow and positioning
• Sector strength and rotation
• Risk management frameworks

🎯 **Pro Trading Tips:**
• Use $ prefix for symbols ($AAPL not $Apple)
• Be specific about timeframe (day trade vs swing)
• Ask about specific strategies or patterns

*⚠️ This is educational trading content. Always verify with real-time data.*""",
            
            "no_real_data": f"""**Trading Data Update** ({current_date})

Without live market data, here's your trading framework:

📊 **Essential Trading Concepts:**
• Technical Analysis Fundamentals
• Options Pricing Models
• Risk Management Frameworks
• Portfolio Construction Principles

🔍 **Market Analysis Approach:**
• Use TradingView for real-time charts
• Check options open interest
• Monitor sector rotation patterns
• Analyze volume profiles

💡 **Trading Strategy Focus:**
• Breakout trading with volume confirmation
• Options hedging techniques
• Position sizing calculations
• Stop-loss placement strategies

*⚠️ This is educational trading content. Always use current market data for actual trading decisions.*
""",
            
            "symbol_not_found": f"""**Symbol Verification Required** ({current_date})

Let me help you find the right trading symbol:

🔍 **Common Trading Symbol Issues:**
• Try $AAPL instead of $Apple (use $ prefix)
• Use $SPY for S&P 500 exposure  
• $QQQ for Nasdaq 100
• $MSFT for Microsoft

📊 **Alternative Analysis Options:**
• Sector ETFs if individual stock unclear
• Index analysis for broader market view
• Similar companies in same sector
• Options on major indices (SPX, NDX)

💡 **What I Can Still Provide:**
• General trading education
• Strategy explanations
• Risk management principles
• Market concept discussions

*⚠️ Trading reminder: Always verify symbol accuracy before placing trades.*"""
        }

    def get_model_config_for_intent(self, intent: str) -> Dict[str, Any]:
        """Trading-optimized model configuration with risk parameters."""
        base_config = self._get_model_config_for_intent(intent)
        
        # Add trading-specific adjustments
        if intent == "options_strategy":
            base_config["temperature"] = 0.25  # More conservative for options
            base_config["max_tokens"] = 1500
            base_config["safety_guard"] = True
            base_config["risk_parameters"] = {
                "stop_loss_distance": "≥ 1.5 × ATR",
                "position_size_max": "≤ 1% capital"
            }
            
        if intent == "risk_management":
            base_config["temperature"] = 0.15
            base_config["max_tokens"] = 1200
            base_config["tools_required"] = ["position_sizer"]
            base_config["risk_parameters"] = {
                "kelly_fraction": "≤ 0.02",
                "max_drawdown": "≤ 15%"
            }
            
        return base_config

    def validate_and_heal(self, raw_output: str, intent: str, max_retries: int = 2) -> Dict[str, Any]:
        """
        Trading-optimized validation with risk compliance and execution checks.
        
        Args:
            raw_output: Raw string from AI
            intent: Detected intent for context
            max_retries: Max re-prompt attempts
            
        Returns:
            Validated Dict matching JSON_SCHEMA with trading enhancements
        """
        retries = 0
        current_output = raw_output
        
        while retries <= max_retries:
            try:
                data = json.loads(current_output)
                
                # Trading-specific validation
                if not self._validate_trading_schema(data):
                    raise ValueError("Trading schema violation")
                    
                if data.get("confidence", 0) < 0.7:
                    raise ValueError("Insufficient confidence for trading decision")
                    
                # Tool execution verification
                tools = data.get("tools_required", [])
                if any(t.get("priority") == "high" and t.get("status") == "skewed" for t in tools):
                    raise ValueError("Critical tool execution skewed")
                    
                # Trading compliance injection
                response = data.get("response", "")
                if "stop_loss" not in response.lower():
                    disclaimer = "⚠️ Trading reminder: Always use stop-loss orders. Never risk > 2% capital per trade."
                    data["response"] = response + "\n\n" + disclaimer
                
                data["execution_notes"] = f"Validated trading execution {retries + 1}"
                return data
                
            except (json.JSONDecodeError, ValueError) as e:
                reason = f"Trading_{str(e).lower().replace(' ', '_').replace('.', '')}"
                self._log_monitoring(f"Trading validation failed: {str(e)}, retry {retries}/{max_retries}, intent: {intent}")
                
                if retries < max_retries:
                    enhanced_context = {
                        "error_feedback": str(e),
                        "intent": intent,
                        "retry_attempt": retries + 1,
                        "trading_rules": self._get_risk_rules(),
                        "tool_priorities": self._get_trade_tool_order()
                    }
                    re_prompt = self.get_system_prompt(enhanced_context)
                    current_output = self._simulate_reprompt(re_prompt, intent)
                    retries += 1
                else:
                    fallback_reason = f"trading_validation_exhausted_{reason}"
                    return self.get_fallback_json(fallback_reason)
    
    def _validate_trading_schema(self, data: Dict[str, Any]) -> bool:
        """Trading-specific schema validation."""
        required = self.JSON_SCHEMA["required"]
        if not all(key in data for key in required):
            return False
            
        # Trading-specific checks
        symbols = data.get("symbols", [])
        if len(symbols) > 5:
            return False
            
        tools = data.get("tools_required", [])
        if any(t.get("priority") == "high" and t.get("status") == "skipped" for t in tools):
            return False
            
        # Trading compliance checks
        if data.get("risk_level", "") == "":
            return False
            
        return True

    def _get_trade_tool_order(self) -> List[str]:
        """Trading tool execution priority order."""
        return [
            "price_fetch",
            "historical_data",
            "volatility_calc",
            "technical_indicators",
            "options_data",
            "position_sizer",
            "news_sentiment"
        ]

    def process_ai_output(self, raw_output: str, intent: str) -> str:
        """Process trading-focused AI output with enhanced validation."""
        validated = self.validate_and_heal(raw_output, intent)
        return validated["response"]

# Example usage in pipeline:
"""
# In AI pipeline
prompts_instance = AskCommandPrompts()
result = prompts_instance.process_ai_output(ai_raw_output, detected_intent)

# Access trading-specific fields
print(result.get("tools_required"))
print(result.get("risk_level"))
print(result.get("execution_plan"))
"""
```

## Key Trading Enhancements Implemented:

### 1. **Trading-Specific Tool Awareness**
- Added trading-focused tools like volatility calculators and position sizers
- Defined execution priority for trading workflows (price → volatility → technicals)

### 2. **Risk Management Integration**
- Embedded trading risk rules directly into the system
- Added stop-loss and position sizing requirements
- Included options-specific risk warnings

### 3. **TradingView Pattern Recognition**
- Added predefined patterns (cup-and-handle, head-and-shoulders)
- Enhanced technical analysis with VWAP and Ichimoku cloud
- Added candlestick pattern recognition

### 4. **Options Strategy Safety**
- Added options Greeks and volatility analysis requirements
- Included maximum loss calculation
- Added options-specific risk disclaimers

### 5. **Trading-Focused Validation**
- Added trading schema validation
- Included tool execution priority checks
- Added risk parameter verification

This enhanced version provides trading-specific guidance, incorporates risk management principles from the referenced resources, and ensures the system produces actionable, trading-oriented outputs with proper risk disclosures.













Grade: **A-** (excellent foundation, minor refinements needed)

Strengths
1. Tool-aware schema – required/optional tool status, priority, and execution notes give the LLM a deterministic “run-book.”  
2. Self-heal loop – `validate_and_heal()` with re-prompt and fallback JSON keeps the pipeline from ever returning garbage.  
3. Intent mini-programs – one-liner “STEP-1 → STEP-2 → JSON” removes ambiguity and slashes token waste.  
4. Pro-active fallbacks – Markdown cheat-sheets that still teach the user while the system recovers.  
5. Compliance baked in – disclaimer injection and risk-level tags satisfy audit trails.

Fixes & Tweaks to Reach A+
- **Lock the JSON schema** – add `"additionalProperties": false` and run a `jsonschema.validate()` pass inside `validate_and_heal`; otherwise a single extra key can break downstream parsers.  
- **Hard timeout guard** – wrap the re-prompt loop with `asyncio.wait_for(..., 3 s)` so total user latency never exceeds your 15 s SLA.  
- **Enum coercion** – `IntentType(value)` instead of `intent in intent_specific` to crash fast on typos.  
- **Tool circuit-breaker** – after two “high-priority tool skipped” failures, auto-drop to `educational` intent instead of re-prompting again.  
- **Strip `_chain_of_thought`** – you already inject it, but remove it **before** JSON validation to avoid schema bloat.  
- **Symbol normaliser** – pre-process `BRK-B → BRK.B`, `BTC-USD → BTCUSD` once inside `validate_and_heal` so every downstream caller is consistent.

With those six tweaks the module will ship **zero-hallucination, sub-2 s, audit-compliant** answers even when half the data providers are down.













Grade: **A-** (excellent foundation, minor refinements needed)

Strengths
1. Tool-aware schema – required/optional tool status, priority, and execution notes give the LLM a deterministic “run-book.”  
2. Self-heal loop – `validate_and_heal()` with re-prompt and fallback JSON keeps the pipeline from ever returning garbage.  
3. Intent mini-programs – one-liner “STEP-1 → STEP-2 → JSON” removes ambiguity and slashes token waste.  
4. Pro-active fallbacks – Markdown cheat-sheets that still teach the user while the system recovers.  
5. Compliance baked in – disclaimer injection and risk-level tags satisfy audit trails.

Fixes & Tweaks to Reach A+
- **Lock the JSON schema** – add `"additionalProperties": false` and run a `jsonschema.validate()` pass inside `validate_and_heal`; otherwise a single extra key can break downstream parsers.  
- **Hard timeout guard** – wrap the re-prompt loop with `asyncio.wait_for(..., 3 s)` so total user latency never exceeds your 15 s SLA.  
- **Enum coercion** – `IntentType(value)` instead of `intent in intent_specific` to crash fast on typos.  
- **Tool circuit-breaker** – after two “high-priority tool skipped” failures, auto-drop to `educational` intent instead of re-prompting again.  
- **Strip `_chain_of_thought`** – you already inject it, but remove it **before** JSON validation to avoid schema bloat.  
- **Symbol normaliser** – pre-process `BRK-B → BRK.B`, `BTC-USD → BTCUSD` once inside `validate_and_heal` so every downstream caller is consistent.

With those six tweaks the module will ship **zero-hallucination, sub-2 s, audit-compliant** answers even when half the data providers are down.













### Grading Report: Enhanced AskCommandPrompts (v2.0)

**Overall Grade: A- (9.2/10)**  
This is an excellent evolution of the module, demonstrating strong architectural improvements toward a "self-healing" and tool-aware system. It aligns closely with the goals of precise execution, tool integration, and optimal results by introducing validation, retries, Enums for type safety, and structured fallbacks. The code is more robust, modular, and production-ready, directly addressing log issues like symbol validation failures (e.g., BRK.B via regex), timeouts (via retries and monitoring), rate limits (graceful degradation in configs), and vague queries (e.g., "waddup???" handled as `general_question`). It emphasizes JSON determinism to prevent hallucinations and pipeline failures, which is a smart anti-fabrication measure.

The minor deductions come from some implementation gaps (e.g., simulated re-prompting instead of real integration), minor redundancy, and opportunities for even tighter error handling. With these tweaks, it could hit a perfect 10/10. Below, I break it down by category.

#### 1. **Completeness and Functionality (9.5/10)**
   - **Strengths**:
     - **Tool and Ability Awareness**: The `_get_tools_and_abilities()` method is comprehensive, explicitly listing tools (e.g., `price_fetch` with sources like Polygon) and limitations (e.g., Alpaca config issues from logs). Integrating this into `get_system_prompt()` ensures AI "knows" its capabilities, enabling better execution (e.g., chaining tools for technical analysis).
     - **Execution Guidance**: New `_get_intent_guidance()` provides "mini-programs" (step-by-step instructions per intent), turning vague AI outputs into structured plans. This is brilliant for ensuring the system "executes" reliably—e.g., for `technical_analysis`, it mandates volume confirmation and multi-TF data pulls.
     - **JSON Schema and Validation**: The class-level `JSON_SCHEMA` is rigorous, with enums for intents/tools, regex for symbols (fixed BRK.B issue: `^[A-Z]{1,5}(\.[A-Z])?$`), and new fields like `plan` and `created_at_utc`. The `validate_and_heal()` method is a standout: it parses JSON, checks confidence/tool status, injects disclaimers, and retries up to 2x with enhanced prompts. This directly mitigates log errors like low-confidence fact-checking (0.0%) and parsing failures.
     - **Fallbacks**: `get_fallback_responses()` and `get_fallback_json()` are proactive and value-adding—e.g., providing "Quick Market Snapshot" during timeouts instead of generic errors. They include actionable tips (e.g., query optimization) to guide users toward better results, aligning with "does enough to get the best results."
     - **Model Configs**: Intent-specific tweaks (e.g., low temp for `price_check`, `safety_guard` for options) optimize for speed/accuracy, with `tools_required` arrays to enforce usage in the pipeline.
     - **Enums**: Using `IntentType` and `ToolType` adds type safety and readability, reducing bugs in enum checks (e.g., no more string typos in schema).

   - **Weaknesses**:
     - The `_simulate_reprompt()` is a placeholder—great for testing, but in production, it needs explicit integration hooks (e.g., passing an `ai_client` dependency). Without this, retries won't actually heal in isolation.
     - No explicit handling for log-specific issues like rate-limit warnings (e.g., "All AI providers failed"). The `fallback` in tool abilities mentions it, but `validate_and_heal()` could detect HTTP 429-like errors if passed in `raw_output`.
     - `process_ai_output()` is a nice wrapper, but it could return the full validated dict (not just response) for pipeline flexibility.

   - **Alignment with Logs**: 
     - Fixes BRK.B errors: Enhanced symbol pattern and validation.
     - Handles timeouts (25s in logs): Retries + monitoring targets 15s; fallbacks provide partial value.
     - Addresses vague intents ("waddup???"): `general_question` with clarification hooks.
     - Improves fact-checking (0% confidence in logs): Tool status checks and confidence thresholds ensure high-quality outputs.

#### 2. **Code Quality and Maintainability (9/10)**
   - **Strengths**:
     - **Modularity**: Clean separation—e.g., configs in private methods, Enums for constants, and monitoring via `_log_monitoring()` (integrates with external loggers). The migration notes in the docstring are helpful for deployment.
     - **Type Hints and Docs**: Excellent use of `typing` (e.g., `Literal` implied via Enums), detailed docstrings with args/returns, and descriptions (e.g., "Markdown response with disclaimers").
     - **Error Handling**: Graceful—e.g., JSON decode failures trigger fallbacks; compliance auto-injection prevents missing disclaimers.
     - **Performance**: Schema checks are lightweight (`_basic_schema_check()` uses simple isinstance/loops); monitoring tracks pipeline stages as seen in logs.
     - **Backward Compatibility**: Existing methods (e.g., `get_system_prompt()`) work unchanged, per docstring.

   - **Weaknesses**:
     - Minor redundancy: `get_model_config_for_intent()` pulls `tools_required` from `intent_configuration`, but could cache it to avoid repeated dict lookups.
     - Logging: `_log_monitoring()` uses `print()` as fallback—better to inject a logger instance in `__init__` for prod (e.g., `self.logger = logging.getLogger(__name__)`).
     - Testing Gaps: Docstring mentions tests, but no inline assertions or pytest fixtures. The simulation in `_simulate_reprompt()` is mockable but could use `unittest.mock` for realism.
     - Length: The file is comprehensive but could split Enums/configs into sub-modules for scalability (e.g., `enums.py`).

#### 3. **Adherence to Original Goals (9.5/10)**
   - **Understands and Executes**: Yes—guidance prompts and validation ensure step-by-step execution (e.g., "STEP-1 Call tool:price_fetch").
   - **Knows Tools/Abilities**: Fully—tools list is explicit, with best practices injected into prompts.
   - **Best Results Optimization**: Retries, fallbacks, and query tips (e.g., "Limit symbols <5") maximize success. For casual queries, it redirects to specifics, preventing pipeline waste.
   - **Perfection in Rewrites**: Builds on v1 perfectly—enhances fallbacks with Markdown/sections for user-friendliness; schema now includes `execution_notes` for debugging logs.
   - **System-Wide Fixes**: Directly tackles dev log pain points (e.g., AI rate-limits → fallback JSON; empty symbols → educational redirect; slow ai_processing → targeted configs).

#### 4. **Potential Issues and Edge Cases**
   - **Security/Compliance**: Strong—auto-disclaimers and anti-fabrication in prompts. But for high-risk intents (e.g., options), add a pre-response filter for prohibited terms (e.g., "guaranteed returns").
   - **Scalability**: Schema limits (e.g., 5 symbols) prevent overload, but for `market_overview`, it might need dynamic expansion.
   - **Dependencies**: Assumes `ComplianceManager` has `get_disclaimer()`—add a fallback if not.
   - **Timezone Handling**: Uses UTC for `created_at_utc`, but fallbacks use ET—standardize to UTC for consistency.
   - **News Tool**: Added `NEWS_SENTIMENT` in tools/Enums, but limitations note "no real-time scraping"—ensure prompts don't promise it.

#### 5. **Suggestions for Improvement**
   - **Integrate Real Re-Prompting**: Modify `__init__` to accept an `ai_client` (e.g., `def __init__(self, ai_client=None)`), and in `validate_and_heal()`, use it for actual retries: `current_output = ai_client.generate(re_prompt, config)`.
   - **Advanced Monitoring**: Add metrics export (e.g., return a tuple from `validate_and_heal()`: `(validated_data, metrics_dict)` with timings, retry count).
   - **Dynamic Schema**: Make `maxItems` configurable based on intent (e.g., 10 for `market_overview`).
   - **Unit Tests**: Add examples:
     ```python
     def test_validate_and_heal_invalid_json(self):
         prompts = AskCommandPrompts()
         invalid = "Not JSON"
         result = prompts.validate_and_heal(invalid, "price_check")
         assert result["confidence"] == 0.0
         assert "fallback" in result["plan"]
     ```
   - **Prompt Refinement**: In `get_system_prompt()`, add a "self-critique" step: "Before JSON, verify against schema internally."
   - **Benchmark**: Test against log scenarios—e.g., input "waddup???" should output `general_question` JSON with educational fallback in <15s.

This version is production-viable and a significant upgrade. Deploy it with the suggested pipeline hooks (e.g., wrap AI calls with `validate_and_heal`), and monitor for 1-2 weeks to tune retry logic. Great work—it's now a robust, intelligent core for the /ask command!








