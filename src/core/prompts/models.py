"""
Data models for the prompt management system.
"""

from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum


class IntentType(Enum):
    """Trading analysis intent categories."""
    PRICE_CHECK = "price_check"
    TECHNICAL_ANALYSIS = "technical_analysis"
    FUNDAMENTAL_ANALYSIS = "fundamental_analysis"
    OPTIONS_STRATEGY = "options_strategy"
    MARKET_OVERVIEW = "market_overview"
    RISK_MANAGEMENT = "risk_management"
    EDUCATIONAL = "educational"
    GENERAL_CHAT = "general_chat"


class ToolType(Enum):
    """Available data analysis tools."""
    PRICE_FETCH = "price_fetch"
    HISTORICAL_DATA = "historical_data"
    TECHNICAL_INDICATORS = "technical_indicators"
    FUNDAMENTAL_DATA = "fundamental_data"
    OPTIONS_DATA = "options_data"


@dataclass
class IntentClassification:
    """Result of prompt-based query classification."""
    intent: IntentType
    symbols: List[str]
    tools_required: List[ToolType]
    confidence: float
    timeframe: str
    risk_level: str
    raw_response: str
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses."""
        return {
            "intent": self.intent.value,
            "symbols": self.symbols,
            "tools_required": [tool.value for tool in self.tools_required],
            "confidence": self.confidence,
            "timeframe": self.timeframe,
            "risk_level": self.risk_level,
            "response": self.raw_response
        }


@dataclass
class ToolRequirements:
    """Defines required tools for analysis."""
    intent: IntentType
    symbols: List[str]
    tools: List[ToolType]
    priority: str = "normal"  # low, normal, high, critical
    cache_ttl: Optional[int] = None  # Cache TTL in seconds


@dataclass
class AnalysisPrompt:
    """Complete prompt configuration for AI analysis."""
    system_prompt: str
    user_prompt: str
    intent: IntentType
    symbols: List[str]
    tools_required: List[ToolType]
    timeframe: str
    risk_level: str
    compliance_template: str


@dataclass
class ComplianceTemplate:
    """Compliance disclaimer templates."""
    standard: str
    options: str
    high_risk: str
    educational: str
    
    def get_for_intent(self, intent: IntentType, risk_level: str) -> str:
        """Get appropriate disclaimer based on intent and risk."""
        if intent == IntentType.OPTIONS_STRATEGY:
            return self.options
        elif risk_level in ["high", "very_high"]:
            return self.high_risk
        elif intent in [IntentType.EDUCATIONAL, IntentType.GENERAL_CHAT]:
            return self.educational
        else:
            return self.standard


@dataclass
class PromptResult:
    """Complete result from prompt processing."""
    classification: IntentClassification
    analysis_prompt: AnalysisPrompt
    formatted_response: str
    metadata: Dict[str, Any]