"""
Prompt Formatting Utilities

This module provides utilities for formatting and composing prompts,
including template processing, section organization, and output formatting.
"""

from typing import Dict, List, Any, Optional, Union
import json
import re
from pathlib import Path

class PromptFormatter:
    """Handles prompt formatting, composition, and template processing"""
    
    def __init__(self):
        """Initialize prompt formatter"""
        self.section_separators = {
            'major': '\n\n## ',
            'minor': '\n\n### ',
            'subsection': '\n\n#### ',
            'paragraph': '\n\n'
        }
    
    def format_system_prompt(self, 
                           sections: Dict[str, str],
                           include_headers: bool = True,
                           separator_style: str = 'major') -> str:
        """
        Format a system prompt from sections
        
        Args:
            sections: Dictionary of section_name -> content
            include_headers: Whether to include section headers
            separator_style: Style of separators (major, minor, subsection, paragraph)
            
        Returns:
            Formatted prompt string
        """
        separator = self.section_separators.get(separator_style, self.section_separators['major'])
        formatted_sections = []
        
        for section_name, content in sections.items():
            if include_headers:
                # Format section header
                header = self._format_section_header(section_name)
                formatted_sections.append(f"{header}\n{content}")
            else:
                formatted_sections.append(content)
        
        return separator.join(formatted_sections)
    
    def _format_section_header(self, section_name: str) -> str:
        """Format section header from name"""
        # Convert snake_case or camelCase to Title Case
        formatted_name = re.sub(r'[_-]', ' ', section_name)
        formatted_name = re.sub(r'([a-z])([A-Z])', r'\1 \2', formatted_name)
        return formatted_name.title()
    
    def format_json_schema_prompt(self, 
                                schema: Dict[str, Any],
                                include_example: bool = True,
                                strict_formatting: bool = True) -> str:
        """
        Format a prompt that requires JSON response with schema
        
        Args:
            schema: JSON schema definition
            include_example: Whether to include example response
            strict_formatting: Whether to include strict formatting rules
            
        Returns:
            Formatted JSON schema prompt
        """
        prompt_parts = []
        
        if strict_formatting:
            prompt_parts.append("""CRITICAL FORMATTING RULE: Respond EXCLUSIVELY with the valid JSON object as specified below. Do NOT include any additional text, explanations, markdown, or prose before or after the JSON. The JSON must be the ONLY content in your response. Invalid formats will cause processing errors.""")
        
        # Add schema description
        if include_example:
            example = self._generate_example_from_schema(schema)
            prompt_parts.append(f"""Example of REQUIRED response format (use this exact structure):
{json.dumps(example, indent=2)}""")
        
        # Add schema definition
        prompt_parts.append(f"""## Required JSON Structure:
```json
{json.dumps(schema, indent=2)}
```""")
        
        return "\n\n".join(prompt_parts)
    
    def _generate_example_from_schema(self, schema: Dict[str, Any]) -> Dict[str, Any]:
        """Generate example JSON from schema"""
        example = {}
        properties = schema.get('properties', {})
        
        for prop_name, prop_def in properties.items():
            prop_type = prop_def.get('type', 'string')
            
            if prop_type == 'string':
                if 'enum' in prop_def:
                    example[prop_name] = prop_def['enum'][0]
                else:
                    example[prop_name] = f"example_{prop_name}"
            elif prop_type == 'number' or prop_type == 'integer':
                example[prop_name] = 0.95 if prop_type == 'number' else 1
            elif prop_type == 'boolean':
                example[prop_name] = True
            elif prop_type == 'array':
                items_type = prop_def.get('items', {}).get('type', 'string')
                if items_type == 'string':
                    example[prop_name] = ["EXAMPLE1", "EXAMPLE2"]
                else:
                    example[prop_name] = []
            elif prop_type == 'object':
                example[prop_name] = {}
        
        return example
    
    def format_compliance_section(self, 
                                disclaimers: List[str],
                                risk_level: str = "standard",
                                format_style: str = "structured") -> str:
        """
        Format compliance and disclaimer section
        
        Args:
            disclaimers: List of disclaimer texts
            risk_level: Risk level (low, medium, high, very_high)
            format_style: Format style (structured, simple, comprehensive)
            
        Returns:
            Formatted compliance section
        """
        if format_style == "simple":
            return "\n".join(disclaimers)
        
        elif format_style == "structured":
            formatted_disclaimers = []
            for i, disclaimer in enumerate(disclaimers, 1):
                formatted_disclaimers.append(f"{i}. {disclaimer}")
            
            return f"""## Compliance Requirements:
{chr(10).join(formatted_disclaimers)}"""
        
        elif format_style == "comprehensive":
            risk_icons = {
                "low": "ℹ️",
                "medium": "⚠️", 
                "high": "🚨",
                "very_high": "🔴"
            }
            
            icon = risk_icons.get(risk_level, "⚠️")
            
            return f"""## {icon} COMPLIANCE & RISK DISCLOSURE:

**MANDATORY DISCLAIMERS:**
{chr(10).join(f'• {disclaimer}' for disclaimer in disclaimers)}

**RISK LEVEL**: {risk_level.upper().replace('_', ' ')}"""
        
        return "\n".join(disclaimers)
    
    def format_persona_section(self, 
                             persona_data: Dict[str, Any],
                             include_expertise: bool = True,
                             include_focus_areas: bool = True) -> str:
        """
        Format persona section for prompt
        
        Args:
            persona_data: Persona data dictionary
            include_expertise: Whether to include expertise areas
            include_focus_areas: Whether to include focus areas
            
        Returns:
            Formatted persona section
        """
        sections = []
        
        # Basic persona info
        sections.append(f"You are a {persona_data.get('name', 'Professional Assistant')} with the following characteristics:")
        sections.append(f"**Personality**: {persona_data.get('personality', 'Professional and helpful')}")
        sections.append(f"**Communication Tone**: {persona_data.get('tone', 'professional')}")
        sections.append(f"**Risk Approach**: {persona_data.get('risk_tolerance', 'moderate')}")
        
        # Expertise areas
        if include_expertise and persona_data.get('expertise'):
            expertise_list = ', '.join(persona_data['expertise'])
            sections.append(f"**Expertise Areas**: {expertise_list}")
        
        # Focus areas
        if include_focus_areas and persona_data.get('focus_areas'):
            focus_areas = '\n'.join(f'- {area}' for area in persona_data['focus_areas'])
            sections.append(f"**Your Focus Areas**:\n{focus_areas}")
        
        # Communication style
        if persona_data.get('communication_style'):
            sections.append(f"**Communication Style**: {persona_data['communication_style']}")
        
        sections.append("Maintain this persona consistently throughout the conversation while providing helpful, accurate, and educational insights.")
        
        return '\n\n'.join(sections)
    
    def format_context_section(self, 
                             context_data: Dict[str, Any],
                             section_title: str = "Context Information") -> str:
        """
        Format context information section
        
        Args:
            context_data: Context data dictionary
            section_title: Title for the context section
            
        Returns:
            Formatted context section
        """
        if not context_data:
            return ""
        
        sections = [f"## {section_title}:"]
        
        for key, value in context_data.items():
            formatted_key = self._format_section_header(key)
            
            if isinstance(value, dict):
                sections.append(f"**{formatted_key}**:")
                for sub_key, sub_value in value.items():
                    sections.append(f"- {sub_key}: {sub_value}")
            elif isinstance(value, list):
                sections.append(f"**{formatted_key}**: {', '.join(map(str, value))}")
            else:
                sections.append(f"**{formatted_key}**: {value}")
        
        return '\n'.join(sections)
    
    def format_instruction_list(self, 
                              instructions: List[str],
                              list_style: str = "numbered",
                              emphasis_level: str = "normal") -> str:
        """
        Format list of instructions
        
        Args:
            instructions: List of instruction strings
            list_style: Style (numbered, bulleted, simple)
            emphasis_level: Emphasis level (normal, strong, critical)
            
        Returns:
            Formatted instruction list
        """
        if list_style == "numbered":
            formatted_instructions = [f"{i}. {instruction}" for i, instruction in enumerate(instructions, 1)]
        elif list_style == "bulleted":
            formatted_instructions = [f"• {instruction}" for instruction in instructions]
        else:  # simple
            formatted_instructions = instructions
        
        if emphasis_level == "strong":
            formatted_instructions = [f"**{instruction}**" for instruction in formatted_instructions]
        elif emphasis_level == "critical":
            formatted_instructions = [f"🚨 **{instruction}**" for instruction in formatted_instructions]
        
        return '\n'.join(formatted_instructions)
    
    def compose_prompt(self, 
                      components: Dict[str, str],
                      component_order: Optional[List[str]] = None,
                      separator_style: str = 'major') -> str:
        """
        Compose a complete prompt from components
        
        Args:
            components: Dictionary of component_name -> content
            component_order: Order of components (if None, uses dict order)
            separator_style: Style of separators between components
            
        Returns:
            Complete composed prompt
        """
        if component_order:
            ordered_components = {name: components[name] for name in component_order if name in components}
        else:
            ordered_components = components
        
        return self.format_system_prompt(ordered_components, separator_style=separator_style)
    
    def minify_prompt(self, prompt: str, preserve_structure: bool = True) -> str:
        """
        Minify prompt by removing unnecessary whitespace
        
        Args:
            prompt: Prompt to minify
            preserve_structure: Whether to preserve section structure
            
        Returns:
            Minified prompt
        """
        if preserve_structure:
            # Remove extra blank lines but preserve section breaks
            lines = prompt.split('\n')
            cleaned_lines = []
            prev_empty = False
            
            for line in lines:
                if line.strip():
                    cleaned_lines.append(line)
                    prev_empty = False
                elif not prev_empty:
                    cleaned_lines.append('')
                    prev_empty = True
            
            return '\n'.join(cleaned_lines)
        else:
            # Aggressive minification
            return re.sub(r'\n\s*\n', '\n\n', prompt.strip())
    
    def expand_prompt_template(self, 
                             template: str,
                             variables: Dict[str, Any],
                             missing_var_action: str = "error") -> str:
        """
        Expand prompt template with variables
        
        Args:
            template: Template string with {variable} placeholders
            variables: Dictionary of variable values
            missing_var_action: Action for missing variables (error, skip, default)
            
        Returns:
            Expanded prompt string
        """
        expanded = template
        
        # Find all variables in template
        var_pattern = r'\{([^}]+)\}'
        found_vars = re.findall(var_pattern, template)
        
        for var_name in found_vars:
            placeholder = f"{{{var_name}}}"
            
            if var_name in variables:
                value = variables[var_name]
                if isinstance(value, (dict, list)):
                    value = json.dumps(value, indent=2)
                expanded = expanded.replace(placeholder, str(value))
            elif missing_var_action == "error":
                raise ValueError(f"Missing variable: {var_name}")
            elif missing_var_action == "skip":
                continue  # Leave placeholder as-is
            elif missing_var_action == "default":
                expanded = expanded.replace(placeholder, f"[{var_name}]")
        
        return expanded
