"""
Prompt Validation Utilities

This module provides validation functions for prompts to ensure they meet
quality standards, include required elements, and follow best practices.
"""

from typing import Dict, List, Any, Optional, Tuple
import re
import json

class PromptValidator:
    """Validates prompts for quality, compliance, and completeness"""
    
    def __init__(self):
        """Initialize prompt validator"""
        self.required_elements = {
            'system_prompt': [
                'educational content',
                'not financial advice',
                'risk',
                'financial advisor'
            ],
            'trading_prompt': [
                'risk management',
                'position sizing',
                'educational',
                'disclaimer'
            ],
            'options_prompt': [
                'options risk',
                'substantial risk',
                'not suitable for all investors',
                'lose entire investment'
            ]
        }
        
        self.prohibited_elements = [
            'guaranteed returns',
            'risk-free',
            'sure thing',
            'can\'t lose',
            'guaranteed profit',
            'no risk'
        ]
    
    def validate_prompt(self, prompt: str, prompt_type: str = 'system_prompt') -> Dict[str, Any]:
        """
        Comprehensive prompt validation
        
        Args:
            prompt: Prompt string to validate
            prompt_type: Type of prompt (system_prompt, trading_prompt, options_prompt)
            
        Returns:
            Validation results dictionary
        """
        results = {
            'is_valid': True,
            'score': 0.0,
            'issues': [],
            'warnings': [],
            'suggestions': [],
            'compliance_check': {},
            'quality_metrics': {}
        }
        
        # Check required elements
        compliance_results = self.check_compliance_elements(prompt, prompt_type)
        results['compliance_check'] = compliance_results
        
        # Check prohibited elements
        prohibited_results = self.check_prohibited_elements(prompt)
        if prohibited_results['found']:
            results['issues'].extend(prohibited_results['elements'])
            results['is_valid'] = False
        
        # Quality metrics
        quality_metrics = self.calculate_quality_metrics(prompt)
        results['quality_metrics'] = quality_metrics
        
        # Length validation
        length_results = self.validate_length(prompt, prompt_type)
        if not length_results['is_valid']:
            results['warnings'].append(length_results['message'])
        
        # Structure validation
        structure_results = self.validate_structure(prompt, prompt_type)
        if not structure_results['is_valid']:
            results['issues'].extend(structure_results['issues'])
        
        # Anti-fabrication check
        anti_fab_results = self.check_anti_fabrication_rules(prompt)
        if not anti_fab_results['is_valid']:
            results['warnings'].extend(anti_fab_results['warnings'])
        
        # Calculate overall score
        results['score'] = self.calculate_overall_score(results)
        
        # Generate suggestions
        results['suggestions'] = self.generate_suggestions(results)
        
        return results
    
    def check_compliance_elements(self, prompt: str, prompt_type: str) -> Dict[str, Any]:
        """Check for required compliance elements"""
        required = self.required_elements.get(prompt_type, self.required_elements['system_prompt'])
        prompt_lower = prompt.lower()
        
        results = {
            'required_elements': required,
            'found_elements': [],
            'missing_elements': [],
            'compliance_score': 0.0
        }
        
        for element in required:
            if element in prompt_lower:
                results['found_elements'].append(element)
            else:
                results['missing_elements'].append(element)
        
        results['compliance_score'] = len(results['found_elements']) / len(required)
        return results
    
    def check_prohibited_elements(self, prompt: str) -> Dict[str, Any]:
        """Check for prohibited language that could mislead users"""
        prompt_lower = prompt.lower()
        found_prohibited = []
        
        for prohibited in self.prohibited_elements:
            if prohibited in prompt_lower:
                found_prohibited.append(prohibited)
        
        return {
            'found': len(found_prohibited) > 0,
            'elements': found_prohibited,
            'count': len(found_prohibited)
        }
    
    def calculate_quality_metrics(self, prompt: str) -> Dict[str, Any]:
        """Calculate various quality metrics for the prompt"""
        words = prompt.split()
        sentences = re.split(r'[.!?]+', prompt)
        
        return {
            'word_count': len(words),
            'sentence_count': len([s for s in sentences if s.strip()]),
            'avg_sentence_length': len(words) / max(len(sentences), 1),
            'readability_score': self.calculate_readability_score(prompt),
            'clarity_score': self.calculate_clarity_score(prompt),
            'completeness_score': self.calculate_completeness_score(prompt)
        }
    
    def calculate_readability_score(self, prompt: str) -> float:
        """Calculate readability score (simplified)"""
        words = prompt.split()
        sentences = re.split(r'[.!?]+', prompt)
        
        if len(sentences) == 0 or len(words) == 0:
            return 0.0
        
        avg_sentence_length = len(words) / len(sentences)
        
        # Simple readability score (lower is better for readability)
        # Penalize very long sentences
        if avg_sentence_length > 25:
            return 0.3
        elif avg_sentence_length > 20:
            return 0.6
        elif avg_sentence_length > 15:
            return 0.8
        else:
            return 1.0
    
    def calculate_clarity_score(self, prompt: str) -> float:
        """Calculate clarity score based on structure and language"""
        score = 1.0
        
        # Check for clear sections
        if '##' in prompt or '**' in prompt:
            score += 0.2
        
        # Check for bullet points or lists
        if '- ' in prompt or '* ' in prompt:
            score += 0.1
        
        # Check for examples
        if 'example' in prompt.lower() or 'for instance' in prompt.lower():
            score += 0.1
        
        # Penalize excessive jargon
        jargon_words = ['algorithmic', 'sophisticated', 'comprehensive', 'methodology']
        jargon_count = sum(1 for word in jargon_words if word in prompt.lower())
        if jargon_count > 5:
            score -= 0.2
        
        return min(score, 1.0)
    
    def calculate_completeness_score(self, prompt: str) -> float:
        """Calculate completeness score based on essential components"""
        essential_components = [
            'role definition',
            'instructions',
            'examples',
            'constraints',
            'output format'
        ]
        
        score = 0.0
        prompt_lower = prompt.lower()
        
        # Role definition
        if any(word in prompt_lower for word in ['you are', 'your role', 'assistant']):
            score += 0.2
        
        # Instructions
        if any(word in prompt_lower for word in ['analyze', 'provide', 'respond', 'generate']):
            score += 0.2
        
        # Examples
        if any(word in prompt_lower for word in ['example', 'for instance', 'such as']):
            score += 0.2
        
        # Constraints
        if any(word in prompt_lower for word in ['never', 'always', 'must', 'should not']):
            score += 0.2
        
        # Output format
        if any(word in prompt_lower for word in ['format', 'structure', 'json', 'response']):
            score += 0.2
        
        return score
    
    def validate_length(self, prompt: str, prompt_type: str) -> Dict[str, Any]:
        """Validate prompt length for different types"""
        word_count = len(prompt.split())
        
        length_limits = {
            'system_prompt': {'min': 100, 'max': 2000, 'optimal': 500},
            'trading_prompt': {'min': 50, 'max': 1000, 'optimal': 300},
            'options_prompt': {'min': 75, 'max': 1200, 'optimal': 400}
        }
        
        limits = length_limits.get(prompt_type, length_limits['system_prompt'])
        
        if word_count < limits['min']:
            return {
                'is_valid': False,
                'message': f"Prompt too short ({word_count} words). Minimum: {limits['min']} words."
            }
        elif word_count > limits['max']:
            return {
                'is_valid': False,
                'message': f"Prompt too long ({word_count} words). Maximum: {limits['max']} words."
            }
        else:
            return {
                'is_valid': True,
                'message': f"Length appropriate ({word_count} words)."
            }
    
    def validate_structure(self, prompt: str, prompt_type: str) -> Dict[str, Any]:
        """Validate prompt structure and organization"""
        issues = []
        
        # Check for clear sections
        if '##' not in prompt and '**' not in prompt:
            issues.append("Prompt lacks clear section headers for better organization")
        
        # Check for proper formatting
        if prompt_type == 'system_prompt':
            required_sections = ['role', 'responsibilities', 'requirements']
            prompt_lower = prompt.lower()
            
            missing_sections = []
            for section in required_sections:
                if section not in prompt_lower:
                    missing_sections.append(section)
            
            if missing_sections:
                issues.append(f"Missing recommended sections: {', '.join(missing_sections)}")
        
        return {
            'is_valid': len(issues) == 0,
            'issues': issues
        }
    
    def check_anti_fabrication_rules(self, prompt: str) -> Dict[str, Any]:
        """Check for anti-fabrication and data integrity rules"""
        warnings = []
        prompt_lower = prompt.lower()
        
        # Check for anti-fabrication language
        anti_fab_keywords = ['never fabricate', 'do not create fake', 'real data', 'accurate information']
        has_anti_fab = any(keyword in prompt_lower for keyword in anti_fab_keywords)
        
        if not has_anti_fab:
            warnings.append("Consider adding explicit anti-fabrication rules")
        
        # Check for data validation requirements
        data_validation_keywords = ['validate', 'verify', 'real-time data', 'current data']
        has_data_validation = any(keyword in prompt_lower for keyword in data_validation_keywords)
        
        if not has_data_validation:
            warnings.append("Consider adding data validation requirements")
        
        return {
            'is_valid': len(warnings) == 0,
            'warnings': warnings
        }
    
    def calculate_overall_score(self, results: Dict[str, Any]) -> float:
        """Calculate overall prompt quality score"""
        score = 0.0
        
        # Compliance score (40% weight)
        compliance_score = results['compliance_check'].get('compliance_score', 0.0)
        score += compliance_score * 0.4
        
        # Quality metrics (30% weight)
        quality_metrics = results['quality_metrics']
        avg_quality = (
            quality_metrics.get('readability_score', 0.0) +
            quality_metrics.get('clarity_score', 0.0) +
            quality_metrics.get('completeness_score', 0.0)
        ) / 3
        score += avg_quality * 0.3
        
        # Issues penalty (20% weight)
        issues_penalty = min(len(results['issues']) * 0.1, 0.2)
        score += (0.2 - issues_penalty)
        
        # Warnings penalty (10% weight)
        warnings_penalty = min(len(results['warnings']) * 0.05, 0.1)
        score += (0.1 - warnings_penalty)
        
        return min(score, 1.0)
    
    def generate_suggestions(self, results: Dict[str, Any]) -> List[str]:
        """Generate improvement suggestions based on validation results"""
        suggestions = []
        
        # Compliance suggestions
        missing_elements = results['compliance_check'].get('missing_elements', [])
        if missing_elements:
            suggestions.append(f"Add missing compliance elements: {', '.join(missing_elements)}")
        
        # Quality suggestions
        quality_metrics = results['quality_metrics']
        
        if quality_metrics.get('readability_score', 1.0) < 0.7:
            suggestions.append("Consider shortening sentences for better readability")
        
        if quality_metrics.get('clarity_score', 1.0) < 0.7:
            suggestions.append("Add more structure with headers, bullet points, or examples")
        
        if quality_metrics.get('completeness_score', 1.0) < 0.7:
            suggestions.append("Include more essential components: role definition, examples, constraints")
        
        # Length suggestions
        word_count = quality_metrics.get('word_count', 0)
        if word_count < 100:
            suggestions.append("Consider expanding the prompt with more detailed instructions")
        elif word_count > 1500:
            suggestions.append("Consider condensing the prompt for better focus")
        
        return suggestions
