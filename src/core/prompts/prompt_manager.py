"""
Enhanced central prompt management system for trading analysis.

This manager integrates all prompt components including personas, system prompts,
command-specific prompts, AI service prompts, and context injection.
"""

from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from pathlib import Path
import re
import json
from .models import (
    IntentType, ToolType, IntentClassification, AnalysisPrompt,
    ComplianceTemplate, PromptResult
)

# Import new centralized components
try:
    from .base.personas import PersonaManager, PERSONAS
    from .base.system_prompts import SystemPromptManager
    from .base.compliance import ComplianceManager
    from .commands.ask_prompts import AskCommandPrompts
    from .services.intent_detection import IntentDetectionPrompts
    from .utils.context_injection import ContextInjector
    from .utils.validation import PromptValidator
    from .utils.formatters import PromptFormatter
    CENTRALIZED_COMPONENTS_AVAILABLE = True
except ImportError:
    # Fallback for backward compatibility during migration
    CENTRALIZED_COMPONENTS_AVAILABLE = False


class PromptManager:
    """Enhanced central manager for all trading analysis prompts and classifications."""

    def __init__(self, prompts_dir: Path = None):
        """Initialize with prompts directory and centralized components."""
        self.prompts_dir = prompts_dir or Path(__file__).parent

        # Initialize centralized components if available
        if CENTRALIZED_COMPONENTS_AVAILABLE:
            self.persona_manager = PersonaManager()
            self.system_prompt_manager = SystemPromptManager()
            self.compliance_manager = ComplianceManager()
            self.ask_prompts = AskCommandPrompts()
            self.intent_detection = IntentDetectionPrompts()
            self.context_injector = ContextInjector()
            self.validator = PromptValidator()
            self.formatter = PromptFormatter()
            self._use_centralized = True
        else:
            self._use_centralized = False

        # Legacy components for backward compatibility
        self.system_prompt = self._load_system_prompt()
        self.intent_patterns = self._build_intent_patterns()
        self.tool_mappings = self._build_tool_mappings()
        self.compliance_templates = self._load_compliance_templates()
    
    def _load_system_prompt(self) -> str:
        """Load the comprehensive system prompt."""
        prompt_file = self.prompts_dir / "templates" / "system_prompt.txt"
        if prompt_file.exists():
            return prompt_file.read_text()
        return self._get_default_system_prompt()
    
    def _get_default_system_prompt(self) -> str:
        """Return the comprehensive system prompt from your document."""
        return """You are a professional trading analysis assistant. Your role is to analyze user queries, extract relevant information, and provide educational trading insights with proper risk disclosures.

## Core Responsibilities:
1. **Intent Classification**: Accurately categorize user queries into specific trading contexts
2. **Symbol Extraction**: Identify and clean stock symbols from user input
3. **Data Requirements**: Determine when real-time market data is essential vs. educational content
4. **Complete Analysis**: Provide comprehensive, actionable trading education and market insights

## Intent Categories:
- `price_check`: Current price requests, quotes, basic market data
- `technical_analysis`: Chart patterns, indicators, support/resistance analysis, comprehensive technical data requests
- `fundamental_analysis`: Financial metrics, earnings, valuation analysis
- `options_strategy`: Option plays, strategies, volatility analysis
- `market_overview`: Sector analysis, broad market conditions
- `risk_management`: Position sizing, stop losses, portfolio protection
- `educational`: Trading concepts, strategy explanations, learning content

## Intent Recognition Examples:

### Technical Analysis Intent (CRITICAL):
**Keywords that ALWAYS trigger `technical_analysis` intent:**
- "indicator values", "indicators", "technical indicators"
- "RSI", "MACD", "moving averages", "support/resistance"
- "chart analysis", "technical analysis", "pattern analysis"
- "all available data", "comprehensive analysis", "full analysis"
- "trend analysis", "momentum", "volume analysis"

## Symbol Extraction Rules:
- **REQUIRED**: Symbols MUST be prefixed with $ (example: $AAPL, $MSFT, $SPY)
- Extract symbols only from $ prefixes, convert to UPPERCASE, validate format
- For options queries without specific symbols: Suggest high-liquidity, high-volume stocks with $ prefix
- Priority symbols for options: $SPY, $QQQ, $AAPL, $MSFT, $GOOGL, $AMZN, $TSLA, $NVDA, $AMD, $META

## Data Strategy (CRITICAL):
**NO MOCK DATA POLICY**: Never fabricate prices, technical indicators, or financial metrics
- Use `tools_required` array to specify which data tools are needed for accurate analysis
- Available tools: `["price_fetch", "historical_data", "technical_indicators", "fundamental_data", "options_data"]`
- Leave empty `[]` for educational content, general strategies, or historical context

## Response Requirements:
**Format**: Valid JSON with exact structure specified below
**Length**: 200-400 words maximum (Discord-optimized)
**Tone**: Professional, educational, risk-aware
**Compliance**: Include appropriate disclaimers and risk warnings

## Required JSON Structure:
```json
{
  "intent": "string (one of the 7 categories above)",
  "symbols": ["SYMBOL1", "SYMBOL2"],
  "tools_required": ["price_fetch", "historical_data"],
  "confidence": float (0.0-1.0),
  "timeframe": "string (intraday|short_term|medium_term|long_term)",
  "risk_level": "string (low|medium|high|very_high)",
  "response": "Complete educational response with disclaimers"
}
```

## Compliance Requirements:
Every response MUST include appropriate risk disclaimers:
- "This is educational content, not financial advice"
- "Past performance doesn't guarantee future results"  
- "Options trading involves substantial risk of loss"
- "Consult a financial advisor for personalized advice"
- Position sizing recommendations (1-3% risk per trade maximum)"""

    async def _classify_intent_ai(self, query: str) -> Tuple[IntentType, float]:
        """Classify intent using AI with regex fallback"""
        try:
            from src.shared.ai_services.enhanced_intent_detector import enhanced_intent_detector

            intent_analysis = await enhanced_intent_detector.analyze_intent(query, use_ai=True)

            # Map AI intent types to prompt manager IntentType enum
            intent_mapping = {
                'price_check': IntentType.PRICE_CHECK,
                'technical_analysis': IntentType.TECHNICAL_ANALYSIS,
                'fundamental_analysis': IntentType.FUNDAMENTAL_ANALYSIS,
                'recommendation': IntentType.RECOMMENDATION,
                'comparison': IntentType.COMPARISON,
                'portfolio_advice': IntentType.PORTFOLIO_ADVICE,
                'risk_assessment': IntentType.RISK_ASSESSMENT,
                'market_news': IntentType.MARKET_NEWS,
                'options_analysis': IntentType.OPTIONS_ANALYSIS,
                'general_question': IntentType.GENERAL_QUESTION,
                'help_request': IntentType.HELP_REQUEST,
                'greeting': IntentType.GREETING,
                'unknown': IntentType.GENERAL_QUESTION
            }

            mapped_intent = intent_mapping.get(intent_analysis.primary_intent.value, IntentType.GENERAL_QUESTION)

            logger.info(f"AI intent classification: {intent_analysis.primary_intent.value} -> {mapped_intent.value} "
                       f"(confidence: {intent_analysis.confidence:.2f})")

            return mapped_intent, intent_analysis.confidence

        except Exception as e:
            logger.warning(f"AI intent classification failed, using regex fallback: {e}")
            return self._classify_intent_regex(query)

    def _classify_intent_regex(self, query: str) -> Tuple[IntentType, float]:
        """Fallback regex-based intent classification"""
        intent_patterns = self._build_intent_patterns()

        query_lower = query.lower()
        for intent_type, patterns in intent_patterns.items():
            for pattern in patterns:
                if re.search(pattern, query_lower, re.IGNORECASE):
                    return intent_type, 0.7

        return IntentType.GENERAL_QUESTION, 0.5

    def _build_intent_patterns(self) -> Dict[IntentType, List[str]]:
        """Build regex patterns for intent classification (fallback only)."""
        return {
            IntentType.TECHNICAL_ANALYSIS: [
                r'\b(indicator values?|indicators?|technical indicators?)\b',
                r'\b(RSI|MACD|moving averages?|support|resistance)\b',
                r'\b(chart analysis|technical analysis|pattern analysis)\b',
                r'\b(all available data|comprehensive analysis|full analysis)\b',
                r'\b(trend analysis|momentum|volume analysis)\b',
                r'\b(bollinger bands?|stochastic|fibonacci)\b',
                r'\b(overbought|oversold|divergence)\b'
            ],
            IntentType.PRICE_CHECK: [
                r'\b(price|current price|quote|ticker)\b',
                r'\b(how much is|what\'?s the price of)\b',
                r'^\$[A-Z]{1,10}\s*$'  # Just a symbol
            ],
            IntentType.OPTIONS_STRATEGY: [
                r'\b(options?|calls?|puts?|volatility)\b',
                r'\b(strategy|strategies|option play)\b',
                r'\b(premium|strike|expiration)\b',
                r'\b(iron condor|straddle|strangle|spread)\b',
                r'\b(implied volatility|IV|greeks)\b'
            ],
            IntentType.FUNDAMENTAL_ANALYSIS: [
                r'\b(earnings|revenue|financial|valuation)\b',
                r'\b(P/E|price earnings|book value|debt)\b',
                r'\b(fundamental|financials)\b',
                r'\b(PE ratio|PB ratio|ROE|ROI|cash flow)\b',
                r'\b(balance sheet|income statement|cash flow statement)\b'
            ],
            IntentType.MARKET_OVERVIEW: [
                r'\b(market|sector|broad|overall)\b',
                r'\b(sentiment|overview|conditions)\b',
                r'\b(market breadth|advance decline|VIX)\b',
                r'\b(sector rotation|market cap|indices)\b'
            ],
            IntentType.RISK_MANAGEMENT: [
                r'\b(risk|position siz|stop loss)\b',
                r'\b(management|protection|hedge)\b',
                r'\b(portfolio management|diversification|allocation)\b',
                r'\b(maximum loss|risk reward|risk tolerance)\b'
            ],
            IntentType.EDUCATIONAL: [
                r'\b(what is|explain|how to|teach me|learn about)\b',
                r'\b(education|tutorial|guide|basics)\b',
                r'\b(beginner|intro|introduction|overview)\b'
            ],
            IntentType.GENERAL_CHAT: [
                r'\b(hello|hi|hey|greetings)\b',
                r'\b(thank you|thanks|appreciate)\b',
                r'\b(how are you|what can you do|help|support)\b',
                r'\b(good morning|good afternoon|good evening)\b'
            ]
        }
    
    def _build_tool_mappings(self) -> Dict[IntentType, List[ToolType]]:
        """Map intents to required data tools."""
        return {
            IntentType.PRICE_CHECK: [ToolType.PRICE_FETCH],
            IntentType.TECHNICAL_ANALYSIS: [
                ToolType.PRICE_FETCH, 
                ToolType.HISTORICAL_DATA, 
                ToolType.TECHNICAL_INDICATORS
            ],
            IntentType.FUNDAMENTAL_ANALYSIS: [
                ToolType.PRICE_FETCH, 
                ToolType.FUNDAMENTAL_DATA
            ],
            IntentType.OPTIONS_STRATEGY: [
                ToolType.PRICE_FETCH, 
                ToolType.HISTORICAL_DATA, 
                ToolType.OPTIONS_DATA
            ],
            IntentType.MARKET_OVERVIEW: [
                ToolType.PRICE_FETCH, 
                ToolType.HISTORICAL_DATA
            ],
            IntentType.RISK_MANAGEMENT: [],  # Educational only
            IntentType.EDUCATIONAL: [],  # Educational only
            IntentType.GENERAL_CHAT: []  # Educational only
        }
    
    def _load_compliance_templates(self) -> ComplianceTemplate:
        """Load compliance disclaimer templates."""
        return ComplianceTemplate(
            standard="⚠️ IMPORTANT: This is educational content, not personalized financial advice. Trading involves substantial risk of loss. Past performance doesn't guarantee future results. Consult a qualified financial advisor for investment decisions.",
            options="⚠️ OPTIONS RISK: Options trading involves substantial risk and is not suitable for all investors. You may lose your entire investment. Understand the risks before trading options.",
            high_risk="⚠️ HIGH RISK: This strategy involves significant risk of loss. Only trade with capital you can afford to lose completely. Use proper position sizing (1-3% account risk maximum).",
            educational="📚 EDUCATIONAL PURPOSE: This analysis teaches trading concepts and methodologies. Always conduct your own research and risk assessment before making investment decisions."
        )
    
    def extract_symbols(self, text: str) -> List[str]:
        """Extract stock symbols with $ prefix from text."""
        if text is None:
            return []
        
        # Find all $SYMBOL patterns
        pattern = r'\$([A-Z]{1,10})\b'
        matches = re.findall(pattern, str(text).upper())
        
        # Validate and deduplicate
        valid_symbols = []
        for symbol in matches:
            if len(symbol) >= 1 and symbol.isalpha():
                if symbol not in valid_symbols:
                    valid_symbols.append(symbol)
        
        return valid_symbols[:10]  # Max 10 symbols

    # New centralized prompt methods
    def get_enhanced_system_prompt(self,
                                 persona: str = "trading_expert",
                                 context: Optional[Dict[str, Any]] = None,
                                 include_json_format: bool = True) -> str:
        """
        Get enhanced system prompt using centralized components

        Args:
            persona: Persona to use for the prompt
            context: Additional context to inject
            include_json_format: Whether to include JSON formatting requirements

        Returns:
            Enhanced system prompt
        """
        if self._use_centralized:
            return self.system_prompt_manager.get_system_prompt(
                persona=persona,
                context=context,
                include_json_format=include_json_format
            )
        else:
            # Fallback to legacy system prompt
            return self.system_prompt

    def get_command_prompt(self,
                          command: str,
                          context: Optional[Dict[str, Any]] = None) -> str:
        """
        Get command-specific prompt

        Args:
            command: Command name (ask, analyze, etc.)
            context: Additional context to inject

        Returns:
            Command-specific prompt
        """
        if self._use_centralized:
            if command == "ask":
                return self.ask_prompts.get_system_prompt(context)
            else:
                # Use general system prompt with appropriate persona
                persona = self.persona_manager.get_persona_for_intent(command)
                return self.system_prompt_manager.get_command_system_prompt(command, persona)
        else:
            # Fallback to legacy system prompt
            return self.system_prompt

    def get_intent_detection_prompt(self,
                                  query: str,
                                  context: Optional[Dict[str, Any]] = None) -> str:
        """
        Get intent detection prompt for AI classification

        Args:
            query: User query to classify
            context: Additional context for classification

        Returns:
            Intent detection prompt
        """
        if self._use_centralized:
            if context:
                return self.intent_detection.get_enhanced_intent_prompt(query, context)
            else:
                return self.intent_detection.get_intent_classification_prompt(query)
        else:
            # Fallback to basic classification
            return f"Classify this trading query: {query}"

    def get_fallback_responses(self, command: str = "ask") -> Dict[str, str]:
        """
        Get fallback responses for a specific command

        Args:
            command: Command name

        Returns:
            Dictionary of fallback responses
        """
        if self._use_centralized and command == "ask":
            return self.ask_prompts.get_fallback_responses()
        else:
            # Basic fallback responses
            return {
                "ai_error": "I'm experiencing technical difficulties. Please try again later.",
                "no_ai_config": "AI services are not configured. Please check your settings.",
                "timeout_error": "Request timed out. Please try a simpler query.",
                "parsing_error": "I had trouble understanding your request. Please rephrase."
            }

    def validate_prompt(self,
                       prompt: str,
                       prompt_type: str = "system_prompt") -> Dict[str, Any]:
        """
        Validate prompt quality and compliance

        Args:
            prompt: Prompt to validate
            prompt_type: Type of prompt for validation

        Returns:
            Validation results
        """
        if self._use_centralized:
            return self.validator.validate_prompt(prompt, prompt_type)
        else:
            # Basic validation
            return {
                "is_valid": len(prompt) > 50,
                "score": 0.8 if len(prompt) > 50 else 0.3,
                "issues": [],
                "warnings": []
            }

    def inject_context(self,
                      prompt: str,
                      market_data: Optional[Dict[str, Any]] = None,
                      user_context: Optional[Dict[str, Any]] = None,
                      session_context: Optional[Dict[str, Any]] = None) -> str:
        """
        Inject dynamic context into prompt

        Args:
            prompt: Base prompt
            market_data: Market context data
            user_context: User context data
            session_context: Session context data

        Returns:
            Prompt with injected context
        """
        if self._use_centralized:
            return self.context_injector.inject_comprehensive_context(
                prompt, market_data, user_context, session_context
            )
        else:
            # Basic date injection
            from datetime import datetime
            current_date = datetime.now().strftime("%B %d, %Y")
            return f"{prompt}\n\nCurrent date: {current_date}"

    def get_persona_list(self) -> List[str]:
        """Get list of available personas"""
        if self._use_centralized:
            return self.persona_manager.list_personas()
        else:
            return ["trading_expert", "risk_analyst", "educational_assistant"]

    def get_compliance_disclaimer(self,
                                risk_level: str = "standard",
                                intent: str = None) -> str:
        """
        Get appropriate compliance disclaimer

        Args:
            risk_level: Risk level for disclaimer
            intent: Trading intent for context-specific disclaimer

        Returns:
            Compliance disclaimer text
        """
        if self._use_centralized:
            if intent:
                return self.compliance_manager.get_intent_specific_compliance(intent)
            else:
                return self.compliance_manager.get_compliance_fragment(risk_level)
        else:
            # Basic disclaimer
            return "⚠️ This is educational content, not financial advice. Trading involves risk."
    
    def classify_intent(self, text: str) -> IntentType:
        """Classify user query intent using pattern matching."""
        if text is None:
            text = ""
        
        text_lower = str(text).lower().strip()
        
        # Handle empty or very short queries
        if not text_lower or len(text_lower) < 3:
            return IntentType.GENERAL_CHAT
        
        # Enhanced handling for combined queries
        has_price_keywords = bool(re.search(r'\b(price|current price|quote|value)\b', text_lower))
        has_indicator_keywords = bool(re.search(r'\b(indicator values?|indicators?|technical indicators?|all available data|comprehensive analysis)\b', text_lower))
        has_market_keywords = bool(re.search(r'\b(market|sector|broad|overall)\b', text_lower))
        
        # If both price and indicator keywords are present, prioritize technical analysis
        if has_price_keywords and has_indicator_keywords:
            return IntentType.TECHNICAL_ANALYSIS
        
        # Enhanced handling for market overview
        if has_market_keywords and not has_price_keywords and not has_indicator_keywords:
            return IntentType.MARKET_OVERVIEW
        
        # Score each intent based on pattern matches
        scores = {}
        for intent, patterns in self.intent_patterns.items():
            score = 0
            for pattern in patterns:
                matches = len(re.findall(pattern, text_lower, re.IGNORECASE))
                score += matches
            scores[intent] = score
        
        # Check for general chat patterns
        general_chat_patterns = [
            r'\b(hello|hi|hey|greetings)\b',
            r'\b(thank you|thanks|appreciate)\b',
            r'\b(how are you|what can you do|help)\b',
            r'\b(good morning|good afternoon|good evening)\b',
            r'^\?$',  # Single question mark
            r'^$',    # Empty string
            r'^\s*$'  # Whitespace only
        ]
        general_chat_score = sum(len(re.findall(pattern, text_lower, re.IGNORECASE)) for pattern in general_chat_patterns)
        
        # Get all score values, filtering out None values
        score_values = [score for score in scores.values() if score is not None]
        
        # Handle edge cases with no matches
        if not score_values or max(score_values) == 0:
            # Check for any symbols - if found, default to price check
            symbols = self.extract_symbols(text)
            if symbols:
                return IntentType.PRICE_CHECK
            return IntentType.GENERAL_CHAT
        
        # Check for ambiguous cases where multiple intents have similar scores
        max_score = max(score_values)
        top_intents = [intent for intent, score in scores.items() if score == max_score]
        
        if len(top_intents) > 1:
            # Disambiguate based on query characteristics
            if IntentType.TECHNICAL_ANALYSIS in top_intents and has_indicator_keywords:
                return IntentType.TECHNICAL_ANALYSIS
            elif IntentType.MARKET_OVERVIEW in top_intents and has_market_keywords:
                return IntentType.MARKET_OVERVIEW
        
        return max(scores.items(), key=lambda x: x[1] or 0)[0]
    
    def determine_tools_required(self, intent: IntentType, symbols: List[str]) -> List[ToolType]:
        """Determine required data tools based on intent and symbols."""
        base_tools = self.tool_mappings.get(intent, [])
        
        # Handle edge case where symbols are provided but no specific intent
        if intent == IntentType.GENERAL_CHAT and symbols:
            # If symbols are present, upgrade to price check
            return [ToolType.PRICE_FETCH]
        
        return base_tools
    
    def calculate_confidence(self, intent: IntentType, symbols: List[str], text: str) -> float:
        """Calculate classification confidence score."""
        confidence = 0.5  # Base confidence
        
        # Handle None inputs
        if text is None:
            text = ""
        if symbols is None:
            symbols = []
        
        # Boost for clear symbol extraction
        if symbols and intent in [IntentType.PRICE_CHECK, IntentType.TECHNICAL_ANALYSIS, IntentType.FUNDAMENTAL_ANALYSIS, IntentType.OPTIONS_STRATEGY]:
            confidence += 0.3
        
        # Boost for strong keyword matches
        patterns = self.intent_patterns.get(intent, [])
        text_str = str(text).lower()
        matches = sum(len(re.findall(pattern, text_str)) for pattern in patterns)
        confidence += min(matches * 0.1, 0.4)
        
        # Reduce confidence for very short queries
        if len(text_str.strip()) < 5:
            confidence = max(0.3, confidence - 0.2)
        
        # Boost confidence for MARKET_OVERVIEW with market-related terms
        if intent == IntentType.MARKET_OVERVIEW and any(word in text_str for word in ['market', 'sector', 'broad']):
            confidence += 0.1
            
        return min(confidence, 1.0)
    
    def determine_timeframe(self, intent: IntentType, text: str) -> str:
        """Determine appropriate timeframe for the query."""
        if text is None:
            text = ""
        
        timeframe_keywords = {
            "intraday": ["today", "intraday", "scalp", "day trade", "now", "current", "today's"],
            "short_term": ["short", "swing", "week", "weeks", "near", "near-term", "next week", "couple weeks"],
            "medium_term": ["medium", "month", "months", "quarterly", "quarter", "3-6 months", "medium term"],
            "long_term": ["long", "invest", "year", "years", "hold", "long term", "investment", "portfolio"]
        }
        
        text_lower = str(text).lower()
        
        # Check for specific timeframe patterns
        for timeframe, keywords in timeframe_keywords.items():
            for keyword in keywords:
                if keyword in text_lower:
                    return timeframe
        
        # Check for numeric timeframes
        import re
        timeframe_patterns = {
            r'\b(daily|1d|today)\b': "intraday",
            r'\b(weekly|1w|week)\b': "short_term",
            r'\b(monthly|1m|month)\b': "medium_term",
            r'\b(yearly|1y|year)\b': "long_term"
        }
        
        for pattern, timeframe in timeframe_patterns.items():
            if re.search(pattern, text_lower):
                return timeframe
        
        # Default based on intent with fallback
        defaults = {
            IntentType.PRICE_CHECK: "intraday",
            IntentType.TECHNICAL_ANALYSIS: "short_term",
            IntentType.FUNDAMENTAL_ANALYSIS: "long_term",
            IntentType.OPTIONS_STRATEGY: "short_term",
            IntentType.MARKET_OVERVIEW: "medium_term",
            IntentType.RISK_MANAGEMENT: "long_term",
            IntentType.EDUCATIONAL: "long_term",
            IntentType.GENERAL_CHAT: "medium_term"
        }
        
        return defaults.get(intent, "medium_term")
    
    def determine_risk_level(self, intent: IntentType, symbols: List[str]) -> str:
        """Determine risk level for the analysis."""
        base_risk = {
            IntentType.PRICE_CHECK: "low",
            IntentType.TECHNICAL_ANALYSIS: "medium",
            IntentType.FUNDAMENTAL_ANALYSIS: "medium",
            IntentType.OPTIONS_STRATEGY: "high",
            IntentType.MARKET_OVERVIEW: "medium",
            IntentType.RISK_MANAGEMENT: "low",
            IntentType.EDUCATIONAL: "low",
            IntentType.GENERAL_CHAT: "low"
        }
        
        risk = base_risk.get(intent, "medium")
        
        # Boost risk for volatile symbols
        volatile_symbols = ["GME", "AMC", "TSLA", "NVDA", "PLTR", "RIOT"]
        if any(symbol in volatile_symbols for symbol in symbols):
            risk_levels = ["low", "medium", "high", "very_high"]
            current_idx = risk_levels.index(risk)
            if current_idx < len(risk_levels) - 1:
                risk = risk_levels[current_idx + 1]
        
        return risk
    
    def build_analysis_prompt(self, user_query: str, classification: IntentClassification) -> AnalysisPrompt:
        """Build complete analysis prompt for AI processing."""
        disclaimer = self.compliance_templates.get_for_intent(
            classification.intent, 
            classification.risk_level
        )
        
        return AnalysisPrompt(
            system_prompt=self.system_prompt,
            user_prompt=user_query,
            intent=classification.intent,
            symbols=classification.symbols,
            tools_required=classification.tools_required,
            timeframe=classification.timeframe,
            risk_level=classification.risk_level,
            compliance_template=disclaimer
        )
    
    def classify_query(self, user_query: str) -> IntentClassification:
        """Main classification method - processes user query into structured result."""
        # Extract components
        symbols = self.extract_symbols(user_query)
        intent = self.classify_intent(user_query)
        tools_required = self.determine_tools_required(intent, symbols)
        confidence = self.calculate_confidence(intent, symbols, user_query)
        timeframe = self.determine_timeframe(intent, user_query)
        risk_level = self.determine_risk_level(intent, symbols)
        
        # Generate initial response (this will be enhanced by AI service)
        disclaimer = self.compliance_templates.get_for_intent(intent, risk_level)
        
        return IntentClassification(
            intent=intent,
            symbols=symbols,
            tools_required=tools_required,
            confidence=confidence,
            timeframe=timeframe,
            risk_level=risk_level,
            raw_response=f"Query classified as {intent.value}. {disclaimer}"
        )
    
    def get_prompt(self, prompt_type: str) -> str:
        """Get a specific prompt by type - main interface for prompt retrieval."""
        try:
            if prompt_type == "intent_detection":
                return self.get_intent_detection_prompt()
            elif prompt_type == "recommendation_personalization":
                return self.get_command_prompt("recommendations", "personalization")
            elif prompt_type == "profile_personalization":
                return self.get_command_prompt("recommendations", "profile")
            elif prompt_type == "ask_response":
                return self.get_command_prompt("ask", "response")
            elif prompt_type == "analyze_response":
                return self.get_command_prompt("analyze", "response")
            else:
                # Fallback to command prompt
                return self.get_command_prompt(prompt_type, "default")
        except Exception as e:
            # Return a basic fallback prompt
            return f"Please provide a helpful response for: {prompt_type}"


# Usage example and testing
if __name__ == "__main__":
    prompt_mgr = PromptManager()
    
    # Test cases
    test_queries = [
        "What is the price of $GME and all available indicator values?",
        "$AAPL current price",
        "What are some good options strategies for high volatility?",
        "How do I calculate position size for risk management?",
        "Explain RSI indicator"
    ]
    
    for query in test_queries:
        result = prompt_mgr.classify_query(query)
        print(f"\nQuery: {query}")
        print(f"Classification: {result.to_dict()}")