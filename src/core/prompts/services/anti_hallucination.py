"""
Anti-Hallucination Prompts

This module contains prompts for preventing AI hallucination and ensuring
data integrity in trading analysis responses.
"""

from typing import Dict, Any, Optional

class AntiHallucinationPrompts:
    """Manages prompts for anti-hallucination and data integrity"""
    
    def __init__(self):
        """Initialize anti-hallucination prompts"""
        pass
    
    def get_anti_hallucination_prompt(self, context: Dict[str, Any]) -> str:
        """
        Generate a strict anti-hallucination prompt for trading AI responses
        
        Args:
            context: Dictionary containing real data context
            
        Returns:
            Formatted prompt string with strict anti-hallucination rules
        """
        real_data_available = context.get('real_data_available', False)
        symbols = context.get('symbols', [])
        data_types = context.get('data_types', [])
        
        prompt = """## CRITICAL: ANTI-FABRICATION RULES (ABSOLUTELY NO EXCEPTIONS)

**NEVER FABRICATE ANY OF THE FOLLOWING:**
- Stock prices, current or historical
- Technical indicator values (RSI, MACD, etc.)
- Volume data or trading statistics
- Earnings dates or financial metrics
- Market events or economic data
- Options prices or volatility data
- Analyst ratings or price targets

**DATA INTEGRITY REQUIREMENTS:**
1. **Only use provided real data** - Never invent numbers
2. **Clearly state data limitations** - If data is missing, say so
3. **No speculative pricing** - Don't guess at current prices
4. **No fake scenarios** - Don't create elaborate fictional examples with specific prices
5. **Acknowledge uncertainty** - When unsure, explicitly state limitations

"""
        
        if real_data_available:
            prompt += f"""**REAL DATA CONTEXT:**
- Symbols with data: {', '.join(symbols) if symbols else 'None specified'}
- Available data types: {', '.join(data_types) if data_types else 'None specified'}
- Use ONLY this provided data for analysis
- If asked about data not provided, state "I don't have current data for [specific request]"

"""
        else:
            prompt += """**NO REAL DATA AVAILABLE:**
- Current market data is not accessible
- Provide educational content and general concepts only
- Clearly state that real-time data is needed for specific analysis
- Suggest where users can find current market information

"""
        
        prompt += """**ACCEPTABLE RESPONSES WHEN DATA IS MISSING:**
- "I need current market data to provide accurate analysis"
- "For real-time prices, please check your broker or financial data provider"
- "I can explain the concept, but current data is required for specific analysis"
- "This analysis requires live market data that I don't currently have access to"

**EDUCATIONAL CONTENT GUIDELINES:**
- You MAY explain trading concepts and strategies
- You MAY provide general market education
- You MAY discuss historical patterns in general terms
- You MAY suggest analytical approaches and methodologies
- You MUST include appropriate disclaimers and risk warnings

**VIOLATION EXAMPLES TO AVOID:**
❌ "AAPL is currently trading at $150.25"
❌ "The RSI for MSFT is 65.4, indicating..."
❌ "Based on current volume of 2.3M shares..."
❌ "Earnings are expected on January 15th..."

**CORRECT APPROACHES:**
✅ "To analyze AAPL's current price, you'll need real-time market data"
✅ "RSI analysis requires current price data that I don't have access to"
✅ "For current volume information, check your trading platform"
✅ "Earnings dates can be found on the company's investor relations page"

Remember: Users rely on accurate information for financial decisions. Fabricated data can cause real financial harm."""
        
        return prompt
    
    def get_data_validation_prompt(self, data: Dict[str, Any]) -> str:
        """
        Get prompt for validating provided data before use
        
        Args:
            data: Data to validate
            
        Returns:
            Data validation prompt
        """
        return f"""Validate this market data for accuracy and completeness before using it in analysis.

Data to validate: {data}

## Validation Checklist:
1. **Price Reasonableness**: Are prices within expected ranges?
2. **Data Freshness**: Is the timestamp recent and relevant?
3. **Completeness**: Are all required fields present?
4. **Consistency**: Do related values make sense together?
5. **Format Validity**: Are numbers properly formatted?

## Red Flags to Check:
- Prices that seem too high/low for the symbol
- Missing or null critical values
- Timestamps that are too old
- Inconsistent data relationships
- Obvious formatting errors

Return JSON validation result:
{{
  "is_valid": true/false,
  "validation_score": 0.0-1.0,
  "issues_found": ["list of specific issues"],
  "recommendations": ["suggested actions"],
  "safe_to_use": true/false
}}

If data fails validation, recommend requesting fresh data or using educational content only."""
    
    def get_uncertainty_communication_prompt(self) -> str:
        """
        Get prompt for properly communicating uncertainty and limitations
        
        Returns:
            Uncertainty communication guidelines
        """
        return """## COMMUNICATING UNCERTAINTY AND LIMITATIONS

When you don't have complete information or data, use these approaches:

**For Missing Data:**
- "I don't have access to current [specific data type] for [symbol]"
- "This analysis requires real-time data that I cannot access right now"
- "For the most current information, please check [specific reliable source]"

**For Uncertain Analysis:**
- "Based on general market principles..." (not specific current data)
- "Historically, this pattern has suggested..." (general historical context)
- "This type of analysis typically considers..." (educational approach)

**For Limitations:**
- "My analysis is limited by the available data"
- "This educational overview doesn't replace real-time market analysis"
- "Professional analysis would require current market data and additional factors"

**Confidence Levels:**
- High confidence: When using verified, current data
- Medium confidence: When using recent but not real-time data
- Low confidence: When providing general educational content
- No confidence: When data is missing or unreliable

**Always Include:**
- Clear statements about data limitations
- Suggestions for where to find current information
- Appropriate disclaimers about the educational nature of content
- Recommendations to verify information independently"""
    
    def get_fabrication_detection_prompt(self, response: str) -> str:
        """
        Get prompt for detecting potential fabrication in AI responses
        
        Args:
            response: AI response to check for fabrication
            
        Returns:
            Fabrication detection prompt
        """
        return f"""Analyze this AI response for potential data fabrication or hallucination.

Response to analyze: "{response}"

## Fabrication Indicators:
1. **Specific numerical data** without data source attribution
2. **Current prices or values** stated as fact
3. **Precise technical indicators** (RSI: 65.4, MACD: 1.23, etc.)
4. **Specific dates** for future events
5. **Exact volume or trading statistics**
6. **Detailed current market conditions**

## Acceptable Content:
- General educational explanations
- Historical pattern discussions (without specific recent data)
- Methodology explanations
- Conceptual analysis frameworks
- Properly disclaimed educational examples

## Red Flag Phrases:
- "Currently trading at..."
- "The RSI is..."
- "Volume today is..."
- "Earnings are scheduled for..."
- "The current price of..."

Return JSON analysis:
{{
  "fabrication_detected": true/false,
  "confidence": 0.0-1.0,
  "fabricated_elements": ["list of specific fabricated content"],
  "risk_level": "low|medium|high",
  "recommended_action": "approve|flag|reject|request_revision"
}}

Be strict - any specific current market data without clear sourcing should be flagged."""
