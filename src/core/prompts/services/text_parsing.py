"""
Text Parsing Prompts

This module contains prompts for AI-powered text parsing and extraction
of symbols, prices, and other financial data from user input.
"""

from typing import Dict, Any, Optional

class TextParsingPrompts:
    """Manages prompts for text parsing and data extraction"""
    
    def __init__(self):
        """Initialize text parsing prompts"""
        pass
    
    def get_symbol_extraction_prompt(self, text: str) -> str:
        """
        Get prompt for extracting stock symbols from text
        
        Args:
            text: Text to extract symbols from
            
        Returns:
            Symbol extraction prompt
        """
        return f"""Extract all stock symbols, tickers, and company identifiers from the following text.
Return a JSON list of objects with 'symbol', 'confidence', and 'context' fields.
Only include valid stock symbols (1-5 uppercase letters).

Text: "{text}"

## Extraction Rules:
1. **$ Prefix Symbols**: $AAPL, $MSFT → Extract directly (high confidence)
2. **Company Names**: "Apple", "Microsoft" → Convert to ticker if confident
3. **Ticker Mentions**: "AAPL stock", "MSFT shares" → Extract ticker
4. **Context Clues**: "Tesla earnings" → TSLA (if confident)

## Common Company → Ticker Mappings:
- Apple → AAPL
- Microsoft → MSFT  
- Google/Alphabet → GOOGL
- Amazon → AMZN
- Tesla → TSLA
- Meta/Facebook → META
- Netflix → NFLX
- Nvidia → NVDA
- AMD → AMD
- Intel → INTC

## Confidence Guidelines:
- 0.9-1.0: $ prefix symbols, exact ticker matches
- 0.7-0.9: Well-known company names in trading context
- 0.5-0.7: Company names without clear trading context
- 0.3-0.5: Ambiguous references that might be symbols
- 0.0-0.3: Very uncertain, probably not a symbol

Response format:
[{{"symbol": "AAPL", "confidence": 0.95, "context": "Apple mentioned with $ prefix"}}]

Return ONLY the JSON array, no other text."""
    
    def get_price_extraction_prompt(self, text: str) -> str:
        """
        Get prompt for extracting price values from text
        
        Args:
            text: Text to extract prices from
            
        Returns:
            Price extraction prompt
        """
        return f"""Extract all price values, monetary amounts, and financial figures from the following text.
Return a JSON list of objects with 'value', 'currency', 'confidence', and 'context' fields.

Text: "{text}"

## Price Pattern Recognition:
1. **Dollar amounts**: $150, $1,500, $1.5K, $1.5M, $1.5B
2. **Percentage values**: 5%, -2.3%, +10.5%
3. **Decimal numbers**: 150.50, 1500.00
4. **Range values**: $100-150, 5%-10%
5. **Strike prices**: 150 strike, 200 calls

## Currency Detection:
- $ → USD (default)
- € → EUR
- £ → GBP
- ¥ → JPY
- No symbol → USD (assume)

## Value Normalization:
- K/k → thousands (1.5K → 1500)
- M/m → millions (1.5M → 1500000)
- B/b → billions (1.5B → 1500000000)

## Context Types:
- "stock_price": Current or target stock prices
- "option_strike": Options strike prices
- "percentage": Percentage changes or values
- "target_price": Analyst price targets
- "range": Price ranges or bands

Response format:
[{{"value": 150.50, "currency": "USD", "confidence": 0.9, "context": "stock_price"}}]

Return ONLY the JSON array, no other text."""
    
    def get_date_extraction_prompt(self, text: str) -> str:
        """
        Get prompt for extracting dates and timeframes from text
        
        Args:
            text: Text to extract dates from
            
        Returns:
            Date extraction prompt
        """
        return f"""Extract all dates, timeframes, and temporal references from the following text.
Return a JSON list of objects with 'date', 'type', 'confidence', and 'context' fields.

Text: "{text}"

## Date Pattern Recognition:
1. **Specific dates**: "Jan 15", "January 15, 2025", "01/15/2025"
2. **Relative dates**: "tomorrow", "next week", "in 2 days"
3. **Expiration dates**: "Jan 15 expiry", "15 DTE", "monthly expiration"
4. **Earnings dates**: "earnings next week", "Q4 earnings"
5. **Market events**: "FOMC meeting", "jobs report Friday"

## Timeframe Types:
- "specific_date": Exact calendar date
- "relative_date": Relative to current date
- "expiration": Options expiration
- "earnings": Earnings announcement
- "market_event": Scheduled market event
- "timeframe": General time period

## Confidence Guidelines:
- 0.9-1.0: Specific dates with clear format
- 0.7-0.9: Well-defined relative dates
- 0.5-0.7: General timeframes
- 0.3-0.5: Vague temporal references

Response format:
[{{"date": "2025-01-15", "type": "expiration", "confidence": 0.9, "context": "options expiry"}}]

Return ONLY the JSON array, no other text."""
    
    def get_intent_keywords_prompt(self, text: str) -> str:
        """
        Get prompt for extracting intent-indicating keywords
        
        Args:
            text: Text to extract keywords from
            
        Returns:
            Intent keywords extraction prompt
        """
        return f"""Extract keywords that indicate trading intent from the following text.
Return a JSON object with categorized keywords and confidence scores.

Text: "{text}"

## Keyword Categories:

**Technical Analysis**: chart, technical, RSI, MACD, support, resistance, indicators, pattern, trend, momentum, volume, moving average, bollinger, stochastic

**Fundamental Analysis**: earnings, revenue, P/E, fundamentals, valuation, financial, balance sheet, income, cash flow, growth, dividend

**Options Trading**: options, calls, puts, strike, expiry, volatility, premium, greeks, delta, gamma, theta, vega

**Price Action**: price, quote, current, trading, worth, value, cost, bid, ask, spread

**Market Sentiment**: sentiment, bullish, bearish, outlook, forecast, prediction, opinion, analysis

**Risk Management**: risk, stop loss, position size, portfolio, diversification, hedge, protection

**Educational**: explain, how to, what is, learn, teach, basics, beginner, understand

Response format:
{{
  "technical_analysis": {{"keywords": ["chart", "RSI"], "confidence": 0.8}},
  "fundamental_analysis": {{"keywords": ["earnings"], "confidence": 0.6}},
  "options_trading": {{"keywords": [], "confidence": 0.0}},
  "price_action": {{"keywords": ["price"], "confidence": 0.9}},
  "market_sentiment": {{"keywords": [], "confidence": 0.0}},
  "risk_management": {{"keywords": [], "confidence": 0.0}},
  "educational": {{"keywords": [], "confidence": 0.0}},
  "primary_category": "price_action",
  "overall_confidence": 0.85
}}

Return ONLY the JSON object, no other text."""
