"""
Intent Detection Prompts

This module contains prompts for AI-powered intent detection and classification
for trading queries and user interactions.
"""

from typing import Dict, Any, Optional, List

class IntentDetectionPrompts:
    """Manages prompts for intent detection and classification"""
    
    def __init__(self):
        """Initialize intent detection prompts"""
        self.intent_categories = [
            "price_check",
            "technical_analysis", 
            "fundamental_analysis",
            "options_strategy",
            "market_overview",
            "risk_management",
            "educational"
        ]
    
    def get_intent_classification_prompt(self, query: str) -> str:
        """
        Get prompt for AI-powered intent classification
        
        Args:
            query: User query to classify
            
        Returns:
            Formatted prompt for intent classification
        """
        return f"""You are a financial intent classifier. Analyze this query and return ONLY a valid JSON object.

Query: "{query}"

CRITICAL: Return ONLY valid JSON, no other text. Use this exact format:

{{
  "intent": "one of: {', '.join(self.intent_categories)}",
  "confidence": 0.0-1.0,
  "reasoning": "brief explanation",
  "symbols": ["extracted symbols without $ prefix"],
  "requires_data": true/false
}}

## Intent Definitions:

**price_check**: Current price requests, quotes, basic market data
- Keywords: "price", "quote", "current", "trading at", "worth"
- Examples: "What's AAPL trading at?", "Current price of SPY"

**technical_analysis**: Chart patterns, indicators, technical data requests
- Keywords: "technical", "chart", "RSI", "MACD", "support", "resistance", "indicators"
- Examples: "AAPL technical analysis", "Show me MSFT indicators"

**fundamental_analysis**: Financial metrics, earnings, valuation analysis
- Keywords: "earnings", "revenue", "P/E", "fundamentals", "valuation", "financial"
- Examples: "AAPL earnings analysis", "MSFT fundamental metrics"

**options_strategy**: Options plays, strategies, volatility analysis
- Keywords: "options", "calls", "puts", "strategy", "volatility", "strike"
- Examples: "Options strategy for AAPL", "Best calls for earnings"

**market_overview**: Sector analysis, broad market conditions
- Keywords: "market", "sector", "overview", "sentiment", "broad", "general"
- Examples: "Market overview today", "Tech sector analysis"

**risk_management**: Position sizing, stop losses, portfolio protection
- Keywords: "risk", "position size", "stop loss", "portfolio", "protection"
- Examples: "How to manage risk", "Position sizing strategy"

**educational**: Trading concepts, strategy explanations, learning
- Keywords: "explain", "how to", "what is", "learn", "teach", "basics"
- Examples: "Explain options", "How to read charts", "What is RSI"

## Symbol Extraction Rules:
- Extract symbols from $ prefixes: $AAPL → ["AAPL"]
- Extract company names: "Apple" → ["AAPL"] (if confident)
- Extract ticker mentions: "MSFT stock" → ["MSFT"]
- Return empty array if no clear symbols

## Data Requirements:
- requires_data: true for price_check, technical_analysis, fundamental_analysis, options_strategy
- requires_data: false for educational, risk_management, general market_overview

Analyze the query and respond with ONLY the JSON object."""
    
    def get_enhanced_intent_prompt(self, 
                                 query: str,
                                 context: Optional[Dict[str, Any]] = None) -> str:
        """
        Get enhanced intent classification prompt with context
        
        Args:
            query: User query to classify
            context: Additional context (previous queries, user preferences, etc.)
            
        Returns:
            Enhanced prompt with context
        """
        base_prompt = self.get_intent_classification_prompt(query)
        
        if not context:
            return base_prompt
        
        context_additions = []
        
        # Previous query context
        if context.get('previous_queries'):
            prev_queries = context['previous_queries'][-3:]  # Last 3 queries
            context_additions.append(f"""
## Session Context:
Previous queries in this session:
{chr(10).join(f'- {q}' for q in prev_queries)}

Consider this context when classifying the current query.""")
        
        # User preferences context
        if context.get('user_preferences'):
            prefs = context['user_preferences']
            context_additions.append(f"""
## User Preferences:
- Experience Level: {prefs.get('experience_level', 'unknown')}
- Risk Tolerance: {prefs.get('risk_tolerance', 'unknown')}
- Preferred Analysis: {prefs.get('preferred_analysis', 'unknown')}

Adjust confidence based on user's typical query patterns.""")
        
        # Market context
        if context.get('market_context'):
            market = context['market_context']
            context_additions.append(f"""
## Market Context:
- Market Hours: {market.get('is_market_open', 'unknown')}
- Market Sentiment: {market.get('sentiment', 'unknown')}
- Volatility: {market.get('volatility_level', 'unknown')}

Consider market conditions when determining data requirements.""")
        
        if context_additions:
            return base_prompt + '\n'.join(context_additions)
        
        return base_prompt
    
    def get_symbol_extraction_prompt(self, text: str) -> str:
        """
        Get prompt specifically for symbol extraction
        
        Args:
            text: Text to extract symbols from
            
        Returns:
            Symbol extraction prompt
        """
        return f"""Extract all stock symbols, tickers, and company identifiers from the following text.
Return a JSON list of objects with 'symbol', 'confidence', and 'context' fields.
Only include valid stock symbols (1-5 uppercase letters).

Text: "{text}"

## Extraction Rules:
1. **$ Prefix Symbols**: $AAPL, $MSFT → Extract directly
2. **Company Names**: "Apple", "Microsoft" → Convert to ticker if confident
3. **Ticker Mentions**: "AAPL stock", "MSFT shares" → Extract ticker
4. **Context Clues**: "Tesla earnings" → TSLA (if confident)

## Confidence Levels:
- 0.9-1.0: $ prefix symbols, clear ticker mentions
- 0.7-0.9: Well-known company names with clear context
- 0.5-0.7: Company names without clear trading context
- 0.3-0.5: Ambiguous references that might be symbols
- 0.0-0.3: Very uncertain, probably not a symbol

Response format:
[{{"symbol": "AAPL", "confidence": 0.95, "context": "Apple stock mentioned with $ prefix"}}]

Return ONLY the JSON array, no other text."""
    
    def get_confidence_calibration_prompt(self, 
                                        query: str,
                                        initial_classification: Dict[str, Any]) -> str:
        """
        Get prompt for confidence calibration and validation
        
        Args:
            query: Original query
            initial_classification: Initial classification result
            
        Returns:
            Confidence calibration prompt
        """
        return f"""Review and calibrate the confidence level for this intent classification.

Original Query: "{query}"

Initial Classification:
- Intent: {initial_classification.get('intent')}
- Confidence: {initial_classification.get('confidence')}
- Reasoning: {initial_classification.get('reasoning')}

## Confidence Calibration Guidelines:

**0.9-1.0 (Very High)**: 
- Clear, unambiguous intent with specific keywords
- Examples: "What's AAPL price?", "Show me MSFT technical indicators"

**0.7-0.9 (High)**:
- Clear intent with minor ambiguity
- Examples: "Analyze Apple", "MSFT outlook"

**0.5-0.7 (Medium)**:
- Somewhat ambiguous, could fit multiple intents
- Examples: "Thoughts on AAPL?", "MSFT analysis"

**0.3-0.5 (Low)**:
- Ambiguous query, intent unclear
- Examples: "What about Apple?", "Any ideas?"

**0.0-0.3 (Very Low)**:
- Very unclear or off-topic
- Examples: "Hello", "Random text"

## Calibration Factors:
- Keyword specificity and clarity
- Context and trading relevance
- Symbol clarity and extraction confidence
- Query completeness and structure

Return ONLY a JSON object with calibrated confidence:
{{"calibrated_confidence": 0.0-1.0, "calibration_reasoning": "brief explanation"}}"""
    
    def get_multi_intent_detection_prompt(self, query: str) -> str:
        """
        Get prompt for detecting multiple intents in a single query
        
        Args:
            query: Query that might contain multiple intents
            
        Returns:
            Multi-intent detection prompt
        """
        return f"""Analyze this query for multiple trading intents and return a JSON object.

Query: "{query}"

Some queries contain multiple intents. For example:
- "What's AAPL price and technical analysis?" → price_check + technical_analysis
- "MSFT earnings and options strategy" → fundamental_analysis + options_strategy
- "Market overview and risk management tips" → market_overview + risk_management

## Analysis Instructions:
1. Identify ALL distinct intents in the query
2. Rank them by prominence/importance
3. Extract symbols relevant to each intent
4. Determine if each intent requires real-time data

Return ONLY this JSON format:
{{
  "primary_intent": "most prominent intent",
  "secondary_intents": ["list of other intents"],
  "intent_breakdown": {{
    "intent_name": {{
      "confidence": 0.0-1.0,
      "symbols": ["relevant symbols"],
      "requires_data": true/false,
      "query_portion": "part of query for this intent"
    }}
  }},
  "overall_confidence": 0.0-1.0,
  "is_multi_intent": true/false
}}

If only one clear intent is found, set "is_multi_intent": false and focus on the primary intent."""
