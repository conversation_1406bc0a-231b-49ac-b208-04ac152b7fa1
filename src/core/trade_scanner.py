import json
from typing import List, Dict, Any
import logging
from datetime import datetime, timedelta
import aiohttp

from src.api.data.cache import market_data_cache
from src.analysis.probability.probability_engine import ProbabilityEngine
from src.core.config_manager import get_config
from src.bot.setup_audit import get_default_webhook_url

logger = logging.getLogger(__name__)

class TradeOpportunity:
    """Represents a trade opportunity"""
    def __init__(self, symbol: str, score: float, probability: float, confidence: float, 
                 indicators: Dict[str, Any], timestamp: str):
        self.symbol = symbol
        self.score = score
        self.probability = probability
        self.confidence = confidence
        self.indicators = indicators
        self.timestamp = timestamp

class TradeScanner:
    """Scans for above-average trade opportunities"""
    
    def __init__(self):
        self.probability_engine = ProbabilityEngine()
        self.min_probability = 0.7  # Minimum bullish probability
        self.min_confidence = 0.8   # Minimum confidence
        self.max_opportunities = 10 # Maximum opportunities to track
    
    async def scan_opportunities(self, symbols: List[str]) -> List[TradeOpportunity]:
        """Scan symbols for trade opportunities"""
        opportunities = []
        
        for symbol in symbols:
            try:
                # Get prebaked data
                end_date = datetime.now()
                start_date = end_date - timedelta(days=30)
                historical_data = await market_data_cache.get_historical_data(symbol, start_date=start_date, end_date=end_date)
                if not historical_data:
                    continue
                
                # Create DataFrame
                import pandas as pd
                df = pd.DataFrame([{
                    'open': d.price,
                    'high': d.price,
                    'low': d.price,
                    'close': d.price,
                    'volume': d.volume if hasattr(d, 'volume') else 1000000
                } for d in historical_data])
                
                # Get prebaked probability assessment
                assessment = self.probability_engine.assess_probabilities(
                    symbol, '1d', df, pd.DataFrame({'volume': df['volume']}), 0.0
                )
                
                # Get prebaked indicators
                from src.shared.technical_analysis.calculator import technical_analysis_calculator
                indicators = technical_analysis_calculator.calculate_all_indicators(df, symbol)
                
                # Score opportunity
                score = self._calculate_opportunity_score(assessment, indicators)
                
                if score > 0.7:  # Above-average threshold
                    opportunity = TradeOpportunity(
                        symbol=symbol,
                        score=score,
                        probability=assessment.bullish_probability,
                        confidence=assessment.confidence_level,
                        indicators=indicators,
                        timestamp=datetime.now().isoformat()
                    )
                    opportunities.append(opportunity)
                
            except Exception as e:
                logger.error(f"Error scanning {symbol}: {e}")
                continue
        
        # Sort by score and return top N
        opportunities.sort(key=lambda x: x.score, reverse=True)
        return opportunities[:self.max_opportunities]
    
    def _calculate_opportunity_score(self, assessment, indicators) -> float:
        """Calculate overall opportunity score"""
        score = 0.0
        
        # Probability component (40%)
        if assessment.bullish_probability > self.min_probability:
            score += 0.4 * assessment.bullish_probability
        
        # Confidence component (30%)
        if assessment.confidence_level > self.min_confidence:
            score += 0.3 * assessment.confidence_level
        
        # Technical indicators component (20%)
        if indicators.get('rsi') and indicators['rsi'] < 30:  # Oversold
            score += 0.2 * 0.8
        elif indicators.get('rsi') and indicators['rsi'] > 70:  # Overbought (avoid)
            score -= 0.1
        else:
            score += 0.2 * 0.5  # Neutral
        
        # Trend component (10%)
        if (indicators.get('sma_20') and indicators.get('ema_20') and 
            indicators['close'] > indicators['sma_20'] > indicators['ema_20']):
            score += 0.1  # Uptrend
        
        return min(1.0, max(0.0, score))

async def send_discord_alert(webhook_url: str, opportunity: TradeOpportunity):
    """Send Discord alert for high-score opportunity via webhook"""
    if not webhook_url:
        logger.warning("No webhook URL configured for alerts")
        return

    embed = {
        "title": "🚨 High-Score Trade Opportunity Alert",
        "description": f"**{opportunity.symbol}** detected with high potential!",
        "color": 0x00ff00,  # Green
        "fields": [
            {"name": "Score", "value": f"{opportunity.score:.2f}", "inline": True},
            {"name": "Bullish Probability", "value": f"{opportunity.probability:.2f}", "inline": True},
            {"name": "Confidence", "value": f"{opportunity.confidence:.2f}", "inline": True},
            {"name": "Timestamp", "value": opportunity.timestamp, "inline": False}
        ],
        "timestamp": datetime.utcnow().isoformat()
    }

    payload = {"embeds": [embed]}

    async with aiohttp.ClientSession() as session:
        try:
            async with session.post(webhook_url, json=payload) as response:
                if response.status == 204:
                    logger.info(f"Discord alert sent for {opportunity.symbol}")
                else:
                    logger.error(f"Failed to send Discord alert: {response.status}")
        except Exception as e:
            logger.error(f"Error sending Discord alert: {e}")


async def update_trade_opportunities():
    """Update trade opportunities cache"""
    from src.api.data.cache import market_data_cache
    from src.core.config_manager import get_config
    
    config = get_config()
    symbols = config.get('top_symbols', ['AAPL', 'GOOGL', 'MSFT', 'TSLA', 'NVDA'])
    
    scanner = TradeScanner()
    opportunities = await scanner.scan_opportunities(symbols)
    
    # Cache opportunities
    cache_key = "trade_opportunities"
    cache_data = {
        'opportunities': [op.__dict__ for op in opportunities],
        'updated': datetime.now().isoformat(),
        'count': len(opportunities)
    }
    
    redis = await market_data_cache._get_redis_client()
    await redis.setex(cache_key, 900, json.dumps(cache_data))  # 15 min TTL
    
    logger.info(f"Updated {len(opportunities)} trade opportunities")

    # Send Discord alerts for high-score opportunities
    webhook_url = get_default_webhook_url()
    high_score_ops = [op for op in opportunities if op.score > 0.8]
    for op in high_score_ops:
        await send_discord_alert(webhook_url, op)

    return opportunities


async def get_cached_opportunities() -> List[TradeOpportunity]:
    """Get cached trade opportunities"""
    from src.api.data.cache import market_data_cache
    
    redis = await market_data_cache._get_redis_client()
    cache_key = "trade_opportunities"
    cached_data = await redis.get(cache_key)
    
    if cached_data:
        data = json.loads(cached_data)
        return [TradeOpportunity(**op) for op in data['opportunities']]
    
    return []