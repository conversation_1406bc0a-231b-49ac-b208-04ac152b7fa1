"""
Simplified Structured Logging for ASK Pipeline

Merged from observability/logger.py:
- JSON structured logging with correlation ID
- Basic sensitive data redaction
- Audit logging with DB persistence using unified_db
- Integration with core ConfigManager
"""

import asyncio
import json
import logging
import uuid
import re
import hashlib
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field, asdict
from datetime import datetime, timedelta
from enum import Enum
from pathlib import Path
import threading
from contextvars import ContextVar

from src.core.config_manager import ConfigManager
# from src.database.unified_db import get_db_session, AuditLogModel  # Not implemented yet

# Mock implementation for now
async def get_db_session():
    """Mock database session for now"""
    class MockSession:
        def __enter__(self):
            return self
        def __exit__(self, exc_type, exc_val, exc_tb):
            pass
        def add(self, obj):
            pass
        async def commit(self):
            pass
    return MockSession()

class AuditLogModel:
    """Mock audit log model for now"""
    def __init__(self, **kwargs):
        pass

logger = logging.getLogger(__name__)

# Context variable for correlation ID
correlation_id_context: ContextVar[str] = ContextVar('correlation_id', default='')

class LogLevel(Enum):
    """Log levels"""
    DEBUG = 10
    INFO = 20
    WARNING = 30
    ERROR = 40
    CRITICAL = 50
    AUDIT = 60

class LogCategory(Enum):
    """Log categories"""
    PIPELINE = "pipeline"
    AUDIT = "audit"
    ERROR = "error"

@dataclass
class LogContext:
    """Context for logging"""
    correlation_id: str = ""
    user_id: Optional[str] = None
    stage: Optional[str] = None
    additional_data: Dict[str, Any] = field(default_factory=dict)

@dataclass
class AuditLogEntry:
    """Simplified audit entry"""
    timestamp: str
    correlation_id: str
    user_id_hash: str
    action: str
    success: bool
    execution_time: float

class SensitiveDataRedactor:
    """Basic sensitive data redactor"""
    
    def __init__(self):
        self.patterns = {
            'api_key': re.compile(r'(?i)(api[_-]?key|token|secret)["\s]*[:=]["\s]*([a-zA-Z0-9_-]{20,})'),
            'email': re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'),
        }
    
    def redact_message(self, message: str) -> str:
        """Redact sensitive data from message"""
        redacted = message
        for pattern in self.patterns.values():
            redacted = pattern.sub('[REDACTED]', redacted)
        return redacted
    
    def redact_dict(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Redact sensitive data from dict"""
        if not isinstance(data, dict):
            return data
        redacted = {}
        for key, value in data.items():
            if 'key' in key.lower() or 'token' in key.lower():
                redacted[key] = "[REDACTED]"
            elif isinstance(value, dict):
                redacted[key] = self.redact_dict(value)
            elif isinstance(value, str):
                redacted[key] = self.redact_message(value)
            else:
                redacted[key] = value
        return redacted

class StructuredLogger:
    """Simplified structured logger with DB integration"""
    
    def __init__(self, name: str, config: ConfigManager):
        self.name = name
        self.config = config
        self.redactor = SensitiveDataRedactor()
        self.audit_enabled = config.get("logging.audit_enabled", True)
        self.audit_retention_days = config.get("logging.audit_retention_days", 90)
        self._setup_logger()
        self.log_stats = {'total_logs': 0, 'audit_logs': 0}
    
    def _setup_logger(self):
        """Setup basic logger"""
        self.logger = logging.getLogger(self.name)
        self.logger.setLevel(logging.INFO)
        handler = logging.StreamHandler()
        handler.setFormatter(logging.Formatter('%(message)s'))
        self.logger.addHandler(handler)
        self.logger.propagate = False
    
    def _create_log_entry(
        self,
        level: LogLevel,
        message: str,
        context: Optional[LogContext] = None,
        category: LogCategory = LogCategory.PIPELINE,
        extra_data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Create log entry"""
        correlation_id = (
            context.correlation_id if context and context.correlation_id
            else correlation_id_context.get() or str(uuid.uuid4())[:8]
        )
        
        log_entry = {
            'timestamp': datetime.utcnow().isoformat() + 'Z',
            'level': level.name,
            'category': category.value,
            'logger': self.name,
            'correlation_id': correlation_id,
            'message': self.redactor.redact_message(message)
        }
        
        if context:
            context_dict = asdict(context)
            context_dict = {k: v for k, v in context_dict.items() if v}
            context_dict = self.redactor.redact_dict(context_dict)
            log_entry['context'] = context_dict
        
        if extra_data:
            redacted_extra = self.redactor.redact_dict(extra_data)
            log_entry['extra'] = redacted_extra
        
        return log_entry
    
    def _log(
        self,
        level: LogLevel,
        message: str,
        context: Optional[LogContext] = None,
        category: LogCategory = LogCategory.PIPELINE,
        extra_data: Optional[Dict[str, Any]] = None
    ):
        """Internal log method"""
        log_entry = self._create_log_entry(level, message, context, category, extra_data)
        json_message = json.dumps(log_entry, ensure_ascii=False)
        
        # Log to console
        self.logger.info(json_message)
        
        self.log_stats['total_logs'] += 1
        
        # Handle audit
        if category == LogCategory.AUDIT and self.audit_enabled:
            asyncio.create_task(self._handle_audit_log(log_entry))
    
    def debug(self, message: str, context: Optional[LogContext] = None, **kwargs):
        """Log debug"""
        # Extract known parameters to avoid conflicts
        category = kwargs.pop('category', LogCategory.PIPELINE)
        extra_data = kwargs.pop('extra_data', None) or kwargs.pop('extra', None)
        self._log(LogLevel.DEBUG, message, context, category, extra_data)

    def info(self, message: str, context: Optional[LogContext] = None, **kwargs):
        """Log info"""
        # Extract known parameters to avoid conflicts
        category = kwargs.pop('category', LogCategory.PIPELINE)
        extra_data = kwargs.pop('extra_data', None) or kwargs.pop('extra', None)
        self._log(LogLevel.INFO, message, context, category, extra_data)

    def error(self, message: str, context: Optional[LogContext] = None, **kwargs):
        """Log error"""
        # Extract known parameters to avoid conflicts
        kwargs.pop('category', None)  # Remove category if present in kwargs
        extra_data = kwargs.pop('extra_data', None) or kwargs.pop('extra', None)
        self._log(LogLevel.ERROR, message, context, LogCategory.ERROR, extra_data)
    
    async def audit(
        self,
        action: str,
        user_id: Optional[str] = None,
        success: bool = True,
        context: Optional[LogContext] = None,
        **kwargs
    ):
        """Log audit with DB persistence"""
        correlation_id = context.correlation_id if context else correlation_id_context.get() or str(uuid.uuid4())[:8]
        user_id_hash = self._hash_pii(user_id) if user_id else ""
        execution_time = kwargs.get('execution_time', 0.0)
        
        audit_entry = AuditLogEntry(
            timestamp=datetime.utcnow().isoformat() + 'Z',
            correlation_id=correlation_id,
            user_id_hash=user_id_hash,
            action=action,
            success=success,
            execution_time=execution_time
        )
        
        audit_message = f"Audit: {action} - User: {user_id_hash[:8]}... - Success: {success}"
        
        self._log(
            LogLevel.AUDIT,
            audit_message,
            context,
            LogCategory.AUDIT,
            extra_data={'audit_entry': asdict(audit_entry)}
        )
        
        # Persist to DB
        await self._persist_audit_entry(audit_entry)
    
    async def _persist_audit_entry(self, audit_entry: AuditLogEntry):
        """Persist audit to unified_db"""
        try:
            # Use mock implementation for now
            session = await get_db_session()
            with session:
                db_entry = AuditLogModel(**asdict(audit_entry))
                session.add(db_entry)
                await session.commit()
                logger.debug("Audit entry persisted to DB")
        except Exception as e:
            logger.error(f"Failed to persist audit: {e}")
    
    def _hash_pii(self, data: Optional[str]) -> str:
        """Hash PII"""
        if not data:
            return ""
        salt = "ask_audit_salt"
        return hashlib.sha256(f"{salt}{data}".encode()).hexdigest()
    
    async def _handle_audit_log(self, log_entry: Dict[str, Any]):
        """Handle audit persistence"""
        audit_data = log_entry.get('extra', {}).get('audit_entry', {})
        if audit_data:
            audit_entry = AuditLogEntry(**audit_data)
            await self._persist_audit_entry(audit_entry)
            self.log_stats['audit_logs'] += 1

# Global logger factory
config_manager = ConfigManager()
_loggers: Dict[str, StructuredLogger] = {}

def get_structured_logger(name: str) -> StructuredLogger:
    """Get logger"""
    if name not in _loggers:
        _loggers[name] = StructuredLogger(name, config_manager)
    return _loggers[name]

def set_correlation_id(correlation_id: str):
    """Set correlation ID"""
    correlation_id_context.set(correlation_id)

def get_correlation_id() -> str:
    """Get correlation ID"""
    return correlation_id_context.get()

def clear_correlation_id():
    """Clear correlation ID"""
    correlation_id_context.set("")