"""
Simplified Health Monitoring for ASK Pipeline

Merged from observability/health_checker.py:
- Basic component health checks
- Integration with unified_db for health status persistence
- Use core ConfigManager for configuration
- Simplified recovery with logging
"""

import asyncio
import time
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import logging

from src.core.config_manager import ConfigManager
from src.database.unified_db import get_db_session, HealthStatusModel  # Assuming models exist or create simple

logger = logging.getLogger(__name__)

class HealthStatus(Enum):
    """Health status levels"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"

class ComponentType(Enum):
    """Types of components to monitor"""
    DATABASE = "database"
    CACHE = "cache"
    AI_SERVICE = "ai_service"
    EXTERNAL_API = "external_api"

@dataclass
class HealthCheckResult:
    """Result of a health check"""
    component: str
    component_type: ComponentType
    status: HealthStatus
    response_time: float
    message: str
    timestamp: datetime
    details: Dict[str, Any] = field(default_factory=dict)
    error: Optional[str] = None

@dataclass
class ComponentConfig:
    """Configuration for a component health check"""
    name: str
    component_type: ComponentType
    check_function: callable
    check_interval: int = 30  # seconds
    timeout: int = 10  # seconds
    retry_count: int = 3
    critical: bool = True

class HealthChecker:
    """Simplified health monitoring system integrated with core"""
    
    def __init__(self, config: ConfigManager):
        self.config = config
        self.components: Dict[str, ComponentConfig] = {}
        self.health_results: Dict[str, HealthCheckResult] = {}
        self.monitoring_active = False
        self.health_stats = {
            'total_checks': 0,
            'successful_checks': 0,
            'failed_checks': 0,
            'uptime_start': datetime.utcnow()
        }
        
        # Initialize standard checks
        self._initialize_standard_checks()
    
    def _initialize_standard_checks(self):
        """Initialize standard health checks using core config"""
        
        # Database health check
        self.register_component(
            ComponentConfig(
                name="database",
                component_type=ComponentType.DATABASE,
                check_function=self._check_database_health,
                check_interval=30,
                timeout=5,
                critical=True
            )
        )
        
        # Cache health check
        self.register_component(
            ComponentConfig(
                name="cache",
                component_type=ComponentType.CACHE,
                check_function=self._check_cache_health,
                check_interval=30,
                timeout=5,
                critical=False
            )
        )
        
        # AI service health check
        self.register_component(
            ComponentConfig(
                name="ai_service",
                component_type=ComponentType.AI_SERVICE,
                check_function=self._check_ai_service_health,
                check_interval=60,
                timeout=15,
                critical=True
            )
        )
    
    def register_component(self, config: ComponentConfig):
        """Register a component for health monitoring"""
        self.components[config.name] = config
        logger.info(f"Registered health check for {config.name}")
    
    async def start_monitoring(self):
        """Start health monitoring"""
        if self.monitoring_active:
            return
        
        self.monitoring_active = True
        
        # Start monitoring tasks
        tasks = []
        for name, config in self.components.items():
            task = asyncio.create_task(self._monitor_component(name, config))
            tasks.append(task)
        
        # Persist initial status to DB
        await self._persist_health_status()
        
        logger.info(f"Started health monitoring for {len(self.components)} components")
        
        # Wait for tasks (in production, this would run in background)
        if tasks:
            await asyncio.gather(*tasks)
    
    async def _monitor_component(self, name: str, config: ComponentConfig):
        """Monitor a single component continuously"""
        while self.monitoring_active:
            try:
                result = await self._perform_health_check(config)
                
                self.health_results[name] = result
                self.health_stats['total_checks'] += 1
                
                if result.status == HealthStatus.HEALTHY:
                    self.health_stats['successful_checks'] += 1
                else:
                    self.health_stats['failed_checks'] += 1
                
                # Log and persist
                logger.info(f"Health check for {name}: {result.status.value} - {result.message}")
                await self._persist_health_status()
                
                # Simple recovery placeholder
                if result.status == HealthStatus.UNHEALTHY and config.critical:
                    await self._attempt_recovery(name, config)
                
                await asyncio.sleep(config.check_interval)
                
            except Exception as e:
                logger.error(f"Monitoring error for {name}: {e}")
                await asyncio.sleep(config.check_interval)
    
    async def _perform_health_check(self, config: ComponentConfig) -> HealthCheckResult:
        """Perform health check with timeout and retry"""
        last_error = None
        
        for attempt in range(config.retry_count):
            try:
                start_time = time.time()
                result = await asyncio.wait_for(
                    config.check_function(),
                    timeout=config.timeout
                )
                result.response_time = time.time() - start_time
                return result
                
            except asyncio.TimeoutError:
                last_error = f"Timeout after {config.timeout}s"
            except Exception as e:
                last_error = str(e)
        
        return HealthCheckResult(
            component=config.name,
            component_type=config.component_type,
            status=HealthStatus.UNHEALTHY,
            response_time=config.timeout,
            message=f"Health check failed after {config.retry_count} attempts",
            timestamp=datetime.utcnow(),
            error=last_error
        )
    
    async def _attempt_recovery(self, name: str, config: ComponentConfig):
        """Placeholder recovery with logging"""
        logger.warning(f"Attempting recovery for critical component: {name}")
        # In production, implement actual recovery
        # For now, just log
        await asyncio.sleep(1)
        logger.info(f"Recovery placeholder completed for {name}")
    
    # Standard health check implementations integrated with core
    
    async def _check_database_health(self) -> HealthCheckResult:
        """Check database health using unified_db"""
        try:
            async with get_db_session() as session:
                # Simple query to test connection
                await session.execute("SELECT 1")
                return HealthCheckResult(
                    component="database",
                    component_type=ComponentType.DATABASE,
                    status=HealthStatus.HEALTHY,
                    response_time=0.0,
                    message="Database connection healthy",
                    timestamp=datetime.utcnow()
                )
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return HealthCheckResult(
                component="database",
                component_type=ComponentType.DATABASE,
                status=HealthStatus.UNHEALTHY,
                response_time=0.0,
                message="Database connection failed",
                timestamp=datetime.utcnow(),
                error=str(e)
            )
    
    async def _check_cache_health(self) -> HealthCheckResult:
        """Check cache health (placeholder using config)"""
        try:
            # Placeholder: check if cache URL is configured
            cache_url = self.config.get("cache.url", "")
            if cache_url:
                # Simulate cache check
                await asyncio.sleep(0.1)
                return HealthCheckResult(
                    component="cache",
                    component_type=ComponentType.CACHE,
                    status=HealthStatus.HEALTHY,
                    response_time=0.1,
                    message="Cache configuration healthy",
                    timestamp=datetime.utcnow()
                )
            else:
                return HealthCheckResult(
                    component="cache",
                    component_type=ComponentType.CACHE,
                    status=HealthStatus.DEGRADED,
                    response_time=0.0,
                    message="Cache not configured",
                    timestamp=datetime.utcnow()
                )
        except Exception as e:
            return HealthCheckResult(
                component="cache",
                component_type=ComponentType.CACHE,
                status=HealthStatus.UNHEALTHY,
                response_time=0.0,
                message="Cache check failed",
                timestamp=datetime.utcnow(),
                error=str(e)
            )
    
    async def _check_ai_service_health(self) -> HealthCheckResult:
        """Check AI service health (placeholder)"""
        try:
            # Placeholder: check AI config
            ai_endpoint = self.config.get("ai.endpoint", "")
            if ai_endpoint:
                await asyncio.sleep(0.1)  # Simulate check
                return HealthCheckResult(
                    component="ai_service",
                    component_type=ComponentType.AI_SERVICE,
                    status=HealthStatus.HEALTHY,
                    response_time=0.1,
                    message="AI service configuration healthy",
                    timestamp=datetime.utcnow()
                )
            else:
                return HealthCheckResult(
                    component="ai_service",
                    component_type=ComponentType.AI_SERVICE,
                    status=HealthStatus.DEGRADED,
                    response_time=0.0,
                    message="AI service not configured",
                    timestamp=datetime.utcnow()
                )
        except Exception as e:
            return HealthCheckResult(
                component="ai_service",
                component_type=ComponentType.AI_SERVICE,
                status=HealthStatus.UNHEALTHY,
                response_time=0.0,
                message="AI service check failed",
                timestamp=datetime.utcnow(),
                error=str(e)
            )
    
    async def _persist_health_status(self):
        """Persist health status to unified_db"""
        try:
            async with get_db_session() as session:
                for name, result in self.health_results.items():
                    # Assuming HealthStatusModel exists; create if needed
                    health_record = HealthStatusModel(
                        component=name,
                        status=result.status.value,
                        message=result.message,
                        timestamp=result.timestamp,
                        details=str(result.details)
                    )
                    session.add(health_record)
                await session.commit()
                logger.debug("Health status persisted to DB")
        except Exception as e:
            logger.error(f"Failed to persist health status: {e}")
    
    def get_overall_health(self) -> HealthStatus:
        """Get overall system health status"""
        if not self.health_results:
            return HealthStatus.UNKNOWN
        
        critical_unhealthy = any(
            self.components.get(name, ComponentConfig("unknown", ComponentType.INTERNAL_SERVICE, lambda: HealthCheckResult("unknown", ComponentType.INTERNAL_SERVICE, HealthStatus.UNKNOWN, 0.0, "", datetime.utcnow()))).critical
            and result.status == HealthStatus.UNHEALTHY
            for name, result in self.health_results.items()
        )
        
        if critical_unhealthy:
            return HealthStatus.UNHEALTHY
        
        any_degraded = any(result.status == HealthStatus.DEGRADED for result in self.health_results.values())
        
        if any_degraded:
            return HealthStatus.DEGRADED
        
        all_healthy = all(result.status == HealthStatus.HEALTHY for result in self.health_results.values())
        
        return HealthStatus.HEALTHY if all_healthy else HealthStatus.UNKNOWN
    
    def get_health_summary(self) -> Dict[str, Any]:
        """Get health summary"""
        overall_health = self.get_overall_health()
        uptime = datetime.utcnow() - self.health_stats['uptime_start']
        
        component_statuses = {}
        for name, result in self.health_results.items():
            config = self.components.get(name, ComponentConfig("unknown", ComponentType.INTERNAL_SERVICE, lambda: HealthCheckResult("unknown", ComponentType.INTERNAL_SERVICE, HealthStatus.UNKNOWN, 0.0, "", datetime.utcnow())))
            component_statuses[name] = {
                'status': result.status.value,
                'message': result.message,
                'response_time': result.response_time,
                'last_check': result.timestamp.isoformat(),
                'component_type': result.component_type.value,
                'critical': config.critical
            }
        
        return {
            'overall_status': overall_health.value,
            'uptime_seconds': uptime.total_seconds(),
            'components': component_statuses,
            'statistics': self.health_stats,
            'monitoring_active': self.monitoring_active
        }

# Global instance using core config
config_manager = ConfigManager()
_health_checker = HealthChecker(config_manager)

def get_health_checker() -> HealthChecker:
    """Get global health checker"""
    return _health_checker