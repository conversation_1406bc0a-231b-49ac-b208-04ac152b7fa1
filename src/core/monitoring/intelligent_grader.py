"""
Intelligent Grader for Compliance Monitoring

Provides AI-powered grading and compliance checking for bot responses.
"""

import logging
from typing import Dict, Any, Optional, Tuple, List
from datetime import datetime
from src.shared.ai_chat.ai_client import AIClientWrapper

logger = logging.getLogger(__name__)

class ComplianceGrader:
    """
    AI-powered compliance grader that evaluates responses for regulatory compliance,
    risk assessment, and content appropriateness.
    """
    
    def __init__(self):
        self.ai_client = AIClientWrapper()
        self.compliance_threshold = 80  # Minimum score for passing compliance
        self.risk_keywords = [
            'guarantee', 'certain', 'risk-free', 'no loss', 'always',
            'buy now', 'sell immediately', 'double your money', 'get rich quick'
        ]
    
    async def grade_response(self, query: str, response: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Grade a response for compliance using AI analysis.
        
        Args:
            query: The original user query
            response: The generated bot response
            context: Additional context information
            
        Returns:
            Dict with compliance score, risk level, and recommendations
        """
        try:
            # Basic keyword-based risk assessment
            risk_score = self._calculate_keyword_risk(response)
            
            # AI-powered compliance check
            ai_score, ai_risk_level, ai_recommendations = await self._ai_compliance_check(query, response, context)
            
            # Combine scores
            overall_score = (risk_score * 0.3) + (ai_score * 0.7)
            overall_risk = 'high' if overall_score < 60 else 'medium' if overall_score < 80 else 'low'
            
            grade_result = {
                'overall_score': round(overall_score, 2),
                'risk_level': overall_risk,
                'keyword_risk': risk_score,
                'ai_compliance_score': ai_score,
                'ai_risk_level': ai_risk_level,
                'recommendations': ai_recommendations,
                'flagged': overall_score < self.compliance_threshold,
                'timestamp': datetime.utcnow().isoformat()
            }
            
            logger.info(f"Compliance grade: {overall_score:.2f} ({overall_risk}) for query: {query[:50]}...")
            
            return grade_result
            
        except Exception as e:
            logger.error(f"Error in compliance grading: {e}")
            return {
                'overall_score': 0.0,
                'risk_level': 'high',
                'keyword_risk': 100,
                'ai_compliance_score': 0,
                'ai_risk_level': 'high',
                'recommendations': ['Grading failed - manual review required'],
                'flagged': True,
                'timestamp': datetime.utcnow().isoformat()
            }
    
    def _calculate_keyword_risk(self, response: str) -> float:
        """
        Calculate risk score based on prohibited keywords.
        
        Args:
            response: The response text to analyze
            
        Returns:
            Risk score (0-100, higher = more risky)
        """
        response_lower = response.lower()
        keyword_count = sum(1 for keyword in self.risk_keywords if keyword in response_lower)
        return min(100, keyword_count * 20)  # 20 points per keyword, max 100
    
    async def _ai_compliance_check(self, query: str, response: str, context: Optional[Dict[str, Any]] = None) -> Tuple[float, str, List[str]]:
        """
        Use AI to perform detailed compliance analysis.
        
        Args:
            query: The original user query
            response: The generated bot response
            context: Additional context information
            
        Returns:
            Tuple of (compliance_score, risk_level, recommendations)
        """
        try:
            prompt = f"""
            You are a compliance expert for a financial trading bot. Analyze the following user query and bot response for regulatory compliance, risk, and appropriateness.

            USER QUERY: "{query}"

            BOT RESPONSE: "{response}"

            CONTEXT: {context or 'No additional context'}

            Evaluate on these criteria (0-100 scale, 100 = fully compliant):
            1. Regulatory compliance (FINRA, SEC guidelines)
            2. Risk disclosure adequacy
            3. Avoidance of guarantees or misleading statements
            4. Appropriate disclaimers
            5. Suitability for general audience
            6. Content accuracy and balance

            Return JSON:
            {{
                "compliance_score": 85,
                "risk_level": "low|medium|high",
                "recommendations": ["Add stronger risk disclaimer", "Remove absolute language", "Include performance disclaimer"],
                "detailed_analysis": "Brief explanation of key findings"
            }}

            Be strict on financial regulations. Flag any absolute guarantees, lack of disclaimers, or high-risk language.
            """
            
            ai_response = await self.ai_client.generate_response(prompt)
            if not ai_response:
                return 50.0, 'medium', ['AI compliance check unavailable - default assessment']
            
            # Parse AI response
            try:
                import json
                parsed = json.loads(ai_response)
                score = parsed.get('compliance_score', 50)
                risk_level = parsed.get('risk_level', 'medium')
                recommendations = parsed.get('recommendations', [])
                return score, risk_level, recommendations
            except json.JSONDecodeError:
                logger.warning("Failed to parse AI compliance response")
                return 50.0, 'medium', ['AI response parsing failed - manual review recommended']
                
        except Exception as e:
            logger.error(f"AI compliance check error: {e}")
            return 50.0, 'medium', ['AI compliance check failed - manual review required']
    
    def is_high_risk_response(self, grade_result: Dict[str, Any]) -> bool:
        """
        Determine if a response requires additional review.
        
        Args:
            grade_result: The result from grade_response()
            
        Returns:
            True if high risk and needs review
        """
        return grade_result.get('flagged', False) or grade_result.get('risk_level') == 'high'
    
    async def generate_compliance_report(self, events: List[Dict[str, Any]]) -> str:
        """
        Generate a compliance report for a batch of events.
        
        Args:
            events: List of audit events
            
        Returns:
            Formatted compliance report
        """
        if not events:
            return "No compliance events to report."
        
        high_risk_count = sum(1 for e in events if e.get('risk_level') == 'high')
        avg_score = sum(e.get('overall_score', 0) for e in events) / len(events)
        
        report = f"""
COMPLIANCE REPORT
================
Total Events: {len(events)}
High Risk Events: {high_risk_count}
Average Compliance Score: {avg_score:.1f}/100

Recommendations:
"""
        
        # Group recommendations
        recommendations = {}
        for event in events:
            for rec in event.get('recommendations', []):
                recommendations[rec] = recommendations.get(rec, 0) + 1
        
        for rec, count in sorted(recommendations.items(), key=lambda x: x[1], reverse=True):
            if count > 1:
                report += f"- {rec} ({count} occurrences)\n"
        
        return report