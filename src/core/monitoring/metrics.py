"""
Simplified Metrics Collection for ASK Pipeline

Merged from observability/metrics.py:
- Basic counters, gauges, histograms
- Integration with unified_db for metrics persistence
- Use core ConfigManager for metric configuration
- Simplified cost and business metrics
"""

import time
import threading
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from collections import defaultdict
import statistics

from src.core.config_manager import ConfigManager
# from src.database.unified_db import get_db_session, MetricModel  # Not implemented yet
from src.core.monitoring.logger import get_structured_logger

logger = get_structured_logger(__name__)

class MetricType(Enum):
    """Types of metrics"""
    COUNTER = "counter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"

class MetricCategory(Enum):
    """Metric categories"""
    PERFORMANCE = "performance"
    BUSINESS = "business"
    SYSTEM = "system"
    COST = "cost"

@dataclass
class MetricValue:
    """Metric value"""
    name: str
    value: float
    category: MetricCategory
    timestamp: datetime
    labels: Dict[str, str] = field(default_factory=dict)

class MetricsCollector:
    """Simplified metrics collector with DB integration"""
    
    def __init__(self, config: ConfigManager):
        self.config = config
        self.counters: Dict[str, float] = defaultdict(float)
        self.gauges: Dict[str, float] = {}
        self.histograms: Dict[str, List[float]] = defaultdict(list)
        self._lock = threading.RLock()
        self.monitoring_interval = config.get("metrics.interval", 30)
        
        # Business and cost tracking
        self.business_metrics = defaultdict(int)
        self.cost_tracking = defaultdict(float)
        
        # Start background monitoring
        self._start_system_monitoring()
    
    def _start_system_monitoring(self):
        """Start system metrics collection"""
        def collect_system_metrics():
            while True:
                try:
                    # CPU and memory
                    import psutil
                    cpu = psutil.cpu_percent()
                    memory = psutil.virtual_memory().percent
                    
                    self.set_gauge("system_cpu_usage", cpu, MetricCategory.SYSTEM)
                    self.set_gauge("system_memory_usage", memory, MetricCategory.SYSTEM)
                    
                    # Persist
                    asyncio.create_task(self._persist_metrics())
                    
                    time.sleep(self.monitoring_interval)
                except Exception as e:
                    logger.error(f"System metrics collection failed: {e}")
                    time.sleep(self.monitoring_interval)
        
        import asyncio
        try:
            loop = asyncio.get_running_loop()
            asyncio.create_task(asyncio.to_thread(collect_system_metrics))
        except RuntimeError:
            # No event loop running, skip background monitoring for now
            logger.info("No event loop running, skipping background system monitoring")
    
    def increment_counter(self, name: str, value: float = 1.0, category: MetricCategory = MetricCategory.PERFORMANCE, labels: Optional[Dict[str, str]] = None):
        """Increment counter"""
        with self._lock:
            key = f"{name}:{str(labels) if labels else ''}"
            self.counters[key] += value
            self.business_metrics[name] += value  # For business tracking
            logger.info(f"Incremented counter {name}: {value}")
    
    def set_gauge(self, name: str, value: float, category: MetricCategory = MetricCategory.PERFORMANCE, labels: Optional[Dict[str, str]] = None):
        """Set gauge"""
        with self._lock:
            key = f"{name}:{str(labels) if labels else ''}"
            self.gauges[key] = value
            if category == MetricCategory.COST:
                self.cost_tracking[name] = value
    
    def observe_histogram(self, name: str, value: float, category: MetricCategory = MetricCategory.PERFORMANCE, labels: Optional[Dict[str, str]] = None):
        """Observe histogram value"""
        with self._lock:
            key = f"{name}:{str(labels) if labels else ''}"
            self.histograms[key].append(value)
            if len(self.histograms[key]) > 1000:  # Limit size
                self.histograms[key] = self.histograms[key][-500:]
    
    # Convenience methods
    
    def record_request(self, success: bool = True):
        """Record request"""
        status = "success" if success else "error"
        self.increment_counter("requests_total", 1, labels={"status": status})
    
    def record_request_duration(self, duration: float, stage: str):
        """Record duration"""
        self.observe_histogram("request_duration_seconds", duration, labels={"stage": stage})
    
    def record_api_cost(self, provider: str, cost: float):
        """Record API cost"""
        self.increment_counter("api_cost_dollars", cost, MetricCategory.COST, labels={"provider": provider})
        self.cost_tracking["total_cost"] += cost
    
    async def _persist_metrics(self):
        """Persist metrics to unified_db"""
        try:
            async with get_db_session() as session:
                # Persist counters
                for name, value in self.counters.items():
                    metric = MetricModel(
                        name=name,
                        value=value,
                        category=MetricCategory.PERFORMANCE.value,  # Default
                        timestamp=datetime.utcnow(),
                        type=MetricType.COUNTER.value
                    )
                    session.add(metric)
                
                # Persist gauges
                for name, value in self.gauges.items():
                    metric = MetricModel(
                        name=name,
                        value=value,
                        category=MetricCategory.PERFORMANCE.value,
                        timestamp=datetime.utcnow(),
                        type=MetricType.GAUGE.value
                    )
                    session.add(metric)
                
                await session.commit()
                logger.debug("Metrics persisted to DB")
        except Exception as e:
            logger.error(f"Failed to persist metrics: {e}")
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get summary"""
        with self._lock:
            histogram_stats = {}
            for name, values in self.histograms.items():
                if values:
                    avg = statistics.mean(values)
                    p95 = sorted(values)[int(len(values) * 0.95)]
                    histogram_stats[name] = {'avg': avg, 'p95': p95}
            
            return {
                'counters': dict(self.counters),
                'gauges': dict(self.gauges),
                'histogram_stats': histogram_stats,
                'business_metrics': dict(self.business_metrics),
                'total_cost': sum(self.cost_tracking.values()),
                'cost_per_1000_requests': self._calculate_cost_per_request()
            }
    
    def _calculate_cost_per_request(self) -> float:
        """Calculate cost per 1000 requests"""
        total_requests = sum(self.counters.get(k, 0) for k in self.counters if "requests_total" in k)
        total_cost = sum(self.cost_tracking.values())
        if total_requests > 0:
            return (total_cost / total_requests) * 1000
        return 0.0

# Global collector
config_manager = ConfigManager()
_metrics_collector = MetricsCollector(config_manager)

def get_metrics_collector() -> MetricsCollector:
    """Get global metrics collector"""
    return _metrics_collector