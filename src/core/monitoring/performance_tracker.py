"""
Historical Performance Tracking Module

This module tracks analysis results, prediction accuracy, and performance
metrics over time to improve future analysis quality.
"""

import json
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timed<PERSON>ta
from enum import Enum
import asyncio
from collections import defaultdict

from src.shared.error_handling.logging import get_logger

logger = get_logger(__name__)


class AnalysisType(Enum):
    """Types of analysis performed."""
    
    TECHNICAL_ANALYSIS = "technical_analysis"
    SENTIMENT_ANALYSIS = "sentiment_analysis"
    PATTERN_RECOGNITION = "pattern_recognition"
    RISK_ASSESSMENT = "risk_assessment"
    RECOMMENDATION = "recommendation"
    MULTI_TIMEFRAME = "multi_timeframe"
    VOLUME_ANALYSIS = "volume_analysis"


class PredictionOutcome(Enum):
    """Outcomes of predictions."""
    
    CORRECT = "correct"
    INCORRECT = "incorrect"
    PARTIAL = "partial"
    UNKNOWN = "unknown"


@dataclass
class AnalysisResult:
    """Individual analysis result with metadata."""
    
    id: str
    symbol: str
    analysis_type: AnalysisType
    timestamp: datetime
    result_data: Dict[str, Any]
    quality_score: float
    execution_time_ms: int
    user_id: Optional[int] = None
    session_id: Optional[str] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class PredictionRecord:
    """Record of a prediction made by the system."""
    
    id: str
    symbol: str
    analysis_id: str
    prediction_type: str
    prediction_value: Any
    confidence: float
    timestamp: datetime
    target_date: Optional[datetime] = None
    actual_outcome: Optional[Any] = None
    outcome_timestamp: Optional[datetime] = None
    accuracy_score: Optional[float] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class PerformanceMetrics:
    """Performance metrics for analysis and predictions."""
    
    symbol: str
    analysis_count: int
    average_quality_score: float
    average_execution_time_ms: float
    prediction_accuracy: float
    total_predictions: int
    correct_predictions: int
    partial_predictions: int
    incorrect_predictions: int
    last_updated: datetime
    
    def __post_init__(self):
        if self.last_updated is None:
            self.last_updated = datetime.now()


class PerformanceTracker:
    """Main performance tracking system."""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.analysis_results = {}  # id -> AnalysisResult
        self.predictions = {}  # id -> PredictionRecord
        self.performance_metrics = {}  # symbol -> PerformanceMetrics
        self.logger.info("PerformanceTracker initialized")
    
    def record_analysis_result(self, analysis_result: AnalysisResult) -> bool:
        """Record an analysis result for tracking."""
        try:
            self.analysis_results[analysis_result.id] = analysis_result
            
            # Update performance metrics
            self._update_performance_metrics(analysis_result.symbol)
            
            self.logger.info(f"Recorded analysis result for {analysis_result.symbol}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error recording analysis result: {e}")
            return False
    
    def record_prediction(self, prediction: PredictionRecord) -> bool:
        """Record a prediction for tracking."""
        try:
            self.predictions[prediction.id] = prediction
            
            # Update performance metrics
            self._update_performance_metrics(prediction.symbol)
            
            self.logger.info(f"Recorded prediction for {prediction.symbol}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error recording prediction: {e}")
            return False
    
    def update_prediction_outcome(self, prediction_id: str, 
                                actual_outcome: Any,
                                accuracy_score: Optional[float] = None) -> bool:
        """Update a prediction with its actual outcome."""
        try:
            if prediction_id not in self.predictions:
                self.logger.warning(f"Prediction {prediction_id} not found")
                return False
            
            prediction = self.predictions[prediction_id]
            prediction.actual_outcome = actual_outcome
            prediction.outcome_timestamp = datetime.now()
            prediction.accuracy_score = accuracy_score
            
            # Update performance metrics
            self._update_performance_metrics(prediction.symbol)
            
            self.logger.info(f"Updated prediction outcome for {prediction.symbol}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error updating prediction outcome: {e}")
            return False
    
    def get_performance_metrics(self, symbol: str) -> Optional[PerformanceMetrics]:
        """Get performance metrics for a specific symbol."""
        return self.performance_metrics.get(symbol)
    
    def get_symbol_performance_summary(self, symbol: str) -> Dict[str, Any]:
        """Get comprehensive performance summary for a symbol."""
        metrics = self.get_performance_metrics(symbol)
        if not metrics:
            return {"error": "No performance data available"}
        
        # Get recent analysis results
        recent_analyses = [
            result for result in self.analysis_results.values()
            if result.symbol == symbol and 
            result.timestamp > datetime.now() - timedelta(days=30)
        ]
        
        # Get recent predictions
        recent_predictions = [
            pred for pred in self.predictions.values()
            if pred.symbol == symbol and 
            pred.timestamp > datetime.now() - timedelta(days=30)
        ]
        
        # Calculate trend metrics
        trend_metrics = self._calculate_trend_metrics(recent_analyses, recent_predictions)
        
        return {
            "symbol": symbol,
            "current_metrics": asdict(metrics),
            "trend_metrics": trend_metrics,
            "recent_analyses_count": len(recent_analyses),
            "recent_predictions_count": len(recent_predictions),
            "data_freshness": "recent" if recent_analyses else "stale"
        }
    
    def get_system_performance_summary(self) -> Dict[str, Any]:
        """Get overall system performance summary."""
        total_analyses = len(self.analysis_results)
        total_predictions = len(self.predictions)
        
        if total_analyses == 0:
            return {"error": "No performance data available"}
        
        # Calculate overall metrics
        overall_quality = sum(result.quality_score for result in self.analysis_results.values()) / total_analyses
        overall_execution_time = sum(result.execution_time_ms for result in self.analysis_results.values()) / total_analyses
        
        # Calculate prediction accuracy
        completed_predictions = [pred for pred in self.predictions.values() if pred.actual_outcome is not None]
        if completed_predictions:
            overall_accuracy = sum(pred.accuracy_score or 0 for pred in completed_predictions) / len(completed_predictions)
        else:
            overall_accuracy = 0.0
        
        # Get top performing symbols
        symbol_performance = []
        for symbol, metrics in self.performance_metrics.items():
            if metrics.total_predictions >= 5:  # Only include symbols with sufficient data
                symbol_performance.append({
                    "symbol": symbol,
                    "accuracy": metrics.prediction_accuracy,
                    "analysis_count": metrics.analysis_count,
                    "quality_score": metrics.average_quality_score
                })
        
        # Sort by accuracy
        symbol_performance.sort(key=lambda x: x["accuracy"], reverse=True)
        top_performers = symbol_performance[:10]
        
        return {
            "total_analyses": total_analyses,
            "total_predictions": total_predictions,
            "overall_quality_score": round(overall_quality, 3),
            "overall_execution_time_ms": round(overall_execution_time, 2),
            "overall_prediction_accuracy": round(overall_accuracy, 3),
            "top_performing_symbols": top_performers,
            "performance_trend": self._calculate_system_trend()
        }
    
    def _update_performance_metrics(self, symbol: str):
        """Update performance metrics for a symbol."""
        try:
            # Get all analysis results for the symbol
            symbol_analyses = [
                result for result in self.analysis_results.values()
                if result.symbol == symbol
            ]
            
            if not symbol_analyses:
                return
            
            # Get all predictions for the symbol
            symbol_predictions = [
                pred for pred in self.predictions.values()
                if pred.symbol == symbol
            ]
            
            # Calculate metrics
            analysis_count = len(symbol_analyses)
            average_quality_score = sum(result.quality_score for result in symbol_analyses) / analysis_count
            average_execution_time_ms = sum(result.execution_time_ms for result in symbol_analyses) / analysis_count
            
            # Calculate prediction accuracy
            total_predictions = len(symbol_predictions)
            completed_predictions = [pred for pred in symbol_predictions if pred.actual_outcome is not None]
            
            if completed_predictions:
                prediction_accuracy = sum(pred.accuracy_score or 0 for pred in completed_predictions) / len(completed_predictions)
                correct_predictions = len([pred for pred in completed_predictions if pred.accuracy_score and pred.accuracy_score >= 0.8])
                partial_predictions = len([pred for pred in completed_predictions if pred.accuracy_score and 0.5 <= pred.accuracy_score < 0.8])
                incorrect_predictions = len([pred for pred in completed_predictions if pred.accuracy_score and pred.accuracy_score < 0.5])
            else:
                prediction_accuracy = 0.0
                correct_predictions = 0
                partial_predictions = 0
                incorrect_predictions = 0
            
            # Create or update performance metrics
            self.performance_metrics[symbol] = PerformanceMetrics(
                symbol=symbol,
                analysis_count=analysis_count,
                average_quality_score=round(average_quality_score, 3),
                average_execution_time_ms=round(average_execution_time_ms, 2),
                prediction_accuracy=round(prediction_accuracy, 3),
                total_predictions=total_predictions,
                correct_predictions=correct_predictions,
                partial_predictions=partial_predictions,
                incorrect_predictions=incorrect_predictions,
                last_updated=datetime.now()
            )
            
        except Exception as e:
            self.logger.error(f"Error updating performance metrics for {symbol}: {e}")
    
    def _calculate_trend_metrics(self, recent_analyses: List[AnalysisResult],
                               recent_predictions: List[PredictionRecord]) -> Dict[str, Any]:
        """Calculate trend metrics for recent data."""
        if not recent_analyses:
            return {"error": "Insufficient data for trend analysis"}
        
        # Sort by timestamp
        recent_analyses.sort(key=lambda x: x.timestamp)
        recent_predictions.sort(key=lambda x: x.timestamp)
        
        # Calculate quality trend
        if len(recent_analyses) >= 3:
            first_half = recent_analyses[:len(recent_analyses)//2]
            second_half = recent_analyses[len(recent_analyses)//2:]
            
            first_avg_quality = sum(result.quality_score for result in first_half) / len(first_half)
            second_avg_quality = sum(result.quality_score for result in second_half) / len(second_half)
            
            quality_trend = "improving" if second_avg_quality > first_avg_quality else "declining"
            quality_change = round(second_avg_quality - first_avg_quality, 3)
        else:
            quality_trend = "insufficient_data"
            quality_change = 0.0
        
        # Calculate execution time trend
        if len(recent_analyses) >= 3:
            first_avg_time = sum(result.execution_time_ms for result in first_half) / len(first_half)
            second_avg_time = sum(result.execution_time_ms for result in second_half) / len(second_half)
            
            time_trend = "improving" if second_avg_time < first_avg_time else "declining"
            time_change = round(second_avg_time - first_avg_time, 2)
        else:
            time_trend = "insufficient_data"
            time_change = 0.0
        
        # Calculate prediction accuracy trend
        if len(recent_predictions) >= 5:
            completed_recent = [pred for pred in recent_predictions if pred.actual_outcome is not None]
            if len(completed_recent) >= 3:
                first_half_preds = completed_recent[:len(completed_recent)//2]
                second_half_preds = completed_recent[len(completed_recent)//2:]
                
                first_avg_acc = sum(pred.accuracy_score or 0 for pred in first_half_preds) / len(first_half_preds)
                second_avg_acc = sum(pred.accuracy_score or 0 for pred in second_half_preds) / len(second_half_preds)
                
                accuracy_trend = "improving" if second_avg_acc > first_avg_acc else "declining"
                accuracy_change = round(second_avg_acc - first_avg_acc, 3)
            else:
                accuracy_trend = "insufficient_data"
                accuracy_change = 0.0
        else:
            accuracy_trend = "insufficient_data"
            accuracy_change = 0.0
        
        return {
            "quality_trend": quality_trend,
            "quality_change": quality_change,
            "execution_time_trend": time_trend,
            "execution_time_change": time_change,
            "accuracy_trend": accuracy_trend,
            "accuracy_change": accuracy_change,
            "overall_trend": self._determine_overall_trend(quality_trend, time_trend, accuracy_trend)
        }
    
    def _determine_overall_trend(self, quality_trend: str, time_trend: str, accuracy_trend: str) -> str:
        """Determine overall performance trend."""
        improving_count = sum(1 for trend in [quality_trend, time_trend, accuracy_trend] if trend == "improving")
        declining_count = sum(1 for trend in [quality_trend, time_trend, accuracy_trend] if trend == "declining")
        
        if improving_count >= 2:
            return "improving"
        elif declining_count >= 2:
            return "declining"
        else:
            return "mixed"
    
    def _calculate_system_trend(self) -> str:
        """Calculate overall system performance trend."""
        if not self.performance_metrics:
            return "insufficient_data"
        
        # Calculate trend across all symbols
        improving_symbols = 0
        declining_symbols = 0
        total_symbols = 0
        
        for metrics in self.performance_metrics.values():
            if metrics.total_predictions >= 3:  # Only consider symbols with sufficient data
                total_symbols += 1
                if metrics.prediction_accuracy > 0.7:
                    improving_symbols += 1
                elif metrics.prediction_accuracy < 0.5:
                    declining_symbols += 1
        
        if total_symbols == 0:
            return "insufficient_data"
        
        improving_ratio = improving_symbols / total_symbols
        declining_ratio = declining_symbols / total_symbols
        
        if improving_ratio > 0.6:
            return "strongly_improving"
        elif improving_ratio > 0.4:
            return "improving"
        elif declining_ratio > 0.6:
            return "strongly_declining"
        elif declining_ratio > 0.4:
            return "declining"
        else:
            return "stable"
    
    def export_performance_data(self, symbol: Optional[str] = None) -> Dict[str, Any]:
        """Export performance data for analysis or backup."""
        try:
            if symbol:
                # Export data for specific symbol
                metrics = self.get_performance_metrics(symbol)
                symbol_analyses = [
                    result for result in self.analysis_results.values()
                    if result.symbol == symbol
                ]
                symbol_predictions = [
                    pred for pred in self.predictions.values()
                    if pred.symbol == symbol
                ]
                
                return {
                    "symbol": symbol,
                    "export_timestamp": datetime.now().isoformat(),
                    "metrics": asdict(metrics) if metrics else None,
                    "analyses": [asdict(result) for result in symbol_analyses],
                    "predictions": [asdict(pred) for pred in symbol_predictions]
                }
            else:
                # Export all data
                return {
                    "export_timestamp": datetime.now().isoformat(),
                    "total_analyses": len(self.analysis_results),
                    "total_predictions": len(self.predictions),
                    "symbols_count": len(self.performance_metrics),
                    "performance_metrics": {sym: asdict(metrics) for sym, metrics in self.performance_metrics.items()},
                    "system_summary": self.get_system_performance_summary()
                }
                
        except Exception as e:
            self.logger.error(f"Error exporting performance data: {e}")
            return {"error": str(e)}
    
    def cleanup_old_data(self, days_to_keep: int = 90) -> int:
        """Clean up old performance data to save memory."""
        try:
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            cleaned_count = 0
            
            # Clean old analysis results
            old_analyses = [
                aid for aid, result in self.analysis_results.items()
                if result.timestamp < cutoff_date
            ]
            for aid in old_analyses:
                del self.analysis_results[aid]
                cleaned_count += 1
            
            # Clean old predictions
            old_predictions = [
                pid for pid, pred in self.predictions.items()
                if pred.timestamp < cutoff_date
            ]
            for pid in old_predictions:
                del self.predictions[pid]
                cleaned_count += 1
            
            # Recalculate metrics for affected symbols
            affected_symbols = set()
            for result in self.analysis_results.values():
                affected_symbols.add(result.symbol)
            for pred in self.predictions.values():
                affected_symbols.add(pred.symbol)
            
            for symbol in affected_symbols:
                self._update_performance_metrics(symbol)
            
            self.logger.info(f"Cleaned up {cleaned_count} old records")
            return cleaned_count
            
        except Exception as e:
            self.logger.error(f"Error cleaning up old data: {e}")
            return 0

# Global performance tracker instance
_performance_tracker: Optional[PerformanceTracker] = None

def get_performance_tracker() -> PerformanceTracker:
    """Get global performance tracker instance"""
    global _performance_tracker
    if _performance_tracker is None:
        _performance_tracker = PerformanceTracker()
    return _performance_tracker
