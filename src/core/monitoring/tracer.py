"""
Simplified Distributed Tracing for ASK Pipeline

Merged from observability/tracer.py:
- Basic span creation and context propagation
- Integration with unified_db for trace persistence
- Use core ConfigManager and logger
- Simplified performance insights
"""

import time
import uuid
import asyncio
import threading
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from contextlib import asynccontextmanager

from src.core.config_manager import ConfigManager
# from src.database.unified_db import get_db_session, TraceSpanModel  # Not implemented yet
from src.core.monitoring.logger import get_structured_logger, set_correlation_id

logger = get_structured_logger(__name__)

class SpanStatus(Enum):
    """Span status"""
    OK = "ok"
    ERROR = "error"

@dataclass
class SpanContext:
    """Basic span context"""
    trace_id: str
    span_id: str
    parent_span_id: Optional[str] = None

@dataclass
class Span:
    """Basic span"""
    trace_id: str
    span_id: str
    parent_span_id: Optional[str]
    operation_name: str
    start_time: float
    end_time: Optional[float] = None
    duration: Optional[float] = None
    status: SpanStatus = SpanStatus.OK
    tags: Dict[str, Any] = field(default_factory=dict)
    error: Optional[str] = None
    
    def finish(self, status: SpanStatus = SpanStatus.OK, error: Optional[str] = None):
        """Finish span"""
        self.end_time = time.time()
        self.duration = self.end_time - self.start_time
        self.status = status
        if error:
            self.error = error
            self.status = SpanStatus.ERROR
    
    def set_tag(self, key: str, value: Any):
        """Set tag"""
        self.tags[key] = value
    
    def set_error(self, error: Exception):
        """Set error"""
        self.status = SpanStatus.ERROR
        self.error = str(error)
        self.tags['error'] = True

@dataclass
class Trace:
    """Basic trace"""
    trace_id: str
    spans: List[Span] = field(default_factory=list)
    start_time: float = field(default_factory=time.time)
    end_time: Optional[float] = None
    duration: Optional[float] = None
    
    def add_span(self, span: Span):
        """Add span"""
        self.spans.append(span)
    
    def finish(self):
        """Finish trace"""
        self.end_time = time.time()
        self.duration = self.end_time - self.start_time

class DistributedTracer:
    """Simplified tracer with DB integration"""
    
    def __init__(self, config: ConfigManager):
        self.config = config
        self.traces: Dict[str, Trace] = {}
        self.active_spans: Dict[str, Span] = {}
        self.sampling_rate = config.get("tracing.sampling_rate", 1.0)
        self.max_traces = config.get("tracing.max_traces", 1000)
        self._lock = threading.RLock()
    
    def start_trace(self, operation_name: str, trace_id: Optional[str] = None) -> SpanContext:
        """Start new trace"""
        if self.sampling_rate < 1.0 and time.time() % (1 / self.sampling_rate) != 0:
            return SpanContext(trace_id="sampled_out", span_id="sampled_out")
        
        trace_id = trace_id or str(uuid.uuid4())[:16]
        span_id = str(uuid.uuid4())[:8]
        
        span = Span(
            trace_id=trace_id,
            span_id=span_id,
            parent_span_id=None,
            operation_name=operation_name,
            start_time=time.time()
        )
        
        trace = Trace(trace_id=trace_id)
        trace.add_span(span)
        
        with self._lock:
            self.traces[trace_id] = trace
            self.active_spans[span_id] = span
        
        # Set correlation ID
        set_correlation_id(trace_id)
        
        return SpanContext(trace_id=trace_id, span_id=span_id)
    
    def start_span(
        self,
        operation_name: str,
        parent_context: Optional[SpanContext] = None
    ) -> SpanContext:
        """Start new span"""
        if not parent_context:
            return self.start_trace(operation_name)
        
        span_id = str(uuid.uuid4())[:8]
        
        span = Span(
            trace_id=parent_context.trace_id,
            span_id=span_id,
            parent_span_id=parent_context.span_id,
            operation_name=operation_name,
            start_time=time.time()
        )
        
        with self._lock:
            if parent_context.trace_id in self.traces:
                self.traces[parent_context.trace_id].add_span(span)
            self.active_spans[span_id] = span
        
        return SpanContext(
            trace_id=parent_context.trace_id,
            span_id=span_id,
            parent_span_id=parent_context.span_id
        )
    
    def finish_span(
        self,
        span_context: SpanContext,
        status: SpanStatus = SpanStatus.OK,
        error: Optional[str] = None
    ):
        """Finish span"""
        with self._lock:
            if span_context.span_id in self.active_spans:
                span = self.active_spans[span_context.span_id]
                span.finish(status, error)
                del self.active_spans[span_context.span_id]
                
                # Finish trace if root
                if span.parent_span_id is None and span_context.trace_id in self.traces:
                    trace = self.traces[span_context.trace_id]
                    trace.finish()
                    asyncio.create_task(self._persist_trace(trace))
    
    async def _persist_trace(self, trace: Trace):
        """Persist trace to DB (placeholder - DB models not implemented yet)"""
        try:
            # TODO: Implement trace persistence when DB models are ready
            logger.debug(f"Trace {trace.trace_id} would be persisted (placeholder)")
        except Exception as e:
            logger.error(f"Failed to persist trace {trace.trace_id}: {e}")
    
    def set_span_tag(self, span_context: SpanContext, key: str, value: Any):
        """Set tag"""
        with self._lock:
            if span_context.span_id in self.active_spans:
                self.active_spans[span_context.span_id].set_tag(key, value)
    
    def set_span_error(self, span_context: SpanContext, error: Exception):
        """Set error"""
        with self._lock:
            if span_context.span_id in self.active_spans:
                self.active_spans[span_context.span_id].set_error(error)
    
    @asynccontextmanager
    async def trace_async_operation(self, operation_name: str, parent_context: Optional[SpanContext] = None):
        """Async context manager for tracing"""
        span_context = self.start_span(operation_name, parent_context)
        try:
            yield span_context
            self.finish_span(span_context, SpanStatus.OK)
        except Exception as e:
            self.set_span_error(span_context, e)
            self.finish_span(span_context, SpanStatus.ERROR, str(e))
            raise
    
    def get_performance_insights(self) -> Dict[str, Any]:
        """Get basic insights"""
        with self._lock:
            if not self.traces:
                return {'message': 'No traces available'}
            
            operation_stats = {}
            for trace in list(self.traces.values()):
                for span in trace.spans:
                    if span.duration:
                        op_name = span.operation_name
                        if op_name not in operation_stats:
                            operation_stats[op_name] = {'count': 0, 'total_duration': 0.0, 'error_count': 0}
                        stats = operation_stats[op_name]
                        stats['count'] += 1
                        stats['total_duration'] += span.duration
                        if span.status == SpanStatus.ERROR:
                            stats['error_count'] += 1
            
            for op_name, stats in operation_stats.items():
                stats['avg_duration'] = stats['total_duration'] / stats['count']
                stats['error_rate'] = stats['error_count'] / stats['count']
            
            slowest = sorted(operation_stats.items(), key=lambda x: x[1]['avg_duration'], reverse=True)[:5]
            
            return {
                'total_traces': len(self.traces),
                'operation_stats': operation_stats,
                'slowest_operations': [
                    {'operation': op, 'avg_duration': s['avg_duration'], 'error_rate': s['error_rate']}
                    for op, s in slowest
                ]
            }

# Global tracer
config_manager = ConfigManager()
_tracer = DistributedTracer(config_manager)

def get_tracer() -> DistributedTracer:
    """Get global tracer"""
    return _tracer