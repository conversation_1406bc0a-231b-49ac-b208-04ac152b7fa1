"""
Simplified Log Analysis for ASK Pipeline

Merged from observability/log_analyzer.py:
- Basic log aggregation and parsing
- Performance and error analysis
- Integration with unified_db for log storage and analysis
- Use core ConfigManager for log paths
"""

import json
import re
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from pathlib import Path
from collections import defaultdict, Counter
import statistics

from src.core.config_manager import ConfigManager
from src.database.unified_db import get_db_session, LogEntryModel  # Assuming models
from src.core.monitoring.logger import get_structured_logger

logger = get_structured_logger(__name__)

class AnalysisTimeframe:
    """Simple time frames"""
    HOUR = "1h"
    DAY = "1d"

@dataclass
class PerformanceMetrics:
    """Basic performance metrics"""
    avg_response_time: float
    total_requests: int
    error_rate: float

@dataclass
class ErrorPattern:
    """Basic error pattern"""
    error_type: str
    count: int

class LogAggregator:
    """Simplified log aggregator using DB"""
    
    def __init__(self, config: ConfigManager):
        self.config = config
        self.log_table = "logs"  # Assuming DB table
    
    async def aggregate_logs(
        self,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """Aggregate logs from unified_db"""
        if not start_time:
            start_time = datetime.utcnow() - timedelta(days=1)
        if not end_time:
            end_time = datetime.utcnow()
        
        try:
            async with get_db_session() as session:
                query = session.query(LogEntryModel).filter(
                    LogEntryModel.timestamp >= start_time,
                    LogEntryModel.timestamp <= end_time
                )
                
                if filters:
                    if 'level' in filters:
                        query = query.filter(LogEntryModel.level == filters['level'])
                
                entries = await query.all()
                
                # Convert to dicts
                log_entries = []
                for entry in entries:
                    log_entries.append({
                        'timestamp': entry.timestamp,
                        'level': entry.level,
                        'category': entry.category,
                        'message': entry.message,
                        'context': json.loads(entry.context) if entry.context else {}
                    })
                
                log_entries.sort(key=lambda x: x['timestamp'])
                return log_entries
                
        except Exception as e:
            logger.error(f"Failed to aggregate logs: {e}")
            return []

class LogAnalyzer:
    """Simplified log analyzer integrated with core"""
    
    def __init__(self, config: ConfigManager):
        self.config = config
        self.aggregator = LogAggregator(config)
    
    async def analyze_performance(
        self,
        timeframe: str = AnalysisTimeframe.DAY
    ) -> PerformanceMetrics:
        """Analyze performance from DB logs"""
        # Calculate time range
        end_time = datetime.utcnow()
        if timeframe == AnalysisTimeframe.DAY:
            start_time = end_time - timedelta(days=1)
        else:
            start_time = end_time - timedelta(hours=1)
        
        entries = await self.aggregator.aggregate_logs(start_time, end_time)
        
        execution_times = []
        total_requests = 0
        errors = 0
        
        for entry in entries:
            if entry['category'] in ['pipeline', 'user_action']:
                total_requests += 1
                
                if entry['level'] in ['ERROR', 'CRITICAL']:
                    errors += 1
                
                exec_time = entry['context'].get('execution_time')
                if exec_time and isinstance(exec_time, (int, float)):
                    execution_times.append(exec_time)
        
        avg_response_time = statistics.mean(execution_times) if execution_times else 0.0
        error_rate = (errors / total_requests * 100) if total_requests > 0 else 0.0
        
        return PerformanceMetrics(
            avg_response_time=avg_response_time,
            total_requests=total_requests,
            error_rate=error_rate
        )
    
    async def detect_error_patterns(self, timeframe: str = AnalysisTimeframe.DAY) -> List[ErrorPattern]:
        """Detect error patterns from logs"""
        end_time = datetime.utcnow()
        if timeframe == AnalysisTimeframe.DAY:
            start_time = end_time - timedelta(days=1)
        else:
            start_time = end_time - timedelta(hours=1)
        
        entries = await self.aggregator.aggregate_logs(start_time, end_time)
        
        error_groups = defaultdict(int)
        
        for entry in entries:
            if entry['level'] in ['ERROR', 'CRITICAL']:
                error_type = self._extract_error_type(entry)
                error_groups[error_type] += 1
        
        patterns = []
        for error_type, count in error_groups.items():
            if count >= 2:
                patterns.append(ErrorPattern(error_type=error_type, count=count))
        
        patterns.sort(key=lambda x: x.count, reverse=True)
        return patterns
    
    def _extract_error_type(self, entry: Dict[str, Any]) -> str:
        """Extract error type"""
        message = entry['message'].lower()
        
        if 'timeout' in message:
            return 'timeout_error'
        elif 'connection' in message:
            return 'connection_error'
        elif 'validation' in message:
            return 'validation_error'
        else:
            return 'unknown_error'
    
    async def generate_report(self, timeframe: str = AnalysisTimeframe.DAY) -> Dict[str, Any]:
        """Generate basic analysis report"""
        performance = await self.analyze_performance(timeframe)
        error_patterns = await self.detect_error_patterns(timeframe)
        
        end_time = datetime.utcnow()
        if timeframe == AnalysisTimeframe.DAY:
            start_time = end_time - timedelta(days=1)
        else:
            start_time = end_time - timedelta(hours=1)
        
        entries = await self.aggregator.aggregate_logs(start_time, end_time)
        
        report = {
            'timeframe': timeframe,
            'analysis_period': {
                'start': start_time.isoformat(),
                'end': end_time.isoformat()
            },
            'total_log_entries': len(entries),
            'performance_metrics': {
                'avg_response_time': performance.avg_response_time,
                'total_requests': performance.total_requests,
                'error_rate': performance.error_rate
            },
            'error_patterns': [
                {
                    'error_type': pattern.error_type,
                    'count': pattern.count
                }
                for pattern in error_patterns[:10]
            ]
        }
        
        logger.info("Generated log analysis report")
        return report

# Global analyzer
config_manager = ConfigManager()
_log_analyzer = LogAnalyzer(config_manager)

def get_log_analyzer() -> LogAnalyzer:
    """Get global log analyzer"""
    return _log_analyzer