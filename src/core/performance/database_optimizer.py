"""
Simplified Database Optimization for ASK Pipeline

Ported from performance/database_optimizer.py:
- Basic query caching and connection pooling using unified_db
- Integration with core metrics and logger
- Simple slow query detection
"""

import asyncio
import time
import hashlib
from typing import Optional, Dict, Any
from functools import wraps
from datetime import datetime

from src.database.unified_db import get_db_session
from src.core.monitoring.logger import get_structured_logger
from src.core.monitoring.metrics import get_metrics_collector
from src.core.config_manager import ConfigManager

logger = get_structured_logger(__name__)
metrics = get_metrics_collector()
config = ConfigManager()

class DatabaseOptimizer:
    """Simplified DB optimizer"""
    
    def __init__(self):
        self.query_cache = {}
        self.cache_ttl = config.get("database.cache_ttl", 300)
        self.slow_query_threshold = config.get("database.slow_threshold", 1.0)
    
    def cache_query(self, func):
        """Decorator for caching queries"""
        @wraps(func)
        async def wrapper(query, *args, **kwargs):
            query_hash = hashlib.md5(query.encode()).hexdigest()
            now = time.time()
            
            # Check cache
            if query_hash in self.query_cache:
                cached = self.query_cache[query_hash]
                if now - cached['timestamp'] < self.cache_ttl:
                    logger.info(f"Cache hit for query {query_hash[:8]}")
                    return cached['result']
            
            # Execute
            start = time.time()
            result = await func(query, *args, **kwargs)
            duration = time.time() - start
            
            # Cache result
            self.query_cache[query_hash] = {'result': result, 'timestamp': now}
            
            # Metrics
            metrics.observe_histogram("db_query_duration", duration)
            if duration > self.slow_query_threshold:
                logger.warning(f"Slow query: {duration:.3f}s - {query[:50]}")
            
            return result
        return wrapper
    
    async def execute_optimized_query(self, query: str, *args, **kwargs):
        """Execute query with optimization"""
        async with get_db_session() as session:
            start = time.time()
            try:
                result = await session.execute(query, *args, **kwargs)
                duration = time.time() - start
                metrics.increment_counter("db_queries_success", 1)
                logger.debug(f"Query executed in {duration:.3f}s")
                return result
            except Exception as e:
                duration = time.time() - start
                metrics.increment_counter("db_queries_error", 1)
                logger.error(f"Query failed after {duration:.3f}s: {e}")
                raise

optimizer = DatabaseOptimizer()

def get_database_optimizer() -> DatabaseOptimizer:
    return optimizer
