"""
Simplified Smart Cache for ASK Pipeline

Ported from performance/smart_cache.py:
- Basic caching with TTL and compression
- Integration with unified_cache for persistence
- Simple hit rate tracking via metrics
"""

import asyncio
import json
import gzip
from typing import Any, Optional
from datetime import datetime, timedelta

from src.bot.pipeline.commands.ask.cache.unified_cache import get_unified_cache
from src.core.monitoring.metrics import get_metrics_collector
from src.core.monitoring.logger import get_structured_logger

logger = get_structured_logger(__name__)
metrics = get_metrics_collector()
unified_cache = get_unified_cache()

class SmartCache:
    """Simplified smart cache wrapper"""
    
    def __init__(self, max_size: int = 1000):
        self.max_size = max_size
        self.cache: dict = {}
        self.hits = 0
        self.misses = 0
    
    async def get(self, key: str) -> Optional[Any]:
        """Get with metrics"""
        if key in self.cache:
            self.hits += 1
            metrics.increment_counter("cache_hits", 1)
            logger.debug(f"Smart cache hit: {key[:8]}")
            return self.cache[key]
        
        self.misses += 1
        metrics.increment_counter("cache_misses", 1)
        logger.debug(f"Smart cache miss: {key[:8]}")
        return None
    
    async def set(self, key: str, value: Any, ttl: int = 300):
        """Set with compression if large"""
        if len(self.cache) >= self.max_size:
            # Simple eviction: remove oldest
            oldest_key = next(iter(self.cache))
            del self.cache[oldest_key]
        
        # Compress large values
        if isinstance(value, str) and len(value) > 1024:
            value = gzip.compress(value.encode())
        
        self.cache[key] = value
        # Set in unified_cache for persistence
        await unified_cache.set(key, value, ttl=ttl)
    
    def get_stats(self):
        """Get stats"""
        hit_rate = self.hits / (self.hits + self.misses) if (self.hits + self.misses) > 0 else 0
        return {"hits": self.hits, "misses": self.misses, "hit_rate": hit_rate, "size": len(self.cache)}

smart_cache = SmartCache()

def get_smart_cache() -> SmartCache:
    return smart_cache
