"""
Simplified Request Batching for ASK Pipeline

Ported from performance/request_batcher.py:
- Basic batching for API calls and tool executions
- Integration with core metrics and logger
- Simple queue for pending requests
"""

import asyncio
import time
from typing import Dict, Any, List, Callable
from enum import Enum
from collections import deque

from src.core.monitoring.logger import get_structured_logger
from src.core.monitoring.metrics import get_metrics_collector

logger = get_structured_logger(__name__)
metrics = get_metrics_collector()

class BatchType(Enum):
    """Batch types"""
    API = "api"
    TOOLS = "tools"

class RequestBatcher:
    """Simplified batcher"""
    
    def __init__(self, max_batch_size: int = 10, max_wait: float = 0.1):
        self.max_batch_size = max_batch_size
        self.max_wait = max_wait
        self.pending: Dict[BatchType, deque] = {t: deque() for t in BatchType}
        self.results: Dict[str, Any] = {}
        self._lock = asyncio.Lock()
    
    async def submit(self, batch_type: BatchType, operation: Callable, *args, **kwargs):
        """Submit request to batch"""
        request_id = f"{batch_type.value}_{id(operation)}_{time.time()}"
        
        async with self._lock:
            self.pending[batch_type].append((request_id, operation, args, kwargs))
        
        # Process if needed
        if len(self.pending[batch_type]) >= self.max_batch_size:
            await self._process_batch(batch_type)
        
        # Wait for result
        return await self._wait_for_result(request_id, timeout=30)
    
    async def _process_batch(self, batch_type: BatchType):
        """Process batch"""
        async with self._lock:
            batch = list(self.pending[batch_type])
            self.pending[batch_type].clear()
        
        if not batch:
            return
        
        start = time.time()
        results = []
        for request_id, op, args, kwargs in batch:
            try:
                result = await op(*args, **kwargs)
                results.append((request_id, result))
                metrics.increment_counter("batch_success", 1)
            except Exception as e:
                results.append((request_id, None))
                logger.error(f"Batch item failed: {e}")
                metrics.increment_counter("batch_error", 1)
        
        duration = time.time() - start
        metrics.observe_histogram("batch_duration", duration, labels={"type": batch_type.value})
        
        async with self._lock:
            for request_id, result in results:
                self.results[request_id] = result
    
    async def _wait_for_result(self, request_id: str, timeout: float):
        """Wait for result"""
        start = time.time()
        while time.time() - start < timeout:
            async with self._lock:
                if request_id in self.results:
                    result = self.results.pop(request_id)
                    return result
            await asyncio.sleep(0.01)
        raise asyncio.TimeoutError("Batch timeout")

batcher = RequestBatcher()

def get_request_batcher() -> RequestBatcher:
    return batcher