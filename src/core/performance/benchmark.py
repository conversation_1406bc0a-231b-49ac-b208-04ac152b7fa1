"""
Simplified Performance Benchmarking for ASK Pipeline

Ported from performance/benchmark.py:
- Basic load testing with concurrent requests
- Integration with core metrics and logger
- Simple metrics calculation and reporting
"""

import asyncio
import time
import statistics
from typing import Callable, Dict, Any
from dataclasses import dataclass
from enum import Enum

from src.core.monitoring.logger import get_structured_logger
from src.core.monitoring.metrics import get_metrics_collector

logger = get_structured_logger(__name__)
metrics = get_metrics_collector()

class TestType(Enum):
    """Types of tests"""
    LOAD = "load"

@dataclass
class BenchmarkConfig:
    """Basic config"""
    duration_seconds: int = 30
    concurrent_users: int = 5

@dataclass
class PerformanceMetrics:
    """Basic metrics"""
    total_requests: int
    successful_requests: int
    avg_response_time: float
    error_rate: float

class PerformanceBenchmark:
    """Simplified benchmarking"""
    
    def __init__(self):
        self.results = []
    
    async def run_benchmark(
        self,
        test_function: Callable,
        config: BenchmarkConfig,
        test_name: str = "benchmark"
    ) -> PerformanceMetrics:
        """Run simple benchmark"""
        logger.info(f"Starting {test_name}")
        
        semaphore = asyncio.Semaphore(config.concurrent_users)
        total_requests = 0
        successful = 0
        response_times = []
        
        async def make_request():
            async with semaphore:
                start = time.time()
                try:
                    await test_function()
                    duration = time.time() - start
                    response_times.append(duration)
                    nonlocal successful
                    successful += 1
                    metrics.record_request_duration(duration, test_name)
                except Exception:
                    duration = time.time() - start
                    response_times.append(duration)
                    metrics.record_request(True)  # Error as failed request
                nonlocal total_requests
                total_requests += 1
        
        end_time = time.time() + config.duration_seconds
        tasks = []
        
        while time.time() < end_time:
            task = asyncio.create_task(make_request())
            tasks.append(task)
            await asyncio.sleep(0.1)  # Rate limit
        
        await asyncio.gather(*tasks, return_exceptions=True)
        
        avg_time = statistics.mean(response_times) if response_times else 0
        error_rate = (total_requests - successful) / total_requests if total_requests else 0
        
        metrics = PerformanceMetrics(
            total_requests=total_requests,
            successful_requests=successful,
            avg_response_time=avg_time,
            error_rate=error_rate
        )
        
        logger.info(f"Benchmark {test_name} complete: {successful}/{total_requests} successful, avg {avg_time:.3f}s")
        self.results.append(metrics)
        return metrics
    
    def get_summary(self) -> Dict[str, Any]:
        """Get summary"""
        if not self.results:
            return {"message": "No benchmarks run"}
        
        total_req = sum(r.total_requests for r in self.results)
        avg_time = statistics.mean([r.avg_response_time for r in self.results])
        
        return {
            "total_benchmarks": len(self.results),
            "total_requests": total_req,
            "avg_response_time": avg_time
        }

benchmark = PerformanceBenchmark()

def get_benchmark() -> PerformanceBenchmark:
    return benchmark