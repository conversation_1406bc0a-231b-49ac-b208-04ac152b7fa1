"""
Simplified Resource Management for ASK Pipeline

Ported from performance/resource_manager.py:
- Basic memory and CPU monitoring using psutil
- Integration with core metrics and logger
- Simple threshold checks and GC triggering
"""

import asyncio
import gc
import time
import psutil
from typing import Dict, Any
from enum import Enum
from datetime import datetime

from src.core.monitoring.logger import get_structured_logger
from src.core.monitoring.metrics import get_metrics_collector

logger = get_structured_logger(__name__)
metrics = get_metrics_collector()

class ResourceType(Enum):
    """Resource types"""
    MEMORY = "memory"
    CPU = "cpu"

class ResourceStatus(Enum):
    """Status levels"""
    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"

class ResourceManager:
    """Simplified resource manager"""
    
    def __init__(self):
        self.thresholds = {
            ResourceType.MEMORY: 80,  # % 
            ResourceType.CPU: 80      # %
        }
        self.monitoring_active = False
    
    async def start_monitoring(self):
        """Start monitoring"""
        self.monitoring_active = True
        asyncio.create_task(self._monitor_loop())
        logger.info("Resource monitoring started")
    
    async def _monitor_loop(self):
        """Monitoring loop"""
        while self.monitoring_active:
            await self._update_metrics()
            await asyncio.sleep(5)
    
    async def _update_metrics(self):
        """Update resources"""
        process = psutil.Process()
        
        # Memory
        memory_percent = process.memory_percent()
        metrics.set_gauge("system_memory_usage", memory_percent, labels={"type": "percent"})
        status = self._get_status(ResourceType.MEMORY, memory_percent)
        if status == ResourceStatus.CRITICAL:
            await self._handle_critical_memory()
        
        # CPU
        cpu_percent = process.cpu_percent()
        metrics.set_gauge("system_cpu_usage", cpu_percent, labels={"type": "percent"})
        status = self._get_status(ResourceType.CPU, cpu_percent)
        if status == ResourceStatus.CRITICAL:
            logger.warning("High CPU usage, consider throttling")
        
        logger.debug(f"Resources: Memory {memory_percent}%, CPU {cpu_percent}%")
    
    def _get_status(self, resource: ResourceType, usage: float) -> ResourceStatus:
        """Get status"""
        threshold = self.thresholds.get(resource, 80)
        if usage > threshold * 1.25:
            return ResourceStatus.CRITICAL
        elif usage > threshold:
            return ResourceStatus.WARNING
        return ResourceStatus.HEALTHY
    
    async def _handle_critical_memory(self):
        """Handle high memory"""
        logger.warning("High memory usage, triggering GC")
        gc.collect()
        metrics.increment_counter("gc_triggers", 1)
    
    def get_status(self) -> Dict[str, Any]:
        """Get current status"""
        process = psutil.Process()
        return {
            "memory_percent": process.memory_percent(),
            "cpu_percent": process.cpu_percent(),
            "status": "healthy"  # Simplified
        }

manager = ResourceManager()

def get_resource_manager() -> ResourceManager:
    return manager