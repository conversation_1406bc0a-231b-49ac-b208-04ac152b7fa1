"""
Simplified Connection Pooling for ASK Pipeline

Ported from performance/connection_pool.py:
- Basic HTTP and Redis pooling using aiohttp and redis.asyncio
- Integration with core config and logger
- Stats tracking via core metrics
"""

import asyncio
import aiohttp
import redis.asyncio as redis
from typing import Optional, Dict, Any
from dataclasses import dataclass
from contextlib import asynccontextmanager

from src.core.config_manager import ConfigManager
from src.core.monitoring.logger import get_structured_logger
from src.core.monitoring.metrics import get_metrics_collector
import time

logger = get_structured_logger(__name__)
metrics = get_metrics_collector()
config = ConfigManager()

@dataclass
class PoolConfig:
    """Basic pool config"""
    max_connections: int = 20
    timeout: float = 30.0

class HTTPConnectionPool:
    """Simplified HTTP pool"""
    
    def __init__(self):
        self.config = PoolConfig()
        self.session: Optional[aiohttp.ClientSession] = None
    
    async def get_session(self) -> aiohttp.ClientSession:
        """Get session"""
        if self.session is None or self.session.closed:
            connector = aiohttp.TCPConnector(limit=self.config.max_connections)
            timeout = aiohttp.ClientTimeout(total=self.config.timeout)
            self.session = aiohttp.ClientSession(connector=connector, timeout=timeout)
            logger.info("HTTP session created")
        return self.session
    
    @asynccontextmanager
    async def request(self, method: str, url: str, **kwargs):
        """HTTP request"""
        session = await self.get_session()
        start = time.time()
        try:
            async with session.request(method, url, **kwargs) as resp:
                duration = time.time() - start
                metrics.record_request_duration(duration, "http")
                yield resp
        except Exception as e:
            logger.error(f"HTTP request failed: {e}")
            raise
        finally:
            if time.time() - start > self.config.timeout:
                metrics.increment_counter("http_timeouts", 1)
    
    async def close(self):
        """Close session"""
        if self.session:
            await self.session.close()
            logger.info("HTTP pool closed")

class RedisConnectionPool:
    """Simplified Redis pool"""
    
    def __init__(self, redis_url: str = "redis://localhost:6379"):
        self.redis_url = redis_url
        self.pool = redis.ConnectionPool.from_url(redis_url, max_connections=10)
        self.client = redis.Redis(connection_pool=self.pool, decode_responses=True)
        logger.info("Redis pool initialized")
    
    async def execute(self, *args):
        """Execute Redis command"""
        start = time.time()
        try:
            result = await self.client.execute_command(*args)
            duration = time.time() - start
            metrics.observe_histogram("redis_duration", duration)
            return result
        except Exception as e:
            logger.error(f"Redis error: {e}")
            raise
    
    async def close(self):
        """Close pool"""
        await self.client.close()
        logger.info("Redis pool closed")

# Global pools
http_pool = HTTPConnectionPool()
redis_pool = RedisConnectionPool()

async def get_http_pool() -> HTTPConnectionPool:
    return http_pool

async def get_redis_pool() -> RedisConnectionPool:
    return redis_pool

async def close_pools():
    await http_pool.close()
    await redis_pool.close()
