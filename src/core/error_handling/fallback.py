from src.shared.error_handling.logging import get_logger

logger = get_logger(__name__)

def handle_error_with_fallback(func, default=None):
    """
    Execute a function with fallback to default value on error.
    
    Args:
        func: Callable to execute
        default: Value to return on exception (default: None)
    
    Returns:
        Result of func() or default
    """
    try:
        return func()
    except Exception as e:
        logger.error(f"Fallback triggered due to error: {e}")
        return default