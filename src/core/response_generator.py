from enum import Enum, auto
from typing import Dict, Any, Optional
import logging
import traceback
from datetime import datetime
import time
from dataclasses import dataclass

# Import the contextual logger and provide a minimal metrics shim
from src.core.monitoring.logger import get_structured_logger as get_logger

class _NoOpMetricsTracker:
    def track_response_generation(self, **kwargs):
        pass
    def track_error(self, **kwargs):
        pass

metrics_tracker = _NoOpMetricsTracker()

class ResponseType(Enum):
    """Enumeration of possible response types"""
    FALLBACK = auto()
    ANALYSIS = auto()
    ERROR = auto()
    KNOWLEDGE = auto()
    DATA_DRIVEN = auto()
    MARKET_TREND = auto()  # New type

@dataclass
class ResponseMetadata:
    intent: str
    symbols: list
    confidence: float
    timestamp: str = datetime.now().isoformat()

class ResponseGenerator:
    """
    Centralized utility for generating consistent responses 
    across the application's AI pipeline
    """
    
    def __init__(self, logger: logging.Logger):
        self.logger = logger
        self.response_templates = {
            'analysis': "Analysis result for {symbols}: {analysis}",
            'fallback': "Couldn't generate analysis for {symbols}",
            'error': "Error: {error_msg}"
        }
    
    def generate_response(
        self,
        response_type: ResponseType,
        context: Dict[str, Any],
        error: Optional[Exception] = None,
        processing_time: float = 0.0
    ) -> Dict[str, Any]:
        """
        Generate a standardized response with comprehensive metadata
        
        Args:
            response_type (ResponseType): Type of response to generate
            context (dict, optional): Context information for the response
            error (Exception, optional): Any error that occurred during processing
            processing_time (float, optional): Time taken to generate response in seconds
        
        Returns:
            dict: Standardized response dictionary
        """
        # Default context if not provided
        context = context or {}
        start_time = time.time()
        processing_time = processing_time  # Initialize processing_time to avoid UnboundLocalError
        
        try:
            # Base response structure
            response = {
                "response": "",
                "response_type": response_type.name.lower(),
                "metadata": {
                    "intent": context.get('intent', 'unknown'),
                    "symbols": context.get('symbols', []),
                    "timestamp": datetime.now().isoformat(),
                    "confidence": context.get('confidence', 0.0)
                },
                "error_details": None
            }
            
            # Generate response based on type
            if response_type == ResponseType.FALLBACK:
                response["response"] = ResponseGenerator._generate_fallback_response(context)
            elif response_type == ResponseType.ERROR:
                response["response"] = ResponseGenerator._generate_error_response(error, context)
            elif response_type == ResponseType.ANALYSIS:
                response["response"] = ResponseGenerator._generate_analysis_response(context)
            elif response_type == ResponseType.KNOWLEDGE:
                response["response"] = ResponseGenerator._generate_knowledge_response(context)
            elif response_type == ResponseType.DATA_DRIVEN:
                response["response"] = ResponseGenerator._generate_data_driven_response(context)
            
            # Add error details if an error occurred
            if error:
                response["error_details"] = {
                    "type": type(error).__name__,
                    "message": str(error),
                    "traceback": traceback.format_exc()
                }
            
            # Calculate actual processing time if not provided
            if processing_time == 0.0:
                processing_time = time.time() - start_time
            
            # Enhanced contextual logging
            log_context = {
                'intent': context.get('intent', 'unknown'),
                'symbols': context.get('symbols', []),
                'response_type': response_type.name.lower(),
                'processing_time': processing_time,
                'confidence': context.get('confidence', 0.0)
            }
            
            if error:
                self.logger.error(
                    f"Generated {response_type.name} response with error: {error}",
                    extra=log_context
                )
            else:
                self.logger.info(
                    f"Generated {response_type.name} response",
                    extra=log_context
                )
            
            # Track metrics
            metrics_tracker.track_response_generation(
                response_type=response_type.name.lower(),
                confidence=context.get('confidence', 0.0),
                processing_time=processing_time,
                context=context
            )
            
            return response
            
        except Exception as e:
            # Log and track any errors during response generation
            # Calculate processing time for error logging
            if processing_time == 0.0:
                processing_time = time.time() - start_time
                
            error_context = {
                'intent': context.get('intent', 'unknown'),
                'symbols': context.get('symbols', []),
                'response_type': response_type.name.lower(),
                'processing_time': processing_time
            }
            
            self.logger.error(
                f"Error during response generation: {e}",
                extra=error_context
            )
            
            metrics_tracker.track_error(
                error_type=type(e).__name__,
                error_message=str(e),
                response_type=response_type.name.lower(),
                processing_time=processing_time,
                context=context
            )
            
            # Return fallback response with error details
            return ResponseGenerator._generate_error_response_fallback(e, context)
    
    @staticmethod
    def _generate_fallback_response(context: Dict[str, Any]) -> str:
        """Generate a fallback response with contextual information"""
        intent = context.get('intent', 'general_question')
        symbols = context.get('symbols', [])
        
        if symbols:
            return (
                f"I couldn't generate a detailed analysis for {', '.join(symbols)}. "
                f"The query intent was: {intent}. "
                "Please provide more context or try rephrasing your question."
            )
        
        return (
            f"I couldn't generate a meaningful response. "
            f"Detected intent: {intent}. "
            "Please ask about specific stocks, market trends, or trading strategies."
        )
    
    @staticmethod
    def _generate_error_response(error: Optional[Exception], context: Dict[str, Any]) -> str:
        """Generate a detailed error response"""
        if error:
            return (
                f"An error occurred during processing: {str(error)}. "
                "Our team has been notified. Please try again later."
            )
        
        return "An unexpected error prevented generating a response."
    
    @staticmethod
    def _generate_error_response_fallback(error: Exception, context: Dict[str, Any]) -> Dict[str, Any]:
        """Generate a fallback error response when response generation itself fails"""
        return {
            "response": f"A system error occurred: {str(error)}. Please try again later.",
            "response_type": "error",
            "metadata": {
                "intent": context.get('intent', 'unknown'),
                "symbols": context.get('symbols', []),
                "timestamp": datetime.now().isoformat(),
                "confidence": 0.0
            },
            "error_details": {
                "type": type(error).__name__,
                "message": str(error),
                "traceback": traceback.format_exc()
            }
        }
    
    @staticmethod
    def _generate_analysis_response(context: Dict[str, Any]) -> str:
        """Generate a standard analysis response"""
        # Placeholder for more complex analysis response generation
        return context.get('raw_response', 'No analysis available.')
    
    @staticmethod
    def _generate_knowledge_response(context: Dict[str, Any]) -> str:
        """Generate a knowledge-based response"""
        # Placeholder for knowledge-based response generation
        return context.get('knowledge_response', 'No knowledge-based insights available.')
    
    @staticmethod
    def _generate_data_driven_response(context: Dict[str, Any]) -> str:
        """Generate a data-driven response"""
        # Placeholder for data-driven response generation
        return context.get('data_response', 'No data-driven insights available.') 