"""
Lightweight FinancialDataValidator shim used during migration.
Implements minimal validation expected by current callers.
"""

from typing import Any, Dict, List


def validate_config_values(config_dict: Dict[str, Any], validation_rules: Dict[str, Dict[str, Any]]) -> List[str]:
    """Simple config validation function"""
    errors = []
    for key, rules in validation_rules.items():
        if key not in config_dict:
            continue
        value = config_dict[key]
        
        if rules.get('type') == 'float':
            try:
                float_val = float(value)
                if 'min' in rules and float_val < rules['min']:
                    errors.append(f"{key} must be >= {rules['min']}")
                if 'max' in rules and float_val > rules['max']:
                    errors.append(f"{key} must be <= {rules['max']}")
            except (ValueError, TypeError):
                errors.append(f"{key} must be a valid number")
        elif rules.get('type') == 'int':
            try:
                int_val = int(value)
                if 'min' in rules and int_val < rules['min']:
                    errors.append(f"{key} must be >= {rules['min']}")
                if 'max' in rules and int_val > rules['max']:
                    errors.append(f"{key} must be <= {rules['max']}")
            except (ValueError, TypeError):
                errors.append(f"{key} must be a valid integer")
    
    return errors


class FinancialDataValidator:
    """Minimal validator for symbol and numeric checks."""

    @staticmethod
    def is_valid_symbol(symbol: str) -> bool:
        if not symbol or not isinstance(symbol, str):
            return False
        return symbol.isalpha() and 1 <= len(symbol) <= 10

    @staticmethod
    def is_valid_price(value: Any) -> bool:
        try:
            float(value)
            return True
        except Exception:
            return False


