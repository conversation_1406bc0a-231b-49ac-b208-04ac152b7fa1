"""
Discord Webhook Handler for AI Automation

Handles sending AI-generated market reports to Discord channels via webhooks.
Integrates seamlessly with the report automation system.
"""

import asyncio
import aiohttp
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timezone

from .report_formatter import FormattedReport, OutputFormat

logger = logging.getLogger(__name__)


class DiscordWebhookHandler:
    """
    Handles Discord webhook delivery for AI automation reports.
    """
    
    def __init__(self, webhook_url: str):
        """
        Initialize Discord webhook handler.
        
        Args:
            webhook_url: Discord webhook URL
        """
        self.webhook_url = webhook_url
        self.session: Optional[aiohttp.ClientSession] = None
        
    async def __aenter__(self):
        """Async context manager entry."""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()
    
    async def send_report(self, formatted_report: FormattedReport) -> bool:
        """
        Send a formatted report to Discord.
        
        Args:
            formatted_report: Formatted report to send
            
        Returns:
            True if successful, False otherwise
        """
        try:
            if not self.session:
                self.session = aiohttp.ClientSession()
            
            # Prepare Discord message
            discord_message = self._prepare_discord_message(formatted_report)
            
            # Send to Discord
            async with self.session.post(
                self.webhook_url,
                json=discord_message,
                timeout=aiohttp.ClientTimeout(total=30)
            ) as response:
                if response.status == 204:  # Discord success status
                    logger.info(f"Discord report sent successfully: {formatted_report.metadata.get('report_type', 'unknown')}")
                    return True
                else:
                    error_text = await response.text()
                    logger.error(f"Discord webhook failed: {response.status} - {error_text}")
                    return False
                    
        except Exception as e:
            logger.error(f"Error sending Discord report: {e}")
            return False
    
    async def send_batch_reports(self, reports: List[FormattedReport]) -> Dict[str, bool]:
        """
        Send multiple reports to Discord.
        
        Args:
            reports: List of formatted reports to send
            
        Returns:
            Dictionary mapping report type to success status
        """
        results = {}
        
        for report in reports:
            if report.format == OutputFormat.DISCORD:
                success = await self.send_report(report)
                report_type = report.metadata.get('report_type', 'unknown')
                results[report_type] = success
                
                # Add delay between messages to avoid rate limiting
                if len(reports) > 1:
                    await asyncio.sleep(1)
        
        return results
    
    def _prepare_discord_message(self, formatted_report: FormattedReport) -> Dict[str, Any]:
        """
        Prepare Discord message from formatted report.
        
        Args:
            formatted_report: Formatted report to convert
            
        Returns:
            Discord message payload
        """
        try:
            # Extract report metadata
            report_type = formatted_report.metadata.get('report_type', 'unknown')
            timestamp = formatted_report.timestamp
            
            # Create Discord embed
            embed = {
                "title": self._get_embed_title(report_type),
                "description": self._truncate_description(formatted_report.content, 2000),
                "color": self._get_embed_color(report_type),
                "timestamp": timestamp.isoformat(),
                "footer": {
                    "text": "🤖 AI-Powered Market Analysis"
                }
            }
            
            # Add fields for better organization
            fields = self._extract_discord_fields(formatted_report.content)
            if fields:
                embed["fields"] = fields
            
            # Create Discord message
            message = {
                "embeds": [embed],
                "username": "Captain Hook - AI Market Analyst",
                "avatar_url": "https://cdn.discordapp.com/emojis/1234567890.png"  # Default avatar
            }
            
            # Add content if it's an alert
            if "alert" in report_type.lower() or "🚨" in formatted_report.content:
                message["content"] = "🚨 **Market Alert** - New analysis available!"
            
            return message
            
        except Exception as e:
            logger.error(f"Error preparing Discord message: {e}")
            # Fallback to simple message
            return {
                "content": f"📊 **AI Market Report**\n\n{formatted_report.content[:1900]}...",
                "username": "Captain Hook - AI Market Analyst"
            }
    
    def _get_embed_title(self, report_type: str) -> str:
        """Get appropriate embed title based on report type."""
        titles = {
            "daily_market_summary": "📊 Daily Market Recap",
            "market_health_dashboard": "🏥 Market Health Dashboard", 
            "anomaly_alert": "🚨 Market Anomaly Alert",
            "weekly_sector_analysis": "🏭 Weekly Sector Analysis",
            "watchlist_update": "📋 Watchlist Update"
        }
        return titles.get(report_type, "📊 AI Market Analysis")
    
    def _get_embed_color(self, report_type: str) -> int:
        """Get appropriate embed color based on report type."""
        colors = {
            "daily_market_summary": 0x00ff00,      # Green
            "market_health_dashboard": 0x0099ff,   # Blue
            "anomaly_alert": 0xff0000,             # Red
            "weekly_sector_analysis": 0xff9900,    # Orange
            "watchlist_update": 0x9932cc          # Purple
        }
        return colors.get(report_type, 0x00ff00)
    
    def _truncate_description(self, content: str, max_length: int) -> str:
        """Truncate content to fit Discord description limits."""
        if len(content) <= max_length:
            return content
        
        # Try to truncate at sentence boundary
        truncated = content[:max_length-3]
        last_period = truncated.rfind('.')
        if last_period > max_length * 0.8:  # If period is in last 20%
            return truncated[:last_period+1] + "..."
        
        return truncated + "..."
    
    def _extract_discord_fields(self, content: str) -> List[Dict[str, Any]]:
        """Extract key information as Discord fields."""
        fields = []
        
        try:
            # Split content into sections
            sections = content.split('\n\n')
            
            for section in sections[:5]:  # Limit to 5 fields
                if section.strip():
                    # Extract section title and content
                    lines = section.strip().split('\n')
                    if len(lines) >= 2:
                        title = lines[0].strip()
                        # Remove emojis and formatting for cleaner title
                        clean_title = title.replace('📊', '').replace('🚀', '').replace('📉', '').replace('🏭', '').replace('🤖', '').replace('📈', '').replace('🔌', '').replace('💡', '').replace('⚠️', '').strip()
                        
                        # Get content (first few lines)
                        content_lines = lines[1:4]  # Take up to 3 content lines
                        content_text = '\n'.join(content_lines).strip()
                        
                        if content_text and len(content_text) <= 1024:  # Discord field value limit
                            fields.append({
                                "name": clean_title[:256],  # Discord field name limit
                                "value": content_text,
                                "inline": len(fields) % 2 == 0  # Alternate inline
                            })
            
            return fields
            
        except Exception as e:
            logger.error(f"Error extracting Discord fields: {e}")
            return []
    
    async def test_connection(self) -> bool:
        """
        Test Discord webhook connection.
        
        Returns:
            True if connection successful, False otherwise
        """
        try:
            if not self.session:
                self.session = aiohttp.ClientSession()
            
            # Send test message
            test_message = {
                "content": "🧪 **Connection Test** - AI automation system is online and ready!",
                "username": "Captain Hook - AI Market Analyst"
            }
            
            async with self.session.post(
                self.webhook_url,
                json=test_message,
                timeout=aiohttp.ClientTimeout(total=10)
            ) as response:
                if response.status == 204:
                    logger.info("Discord webhook connection test successful")
                    return True
                else:
                    error_text = await response.text()
                    logger.error(f"Discord webhook test failed: {response.status} - {error_text}")
                    return False
                    
        except Exception as e:
            logger.error(f"Error testing Discord connection: {e}")
            return False


# Global Discord handler instance
discord_handler: Optional[DiscordWebhookHandler] = None


def initialize_discord_handler(webhook_url: str) -> DiscordWebhookHandler:
    """
    Initialize global Discord handler.
    
    Args:
        webhook_url: Discord webhook URL
        
    Returns:
        Initialized Discord handler
    """
    global discord_handler
    discord_handler = DiscordWebhookHandler(webhook_url)
    logger.info("Discord webhook handler initialized")
    return discord_handler


def get_discord_handler() -> Optional[DiscordWebhookHandler]:
    """Get global Discord handler instance."""
    return discord_handler


async def send_report_to_discord(formatted_report: FormattedReport) -> bool:
    """
    Convenience function to send report to Discord.
    
    Args:
        formatted_report: Formatted report to send
        
    Returns:
        True if successful, False otherwise
    """
    handler = get_discord_handler()
    if not handler:
        logger.error("Discord handler not initialized")
        return False
    
    return await handler.send_report(formatted_report)


async def test_discord_connection(webhook_url: str) -> bool:
    """
    Test Discord webhook connection.
    
    Args:
        webhook_url: Discord webhook URL to test
        
    Returns:
        True if connection successful, False otherwise
    """
    async with DiscordWebhookHandler(webhook_url) as handler:
        return await handler.test_connection() 