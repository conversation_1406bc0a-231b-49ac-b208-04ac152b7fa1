"""
Data quality validation to prevent false confidence scores and trading signals.
Implements validation logic for confidence scores, support/resistance levels, and action determination.
"""

import logging
from typing import Dict, Any, Tu<PERSON>, Optional, Union
from dataclasses import dataclass
import os

logger = logging.getLogger(__name__)

@dataclass
class DataQualityMetrics:
    """Data quality assessment metrics"""
    completeness: float  # 0.0 to 1.0 - how complete the data is
    freshness: float     # 0.0 to 1.0 - how recent the data is
    accuracy: float      # 0.0 to 1.0 - estimated accuracy of the data
    volume: float        # 0.0 to 1.0 - volume data quality
    technical: float     # 0.0 to 1.0 - technical indicator quality
    
    @property
    def overall_quality(self) -> float:
        """Calculate overall data quality score"""
        weights = {
            'completeness': 0.3,
            'freshness': 0.25,
            'accuracy': 0.25,
            'volume': 0.1,
            'technical': 0.1
        }
        return sum(getattr(self, attr) * weight for attr, weight in weights.items())

class DataQualityValidator:
    """Validates data quality before generating trading signals"""
    
    @staticmethod
    def calculate_confidence(data_quality: float, completeness: float, 
                           technical_signals: Dict[str, Any]) -> float:
        """
        Calculate validated confidence score (0-100) based on data quality.
        
        Args:
            data_quality: Overall data quality score (0-100)
            completeness: Data completeness ratio (0.0-1.0)
            technical_signals: Technical analysis signals
            
        Returns:
            Confidence score (0-100)
        """
        # Reject low-quality data
        if data_quality < 30 or completeness < 0.5:
            logger.warning(f"Data quality too low for confidence calculation: quality={data_quality}, completeness={completeness}")
            return 0.0
        
        # Base confidence on data quality
        base_confidence = min(90, data_quality * 0.9)
        
        # Adjust based on technical signal strength
        if technical_signals:
            signal_strength = DataQualityValidator._assess_signal_strength(technical_signals)
            adjusted_confidence = base_confidence * signal_strength
        else:
            adjusted_confidence = base_confidence * 0.7  # Reduce confidence without technical signals
        
        # Ensure confidence stays within bounds
        final_confidence = max(0.0, min(90.0, adjusted_confidence))
        
        logger.info(f"Calculated confidence: {final_confidence:.1f} (base: {base_confidence:.1f}, signals: {technical_signals})")
        return final_confidence
    
    @staticmethod
    def _assess_signal_strength(signals: Dict[str, Any]) -> float:
        """Assess the strength of technical signals (0.0-1.0)"""
        if not signals:
            return 0.5
        
        # Count confirmed signals
        confirmed_count = sum(1 for signal in signals.values() if signal.get('confirmed', False))
        total_signals = len(signals)
        
        if total_signals == 0:
            return 0.5
        
        # Calculate signal strength
        strength = confirmed_count / total_signals
        
        # Boost strength if multiple timeframes agree
        if 'multi_timeframe' in signals and signals['multi_timeframe'].get('agreement', False):
            strength *= 1.2
        
        return min(1.0, strength)
    
    @staticmethod
    def validate_support_resistance(symbol: str, timeframe: str, 
                                  data_quality: float) -> Tuple[Optional[float], Optional[float]]:
        """
        Validate support/resistance calculation requirements.
        
        Args:
            symbol: Trading symbol
            timeframe: Analysis timeframe
            data_quality: Data quality score
            
        Returns:
            Tuple of (support, resistance) or (None, None) if insufficient data
        """
        # Require minimum data quality for S/R calculation
        if data_quality < 70:
            logger.warning(f"Insufficient data quality for S/R calculation: {data_quality} < 70")
            return None, None
        
        # Check if we have enough price history
        if not DataQualityValidator._has_adequate_price_history(symbol, timeframe):
            logger.warning(f"Insufficient price history for S/R calculation: {symbol} {timeframe}")
            return None, None
        
        # Calculate actual technical support/resistance
        support = DataQualityValidator._calculate_technical_support(symbol, timeframe)
        resistance = DataQualityValidator._calculate_technical_resistance(symbol, timeframe)
        
        return support, resistance
    
    @staticmethod
    def _has_adequate_price_history(symbol: str, timeframe: str) -> bool:
        """Check if we have adequate price history for technical analysis"""
        # This would integrate with your data providers
        # For now, return True as placeholder
        return True
    
    @staticmethod
    def _calculate_technical_support(symbol: str, timeframe: str) -> Optional[float]:
        """Calculate technical support level using actual analysis"""
        # This would implement your technical analysis logic
        # For now, return None as placeholder
        return None
    
    @staticmethod
    def _calculate_technical_resistance(symbol: str, timeframe: str) -> Optional[float]:
        """Calculate technical resistance level using actual analysis"""
        # This would implement your technical analysis logic
        # For now, return None as placeholder
        return None

class ActionValidator:
    """Validates trading actions based on data quality and technical confirmation"""
    
    @staticmethod
    def determine_action(change: float, technical_signals: Dict[str, Any], 
                        data_quality: float, volatility: float) -> str:
        """
        Determine trading action with validation.
        
        Args:
            change: Price change percentage
            technical_signals: Technical analysis signals
            data_quality: Data quality score
            volatility: Current market volatility
            
        Returns:
            Action: 'BUY', 'SELL', or 'HOLD'
        """
        # Require minimum data quality
        if data_quality < 60:
            logger.warning(f"Insufficient data quality for action determination: {data_quality} < 60")
            return 'HOLD'
        
        # Calculate dynamic threshold based on volatility
        threshold = ActionValidator._calculate_dynamic_threshold(volatility)
        
        # Require technical confirmation
        if not ActionValidator._has_technical_confirmation(technical_signals):
            logger.info("No technical confirmation, holding position")
            return 'HOLD'
        
        # Determine action based on validated signals
        if change > threshold and technical_signals.get('bullish', False):
            return 'BUY'
        elif change < -threshold and technical_signals.get('bearish', False):
            return 'SELL'
        
        return 'HOLD'
    
    @staticmethod
    def _calculate_dynamic_threshold(volatility: float) -> float:
        """Calculate dynamic threshold based on market volatility"""
        base_threshold = float(os.getenv('ACTION_THRESHOLD_BASE', '2.0'))
        
        # Adjust threshold based on volatility
        if volatility < 0.01:  # Low volatility
            return base_threshold * 0.5
        elif volatility > 0.05:  # High volatility
            return base_threshold * 2.0
        else:
            return base_threshold
    
    @staticmethod
    def _has_technical_confirmation(signals: Dict[str, Any]) -> bool:
        """Check if we have technical confirmation for trading action"""
        if not signals:
            return False
        
        # Require at least one confirmed signal
        confirmed_signals = [s for s in signals.values() if s.get('confirmed', False)]
        return len(confirmed_signals) > 0

def validate_data_quality_on_startup():
    """Validate data quality validation system on startup"""
    logger.info("Starting data quality validation system...")
    
    # Test confidence calculation
    test_confidence = DataQualityValidator.calculate_confidence(
        data_quality=80,
        completeness=0.9,
        technical_signals={'rsi': {'confirmed': True}}
    )
    
    if test_confidence > 0:
        logger.info("Data quality validation system ready")
        return True
    else:
        logger.error("Data quality validation system failed")
        return False 