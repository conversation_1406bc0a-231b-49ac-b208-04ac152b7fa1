"""
Modern Python Utilities for ASK Pipeline

Extracted and simplified modern Python patterns:
- AsyncResourceManager for resource management with async context managers
- ModernAsyncPatterns for common async utilities
- Integration with core logging and config
"""

import asyncio
import logging
from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass, field, asdict
from datetime import datetime
from contextlib import asynccontextmanager
from enum import Enum

from src.core.config_manager import ConfigManager
from src.database.unified_db import get_db_session  # Assuming unified_db for DB integration

logger = logging.getLogger(__name__)

class ResourceType(Enum):
    """Resource type enumeration"""
    DATABASE = "database"
    CACHE = "cache"
    HTTP_SESSION = "http_session"

@dataclass
class ResourceInfo:
    """Resource information"""
    resource_type: ResourceType
    name: str
    created_at: datetime = field(default_factory=datetime.utcnow)
    last_accessed: datetime = field(default_factory=datetime.utcnow)
    access_count: int = 0

class AsyncResourceManager:
    """Simplified async resource manager with context managers"""
    
    def __init__(self, config: ConfigManager):
        self.config = config
        self.resources: Dict[str, ResourceInfo] = {}
        self._lock = asyncio.Lock()
    
    @asynccontextmanager
    async def get_database_connection(self, connection_string: Optional[str] = None):
        """Async context manager for database connections with DB integration"""
        if connection_string is None:
            connection_string = self.config.get("database.connection_string", "default_db")
        
        connection_id = f"db_{hash(connection_string)}"
        
        async with self._lock:
            self.resources[connection_id] = ResourceInfo(
                resource_type=ResourceType.DATABASE,
                name="database_connection"
            )
            self.resources[connection_id].access_count += 1
        
        db_session = None
        try:
            logger.debug(f"Opening database connection: {connection_id}")
            db_session = await get_db_session()  # Use core unified_db
            yield db_session
            logger.info(f"Database operation completed for {connection_id}")
        except Exception as e:
            logger.error(f"Database connection error for {connection_id}: {e}")
            raise
        finally:
            if db_session:
                await db_session.close()
            async with self._lock:
                if connection_id in self.resources:
                    del self.resources[connection_id]
            logger.debug(f"Closed database connection: {connection_id}")
    
    @asynccontextmanager
    async def get_cache_connection(self, cache_url: Optional[str] = None):
        """Async context manager for cache connections"""
        if cache_url is None:
            cache_url = self.config.get("cache.url", "redis://localhost:6379")
        
        cache_id = f"cache_{hash(cache_url)}"
        
        async with self._lock:
            self.resources[cache_id] = ResourceInfo(
                resource_type=ResourceType.CACHE,
                name="cache_connection"
            )
            self.resources[cache_id].access_count += 1
        
        try:
            logger.debug(f"Opening cache connection: {cache_id}")
            # Placeholder for cache connection using core cache_service
            cache = {"url": cache_url, "connected": True}
            yield cache
            logger.info(f"Cache operation completed for {cache_id}")
        except Exception as e:
            logger.error(f"Cache connection error for {cache_id}: {e}")
            raise
        finally:
            async with self._lock:
                if cache_id in self.resources:
                    del self.resources[cache_id]
            logger.debug(f"Closed cache connection: {cache_id}")
    
    async def get_resource_stats(self) -> Dict[str, Any]:
        """Get resource statistics with logging"""
        async with self._lock:
            stats = {
                "total_resources": len(self.resources),
                "by_type": {
                    rt.value: sum(1 for r in self.resources.values() if r.resource_type == rt)
                    for rt in ResourceType
                },
                "resources": [asdict(r) for r in self.resources.values()]
            }
            logger.info(f"Resource stats: {stats}")
            return stats

class ModernAsyncPatterns:
    """Simplified modern async patterns"""
    
    @staticmethod
    async def gather_with_timeout(tasks: List[asyncio.Task], timeout: float = 30.0) -> List[Any]:
        """Gather tasks with timeout and logging"""
        try:
            results = await asyncio.wait_for(asyncio.gather(*tasks), timeout=timeout)
            logger.debug(f"Successfully gathered {len(tasks)} tasks")
            return results
        except asyncio.TimeoutError:
            logger.warning("Tasks timed out, cancelling remaining")
            for task in tasks:
                if not task.done():
                    task.cancel()
            raise
    
    @staticmethod
    async def retry_async(func: Callable, max_retries: int = 3, delay: float = 1.0) -> Any:
        """Retry async function with exponential backoff and logging"""
        for attempt in range(max_retries):
            try:
                return await func()
            except Exception as e:
                logger.warning(f"Attempt {attempt + 1} failed: {e}")
                if attempt == max_retries - 1:
                    logger.error(f"All {max_retries} retries failed for {func.__name__}")
                    raise
                await asyncio.sleep(delay * (2 ** attempt))
    
    @staticmethod
    async def batch_process(items: List[Any], processor: Callable, 
                            batch_size: int = 10, max_concurrent: int = 5) -> List[Any]:
        """Process items in batches with concurrency control and logging"""
        results = []
        for i in range(0, len(items), batch_size):
            batch = items[i:i + batch_size]
            logger.debug(f"Processing batch {i//batch_size + 1} with {len(batch)} items")
            
            semaphore = asyncio.Semaphore(max_concurrent)
            
            async def process_item(item):
                async with semaphore:
                    return await processor(item)
            
            batch_results = await asyncio.gather(*[process_item(item) for item in batch])
            results.extend(batch_results)
        
        logger.info(f"Batch processing completed: {len(results)} results")
        return results

# Global instances (using core config)
config_manager = ConfigManager()  # Assuming it can be instantiated
_resource_manager = AsyncResourceManager(config_manager)
_async_patterns = ModernAsyncPatterns()

def get_async_resource_manager() -> AsyncResourceManager:
    """Get global async resource manager"""
    return _resource_manager

def get_async_patterns() -> ModernAsyncPatterns:
    """Get global async patterns"""
    return _async_patterns