"""
Simplified Code Standards for ASK Pipeline

Ported from quality/code_standards.py:
- Basic integration with black, isort, mypy via subprocess
- Use core ConfigManager for tool configuration
- Simple reporting with logger
"""

import subprocess
import os
from typing import Dict, Any, List
from dataclasses import dataclass

from src.core.config_manager import ConfigManager
from src.core.monitoring.logger import get_structured_logger

logger = get_structured_logger(__name__)
config = ConfigManager()

@dataclass
class CodeQualityIssue:
    """Basic issue"""
    file: str
    line: int
    severity: str
    message: str
    tool: str

class CodeStandardsManager:
    """Simplified code standards checker"""
    
    def __init__(self):
        self.tools = config.get_section("quality.tools", default={
            "black": True,
            "isort": True,
            "mypy": True
        })
    
    def check_file(self, file_path: str) -> List[CodeQualityIssue]:
        """Check single file"""
        issues = []
        
        if self.tools.get("black") and file_path.endswith('.py'):
            issues.extend(self._run_black(file_path))
        
        if self.tools.get("isort") and file_path.endswith('.py'):
            issues.extend(self._run_isort(file_path))
        
        if self.tools.get("mypy"):
            issues.extend(self._run_mypy(file_path))
        
        return issues
    
    def _run_black(self, file_path: str) -> List[CodeQualityIssue]:
        """Run black"""
        try:
            result = subprocess.run(["black", "--check", file_path], capture_output=True, text=True)
            if result.returncode != 0:
                return [CodeQualityIssue(file=file_path, line=0, severity="error", message="Black formatting issues", tool="black")]
        except FileNotFoundError:
            logger.warning("black not found")
        return []
    
    def _run_isort(self, file_path: str) -> List[CodeQualityIssue]:
        """Run isort"""
        try:
            result = subprocess.run(["isort", "--check-only", file_path], capture_output=True, text=True)
            if result.returncode != 0:
                return [CodeQualityIssue(file=file_path, line=0, severity="error", message="Import sorting issues", tool="isort")]
        except FileNotFoundError:
            logger.warning("isort not found")
        return []
    
    def _run_mypy(self, file_path: str) -> List[CodeQualityIssue]:
        """Run mypy"""
        try:
            result = subprocess.run(["mypy", file_path], capture_output=True, text=True)
            if result.returncode != 0:
                lines = result.stdout.split('\n')
                issues = []
                for line in lines:
                    if ':' in line:
                        file, line_num, msg = line.split(':', 2)
                        issues.append(CodeQualityIssue(file=file, line=int(line_num), severity="error", message=msg.strip(), tool="mypy"))
                return issues
        except FileNotFoundError:
            logger.warning("mypy not found")
        return []
    
    def check_directory(self, directory: str) -> Dict[str, Any]:
        """Check directory"""
        issues = []
        for root, _, files in os.walk(directory):
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    issues.extend(self.check_file(file_path))
        
        logger.info(f"Found {len(issues)} quality issues in {directory}")
        return {"issues": [i.__dict__ for i in issues]}

manager = CodeStandardsManager()

def get_code_standards_manager() -> CodeStandardsManager:
    return manager