"""
Simplified Type Safety for ASK Pipeline

Ported from quality/type_safety.py:
- Basic runtime type checking decorator
- Integration with core config for strict mode
- Simple validation using typing hints
"""

import inspect
import functools
from typing import Any, Callable, get_type_hints
from dataclasses import dataclass

from src.core.config_manager import ConfigManager
from src.core.monitoring.logger import get_structured_logger

logger = get_structured_logger(__name__)
config = ConfigManager()

@dataclass
class ValidationResult:
    """Validation result"""
    is_valid: bool
    errors: list = None

class TypeSafetyManager:
    """Simplified type safety"""
    
    def __init__(self):
        self.strict = config.get("quality.type_strict", True)
    
    def validate_function(self, func: Callable) -> ValidationResult:
        """Validate function types"""
        hints = get_type_hints(func)
        if not hints:
            return ValidationResult(is_valid=False, errors=["No type hints"])
        
        # Basic check: ensure return type is defined
        if 'return' not in hints:
            return ValidationResult(is_valid=False, errors=["Missing return type"])
        
        logger.info(f"Validated {func.__name__}: {len(hints)} hints")
        return ValidationResult(is_valid=True)
    
    def type_check_decorator(self, func: Callable) -> Callable:
        """Decorator for runtime checking"""
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            hints = get_type_hints(func)
            bound = inspect.signature(func).bind(*args, **kwargs)
            bound.apply_defaults()
            
            errors = []
            for name, value in bound.arguments.items():
                if name in hints:
                    expected = hints[name]
                    if not isinstance(value, expected):
                        errors.append(f"{name}: expected {expected}, got {type(value)}")
            
            if errors:
                if self.strict:
                    raise ValueError("; ".join(errors))
                else:
                    logger.warning(f"Type issues in {func.__name__}: {errors}")
            
            return func(*args, **kwargs)
        return wrapper

manager = TypeSafetyManager()

def get_type_safety_manager() -> TypeSafetyManager:
    return manager