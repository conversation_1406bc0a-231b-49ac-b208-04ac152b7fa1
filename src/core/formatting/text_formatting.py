"""
Text Formatting Utilities for Financial Data
"""
from typing import Union

def format_currency(value: Union[float, int]) -> str:
    """Formats a numeric value as currency string (e.g., $1,234.56)."""
    try:
        return f"${value:,.2f}"
    except (TypeError, ValueError):
        return str(value)

def format_percentage(value: Union[float, int]) -> str:
    """Formats a numeric value as a percentage string (e.g., 12.34%)."""
    try:
        # Handle cases where value might already be a percentage (e.g., 0.1234)
        if 0 <= value <= 1:
            return f"{value * 100:.2f}%"
        return f"{value:.2f}%"
    except (TypeError, ValueError):
        return str(value)