from typing import List, Dict, Any
from dataclasses import dataclass, field
from datetime import datetime

# Import the new analysis template
from .analysis_template import AnalysisTemplate, PortfolioTemplate, MarketTrendTemplate

@dataclass
class StockPerformanceTemplate:
    """
    Structured template for stock performance analysis
    """
    symbol: str
    company_name: str
    return_percentage: float
    key_factors: List[str] = field(default_factory=list)
    current_price: float = None
    market_cap: float = None
    analysis_timestamp: datetime = field(default_factory=datetime.now)

    def generate_summary(self) -> str:
        """
        Generate a concise summary of stock performance
        
        Returns:
            str: Formatted stock performance summary
        """
        factors_str = "\n".join(f"• {factor}" for factor in self.key_factors)
        
        return f"""
🚀 {self.company_name} ({self.symbol})
10-Year Return: {self.return_percentage}%
Current Price: ${self.current_price}

Key Growth Factors:
{factors_str}
""".strip()

class ResponseGenerator:
    """
    Utility class for generating structured AI responses
    """
    
    @staticmethod
    def generate_analysis_response(
        symbol: str,
        current_price: float = None,
        target_price: str = None,
        indicators: str = None,
        sentiment: str = None,
        analysis_text: str = ""
    ) -> str:
        """
        Generate a structured technical analysis response
        
        Args:
            symbol: Stock ticker symbol
            current_price: Current stock price
            target_price: Target price for the stock
            indicators: Technical indicators used
            sentiment: Market sentiment (bullish/bearish/neutral)
            analysis_text: Raw analysis text
            
        Returns:
            str: Formatted analysis response
        """
        template = AnalysisTemplate(
            symbol=symbol,
            current_price=current_price,
            target_price=target_price,
            indicators=indicators,
            sentiment=sentiment,
            analysis_text=analysis_text
        )
        return template.generate()
    
    @staticmethod
    def generate_portfolio_response(
        symbols: List[str],
        total_value: float = None,
        allocation: Dict[str, float] = None,
        performance: Dict[str, float] = None,
        recommendations: List[str] = None
    ) -> str:
        """
        Generate a portfolio analysis response
        
        Args:
            symbols: List of stock symbols
            total_value: Total portfolio value
            allocation: Portfolio allocation percentages
            performance: Performance metrics for each symbol
            recommendations: Investment recommendations
            
        Returns:
            str: Formatted portfolio response
        """
        template = PortfolioTemplate(
            symbols=symbols,
            total_value=total_value,
            allocation=allocation,
            performance=performance,
            recommendations=recommendations
        )
        return template.generate()
    
    @staticmethod
    def generate_market_trend_response(
        trend: str,
        sectors: List[str] = None,
        key_drivers: List[str] = None,
        outlook: str = "",
        timeframe: str = "short-term"
    ) -> str:
        """
        Generate a market trend analysis response
        
        Args:
            trend: Overall market trend
            sectors: Key sectors to watch
            key_drivers: Main market drivers
            outlook: Market outlook
            timeframe: Analysis timeframe
            
        Returns:
            str: Formatted market trend response
        """
        template = MarketTrendTemplate(
            trend=trend,
            sectors=sectors,
            key_drivers=key_drivers,
            outlook=outlook,
            timeframe=timeframe
        )
        return template.generate()
    
    @staticmethod
    def generate_top_performers_response(
        performers: List[StockPerformanceTemplate], 
        title: str = "Top Performing Stocks"
    ) -> str:
        """
        Generate a comprehensive response for top performing stocks
        
        Args:
            performers (List[StockPerformanceTemplate]): List of top-performing stocks
            title (str, optional): Response title. Defaults to "Top Performing Stocks"
        
        Returns:
            str: Formatted response
        """
        # Sort performers by return percentage in descending order
        sorted_performers = sorted(
            performers, 
            key=lambda x: x.return_percentage, 
            reverse=True
        )
        
        # Generate individual stock summaries
        stock_summaries = [
            performer.generate_summary() 
            for performer in sorted_performers
        ]
        
        # Combine summaries
        full_response = f"""
📈 {title} (2014-2024)

{chr(10).join(stock_summaries)}

🔍 Investment Insights:
• Diversification is key
• Past performance doesn't guarantee future results
• Conduct thorough research before investing
""".strip()
        
        return full_response
    
    @staticmethod
    def add_data_validation_note(response: str) -> str:
        """
        Add a standardized data validation note to the response
        
        Args:
            response (str): Original response
        
        Returns:
            str: Response with validation note
        """
        validation_note = f"""
⚠️ Data Validation:
• Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
• Confidence: Moderate
• Recommendation: Verify with current financial sources
• Disclaimer: For informational purposes only

🤝 Always consult a financial advisor before making investment decisions.
"""
        return response + validation_note

# Example usage template
def generate_top_stocks_analysis() -> str:
    """
    Example method to generate top stocks analysis
    
    Returns:
        str: Formatted top stocks analysis
    """
    top_performers = [
        StockPerformanceTemplate(
            symbol="NVDA",
            company_name="Nvidia",
            return_percentage=8600,
            key_factors=[
                "AI leadership", 
                "GPU dominance", 
                "Data center growth"
            ],
            current_price=1000.50,
            market_cap=2500000000000
        ),
        # Add more performers...
    ]
    
    response = ResponseGenerator.generate_top_performers_response(
        top_performers, 
        "Top Performing Stocks of the Decade"
    )
    
    return ResponseGenerator.add_data_validation_note(response) 