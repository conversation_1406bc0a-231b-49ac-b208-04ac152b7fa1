"""
Core Configuration Manager for Trading Bot

Provides basic configuration management for the trading bot with environment variable support.
"""

import os
from typing import Any, Optional, Dict
from enum import Enum
import logging
from datetime import datetime
from src.database.unified_db import get_supabase_client

logger = logging.getLogger(__name__)

class Environment(Enum):
    """Environment types"""
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"
    TESTING = "testing"

class CoreConfigManager:
    """Core configuration manager for trading bot"""
    
    def __init__(self):
        self.environment = self._get_environment()
        self._config_cache = {}
        self._sections = {}
        self.load_from_db()
    
    def _get_environment(self) -> Environment:
        """Get environment from environment variable"""
        env_str = os.getenv('ENVIRONMENT', 'development').lower()
        try:
            return Environment(env_str)
        except ValueError:
            return Environment.DEVELOPMENT
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value with fallback to environment variables"""
        # Check cache first
        if key in self._config_cache:
            return self._config_cache[key]
        
        # Check environment variable
        env_key = key.upper().replace('.', '_')
        env_value = os.getenv(env_key)
        
        if env_value is not None:
            # Convert common types
            if env_value.lower() in ('true', 'false'):
                value = env_value.lower() == 'true'
            elif env_value.isdigit():
                value = int(env_value)
            elif env_value.replace('.', '').isdigit():
                value = float(env_value)
            else:
                value = env_value
            
            self._config_cache[key] = value
            return value
        
        # Return default
        self._config_cache[key] = default
        return default
    
    def get_bool(self, key: str, default: bool = False) -> bool:
        """Get boolean configuration value"""
        value = self.get(key, default)
        if isinstance(value, bool):
            return value
        if isinstance(value, str):
            return value.lower() in ('true', '1', 'yes', 'on')
        return bool(value)
    
    def get_int(self, key: str, default: int = 0) -> int:
        """Get integer configuration value"""
        value = self.get(key, default)
        if isinstance(value, int):
            return value
        try:
            return int(value)
        except (ValueError, TypeError):
            return default
    
    def get_float(self, key: str, default: float = 0.0) -> float:
        """Get float configuration value"""
        value = self.get(key, default)
        if isinstance(value, (int, float)):
            return float(value)
        try:
            return float(value)
        except (ValueError, TypeError):
            return default
    
    def get_list(self, key: str, default: list = None, separator: str = ',') -> list:
        """Get list configuration value"""
        if default is None:
            default = []
        
        value = self.get(key, default)
        if isinstance(value, list):
            return value
        if isinstance(value, str):
            return [item.strip() for item in value.split(separator) if item.strip()]
        return default
    
    def set(self, key: str, value: Any) -> None:
        """Set configuration value"""
        self._config_cache[key] = value
    
    def get_all(self) -> Dict[str, Any]:
        """Get all configuration values"""
        return self._config_cache.copy()
    
    def clear_cache(self) -> None:
        """Clear configuration cache"""
        self._config_cache.clear()

    def load_from_db(self) -> None:
        """Load configuration sections from database"""
        try:
            client = get_supabase_client()
            results = client.table('configs').select('section, data').eq('environment', self.environment.value).execute().data
            for row in results:
                self.set_section(row['section'], row['data'])
            logger.info(f"Loaded {len(results)} config sections from database for {self.environment.value}")
        except Exception as e:
            logger.warning(f"Could not load config sections from DB for {self.environment.value}: {e}")

    def get_section(self, section: str, default: Dict[str, Any] = None) -> Dict[str, Any]:
        """Get a configuration section"""
        if default is None:
            default = {}
        return self._sections.get(section, default.copy())

    def set_section(self, section: str, config: Dict[str, Any]) -> None:
        """Set a configuration section"""
        self._sections[section] = config.copy()

    def save_to_db(self) -> None:
        """Save all configuration sections to database"""
        try:
            client = get_supabase_client()
            config_list = []
            for section_name, section_data in self._sections.items():
                config_list.append({
                    'section': section_name,
                    'data': section_data,
                    'environment': self.environment.value,
                    'updated_at': datetime.utcnow().isoformat()
                })
            if config_list:
                client.table('configs').upsert(config_list, on_conflict=['section', 'environment']).execute()
            logger.info(f"Saved {len(config_list)} config sections to database for {self.environment.value}")
        except Exception as e:
            logger.error(f"Failed to save config to DB for {self.environment.value}: {e}")
            raise

# Global configuration manager instance
_config_manager: Optional[CoreConfigManager] = None

def get_config() -> CoreConfigManager:
    """Get global configuration manager"""
    global _config_manager
    if _config_manager is None:
        _config_manager = CoreConfigManager()
    return _config_manager

def cleanup_config():
    """Cleanup global configuration manager"""
    global _config_manager
    if _config_manager:
        _config_manager.clear_cache()
        _config_manager = None

# Backward compatibility alias
ConfigManager = CoreConfigManager
