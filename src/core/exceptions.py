"""
Core exceptions module for TradingView Automation.

This module defines base exceptions used throughout the application.
"""

class TradingBotBaseException(Exception):
    """Base exception for all trading bot exceptions."""
    
    def __init__(self, message: str = "An error occurred in the trading bot"):
        self.message = message
        super().__init__(self.message)
        
class ConfigurationError(TradingBotBaseException):
    """Exception raised for configuration errors."""
    
    def __init__(self, message: str = "Configuration error"):
        super().__init__(f"Configuration error: {message}")
        
class DataProviderError(TradingBotBaseException):
    """Exception raised for data provider errors."""
    
    def __init__(self, provider: str, message: str = "Data provider error"):
        super().__init__(f"Data provider error ({provider}): {message}")
        
class ValidationError(TradingBotBaseException):
    """Exception raised for validation errors."""
    
    def __init__(self, message: str = "Validation error"):
        super().__init__(f"Validation error: {message}")
        
class APIError(TradingBotBaseException):
    """Exception raised for API errors."""
    
    def __init__(self, api: str, message: str = "API error"):
        super().__init__(f"API error ({api}): {message}")
        
class DatabaseError(TradingBotBaseException):
    """Exception raised for database errors."""
    
    def __init__(self, message: str = "Database error"):
        super().__init__(f"Database error: {message}")
        
class AuthenticationError(TradingBotBaseException):
    """Exception raised for authentication errors."""
    
    def __init__(self, message: str = "Authentication error"):
        super().__init__(f"Authentication error: {message}")
        
class PermissionError(TradingBotBaseException):
    """Exception raised for permission errors."""
    
    def __init__(self, message: str = "Permission error"):
        super().__init__(f"Permission error: {message}")
        
class RateLimitError(TradingBotBaseException):
    """Exception raised for rate limit errors."""
    
    def __init__(self, message: str = "Rate limit exceeded"):
        super().__init__(f"Rate limit error: {message}")
        
class TimeoutError(TradingBotBaseException):
    """Exception raised for timeout errors."""
    
    def __init__(self, message: str = "Operation timed out"):
        super().__init__(f"Timeout error: {message}")
        
class PipelineError(TradingBotBaseException):
    """Exception raised for pipeline errors."""
    
    def __init__(self, pipeline: str, message: str = "Pipeline error"):
        super().__init__(f"Pipeline error ({pipeline}): {message}")
        
class AIServiceError(TradingBotBaseException):
    """Exception raised for AI service errors."""
    
    def __init__(self, message: str = "AI service error"):
        super().__init__(f"AI service error: {message}")

# Export all exceptions
__all__ = [
    'TradingBotBaseException',
    'ConfigurationError',
    'DataProviderError',
    'ValidationError',
    'APIError',
    'DatabaseError',
    'AuthenticationError',
    'PermissionError',
    'RateLimitError',
    'TimeoutError',
    'PipelineError',
    'AIServiceError'
] 
class MarketDataError(TradingBotBaseException):
    """Exception raised for market data errors."""
    
    def __init__(self, message: str = "Market data error"):
        super().__init__(f"Market data error: {message}")

class ProviderUnavailableError(MarketDataError):
    """Exception raised when a data provider is unavailable."""
    
    def __init__(self, provider: str, message: str = "Provider unavailable"):
        super().__init__(f"Provider unavailable ({provider}): {message}")

class RateLimitError(MarketDataError):
    """Exception raised when rate limit is exceeded."""
    
    def __init__(self, provider: str, message: str = "Rate limit exceeded"):
        super().__init__(f"Rate limit exceeded ({provider}): {message}")
