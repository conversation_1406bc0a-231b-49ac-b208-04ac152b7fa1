"""
Pipeline Monitoring and Grading System

This module provides a robust system for monitoring and grading pipeline execution.
It allows for detailed performance tracking, quality assessment, and automated
reporting on pipeline execution quality.
"""

import time
import logging
import statistics
from enum import Enum
from typing import Dict, List, Any, Optional, Callable, Union, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import json
import os
from pathlib import Path

logger = logging.getLogger(__name__)

class GradeLevel(Enum):
    """Grade levels for pipeline steps and overall pipeline execution."""
    A_PLUS = "A+"
    A = "A"
    A_MINUS = "A-"
    B_PLUS = "B+"
    B = "B"
    B_MINUS = "B-"
    C_PLUS = "C+"
    C = "C"
    C_MINUS = "C-"
    D_PLUS = "D+"
    D = "D"
    D_MINUS = "D-"
    F = "F"

    @classmethod
    def from_score(cls, score: float) -> 'GradeLevel':
        """Convert a numeric score (0-100) to a letter grade."""
        if score >= 97:
            return cls.A_PLUS
        elif score >= 93:
            return cls.A
        elif score >= 90:
            return cls.A_MINUS
        elif score >= 87:
            return cls.B_PLUS
        elif score >= 83:
            return cls.B
        elif score >= 80:
            return cls.B_MINUS
        elif score >= 77:
            return cls.C_PLUS
        elif score >= 73:
            return cls.C
        elif score >= 70:
            return cls.C_MINUS
        elif score >= 67:
            return cls.D_PLUS
        elif score >= 63:
            return cls.D
        elif score >= 60:
            return cls.D_MINUS
        else:
            return cls.F

@dataclass
class StepGrade:
    """Detailed grade for a single pipeline step."""
    step_name: str
    execution_time: float
    success: bool
    error_message: Optional[str] = None
    data_quality_score: Optional[float] = None
    performance_score: Optional[float] = None
    reliability_score: Optional[float] = None
    overall_score: Optional[float] = None
    grade: Optional[GradeLevel] = None
    metrics: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)

    def calculate_overall_score(self) -> float:
        """Calculate the overall score based on component scores with emphasis on response quality."""
        scores = []
        weights = []
        
        if self.data_quality_score is not None:
            scores.append(self.data_quality_score)
            weights.append(0.7)  # 70% weight for data quality (response quality) - increased from 40%
            
        if self.performance_score is not None:
            scores.append(self.performance_score)
            weights.append(0.2)  # 20% weight for performance - reduced from 30%
            
        if self.reliability_score is not None:
            scores.append(self.reliability_score)
            weights.append(0.1)  # 10% weight for reliability - reduced from 30%
        
        # If no component scores are available, base on success
        if not scores:
            return 100.0 if self.success else 0.0
        
        # Calculate weighted average
        total_weight = sum(weights)
        weighted_sum = sum(score * weight for score, weight in zip(scores, weights))
        
        return weighted_sum / total_weight if total_weight > 0 else 0.0
    
    def assign_grade(self) -> GradeLevel:
        """Assign a letter grade based on the overall score."""
        if self.overall_score is None:
            self.overall_score = self.calculate_overall_score()
        
        return GradeLevel.from_score(self.overall_score)
    
    def finalize(self) -> 'StepGrade':
        """Calculate final scores and grades if not already done."""
        if self.overall_score is None:
            self.overall_score = self.calculate_overall_score()
        
        if self.grade is None:
            self.grade = self.assign_grade()
            
        return self

@dataclass
class PipelineGrade:
    """Overall grade for an entire pipeline execution."""
    pipeline_id: str
    pipeline_name: str
    start_time: datetime
    end_time: Optional[datetime] = None
    execution_time: Optional[float] = None
    step_grades: List[StepGrade] = field(default_factory=list)
    overall_score: Optional[float] = None
    grade: Optional[GradeLevel] = None
    success_rate: Optional[float] = None
    avg_step_time: Optional[float] = None
    total_errors: int = 0
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def add_step_grade(self, step_grade: StepGrade) -> None:
        """Add a step grade to the pipeline grade."""
        self.step_grades.append(step_grade)
        
        # Update error count
        if not step_grade.success:
            self.total_errors += 1
    
    def calculate_metrics(self) -> None:
        """Calculate aggregate metrics based on step grades."""
        if not self.step_grades:
            return
            
        # Calculate success rate
        successful_steps = sum(1 for grade in self.step_grades if grade.success)
        self.success_rate = (successful_steps / len(self.step_grades)) * 100
        
        # Calculate average step time
        step_times = [grade.execution_time for grade in self.step_grades]
        self.avg_step_time = statistics.mean(step_times) if step_times else 0
        
        # Ensure all step grades are finalized
        for step in self.step_grades:
            step.finalize()
    
    def calculate_overall_score(self) -> float:
        """Calculate the overall pipeline score with emphasis on response quality."""
        if not self.step_grades:
            return 0.0
            
        # Calculate metrics if not already done
        if self.success_rate is None:
            self.calculate_metrics()
        
        # Component scores with weights - prioritize response quality over technical metrics
        components = [
            (self.success_rate or 0, 0.2),  # 20% weight for success rate (reduced from 40%)
            (100 - min(100, self.total_errors * 10), 0.1),  # 10% weight for error count (reduced from 30%)
        ]
        
        # Add average step score if available - this now includes response quality
        step_scores = [step.overall_score for step in self.step_grades if step.overall_score is not None]
        if step_scores:
            avg_step_score = statistics.mean(step_scores)
            components.append((avg_step_score, 0.7))  # 70% weight for step scores (increased from 30%)
        
        # Calculate weighted average
        weighted_sum = sum(score * weight for score, weight in components)
        total_weight = sum(weight for _, weight in components)
        
        return weighted_sum / total_weight if total_weight > 0 else 0.0
    
    def assign_grade(self) -> GradeLevel:
        """Assign a letter grade based on the overall score."""
        if self.overall_score is None:
            self.overall_score = self.calculate_overall_score()
        
        return GradeLevel.from_score(self.overall_score)
    
    def finalize(self) -> 'PipelineGrade':
        """Finalize the pipeline grade by calculating all metrics and scores."""
        if self.end_time is None:
            self.end_time = datetime.now()
            
        if self.execution_time is None and self.start_time and self.end_time:
            self.execution_time = (self.end_time - self.start_time).total_seconds()
        
        self.calculate_metrics()
        
        if self.overall_score is None:
            self.overall_score = self.calculate_overall_score()
            
        if self.grade is None:
            self.grade = self.assign_grade()
            
        return self
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert the pipeline grade to a dictionary."""
        result = {
            "pipeline_id": self.pipeline_id,
            "pipeline_name": self.pipeline_name,
            "start_time": self.start_time.isoformat(),
            "end_time": self.end_time.isoformat() if self.end_time else None,
            "execution_time": self.execution_time,
            "overall_score": self.overall_score,
            "grade": self.grade.value if self.grade else None,
            "success_rate": self.success_rate,
            "avg_step_time": self.avg_step_time,
            "total_errors": self.total_errors,
            "metadata": self.metadata,
            "steps": []
        }
        
        for step in self.step_grades:
            step_dict = {
                "step_name": step.step_name,
                "execution_time": step.execution_time,
                "success": step.success,
                "error_message": step.error_message,
                "data_quality_score": step.data_quality_score,
                "performance_score": step.performance_score,
                "reliability_score": step.reliability_score,
                "overall_score": step.overall_score,
                "grade": step.grade.value if step.grade else None,
                "metrics": step.metrics,
                "timestamp": step.timestamp.isoformat()
            }
            result["steps"].append(step_dict)
            
        return result
    
    def save_to_file(self, directory: str = "logs/pipeline_grades") -> str:
        """Save the pipeline grade to a JSON file."""
        try:
            logger.info(f"Attempting to save pipeline grade to directory: {directory}")
            logger.info(f"Current working directory: {os.getcwd()}")
            
            os.makedirs(directory, exist_ok=True)
            logger.info(f"Directory '{directory}' created or exists")
            
            # Check if directory is writable
            if os.access(directory, os.W_OK):
                logger.info(f"Directory '{directory}' is writable")
            else:
                logger.warning(f"Directory '{directory}' is NOT writable")
            
            filename = f"{self.pipeline_name}_{self.pipeline_id}_{self.start_time.strftime('%Y%m%d_%H%M%S')}.json"
            filepath = os.path.join(directory, filename)
            logger.info(f"Full filepath: {filepath}")
            
            with open(filepath, 'w') as f:
                json.dump(self.to_dict(), f, indent=2)
                
            logger.info(f"Successfully saved pipeline grade to {filepath}")
            return filepath
            
        except PermissionError as e:
            logger.error(f"Permission denied saving to {directory}/{filename}: errno={e.errno}, strerror={e.strerror}, filepath={filepath}")
            logger.error(f"Directory permissions: {os.stat(directory).st_mode if os.path.exists(directory) else 'Directory does not exist'}")
            logger.error(f"Current user: {os.getuid()}:{os.getgid()}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error saving pipeline grade: {e}", exc_info=True)
            raise

class PipelineGrader:
    """
    Pipeline grading system that monitors and evaluates pipeline execution.
    
    This class provides methods to track pipeline execution, grade individual steps,
    and generate comprehensive reports on pipeline performance and quality.
    """
    
    def __init__(self, 
                 pipeline_name: str, 
                 save_grades: bool = True,
                 grade_directory: str = "logs/pipeline_grades"):
        """
        Initialize a new pipeline grader.
        
        Args:
            pipeline_name: Name of the pipeline being graded
            save_grades: Whether to automatically save grades to disk
            grade_directory: Directory to save grade files
        """
        self.pipeline_name = pipeline_name
        self.pipeline_id = f"{pipeline_name}_{int(time.time())}_{id(self)}"
        self.start_time = datetime.now()  # Use datetime.now() for consistent timing
        self.save_grades = save_grades
        self.grade_directory = grade_directory
        self.step_grades: List[StepGrade] = []
        self.current_step_start: Optional[float] = None
        self.current_step_name: Optional[str] = None
        self.metadata: Dict[str, Any] = {}
        
        logger.info(f"Started grading pipeline: {pipeline_name} (ID: {self.pipeline_id})")
    
    def start_step(self, step_name: str) -> None:
        """
        Start timing a new pipeline step.
        
        Args:
            step_name: Name of the step being started
        """
        self.current_step_name = step_name
        self.current_step_start = time.time()
        logger.debug(f"Started pipeline step: {step_name}")
    
    def end_step(self,
                success: bool = True,
                error_message: Optional[str] = None,
                data_quality_score: Optional[float] = None,
                performance_score: Optional[float] = None,
                reliability_score: Optional[float] = None,
                metrics: Optional[Dict[str, Any]] = None) -> StepGrade:
        """
        End timing for the current step and record its grade.

        Args:
            success: Whether the step completed successfully
            error_message: Error message if the step failed
            data_quality_score: Score for data quality (0-100)
            performance_score: Score for performance (0-100)
            reliability_score: Score for reliability (0-100)
            metrics: Additional metrics to record for this step

        Returns:
            The grade for this step
        """
        if self.current_step_start is None or self.current_step_name is None:
            # Create a mock step for debugging purposes
            logger.warning(f"end_step called without active step. Creating mock step: {success}")
            if self.current_step_name is None:
                self.current_step_name = "unknown_step"
            if self.current_step_start is None:
                self.current_step_start = time.time() - 0.1  # 100ms ago

        # Check if we're trying to end the same step twice
        if not success and error_message and self.current_step_name:
            # Look for existing step with same name
            for step_grade in self.step_grades:
                if step_grade.step_name == self.current_step_name and not step_grade.success:
                    logger.warning(f"Attempting to end already failed step: {self.current_step_name}")
                    # Return existing failed step
                    return step_grade
        
        execution_time = time.time() - self.current_step_start
        
        step_grade = StepGrade(
            step_name=self.current_step_name,
            execution_time=execution_time,
            success=success,
            error_message=error_message,
            data_quality_score=data_quality_score,
            performance_score=performance_score,
            reliability_score=reliability_score,
            metrics=metrics or {}
        )
        
        # Calculate scores and grade
        step_grade.finalize()
        
        # Add to list of step grades
        self.step_grades.append(step_grade)
        
        # Log result
        grade_str = step_grade.grade.value if step_grade.grade else "N/A"
        if success:
            logger.info(f"Step '{self.current_step_name}' completed in {execution_time:.2f}s with grade {grade_str}")
        else:
            logger.warning(f"Step '{self.current_step_name}' failed after {execution_time:.2f}s with grade {grade_str}: {error_message}")
        
        # Reset current step tracking
        self.current_step_name = None
        self.current_step_start = None
        
        return step_grade
    
    def add_metadata(self, key: str, value: Any) -> None:
        """
        Add metadata to the pipeline grade.
        
        Args:
            key: Metadata key
            value: Metadata value
        """
        self.metadata[key] = value
    
    def finalize_grade(self) -> PipelineGrade:
        """
        Finalize the pipeline grade and return the result.
        
        Returns:
            The final pipeline grade
        """
        # Calculate execution time
        end_time = datetime.now()
        if isinstance(self.start_time, (int, float)):
            execution_time = time.time() - self.start_time
            start_time = datetime.fromtimestamp(self.start_time)
        else:
            execution_time = (end_time - self.start_time).total_seconds()
            start_time = self.start_time
        
        if not self.step_grades:
            # No steps were graded, create a minimal grade
            pipeline_grade = PipelineGrade(
                pipeline_name=self.pipeline_name,
                pipeline_id=self.pipeline_id,
                start_time=start_time,
                end_time=end_time,
                execution_time=execution_time,
                step_grades=[],
                metadata={
                    **self.metadata,
                    'avg_data_quality': 0.0,
                    'avg_performance': 0.0,
                    'avg_reliability': 0.0
                }
            )
            pipeline_grade.finalize()
            return pipeline_grade
            
        # Calculate average scores across all steps
        data_quality_scores = [step.data_quality_score for step in self.step_grades 
                             if step.data_quality_score is not None]
        performance_scores = [step.performance_score for step in self.step_grades 
                           if step.performance_score is not None]
        reliability_scores = [step.reliability_score for step in self.step_grades 
                           if step.reliability_score is not None]
        
        # Check for failed stages - if any critical stage failed, heavily penalize the overall grade
        failed_stages = [step for step in self.step_grades if not step.success]
        if failed_stages:
            # Calculate percentage of failed stages
            failure_percentage = len(failed_stages) / len(self.step_grades)
            # Apply a significant penalty based on the percentage of failed stages
            failure_penalty = min(80.0, failure_percentage * 100)
            logger.warning(f"Pipeline has {len(failed_stages)} failed stages out of {len(self.step_grades)}. Applying {failure_penalty:.1f}% penalty.")
        else:
            failure_penalty = 0.0
        
        # Calculate average scores with fallbacks
        avg_data_quality = sum(data_quality_scores) / len(data_quality_scores) if data_quality_scores else 50.0
        avg_performance = sum(performance_scores) / len(performance_scores) if performance_scores else 50.0
        avg_reliability = sum(reliability_scores) / len(reliability_scores) if reliability_scores else 50.0
        
        # Apply failure penalty to all scores
        if failure_penalty > 0:
            avg_data_quality = max(0, avg_data_quality - failure_penalty)
            avg_performance = max(0, avg_performance - failure_penalty)
            avg_reliability = max(0, avg_reliability - failure_penalty)
        
        # Create pipeline grade with calculated metrics
        pipeline_grade = PipelineGrade(
            pipeline_name=self.pipeline_name,
            pipeline_id=self.pipeline_id,
            start_time=start_time,
            end_time=end_time,
            execution_time=execution_time,
            step_grades=self.step_grades,
            # Aggregate scores are stored in metadata to avoid unexpected kwargs
            metadata={
                **self.metadata, 
                'avg_data_quality': avg_data_quality, 
                'avg_performance': avg_performance, 
                'avg_reliability': avg_reliability
            }
        )
        
        # Finalize the grade
        pipeline_grade.finalize()
        
        # Log result
        grade_str = pipeline_grade.grade.value if pipeline_grade.grade else "N/A"
        logger.info(f"Pipeline '{self.pipeline_name}' completed in {execution_time:.2f}s with grade {grade_str}")
        
        # Save to file if enabled
        if self.save_grades:
            filepath = pipeline_grade.save_to_file(self.grade_directory)
            logger.info(f"Pipeline grade saved to {filepath}")
            
        return pipeline_grade

# Example grading functions for common metrics

def grade_data_quality(data: Any, 
                      required_fields: List[str] = None,
                      expected_length: Optional[int] = None,
                      validation_fn: Optional[Callable[[Any], bool]] = None) -> float:
    """
    Grade the quality of data produced by a pipeline step.
    
    Args:
        data: The data to grade
        required_fields: List of required fields in the data
        expected_length: Expected length of the data (for lists/arrays)
        validation_fn: Custom validation function
        
    Returns:
        A score from 0-100
    """
    score = 100.0
    deductions = []
    
    # Check if data exists
    if data is None:
        return 0.0
    
    # Check required fields
    if required_fields:
        if isinstance(data, dict):
            missing_fields = [field for field in required_fields if field not in data]
            if missing_fields:
                deduction = len(missing_fields) / len(required_fields) * 50
                deductions.append(deduction)
                logger.debug(f"Missing required fields: {missing_fields}")
        else:
            deductions.append(50.0)
            logger.debug("Data is not a dictionary, cannot check required fields")
    
    # Check expected length
    if expected_length is not None:
        if hasattr(data, '__len__'):
            actual_length = len(data)
            if actual_length != expected_length:
                # Deduct based on percentage difference
                diff_pct = abs(actual_length - expected_length) / expected_length
                deduction = min(40.0, diff_pct * 100)
                deductions.append(deduction)
                logger.debug(f"Length mismatch: expected {expected_length}, got {actual_length}")
        else:
            deductions.append(30.0)
            logger.debug("Data has no length, cannot check expected length")
    
    # Run custom validation
    if validation_fn:
        try:
            if not validation_fn(data):
                deductions.append(40.0)
                logger.debug("Custom validation failed")
        except Exception as e:
            deductions.append(50.0)
            logger.debug(f"Custom validation error: {str(e)}")
    
    # Apply deductions
    for deduction in deductions:
        score -= deduction
    
    return max(0.0, score)

def grade_performance(execution_time: float,
                      target_time: float,
                      critical_threshold: Optional[float] = None) -> float:
    """
    Grade the performance of a pipeline step based on execution time.
    
    Args:
        execution_time: Actual execution time in seconds
        target_time: Target execution time in seconds
        critical_threshold: Critical threshold in seconds (fails if exceeded)
        
    Returns:
        A score from 0-100
    """
    # If critical threshold is exceeded, return 0
    if critical_threshold and execution_time > critical_threshold:
        return 0.0
    
    # If execution time is below target, perfect score
    if execution_time <= target_time:
        return 100.0
    
    # Calculate score based on how much the execution time exceeds the target
    ratio = target_time / execution_time
    score = ratio * 100
    
    return max(0.0, min(100.0, score))

def grade_reliability(success: bool,
                      error_count: int = 0,
                      retry_count: int = 0,
                      max_retries: int = 3) -> float:
    """
    Grade the reliability of a pipeline step.
    
    Args:
        success: Whether the step completed successfully
        error_count: Number of errors encountered
        retry_count: Number of retries needed
        max_retries: Maximum allowed retries
        
    Returns:
        A score from 0-100
    """
    if not success:
        return 0.0
    
    score = 100.0
    
    # Deduct for errors
    if error_count > 0:
        score -= min(70, error_count * 15)
    
    # Deduct for retries
    if retry_count > 0:
        retry_penalty = (retry_count / max_retries) * 30
        score -= retry_penalty
    
    return max(0.0, score)