import yaml
import os
from typing import Dict, Any

def load_config(config_path: str = None) -> Dict[str, Any]:
    """
    Load configuration from YAML file.
    
    Args:
        config_path: Path to config file. Defaults to 'config.yaml' in project root.
    
    Returns:
        Dictionary containing configuration data.
    """
    if config_path is None:
        # Calculate project root dynamically. Assuming this file is in src/config/
        # Path: /path/to/root/src/config/config_manager.py
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(os.path.dirname(current_dir))
        config_path = os.path.join(project_root, 'config.yaml')
    
    try:
        with open(config_path, 'r') as file:
            config = yaml.safe_load(file)
        return config or {}
    except FileNotFoundError:
        print(f"Config file not found at {config_path}. Using empty config.")
        return {}
    except yaml.YAMLError as e:
        print(f"Error parsing config file: {e}")
        return {}
    except Exception as e:
        print(f"Unexpected error loading config: {e}")
        return {}

# Example usage (for testing)
if __name__ == "__main__":
    config = load_config()
    print("Loaded config:", config)