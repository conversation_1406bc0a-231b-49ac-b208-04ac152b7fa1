"""
Lazy import utility for heavy dependencies.
This module provides a way to defer importing heavy packages until they're actually needed,
which significantly improves startup times for applications that only need a subset of functionality.
"""

import importlib
import sys
from typing import Any, Optional, Union, Dict, Set
import logging

logger = logging.getLogger(__name__)

class LazyModule:
    """A lazy-loaded module that imports only when first accessed."""
    
    def __init__(self, module_name: str, package: Optional[str] = None):
        self._module_name = module_name
        self._package = package
        self._module = None
        self._loaded = False
    
    def _load_module(self) -> Any:
        """Load the module if not already loaded."""
        if not self._loaded:
            try:
                if self._package:
                    self._module = importlib.import_module(self._module_name, self._package)
                else:
                    self._module = importlib.import_module(self._module_name)
                self._loaded = True
                logger.debug(f"Lazy loaded module: {self._module_name}")
            except ImportError as e:
                logger.error(f"Failed to lazy import {self._module_name}: {e}")
                raise
        return self._module
    
    def __getattr__(self, name: str) -> Any:
        """Delegate attribute access to the loaded module."""
        if name.startswith('_'):
            # Don't intercept private attributes
            raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")
        module = self._load_module()
        return getattr(module, name)
    
    def __dir__(self) -> list:
        """Return the attributes of the loaded module."""
        module = self._load_module()
        return dir(module)
    
    def __repr__(self) -> str:
        """String representation of the lazy module."""
        if self._loaded:
            return f"<LazyModule '{self._module_name}' (loaded)>"
        else:
            return f"<LazyModule '{self._module_name}' (not yet loaded)>"

class LazyImportManager:
    """Manager for lazy imports with caching and monitoring."""
    
    def __init__(self):
        self._loaded_modules: Set[str] = set()
        self._lazy_modules: Dict[str, LazyModule] = {}
        self._import_times: Dict[str, float] = {}
    
    def register_lazy_module(self, alias: str, module_name: str, package: Optional[str] = None) -> LazyModule:
        """
        Register a module for lazy loading.
        
        Args:
            alias: The name to use when accessing the module
            module_name: The actual module name to import
            package: Optional package name for relative imports
            
        Returns:
            LazyModule instance
        """
        if alias not in self._lazy_modules:
            self._lazy_modules[alias] = LazyModule(module_name, package)
        return self._lazy_modules[alias]
    
    def get_module(self, alias: str) -> LazyModule:
        """
        Get a lazy module by alias.
        
        Args:
            alias: The alias of the module
            
        Returns:
            LazyModule instance
        """
        if alias not in self._lazy_modules:
            raise KeyError(f"Module '{alias}' not registered for lazy loading")
        return self._lazy_modules[alias]
    
    def is_loaded(self, alias: str) -> bool:
        """
        Check if a module has been loaded.
        
        Args:
            alias: The alias of the module
            
        Returns:
            True if loaded, False otherwise
        """
        return alias in self._loaded_modules
    
    def get_import_stats(self) -> Dict[str, Any]:
        """
        Get statistics about lazy imports.
        
        Returns:
            Dictionary with import statistics
        """
        return {
            "total_registered": len(self._lazy_modules),
            "loaded_modules": list(self._loaded_modules),
            "import_times": self._import_times.copy()
        }

# Global lazy import manager
lazy_import_manager = LazyImportManager()

# Convenience functions
def lazy_import(alias: str, module_name: str, package: Optional[str] = None) -> LazyModule:
    """
    Register and return a lazy module.
    
    Args:
        alias: The name to use when accessing the module
        module_name: The actual module name to import
        package: Optional package name for relative imports
        
    Returns:
        LazyModule instance
    """
    return lazy_import_manager.register_lazy_module(alias, module_name, package)

def get_lazy_module(alias: str) -> LazyModule:
    """
    Get a previously registered lazy module.
    
    Args:
        alias: The alias of the module
        
    Returns:
        LazyModule instance
    """
    return lazy_import_manager.get_module(alias)

# Pre-register common heavy modules
# Data analysis libraries
lazy_import("pandas", "pandas")
lazy_import("numpy", "numpy")
lazy_import("scipy", "scipy")

# Machine learning libraries
lazy_import("sklearn", "sklearn")
lazy_import("torch", "torch")
lazy_import("transformers", "transformers")

# Financial data libraries
lazy_import("yfinance", "yfinance")
lazy_import("alpha_vantage", "alpha_vantage")

# Example usage:
# pd = get_lazy_module("pandas")
# np = get_lazy_module("numpy")
# Then use pd.DataFrame, np.array, etc. as normal