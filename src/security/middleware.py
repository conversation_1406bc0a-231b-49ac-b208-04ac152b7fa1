"""
Comprehensive Security Middleware for TradingView Webhook Receiver
Provides rate limiting, request validation, security headers, and webhook-specific security measures.
"""

import time
import hashlib
import hmac
import json
from typing import Dict, Any, Optional, List
from urllib.parse import urlparse
from fastapi import Request, HTTPException, status
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response

from src.shared.error_handling.logging import get_logger

logger = get_logger(__name__)

class SecurityMiddleware(BaseHTTPMiddleware):
    """
    Comprehensive security middleware for the TradingView webhook receiver.
    Implements rate limiting, request validation, security headers, and webhook-specific security.
    """
    
    def __init__(
        self, 
        app,
        rate_limit: int = 100,  # requests per window
        window: int = 60,  # seconds
        max_content_length: int = 1024 * 1024,  # 1MB max request size
        webhook_secret: Optional[str] = None,
        allowed_origins: Optional[List[str]] = None,
        exempt_routes: Optional[List[str]] = None
    ):
        super().__init__(app)
        self.rate_limit = rate_limit
        self.window = window
        self.max_content_length = max_content_length
        self.webhook_secret = webhook_secret
        self.allowed_origins = allowed_origins or ["*"]
        self.exempt_routes = exempt_routes or ["/health", "/metrics", "/docs", "/openapi.json"]
        self.request_counts = {}
        self.blocked_ips = set()
        
    def _get_client_ip(self, request: Request) -> str:
        """Extract client IP from request headers."""
        xff = request.headers.get("x-forwarded-for")
        if xff:
            return xff.split(",")[0].strip()
        return request.client.host if request.client else "unknown"
    
    def _is_exempt_route(self, path: str) -> bool:
        """Check if route is exempt from security checks."""
        return any(route in path for route in self.exempt_routes)
    
    def _validate_origin(self, request: Request) -> bool:
        """Validate request origin."""
        if "*" in self.allowed_origins:
            return True
            
        origin = request.headers.get("origin")
        referer = request.headers.get("referer")
        
        if origin:
            parsed_origin = urlparse(origin)
            return parsed_origin.netloc in self.allowed_origins
        elif referer:
            parsed_referer = urlparse(referer)
            return parsed_referer.netloc in self.allowed_origins
        
        return True  # Allow requests without origin/referer
    
    def _validate_webhook_signature(self, request: Request, body: bytes) -> bool:
        """Validate TradingView webhook signature if secret is provided."""
        if not self.webhook_secret:
            return True  # Skip validation if no secret provided
            
        signature = request.headers.get("x-tradingview-signature")
        if not signature:
            logger.warning("Webhook signature missing")
            return False
            
        try:
            expected_signature = hmac.new(
                self.webhook_secret.encode(),
                body,
                hashlib.sha256
            ).hexdigest()
            
            return hmac.compare_digest(signature, expected_signature)
        except Exception as e:
            logger.error(f"Error validating webhook signature: {e}")
            return False
    
    def _validate_request_size(self, request: Request) -> bool:
        """Validate request size."""
        content_length = request.headers.get("content-length")
        if content_length and int(content_length) > self.max_content_length:
            return False
        return True
    
    def _check_rate_limit(self, client_ip: str) -> bool:
        """Check if client has exceeded rate limit."""
        if client_ip in self.blocked_ips:
            return False
            
        current_time = time.time()
        
        # Clean up old request records
        self.request_counts = {
            ip: [t for t in times if t is not None and current_time - t <= self.window]
            for ip, times in self.request_counts.items()
            if times is not None
        }
        
        # Check rate limit
        if client_ip in self.request_counts:
            if len(self.request_counts[client_ip]) >= self.rate_limit:
                logger.warning(f"Rate limit exceeded for IP {client_ip}")
                self.blocked_ips.add(client_ip)
                return False
            self.request_counts[client_ip].append(current_time)
        else:
            self.request_counts[client_ip] = [current_time]
        
        return True
    
    def _add_security_headers(self, response: Response) -> None:
        """Add security headers to response."""
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['X-Frame-Options'] = 'DENY'
        response.headers['X-XSS-Protection'] = '1; mode=block'
        response.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        response.headers['Content-Security-Policy'] = "default-src 'self'"
        response.headers['X-Permitted-Cross-Domain-Policies'] = 'none'
        response.headers['Server'] = ''  # Remove server information
        
        # Add HSTS if HTTPS
        if hasattr(response, 'request') and response.request.headers.get("x-forwarded-proto", "").lower() == "https":
            response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
    
    def _log_security_event(self, event_type: str, client_ip: str, details: Dict[str, Any]) -> None:
        """Log security events for monitoring."""
        try:
            logger.warning("Security event: %s | client_ip=%s | details=%s", event_type, client_ip, details)
        except Exception:
            # Fallback to simple logging to avoid breaking request flow
            logger.warning(f"Security event: {event_type} | ip={client_ip} | details={details}")

    async def dispatch(self, request: Request, call_next):
        """Main middleware dispatch method."""
        client_ip = self._get_client_ip(request)
        path = request.url.path
        
        try:
            # Skip security checks for exempt routes
            if self._is_exempt_route(path):
                response = await call_next(request)
                self._add_security_headers(response)
                return response
            
            # Validate request size
            if not self._validate_request_size(request):
                self._log_security_event("REQUEST_TOO_LARGE", client_ip, {
                    "content_length": request.headers.get("content-length"),
                    "max_allowed": self.max_content_length
                })
                return JSONResponse(
                    content={"error": "Request too large"},
                    status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE
                )
            
            # Validate origin
            if not self._validate_origin(request):
                self._log_security_event("INVALID_ORIGIN", client_ip, {
                    "origin": request.headers.get("origin"),
                    "referer": request.headers.get("referer")
                })
                return JSONResponse(
                    content={"error": "Invalid origin"},
                    status_code=status.HTTP_403_FORBIDDEN
                )
            
            # Check rate limit
            if not self._check_rate_limit(client_ip):
                self._log_security_event("RATE_LIMIT_EXCEEDED", client_ip, {
                    "rate_limit": self.rate_limit,
                    "window": self.window
                })
                return JSONResponse(
                    content={"error": "Too many requests"},
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS
                )
            
            # For webhook endpoints, validate signature
            if path.startswith("/webhook"):
                body = await request.body()
                if not self._validate_webhook_signature(request, body):
                    self._log_security_event("INVALID_WEBHOOK_SIGNATURE", client_ip, {
                        "path": path,
                        "signature": request.headers.get("x-tradingview-signature")
                    })
                    return JSONResponse(
                        content={"error": "Invalid webhook signature"},
                        status_code=status.HTTP_401_UNAUTHORIZED
                    )
                
                # Create new request with body for downstream processing
                async def receive():
                    return {"type": "http.request", "body": body}
                
                # Process the request
                response = await call_next(request)
            else:
                response = await call_next(request)
            
            # Add security headers to response
            self._add_security_headers(response)
            
            return response
            
        except Exception as e:
            logger.error(f"Security middleware error: {e}", exc_info=True)
            self._log_security_event("MIDDLEWARE_ERROR", client_ip, {
                "error": str(e),
                "path": path
            })
            return JSONResponse(
                content={"error": "Internal security error"},
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
