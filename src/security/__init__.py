# Security module
from .middleware import SecurityMiddleware

def create_security_middleware(
    rate_limit: int = 100,
    window: int = 60,
    max_content_length: int = 1024 * 1024,
    webhook_secret: str = None,
    allowed_origins: list = None,
    exempt_routes: list = None
):
    """
    Factory function to create a configured SecurityMiddleware instance.
    
    Args:
        rate_limit: Maximum requests per window (default: 100)
        window: Time window in seconds (default: 60)
        max_content_length: Maximum request size in bytes (default: 1MB)
        webhook_secret: Secret for webhook signature validation (optional)
        allowed_origins: List of allowed origins (default: ["*"])
        exempt_routes: List of routes exempt from security checks
    
    Returns:
        Configured SecurityMiddleware instance
    """
    return SecurityMiddleware(
        app=None,  # Will be set by FastAPI
        rate_limit=rate_limit,
        window=window,
        max_content_length=max_content_length,
        webhook_secret=webhook_secret,
        allowed_origins=allowed_origins,
        exempt_routes=exempt_routes
    )

__all__ = ['SecurityMiddleware', 'create_security_middleware']
