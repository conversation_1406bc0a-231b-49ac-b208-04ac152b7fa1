
"""
Unified Data Access Layer

Provides a single interface for accessing both SQLAlchemy and Supabase databases
with correlation ID logging, connection pooling, and automatic failover.
"""

import asyncio
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union, AsyncGenerator
from contextlib import asynccontextmanager
import backoff
from src.shared.error_handling.logging import get_logger
from src.shared.error_handling.fallback import DatabaseError, ConfigurationError

logger = get_logger(__name__)


class DataAccessInterface(ABC):
    """Abstract interface for data access operations."""
    
    @abstractmethod
    async def query(
        self,
        table: str,
        filters: Optional[Dict[str, Any]] = None,
        correlation_id: Optional[str] = None,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """Query data from a table."""
        pass
    
    @abstractmethod
    async def insert(
        self,
        table: str,
        data: Dict[str, Any],
        correlation_id: Optional[str] = None,
        **kwargs
    ) -> Optional[Dict[str, Any]]:
        """Insert data into a table."""
        pass
    
    @abstractmethod
    async def update(
        self,
        table: str,
        filters: Dict[str, Any],
        data: Dict[str, Any],
        correlation_id: Optional[str] = None,
        **kwargs
    ) -> Optional[Dict[str, Any]]:
        """Update data in a table."""
        pass
    
    @abstractmethod
    async def delete(
        self,
        table: str,
        filters: Dict[str, Any],
        correlation_id: Optional[str] = None,
        **kwargs
    ) -> bool:
        """Delete data from a table."""
        pass
    
    @abstractmethod
    async def execute_raw(
        self,
        query: str,
        params: Optional[Dict[str, Any]] = None,
        correlation_id: Optional[str] = None,
        **kwargs
    ) -> Any:
        """Execute raw SQL query."""
        pass
    
    @abstractmethod
    async def health_check(self, correlation_id: Optional[str] = None) -> bool:
        """Check database connection health."""
        pass


class SQLAlchemyAdapter(DataAccessInterface):
    """SQLAlchemy database adapter."""
    
    def __init__(self):
        self._initialized = False
        self._engine = None
    
    async def _ensure_initialized(self):
        """Ensure SQLAlchemy is initialized."""
        if not self._initialized:
            from .connection import engine
            self._engine = engine
            self._initialized = True
    
    @backoff.on_exception(
        backoff.expo,
        Exception,
        max_tries=3,
        max_time=30
    )
    async def query(
        self,
        table: str,
        filters: Optional[Dict[str, Any]] = None,
        correlation_id: Optional[str] = None,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """Query data using SQLAlchemy."""
        await self._ensure_initialized()
        
        from .query_wrapper import execute_query_with_correlation
        
        where_clause = ""
        params = {}
        if filters:
            conditions = [f"{k} = :{k}" for k in filters.keys()]
            where_clause = f" WHERE {' AND '.join(conditions)}"
            params.update(filters)
        
        query = f"SELECT * FROM {table}{where_clause}"
        
        result = await execute_query_with_correlation(
            query=query,
            params=params if params else None,
            correlation_id=correlation_id,
            query_type="select"
        )
        
        if hasattr(result, 'fetchall'):
            rows = result.fetchall()
            return [dict(row._mapping) for row in rows]
        
        return []
    
    @backoff.on_exception(
        backoff.expo,
        Exception,
        max_tries=3,
        max_time=30
    )
    async def insert(
        self,
        table: str,
        data: Dict[str, Any],
        correlation_id: Optional[str] = None,
        **kwargs
    ) -> Optional[Dict[str, Any]]:
        """Insert data using SQLAlchemy."""
        await self._ensure_initialized()
        
        from .query_wrapper import execute_query_with_correlation
        
        columns = ", ".join(data.keys())
        placeholders = ", ".join(f":{k}" for k in data.keys())
        query = f"INSERT INTO {table} ({columns}) VALUES ({placeholders}) RETURNING *"
        
        result = await execute_query_with_correlation(
            query=query,
            params=data,
            correlation_id=correlation_id,
            query_type="insert"
        )
        
        if hasattr(result, 'fetchone'):
            row = result.fetchone()
            return dict(row._mapping) if row else None
        
        return None
    
    @backoff.on_exception(
        backoff.expo,
        Exception,
        max_tries=3,
        max_time=30
    )
    async def update(
        self,
        table: str,
        filters: Dict[str, Any],
        data: Dict[str, Any],
        correlation_id: Optional[str] = None,
        **kwargs
    ) -> Optional[Dict[str, Any]]:
        """Update data using SQLAlchemy."""
        await self._ensure_initialized()
        
        from .query_wrapper import execute_query_with_correlation
        
        set_clause = ", ".join(f"{k} = :set_{k}" for k in data.keys())
        where_clause = " AND ".join(f"{k} = :where_{k}" for k in filters.keys())
        
        query = f"UPDATE {table} SET {set_clause} WHERE {where_clause} RETURNING *"
        
        params = {f"set_{k}": v for k, v in data.items()}
        params.update({f"where_{k}": v for k, v in filters.items()})
        
        result = await execute_query_with_correlation(
            query=query,
            params=params,
            correlation_id=correlation_id,
            query_type="update"
        )
        
        if hasattr(result, 'fetchall'):
            rows = result.fetchall()
            return dict(rows[0]._mapping) if rows else None
        
        return None
    
    @backoff.on_exception(
        backoff.expo,
        Exception,
        max_tries=3,
        max_time=30
    )
    async def delete(
        self,
        table: str,
        filters: Dict[str, Any],
        correlation_id: Optional[str] = None,
        **kwargs
    ) -> bool:
        """Delete data using SQLAlchemy."""
        await self._ensure_initialized()
        
        from .query_wrapper import execute_query_with_correlation
        
        where_clause = " AND ".join(f"{k} = :{k}" for k in filters.keys())
        query = f"DELETE FROM {table} WHERE {where_clause}"
        
        result = await execute_query_with_correlation(
            query=query,
            params=filters,
            correlation_id=correlation_id,
            query_type="delete"
        )
        
        return getattr(result, 'rowcount', 0) > 0
    
    async def execute_raw(
        self,
        query: str,
        params: Optional[Dict[str, Any]] = None,
        correlation_id: Optional[str] = None,
        **kwargs
    ) -> Any:
        """Execute raw SQL query."""
        await self._ensure_initialized()