"""
Supabase-Only Database Manager
=============================

Simplified database manager optimized for Supabase operations.
Perfect for business monetization with built-in API access controls.

Features:
- Supabase SDK integration
- Health monitoring
- Business-ready API structure
- Read-only access tier support
"""

import os
import logging
import uuid
from datetime import datetime
from typing import Optional, Dict, Any, List

# Supabase SDK (required)
try:
    from supabase import create_client, Client
    SUPABASE_AVAILABLE = True
except ImportError:
    SUPABASE_AVAILABLE = False
    Client = None
    create_client = None
    raise ImportError("Supabase package required. Install with: pip install supabase")

logger = logging.getLogger(__name__)

class SupabaseConfig:
    """Supabase configuration with validation"""
    
    def __init__(self):
        self.supabase_url = os.getenv('SUPABASE_URL', '')
        self.supabase_key = os.getenv('SUPABASE_KEY', '')
        self.environment = os.getenv('ENVIRONMENT', 'development')
        
        self._validate_config()
    
    def _validate_config(self):
        """Validate Supabase configuration"""
        if not self.supabase_url or not self.supabase_key:
            raise ValueError("SUPABASE_URL and SUPABASE_KEY are required")
        
        if '.supabase.co' not in self.supabase_url:
            raise ValueError("SUPABASE_URL must be a valid Supabase URL")
    
    def get_connection_info(self) -> Dict[str, Any]:
        """Get connection information for logging (without sensitive data)"""
        return {
            'has_supabase_url': bool(self.supabase_url),
            'has_supabase_key': bool(self.supabase_key),
            'environment': self.environment,
            'supabase_project': self.supabase_url.split('//')[1].split('.')[0] if self.supabase_url else None
        }

# Global instances
_config: Optional[SupabaseConfig] = None
_supabase_client: Optional[Client] = None

def get_config() -> SupabaseConfig:
    """Get Supabase configuration (singleton)"""
    global _config
    if _config is None:
        _config = SupabaseConfig()
    return _config

def get_supabase_client() -> Client:
    """
    Get Supabase client (singleton)
    
    Returns:
        Client: Supabase client instance
    
    Raises:
        ValueError: If Supabase is not configured
        ImportError: If supabase package is not installed
    """
    global _supabase_client
    
    if not SUPABASE_AVAILABLE:
        raise ImportError("Supabase package not installed. Install with: pip install supabase")
    
    config = get_config()
    
    if _supabase_client is None:
        try:
            _supabase_client = create_client(config.supabase_url, config.supabase_key)
            logger.info("Supabase client initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Supabase client: {e}")
            raise
    
    return _supabase_client

async def test_connections() -> Dict[str, bool]:
    """
    Test Supabase connection
    
    Returns:
        Dict[str, bool]: Connection test results
    """
    results = {}
    
    # Test Supabase connection
    try:
        client = get_supabase_client()
        # Test with a simple query to a known table
        result = client.table('tradingview_webhooks').select('id').limit(1).execute()
        results['supabase'] = True
        logger.info("Supabase connection test: SUCCESS")
    except Exception as e:
        results['supabase'] = False
        logger.error(f"Supabase connection test: FAILED - {e}")
    
    return results

async def initialize_database():
    """
    Initialize database connections and test connectivity
    """
    config = get_config()
    logger.info("Initializing Supabase database connection")
    
    # Test connection
    results = await test_connections()
    
    # Log results
    if results.get('supabase'):
        logger.info("Supabase connection: READY")
    else:
        logger.error("Supabase connection: FAILED")
        raise RuntimeError("Supabase connection is not available")
    
    logger.info("Database initialization completed successfully")

class UnifiedDatabaseManager:
    """
    Simplified database manager for Supabase operations
    Provides business-ready API structure for monetization
    """
    
    def __init__(self):
        self.logger = logger
        self._initialized = False
    
    async def initialize(self) -> bool:
        """Initialize database connections"""
        try:
            await initialize_database()
            self._initialized = True
            return True
        except Exception as e:
            self.logger.error(f"Database initialization failed: {e}")
            return False
    
    def get_supabase_client(self) -> Client:
        """Get Supabase client"""
        return get_supabase_client()
    
    async def health_check(self) -> bool:
        """Check database connection health"""
        try:
            client = get_supabase_client()
            result = client.table('tradingview_webhooks').select('id').limit(1).execute()
            return True
        except Exception as e:
            self.logger.error(f"Database health check failed: {e}")
            return False
    
    # Core table operations for the 3 existing tables
    def get_webhooks(self, filters: Optional[Dict] = None, limit: int = 100) -> List[Dict[str, Any]]:
        """Get TradingView webhooks"""
        client = get_supabase_client()
        query = client.table('tradingview_webhooks').select('*')
        
        if filters:
            for key, value in filters.items():
                query = query.eq(key, value)
        
        return query.limit(limit).execute().data
    
    def get_tickers(self, symbol: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get ticker information"""
        client = get_supabase_client()
        query = client.table('tickers').select('*')
        
        if symbol:
            query = query.eq('symbol', symbol)
        
        return query.execute().data
    
    def get_balances(self, user_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get balance information"""
        client = get_supabase_client()
        query = client.table('balances').select('*')
        
        if user_id:
            query = query.eq('user_id', user_id)
        
        return query.execute().data
    
    def store_webhook(self, webhook_data: Dict[str, Any]) -> Dict[str, Any]:
        """Store a new webhook"""
        client = get_supabase_client()
        
        # Add metadata
        webhook_record = {
            'id': str(uuid.uuid4()),
            'created_at': datetime.utcnow().isoformat(),
            'raw_data': webhook_data,
            'status': 'received'
        }
        
        result = client.table('tradingview_webhooks').insert(webhook_record).execute()
        return result.data[0] if result.data else {}

# Global database manager instance
database_manager = UnifiedDatabaseManager()

# Convenience functions for backward compatibility
async def execute_query(query: str, params: Optional[Dict] = None) -> Any:
    """Execute a query using Supabase (for backward compatibility)"""
    logger.warning("execute_query is deprecated. Use Supabase client directly.")
    client = get_supabase_client()
    # Note: Raw SQL queries are not directly supported by Supabase SDK
    # This is a placeholder for backward compatibility
    return []

async def get_db_connection_status() -> Dict[str, Any]:
    """
    Get database connection status for health checks

    Returns:
        Dict[str, Any]: Connection status information
    """
    try:
        client = get_supabase_client()
        # Test with a simple query
        result = client.table('tradingview_webhooks').select('id').limit(1).execute()
        return {
            "connected": True,
            "status": "healthy",
            "message": "Supabase connection successful",
            "version": "Supabase SDK",
            "test_query": "successful"
        }
    except Exception as e:
        return {
            "connected": False,
            "status": "error",
            "message": f"Connection failed: {str(e)}",
            "version": "Supabase SDK",
            "test_query": "failed"
        }

def get_table(table_name: str):
    """Get table reference (for backward compatibility)"""
    client = get_supabase_client()
    return client.table(table_name)

# Export main functions
__all__ = [
    'get_supabase_client',
    'get_config', 
    'database_manager',
    'UnifiedDatabaseManager',
    'test_connections',
    'initialize_database'
]
