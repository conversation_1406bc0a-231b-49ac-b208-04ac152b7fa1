"""
Database Query Optimizer
Provides intelligent query optimization, caching, and performance monitoring for database operations
"""

import asyncio
import time
import hashlib
import json
from typing import Dict, Any, Optional, List, Tuple, Union
from datetime import datetime, timedelta
from dataclasses import dataclass
from collections import defaultdict, deque
import re

from src.shared.error_handling.logging import get_logger
from src.shared.cache.cache_service import CacheService
from src.shared.redis.redis_manager import get_redis_client

logger = get_logger(__name__)


@dataclass
class QueryMetrics:
    """Query performance metrics"""
    query_hash: str
    execution_time: float
    rows_returned: int
    cache_hit: bool
    timestamp: datetime
    optimization_applied: bool = False
    error: Optional[str] = None


class QueryAnalyzer:
    """Analyzes SQL queries for optimization opportunities"""
    
    def __init__(self):
        self.optimization_patterns = {
            'missing_index': [
                r'WHERE\s+(\w+)\s*=',  # Simple equality without index
                r'ORDER\s+BY\s+(\w+)',  # ORDER BY without index
                r'GROUP\s+BY\s+(\w+)',  # GROUP BY without index
            ],
            'inefficient_joins': [
                r'SELECT\s+\*\s+FROM.*JOIN',  # SELECT * with JOINs
                r'LEFT\s+JOIN.*WHERE.*IS\s+NULL',  # LEFT JOIN with NULL check
            ],
            'subquery_optimization': [
                r'WHERE\s+\w+\s+IN\s*\(SELECT',  # IN subquery
                r'WHERE\s+EXISTS\s*\(SELECT',  # EXISTS subquery
            ],
            'limit_optimization': [
                r'SELECT.*ORDER\s+BY.*(?!LIMIT)',  # ORDER BY without LIMIT
            ]
        }
    
    def analyze_query(self, query: str) -> Dict[str, Any]:
        """Analyze query for optimization opportunities"""
        analysis = {
            'query_type': self._detect_query_type(query),
            'complexity_score': self._calculate_complexity(query),
            'optimization_suggestions': [],
            'estimated_cost': self._estimate_cost(query),
            'cacheable': self._is_cacheable(query)
        }
        
        # Check for optimization patterns
        for category, patterns in self.optimization_patterns.items():
            for pattern in patterns:
                if re.search(pattern, query, re.IGNORECASE):
                    analysis['optimization_suggestions'].append({
                        'category': category,
                        'pattern': pattern,
                        'suggestion': self._get_optimization_suggestion(category)
                    })
        
        return analysis
    
    def _detect_query_type(self, query: str) -> str:
        """Detect the type of SQL query"""
        query_upper = query.upper().strip()
        if query_upper.startswith('SELECT'):
            return 'SELECT'
        elif query_upper.startswith('INSERT'):
            return 'INSERT'
        elif query_upper.startswith('UPDATE'):
            return 'UPDATE'
        elif query_upper.startswith('DELETE'):
            return 'DELETE'
        else:
            return 'OTHER'
    
    def _calculate_complexity(self, query: str) -> int:
        """Calculate query complexity score (1-10)"""
        score = 1
        
        # Count JOINs
        joins = len(re.findall(r'\bJOIN\b', query, re.IGNORECASE))
        score += joins * 2
        
        # Count subqueries
        subqueries = len(re.findall(r'\(SELECT', query, re.IGNORECASE))
        score += subqueries * 3
        
        # Count WHERE conditions
        where_conditions = len(re.findall(r'\bWHERE\b|\bAND\b|\bOR\b', query, re.IGNORECASE))
        score += where_conditions
        
        # Count aggregations
        aggregations = len(re.findall(r'\b(COUNT|SUM|AVG|MAX|MIN|GROUP BY)\b', query, re.IGNORECASE))
        score += aggregations
        
        return min(score, 10)
    
    def _estimate_cost(self, query: str) -> str:
        """Estimate query execution cost"""
        complexity = self._calculate_complexity(query)
        
        if complexity <= 3:
            return 'LOW'
        elif complexity <= 6:
            return 'MEDIUM'
        else:
            return 'HIGH'
    
    def _is_cacheable(self, query: str) -> bool:
        """Determine if query results can be cached"""
        # Don't cache write operations
        if re.search(r'\b(INSERT|UPDATE|DELETE|CREATE|DROP|ALTER)\b', query, re.IGNORECASE):
            return False
        
        # Don't cache queries with time-sensitive functions
        if re.search(r'\b(NOW|CURRENT_TIMESTAMP|RAND|RANDOM)\b', query, re.IGNORECASE):
            return False
        
        return True
    
    def _get_optimization_suggestion(self, category: str) -> str:
        """Get optimization suggestion for category"""
        suggestions = {
            'missing_index': 'Consider adding an index on the filtered/sorted columns',
            'inefficient_joins': 'Review JOIN conditions and consider using INNER JOIN where possible',
            'subquery_optimization': 'Consider rewriting subquery as JOIN for better performance',
            'limit_optimization': 'Add LIMIT clause to prevent unnecessary data retrieval'
        }
        return suggestions.get(category, 'Review query for optimization opportunities')


class QueryCache:
    """Intelligent query result caching"""
    
    def __init__(self, default_ttl: int = 1800):  # 30 minutes
        self.default_ttl = default_ttl
        self.cache_stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'total_queries': 0
        }
        self.query_patterns = {}
        
    async def get_cached_result(self, query_hash: str) -> Optional[Any]:
        """Get cached query result"""
        try:
            redis_client = await get_redis_client()
            if redis_client:
                cached_data = await redis_client.get(f"query:{query_hash}")
                if cached_data:
                    self.cache_stats['hits'] += 1
                    return json.loads(cached_data)
            
            self.cache_stats['misses'] += 1
            return None
            
        except Exception as e:
            logger.warning(f"Cache retrieval failed for query {query_hash}: {e}")
            self.cache_stats['misses'] += 1
            return None
    
    async def cache_result(self, query_hash: str, result: Any, ttl: Optional[int] = None) -> bool:
        """Cache query result"""
        try:
            redis_client = await get_redis_client()
            if redis_client:
                cache_ttl = ttl or self.default_ttl
                await redis_client.setex(
                    f"query:{query_hash}",
                    cache_ttl,
                    json.dumps(result)
                )
                return True
            return False
            
        except Exception as e:
            logger.warning(f"Cache storage failed for query {query_hash}: {e}")
            return False
    
    def generate_query_hash(self, query: str, params: Optional[Dict] = None) -> str:
        """Generate hash for query and parameters"""
        query_normalized = re.sub(r'\s+', ' ', query.strip().lower())
        
        if params:
            params_str = json.dumps(params, sort_keys=True)
            combined = f"{query_normalized}:{params_str}"
        else:
            combined = query_normalized
        
        return hashlib.md5(combined.encode()).hexdigest()
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache performance statistics"""
        total_requests = self.cache_stats['hits'] + self.cache_stats['misses']
        hit_rate = (self.cache_stats['hits'] / total_requests * 100) if total_requests > 0 else 0
        
        return {
            **self.cache_stats,
            'hit_rate': hit_rate,
            'total_requests': total_requests
        }


class DatabaseQueryOptimizer:
    """Main query optimizer with caching and performance monitoring"""
    
    def __init__(self, cache_ttl: int = 1800):
        self.analyzer = QueryAnalyzer()
        self.cache = QueryCache(cache_ttl)
        self.metrics = deque(maxlen=10000)
        self.slow_query_threshold = 2.0  # seconds
        self.optimization_enabled = True
        
    async def optimize_query(self, query: str, params: Optional[Dict] = None, 
                           force_cache: bool = False) -> Tuple[Any, QueryMetrics]:
        """
        Optimize query execution with caching and performance monitoring
        
        Args:
            query: SQL query string
            params: Query parameters
            force_cache: Force caching even if query seems non-cacheable
            
        Returns:
            Tuple of (result, metrics)
        """
        start_time = time.time()
        query_hash = self.cache.generate_query_hash(query, params)
        
        # Analyze query
        analysis = self.analyzer.analyze_query(query)
        
        # Try cache first if query is cacheable
        cached_result = None
        if analysis['cacheable'] or force_cache:
            cached_result = await self.cache.get_cached_result(query_hash)
            
            if cached_result is not None:
                execution_time = time.time() - start_time
                metrics = QueryMetrics(
                    query_hash=query_hash,
                    execution_time=execution_time,
                    rows_returned=len(cached_result) if isinstance(cached_result, list) else 1,
                    cache_hit=True,
                    timestamp=datetime.now(),
                    optimization_applied=True
                )
                
                self.metrics.append(metrics)
                logger.debug(f"Query cache hit ({execution_time:.3f}s saved): {query_hash}")
                return cached_result, metrics
        
        # Execute query (this would integrate with your actual database layer)
        try:
            result = await self._execute_query(query, params, analysis)
            execution_time = time.time() - start_time
            
            # Cache result if appropriate
            if (analysis['cacheable'] or force_cache) and result is not None:
                # Determine TTL based on query type and complexity
                ttl = self._calculate_cache_ttl(analysis)
                await self.cache.cache_result(query_hash, result, ttl)
            
            # Record metrics
            metrics = QueryMetrics(
                query_hash=query_hash,
                execution_time=execution_time,
                rows_returned=len(result) if isinstance(result, list) else 1,
                cache_hit=False,
                timestamp=datetime.now(),
                optimization_applied=self.optimization_enabled
            )
            
            self.metrics.append(metrics)
            
            # Log slow queries
            if execution_time > self.slow_query_threshold:
                logger.warning(f"SLOW QUERY ({execution_time:.2f}s): {query[:100]}...")
                await self._log_slow_query(query, params, execution_time, analysis)
            
            return result, metrics
            
        except Exception as e:
            execution_time = time.time() - start_time
            metrics = QueryMetrics(
                query_hash=query_hash,
                execution_time=execution_time,
                rows_returned=0,
                cache_hit=False,
                timestamp=datetime.now(),
                optimization_applied=False,
                error=str(e)
            )
            
            self.metrics.append(metrics)
            logger.error(f"Query execution failed ({execution_time:.2f}s): {e}")
            raise
    
    async def _execute_query(self, query: str, params: Optional[Dict], 
                           analysis: Dict[str, Any]) -> Any:
        """Execute the actual database query"""
        # This would integrate with your actual database layer
        # For now, simulate query execution
        
        # Simulate execution time based on complexity
        complexity_delay = analysis['complexity_score'] * 0.1
        await asyncio.sleep(complexity_delay)
        
        # Return mock result
        return [{"id": i, "data": f"result_{i}"} for i in range(10)]
    
    def _calculate_cache_ttl(self, analysis: Dict[str, Any]) -> int:
        """Calculate appropriate cache TTL based on query analysis"""
        base_ttl = self.cache.default_ttl
        
        # Adjust TTL based on query type and complexity
        if analysis['query_type'] == 'SELECT':
            if analysis['complexity_score'] <= 3:
                return base_ttl * 2  # Simple queries can be cached longer
            elif analysis['complexity_score'] >= 7:
                return base_ttl // 2  # Complex queries cached for shorter time
        
        return base_ttl
    
    async def _log_slow_query(self, query: str, params: Optional[Dict], 
                            execution_time: float, analysis: Dict[str, Any]):
        """Log slow query for analysis"""
        slow_query_info = {
            'query': query[:500],  # Truncate long queries
            'params': params,
            'execution_time': execution_time,
            'analysis': analysis,
            'timestamp': datetime.now().isoformat()
        }
        
        # This could be sent to a monitoring system
        logger.warning(f"Slow query logged: {json.dumps(slow_query_info, indent=2)}")
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get query performance summary"""
        if not self.metrics:
            return {"status": "no_data"}
        
        recent_metrics = list(self.metrics)[-1000:]  # Last 1000 queries
        successful_queries = [m for m in recent_metrics if m.error is None]
        failed_queries = [m for m in recent_metrics if m.error is not None]
        cached_queries = [m for m in recent_metrics if m.cache_hit]
        
        if successful_queries:
            avg_execution_time = sum(m.execution_time for m in successful_queries) / len(successful_queries)
            avg_rows_returned = sum(m.rows_returned for m in successful_queries) / len(successful_queries)
        else:
            avg_execution_time = 0
            avg_rows_returned = 0
        
        slow_queries = [m for m in successful_queries if m.execution_time > self.slow_query_threshold]
        
        return {
            "status": "active",
            "total_queries": len(recent_metrics),
            "successful_queries": len(successful_queries),
            "failed_queries": len(failed_queries),
            "cached_queries": len(cached_queries),
            "cache_hit_rate": (len(cached_queries) / len(recent_metrics)) * 100 if recent_metrics else 0,
            "avg_execution_time": avg_execution_time,
            "avg_rows_returned": avg_rows_returned,
            "slow_queries": len(slow_queries),
            "slow_query_rate": (len(slow_queries) / len(successful_queries)) * 100 if successful_queries else 0,
            "cache_stats": self.cache.get_cache_stats()
        }
    
    def get_slow_queries(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent slow queries for analysis"""
        slow_queries = [
            {
                "query_hash": m.query_hash,
                "execution_time": m.execution_time,
                "rows_returned": m.rows_returned,
                "timestamp": m.timestamp.isoformat(),
                "cache_hit": m.cache_hit
            }
            for m in self.metrics 
            if m.execution_time > self.slow_query_threshold and m.error is None
        ]
        
        # Sort by execution time (slowest first) and limit
        slow_queries.sort(key=lambda x: x["execution_time"], reverse=True)
        return slow_queries[:limit]


# Global instance
query_optimizer = DatabaseQueryOptimizer()


# Convenience functions
async def optimize_query(query: str, params: Optional[Dict] = None) -> Tuple[Any, QueryMetrics]:
    """Optimize database query execution"""
    return await query_optimizer.optimize_query(query, params)


def get_query_performance_stats() -> Dict[str, Any]:
    """Get query performance statistics"""
    return query_optimizer.get_performance_summary()


def get_slow_queries(limit: int = 10) -> List[Dict[str, Any]]:
    """Get recent slow queries"""
    return query_optimizer.get_slow_queries(limit)
