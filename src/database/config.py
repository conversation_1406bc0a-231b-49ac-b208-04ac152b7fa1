"""
Database Configuration Module

Provides database-specific configuration for the application.
All database access is configured to use Supabase PostgreSQL.
"""

from typing import Dict, Any, Optional
from src.core.config_manager import get_config
from src.shared.error_handling.fallback import ConfigurationError


def get_database_config() -> Dict[str, Any]:
    """
    Get database configuration from centralized config manager
    
    Returns:
        Dictionary containing database configuration
        
    Raises:
        ConfigurationError: If required database configuration is missing
    """
    config = get_config()
    db_config = config.get_section('database')
    
    # Validate required configuration
    required_keys = ['url', 'supabase_url', 'supabase_key']
    missing = [key for key in required_keys if not db_config.get(key)]
    if missing:
        raise ConfigurationError(
            f"Missing required database configuration: {', '.join(missing)}. "
            "Please check your configuration files and environment variables."
        )
    
    return db_config


def get_database_url() -> str:
    """
    Get database URL from configuration
    
    Returns:
        str: Database connection URL
        
    Raises:
        ConfigurationError: If database URL is not configured
    """
    config = get_config()
    db_url = config.get('database', 'url', '')
    if not db_url:
        raise ConfigurationError(
            "Database URL is not configured. "
            "Please set DATABASE_URL in your environment."
        )
    return db_url


def use_supabase() -> bool:
    """
    Check if Supabase is configured to be used
    
    Returns:
        bool: Always returns True as we only support Supabase
    """
    return True


def get_pool_config() -> Dict[str, int]:
    """
    Get database connection pool configuration
    
    Returns:
        Dict[str, int]: Pool configuration with pool_size and max_overflow
    """
    config = get_config()
    return {
        'pool_size': config.get('database', 'pool_size', 5),
        'max_overflow': config.get('database', 'max_overflow', 10),
        'connect_timeout': config.get('database', 'connect_timeout', 10),
        'statement_timeout': config.get('database', 'statement_timeout', 30000)
    }


def get_config_summary() -> Dict[str, Any]:
    """
    Get a summary of the database configuration
    
    Returns:
        Dict[str, Any]: Database configuration summary with sensitive data redacted
    """
    config = get_config()
    db_config = config.get_section('database')
    
    # Create a safe copy of the config with sensitive data redacted
    safe_config = {
        'use_supabase': True,
        'pool_size': db_config.get('pool_size', 5),
        'max_overflow': db_config.get('max_overflow', 10),
        'connect_timeout': db_config.get('connect_timeout', 10),
        'statement_timeout': db_config.get('statement_timeout', 30000),
        'ssl_required': db_config.get('ssl_required', True),
        'url_configured': bool(db_config.get('url')),
        'supabase_url_configured': bool(db_config.get('supabase_url')),
        'supabase_key_configured': bool(db_config.get('supabase_key'))
    }
    
    return safe_config