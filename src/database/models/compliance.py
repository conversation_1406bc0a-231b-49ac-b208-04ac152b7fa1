"""
Compliance Database Models

Defines database models for compliance and audit tracking.
"""

from datetime import datetime
from typing import Optional, Dict, Any
from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, JSON
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()

class AuditEventModel(Base):
    """
    Database model for audit events and compliance tracking.
    """
    __tablename__ = 'audit_events'

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(String, nullable=False)
    event_type = Column(String, nullable=False)  # e.g., 'trade_recommendation', 'price_query'
    query = Column(Text, nullable=True)
    response = Column(Text, nullable=True)
    compliance_score = Column(Integer, nullable=True)  # 0-100
    risk_level = Column(String, nullable=True)  # 'low', 'medium', 'high'
    flagged = Column(Boolean, default=False)
    metadata = Column(JSON, nullable=True)  # Additional context
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def to_dict(self) -> Dict[str, Any]:
        """Convert model to dictionary for serialization."""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'event_type': self.event_type,
            'query': self.query,
            'response': self.response,
            'compliance_score': self.compliance_score,
            'risk_level': self.risk_level,
            'flagged': self.flagged,
            'metadata': self.metadata,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }