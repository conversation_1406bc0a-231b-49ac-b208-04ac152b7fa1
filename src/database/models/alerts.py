from sqlalchemy import <PERSON><PERSON><PERSON>, Integer, String, Float, DateTime, Boolean, Enum
from sqlalchemy.orm import Mapped, mapped_column
from datetime import datetime, timezone
import enum

from src.database.connection import Base
from src.core.utils import generate_unique_id

class AlertType(enum.Enum):
    """Enum for different types of market alerts."""
    PRICE_THRESHOLD = "price_threshold"
    TECHNICAL_INDICATOR = "technical_indicator"
    VOLUME_CHANGE = "volume_change"
    NEWS_SENTIMENT = "news_sentiment"

class MarketAlert(Base):
    """
    SQLAlchemy model for market alerts and notifications.
    
    Tracks user-defined alerts for specific market conditions.
    """
    __tablename__ = "market_alerts"

    # Primary key
    id: Mapped[str] = mapped_column(
        String, 
        primary_key=True, 
        default=generate_unique_id
    )
    
    # Alert details
    user_id: Mapped[str] = mapped_column(String, index=True)
    symbol: Mapped[str] = mapped_column(String(10), index=True)
    
    # Alert configuration
    alert_type: Mapped[AlertType] = mapped_column(Enum(AlertType))
    
    # Threshold conditions
    threshold_value: Mapped[float] = mapped_column(Float, nullable=True)
    comparison_type: Mapped[str] = mapped_column(String(10), nullable=True)  # 'above', 'below', 'cross'
    
    # Notification settings
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)
    discord_channel_id: Mapped[str] = mapped_column(String(50), nullable=True)
    
    # Tracking and timestamps
    triggered_count: Mapped[int] = mapped_column(Integer, default=0)
    last_triggered_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        nullable=True
    )
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        default=lambda: datetime.now(timezone.utc)
    )
    
    def __repr__(self):
        return f"<MarketAlert(symbol={self.symbol}, type={self.alert_type})>"

class AlertHistory(Base):
    """
    Tracks the history of triggered market alerts.
    """
    __tablename__ = "alert_history"
    
    id: Mapped[str] = mapped_column(
        String, 
        primary_key=True, 
        default=generate_unique_id
    )
    
    alert_id: Mapped[str] = mapped_column(String, index=True)
    user_id: Mapped[str] = mapped_column(String, index=True)
    symbol: Mapped[str] = mapped_column(String(10), index=True)
    
    # Trigger details
    current_value: Mapped[float] = mapped_column(Float)
    trigger_message: Mapped[str] = mapped_column(String(500))
    
    triggered_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        default=lambda: datetime.now(timezone.utc)
    )
    
    def __repr__(self):
        return f"<AlertHistory(symbol={self.symbol}, triggered_at={self.triggered_at})>" 