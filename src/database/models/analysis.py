from sqlalchemy import <PERSON><PERSON><PERSON>, Integer, String, Float, DateTime, JSON, Enum
from sqlalchemy.orm import Mapped, mapped_column
from datetime import datetime, timezone
import enum

from src.database.connection import Base
from src.core.utils import generate_unique_id

class AnalysisType(enum.Enum):
    """Enum for different types of market analysis."""
    TECHNICAL = "technical"
    FUNDAMENTAL = "fundamental"
    SENTIMENT = "sentiment"
    PREDICTIVE = "predictive"

class MarketAnalysis(Base):
    """
    SQLAlchemy model for storing market analysis results.
    
    Tracks detailed analysis for different financial instruments.
    """
    __tablename__ = "market_analysis"

    # Primary key
    id: Mapped[str] = mapped_column(
        String, 
        primary_key=True, 
        default=generate_unique_id
    )
    
    # Analysis details
    symbol: Mapped[str] = mapped_column(String(10), index=True)
    analysis_type: Mapped[AnalysisType] = mapped_column(Enum(AnalysisType))
    
    # Analysis results
    recommendation: Mapped[str] = mapped_column(String(20))  # 'BUY', 'SELL', 'HOLD'
    confidence_score: Mapped[float] = mapped_column(Float)
    
    # Detailed analysis data
    indicators: Mapped[dict] = mapped_column(JSON, nullable=True)
    summary: Mapped[str] = mapped_column(String(500), nullable=True)
    
    # Timestamps
    analyzed_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        default=lambda: datetime.now(timezone.utc)
    )
    
    def __repr__(self):
        return f"<MarketAnalysis(symbol={self.symbol}, type={self.analysis_type})>"

class AnalysisHistory(Base):
    """
    Tracks the history of market analyses for trend and performance tracking.
    """
    __tablename__ = "analysis_history"
    
    id: Mapped[str] = mapped_column(
        String, 
        primary_key=True, 
        default=generate_unique_id
    )
    
    symbol: Mapped[str] = mapped_column(String(10), index=True)
    analysis_id: Mapped[str] = mapped_column(String, index=True)
    
    # Performance tracking
    initial_price: Mapped[float] = mapped_column(Float)
    final_price: Mapped[float] = mapped_column(Float, nullable=True)
    price_change_percentage: Mapped[float] = mapped_column(Float, nullable=True)
    
    # Timestamps
    start_date: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        default=lambda: datetime.now(timezone.utc)
    )
    end_date: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        nullable=True
    )
    
    def __repr__(self):
        return f"<AnalysisHistory(symbol={self.symbol}, start_date={self.start_date})>" 