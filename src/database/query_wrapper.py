"""
Database Query Wrapper with Correlation ID Logging

Provides correlation ID logging for all database operations to enable request tracing.
"""

import logging
from typing import Any, Optional, Dict, List
from contextlib import asynccontextmanager
from src.shared.error_handling.logging import get_logger

logger = get_logger(__name__)


async def execute_query_with_correlation(
    query: str, 
    params: Optional[Dict[str, Any]] = None,
    correlation_id: Optional[str] = None,
    query_type: str = "unknown"
) -> Any:
    """
    Execute a database query with correlation ID logging.
    
    Args:
        query: SQL query string
        params: Query parameters
        correlation_id: Correlation ID for request tracing
        query_type: Type of query (e.g., 'select', 'insert', 'update', 'delete')
        
    Returns:
        Query result
    """
    if correlation_id:
        logger.set_correlation_id(correlation_id)
    
    logger.info(
        "Database query started", 
        extra={
            "query_type": query_type,
            "query_length": len(query),
            "has_params": bool(params),
            "correlation_id": correlation_id
        }
    )
    
    try:
        # Use the module-level get_db_connection (allows tests to patch src.database.query_wrapper.get_db_connection)
        async with get_db_connection() as conn:
            if params:
                result = await conn.execute(query, params)
            else:
                result = await conn.execute(query)
            
            logger.info(
                "Database query completed successfully",
                extra={
                    "query_type": query_type,
                    "rows_affected": getattr(result, 'rowcount', 0),
                    "correlation_id": correlation_id
                }
            )
            return result
            
    except Exception as e:
        logger.error(
            "Database query failed",
            extra={
                "query_type": query_type,
                "error": str(e),
                "error_type": type(e).__name__,
                "correlation_id": correlation_id
            },
            exc_info=True
        )
        raise


async def execute_many_with_correlation(
    query: str,
    params_list: List[Dict[str, Any]],
    correlation_id: Optional[str] = None,
    query_type: str = "batch"
) -> Any:
    """
    Execute multiple database queries with correlation ID logging.
    
    Args:
        query: SQL query string
        params_list: List of parameter dictionaries
        correlation_id: Correlation ID for request tracing
        query_type: Type of batch operation
        
    Returns:
        Batch operation result
    """
    if correlation_id:
        logger.set_correlation_id(correlation_id)
    
    logger.info(
        "Database batch operation started",
        extra={
            "query_type": query_type,
            "batch_size": len(params_list),
            "query_length": len(query),
            "correlation_id": correlation_id
        }
    )
    
    try:
        # Use the module-level get_db_connection (allows tests to patch src.database.query_wrapper.get_db_connection)
        async with get_db_connection() as conn:
            result = await conn.executemany(query, params_list)
            
            logger.info(
                "Database batch operation completed successfully",
                extra={
                    "query_type": query_type,
                    "rows_affected": getattr(result, 'rowcount', 0),
                    "batch_size": len(params_list),
                    "correlation_id": correlation_id
                }
            )
            return result
            
    except Exception as e:
        logger.error(
            "Database batch operation failed",
            extra={
                "query_type": query_type,
                "batch_size": len(params_list),
                "error": str(e),
                "error_type": type(e).__name__,
                "correlation_id": correlation_id
            },
            exc_info=True
        )
        raise


@asynccontextmanager
async def transaction_with_correlation(
    correlation_id: Optional[str] = None,
    transaction_name: str = "default"
):
    """
    Database transaction context manager with correlation ID logging.
    
    Args:
        correlation_id: Correlation ID for request tracing
        transaction_name: Name/description of the transaction
        
    Yields:
        Database connection with active transaction
    """
    if correlation_id:
        logger.set_correlation_id(correlation_id)
    
    logger.info(
        "Database transaction started",
        extra={
            "transaction_name": transaction_name,
            "correlation_id": correlation_id
        }
    )
    
    try:
        # Use the module-level get_db_connection (allows tests to patch src.database.query_wrapper.get_db_connection)
        async with get_db_connection() as conn:
            # Obtain the transaction context/manager from the connection. Different
            # database drivers or test mocks may return:
            # - an async context manager (has __aenter__)
            # - an awaitable that yields a context manager
            # - a synchronous context manager wrapped for async use
            tx_candidate = conn.transaction()

            # If the returned object is awaitable (a coroutine), await it to get
            # the actual context manager.
            try:
                import asyncio
                if asyncio.iscoroutine(tx_candidate) or hasattr(tx_candidate, "__await__"):
                    tx_obj = await tx_candidate
                else:
                    tx_obj = tx_candidate
            except Exception:
                # Fallback: use the original candidate
                tx_obj = tx_candidate

            # If the object supports async context management, use it directly.
            if hasattr(tx_obj, "__aenter__"):
                async with tx_obj:
                    yield conn
            else:
                # As a last resort, attempt to use it as an async manager by
                # awaiting its __aenter__/__aexit__ if present; otherwise yield
                # the connection and hope the mock/test handles commit/rollback.
                try:
                    enter = getattr(tx_obj, "__aenter__", None)
                    exit = getattr(tx_obj, "__aexit__", None)
                    if enter and exit:
                        await enter()
                        try:
                            yield conn
                        finally:
                            await exit(None, None, None)
                    else:
                        # No async enter/exit; yield connection without explicit tx
                        yield conn
                except Exception:
                    # If anything goes wrong, fallback to yielding conn
                    yield conn
                
        logger.info(
            "Database transaction committed successfully",
            extra={
                "transaction_name": transaction_name,
                "correlation_id": correlation_id
            }
        )
        
    except Exception as e:
        logger.error(
            "Database transaction failed/rolled back",
            extra={
                "transaction_name": transaction_name,
                "error": str(e),
                "error_type": type(e).__name__,
                "correlation_id": correlation_id
            },
            exc_info=True
        )
        raise 


def get_db_connection(*args, **kwargs):
    """Proxy accessor for `src.database.connection.get_db_connection`.

    This avoids a circular import at module import time while still
    exposing `get_db_connection` on this module so tests can patch
    `src.database.query_wrapper.get_db_connection`.
    """
    from .connection import get_db_connection as _get_db_connection
    return _get_db_connection(*args, **kwargs) 