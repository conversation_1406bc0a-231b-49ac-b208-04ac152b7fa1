"""Thin compatibility wrapper that exposes the shared aggregator.

This file previously contained a placeholder implementation. Replace it by
delegating to the centralized shared implementation so other modules that
import ``src.data_providers.aggregator`` keep working.
"""

from src.shared.data_providers.aggregator import DataProviderAggregator, data_provider_aggregator

# Re-export for backwards compatibility
__all__ = [
    "DataProviderAggregator",
    "data_provider_aggregator",
]