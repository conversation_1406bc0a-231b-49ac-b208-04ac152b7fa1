#!/usr/bin/env python3
"""
Trading MCP Server

Exposes our trading bot's capabilities as MCP tools for use by AI systems.
Based on the financial-datasets MCP server pattern.
"""

import asyncio
import json
import logging
from typing import Any, Dict, List, Optional, Union
from datetime import datetime

from mcp.server.models import InitializationOptions
from mcp.server import NotificationOptions, Server
from mcp.types import (
    Resource, Tool, TextContent, ImageContent, EmbeddedResource,
    LoggingLevel
)
import mcp.types as types

# Our existing capabilities
from src.data_providers.aggregator import DataProviderAggregator
from src.technical_analysis.signal_generator import SignalGenerator
from src.services.ai.enhanced_intent_detector import EnhancedIntentDetector
from src.data_providers.data_source_manager import DataSourceManager

logger = logging.getLogger(__name__)

class TradingMCPServer:
    """MCP Server exposing trading bot capabilities as tools"""
    
    def __init__(self):
        self.server = Server("trading-bot")
        self.data_manager = DataSourceManager()
        self.signal_generator = SignalGenerator()
        self.intent_detector = EnhancedIntentDetector()
        
        # Register our tools
        self._register_tools()
        
        logger.info("🚀 Trading MCP Server initialized")
    
    def _register_tools(self):
        """Register all available trading tools"""
        
        # Market Data Tools
        @self.server.list_tools()
        async def handle_list_tools() -> list[Tool]:
            """List all available trading tools"""
            return [
                Tool(
                    name="get_stock_price",
                    description="Get current stock price and basic metrics",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "symbol": {
                                "type": "string",
                                "description": "Stock symbol (e.g., AAPL, MSFT)"
                            },
                            "include_extended": {
                                "type": "boolean",
                                "description": "Include extended trading hours data",
                                "default": False
                            }
                        },
                        "required": ["symbol"]
                    }
                ),
                Tool(
                    name="get_technical_analysis",
                    description="Perform technical analysis on a stock",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "symbol": {
                                "type": "string",
                                "description": "Stock symbol to analyze"
                            },
                            "timeframe": {
                                "type": "string",
                                "enum": ["1d", "5d", "1mo", "3mo", "6mo", "1y"],
                                "description": "Analysis timeframe",
                                "default": "1mo"
                            },
                            "indicators": {
                                "type": "array",
                                "items": {
                                    "type": "string",
                                    "enum": ["rsi", "macd", "bollinger", "sma", "ema", "volume"]
                                },
                                "description": "Technical indicators to include",
                                "default": ["rsi", "macd", "sma"]
                            }
                        },
                        "required": ["symbol"]
                    }
                ),
                Tool(
                    name="analyze_market_sentiment",
                    description="Analyze overall market sentiment and trends",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "indices": {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "Market indices to analyze",
                                "default": ["SPY", "QQQ", "IWM"]
                            },
                            "include_vix": {
                                "type": "boolean",
                                "description": "Include VIX volatility analysis",
                                "default": True
                            }
                        }
                    }
                ),
                Tool(
                    name="detect_trading_intent",
                    description="Analyze user query to detect trading intent and extract entities",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "query": {
                                "type": "string",
                                "description": "User's trading-related query"
                            },
                            "context": {
                                "type": "object",
                                "description": "Additional context for intent detection",
                                "default": {}
                            }
                        },
                        "required": ["query"]
                    }
                ),
                Tool(
                    name="get_options_data",
                    description="Get options chain data and analysis",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "symbol": {
                                "type": "string",
                                "description": "Underlying stock symbol"
                            },
                            "expiration": {
                                "type": "string",
                                "description": "Options expiration date (YYYY-MM-DD)",
                                "default": "next_friday"
                            },
                            "option_type": {
                                "type": "string",
                                "enum": ["calls", "puts", "both"],
                                "description": "Type of options to retrieve",
                                "default": "both"
                            }
                        },
                        "required": ["symbol"]
                    }
                ),
                Tool(
                    name="calculate_risk_metrics",
                    description="Calculate risk metrics for a position or portfolio",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "positions": {
                                "type": "array",
                                "items": {
                                    "type": "object",
                                    "properties": {
                                        "symbol": {"type": "string"},
                                        "quantity": {"type": "number"},
                                        "entry_price": {"type": "number"}
                                    }
                                },
                                "description": "Portfolio positions"
                            },
                            "portfolio_value": {
                                "type": "number",
                                "description": "Total portfolio value"
                            },
                            "risk_free_rate": {
                                "type": "number",
                                "description": "Risk-free rate for calculations",
                                "default": 0.05
                            }
                        },
                        "required": ["positions", "portfolio_value"]
                    }
                )
            ]
        
        # Tool execution handlers
        @self.server.call_tool()
        async def handle_call_tool(name: str, arguments: dict) -> list[types.TextContent]:
            """Handle tool execution requests"""
            
            try:
                if name == "get_stock_price":
                    result = await self._get_stock_price(
                        arguments["symbol"],
                        arguments.get("include_extended", False)
                    )
                
                elif name == "get_technical_analysis":
                    result = await self._get_technical_analysis(
                        arguments["symbol"],
                        arguments.get("timeframe", "1mo"),
                        arguments.get("indicators", ["rsi", "macd", "sma"])
                    )
                
                elif name == "analyze_market_sentiment":
                    result = await self._analyze_market_sentiment(
                        arguments.get("indices", ["SPY", "QQQ", "IWM"]),
                        arguments.get("include_vix", True)
                    )
                
                elif name == "detect_trading_intent":
                    result = await self._detect_trading_intent(
                        arguments["query"],
                        arguments.get("context", {})
                    )
                
                elif name == "get_options_data":
                    result = await self._get_options_data(
                        arguments["symbol"],
                        arguments.get("expiration", "next_friday"),
                        arguments.get("option_type", "both")
                    )
                
                elif name == "calculate_risk_metrics":
                    result = await self._calculate_risk_metrics(
                        arguments["positions"],
                        arguments["portfolio_value"],
                        arguments.get("risk_free_rate", 0.05)
                    )
                
                else:
                    raise ValueError(f"Unknown tool: {name}")
                
                return [types.TextContent(
                    type="text",
                    text=json.dumps(result, indent=2, default=str)
                )]
                
            except Exception as e:
                logger.error(f"❌ Error executing tool {name}: {e}")
                return [types.TextContent(
                    type="text", 
                    text=json.dumps({
                        "error": str(e),
                        "tool": name,
                        "timestamp": datetime.now().isoformat()
                    }, indent=2)
                )]
    
    async def _get_stock_price(self, symbol: str, include_extended: bool = False) -> Dict[str, Any]:
        """Get current stock price and metrics"""
        try:
            # Use the correct method name from DataSourceManager
            response = await self.data_manager.get_market_data(symbol)
            data = response.get("data", {}) if response else {}

            result = {
                "symbol": symbol,
                "price": data.get("current_price") or data.get("price"),
                "change": data.get("price_change") or data.get("change"),
                "change_percent": data.get("price_change_percent") or data.get("change_percent"),
                "volume": data.get("volume"),
                "market_cap": data.get("market_cap"),
                "timestamp": datetime.now().isoformat(),
                "provider": response.get("provider") if response else None,
                "status": response.get("status") if response else "unknown"
            }

            if include_extended:
                result["extended_hours"] = data.get("extended_hours_data", {})

            return result

        except Exception as e:
            return {"error": f"Failed to get price for {symbol}: {e}"}
    
    async def _get_technical_analysis(self, symbol: str, timeframe: str, indicators: List[str]) -> Dict[str, Any]:
        """Perform technical analysis"""
        try:
            # Get market data first (since we don't have historical data method)
            response = await self.data_manager.get_market_data(symbol)
            data = response.get("data", {}) if response else {}

            # Mock technical analysis (replace with real implementation when available)
            mock_signals = {
                "rsi": {"value": 65.5, "signal": "neutral", "description": "RSI indicates neutral momentum"},
                "macd": {"value": 0.25, "signal": "bullish", "description": "MACD shows bullish crossover"},
                "sma": {"value": data.get("price", 100), "signal": "bullish", "description": "Price above SMA"}
            }

            # Filter to requested indicators
            filtered_signals = {ind: mock_signals.get(ind, {"value": 0, "signal": "unknown"})
                              for ind in indicators if ind in mock_signals}

            return {
                "symbol": symbol,
                "timeframe": timeframe,
                "indicators": indicators,
                "signals": filtered_signals,
                "current_price": data.get("price") or data.get("current_price"),
                "analysis_timestamp": datetime.now().isoformat(),
                "provider": response.get("provider") if response else None
            }

        except Exception as e:
            return {"error": f"Technical analysis failed for {symbol}: {e}"}
    
    async def _analyze_market_sentiment(self, indices: List[str], include_vix: bool) -> Dict[str, Any]:
        """Analyze market sentiment"""
        try:
            sentiment_data = {}

            for index in indices:
                response = await self.data_manager.get_market_data(index)
                data = response.get("data", {}) if response else {}

                price_change = data.get("price_change_percent") or data.get("change_percent", 0)
                sentiment_data[index] = {
                    "price_change_percent": price_change,
                    "volume_ratio": data.get("volume_ratio", 1.0),
                    "trend": "bullish" if price_change > 0 else "bearish",
                    "current_price": data.get("price") or data.get("current_price")
                }

            if include_vix:
                vix_response = await self.data_manager.get_market_data("VIX")
                vix_data = vix_response.get("data", {}) if vix_response else {}
                vix_level = vix_data.get("current_price") or vix_data.get("price")

                # Handle case where VIX data is not available
                if vix_level is None:
                    vix_level = 20  # Default VIX level

                sentiment_data["VIX"] = {
                    "level": vix_level,
                    "fear_greed": "fear" if vix_level > 25 else "greed"
                }

            return {
                "market_sentiment": sentiment_data,
                "overall_sentiment": self._calculate_overall_sentiment(sentiment_data),
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            return {"error": f"Market sentiment analysis failed: {e}"}
    
    async def _detect_trading_intent(self, query: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Detect trading intent from user query"""
        try:
            # Use the correct method name from EnhancedIntentDetector
            result = await self.intent_detector.analyze_intent(query)

            return {
                "query": query,
                "intent": result.primary_intent.value if hasattr(result, 'primary_intent') else "unknown",
                "confidence": result.confidence,
                "entities": result.entities or {},
                "urgency": result.urgency_level.value if hasattr(result, 'urgency_level') else "normal",
                "response_style": result.response_style.value if hasattr(result, 'response_style') else "informative",
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            return {"error": f"Intent detection failed: {e}"}
    
    async def _get_options_data(self, symbol: str, expiration: str, option_type: str) -> Dict[str, Any]:
        """Get options data and analysis"""
        try:
            # Get underlying stock data
            response = await self.data_manager.get_market_data(symbol)
            stock_data = response.get("data", {}) if response else {}
            current_price = stock_data.get("current_price") or stock_data.get("price", 100)

            # Mock options data (replace with real options API when available)
            options_data = {
                "underlying_symbol": symbol,
                "underlying_price": current_price,
                "expiration": expiration,
                "option_type": option_type,
                "implied_volatility": 0.25,  # Mock IV
                "options_chain": {
                    "calls": [
                        {"strike": current_price + 5, "bid": 2.50, "ask": 2.75, "volume": 150, "open_interest": 500},
                        {"strike": current_price + 10, "bid": 1.25, "ask": 1.50, "volume": 200, "open_interest": 750}
                    ] if option_type in ["calls", "both"] else [],
                    "puts": [
                        {"strike": current_price - 5, "bid": 1.75, "ask": 2.00, "volume": 100, "open_interest": 300},
                        {"strike": current_price - 10, "bid": 3.25, "ask": 3.50, "volume": 75, "open_interest": 400}
                    ] if option_type in ["puts", "both"] else []
                },
                "greeks": {
                    "delta": 0.65,
                    "gamma": 0.03,
                    "theta": -0.05,
                    "vega": 0.15
                },
                "timestamp": datetime.now().isoformat()
            }

            return options_data

        except Exception as e:
            return {"error": f"Options data retrieval failed for {symbol}: {e}"}
    
    async def _calculate_risk_metrics(self, positions: List[Dict[str, Any]], portfolio_value: float, risk_free_rate: float) -> Dict[str, Any]:
        """Calculate risk metrics for a position or portfolio"""
        try:
            total_value = 0
            total_risk = 0
            position_risks = []

            for position in positions:
                symbol = position["symbol"]
                quantity = position["quantity"]
                entry_price = position["entry_price"]

                # Get current price
                response = await self.data_manager.get_market_data(symbol)
                stock_data = response.get("data", {}) if response else {}
                current_price = stock_data.get("current_price") or stock_data.get("price", entry_price)

                # Calculate position metrics
                position_value = quantity * current_price
                unrealized_pnl = quantity * (current_price - entry_price)
                position_weight = position_value / portfolio_value

                # Mock volatility calculation (replace with real historical data)
                volatility = 0.25  # 25% annual volatility
                position_risk = position_value * volatility

                position_risks.append({
                    "symbol": symbol,
                    "position_value": position_value,
                    "unrealized_pnl": unrealized_pnl,
                    "weight": position_weight,
                    "volatility": volatility,
                    "var_95": position_value * 0.05,  # 5% VaR
                    "beta": 1.0  # Mock beta
                })

                total_value += position_value
                total_risk += position_risk

            # Portfolio-level metrics
            portfolio_volatility = total_risk / portfolio_value
            sharpe_ratio = (0.10 - risk_free_rate) / portfolio_volatility  # Assume 10% return
            max_drawdown = 0.15  # Mock 15% max drawdown

            return {
                "portfolio_value": portfolio_value,
                "total_positions_value": total_value,
                "portfolio_volatility": portfolio_volatility,
                "sharpe_ratio": sharpe_ratio,
                "max_drawdown": max_drawdown,
                "var_95": portfolio_value * 0.05,  # 5% portfolio VaR
                "risk_free_rate": risk_free_rate,
                "position_risks": position_risks,
                "diversification_ratio": len(positions) / 10,  # Simple diversification metric
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            return {"error": f"Risk metrics calculation failed: {e}"}
    
    def _calculate_overall_sentiment(self, sentiment_data: Dict[str, Any]) -> str:
        """Calculate overall market sentiment"""
        positive_count = sum(1 for data in sentiment_data.values() 
                           if isinstance(data, dict) and data.get("trend") == "bullish")
        total_count = len([data for data in sentiment_data.values() if isinstance(data, dict)])
        
        if total_count == 0:
            return "neutral"
            
        if positive_count / total_count > 0.6:
            return "bullish"
        elif positive_count / total_count < 0.4:
            return "bearish"
        else:
            return "neutral"
    
    async def run(self):
        """Run the MCP server"""
        from mcp.server.stdio import stdio_server
        
        async with stdio_server() as (read_stream, write_stream):
            await self.server.run(
                read_stream,
                write_stream,
                InitializationOptions(
                    server_name="trading-bot",
                    server_version="1.0.0",
                    capabilities=self.server.get_capabilities(
                        notification_options=NotificationOptions(),
                        experimental_capabilities={}
                    )
                )
            )

async def main():
    """Main entry point"""
    server = TradingMCPServer()
    await server.run()

if __name__ == "__main__":
    asyncio.run(main())