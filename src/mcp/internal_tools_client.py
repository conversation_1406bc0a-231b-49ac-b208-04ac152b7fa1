"""
Internal Tools MCP Client Wrapper
A simple wrapper to manage the lifecycle of the Internal Tools MCP Server.
"""
import asyncio
import logging
import subprocess
import sys
import json
from typing import Dict, Any, Optional, List

from src.mcp.mcp_models import MCPToolResult

logger = logging.getLogger(__name__)

class InternalToolsClient:
    """Client wrapper for the Internal Tools MCP Server."""
    
    def __init__(self):
        self.server_process = None
        self.available_tools = []
        logger.info("🔧 Internal Tools MCP Client initialized")

    async def start_server(self) -> bool:
        """Start the Internal Tools MCP server process."""
        try:
            # Start our MCP server as a subprocess
            self.server_process = await asyncio.create_subprocess_exec(
                sys.executable, "-m", "src.mcp.internal_tools_mcp_server",
                stdin=asyncio.subprocess.PIPE,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd="."
            )
            
            # Initialize connection and get available tools
            await self._initialize_connection()
            
            logger.info(f"✅ Internal Tools MCP server started with {len(self.available_tools)} tools")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to start Internal Tools MCP server: {e}")
            return False

    async def _initialize_connection(self):
        """Initialize MCP connection and discover tools"""
        try:
            # Send initialization request
            init_request = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "initialize",
                "params": {
                    "protocolVersion": "2024-11-05",
                    "capabilities": {
                        "tools": {}
                    },
                    "clientInfo": {
                        "name": "internal-tools-client",
                        "version": "1.0.0"
                    }
                }
            }
            
            await self._send_request(init_request)
            
            # List available tools
            tools_request = {
                "jsonrpc": "2.0",
                "id": 2,
                "method": "tools/list",
                "params": {}
            }
            
            response = await self._send_request(tools_request)
            if response and "result" in response:
                self.available_tools = response["result"].get("tools", [])
            
        except Exception as e:
            logger.error(f"❌ Internal Tools MCP connection initialization failed: {e}")

    async def _send_request(self, request: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Send request to MCP server"""
        if not self.server_process:
            return None
        
        try:
            # Send request
            request_json = json.dumps(request) + "\n"
            self.server_process.stdin.write(request_json.encode())
            await self.server_process.stdin.drain()
            
            # Read response
            response_line = await self.server_process.stdout.readline()
            if response_line:
                return json.loads(response_line.decode().strip())
            
        except Exception as e:
            logger.error(f"❌ Internal Tools MCP request failed: {e}")
        
        return None

    async def execute_tool(self, tool_name: str, arguments: Dict[str, Any]) -> MCPToolResult:
        """Execute an internal tool via MCP."""
        start_time = asyncio.get_event_loop().time()
        
        try:
            # Prepare tool call request
            tool_request = {
                "jsonrpc": "2.0",
                "id": 3,
                "method": "tools/call",
                "params": {
                    "name": tool_name,
                    "arguments": arguments
                }
            }
            
            # Send request and get response
            response = await self._send_request(tool_request)
            execution_time = asyncio.get_event_loop().time() - start_time
            
            if response and "result" in response:
                # Parse tool result
                content = response["result"].get("content", [])
                if content and len(content) > 0:
                    result_text = content[0].get("text", "{}")
                    result_data = json.loads(result_text)
                    
                    return MCPToolResult(
                        success=True,
                        data=result_data,
                        tool_name=tool_name,
                        execution_time=execution_time
                    )
            
            return MCPToolResult(
                success=False,
                data={},
                tool_name=tool_name,
                execution_time=execution_time,
                error="Invalid response from Internal Tools MCP server"
            )
            
        except Exception as e:
            execution_time = asyncio.get_event_loop().time() - start_time
            logger.error(f"❌ Internal Tools MCP tool execution failed: {e}")
            
            return MCPToolResult(
                success=False,
                data={},
                tool_name=tool_name,
                execution_time=execution_time,
                error=str(e)
            )

    def get_available_tools(self) -> List[str]:
        """Get list of available tool names"""
        return [tool.get("name", "") for tool in self.available_tools]

    async def close(self):
        """Close the MCP server connection."""
        if self.server_process:
            try:
                self.server_process.terminate()
                await self.server_process.wait()
                logger.info("✅ Internal Tools MCP server closed")
            except Exception as e:
                logger.error(f"❌ Error closing Internal Tools MCP server: {e}")

# Global client instance
_internal_client = None

async def get_internal_mcp_client() -> InternalToolsClient:
    """Get or create the global Internal Tools MCP client"""
    global _internal_client
    
    if _internal_client is None:
        _internal_client = InternalToolsClient()
        await _internal_client.start_server()
    
    return _internal_client

async def close_internal_mcp_client():
    """Close the global Internal Tools MCP client"""
    global _internal_client
    
    if _internal_client:
        await _internal_client.close()
        _internal_client = None