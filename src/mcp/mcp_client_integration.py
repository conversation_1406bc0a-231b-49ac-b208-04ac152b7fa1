"""
MCP Client Integration for ASK Pipeline

Integrates our MCP server tools into the ASK pipeline for enhanced capabilities.
"""

import asyncio
import json
import subprocess
import sys
import logging
from typing import Dict, List, Any, Optional

from src.mcp.mcp_models import MCPToolResult

logger = logging.getLogger(__name__)

class TradingMCPClient:
    """Client for our trading MCP server"""
    
    def __init__(self):
        self.server_process = None
        self.available_tools = []
        logger.info("🔧 Trading MCP Client initialized")
    
    async def start_server(self) -> bool:
        """Start the MCP server process"""
        try:
            # Start our MCP server as a subprocess
            self.server_process = await asyncio.create_subprocess_exec(
                sys.executable, "-m", "src.mcp.trading_mcp_server",
                stdin=asyncio.subprocess.PIPE,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd="."
            )
            
            # Initialize connection and get available tools
            await self._initialize_connection()
            
            logger.info(f"✅ MCP server started with {len(self.available_tools)} tools")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to start MCP server: {e}")
            return False
    
    async def _initialize_connection(self):
        """Initialize MCP connection and discover tools"""
        try:
            # Send initialization request
            init_request = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "initialize",
                "params": {
                    "protocolVersion": "2024-11-05",
                    "capabilities": {
                        "tools": {}
                    },
                    "clientInfo": {
                        "name": "trading-bot-client",
                        "version": "1.0.0"
                    }
                }
            }
            
            await self._send_request(init_request)
            
            # List available tools
            tools_request = {
                "jsonrpc": "2.0",
                "id": 2,
                "method": "tools/list",
                "params": {}
            }
            
            response = await self._send_request(tools_request)
            if response and "result" in response:
                self.available_tools = response["result"].get("tools", [])
            
        except Exception as e:
            logger.error(f"❌ MCP connection initialization failed: {e}")
    
    async def _send_request(self, request: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Send request to MCP server"""
        if not self.server_process:
            return None
        
        try:
            # Send request
            request_json = json.dumps(request) + "\n"
            self.server_process.stdin.write(request_json.encode())
            await self.server_process.stdin.drain()
            
            # Read response
            response_line = await self.server_process.stdout.readline()
            if response_line:
                return json.loads(response_line.decode().strip())
            
        except Exception as e:
            logger.error(f"❌ MCP request failed: {e}")
        
        return None
    
    async def execute_tool(self, tool_name: str, arguments: Dict[str, Any]) -> MCPToolResult:
        """Execute an MCP tool"""
        start_time = asyncio.get_event_loop().time()
        
        try:
            # Prepare tool call request
            tool_request = {
                "jsonrpc": "2.0",
                "id": 3,
                "method": "tools/call",
                "params": {
                    "name": tool_name,
                    "arguments": arguments
                }
            }
            
            # Send request and get response
            response = await self._send_request(tool_request)
            execution_time = asyncio.get_event_loop().time() - start_time
            
            if response and "result" in response:
                # Parse tool result
                content = response["result"].get("content", [])
                if content and len(content) > 0:
                    result_text = content[0].get("text", "{}")
                    result_data = json.loads(result_text)
                    
                    return MCPToolResult(
                        success=True,
                        data=result_data,
                        tool_name=tool_name,
                        execution_time=execution_time
                    )
            
            return MCPToolResult(
                success=False,
                data={},
                tool_name=tool_name,
                execution_time=execution_time,
                error="Invalid response from MCP server"
            )
            
        except Exception as e:
            execution_time = asyncio.get_event_loop().time() - start_time
            logger.error(f"❌ MCP tool execution failed: {e}")
            
            return MCPToolResult(
                success=False,
                data={},
                tool_name=tool_name,
                execution_time=execution_time,
                error=str(e)
            )
    
    async def get_stock_price(self, symbol: str, include_extended: bool = False) -> MCPToolResult:
        """Get stock price using MCP tool"""
        return await self.execute_tool("get_stock_price", {
            "symbol": symbol,
            "include_extended": include_extended
        })
    
    async def get_technical_analysis(self, symbol: str, timeframe: str = "1mo", indicators: List[str] = None) -> MCPToolResult:
        """Get technical analysis using MCP tool"""
        if indicators is None:
            indicators = ["rsi", "macd", "sma"]
        
        return await self.execute_tool("get_technical_analysis", {
            "symbol": symbol,
            "timeframe": timeframe,
            "indicators": indicators
        })
    
    async def analyze_market_sentiment(self, indices: List[str] = None, include_vix: bool = True) -> MCPToolResult:
        """Analyze market sentiment using MCP tool"""
        if indices is None:
            indices = ["SPY", "QQQ", "IWM"]
        
        return await self.execute_tool("analyze_market_sentiment", {
            "indices": indices,
            "include_vix": include_vix
        })
    
    async def detect_trading_intent(self, query: str, context: Dict[str, Any] = None) -> MCPToolResult:
        """Detect trading intent using MCP tool"""
        if context is None:
            context = {}
        
        return await self.execute_tool("detect_trading_intent", {
            "query": query,
            "context": context
        })
    
    async def get_options_data(self, symbol: str, expiration: str = "next_friday", option_type: str = "both") -> MCPToolResult:
        """Get options data using MCP tool"""
        return await self.execute_tool("get_options_data", {
            "symbol": symbol,
            "expiration": expiration,
            "option_type": option_type
        })
    
    async def calculate_risk_metrics(self, positions: List[Dict[str, Any]], portfolio_value: float, risk_free_rate: float = 0.05) -> MCPToolResult:
        """Calculate risk metrics using MCP tool"""
        return await self.execute_tool("calculate_risk_metrics", {
            "positions": positions,
            "portfolio_value": portfolio_value,
            "risk_free_rate": risk_free_rate
        })
    
    def get_available_tools(self) -> List[str]:
        """Get list of available tool names"""
        return [tool.get("name", "") for tool in self.available_tools]
    
    async def close(self):
        """Close the MCP server connection"""
        if self.server_process:
            try:
                self.server_process.terminate()
                await self.server_process.wait()
                logger.info("✅ MCP server closed")
            except Exception as e:
                logger.error(f"❌ Error closing MCP server: {e}")

# Global MCP client instance
_mcp_client = None

async def get_mcp_client() -> TradingMCPClient:
    """Get or create the global MCP client"""
    global _mcp_client
    
    if _mcp_client is None:
        _mcp_client = TradingMCPClient()
        await _mcp_client.start_server()
    
    return _mcp_client

async def close_mcp_client():
    """Close the global MCP client"""
    global _mcp_client
    
    if _mcp_client:
        await _mcp_client.close()
        _mcp_client = None