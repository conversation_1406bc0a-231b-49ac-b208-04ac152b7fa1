#!/usr/bin/env python3
"""
Internal Tools MCP Server
Exposes our trading bot's internal capabilities as MCP tools for external use.

This server makes our core analysis engines, data providers, and processing
components available to other AI agents through the MCP protocol.
"""

import asyncio
import json
import logging
from typing import Any, Dict, List, Optional, Union
from datetime import datetime

from mcp.server.models import InitializationOptions
from mcp.server import NotificationOptions, Server
from mcp.types import (
    Resource, Tool, TextContent, ImageContent, EmbeddedResource,
    LoggingLevel
)
import mcp.types as types

# Import our internal systems (Updated paths based on consolidation plan)
from src.services.ai.enhanced_intent_detector import enhanced_intent_detector
from src.services.ai.response_synthesizer import ResponseSynthesizer
from src.technical_analysis.unified_analyzer import unified_analyzer
from src.data_providers.aggregator import DataProviderAggregator
from src.data_providers.data_source_manager import DataSourceManager
from src.bot.formatting.discord_formatter import DiscordFormatter

from src.core.formatting.text_formatting import format_currency, format_percentage

logger = logging.getLogger(__name__)

class InternalToolsMCPServer:
    """MCP Server exposing our internal trading bot capabilities"""
    
    def __init__(self):
        self.server = Server("internal-trading-tools")
        
        # Initialize our internal systems
        self.data_manager = DataSourceManager()
        self.response_synthesizer = ResponseSynthesizer()
        self.formatter = DiscordFormatter()
        
        
        # Register our tools
        self._register_tools()
        
        logger.info("🚀 Internal Tools MCP Server initialized")
    
    def _register_tool_definitions(self):
        """Register tool definitions with the MCP server"""

        # Intent Detection Tool
        @self.server.list_tools()
        async def handle_list_tools() -> List[Tool]:
            return [
                Tool(
                    name="analyze_trading_intent",
                    description="Analyze trading query intent using enhanced AI detection",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "query": {
                                "type": "string",
                                "description": "Trading query to analyze"
                            },
                            "use_ai": {
                                "type": "boolean",
                                "description": "Whether to use AI analysis (default: true)",
                                "default": True
                            }
                        },
                        "required": ["query"]
                    }
                ),
                Tool(
                    name="get_market_data",
                    description="Get comprehensive market data using our data aggregator",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "symbol": {
                                "type": "string",
                                "description": "Stock symbol (e.g., AAPL, TSLA)"
                            },
                            "data_type": {
                                "type": "string",
                                "description": "Type of data: quote or historical",
                                "enum": ["quote", "historical"],
                                "default": "quote"
                            },
                            "timeframe": {
                                "type": "string",
                                "description": "Timeframe for historical data",
                                "default": "1d"
                            },
                            "period": {
                                "type": "string",
                                "description": "Period for historical data",
                                "default": "1mo"
                            }
                        },
                        "required": ["symbol"]
                    }
                ),
                Tool(
                    name="calculate_technical_indicators",
                    description="Calculate technical indicators using our unified analyzer",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "symbol": {
                                "type": "string",
                                "description": "Stock symbol"
                            },
                            "indicators": {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "List of indicators to calculate",
                                "default": ["rsi", "macd", "sma"]
                            },
                            "period": {
                                "type": "string",
                                "description": "Data period",
                                "default": "1mo"
                            }
                        },
                        "required": ["symbol"]
                    }
                ),
                Tool(
                    name="synthesize_trading_response",
                    description="Generate trading response using our response synthesizer",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "query": {
                                "type": "string",
                                "description": "Original query"
                            },
                            "query_type": {
                                "type": "string",
                                "description": "Type of query",
                                "default": "general"
                            },
                            "analysis_data": {
                                "type": "object",
                                "description": "Analysis data to include"
                            },
                            "symbols": {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "Symbols analyzed"
                            },
                            "style": {
                                "type": "string",
                                "description": "Response style",
                                "enum": ["professional", "conversational"],
                                "default": "professional"
                            }
                        },
                        "required": ["query"]
                    }
                ),
                Tool(
                    name="format_for_discord",
                    description="Format response for Discord using our formatter",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "content": {
                                "type": "string",
                                "description": "Content to format"
                            },
                            "title": {
                                "type": "string",
                                "description": "Embed title",
                                "default": "Trading Analysis"
                            },
                            "color": {
                                "type": "string",
                                "description": "Embed color",
                                "default": "blue"
                            }
                        },
                        "required": ["content"]
                    }
                ),

                Tool(
                    name="format_financial_data",
                    description="Format financial data using our formatting utilities",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "data_type": {
                                "type": "string",
                                "description": "Type of data to format",
                                "enum": ["currency", "percentage"],
                                "default": "currency"
                            },
                            "value": {
                                "type": "number",
                                "description": "Value to format"
                            }
                        },
                        "required": ["value"]
                    }
                )
            ]

    def _register_tools(self):
        """Register all available internal tools"""

        # Register tool definitions first
        self._register_tool_definitions()

        # 1. Intent Detection Tools
        @self.server.call_tool()
        async def analyze_trading_intent(arguments: dict) -> List[types.TextContent]:
            """Analyze trading query intent using our enhanced AI detector"""
            try:
                query = arguments.get("query", "")
                use_ai = arguments.get("use_ai", True)
                
                result = await enhanced_intent_detector.analyze_intent(query, use_ai=use_ai)
                
                return [types.TextContent(
                    type="text",
                    text=json.dumps({
                        "primary_intent": result.primary_intent.value,
                        "confidence": result.confidence,
                        "entities": result.entities,
                        "urgency_level": result.urgency_level,
                        "response_style": result.response_style,
                        "reasoning": result.reasoning
                    }, indent=2)
                )]
                
            except Exception as e:
                logger.error(f"❌ Error in analyze_trading_intent: {e}")
                return [types.TextContent(
                    type="text",
                    text=json.dumps({
                        "error": str(e),
                        "tool": "analyze_trading_intent"
                    }, indent=2)
                )]
        
        # 2. Market Data Tools
        @self.server.call_tool()
        async def get_market_data(arguments: dict) -> List[types.TextContent]:
            """Get comprehensive market data using our data aggregator"""
            try:
                symbol = arguments.get("symbol", "")
                data_type = arguments.get("data_type", "quote")
                
                if data_type == "quote":
                    result = await self.data_manager.get_market_data(symbol)
                elif data_type == "historical":
                    timeframe = arguments.get("timeframe", "1d")
                    period = arguments.get("period", "1mo")
                    result = await self.data_manager.get_historical_data(symbol, timeframe, period)
                else:
                    raise ValueError(f"Unsupported data_type: {data_type}")
                
                return [types.TextContent(
                    type="text",
                    text=json.dumps(result, indent=2, default=str)
                )]
                
            except Exception as e:
                logger.error(f"❌ Error in get_market_data: {e}")
                return [types.TextContent(
                    type="text",
                    text=json.dumps({
                        "error": str(e),
                        "tool": "get_market_data",
                        "symbol": arguments.get("symbol", "unknown")
                    }, indent=2)
                )]
        
        # 3. Technical Analysis Tools
        @self.server.call_tool()
        async def calculate_technical_indicators(arguments: dict) -> List[types.TextContent]:
            """Calculate technical indicators using our unified analyzer"""
            try:
                symbol = arguments.get("symbol", "")
                indicators = arguments.get("indicators", ["rsi", "macd", "sma"])
                period = arguments.get("period", "1mo")
                
                # Get historical data first
                data_result = await self.data_manager.get_historical_data(symbol, "1d", period)
                
                if not data_result.get("success"):
                    raise ValueError(f"Failed to get data for {symbol}")
                
                # Calculate indicators
                historical_data = data_result.get("data", {})
                analysis_result = await unified_analyzer.calculate_indicators(
                    historical_data, indicators
                )
                
                return [types.TextContent(
                    type="text",
                    text=json.dumps({
                        "symbol": symbol,
                        "indicators": analysis_result,
                        "timestamp": datetime.now().isoformat()
                    }, indent=2, default=str)
                )]
                
            except Exception as e:
                logger.error(f"❌ Error in calculate_technical_indicators: {e}")
                return [types.TextContent(
                    type="text",
                    text=json.dumps({
                        "error": str(e),
                        "tool": "calculate_technical_indicators",
                        "symbol": arguments.get("symbol", "unknown")
                    }, indent=2)
                )]
        
        # 4. Response Generation Tools
        @self.server.call_tool()
        async def synthesize_trading_response(arguments: dict) -> List[types.TextContent]:
            """Generate trading response using our response synthesizer"""
            try:
                query = arguments.get("query", "")
                query_type = arguments.get("query_type", "general")
                analysis_data = arguments.get("analysis_data", {})
                symbols = arguments.get("symbols", [])
                style = arguments.get("style", "professional")
                
                if style == "conversational":
                    response = self.response_synthesizer.create_conversational_response(
                        query, analysis_data, symbols
                    )
                else:
                    response = self.response_synthesizer.synthesize_response(
                        query, query_type, "medium", analysis_data, symbols
                    )
                
                return [types.TextContent(
                    type="text",
                    text=json.dumps({
                        "response": response,
                        "style": style,
                        "timestamp": datetime.now().isoformat()
                    }, indent=2)
                )]
                
            except Exception as e:
                logger.error(f"❌ Error in synthesize_trading_response: {e}")
                return [types.TextContent(
                    type="text",
                    text=json.dumps({
                        "error": str(e),
                        "tool": "synthesize_trading_response"
                    }, indent=2)
                )]
        
        # 5. Discord Formatting Tools
        @self.server.call_tool()
        async def format_for_discord(arguments: dict) -> List[types.TextContent]:
            """Format response for Discord using our formatter"""
            try:
                content = arguments.get("content", "")
                title = arguments.get("title", "Trading Analysis")
                color = arguments.get("color", "blue")
                
                formatted_response = await self.formatter.format_response(
                    content, title, color
                )
                
                return [types.TextContent(
                    type="text",
                    text=json.dumps({
                        "embed": formatted_response.embed_data,
                        "content": formatted_response.content,
                        "timestamp": datetime.now().isoformat()
                    }, indent=2, default=str)
                )]
                
            except Exception as e:
                logger.error(f"❌ Error in format_for_discord: {e}")
                return [types.TextContent(
                    type="text",
                    text=json.dumps({
                        "error": str(e),
                        "tool": "format_for_discord"
                    }, indent=2)
                )]
        
        
        # 7. Formatting Utilities
        @self.server.call_tool()
        async def format_financial_data(arguments: dict) -> List[types.TextContent]:
            """Format financial data using our formatting utilities"""
            try:
                data_type = arguments.get("data_type", "currency")
                value = arguments.get("value", 0)
                
                if data_type == "currency":
                    formatted = format_currency(value)
                elif data_type == "percentage":
                    formatted = format_percentage(value)
                else:
                    formatted = str(value)
                
                return [types.TextContent(
                    type="text",
                    text=json.dumps({
                        "data_type": data_type,
                        "original_value": value,
                        "formatted_value": formatted
                    }, indent=2)
                )]
                
            except Exception as e:
                logger.error(f"❌ Error in format_financial_data: {e}")
                return [types.TextContent(
                    type="text",
                    text=json.dumps({
                        "error": str(e),
                        "tool": "format_financial_data"
                    }, indent=2)
                )]
    
    async def run(self):
        """Run the MCP server"""
        from mcp.server.stdio import stdio_server
        
        async with stdio_server() as (read_stream, write_stream):
            await self.server.run(
                read_stream,
                write_stream,
                InitializationOptions(
                    server_name="internal-trading-tools",
                    server_version="1.0.0",
                    capabilities=self.server.get_capabilities(
                        notification_options=NotificationOptions(),
                        experimental_capabilities={}
                    )
                )
            )

async def main():
    """Main entry point"""
    server = InternalToolsMCPServer()
    await server.run()

if __name__ == "__main__":
    asyncio.run(main())