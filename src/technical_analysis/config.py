"""
Unified Technical Analysis Configuration
Merged from src/analysis/technical/config.py to provide centralized configuration.
"""

import os
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, field

@dataclass
class UnifiedTechnicalConfig:
    """
    Unified configuration for technical analysis parameters.
    Supports environment variable overrides and sensible defaults.
    """
    
    # RSI Configuration
    rsi_period: int = field(default=14)
    rsi_overbought: float = field(default=70.0)
    rsi_oversold: float = field(default=30.0)
    
    # MACD Configuration
    macd_fast_period: int = field(default=12)
    macd_slow_period: int = field(default=26)
    macd_signal_period: int = field(default=9)
    
    # Moving Average Configuration
    sma_periods: List[int] = field(default_factory=lambda: [20, 50, 200])
    ema_periods: List[int] = field(default_factory=lambda: [12, 26])
    sma_window: int = field(default=20)  # For compatibility with existing code
    ema_span: int = field(default=20)    # For compatibility with existing code
    
    # Bollinger Bands Configuration
    bb_period: int = field(default=20)
    bb_std_dev: float = field(default=2.0)
    bb_window: int = field(default=20)   # For compatibility
    bb_std: float = field(default=2.0)   # For compatibility
    
    # Volume Analysis Configuration
    volume_sma_period: int = field(default=20)
    volume_window: int = field(default=20)  # For compatibility
    
    # Volatility Configuration
    volatility_period: int = field(default=20)
    atr_period: int = field(default=14)
    
    # Support/Resistance Configuration
    sr_lookback_period: int = field(default=20)
    
    # Trend Analysis Configuration
    trend_analysis_periods: List[int] = field(default_factory=lambda: [20, 50, 200])
    
    # Stochastic Configuration
    stoch_k_period: int = field(default=14)
    stoch_d_period: int = field(default=3)
    
    # Williams %R Configuration
    williams_period: int = field(default=14)
    
    # CCI Configuration
    cci_period: int = field(default=20)
    
    def __post_init__(self):
        """Initialize configuration with environment variable overrides"""
        # RSI Configuration
        self.rsi_period = int(os.getenv('TECH_RSI_PERIOD', self.rsi_period))
        self.rsi_overbought = float(os.getenv('TECH_RSI_OVERBOUGHT', self.rsi_overbought))
        self.rsi_oversold = float(os.getenv('TECH_RSI_OVERSOLD', self.rsi_oversold))
        
        # MACD Configuration
        self.macd_fast_period = int(os.getenv('TECH_MACD_FAST', self.macd_fast_period))
        self.macd_slow_period = int(os.getenv('TECH_MACD_SLOW', self.macd_slow_period))
        self.macd_signal_period = int(os.getenv('TECH_MACD_SIGNAL', self.macd_signal_period))
        
        # Moving Average Configuration
        self.sma_window = int(os.getenv('TECH_SMA_WINDOW', self.sma_window))
        self.ema_span = int(os.getenv('TECH_EMA_SPAN', self.ema_span))
        
        # Bollinger Bands Configuration
        self.bb_period = int(os.getenv('TECH_BB_PERIOD', self.bb_period))
        self.bb_std_dev = float(os.getenv('TECH_BB_STD_DEV', self.bb_std_dev))
        self.bb_window = self.bb_period  # Keep in sync
        self.bb_std = self.bb_std_dev    # Keep in sync
        
        # Volume Configuration
        self.volume_sma_period = int(os.getenv('TECH_VOLUME_PERIOD', self.volume_sma_period))
        self.volume_window = self.volume_sma_period  # Keep in sync
        
        # Volatility Configuration
        self.volatility_period = int(os.getenv('TECH_VOLATILITY_PERIOD', self.volatility_period))
        self.atr_period = int(os.getenv('TECH_ATR_PERIOD', self.atr_period))
        
        # Support/Resistance Configuration
        self.sr_lookback_period = int(os.getenv('TECH_SR_LOOKBACK', self.sr_lookback_period))
        
        # Parse list configurations from environment
        sma_periods_env = os.getenv('TECH_SMA_PERIODS')
        if sma_periods_env:
            self.sma_periods = [int(x.strip()) for x in sma_periods_env.split(',')]
        
        ema_periods_env = os.getenv('TECH_EMA_PERIODS')
        if ema_periods_env:
            self.ema_periods = [int(x.strip()) for x in ema_periods_env.split(',')]
        
        trend_periods_env = os.getenv('TECH_TREND_PERIODS')
        if trend_periods_env:
            self.trend_analysis_periods = [int(x.strip()) for x in trend_periods_env.split(',')]
    
    def get_rsi_config(self) -> Dict[str, Any]:
        """Get RSI-specific configuration"""
        return {
            'period': self.rsi_period,
            'overbought': self.rsi_overbought,
            'oversold': self.rsi_oversold
        }
    
    def get_macd_config(self) -> Dict[str, Any]:
        """Get MACD-specific configuration"""
        return {
            'fast': self.macd_fast_period,
            'slow': self.macd_slow_period,
            'signal': self.macd_signal_period
        }
    
    def get_bollinger_config(self) -> Dict[str, Any]:
        """Get Bollinger Bands configuration"""
        return {
            'period': self.bb_period,
            'std_dev': self.bb_std_dev,
            'window': self.bb_window,
            'std': self.bb_std
        }
    
    def get_volume_config(self) -> Dict[str, Any]:
        """Get volume analysis configuration"""
        return {
            'sma_period': self.volume_sma_period,
            'window': self.volume_window
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary"""
        return {
            'rsi': self.get_rsi_config(),
            'macd': self.get_macd_config(),
            'bollinger': self.get_bollinger_config(),
            'volume': self.get_volume_config(),
            'sma_window': self.sma_window,
            'ema_span': self.ema_span,
            'atr_period': self.atr_period,
            'volatility_period': self.volatility_period,
            'sr_lookback_period': self.sr_lookback_period
        }
    
    def get_rsi_config(self) -> Dict[str, Any]:
        """Get RSI-specific configuration"""
        return {
            'period': self.rsi_period,
            'overbought': self.rsi_overbought,
            'oversold': self.rsi_oversold
        }
    
    def get_macd_config(self) -> Dict[str, Any]:
        """Get MACD-specific configuration"""
        return {
            'fast': self.macd_fast_period,
            'slow': self.macd_slow_period,
            'signal': self.macd_signal_period
        }
    
    def get_bollinger_config(self) -> Dict[str, Any]:
        """Get Bollinger Bands configuration"""
        return {
            'period': self.bb_period,
            'std_dev': self.bb_std_dev,
            'window': self.bb_window,
            'std': self.bb_std
        }
    
    def get_volume_config(self) -> Dict[str, Any]:
        """Get volume analysis configuration"""
        return {
            'sma_period': self.volume_sma_period,
            'window': self.volume_window
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary"""
        return {
            'rsi': self.get_rsi_config(),
            'macd': self.get_macd_config(),
            'bollinger': self.get_bollinger_config(),
            'volume': self.get_volume_config(),
            'sma_window': self.sma_window,
            'ema_span': self.ema_span,
            'atr_period': self.atr_period,
            'volatility_period': self.volatility_period,
            'sr_lookback_period': self.sr_lookback_period
        }

# Global configuration instance
_config: Optional[UnifiedTechnicalConfig] = None

def get_technical_config() -> UnifiedTechnicalConfig:
    """Get global technical analysis configuration (singleton)"""
    global _config
    if _config is None:
        _config = UnifiedTechnicalConfig()
    return _config

def reset_config():
    """Reset configuration (useful for testing)"""
    global _config
    _config = None

# Backward compatibility aliases
def get_tech_config() -> UnifiedTechnicalConfig:
    """Backward compatibility alias"""
    return get_technical_config()

TechnicalConfig = UnifiedTechnicalConfig  # Backward compatibility alias