"""
Unified Technical Analysis Analyzer
This module acts as a facade for the TechnicalAnalysisCalculator, providing a unified interface
for complex analysis requests, potentially integrating multiple indicators or strategies.
"""
from typing import Dict, Any, List
from .calculator import technical_analysis_calculator


class UnifiedAnalyzer:
    """Facade for technical analysis calculations.

    Delegates to the main TechnicalAnalysisCalculator instance so callers that
    previously used this module get a stable interface.
    """

    def __init__(self):
        self.calculator = technical_analysis_calculator

    async def calculate_indicators(self, historical_data: Dict[str, Any], indicators_requested: List[str]) -> Dict[str, Any]:
        # Normalize input shape and delegate to the calculator's synchronous method
        if 'data' in historical_data:
            df = historical_data['data']
        else:
            df = historical_data

        # The underlying calculator operates synchronously; call it and wrap result
        try:
            result = self.calculator.calculate_all_indicators(df)
            # Filter by requested indicators if needed
            if indicators_requested:
                filtered = {k: v for k, v in result.items() if k in indicators_requested or k in ('symbol', 'timestamp')}
                return {"status": "success", "indicators": filtered}
            return {"status": "success", "indicators": result}
        except Exception as e:
            return {"status": "error", "message": str(e)}


# Global instance
unified_analyzer = UnifiedAnalyzer()