"""Technical Analysis Calculator - Orchestrates indicator calculations with configurable parameters.
Supports environment-based configuration for flexible indicator settings.
"""
import logging
from typing import Dict, List, Optional, Any
import pandas as pd

from .indicators import (
    calculate_sma,
    calculate_ema,
    calculate_rsi,
    calculate_macd,
    calculate_bollinger_bands,
    calculate_atr,
    calculate_vwap,
    calculate_volume_analysis
)
from .zones import supply_demand_detector
from .config import get_technical_config

logger = logging.getLogger(__name__)

class TechnicalAnalysisCalculator:
    """Orchestrator for technical indicator calculations with configurable parameters.
    All parameters can be set via environment variables or constructor arguments.
    """
    
    def __init__(self, 
                 sma_window: Optional[int] = None,
                 ema_span: Optional[int] = None,
                 rsi_period: Optional[int] = None,
                 macd_fast: Optional[int] = None,
                 macd_slow: Optional[int] = None,
                 macd_signal: Optional[int] = None,
                 bb_window: Optional[int] = None,
                 bb_std: Optional[float] = None,
                 atr_period: Optional[int] = None,
                 volume_window: Optional[int] = None):
        """
        Initialize calculator with configurable parameters.
        Parameters are loaded from central configuration if not provided.
        """
        # Get unified technical configuration
        technical_config = get_technical_config()

        # Use provided values or fall back to unified config
        self.sma_window = sma_window if sma_window is not None else technical_config.sma_window
        self.ema_span = ema_span if ema_span is not None else technical_config.ema_span
        self.rsi_period = rsi_period if rsi_period is not None else technical_config.rsi_period
        self.macd_fast = macd_fast if macd_fast is not None else technical_config.macd_fast_period
        self.macd_slow = macd_slow if macd_slow is not None else technical_config.macd_slow_period
        self.macd_signal = macd_signal if macd_signal is not None else technical_config.macd_signal_period
        self.bb_window = bb_window if bb_window is not None else technical_config.bb_window
        self.bb_std = bb_std if bb_std is not None else technical_config.bb_std
        self.atr_period = atr_period if atr_period is not None else technical_config.atr_period
        self.volume_window = volume_window if volume_window is not None else technical_config.volume_window
        
        logger.debug(f"TechnicalAnalysisCalculator initialized with: SMA={self.sma_window}, "
                    f"EMA={self.ema_span}, RSI={self.rsi_period}, MACD={self.macd_fast}/{self.macd_slow}/{self.macd_signal}, "
                    f"BB={self.bb_window}/{self.bb_std}, ATR={self.atr_period}, Volume={self.volume_window}")
        
    def calculate_all_indicators(self, df: pd.DataFrame, symbol: str = "") -> Dict[str, Any]:
        """
        Calculate all technical indicators for a given DataFrame with enhanced validation.

        Args:
            df: Pandas DataFrame with OHLCV data (columns: open, high, low, close, volume)
            symbol: Optional symbol for logging purposes

        Returns:
            Dict containing all calculated indicators
        """
        # Enhanced data validation
        validation_result = self._validate_data(df, symbol)
        if not validation_result['valid']:
            logger.warning(f"Data validation failed for {symbol}: {validation_result['reason']}")
            return {
                'symbol': symbol,
                'error': 'data_validation_failed',
                'validation_details': validation_result,
                'timestamp': pd.Timestamp.now().isoformat()
            }

        df_clean = validation_result['clean_data']

        try:
            # Extract series from DataFrame with validation
            close = self._get_validated_series(df_clean, 'close', symbol)
            high = self._get_validated_series(df_clean, 'high', symbol)
            low = self._get_validated_series(df_clean, 'low', symbol)
            volume = self._get_validated_series(df_clean, 'volume', symbol)

            if close is None or close.empty:
                logger.error(f"No valid close price data for symbol {symbol}")
                return {'symbol': symbol, 'error': 'no_close_data'}

            results = {
                'symbol': symbol,
                'price': float(close.iloc[-1]) if not close.empty else None,
                'timestamp': pd.Timestamp.now().isoformat(),
                'data_quality': {
                    'total_points': len(close),
                    'valid_points': len(close.dropna()),
                    'coverage_percentage': (len(close.dropna()) / len(close)) * 100
                }
            }

            # Calculate individual indicators with error handling
            results.update(self._calculate_trend_indicators_safe(close))
            results.update(self._calculate_momentum_indicators_safe(close))
            results.update(self._calculate_volatility_indicators_safe(close))

            # Calculate indicators that require multiple series
            if high is not None and low is not None:
                results.update(self._calculate_range_indicators_safe(high, low, close))

            if volume is not None:
                results.update(self._calculate_volume_indicators_safe(volume))
                if high is not None and low is not None and close is not None:
                    results.update(self._calculate_vwap_indicator_safe(high, low, close, volume))

            # Calculate supply and demand zones
            zone_results = self._calculate_supply_demand_zones_safe(df_clean, close, high, low)
            results.update(zone_results)

            # Add summary metrics
            results['summary'] = self._generate_analysis_summary(results)

            return results

        except Exception as e:
            logger.error(f"Error calculating indicators for {symbol}: {e}")
            return {
                'symbol': symbol,
                'error': str(e),
                'error_type': type(e).__name__,
                'timestamp': pd.Timestamp.now().isoformat()
            }

    def _validate_data(self, df: pd.DataFrame, symbol: str) -> Dict[str, Any]:
        """Enhanced data validation with detailed reporting"""
        if df is None or df.empty:
            return {
                'valid': False,
                'reason': 'Empty DataFrame',
                'clean_data': pd.DataFrame()
            }

        # Check required columns
        required_columns = ['close']
        missing_columns = [col for col in required_columns if col not in df.columns]

        if missing_columns:
            return {
                'valid': False,
                'reason': f'Missing required columns: {missing_columns}',
                'clean_data': pd.DataFrame()
            }

        # Check data quality
        total_rows = len(df)
        if total_rows < 10:
            return {
                'valid': False,
                'reason': f'Insufficient data points: {total_rows} (minimum 10 required)',
                'clean_data': pd.DataFrame()
            }

        # Clean the data
        df_clean = df.copy()

        # Remove rows with NaN in critical columns
        df_clean = df_clean.dropna(subset=['close'])

        # Ensure numeric data types
        numeric_columns = ['close', 'high', 'low', 'volume', 'open']
        for col in numeric_columns:
            if col in df_clean.columns:
                df_clean[col] = pd.to_numeric(df_clean[col], errors='coerce')

        # Remove any remaining NaN values
        df_clean = df_clean.dropna()

        if len(df_clean) < 10:
            return {
                'valid': False,
                'reason': f'Insufficient valid data points after cleaning: {len(df_clean)}',
                'clean_data': df_clean
            }

        return {
            'valid': True,
            'reason': 'Data validation passed',
            'clean_data': df_clean,
            'original_rows': total_rows,
            'clean_rows': len(df_clean),
            'data_loss_percentage': ((total_rows - len(df_clean)) / total_rows) * 100
        }

    def _get_validated_series(self, df: pd.DataFrame, column: str, symbol: str) -> Optional[pd.Series]:
        """Get a validated series with error handling"""
        if column not in df.columns:
            logger.debug(f"Column '{column}' not available for {symbol}")
            return None

        series = df[column]
        if series is None or series.empty:
            logger.debug(f"Empty series for column '{column}' in {symbol}")
            return None

        # Drop NaN values
        series = series.dropna()
        if series.empty:
            logger.debug(f"No valid data for column '{column}' in {symbol}")
            return None

        return series

    def _calculate_trend_indicators_safe(self, close: pd.Series) -> Dict[str, Any]:
        """Calculate trend indicators with error handling"""
        try:
            return self._calculate_trend_indicators(close)
        except Exception as e:
            logger.warning(f"Error calculating trend indicators: {e}")
            return {'trend_error': str(e)}

    def _calculate_momentum_indicators_safe(self, close: pd.Series) -> Dict[str, Any]:
        """Calculate momentum indicators with error handling"""
        try:
            return self._calculate_momentum_indicators(close)
        except Exception as e:
            logger.warning(f"Error calculating momentum indicators: {e}")
            return {'momentum_error': str(e)}

    def _calculate_volatility_indicators_safe(self, close: pd.Series) -> Dict[str, Any]:
        """Calculate volatility indicators with error handling"""
        try:
            return self._calculate_volatility_indicators(close)
        except Exception as e:
            logger.warning(f"Error calculating volatility indicators: {e}")
            return {'volatility_error': str(e)}

    def _calculate_range_indicators_safe(self, high: pd.Series, low: pd.Series, close: pd.Series) -> Dict[str, Any]:
        """Calculate range indicators with error handling"""
        try:
            return self._calculate_range_indicators(high, low, close)
        except Exception as e:
            logger.warning(f"Error calculating range indicators: {e}")
            return {'range_error': str(e)}

    def _calculate_volume_indicators_safe(self, volume: pd.Series) -> Dict[str, Any]:
        """Calculate volume indicators with error handling"""
        try:
            return self._calculate_volume_indicators(volume)
        except Exception as e:
            logger.warning(f"Error calculating volume indicators: {e}")
            return {'volume_error': str(e)}

    def _calculate_vwap_indicator_safe(self, high: pd.Series, low: pd.Series, close: pd.Series, volume: pd.Series) -> Dict[str, Any]:
        """Calculate VWAP indicator with error handling"""
        try:
            return self._calculate_vwap_indicator(high, low, close, volume)
        except Exception as e:
            logger.warning(f"Error calculating VWAP indicator: {e}")
            return {'vwap_error': str(e)}

    def _calculate_supply_demand_zones_safe(self, df: pd.DataFrame, close: pd.Series, high: pd.Series, low: pd.Series) -> Dict[str, Any]:
        """Calculate supply/demand zones with error handling"""
        try:
            return self._calculate_supply_demand_zones(df, close, high, low)
        except Exception as e:
            logger.warning(f"Error calculating supply/demand zones: {e}")
            return {'supply_demand_zones': [], 'zone_metrics': {}, 'zones_error': str(e)}

    def _generate_analysis_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate a summary of the analysis"""
        summary = {
            'calculation_success': True,
            'indicators_calculated': [],
            'errors_encountered': []
        }

        # Check which indicators were successfully calculated
        indicator_categories = {
            'trend': ['sma', 'ema', 'sma_20', 'sma_50', 'ema_20', 'ema_50'],
            'momentum': ['rsi', 'macd', 'macd_signal', 'macd_histogram'],
            'volatility': ['bb_middle', 'bb_upper', 'bb_lower', 'bb_width'],
            'range': ['atr'],
            'volume': ['volume_ma', 'volume_ratio', 'volume_spike'],
            'vwap': ['vwap'],
            'zones': ['supply_demand_zones', 'zone_metrics']
        }

        for category, indicators in indicator_categories.items():
            for indicator in indicators:
                if indicator in results and results[indicator] is not None:
                    if not indicator.endswith('_error'):
                        summary['indicators_calculated'].append(indicator)
                elif f'{indicator}_error' in results:
                    summary['errors_encountered'].append(f"{indicator}: {results[f'{indicator}_error']}")

        return summary

    def _calculate_trend_indicators(self, close: pd.Series) -> Dict[str, Any]:
        """Calculate trend-following indicators"""
        return {
            'sma': calculate_sma(close, self.sma_window),
            'ema': calculate_ema(close, self.ema_span),
            'sma_20': calculate_sma(close, 20),  # Common default
            'sma_50': calculate_sma(close, 50),  # Common default
            'ema_20': calculate_ema(close, 20),  # Common default
            'ema_50': calculate_ema(close, 50)   # Common default
        }

    def _calculate_momentum_indicators(self, close: pd.Series) -> Dict[str, Any]:
        """Calculate momentum indicators"""
        macd_result = calculate_macd(close, self.macd_fast, self.macd_slow, self.macd_signal)
        return {
            'rsi': calculate_rsi(close, self.rsi_period),
            'macd': macd_result.get('macd'),
            'macd_signal': macd_result.get('signal'),
            'macd_histogram': macd_result.get('histogram')
        }

    def _calculate_volatility_indicators(self, close: pd.Series) -> Dict[str, Any]:
        """Calculate volatility indicators"""
        bb_result = calculate_bollinger_bands(close, self.bb_window, self.bb_std)
        return {
            'bb_middle': bb_result.get('middle'),
            'bb_upper': bb_result.get('upper'),
            'bb_lower': bb_result.get('lower'),
            'bb_width': (bb_result.get('upper') - bb_result.get('lower')) / bb_result.get('middle') 
                        if bb_result.get('upper') and bb_result.get('lower') and bb_result.get('middle') else None
        }

    def _calculate_range_indicators(self, high: pd.Series, low: pd.Series, close: pd.Series) -> Dict[str, Any]:
        """Calculate range-based indicators"""
        return {
            'atr': calculate_atr(high, low, close, self.atr_period)
        }

    def _calculate_volume_indicators(self, volume: pd.Series) -> Dict[str, Any]:
        """Calculate volume analysis indicators"""
        volume_result = calculate_volume_analysis(volume, self.volume_window)
        return {
            'volume_ma': volume_result.get('volume_ma'),
            'volume_ratio': volume_result.get('volume_ratio'),
            'volume_spike': volume_result.get('volume_spike')
        }

    def _calculate_vwap_indicator(self, high: pd.Series, low: pd.Series,
                                 close: pd.Series, volume: pd.Series) -> Dict[str, Any]:
        """Calculate VWAP indicator"""
        return {
            'vwap': calculate_vwap(high, low, close, volume)
        }
    
    def _calculate_supply_demand_zones(self, df: pd.DataFrame, close: pd.Series,
                                     high: pd.Series, low: pd.Series) -> Dict[str, Any]:
        """Calculate supply and demand zones with zone analysis"""
        try:
            # Use the global zone detector - it may return a dict with a 'zones' key or a list
            raw_zones = supply_demand_detector.detect_zones(df, symbol="")

            # Normalize raw_zones to a list of zone dicts
            normalized_zones = []
            if isinstance(raw_zones, dict):
                # Expect structure: { 'zones': [ {..}, ... ], ... }
                normalized_zones = raw_zones.get('zones', []) if raw_zones.get('zones') else []
            elif isinstance(raw_zones, list):
                # Could be list of SupplyDemandZone objects or dicts
                if raw_zones and hasattr(raw_zones[0], 'to_dict'):
                    normalized_zones = [z.to_dict() for z in raw_zones]
                else:
                    normalized_zones = raw_zones
            else:
                # Fallback - attempt to coerce single zone object
                try:
                    normalized_zones = [raw_zones.to_dict()]
                except Exception:
                    normalized_zones = []

            # Calculate zone metrics using normalized list of dicts
            zone_metrics = self._calculate_zone_metrics(normalized_zones, close.iloc[-1] if not close.empty else 0)

            return {
                'supply_demand_zones': normalized_zones,
                'zone_metrics': zone_metrics
            }
        except Exception as e:
            logger.error(f"Error calculating supply/demand zones: {e}")
            return {'supply_demand_zones': [], 'zone_metrics': {}}
    
    def _calculate_zone_metrics(self, zones: List[Dict], current_price: float) -> Dict[str, Any]:
        """Calculate metrics for supply and demand zones"""
        if not zones:
            return {'total_zones': 0, 'supply_zone_count': 0, 'demand_zone_count': 0}
        
        supply_zones = [z for z in zones if z.get('zone_type') == 'supply']
        demand_zones = [z for z in zones if z.get('zone_type') == 'demand']
        
        metrics = {
            'total_zones': len(zones),
            'supply_zone_count': len(supply_zones),
            'demand_zone_count': len(demand_zones)
        }
        
        # Find nearest zones
        if supply_zones and current_price > 0:
            nearest_supply = min(supply_zones, key=lambda z: abs(z.get('center_price', 0) - current_price))
            metrics['nearest_supply'] = nearest_supply
        
        if demand_zones and current_price > 0:
            nearest_demand = min(demand_zones, key=lambda z: abs(z.get('center_price', 0) - current_price))
            metrics['nearest_demand'] = nearest_demand
        
        return metrics

    def get_configuration(self) -> Dict[str, Any]:
        """Return current configuration settings"""
        return {
            'sma_window': self.sma_window,
            'ema_span': self.ema_span,
            'rsi_period': self.rsi_period,
            'macd_fast': self.macd_fast,
            'macd_slow': self.macd_slow,
            'macd_signal': self.macd_signal,
            'bb_window': self.bb_window,
            'bb_std': self.bb_std,
            'atr_period': self.atr_period,
            'volume_window': self.volume_window
        }


# Global instance for easy access
technical_analysis_calculator = TechnicalAnalysisCalculator()