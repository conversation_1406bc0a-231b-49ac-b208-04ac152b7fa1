"""
Signal Generator Module

Provides technical signal generation functionality for the watchlist alerts system.
"""

import logging
from typing import Dict, List, Any
import pandas as pd

from .calculator import technical_analysis_calculator

logger = logging.getLogger(__name__)


class SignalGenerator:
    """
    Generates technical signals based on indicator analysis.
    """
    
    def __init__(self):
        """Initialize the signal generator."""
        self.calculator = technical_analysis_calculator
        logger.info("SignalGenerator initialized")
    
    async def generate_signals(self, symbol: str, historical_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Generate technical signals for a symbol based on historical data.
        
        Args:
            symbol: Stock symbol
            historical_data: Historical price data
            
        Returns:
            List of signal dictionaries with signal type, strength, and indicators
        """
        try:
            # Convert historical data to DataFrame if it's not already
            if isinstance(historical_data, dict) and 'data' in historical_data:
                data = historical_data['data']
            else:
                data = historical_data
                
            # Create DataFrame from historical data
            df = pd.DataFrame(data)
            
            # Ensure required columns are present
            required_columns = ['close']
            if not all(col in df.columns for col in required_columns):
                logger.warning(f"Missing required columns in historical data for {symbol}")
                return []
            
            # Calculate technical indicators
            indicators = self.calculator.calculate_all_indicators(df, symbol)
            
            # Generate signals based on indicators
            signals = self._analyze_indicators(indicators)
            
            return signals
            
        except Exception as e:
            logger.error(f"Error generating signals for {symbol}: {e}")
            return []
    
    def _analyze_indicators(self, indicators: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Analyze technical indicators to generate signals.
        
        Args:
            indicators: Dictionary of calculated technical indicators
            
        Returns:
            List of signal dictionaries
        """
        signals = []
        
        # RSI Signal
        rsi = indicators.get('rsi')
        if rsi is not None:
            if rsi < 30:
                signals.append({
                    'signal': 'oversold',
                    'strength': min(1.0, (30 - rsi) / 30),
                    'indicators': {'rsi': rsi}
                })
            elif rsi > 70:
                signals.append({
                    'signal': 'overbought',
                    'strength': min(1.0, (rsi - 70) / 30),
                    'indicators': {'rsi': rsi}
                })
        
        # MACD Signal
        macd = indicators.get('macd')
        macd_signal = indicators.get('macd_signal')
        if macd is not None and macd_signal is not None:
            # MACD crossover signal
            if macd > macd_signal and macd > 0:
                signals.append({
                    'signal': 'bullish_macd',
                    'strength': min(1.0, abs(macd - macd_signal) / abs(macd)),
                    'indicators': {'macd': macd, 'macd_signal': macd_signal}
                })
            elif macd < macd_signal and macd < 0:
                signals.append({
                    'signal': 'bearish_macd',
                    'strength': min(1.0, abs(macd - macd_signal) / abs(macd)),
                    'indicators': {'macd': macd, 'macd_signal': macd_signal}
                })
        
        # Moving Average Signal
        sma_20 = indicators.get('sma_20')
        sma_50 = indicators.get('sma_50')
        price = indicators.get('price')
        if sma_20 is not None and sma_50 is not None and price is not None:
            # Golden cross / death cross signal
            if sma_20 > sma_50 and price > sma_20:
                signals.append({
                    'signal': 'golden_cross',
                    'strength': min(1.0, abs(sma_20 - sma_50) / sma_50),
                    'indicators': {'sma_20': sma_20, 'sma_50': sma_50, 'price': price}
                })
            elif sma_20 < sma_50 and price < sma_20:
                signals.append({
                    'signal': 'death_cross',
                    'strength': min(1.0, abs(sma_50 - sma_20) / sma_50),
                    'indicators': {'sma_20': sma_20, 'sma_50': sma_50, 'price': price}
                })
        
        return signals


# Global instance for easy access
signal_generator = SignalGenerator()