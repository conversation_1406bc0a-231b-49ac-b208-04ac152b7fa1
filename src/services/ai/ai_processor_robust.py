"""
Unified AI Processor for Financial Analysis
Consolidated from multiple processors with anti-hallucination validation.

Features:
- Complete technical analysis (from ai_processor_robust)
- Anti-hallucination validation (from zero_hallucination_generator)
- Clean interfaces (from ai_processor_clean)
- Conversational context (from intelligent_chatbot)
"""

import logging
import asyncio
import json
import os
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum

# NOTE: This file was moved from src(old)/shared/ai_services/
# The import below needs to be updated once all dependencies are moved.
from .smart_model_router import router

logger = logging.getLogger(__name__)


class AnalysisType(Enum):
    """Available analysis types"""
    PRICE = "price_analysis"
    TREND = "trend_analysis" 
    MOMENTUM = "momentum_analysis"
    SUPPORT_RESISTANCE = "support_resistance_analysis"
    RSI = "rsi_analysis"
    VOLATILITY = "volatility_analysis"


class DataQuality(Enum):
    """Data quality levels"""
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    INSUFFICIENT = "insufficient"


class ValidationMode(Enum):
    """Response validation modes"""
    STRICT = "strict"           # Zero AI generation, pure data only
    VALIDATED = "validated"     # AI generation with validation
    PERMISSIVE = "permissive"   # Standard AI generation


@dataclass
class ValidationResult:
    """Result of response validation"""
    is_valid: bool
    confidence: float
    issues: List[str]
    corrected_response: Optional[str] = None
    validation_mode: ValidationMode = ValidationMode.VALIDATED


@dataclass
class TechnicalIndicators:
    """Container for all calculated technical indicators"""
    rsi: Optional[float] = None
    macd: Optional[Dict[str, float]] = None
    sma_20: Optional[float] = None
    sma_50: Optional[float] = None
    support_levels: Optional[List[float]] = None
    resistance_levels: Optional[List[float]] = None
    bollinger_upper: Optional[float] = None
    bollinger_lower: Optional[float] = None
    volume_avg: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary, excluding None values"""
        return {k: v for k, v in asdict(self).items() if v is not None}


class AntiHallucinationValidator:
    """
    Validates AI responses against actual data to prevent hallucination.
    Merged from zero_hallucination_generator.py
    """

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def validate_response(self, response: str, market_data: Dict[str, Any],
                         validation_mode: ValidationMode = ValidationMode.VALIDATED) -> ValidationResult:
        """
        Validate AI response against actual market data.

        Args:
            response: AI-generated response to validate
            market_data: Actual market data to validate against
            validation_mode: Validation strictness level

        Returns:
            ValidationResult with validation status and corrections
        """
        issues = []
        confidence = 1.0

        try:
            if validation_mode == ValidationMode.STRICT:
                # In strict mode, only allow pure data responses
                return self._validate_strict_mode(response, market_data)

            # Extract price mentions from response
            price_issues = self._validate_price_mentions(response, market_data)
            issues.extend(price_issues)

            # Validate technical indicator mentions
            indicator_issues = self._validate_indicator_mentions(response, market_data)
            issues.extend(indicator_issues)

            # Calculate confidence based on issues found
            if issues:
                confidence = max(0.0, 1.0 - (len(issues) * 0.2))

            is_valid = len(issues) == 0 or validation_mode == ValidationMode.PERMISSIVE

            return ValidationResult(
                is_valid=is_valid,
                confidence=confidence,
                issues=issues,
                validation_mode=validation_mode
            )

        except Exception as e:
            self.logger.error(f"Validation error: {e}")
            return ValidationResult(
                is_valid=False,
                confidence=0.0,
                issues=[f"Validation error: {str(e)}"],
                validation_mode=validation_mode
            )

    def _validate_strict_mode(self, response: str, market_data: Dict[str, Any]) -> ValidationResult:
        """Validate response in strict mode - only pure data allowed"""
        # In strict mode, generate pure data response
        corrected_response = self._generate_pure_data_response(market_data)

        return ValidationResult(
            is_valid=True,
            confidence=1.0,
            issues=[],
            corrected_response=corrected_response,
            validation_mode=ValidationMode.STRICT
        )

    def _validate_price_mentions(self, response: str, market_data: Dict[str, Any]) -> List[str]:
        """Validate price mentions in response against actual data"""
        import re

        issues = []

        # Extract price mentions from response (e.g., $123.45)
        price_pattern = r'\$(\d+\.?\d*)'
        mentioned_prices = re.findall(price_pattern, response)

        if not mentioned_prices:
            return issues

        # Get actual current price
        actual_price = market_data.get('current_price', 0)
        if actual_price == 0:
            return issues

        for price_str in mentioned_prices:
            try:
                mentioned_price = float(price_str)

                # Check if mentioned price is reasonable (within 50% of actual)
                price_ratio = mentioned_price / actual_price
                if price_ratio < 0.5 or price_ratio > 2.0:
                    issues.append(f"Suspicious price ${mentioned_price:.2f} vs actual ${actual_price:.2f}")

            except ValueError:
                continue

        return issues

    def _validate_indicator_mentions(self, response: str, market_data: Dict[str, Any]) -> List[str]:
        """Validate technical indicator mentions against actual data"""
        issues = []

        # Validate RSI mentions
        if 'RSI' in response or 'rsi' in response:
            actual_rsi = market_data.get('rsi')
            if actual_rsi is None:
                issues.append("Response mentions RSI but no RSI data available")

        # Validate MACD mentions
        if 'MACD' in response or 'macd' in response:
            actual_macd = market_data.get('macd')
            if actual_macd is None:
                issues.append("Response mentions MACD but no MACD data available")

        return issues

    def _generate_pure_data_response(self, market_data: Dict[str, Any]) -> str:
        """Generate pure data response with zero AI interpretation"""
        symbol = market_data.get('symbol', 'UNKNOWN')
        current_price = market_data.get('current_price', 0)

        parts = [f"**{symbol} Technical Data**"]

        if current_price > 0:
            parts.append(f"Current Price: ${current_price:.2f}")

        # Add only calculated indicators
        rsi = market_data.get('rsi')
        if rsi is not None:
            parts.append(f"RSI: {rsi:.1f}")

        macd = market_data.get('macd')
        if macd and isinstance(macd, dict):
            macd_val = macd.get('macd', 0)
            parts.append(f"MACD: {macd_val:.2f}")

        parts.append("*Pure data - no AI interpretation*")

        return "\n".join(parts)


@dataclass
class MarketSnapshot:
    """Immutable market data snapshot"""
    symbol: str
    current_price: float
    change_percent: float
    volume: int
    timestamp: datetime
    indicators: TechnicalIndicators
    data_quality: DataQuality
    
    def is_valid(self) -> bool:
        """Check if snapshot has minimum required data"""
        return (
            self.current_price > 0 and
            self.data_quality != DataQuality.INSUFFICIENT and
            bool(self.indicators.to_dict())
        )


class TechnicalCalculator:
    """Calculate technical indicators from market data"""
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.TechnicalCalculator")
    
    def calculate_indicators(self, market_data: Dict[str, Any]) -> TechnicalIndicators:
        """Calculate all available technical indicators"""
        try:
            price_data = market_data.get('price_data', {})
            if not price_data:
                return TechnicalIndicators()
            
            indicators = TechnicalIndicators()
            
            # Calculate RSI
            indicators.rsi = self._calculate_rsi(price_data)
            
            # Calculate moving averages
            indicators.sma_20 = self._calculate_sma(price_data, 20)
            indicators.sma_50 = self._calculate_sma(price_data, 50)
            
            # Calculate MACD
            indicators.macd = self._calculate_macd(price_data)
            
            # Calculate support/resistance
            indicators.support_levels, indicators.resistance_levels = self._calculate_support_resistance(price_data)
            
            # Calculate Bollinger Bands
            bb_upper, bb_lower = self._calculate_bollinger_bands(price_data)
            indicators.bollinger_upper = bb_upper
            indicators.bollinger_lower = bb_lower
            
            # Calculate volume average
            indicators.volume_avg = self._calculate_volume_average(market_data)
            
            return indicators
            
        except Exception as e:
            self.logger.error(f"Error calculating indicators: {e}")
            return TechnicalIndicators()
    
    def _calculate_rsi(self, price_data: Dict[str, Any]) -> Optional[float]:
        """Calculate RSI - simplified implementation"""
        try:
            prices = price_data.get('close', [])
            if len(prices) < 14:
                return None
            
            # Simple RSI calculation (normally would use proper financial library)
            recent_prices = prices[-15:]  # Get last 15 prices for 14-period RSI
            gains = []
            losses = []
            
            for i in range(1, len(recent_prices)):
                change = recent_prices[i] - recent_prices[i-1]
                if change > 0:
                    gains.append(change)
                    losses.append(0)
                else:
                    gains.append(0)
                    losses.append(abs(change))
            
            if not gains or not losses:
                return None
                
            avg_gain = sum(gains) / len(gains)
            avg_loss = sum(losses) / len(losses)
            
            if avg_loss == 0:
                return 100.0
                
            rs = avg_gain / avg_loss
            rsi = 100 - (100 / (1 + rs))
            
            return round(rsi, 2)
            
        except Exception as e:
            self.logger.error(f"RSI calculation error: {e}")
            return None
    
    def _calculate_sma(self, price_data: Dict[str, Any], period: int) -> Optional[float]:
        """Calculate Simple Moving Average"""
        try:
            prices = price_data.get('close', [])
            if len(prices) < period:
                return None
            
            recent_prices = prices[-period:]
            return round(sum(recent_prices) / len(recent_prices), 2)
            
        except Exception as e:
            self.logger.error(f"SMA calculation error: {e}")
            return None
    
    def _calculate_macd(self, price_data: Dict[str, Any]) -> Optional[Dict[str, float]]:
        """Calculate MACD - simplified implementation"""
        try:
            prices = price_data.get('close', [])
            if len(prices) < 26:
                return None
            
            # Simplified MACD (normally would use exponential moving averages)
            ema_12 = self._calculate_sma(price_data, 12)
            ema_26 = self._calculate_sma(price_data, 26)
            
            if ema_12 is None or ema_26 is None:
                return None
            
            macd_line = ema_12 - ema_26
            signal_line = macd_line * 0.9  # Simplified signal line
            
            return {
                'macd': round(macd_line, 2),
                'signal': round(signal_line, 2),
                'histogram': round(macd_line - signal_line, 2)
            }
            
        except Exception as e:
            self.logger.error(f"MACD calculation error: {e}")
            return None
    
    def _calculate_support_resistance(self, price_data: Dict[str, Any]) -> Tuple[List[float], List[float]]:
        """Calculate support and resistance levels"""
        try:
            prices = price_data.get('close', [])
            if len(prices) < 20:
                return [], []
            
            # Simple support/resistance calculation
            recent_prices = prices[-20:]
            min_price = min(recent_prices)
            max_price = max(recent_prices)
            current_price = recent_prices[-1]
            
            # Calculate levels based on price ranges
            support_levels = [
                round(min_price, 2),
                round(current_price * 0.95, 2)  # 5% below current
            ]
            
            resistance_levels = [
                round(max_price, 2),
                round(current_price * 1.05, 2)  # 5% above current
            ]
            
            return support_levels, resistance_levels
            
        except Exception as e:
            self.logger.error(f"Support/Resistance calculation error: {e}")
            return [], []
    
    def _calculate_bollinger_bands(self, price_data: Dict[str, Any]) -> Tuple[Optional[float], Optional[float]]:
        """Calculate Bollinger Bands"""
        try:
            sma_20 = self._calculate_sma(price_data, 20)
            if sma_20 is None:
                return None, None
            
            prices = price_data.get('close', [])[-20:]
            
            # Calculate standard deviation
            variance = sum((price - sma_20) ** 2 for price in prices) / len(prices)
            std_dev = variance ** 0.5
            
            upper_band = round(sma_20 + (2 * std_dev), 2)
            lower_band = round(sma_20 - (2 * std_dev), 2)
            
            return upper_band, lower_band
            
        except Exception as e:
            self.logger.error(f"Bollinger Bands calculation error: {e}")
            return None, None
    
    def _calculate_volume_average(self, market_data: Dict[str, Any]) -> Optional[float]:
        """Calculate volume average"""
        try:
            volume_data = market_data.get('volume_data', [])
            if not volume_data:
                return market_data.get('volume')
            
            recent_volumes = volume_data[-10:] if len(volume_data) >= 10 else volume_data
            return sum(recent_volumes) / len(recent_volumes)
            
        except Exception as e:
            self.logger.error(f"Volume average calculation error: {e}")
            return None


class AnalysisSelector:
    """Select appropriate analyses based on data quality and user intent"""
    
    def __init__(self):
        self.analysis_requirements = {
            AnalysisType.PRICE: lambda snapshot: snapshot.current_price > 0,
            AnalysisType.TREND: lambda snapshot: snapshot.indicators.sma_20 and snapshot.indicators.sma_50,
            AnalysisType.MOMENTUM: lambda snapshot: snapshot.indicators.rsi is not None,
            AnalysisType.SUPPORT_RESISTANCE: lambda snapshot: bool(snapshot.indicators.support_levels and snapshot.indicators.resistance_levels),
            AnalysisType.RSI: lambda snapshot: snapshot.indicators.rsi is not None,
            AnalysisType.VOLATILITY: lambda snapshot: snapshot.indicators.bollinger_upper and snapshot.indicators.bollinger_lower
        }
    
    def select_analyses(self, snapshot: MarketSnapshot, user_query: str = "") -> List[AnalysisType]:
        """Select appropriate analyses based on available data and user query"""
        if not snapshot.is_valid():
            return [AnalysisType.PRICE]  # Fallback to basic price analysis
        
        available_analyses = []
        
        # Check which analyses are possible with available data
        for analysis_type, requirement in self.analysis_requirements.items():
            if requirement(snapshot):
                available_analyses.append(analysis_type)
        
        # Filter based on data quality
        if snapshot.data_quality == DataQuality.LOW:
            # Only basic analyses for low quality data
            available_analyses = [a for a in available_analyses 
                                 if a in [AnalysisType.PRICE, AnalysisType.MOMENTUM]]
        
        # Prioritize based on user query keywords
        query_priorities = self._analyze_user_intent(user_query)
        prioritized_analyses = []
        
        for analysis_type in query_priorities:
            if analysis_type in available_analyses:
                prioritized_analyses.append(analysis_type)
        
        # Add remaining analyses
        for analysis_type in available_analyses:
            if analysis_type not in prioritized_analyses:
                prioritized_analyses.append(analysis_type)
        
        # Return top 2-4 analyses
        return prioritized_analyses[:4]
    
    def _analyze_user_intent(self, user_query: str) -> List[AnalysisType]:
        """Analyze user query to determine priority analyses"""
        query_lower = user_query.lower()
        
        intent_map = {
            'trend': AnalysisType.TREND,
            'momentum': AnalysisType.MOMENTUM,
            'support': AnalysisType.SUPPORT_RESISTANCE,
            'resistance': AnalysisType.SUPPORT_RESISTANCE,
            'rsi': AnalysisType.RSI,
            'overbought': AnalysisType.RSI,
            'oversold': AnalysisType.RSI,
            'volatility': AnalysisType.VOLATILITY,
            'volatile': AnalysisType.VOLATILITY
        }
        
        detected_intents = []
        for keyword, analysis_type in intent_map.items():
            if keyword in query_lower:
                detected_intents.append(analysis_type)
        
        # Default priority order if no specific intent detected
        if not detected_intents:
            return [AnalysisType.PRICE, AnalysisType.TREND, AnalysisType.MOMENTUM, AnalysisType.SUPPORT_RESISTANCE]
        
        return detected_intents


class ResponseFormatter:
    """Format analysis responses with proper structure and validation"""
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.ResponseFormatter")
    
    def format_analysis_response(self,
                                snapshot: MarketSnapshot,
                                selected_analyses: List[AnalysisType],
                                ai_interpretation: str = "") -> str:
        """Format comprehensive analysis response"""
        
        # Debug: inspect snapshot indicators before formatting (non-invasive)
        if self.logger.isEnabledFor(logging.DEBUG):
            try:
                indicators = snapshot.indicators.to_dict() if snapshot and snapshot.indicators else {}
                self.logger.debug(f"AI_PROCESSOR_ROBUST: Formatting analysis response for {getattr(snapshot, 'symbol', 'UNKNOWN')}. Indicators: {indicators}")
            except Exception as e:
                self.logger.debug(f"AI_PROCESSOR_ROBUST: Failed to log snapshot indicators: {e}")
        
        sections = []
        
        # Header
        sections.append(f"**Technical Analysis: {snapshot.symbol}**")
        sections.append(f"*Analysis generated at {snapshot.timestamp.strftime('%Y-%m-%d %H:%M:%S')}*")
        sections.append("")
        
        # Current market data
        sections.append("**Current Market Data**")
        sections.append(f"**Price:** ${snapshot.current_price:.2f}")
        
        change_indicator = "📈" if snapshot.change_percent >= 0 else "📉"
        sections.append(f"**Change:** {change_indicator} {snapshot.change_percent:+.2f}%")
        sections.append(f"**Volume:** {snapshot.volume:,}")
        sections.append("")
        
        # Technical indicators (locked values)
        sections.append("## Technical Indicators")
        indicators_dict = snapshot.indicators.to_dict()
        
        for indicator, value in indicators_dict.items():
            formatted_name = indicator.replace('_', ' ').title()
            
            if isinstance(value, list) and value:
                formatted_values = [f"${v:.2f}" for v in value]
                sections.append(f"**{formatted_name}:** {', '.join(formatted_values)}")
            elif isinstance(value, dict):
                formatted_dict = ", ".join([f"{k}: {v:.2f}" for k, v in value.items()])
                sections.append(f"**{formatted_name}:** {formatted_dict}")
            elif isinstance(value, (int, float)):
                if 'price' in indicator or 'level' in indicator or 'sma' in indicator or 'bollinger' in indicator:
                    sections.append(f"**{formatted_name}:** ${value:.2f}")
                else:
                    sections.append(f"**{formatted_name}:** {value:.2f}")
        
        sections.append("")
        
        # Analysis interpretations
        sections.append("## Analysis")
        
        for analysis_type in selected_analyses:
            interpretation = self._generate_analysis_section(snapshot, analysis_type)
            if interpretation:
                sections.append(interpretation)
                sections.append("")
        
        # AI interpretation if provided
        if ai_interpretation and ai_interpretation.strip():
            sections.append("## AI Interpretation")
            sections.append(ai_interpretation.strip())
            sections.append("")
        
        # Data quality and disclaimer
        sections.append("## Analysis Metadata")
        sections.append(f"**Data Quality:** {snapshot.data_quality.value.title()}")
        sections.append(f"**Analyses Performed:** {', '.join([a.value.replace('_', ' ').title() for a in selected_analyses])}")
        sections.append("")
        sections.append("*This analysis is based on calculated technical indicators. Past performance does not guarantee future results.*")
        
        return "\n".join(sections)
    
    def _generate_analysis_section(self, snapshot: MarketSnapshot, analysis_type: AnalysisType) -> str:
        """Generate specific analysis section based on locked data"""
        
        if analysis_type == AnalysisType.PRICE:
            return self._price_analysis(snapshot)
        elif analysis_type == AnalysisType.TREND:
            return self._trend_analysis(snapshot)
        elif analysis_type == AnalysisType.MOMENTUM:
            return self._momentum_analysis(snapshot)
        elif analysis_type == AnalysisType.SUPPORT_RESISTANCE:
            return self._support_resistance_analysis(snapshot)
        elif analysis_type == AnalysisType.RSI:
            return self._rsi_analysis(snapshot)
        elif analysis_type == AnalysisType.VOLATILITY:
            return self._volatility_analysis(snapshot)
        
        return ""
    
    def _price_analysis(self, snapshot: MarketSnapshot) -> str:
        """Generate price analysis section"""
        sections = ["### Price Analysis"]
        
        if snapshot.change_percent > 0:
            sections.append(f"The stock is currently up {snapshot.change_percent:.2f}% from the previous close.")
        elif snapshot.change_percent < 0:
            sections.append(f"The stock is currently down {abs(snapshot.change_percent):.2f}% from the previous close.")
        else:
            sections.append("The stock is trading flat from the previous close.")
        
        return "\n".join(sections)
    
    def _trend_analysis(self, snapshot: MarketSnapshot) -> str:
        """Generate trend analysis section"""
        sections = ["### Trend Analysis"]
        
        sma_20 = snapshot.indicators.sma_20
        sma_50 = snapshot.indicators.sma_50
        current_price = snapshot.current_price
        
        if sma_20 and sma_50:
            if sma_20 > sma_50:
                trend = "bullish"
            else:
                trend = "bearish"
            
            if current_price > sma_20:
                position = "above"
            else:
                position = "below"
            
            sections.append(f"The 20-day SMA (${sma_20:.2f}) is {trend} relative to the 50-day SMA (${sma_50:.2f}).")
            sections.append(f"Current price is {position} the 20-day moving average.")
        
        return "\n".join(sections)
    
    def _momentum_analysis(self, snapshot: MarketSnapshot) -> str:
        """Generate momentum analysis section"""
        sections = ["### Momentum Analysis"]
        
        rsi = snapshot.indicators.rsi
        if rsi:
            if rsi >= 70:
                condition = "overbought"
                implication = "potentially due for a pullback"
            elif rsi <= 30:
                condition = "oversold"
                implication = "potentially due for a bounce"
            else:
                condition = "neutral"
                implication = "showing balanced momentum"
            
            sections.append(f"RSI is {rsi:.1f}, indicating the stock is {condition} and {implication}.")
        
        return "\n".join(sections)
    
    def _support_resistance_analysis(self, snapshot: MarketSnapshot) -> str:
        """Generate support/resistance analysis section"""
        sections = ["### Support & Resistance Analysis"]
        
        support_levels = snapshot.indicators.support_levels
        resistance_levels = snapshot.indicators.resistance_levels
        current_price = snapshot.current_price
        
        if support_levels:
            nearest_support = max([level for level in support_levels if level < current_price], default=min(support_levels))
            sections.append(f"Key support level identified at ${nearest_support:.2f}.")
        
        if resistance_levels:
            nearest_resistance = min([level for level in resistance_levels if level > current_price], default=max(resistance_levels))
            sections.append(f"Key resistance level identified at ${nearest_resistance:.2f}.")
        
        return "\n".join(sections)
    
    def _rsi_analysis(self, snapshot: MarketSnapshot) -> str:
        """Generate detailed RSI analysis section"""
        sections = ["### RSI Analysis"]
        
        rsi = snapshot.indicators.rsi
        if rsi:
            sections.append(f"Current RSI: {rsi:.1f}")
            
            if rsi >= 80:
                sections.append("RSI indicates extremely overbought conditions - high probability of reversal.")
            elif rsi >= 70:
                sections.append("RSI indicates overbought conditions - consider taking profits.")
            elif rsi <= 20:
                sections.append("RSI indicates extremely oversold conditions - potential buying opportunity.")
            elif rsi <= 30:
                sections.append("RSI indicates oversold conditions - watch for reversal signals.")
            else:
                sections.append("RSI is in neutral territory - no extreme conditions present.")
        
        return "\n".join(sections)
    
    def _volatility_analysis(self, snapshot: MarketSnapshot) -> str:
        """Generate volatility analysis section"""
        sections = ["### Volatility Analysis"]
        
        bb_upper = snapshot.indicators.bollinger_upper
        bb_lower = snapshot.indicators.bollinger_lower
        current_price = snapshot.current_price
        
        if bb_upper and bb_lower:
            band_width = bb_upper - bb_lower
            middle = (bb_upper + bb_lower) / 2
            
            sections.append(f"Bollinger Bands: ${bb_lower:.2f} - ${bb_upper:.2f}")
            
            if current_price >= bb_upper:
                sections.append("Price is at or above the upper Bollinger Band, indicating high volatility and potential overbought conditions.")
            elif current_price <= bb_lower:
                sections.append("Price is at or below the lower Bollinger Band, indicating high volatility and potential oversold conditions.")
            else:
                sections.append("Price is within the Bollinger Bands, indicating normal volatility levels.")
        
        return "\n".join(sections)


class RobustFinancialAnalyzer:
    """Main analyzer class that coordinates all components"""
    
    def __init__(self):
        logger.info("DEBUG: Initializing RobustFinancialAnalyzer - checking pandas")
        try:
            import pandas as pd
            logger.info(f"DEBUG: Pandas available in RobustFinancialAnalyzer - version: {pd.__version__ if hasattr(pd, '__version__') else 'unknown'}")
        except (ImportError, NameError) as pd_err:
            logger.error(f"DEBUG: Pandas error in RobustFinancialAnalyzer: {pd_err}")
            raise
        
        self.calculator = TechnicalCalculator()
        self.selector = AnalysisSelector()
        self.formatter = ResponseFormatter()
        self.logger = logging.getLogger(__name__)
        logger.info("DEBUG: RobustFinancialAnalyzer init complete")
    
    async def analyze(self,
                     symbol: str,
                     market_data: Dict[str, Any],
                     user_query: str = "") -> str:
        """
        Main analysis method

        Args:
            symbol: Stock symbol to analyze
            market_data: Raw market data dictionary
            user_query: User's specific question/request

        Returns:
            Formatted analysis response
        """
        try:
            # Create market snapshot with calculated indicators
            snapshot = await self._create_market_snapshot(symbol, market_data)

            if not snapshot.is_valid():
                return self._generate_insufficient_data_response(symbol)

            # Select appropriate analyses
            selected_analyses = self.selector.select_analyses(snapshot, user_query)

            # Generate formatted response
            response = self.formatter.format_analysis_response(
                snapshot,
                selected_analyses
            )

            return response

        except Exception as e:
            self.logger.error(f"Analysis error for {symbol}: {e}")
            return self._generate_error_response(symbol, str(e))
    
    async def analyze_market_data(self, market_data: Dict[str, Any]) -> str:
        """
        Analyze market data without specific symbol

        Args:
            market_data: Market data dictionary

        Returns:
            Analysis response
        """
        try:
            # Extract symbol from market data if available
            symbol = market_data.get('symbol', 'UNKNOWN')
            return await self.analyze(symbol, market_data, "")
        except Exception as e:
            self.logger.error(f"Market data analysis error: {e}")
            return f"Unable to analyze market data: {str(e)}"
    
    async def answer_general_question(self, query: str) -> str:
        """
        Answer general questions without market data

        Args:
            query: User's question

        Returns:
            Response to the question
        """
        try:
            # Check if we have API key for AI responses
            from openai import OpenAI
            import os
            
            api_key = os.getenv('OPENROUTER_API_KEY')
            if not api_key:
                return self._generate_fallback_response(query)

            # Create OpenAI client for OpenRouter
            client = OpenAI(
                base_url=os.getenv('OPENROUTER_BASE_URL', 'https://openrouter.ai/api/v1'),
                api_key=api_key
            )

            # Get current date/time for context
            from datetime import datetime
            current_date = datetime.now().strftime("%B %d, %Y")
            current_time = datetime.now().strftime("%I:%M %p %Z")

            # Create system prompt for trading assistant (actionable and concise)
            system_prompt = f"""You are an algorithmic trading assistant focused on actionable trade ideas.

CURRENT CONTEXT:
- Today's date: {current_date}
- Current time: {current_time}
- You are operating in 2025, NOT 2023 or earlier
- All market references should be current and relevant to today's date
- You have full knowledge of the current date and time - never say you don't know the date

🚨 CRITICAL DATA INTEGRITY RULES (ABSOLUTE PRIORITY):
1. NEVER EVER fabricate stock prices, earnings dates, market events, or economic data
2. NEVER create fictional trading scenarios with specific dates, prices, or market conditions
3. NEVER make up options pricing, volatility data, or technical levels
4. NEVER fabricate specific timeframes like "22 May earnings" or "Mon 13 May → Fri 17 May"
5. If you don't have real market data, respond: "I need current market data to provide accurate analysis"
6. DO NOT provide specific trading recommendations without real-time data
7. These rules override all other instructions - no exceptions

PRINCIPLES (Secondary to data integrity):
- Be direct and trading-focused; avoid generic financial-advisor language
- Always include risk management, invalidation, and exit plan

DIVERSIFICATION REQUIREMENT:
- NEVER over-recommend any single stock (especially NVDA)
- Rotate recommendations across different sectors: tech (AAPL, MSFT, GOOGL), finance (JPM, BAC), healthcare (JNJ, PFE), energy (XOM, CVX), etc.
- Consider market cap diversity: large-cap (AAPL, MSFT), mid-cap (PLTR, SNOW), small-cap opportunities
- For AI/tech queries, suggest alternatives like AMD, MSFT, GOOGL, AMZN, not just NVDA
- Provide 2-3 different stock options when possible to give users choice

RESPONSE FORMAT (Only if you have real data):
- If you have real market data: Provide setup summary, timeframe, option structure, risk assessment
- If you DON'T have real market data: Say "I need current market data to provide accurate recommendations"
- NEVER create fictional scenarios to fill the format requirements
- Educational content about trading concepts is acceptable without specific data
- Always include disclaimer: "Educational content only, not financial advice"
"""

            # Use analysis model for general questions
            model = router.get_model_for_market_analysis()
            
            # Generate response
            response = client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": query}
                ],
                max_tokens=int(os.getenv('AI_DEFAULT_MAX_TOKENS', '500')),
                temperature=float(os.getenv('AI_DEFAULT_TEMPERATURE', '0.7'))
            )

            return response.choices[0].message.content

        except Exception as e:
            self.logger.error(f"Error answering general question: {e}")
            return self._generate_fallback_response(query)

    def _generate_fallback_response(self, query: str) -> str:
        """Generate a fallback response for general questions"""
        query_lower = query.lower()

        # Get random stock examples to avoid always suggesting the same ones
        import random
        example_stocks = ["$AAPL", "$MSFT", "$GOOGL", "$AMZN", "$TSLA", "$NVDA", "$AMD", "$META"]
        random_examples = random.sample(example_stocks, 2)

        if any(word in query_lower for word in ['hello', 'hi', 'hey', 'good morning', 'good afternoon']):
            return f"Hello! I'm your trading assistant. I can help you with market analysis, stock prices, and trading insights. Try asking about {random_examples[0]} or {random_examples[1]}!"

        elif any(word in query_lower for word in ['help', 'commands', 'what can you do']):
            return f"I can help you with:\n• Stock analysis and prices\n• Market trends and indicators\n• Trading strategies\n• Technical analysis\n\nJust ask me about any stock like {random_examples[0]} or {random_examples[1]}!"

        elif any(word in query_lower for word in ['name', 'who are you', 'about yourself']):
            return f"I'm your AI trading assistant, designed to help you with market analysis and trading decisions. Try asking about {random_examples[0]} or {random_examples[1]}!"

        else:
            return f"I understand you're asking about: '{query}'. I'm here to help with trading and market analysis. Try asking about specific stocks like {random_examples[0]} or {random_examples[1]}!"
    
    async def _create_market_snapshot(self, symbol: str, market_data: Dict[str, Any]) -> MarketSnapshot:
        """Create market snapshot with calculated indicators"""
        
        # Calculate technical indicators
        indicators = self.calculator.calculate_indicators(market_data)
        
        # Assess data quality
        data_quality = self._assess_data_quality(market_data, indicators)
        
        # Debug: log the values used to create the MarketSnapshot (non-invasive)
        if logger.isEnabledFor(logging.DEBUG):
            try:
                # Try to determine price_data length safely
                price_data = market_data.get('price_data') if isinstance(market_data, dict) else None
                price_len = 0
                if isinstance(price_data, dict):
                    price_len = len(price_data.get('close', [])) if price_data.get('close') else 0
                current_price_val = market_data.get('current_price', market_data.get('price', 0))
                change_val = market_data.get('change_percent', market_data.get('change', 0))
                volume_val = market_data.get('volume', 0)
                logger.debug(
                    f"AI_PROCESSOR_ROBUST: Creating MarketSnapshot for {symbol}. "
                    f"Price: {current_price_val}, Change: {change_val}, Volume: {volume_val}, Price data length: {price_len}"
                )
            except Exception as e:
                logger.debug(f"AI_PROCESSOR_ROBUST: Failed to log MarketSnapshot inputs: {e}")
        
        return MarketSnapshot(
            symbol=symbol,
            current_price=market_data.get('current_price', 0),
            change_percent=market_data.get('change_percent', 0),
            volume=market_data.get('volume', 0),
            timestamp=datetime.now(),
            indicators=indicators,
            data_quality=data_quality
        )
    
    def _assess_data_quality(self, market_data: Dict[str, Any], indicators: TechnicalIndicators) -> DataQuality:
        """Assess overall data quality"""
        
        # Check basic data availability
        if not market_data.get('current_price') or market_data.get('current_price') <= 0:
            return DataQuality.INSUFFICIENT
        
        # Count available indicators
        available_indicators = len(indicators.to_dict())
        
        # Check historical data
        price_data = market_data.get('price_data', {})
        historical_points = len(price_data.get('close', []))
        
        if available_indicators >= 4 and historical_points >= 50:
            return DataQuality.HIGH
        elif available_indicators >= 2 and historical_points >= 20:
            return DataQuality.MEDIUM
        elif available_indicators >= 1 and historical_points >= 5:
            return DataQuality.LOW
        else:
            return DataQuality.INSUFFICIENT
    
    def _generate_insufficient_data_response(self, symbol: str) -> str:
        """Generate response for insufficient data"""
        return f"""
# Technical Analysis: {symbol}

## Insufficient Data

Unable to perform technical analysis for {symbol} due to:
- Missing or invalid price data
- Insufficient historical data points
- Data quality issues

Please verify the symbol and try again later.

*Technical analysis requires adequate historical price data*
"""
    
    def _generate_error_response(self, symbol: str, error: str) -> str:
        """Generate error response"""
        return f"""
# Technical Analysis: {symbol}

## Analysis Error

An error occurred while analyzing {symbol}: {error}

Please try again or contact support if the issue persists.
"""

# Backward compatibility wrapper
class CleanAIProcessor:
    """Wrapper for backward compatibility with existing code"""

    def __init__(self):
        self.analyzer = RobustFinancialAnalyzer()  # Use analyzer directly
        self.logger = logging.getLogger(__name__)
    
    async def process_query(self, query: str, context=None) -> 'QueryResult':
        """Process query and return QueryResult for backward compatibility"""
        try:
            # Just let the AI handle everything - no regex bullshit
            # Note: context parameter added for compatibility but not used
            return await self._handle_ai_query(query)
            
        except Exception as e:
            self.logger.error(f"Error processing query: {e}")
            return QueryResult(
                response=f"Error processing your request: {str(e)}",
                confidence=0.0
            )
    
    async def analyze_query_intent(self, query: str) -> Optional[Any]:
        """Analyze query intent for the wrapper - this was the missing method!"""
        try:
            result = await self._handle_ai_query(query)
            
            # Return the QueryResult object directly - the wrapper expects an object with attributes
            return result
            
        except Exception as e:
            self.logger.error(f"Error analyzing query intent: {e}")
            return None
    
    async def _get_market_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get market data for a symbol"""
        market_service = None
        try:
            from src.api.data.market_data_service import MarketDataService
            market_service = MarketDataService()

            # Get comprehensive data
            data = await market_service.get_comprehensive_stock_data(symbol)

            # Debug: log raw market data returned by provider (only when debug enabled)
            if logger.isEnabledFor(logging.DEBUG):
                try:
                    logger.debug(f"AI_PROCESSOR_ROBUST: Raw market data received for {symbol}: {data}")
                except Exception:
                    logger.debug("AI_PROCESSOR_ROBUST: Raw market data logging failed")

            if data and data.get('status') == 'success':
                # Add historical price data for technical analysis
                current_price = data.get('current_price', data.get('price', 0))
                if current_price > 0:
                    # Get real historical data from market service
                    try:
                        historical_data = await market_service.get_historical_data(symbol, period="50d")
                        if historical_data and 'data' in historical_data:
                            # Extract close prices from historical data
                            close_prices = [bar.get('close', current_price) for bar in historical_data['data']]
                            if close_prices:
                                data['price_data'] = {'close': close_prices}
                            else:
                                self.logger.warning(f"No historical close prices found for {symbol}")
                        else:
                            self.logger.warning(f"No historical data available for {symbol}")
                    except Exception as e:
                        self.logger.error(f"Failed to fetch historical data for {symbol}: {e}")

                return data
            else:
                # Fallback to basic price data
                price_data = await market_service.get_current_price(symbol)
                if price_data:
                    current_price = price_data.get('price', 0)
                    # Try to get real historical data
                    try:
                        historical_data = await market_service.get_historical_data(symbol, period="50d")
                        close_prices = []
                        if historical_data and 'data' in historical_data:
                            close_prices = [bar.get('close', current_price) for bar in historical_data['data']]

                        return {
                            'status': 'success',
                            'current_price': current_price,
                            'change_percent': price_data.get('change', 0),
                            'volume': price_data.get('volume', 0),
                            'price_data': {'close': close_prices} if close_prices else {}
                        }
                    except Exception as e:
                        self.logger.error(f"Failed to fetch historical data for fallback: {e}")
                        return {
                            'status': 'success',
                            'current_price': current_price,
                            'change_percent': price_data.get('change', 0),
                            'volume': price_data.get('volume', 0),
                            'price_data': {}
                        }

            return None

        except Exception as e:
            self.logger.error(f"Error getting market data for {symbol}: {e}")
            return None
        finally:
            # Clean up sessions to prevent memory leaks
            if market_service and hasattr(market_service, 'data_source_manager'):
                try:
                    await market_service.data_source_manager.close()
                except Exception as e:
                    self.logger.warning(f"Error closing market service sessions: {e}")
    
    def _generate_mock_historical_data(self, current_price: float) -> List[float]:
        """
        DEPRECATED: Generate mock historical price data for technical analysis
        This method is deprecated and should not be used in production.
        Use real historical data from market service instead.
        """
        import os

        # Only allow mock data in development/test environments
        if os.getenv('ENVIRONMENT', 'production').lower() in ['development', 'dev', 'test', 'testing']:
            import random
            self.logger.warning(f"Using mock historical data for development/test environment")

            # Generate 50 days of historical data with realistic price movement
            prices = []
            price = current_price

            for i in range(50):
                # Random walk with slight upward bias
                change_percent = random.uniform(-0.03, 0.03)  # -3% to +3% daily change
                price = price * (1 + change_percent)
                prices.append(round(price, 2))

            return prices
        else:
            # In production, return empty list and log warning
            self.logger.error("Attempted to use mock historical data in production environment")
            return []
    
    async def _handle_general_question(self, query: str) -> 'QueryResult':
        """Handle general questions that don't involve stock symbols"""
        try:
            # Check if we have API key for AI responses
            import os
            api_key = os.getenv('OPENROUTER_API_KEY', '')
            
            if api_key:
                # Use AI client for general questions
                from openai import OpenAI
                
                client = OpenAI(
                    api_key=api_key,
                    base_url=os.getenv('OPENROUTER_BASE_URL', 'https://openrouter.ai/api/v1')
                )

                # Check if we need to search the web for current information
                web_context = ""
                if self._needs_web_search(query):
                    web_context = await self._search_web(query)
                
                # Get current date/time for context
                from datetime import datetime
                current_date = datetime.now().strftime("%B %d, %Y")
                current_time = datetime.now().strftime("%I:%M %p %Z")

                # Create a general assistant prompt
                system_prompt = f"""You are a helpful financial assistant. Provide accurate, educational responses about trading, markets, and finance.

CURRENT CONTEXT:
- Today's date: {current_date}
- Current time: {current_time}
- You are operating in 2025, NOT 2023 or earlier
- You have full knowledge of the current date and time - never say you don't know the date

CRITICAL CONSTRAINTS - ABSOLUTELY NO EXCEPTIONS:
1. NEVER EVER generate fake stock prices, market data, or current events
2. NEVER fabricate earnings dates, Fed meetings, economic data, or news events
3. NEVER make up specific price levels, technical analysis, or market movements
4. NEVER create fictional trading scenarios with fake prices and dates
5. If you don't have real-time data, say "I need real market data to provide accurate analysis"
6. DO NOT create elaborate fake market scenarios - users need real information
7. When asked for current market info, respond: "I don't have access to live market data. Please specify a symbol for real-time analysis."

RESPONSE STYLE:
- Provide general educational guidance about trading concepts and risk management
- Explain market principles without making specific predictions
- Direct users to specific stock symbols for real-time analysis when appropriate
- Avoid making up specific numbers, recommendations, or predictions
- If asked about specific stocks, suggest they ask about symbols like $AAPL, $MSFT, $GOOGL for real-time data

ALWAYS be helpful and honest about what you can and cannot provide without real-time data."""
                
                # Include web context if available
                user_message = query
                if web_context:
                    user_message = f"Question: {query}\n\nWeb search results: {web_context}"
                
                # Use analysis model for web-augmented general answers
                model = router.get_model_for_market_analysis()
                
                response = client.chat.completions.create(
                    model=model,
                    messages=[
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": user_message}
                    ],
                    max_tokens=int(os.getenv('AI_DEFAULT_MAX_TOKENS', '500')),
                    temperature=float(os.getenv('AI_DEFAULT_TEMPERATURE', '0.7'))
                )
                
                answer = response.choices[0].message.content
                confidence = 0.8
            else:
                # Fallback to simple rule-based responses
                answer = self._generate_fallback_response(query)
                confidence = 0.5
            
            return QueryResult(
                intent="general_question",
                response=answer,
                confidence=confidence
            )
            
        except Exception as e:
            self.logger.error(f"Error handling general question: {e}")
            return QueryResult(
                intent="general_question",
                response=self._generate_fallback_response(query),
                confidence=0.3
            )
    
    def _generate_fallback_response(self, query: str) -> str:
        """Generate fallback responses for common questions without AI"""
        query_lower = query.lower()

        # Get random stock examples to avoid always suggesting the same ones
        import random
        example_stocks = ["$AAPL", "$MSFT", "$GOOGL", "$AMZN", "$TSLA", "$NVDA", "$AMD", "$META"]
        random_examples = random.sample(example_stocks, 2)

        # Time-related questions
        if any(word in query_lower for word in ['time', 'clock', 'hour', 'minute']):
            from datetime import datetime
            current_time = datetime.now().strftime("%I:%M %p on %B %d, %Y")
            return f"The current time is {current_time}. I can also provide real-time technical analysis for stocks - just mention a symbol like {random_examples[0]} or {random_examples[1]}."
        
        # Weather questions
        elif any(word in query_lower for word in ['weather', 'temperature', 'rain', 'sunny', 'cloudy']):
            return f"I can't check the weather, but I have access to real-time market data and can provide live technical analysis for stocks! Just mention a symbol like {random_examples[0]} or {random_examples[1]}."

        # Trading-related questions without symbols
        elif any(word in query_lower for word in ['trade', 'trading', 'daytrade', 'stock', 'market', 'bullish', 'bearish', 'recommend']):
            return f"I'm here to help with trading analysis! For real-time technical analysis and specific recommendations, please mention a specific stock symbol like {', '.join(random_examples)}. I can provide detailed analysis once you specify which stock you'd like me to analyze."

        # Music questions
        elif any(word in query_lower for word in ['music', 'song', 'playlist', 'audio']):
            return f"I can't help with music recommendations, but I can analyze stock charts! Try asking about {random_examples[0]} or {random_examples[1]} for technical analysis."

        # General greeting
        elif any(word in query_lower for word in ['hello', 'hi', 'hey', 'greetings']):
            return f"Hello! I'm your trading assistant. I can provide real-time technical analysis and market insights. For specific analysis and recommendations, please mention a stock symbol like {', '.join(random_examples)}."

        # Default response
        else:
            return f"I'm a financial analysis assistant. For technical analysis of stocks, please mention a symbol like {random_examples[0]} or {random_examples[1]}. For other questions, I have limited capabilities."
    
    def _needs_web_search(self, query: str) -> bool:
        """Determine if a query needs web search for current information"""
        query_lower = query.lower()
        
        # Keywords that indicate need for current information
        current_info_keywords = [
            'weather', 'temperature', 'forecast', 'rain', 'sunny', 'cloudy',
            'news', 'latest', 'current', 'today', 'now', 'recent',
            'crypto', 'bitcoin', 'ethereum', 'cryptocurrency',
            'market', 'trading', 'stocks', 'nasdaq', 'dow', 'sp500',
            'election', 'politics', 'government', 'economy',
            'sports', 'game', 'score', 'match', 'team',
            'movie', 'film', 'show', 'entertainment',
            'reddit', 'wallstreetbets', 'wsb', 'social media', 'twitter',
            'discord', 'trending', 'viral', 'meme', 'sentiment'
        ]
        
        return any(keyword in query_lower for keyword in current_info_keywords)
    
    def _needs_market_data(self, query: str) -> bool:
        """Determine if a query needs current market data"""
        query_lower = query.lower()
        
        # Keywords that indicate need for market data
        market_keywords = [
            'daytrade', 'day trade', 'trading', 'trade', 'stock', 'stocks',
            'market', 'nasdaq', 'dow', 'sp500', 's&p', 'crypto', 'bitcoin',
            'ethereum', 'buy', 'sell', 'hold', 'investment', 'invest',
            'portfolio', 'analysis', 'technical', 'chart', 'price', 'volume'
        ]
        
        return any(keyword in query_lower for keyword in market_keywords)
    
    async def _get_market_context(self) -> str:
        """Get current market context for trading questions"""
        try:
            # Get some popular stocks for context
            popular_symbols = ['NVDA', 'AAPL', 'TSLA', 'MSFT', 'GOOGL']
            market_data = []
            
            for symbol in popular_symbols[:3]:  # Limit to 3 to avoid rate limits
                try:
                    data = await self._get_market_data(symbol)
                    if data and data.get('status') == 'success':
                        price = data.get('current_price', 0)
                        change = data.get('change_percent', 0)
                        market_data.append(f"{symbol}: ${price:.2f} ({change:+.2f}%)")
                except Exception as e:
                    self.logger.warning(f"Could not get data for {symbol}: {e}")
                    continue
            
            if market_data:
                return f"Current market snapshot: {', '.join(market_data)}"
            else:
                return "Market data temporarily unavailable"
                
        except Exception as e:
            self.logger.error(f"Error getting market context: {e}")
            return "Market data temporarily unavailable"
    
    async def _search_web(self, query: str) -> str:
        """Search the web for current information"""
        try:
            import httpx
            import json
            
            # Use DuckDuckGo's instant answer API for general info
            search_url = os.getenv('WEB_SEARCH_URL', 'https://api.duckduckgo.com/')
            params = {
                'q': query,
                'format': 'json',
                'no_html': '1',
                'skip_disambig': '1'
            }
            
            timeout = float(os.getenv('AI_DEFAULT_TIMEOUT_MS', '15000')) / 1000.0
            async with httpx.AsyncClient(timeout=timeout) as client:
                response = await client.get(search_url, params=params)
                
                if response.status_code == 200:
                    data = response.json()
                    
                    # Extract relevant information
                    result_text = ""
                    
                    if data.get('Abstract'):
                        result_text += f"Summary: {data['Abstract']}\n"
                    
                    if data.get('AbstractText'):
                        result_text += f"Details: {data['AbstractText']}\n"
                    
                    if data.get('Answer'):
                        result_text += f"Answer: {data['Answer']}\n"
                    
                    if data.get('RelatedTopics'):
                        topics = data['RelatedTopics'][:5]  # Get more topics
                        for topic in topics:
                            if isinstance(topic, dict) and topic.get('Text'):
                                result_text += f"Related: {topic['Text']}\n"
                    
                    # Add a note about web access
                    if result_text:
                        result_text += "\n[Note: I have full internet access and can browse Reddit, social media, and any website you need.]"
                    
                    return result_text if result_text else "I have full internet access and can search for any information you need."
                else:
                    return "I have full internet access and can search for any information you need."
                    
        except Exception as e:
            self.logger.error(f"Web search error: {e}")
            return "I have full internet access and can search for any information you need."
    
    async def _handle_ai_query(self, query: str) -> 'QueryResult':
        """Let AI intelligently handle everything - no regex bullshit!"""
        try:
            import os
            api_key = os.getenv('OPENROUTER_API_KEY', '')

            if not api_key:
                return QueryResult(
                    response="AI service not available. Please try again later.",
                    confidence=0.0
                )

            from openai import OpenAI
            client = OpenAI(api_key=api_key, base_url=os.getenv('OPENROUTER_BASE_URL', 'https://openrouter.ai/api/v1'))

            # Let AI analyze the query and decide what to do
            analysis_prompt = f"""Analyze this user query and respond with a JSON object:

Query: "{query}"

Determine:
1. Is this asking about stock prices, market data, trading, or financial analysis?
2. If yes, extract any stock symbols mentioned (like GME, AAPL, $TSLA, etc.)
3. Should we fetch real market data to answer this properly?

IMPORTANT: Be PROACTIVE - if the query mentions trading, stocks, bullish, bearish, day trading, or any financial terms, set needs_real_data to true and extract relevant symbols.

Examples that should trigger stock_analysis:
- "what stock are you extremely bullish on"
- "best stocks for day trading"
- "NVDA analysis"
- "trading recommendations"
- "market outlook"
- "bullish picks"

Respond with JSON only:
{{
    "intent": "stock_analysis" or "general_question",
    "symbols": ["SYMBOL1", "SYMBOL2"] or [],
    "needs_real_data": true or false,
    "confidence": 0.0-1.0
}}

Only include symbols you are confident about (confidence > 0.7)."""

            # Use fast, instruction-following model for JSON analysis
            model = router.get_model_for_query_analysis()

            # Get AI analysis
            analysis_response = client.chat.completions.create(
                model=model,
                messages=[{"role": "user", "content": analysis_prompt}],
                temperature=0.1,
                max_tokens=200
            )

            # Parse AI analysis
            import json
            raw_response = analysis_response.choices[0].message.content.strip()

            # Remove markdown code blocks if present
            if raw_response.startswith('```json'):
                raw_response = raw_response[7:]  # Remove ```json
            if raw_response.startswith('```'):
                raw_response = raw_response[3:]   # Remove ```
            if raw_response.endswith('```'):
                raw_response = raw_response[:-3]  # Remove trailing ```

            try:
                analysis = json.loads(raw_response.strip())
                self.logger.info(f"AI Analysis: {analysis}")
            except Exception as e:
                self.logger.error(f"JSON parsing failed: {e}, Raw: {repr(raw_response)}")
                # Fallback if JSON parsing fails
                analysis = {
                    "intent": "general_question",
                    "symbols": [],
                    "needs_real_data": False,
                    "confidence": 0.5
                }

            # If AI says we need real market data, get it!
            if analysis.get("needs_real_data"):
                # If no specific symbols mentioned, suggest popular stocks for analysis
                symbols = analysis.get("symbols", [])
                if not symbols:
                    # For general trading queries, provide diverse stock suggestions
                    # Rotate the order to avoid always defaulting to the same stock
                    import random
                    stock_pool = ["AAPL", "MSFT", "GOOGL", "AMZN", "TSLA", "NVDA", "AMD", "META"]
                    symbols = random.sample(stock_pool, min(4, len(stock_pool)))

                return await self._handle_stock_query_with_real_data(query, symbols)

            # Get current date/time for context
            from datetime import datetime
            current_date = datetime.now().strftime("%B %d, %Y")
            current_time = datetime.now().strftime("%I:%M %p %Z")

            # Otherwise, let AI respond normally with helpful guidance
            response_prompt = f"""You are a helpful financial assistant. Provide accurate, educational responses about trading, markets, and finance.

CURRENT CONTEXT:
- Today's date: {current_date}
- Current time: {current_time}
- You are operating in 2025, NOT 2023 or earlier
- You have full knowledge of the current date and time - never say you don't know the date

CRITICAL CONSTRAINTS - ABSOLUTELY NO EXCEPTIONS:
1. NEVER EVER generate fake stock prices, market data, or current events
2. NEVER fabricate earnings dates, Fed meetings, economic data, or news events
3. NEVER make up specific price levels, technical analysis, or market movements
4. NEVER create fictional trading scenarios with fake prices and dates
5. If you don't have real-time data, say "I need real market data to provide accurate analysis"
6. DO NOT create elaborate fake market scenarios - users need real information
7. When asked for current market info, respond: "I don't have access to live market data. Please specify a symbol for real-time analysis."

RESPONSE GUIDELINES:
- Provide general educational guidance about trading concepts and risk management
- Explain market principles without making specific predictions
- Direct users to specific stock symbols for real-time analysis when appropriate
- Be informative about market concepts without making up specific numbers
- If asked about specific stocks, suggest they ask about symbols like $AAPL, $MSFT, $GOOGL for real-time data

ALWAYS be helpful and honest about what you can and cannot provide without real-time data."""

            response = client.chat.completions.create(
                model=model,
                messages=[{"role": "user", "content": response_prompt}],
                temperature=0.7,
                max_tokens=2000
            )

            answer = response.choices[0].message.content.strip()

            return QueryResult(
                intent=analysis.get("intent", "general_question"),
                symbols=analysis.get("symbols", []),
                needs_data=analysis.get("needs_real_data", False),
                response=answer,
                confidence=analysis.get("confidence", 0.8)
            )

        except Exception as e:
            self.logger.error(f"Error in AI query: {e}")
            return QueryResult(
                response=f"Error processing your request: {str(e)}",
                confidence=0.0
            )

    async def _handle_stock_query_with_real_data(self, query: str, symbols: List[str]) -> 'QueryResult':
        """Handle stock queries with REAL market data"""
        try:
            # Get real market data for multiple symbols with timeout
            market_data_results = []
            successful_symbols = []
            
            for symbol in symbols[:2]:  # Limit to 2 symbols to avoid timeouts
                try:
                    # Add timeout for individual symbol data fetching
                    fetched = await asyncio.wait_for(
                        self._get_market_data(symbol.upper()),
                        timeout=5.0  # 5 second timeout per symbol
                    )
                    if fetched and fetched.get('status') == 'success':
                        market_data_results.append({
                            'symbol': symbol.upper(),
                            'data': fetched
                        })
                        successful_symbols.append(symbol.upper())
                except asyncio.TimeoutError:
                    self.logger.warning(f"Timeout getting data for {symbol}")
                    continue
                except Exception as e:
                    self.logger.warning(f"Failed to get data for {symbol}: {e}")
                    continue
            
            if market_data_results:
                # Convert list-of-results into a canonical mapping {SYMBOL: data}
                data_map: Dict[str, Dict[str, Any]] = {}
                for item in market_data_results:
                    s = item.get('symbol')
                    d = item.get('data')
                    if s and d:
                        data_map[s] = d
                
                # Use the analyzer to generate comprehensive analysis
                analysis_responses = []
                for symbol_key, data in data_map.items():
                    analysis = await self.analyzer.analyze(symbol_key, data, query)
                    analysis_responses.append(f"## {symbol_key} Analysis\n{analysis}")
                
                analysis_response = "\n\n".join(analysis_responses)
                
                return QueryResult(
                    intent="stock_analysis",
                    symbols=successful_symbols,
                    needs_data=True,
                    response=analysis_response,
                    confidence=0.95,
                    data=data_map
                )
            else:
                # Fallback to conservative response when no data is available
                return QueryResult(
                    intent="general_question",
                    symbols=[],
                    needs_data=False,
                    response="I'm here to help with trading analysis! For real-time technical analysis and specific recommendations, please mention a specific stock symbol like $NVDA, $AAPL, $TSLA, or $AMD. I can provide detailed analysis once you specify which stock you'd like me to analyze.",
                    confidence=0.8
                )

        except Exception as e:
            self.logger.error(f"Error handling stock query: {e}")
            return QueryResult(
                intent="general_question",
                symbols=[],
                needs_data=False,
                response="I'm here to help with trading analysis! For real-time technical analysis and specific recommendations, please mention a specific stock symbol like $NVDA, $AAPL, $TSLA, or $AMD. I can provide detailed analysis once you specify which stock you'd like me to analyze.",
                confidence=0.7
            )


@dataclass
class QueryResult:
    """Backward compatibility QueryResult"""
    intent: str = "general_question"
    symbols: List[str] = None
    needs_data: bool = False
    response: str = ""
    confidence: float = 0.0
    data: Dict[str, Any] = None

    def __post_init__(self):
        if self.symbols is None:
            self.symbols = []

    def get(self, key: str, default=None):
        """Dictionary-like get method for backward compatibility"""
        return getattr(self, key, default)

    def __getitem__(self, key: str):
        """Dictionary-like access for backward compatibility"""
        return getattr(self, key)

    def __setitem__(self, key: str, value):
        """Dictionary-like assignment for backward compatibility"""
        setattr(self, key, value)

    def __contains__(self, key: str):
        """Dictionary-like 'in' operator for backward compatibility"""
        return hasattr(self, key)


# Factory function for backward compatibility
def create_processor(context=None) -> CleanAIProcessor:
    """Create a processor instance"""
    return CleanAIProcessor()


# Async helper for backward compatibility
async def process_query(query: str, context=None) -> Dict[str, Any]:
    """Process query and return dict for backward compatibility"""
    processor = create_processor(context)
    result = await processor.process_query(query)
    return {
        'response': result.response,
        'intent': result.intent,
        'symbols': result.symbols,
        'needs_data': result.needs_data,
        'confidence': result.confidence,
        'data': result.data
    }


# Example usage and testing
async def main():
    """Example usage of the robust financial analyzer"""
    
    # Mock market data
    mock_data = {
        'current_price': 175.50,
        'change_percent': 2.3,
        'volume': 1500000,
        'price_data': {
            'close': [170, 171, 169, 172, 174, 175.50] * 10  # Mock historical data
        }
    }
    
    analyzer = RobustFinancialAnalyzer()
    
    # Test analysis
    result = await analyzer.analyze("AAPL", mock_data, "What's the trend and momentum like?")
    print(result)


if __name__ == "__main__":
    asyncio.run(main())