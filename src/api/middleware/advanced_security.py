"""
Advanced Security Hardening for Trading Bot
Implements comprehensive security measures and validation
"""

import hashlib
import hmac
import time
import secrets
import asyncio
from typing import Dict, Any, List, Optional, Set, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import ipaddress
import re

from src.shared.error_handling.logging import get_logger

logger = get_logger(__name__)

class SecurityLevel(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class SecurityEvent:
    """Security event for logging and analysis"""
    event_type: str
    severity: SecurityLevel
    user_id: Optional[str]
    ip_address: Optional[str]
    timestamp: datetime
    details: Dict[str, Any]
    action_taken: str = ""

class RateLimitBucket:
    """Advanced rate limiting with multiple time windows"""
    
    def __init__(self, requests_per_minute: int = 60, requests_per_hour: int = 1000):
        self.requests_per_minute = requests_per_minute
        self.requests_per_hour = requests_per_hour
        self.minute_requests: List[datetime] = []
        self.hour_requests: List[datetime] = []
        self.lock = asyncio.Lock()
    
    async def is_allowed(self) -> Tuple[bool, str]:
        """Check if request is allowed"""
        async with self.lock:
            now = datetime.now()
            
            # Clean old requests
            cutoff_minute = now - timedelta(minutes=1)
            cutoff_hour = now - timedelta(hours=1)
            
            self.minute_requests = [req for req in self.minute_requests if req > cutoff_minute]
            self.hour_requests = [req for req in self.hour_requests if req > cutoff_hour]
            
            # Check limits
            if len(self.minute_requests) >= self.requests_per_minute:
                return False, "Rate limit exceeded: too many requests per minute"
            
            if len(self.hour_requests) >= self.requests_per_hour:
                return False, "Rate limit exceeded: too many requests per hour"
            
            # Record request
            self.minute_requests.append(now)
            self.hour_requests.append(now)
            
            return True, ""

class InputValidator:
    """Advanced input validation and sanitization"""
    
    # Dangerous patterns to block
    DANGEROUS_PATTERNS = [
        r'<script[^>]*>.*?</script>',  # Script tags
        r'javascript:',  # JavaScript URLs
        r'on\w+\s*=',  # Event handlers
        r'eval\s*\(',  # eval() calls
        r'exec\s*\(',  # exec() calls
        r'import\s+',  # Import statements
        r'__\w+__',  # Python dunder methods
        r'\.\./',  # Path traversal
        r'[;\|&`$]',  # Command injection chars
    ]
    
    # SQL injection patterns
    SQL_INJECTION_PATTERNS = [
        r'union\s+select',
        r'drop\s+table',
        r'delete\s+from',
        r'insert\s+into',
        r'update\s+.*\s+set',
        r'--\s*$',  # SQL comments
        r'/\*.*\*/',  # SQL block comments
    ]
    
    @classmethod
    def validate_user_input(cls, input_text: str, max_length: int = 1000) -> Tuple[bool, str, str]:
        """
        Validate and sanitize user input
        
        Returns:
            (is_valid, sanitized_text, error_message)
        """
        if not input_text:
            return True, "", ""
        
        # Length check
        if len(input_text) > max_length:
            return False, "", f"Input too long (max {max_length} characters)"
        
        # Check for dangerous patterns
        input_lower = input_text.lower()
        
        for pattern in cls.DANGEROUS_PATTERNS:
            if re.search(pattern, input_lower, re.IGNORECASE):
                return False, "", f"Input contains potentially dangerous content"
        
        for pattern in cls.SQL_INJECTION_PATTERNS:
            if re.search(pattern, input_lower, re.IGNORECASE):
                return False, "", f"Input contains potential SQL injection"
        
        # Sanitize
        sanitized = cls._sanitize_text(input_text)
        
        return True, sanitized, ""
    
    @classmethod
    def _sanitize_text(cls, text: str) -> str:
        """Sanitize text by removing/escaping dangerous characters"""
        # Remove null bytes
        text = text.replace('\x00', '')
        
        # Limit consecutive whitespace
        text = re.sub(r'\s{10,}', ' ', text)
        
        # Remove control characters except newlines and tabs
        text = ''.join(char for char in text if ord(char) >= 32 or char in '\n\t')
        
        return text.strip()
    
    @classmethod
    def validate_symbol(cls, symbol: str) -> Tuple[bool, str]:
        """Validate stock symbol format"""
        if not symbol:
            return False, "Symbol cannot be empty"
        
        # Basic symbol validation
        if not re.match(r'^[A-Z]{1,10}$', symbol.upper()):
            return False, "Invalid symbol format (1-10 uppercase letters only)"
        
        return True, ""
    
    @classmethod
    def validate_discord_id(cls, discord_id: str) -> Tuple[bool, str]:
        """Validate Discord ID format"""
        if not discord_id:
            return False, "Discord ID cannot be empty"
        
        if not re.match(r'^\d{17,20}$', discord_id):
            return False, "Invalid Discord ID format"
        
        return True, ""

class IPSecurityManager:
    """IP-based security management"""
    
    def __init__(self):
        self.blocked_ips: Set[str] = set()
        self.suspicious_ips: Dict[str, List[datetime]] = {}
        self.whitelist_ips: Set[str] = set()
        self.lock = asyncio.Lock()
    
    async def is_ip_allowed(self, ip_address: str) -> Tuple[bool, str]:
        """Check if IP address is allowed"""
        async with self.lock:
            # Check whitelist first
            if ip_address in self.whitelist_ips:
                return True, ""
            
            # Check blocked IPs
            if ip_address in self.blocked_ips:
                return False, "IP address is blocked"
            
            # Check for suspicious activity
            if ip_address in self.suspicious_ips:
                recent_events = [
                    event for event in self.suspicious_ips[ip_address]
                    if datetime.now() - event < timedelta(hours=1)
                ]
                
                if len(recent_events) > 10:  # More than 10 suspicious events in an hour
                    await self.block_ip(ip_address, "Too many suspicious events")
                    return False, "IP address blocked due to suspicious activity"
            
            return True, ""
    
    async def record_suspicious_activity(self, ip_address: str, event_type: str):
        """Record suspicious activity from an IP"""
        async with self.lock:
            if ip_address not in self.suspicious_ips:
                self.suspicious_ips[ip_address] = []
            
            self.suspicious_ips[ip_address].append(datetime.now())
            
            # Clean old events
            cutoff = datetime.now() - timedelta(days=1)
            self.suspicious_ips[ip_address] = [
                event for event in self.suspicious_ips[ip_address] if event > cutoff
            ]
    
    async def block_ip(self, ip_address: str, reason: str):
        """Block an IP address"""
        async with self.lock:
            self.blocked_ips.add(ip_address)
            logger.warning(f"Blocked IP {ip_address}: {reason}")
    
    async def unblock_ip(self, ip_address: str):
        """Unblock an IP address"""
        async with self.lock:
            self.blocked_ips.discard(ip_address)
            logger.info(f"Unblocked IP {ip_address}")

class SecurityAuditLogger:
    """Comprehensive security event logging"""
    
    def __init__(self):
        self.events: List[SecurityEvent] = []
        self.max_events = 10000
        self.lock = asyncio.Lock()
    
    async def log_event(self, event_type: str, severity: SecurityLevel, 
                       user_id: Optional[str] = None, ip_address: Optional[str] = None,
                       details: Dict[str, Any] = None, action_taken: str = ""):
        """Log a security event"""
        async with self.lock:
            event = SecurityEvent(
                event_type=event_type,
                severity=severity,
                user_id=user_id,
                ip_address=ip_address,
                timestamp=datetime.now(),
                details=details or {},
                action_taken=action_taken
            )
            
            self.events.append(event)
            
            # Trim old events
            if len(self.events) > self.max_events:
                self.events = self.events[-self.max_events:]
            
            # Log to standard logger based on severity
            log_message = f"Security Event: {event_type}"
            if user_id:
                log_message += f" (User: {user_id})"
            if ip_address:
                log_message += f" (IP: {ip_address})"
            if action_taken:
                log_message += f" - Action: {action_taken}"
            
            if severity == SecurityLevel.CRITICAL:
                logger.critical(log_message)
            elif severity == SecurityLevel.HIGH:
                logger.error(log_message)
            elif severity == SecurityLevel.MEDIUM:
                logger.warning(log_message)
            else:
                logger.info(log_message)
    
    async def get_recent_events(self, hours: int = 24) -> List[SecurityEvent]:
        """Get recent security events"""
        async with self.lock:
            cutoff = datetime.now() - timedelta(hours=hours)
            return [event for event in self.events if event.timestamp > cutoff]
    
    async def get_events_by_user(self, user_id: str, hours: int = 24) -> List[SecurityEvent]:
        """Get security events for a specific user"""
        async with self.lock:
            cutoff = datetime.now() - timedelta(hours=hours)
            return [
                event for event in self.events 
                if event.user_id == user_id and event.timestamp > cutoff
            ]

class AdvancedSecurityManager:
    """Main security management system"""
    
    def __init__(self):
        self.rate_limiters: Dict[str, RateLimitBucket] = {}
        self.ip_manager = IPSecurityManager()
        self.audit_logger = SecurityAuditLogger()
        self.validator = InputValidator()
        self.session_tokens: Dict[str, datetime] = {}
        self.failed_attempts: Dict[str, List[datetime]] = {}
        self.lock = asyncio.Lock()
    
    async def validate_request(self, user_id: str, ip_address: Optional[str] = None,
                             input_data: Optional[str] = None) -> Tuple[bool, str]:
        """Comprehensive request validation"""
        
        # Check IP if provided
        if ip_address:
            ip_allowed, ip_message = await self.ip_manager.is_ip_allowed(ip_address)
            if not ip_allowed:
                await self.audit_logger.log_event(
                    "blocked_ip_request", SecurityLevel.HIGH,
                    user_id=user_id, ip_address=ip_address,
                    details={"reason": ip_message}
                )
                return False, ip_message
        
        # Rate limiting
        if user_id not in self.rate_limiters:
            self.rate_limiters[user_id] = RateLimitBucket()
        
        rate_allowed, rate_message = await self.rate_limiters[user_id].is_allowed()
        if not rate_allowed:
            await self.audit_logger.log_event(
                "rate_limit_exceeded", SecurityLevel.MEDIUM,
                user_id=user_id, ip_address=ip_address,
                details={"reason": rate_message}
            )
            return False, rate_message
        
        # Input validation
        if input_data:
            input_valid, sanitized, input_message = self.validator.validate_user_input(input_data)
            if not input_valid:
                await self.audit_logger.log_event(
                    "invalid_input", SecurityLevel.HIGH,
                    user_id=user_id, ip_address=ip_address,
                    details={"input_length": len(input_data), "reason": input_message}
                )
                
                # Record suspicious activity
                if ip_address:
                    await self.ip_manager.record_suspicious_activity(ip_address, "invalid_input")
                
                return False, input_message
        
        # Log successful validation
        await self.audit_logger.log_event(
            "request_validated", SecurityLevel.LOW,
            user_id=user_id, ip_address=ip_address
        )
        
        return True, ""
    
    async def record_failed_attempt(self, user_id: str, attempt_type: str, 
                                  ip_address: Optional[str] = None):
        """Record failed authentication/authorization attempt"""
        async with self.lock:
            if user_id not in self.failed_attempts:
                self.failed_attempts[user_id] = []
            
            self.failed_attempts[user_id].append(datetime.now())
            
            # Clean old attempts
            cutoff = datetime.now() - timedelta(hours=1)
            self.failed_attempts[user_id] = [
                attempt for attempt in self.failed_attempts[user_id] if attempt > cutoff
            ]
            
            # Check for brute force
            if len(self.failed_attempts[user_id]) > 5:  # More than 5 failures in an hour
                await self.audit_logger.log_event(
                    "potential_brute_force", SecurityLevel.CRITICAL,
                    user_id=user_id, ip_address=ip_address,
                    details={"attempt_type": attempt_type, "failure_count": len(self.failed_attempts[user_id])},
                    action_taken="User temporarily restricted"
                )
                
                if ip_address:
                    await self.ip_manager.record_suspicious_activity(ip_address, "brute_force")
    
    async def generate_secure_token(self, user_id: str) -> str:
        """Generate secure session token"""
        token_data = f"{user_id}:{time.time()}:{secrets.token_hex(16)}"
        token = hashlib.sha256(token_data.encode()).hexdigest()
        
        async with self.lock:
            self.session_tokens[token] = datetime.now()
        
        await self.audit_logger.log_event(
            "token_generated", SecurityLevel.LOW,
            user_id=user_id
        )
        
        return token
    
    async def validate_token(self, token: str, max_age_hours: int = 24) -> Tuple[bool, str]:
        """Validate session token"""
        async with self.lock:
            if token not in self.session_tokens:
                return False, "Invalid token"
            
            token_age = datetime.now() - self.session_tokens[token]
            if token_age > timedelta(hours=max_age_hours):
                del self.session_tokens[token]
                return False, "Token expired"
            
            return True, ""
    
    async def get_security_report(self) -> Dict[str, Any]:
        """Get comprehensive security report"""
        recent_events = await self.audit_logger.get_recent_events(24)
        
        # Count events by severity
        severity_counts = {}
        for event in recent_events:
            severity = event.severity.value
            severity_counts[severity] = severity_counts.get(severity, 0) + 1
        
        # Count events by type
        type_counts = {}
        for event in recent_events:
            event_type = event.event_type
            type_counts[event_type] = type_counts.get(event_type, 0) + 1
        
        return {
            "total_events_24h": len(recent_events),
            "events_by_severity": severity_counts,
            "events_by_type": type_counts,
            "blocked_ips": len(self.ip_manager.blocked_ips),
            "active_rate_limiters": len(self.rate_limiters),
            "active_sessions": len(self.session_tokens),
            "users_with_failed_attempts": len(self.failed_attempts),
            "last_updated": datetime.now().isoformat()
        }

# Global security manager
security_manager = AdvancedSecurityManager()

async def validate_user_request(user_id: str, input_data: str = None, 
                               ip_address: str = None) -> Tuple[bool, str]:
    """Validate user request with comprehensive security checks"""
    return await security_manager.validate_request(user_id, ip_address, input_data)

async def get_security_status() -> Dict[str, Any]:
    """Get current security status"""
    return await security_manager.get_security_report()
