from fastapi import APIRouter, HTTPException, status
from typing import Dict, Any, Optional
import asyncio
import psutil
import time
from datetime import datetime, timedelta

from src.shared.error_handling.logging import get_logger
from src.core.monitoring_pkg import SystemMonitor
from src.bot.client import TradingBot

logger = get_logger(__name__)
router = APIRouter()

class BotHealthMonitor:
    """Monitor bot health and status"""
    
    def __init__(self):
        self.bot_instance: Optional[TradingBot] = None
        self.last_bot_check = None
        self.bot_status_cache = {}
        self.cache_timeout = 30  # Cache for 30 seconds
    
    def set_bot_instance(self, bot: TradingBot):
        """Set the bot instance for monitoring"""
        self.bot_instance = bot
    
    async def get_bot_status(self) -> Dict[str, Any]:
        """Get comprehensive bot status"""
        try:
            # Check cache first
            if (self.last_bot_check and 
                time.time() - self.last_bot_check < self.cache_timeout):
                return self.bot_status_cache
            
            status_data = {
                "connected": False,
                "guilds": 0,
                "latency": None,
                "commands_registered": 0,
                "uptime": None,
                "mode": "unknown",
                "rate_limiting": {},
                "errors": []
            }
            
            if not self.bot_instance:
                status_data["mode"] = "no_bot_instance"
                status_data["errors"].append("Bot instance not set")
            else:
                bot = self.bot_instance.bot
                
                # Check if bot is ready and connected
                if bot.is_ready() and not bot.is_closed():
                    status_data["connected"] = True
                    status_data["guilds"] = len(bot.guilds)
                    status_data["latency"] = round(bot.latency * 1000, 2) if bot.latency else None
                    status_data["commands_registered"] = len(bot.commands) + len(bot.tree.get_commands())
                    status_data["mode"] = "discord_connected"
                    
                    # Get rate limiter status
                    rate_limiter = self.bot_instance.rate_limiter
                    total_users = len(rate_limiter.user_requests)
                    active_limits = sum(1 for user_data in rate_limiter.user_requests.values() 
                                      if len(user_data) >= rate_limiter.max_requests * 0.8)
                    
                    status_data["rate_limiting"] = {
                        "total_users_tracked": total_users,
                        "users_near_limit": active_limits,
                        "max_requests_per_hour": rate_limiter.max_requests,
                        "time_window_hours": rate_limiter.time_window / 3600
                    }
                else:
                    status_data["mode"] = "degraded"
                    status_data["errors"].append("Bot not connected to Discord")
            
            # Cache the result
            self.bot_status_cache = status_data
            self.last_bot_check = time.time()
            
            return status_data
            
        except Exception as e:
            logger.error(f"Error checking bot status: {e}")
            return {
                "connected": False,
                "mode": "error",
                "errors": [str(e)]
            }

# Global bot health monitor instance
bot_health_monitor = BotHealthMonitor()

@router.get("/health/bot", tags=["Bot"], status_code=status.HTTP_200_OK)
async def bot_health_check() -> Dict[str, Any]:
    """
    Comprehensive bot health check endpoint.
    
    Returns:
        Dict containing bot health status and connection details
    """
    try:
        bot_status = await bot_health_monitor.get_bot_status()
        
        # Add additional system metrics
        system_health = SystemMonitor.health_check()
        
        response = {
            "status": "healthy" if bot_status["connected"] else "degraded",
            "bot": bot_status,
            "system": system_health,
            "timestamp": datetime.utcnow().isoformat(),
            "uptime": {
                "process": get_process_uptime(),
                "system": get_system_uptime()
            }
        }
        
        # Include warnings for degraded states
        if not bot_status["connected"]:
            response["warnings"] = ["Bot is running in degraded mode"]
        
        return response
        
    except Exception as e:
        logger.error(f"Bot health check failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Bot health check failed: {str(e)}"
        )

@router.get("/health/detailed", tags=["System"], status_code=status.HTTP_200_OK)
async def detailed_health_check() -> Dict[str, Any]:
    """
    Detailed health check including all services.
    
    Returns:
        Comprehensive health status for all system components
    """
    try:
        # Get all health checks
        from src.api.routes.health import health_check as system_health
        
        system_data = await system_health()
        bot_data = await bot_health_check()
        
        # Combine and enhance
        combined_health = {
            "overall_status": "healthy",
            "services": {
                "api": system_data.get("services", {}).get("api", "unknown"),
                "bot": bot_data["bot"]["mode"],
                "database": system_data.get("database", {}).get("status", "unknown"),
                "system": system_data.get("status", "unknown")
            },
            "details": {
                "system": system_data,
                "bot": bot_data
            },
            "metrics": {
                "process_uptime": get_process_uptime(),
                "system_uptime": get_system_uptime(),
                "memory_usage": psutil.virtual_memory().percent,
                "cpu_usage": psutil.cpu_percent(interval=1),
                "disk_usage": psutil.disk_usage('/').percent
            },
            "timestamp": datetime.utcnow().isoformat()
        }
        
        # Determine overall status
        critical_services = ["api", "system"]
        degraded_services = []
        
        for service, status_val in combined_health["services"].items():
            if status_val in ["unhealthy", "unreachable", "error"]:
                combined_health["overall_status"] = "unhealthy"
                degraded_services.append(service)
            elif status_val in ["degraded", "no_bot_instance"]:
                if combined_health["overall_status"] == "healthy":
                    combined_health["overall_status"] = "degraded"
                degraded_services.append(service)
        
        if degraded_services:
            combined_health["degraded_services"] = degraded_services
        
        return combined_health
        
    except Exception as e:
        logger.error(f"Detailed health check failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Detailed health check failed: {str(e)}"
        )

@router.get("/health/metrics", tags=["Monitoring"], status_code=status.HTTP_200_OK)
async def system_metrics() -> Dict[str, Any]:
    """
    Get real-time system metrics.
    
    Returns:
        Current system resource usage and performance metrics
    """
    try:
        # Get system metrics
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        cpu_percent = psutil.cpu_percent(interval=1)
        
        # Network stats
        net_io = psutil.net_io_counters()
        
        # Process info
        process = psutil.Process()
        process_memory = process.memory_info()
        
        metrics = {
            "cpu": {
                "percent": cpu_percent,
                "count": psutil.cpu_count(),
                "freq": psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None
            },
            "memory": {
                "total": memory.total,
                "available": memory.available,
                "percent": memory.percent,
                "used": memory.used,
                "process": {
                    "rss": process_memory.rss,
                    "vms": process_memory.vms
                }
            },
            "disk": {
                "total": disk.total,
                "free": disk.free,
                "used": disk.used,
                "percent": disk.percent
            },
            "network": {
                "bytes_sent": net_io.bytes_sent,
                "bytes_recv": net_io.bytes_recv,
                "packets_sent": net_io.packets_sent,
                "packets_recv": net_io.packets_recv
            },
            "process": {
                "pid": process.pid,
                "threads": process.num_threads(),
                "connections": len(process.connections()),
                "open_files": len(process.open_files()),
                "uptime": time.time() - process.create_time()
            },
            "timestamp": datetime.utcnow().isoformat()
        }
        
        return metrics
        
    except Exception as e:
        logger.error(f"Metrics collection failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Metrics collection failed: {str(e)}"
        )

def get_process_uptime() -> float:
    """Get current process uptime in seconds"""
    try:
        return time.time() - psutil.Process().create_time()
    except:
        return 0

def get_system_uptime() -> float:
    """Get system uptime in seconds"""
    try:
        with open('/proc/uptime', 'r') as f:
            return float(f.readline().split()[0])
    except:
        return 0