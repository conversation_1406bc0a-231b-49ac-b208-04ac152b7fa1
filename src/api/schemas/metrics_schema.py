from pydantic import BaseModel, Field
from typing import Dict, Any, Optional
from datetime import datetime

class MetricsReport(BaseModel):
    """
    Comprehensive metrics report schema
    """
    total_responses: int = Field(0, description="Total number of responses generated")
    response_type_distribution: Dict[str, int] = Field(
        default_factory=dict, 
        description="Distribution of response types"
    )
    avg_processing_time: float = Field(
        0.0, 
        description="Average time taken to generate responses"
    )
    daily_metrics: Dict[str, Any] = Field(
        default_factory=dict, 
        description="Detailed daily metrics"
    )
    timestamp: Optional[datetime] = Field(
        default_factory=datetime.now, 
        description="Timestamp of metrics generation"
    )

    class Config:
        """Pydantic configuration"""
        json_schema_extra = {
            "example": {
                "total_responses": 1000,
                "response_type_distribution": {
                    "analysis": 600,
                    "fallback": 200,
                    "error": 50
                },
                "avg_processing_time": 0.25,
                "daily_metrics": {
                    "2023-07-15": {
                        "total_responses": 50,
                        "response_types": {
                            "analysis": 30,
                            "fallback": 15
                        }
                    }
                }
            }
        } 