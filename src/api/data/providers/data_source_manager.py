"""
Streamlined Data Source Manager

REFACTORED: Reduced from 1,424 lines to a focused manager that delegates
to modular components for better maintainability.

Modules extracted:
- modules/config.py: Configuration and data structures
- modules/auditing.py: Pipeline auditing and monitoring  
- modules/rate_limiting.py: Rate limiting and throttling
- modules/validation.py: Data validation and quality assessment
"""

import asyncio
import time
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime

# Import modular components
from .modules import (
    config,
    auditor,
    rate_limit_manager,
    quality_assessor,
    DataStatus
)

# Import consolidated providers from shared canonical location
try:
    from src.shared.data_providers.polygon_provider import PolygonProvider as ConsolidatedPolygonProvider
    from src.shared.data_providers.finnhub_provider import FinnhubProvider as ConsolidatedFinnhubProvider
    from src.shared.data_providers.yfinance_provider import YFinanceProvider as ConsolidatedYFinanceProvider
    from src.shared.data_providers.alpaca_provider import AlpacaProvider as ConsolidatedAlpacaProvider
    from src.shared.data_providers.enhanced_error_handler import enhanced_error_handler
    from src.shared.data_providers.health_monitor import health_monitor
    CONSOLIDATED_PROVIDERS_AVAILABLE = True
    logger = logging.getLogger(__name__)
    logger.info("✅ Consolidated data providers (shared) imported successfully")
except ImportError as e:
    CONSOLIDATED_PROVIDERS_AVAILABLE = False
    logger = logging.getLogger(__name__)
    logger.warning(f"⚠️ Could not import consolidated providers from shared: {e}")

logger = logging.getLogger(__name__)


class DataSourceManager:
    """
    Streamlined data source manager with modular architecture.
    
    REFACTORED: Core functionality preserved while delegating specialized
    concerns to dedicated modules for better maintainability.
    """
    
    def __init__(self):
        """Initialize the data source manager with modular components"""
        self.config = config
        self.auditor = auditor
        self.rate_limiter = rate_limit_manager
        self.quality_assessor = quality_assessor
        
        # Provider instances
        self.providers = {}
        self.provider_health = {}
        
        # Concurrency control
        self.max_concurrent_provider_calls = self.config.max_concurrent_provider_calls
        self._provider_semaphore = asyncio.Semaphore(self.max_concurrent_provider_calls)
        
        # Rate limit tracking
        self.rate_limit_attempts: Dict[str, int] = {}
        
        # Initialize providers
        self._initialize_providers()

        # Initialize health monitoring
        self._setup_health_monitoring()

        logger.info("✅ Streamlined DataSourceManager initialized with modular architecture")
    
    def _initialize_providers(self):
        """Initialize available data providers"""
        if not CONSOLIDATED_PROVIDERS_AVAILABLE:
            logger.warning("⚠️ Consolidated providers not available, using fallback initialization")
            return
        
        try:
            # Initialize consolidated providers
            if self.config.polygon_api_key:
                self.providers['polygon'] = ConsolidatedPolygonProvider()
                self.provider_health['polygon'] = True
                logger.info("✅ Polygon provider initialized")
            
            if self.config.finnhub_api_key:
                self.providers['finnhub'] = ConsolidatedFinnhubProvider()
                self.provider_health['finnhub'] = True
                logger.info("✅ Finnhub provider initialized")
            
            # YFinance doesn't require API key
            self.providers['yfinance'] = ConsolidatedYFinanceProvider()
            self.provider_health['yfinance'] = True
            logger.info("✅ YFinance provider initialized")
            
            if self.config.alpha_vantage_api_key:
                self.providers['alpaca'] = ConsolidatedAlpacaProvider()
                self.provider_health['alpaca'] = True
                logger.info("✅ Alpaca provider initialized")
            
        except Exception as e:
            logger.error(f"❌ Provider initialization failed: {e}")

    def _setup_health_monitoring(self):
        """Setup health monitoring for all providers"""
        if not CONSOLIDATED_PROVIDERS_AVAILABLE:
            return

        try:
            # Register health check functions for each provider
            for provider_name, provider in self.providers.items():
                async def health_check_func(p=provider):
                    return await p.get_current_price('AAPL')  # Simple health check

                health_monitor.register_provider(provider_name, health_check_func)

            logger.info("✅ Health monitoring setup complete")
        except Exception as e:
            logger.warning(f"⚠️ Health monitoring setup failed: {e}")
    
    async def get_market_data(self, symbol: str, user_id: str = None) -> Dict[str, Any]:
        """
        Get market data with enhanced error handling and fallback.

        Args:
            symbol: Stock symbol to fetch
            user_id: User ID for audit trail

        Returns:
            Dict containing market data and metadata
        """
        # Log data access for audit trail
        if user_id:
            self.auditor.log_data_access("data_source_manager", symbol, user_id)

        if not CONSOLIDATED_PROVIDERS_AVAILABLE:
            return {
                'data': None,
                'provider': None,
                'status': DataStatus.ERROR.value,
                'error': 'No providers available',
                'timestamp': datetime.now().isoformat()
            }

        # Use enhanced error handler with fallback
        provider_priority = self._get_provider_priority_order()

        async def fetch_operation(provider_name: str):
            """Operation to execute with fallback"""
            provider = self.providers.get(provider_name)
            if not provider:
                raise Exception(f"Provider {provider_name} not available")

            # Rate limiting
            provider_config = self.config.get_provider_config(provider_name)
            rate_limit = provider_config.get('rate_limit', 60)

            async with self._provider_semaphore:
                await self.rate_limiter.wait_for_provider(provider_name, rate_limit)

                # Fetch data from provider
                data = await self._fetch_from_provider(provider, symbol, provider_name)

                if not data:
                    raise Exception("No data returned from provider")

                # Assess data quality
                quality_metrics = self.quality_assessor.assess_data_quality(data, provider_name)

                return {
                    'data': data,
                    'quality_metrics': quality_metrics
                }

        # Execute with enhanced error handling
        result = await enhanced_error_handler.execute_with_fallback(
            operation=f"get_market_data_{symbol}",
            providers=provider_priority,
            operation_func=fetch_operation
        )

        # Log performance
        if result['success']:
            self.auditor.log_performance(
                f"get_market_data_{result['provider']}",
                result['duration'],
                True
            )

            return {
                'data': result['data']['data'],
                'provider': result['provider'],
                'status': DataStatus.SUCCESS.value,
                'quality_metrics': result['data']['quality_metrics'],
                'response_time': result['duration'],
                'timestamp': result['timestamp']
            }
        else:
            # Log failure
            self.auditor.log_event(
                "DATA_FETCH_FAILED",
                "ERROR",
                {"symbol": symbol, "error": result['error']},
                {"duration": result['duration']}
            )

            return {
                'data': None,
                'provider': None,
                'status': DataStatus.ERROR.value,
                'error': result['error'],
                'response_time': result['duration'],
                'timestamp': result['timestamp']
            }
    
    async def _fetch_from_provider(self, provider, symbol: str, provider_name: str) -> Optional[Dict[str, Any]]:
        """Fetch data from a specific provider"""
        try:
            # Use the consolidated provider's interface
            if hasattr(provider, 'get_current_price'):
                price_data = await provider.get_current_price(symbol)
                return {
                    'symbol': symbol,
                    'price': price_data.get('price'),
                    'timestamp': price_data.get('timestamp', datetime.now().isoformat()),
                    'volume': price_data.get('volume'),
                    'provider_specific': price_data
                }
            else:
                logger.warning(f"Provider {provider_name} doesn't have expected interface")
                return None
        
        except Exception as e:
            logger.error(f"❌ Error fetching from {provider_name}: {e}")
            raise
    
    def _get_provider_priority_order(self) -> List[str]:
        """Get providers in priority order (highest priority first)"""
        # Simple priority order - can be made configurable
        priority_order = ['polygon', 'finnhub', 'yfinance', 'alpaca']
        
        # Filter to only available providers
        return [p for p in priority_order if p in self.providers and self.provider_health.get(p, False)]
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get comprehensive health status with enhanced monitoring"""
        base_status = {
            'providers': self.provider_health,
            'rate_limits': self.rate_limiter.get_all_status(),
            'performance': self.auditor.get_performance_summary(),
            'errors': self.auditor.get_error_summary(),
            'config_valid': self.config.validate_config()
        }

        # Add enhanced error handler metrics
        if CONSOLIDATED_PROVIDERS_AVAILABLE:
            try:
                base_status['enhanced_metrics'] = enhanced_error_handler.get_provider_health_report()
                base_status['health_monitoring'] = health_monitor.get_health_report()
            except Exception as e:
                logger.warning(f"⚠️ Could not get enhanced metrics: {e}")

        return base_status
    
    def get_audit_report(self) -> Dict[str, Any]:
        """Get comprehensive audit report"""
        return self.auditor.get_audit_report()

    async def start_health_monitoring(self):
        """Start continuous health monitoring"""
        if CONSOLIDATED_PROVIDERS_AVAILABLE:
            await health_monitor.start_monitoring()
            logger.info("✅ Health monitoring started")
        else:
            logger.warning("⚠️ Cannot start health monitoring - providers not available")

    async def stop_health_monitoring(self):
        """Stop health monitoring"""
        if CONSOLIDATED_PROVIDERS_AVAILABLE:
            await health_monitor.stop_monitoring()
            logger.info("⏹️ Health monitoring stopped")

    async def close(self):
        """Close all provider sessions to prevent memory leaks"""
        for provider_name, provider in self.providers.items():
            try:
                if hasattr(provider, 'close'):
                    await provider.close()
                    logger.info(f"✅ Closed {provider_name} provider session")
            except Exception as e:
                logger.warning(f"⚠️ Error closing {provider_name} provider: {e}")

    async def __aenter__(self):
        """Async context manager entry"""
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close()


# Global instance for backward compatibility
data_source_manager = DataSourceManager()
