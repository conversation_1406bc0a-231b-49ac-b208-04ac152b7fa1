"""
Rate Limiting Module

Extracted from data_source_manager.py for better modularity.
Handles rate limiting and throttling for API providers.
"""

import asyncio
import time
import logging
from typing import Dict, List, Optional
from collections import defaultdict
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class RateLimiter:
    """Rate limiting for API providers"""
    
    def __init__(self, requests_per_minute: int):
        self.requests_per_minute = requests_per_minute
        self.requests: List[float] = []
        self.lock = asyncio.Lock()
    
    async def acquire(self) -> bool:
        """Acquire permission to make a request"""
        async with self.lock:
            now = time.time()
            
            # Remove requests older than 1 minute
            self.requests = [req_time for req_time in self.requests if now - req_time < 60]
            
            # Check if we can make a request
            if len(self.requests) < self.requests_per_minute:
                self.requests.append(now)
                return True
            
            return False
    
    async def wait_if_needed(self) -> None:
        """Wait if rate limit would be exceeded"""
        while not await self.acquire():
            await asyncio.sleep(1)  # Wait 1 second before retrying
    
    def get_remaining_requests(self) -> int:
        """Get number of remaining requests in current window"""
        now = time.time()
        recent_requests = [req_time for req_time in self.requests if now - req_time < 60]
        return max(0, self.requests_per_minute - len(recent_requests))
    
    def get_reset_time(self) -> Optional[datetime]:
        """Get time when rate limit window resets"""
        if not self.requests:
            return None
        
        oldest_request = min(self.requests)
        reset_time = datetime.fromtimestamp(oldest_request + 60)
        return reset_time


class GlobalRateLimitManager:
    """Manages rate limits across all providers"""
    
    def __init__(self):
        self.limiters: Dict[str, RateLimiter] = {}
        self.request_counts: Dict[str, int] = defaultdict(int)
        self.last_reset: Dict[str, datetime] = {}
    
    def get_limiter(self, provider_name: str, requests_per_minute: int) -> RateLimiter:
        """Get or create rate limiter for a provider"""
        if provider_name not in self.limiters:
            self.limiters[provider_name] = RateLimiter(requests_per_minute)
            self.last_reset[provider_name] = datetime.now()
        
        return self.limiters[provider_name]
    
    async def acquire_for_provider(self, provider_name: str, requests_per_minute: int) -> bool:
        """Acquire rate limit permission for a specific provider"""
        limiter = self.get_limiter(provider_name, requests_per_minute)
        success = await limiter.acquire()
        
        if success:
            self.request_counts[provider_name] += 1
        
        return success
    
    async def wait_for_provider(self, provider_name: str, requests_per_minute: int) -> None:
        """Wait for rate limit availability for a specific provider"""
        limiter = self.get_limiter(provider_name, requests_per_minute)
        await limiter.wait_if_needed()
        self.request_counts[provider_name] += 1
    
    def get_provider_status(self, provider_name: str) -> Dict[str, any]:
        """Get rate limit status for a provider"""
        if provider_name not in self.limiters:
            return {"status": "not_initialized"}
        
        limiter = self.limiters[provider_name]
        
        return {
            "provider": provider_name,
            "remaining_requests": limiter.get_remaining_requests(),
            "total_requests": self.request_counts[provider_name],
            "reset_time": limiter.get_reset_time(),
            "last_reset": self.last_reset.get(provider_name)
        }
    
    def get_all_status(self) -> Dict[str, Dict[str, any]]:
        """Get rate limit status for all providers"""
        return {
            provider: self.get_provider_status(provider)
            for provider in self.limiters.keys()
        }
    
    def reset_provider_stats(self, provider_name: str) -> None:
        """Reset statistics for a provider"""
        if provider_name in self.request_counts:
            self.request_counts[provider_name] = 0
        
        if provider_name in self.last_reset:
            self.last_reset[provider_name] = datetime.now()
        
        logger.info(f"Reset rate limit stats for provider: {provider_name}")
    
    def reset_all_stats(self) -> None:
        """Reset statistics for all providers"""
        self.request_counts.clear()
        now = datetime.now()
        
        for provider in self.limiters.keys():
            self.last_reset[provider] = now
        
        logger.info("Reset rate limit stats for all providers")


# Global rate limit manager instance
rate_limit_manager = GlobalRateLimitManager()
