"""
Data Validation Module

Extracted from data_source_manager.py for better modularity.
Validates that data is real and not generated, with comprehensive quality checks.
"""

import logging
import re
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass

from .config import DataQualityMetrics

logger = logging.getLogger(__name__)


@dataclass
class ValidationResult:
    """Result of data validation"""
    is_valid: bool
    quality_score: float
    issues: List[str]
    warnings: List[str]
    metadata: Dict[str, Any]


class RealDataValidator:
    """Validates that data is real and not generated"""
    
    @staticmethod
    def validate_stock_data(data: Dict[str, Any]) -> ValidationResult:
        """Validate that stock data is real"""
        issues = []
        warnings = []
        metadata = {}
        
        # Check for required fields
        required_fields = ['symbol', 'price', 'timestamp']
        missing_fields = [field for field in required_fields if field not in data]
        
        if missing_fields:
            issues.append(f"Missing required fields: {missing_fields}")
        
        # Validate price data
        price = data.get('price')
        if price is not None:
            if not isinstance(price, (int, float)):
                issues.append("Price must be numeric")
            elif price <= 0:
                issues.append("Price must be positive")
            elif price > 100000:  # Sanity check for extremely high prices
                warnings.append("Price seems unusually high")
        
        # Validate timestamp
        timestamp = data.get('timestamp')
        if timestamp:
            try:
                if isinstance(timestamp, str):
                    parsed_time = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                elif isinstance(timestamp, datetime):
                    parsed_time = timestamp
                else:
                    issues.append("Invalid timestamp format")
                    parsed_time = None
                
                if parsed_time:
                    # Check if data is too old
                    age = datetime.now() - parsed_time.replace(tzinfo=None)
                    if age > timedelta(hours=24):
                        warnings.append("Data is more than 24 hours old")
                    
                    metadata['data_age_hours'] = age.total_seconds() / 3600
            
            except Exception as e:
                issues.append(f"Timestamp parsing error: {e}")
        
        # Validate symbol format
        symbol = data.get('symbol')
        if symbol:
            if not isinstance(symbol, str):
                issues.append("Symbol must be a string")
            elif not re.match(r'^[A-Z]{1,5}$', symbol.upper()):
                warnings.append("Symbol format may be invalid")
        
        # Calculate quality score
        quality_score = RealDataValidator._calculate_quality_score(data, issues, warnings)
        
        return ValidationResult(
            is_valid=len(issues) == 0,
            quality_score=quality_score,
            issues=issues,
            warnings=warnings,
            metadata=metadata
        )
    
    @staticmethod
    def validate_market_data(data: Dict[str, Any]) -> ValidationResult:
        """Validate comprehensive market data"""
        issues = []
        warnings = []
        metadata = {}
        
        # Validate basic stock data first
        basic_validation = RealDataValidator.validate_stock_data(data)
        issues.extend(basic_validation.issues)
        warnings.extend(basic_validation.warnings)
        metadata.update(basic_validation.metadata)
        
        # Additional market data validation
        volume = data.get('volume')
        if volume is not None:
            if not isinstance(volume, (int, float)):
                issues.append("Volume must be numeric")
            elif volume < 0:
                issues.append("Volume cannot be negative")
        
        # Validate OHLC data if present
        ohlc_fields = ['open', 'high', 'low', 'close']
        ohlc_values = {field: data.get(field) for field in ohlc_fields if data.get(field) is not None}
        
        if len(ohlc_values) > 1:
            # Validate OHLC relationships
            if 'high' in ohlc_values and 'low' in ohlc_values:
                if ohlc_values['high'] < ohlc_values['low']:
                    issues.append("High price cannot be less than low price")
            
            # Check for reasonable price ranges
            prices = list(ohlc_values.values())
            price_range = max(prices) - min(prices)
            avg_price = sum(prices) / len(prices)
            
            if price_range / avg_price > 0.5:  # 50% daily range seems excessive
                warnings.append("Unusually large price range detected")
        
        # Calculate enhanced quality score
        quality_score = RealDataValidator._calculate_market_quality_score(data, issues, warnings)
        
        return ValidationResult(
            is_valid=len(issues) == 0,
            quality_score=quality_score,
            issues=issues,
            warnings=warnings,
            metadata=metadata
        )
    
    @staticmethod
    def _calculate_quality_score(data: Dict[str, Any], issues: List[str], warnings: List[str]) -> float:
        """Calculate basic quality score"""
        base_score = 1.0
        
        # Deduct for issues and warnings
        base_score -= len(issues) * 0.2
        base_score -= len(warnings) * 0.1
        
        # Bonus for completeness
        required_fields = ['symbol', 'price', 'timestamp']
        completeness = sum(1 for field in required_fields if data.get(field) is not None) / len(required_fields)
        base_score *= completeness
        
        return max(0.0, min(1.0, base_score))
    
    @staticmethod
    def _calculate_market_quality_score(data: Dict[str, Any], issues: List[str], warnings: List[str]) -> float:
        """Calculate enhanced market data quality score"""
        base_score = RealDataValidator._calculate_quality_score(data, issues, warnings)
        
        # Additional scoring for market data completeness
        optional_fields = ['volume', 'open', 'high', 'low', 'close']
        optional_completeness = sum(1 for field in optional_fields if data.get(field) is not None) / len(optional_fields)
        
        # Weighted combination
        enhanced_score = (base_score * 0.7) + (optional_completeness * 0.3)
        
        return max(0.0, min(1.0, enhanced_score))
    
    @staticmethod
    def validate_provider_response(response: Dict[str, Any], provider_name: str) -> ValidationResult:
        """Validate response from a specific provider"""
        issues = []
        warnings = []
        metadata = {'provider': provider_name}
        
        # Check response structure
        if not isinstance(response, dict):
            issues.append("Response must be a dictionary")
            return ValidationResult(False, 0.0, issues, warnings, metadata)
        
        # Check for error indicators
        if 'error' in response:
            issues.append(f"Provider returned error: {response['error']}")
        
        if 'status' in response and response['status'] != 'success':
            warnings.append(f"Provider status: {response['status']}")
        
        # Validate data payload
        data = response.get('data', response)
        if data:
            data_validation = RealDataValidator.validate_market_data(data)
            issues.extend(data_validation.issues)
            warnings.extend(data_validation.warnings)
            metadata.update(data_validation.metadata)
            quality_score = data_validation.quality_score
        else:
            issues.append("No data found in response")
            quality_score = 0.0
        
        return ValidationResult(
            is_valid=len(issues) == 0,
            quality_score=quality_score,
            issues=issues,
            warnings=warnings,
            metadata=metadata
        )


class DataQualityAssessor:
    """Assesses overall data quality across multiple sources"""
    
    def __init__(self):
        self.validation_history: List[ValidationResult] = []
    
    def assess_data_quality(self, data: Dict[str, Any], provider_name: str) -> DataQualityMetrics:
        """Assess comprehensive data quality metrics"""
        validation = RealDataValidator.validate_provider_response(data, provider_name)
        self.validation_history.append(validation)
        
        # Calculate quality metrics
        completeness = self._calculate_completeness(data)
        freshness = self._calculate_freshness(data)
        accuracy = validation.quality_score
        consistency = self._calculate_consistency(data)
        source_reliability = self._calculate_source_reliability(provider_name)
        
        return DataQualityMetrics(
            completeness=completeness,
            freshness=freshness,
            accuracy=accuracy,
            consistency=consistency,
            source_reliability=source_reliability
        )
    
    def _calculate_completeness(self, data: Dict[str, Any]) -> float:
        """Calculate data completeness score"""
        all_fields = ['symbol', 'price', 'timestamp', 'volume', 'open', 'high', 'low', 'close']
        present_fields = sum(1 for field in all_fields if data.get(field) is not None)
        return present_fields / len(all_fields)
    
    def _calculate_freshness(self, data: Dict[str, Any]) -> float:
        """Calculate data freshness score"""
        timestamp = data.get('timestamp')
        if not timestamp:
            return 0.0
        
        try:
            if isinstance(timestamp, str):
                data_time = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            else:
                data_time = timestamp
            
            age_hours = (datetime.now() - data_time.replace(tzinfo=None)).total_seconds() / 3600
            
            # Fresher data gets higher score
            if age_hours < 1:
                return 1.0
            elif age_hours < 6:
                return 0.8
            elif age_hours < 24:
                return 0.6
            else:
                return 0.3
        
        except Exception:
            return 0.0
    
    def _calculate_consistency(self, data: Dict[str, Any]) -> float:
        """Calculate data consistency score"""
        # Simple consistency checks
        score = 1.0
        
        # Check OHLC consistency
        ohlc = {k: data.get(k) for k in ['open', 'high', 'low', 'close'] if data.get(k) is not None}
        if len(ohlc) >= 2:
            values = list(ohlc.values())
            if max(values) == min(values):
                score *= 0.8  # All same values is suspicious
        
        return score
    
    def _calculate_source_reliability(self, provider_name: str) -> float:
        """Calculate source reliability based on historical performance"""
        # Simple reliability scoring based on provider
        reliability_scores = {
            'polygon': 0.95,
            'finnhub': 0.90,
            'alpha_vantage': 0.85,
            'yahoo_finance': 0.80
        }
        
        return reliability_scores.get(provider_name.lower(), 0.70)


# Global quality assessor instance
quality_assessor = DataQualityAssessor()
