import time
import logging
from typing import Dict, Optional, List
from functools import wraps

from prometheus_client import Counter, Histogram, Gauge, start_http_server
from prometheus_client.core import CollectorRegistry

from src.shared.data_validation import assess_data_quality, detect_data_gaps
# Removed circular import - these functions are not used in this file
from src.api.data.providers.base import ProviderMetadata, ProviderType

logger = logging.getLogger(__name__)

# Prometheus metrics for cache warming
CACHE_WARMING_REGISTRY = CollectorRegistry()

# Cache warming job metrics
CACHE_WARMING_DURATION = Histogram(
    'cache_warming_duration_seconds',
    'Time spent warming cache',
    ['job_type'],
    registry=CACHE_WARMING_REGISTRY
)

CACHE_WARMING_SYMBOLS_TOTAL = Gauge(
    'cache_warming_symbols_total',
    'Total number of symbols processed in cache warming',
    ['job_type'],
    registry=CACHE_WARMING_REGISTRY
)

CACHE_WARMING_SYMBOLS_SUCCESS = Gauge(
    'cache_warming_symbols_success',
    'Number of symbols successfully cached',
    ['job_type'],
    registry=CACHE_WARMING_REGISTRY
)

CACHE_WARMING_SUCCESS_RATE = Gauge(
    'cache_warming_success_rate',
    'Success rate of cache warming job (0-1)',
    ['job_type'],
    registry=CACHE_WARMING_REGISTRY
)

CACHE_WARMING_LAST_RUN = Gauge(
    'cache_warming_last_run_timestamp',
    'Timestamp of last cache warming job execution',
    ['job_type'],
    registry=CACHE_WARMING_REGISTRY
)

# Cache performance metrics
CACHE_HIT_RATE = Gauge(
    'redis_cache_hit_rate',
    'Redis cache hit rate percentage',
    registry=CACHE_WARMING_REGISTRY
)

CACHE_MEMORY_USAGE = Gauge(
    'redis_cache_memory_bytes',
    'Redis cache memory usage in bytes',
    registry=CACHE_WARMING_REGISTRY
)

CACHE_OPERATIONS = Counter(
    'cache_operations_total',
    'Total cache operations',
    ['operation', 'result'],
    registry=CACHE_WARMING_REGISTRY
)

# API request metrics related to cache
API_CACHE_HITS = Counter(
    'api_cache_hits_total',
    'Number of API requests served from cache',
    ['endpoint', 'symbol'],
    registry=CACHE_WARMING_REGISTRY
)

API_CACHE_MISSES = Counter(
    'api_cache_misses_total', 
    'Number of API requests that missed cache',
    ['endpoint', 'symbol'],
    registry=CACHE_WARMING_REGISTRY
)

# Data quality and gap detection metrics
DATA_GAPS_TOTAL = Counter(
    'data_gaps_total',
    'Total number of data gaps detected',
    ['symbol', 'interval_type', 'severity'],
    registry=CACHE_WARMING_REGISTRY
)

DATA_GAP_DURATION = Histogram(
    'data_gap_duration_seconds',
    'Duration of detected data gaps',
    ['symbol', 'interval_type', 'severity'],
    registry=CACHE_WARMING_REGISTRY
)

DATA_QUALITY_SCORE = Gauge(
    'data_quality_score',
    'Data quality score (0-100)',
    ['symbol', 'interval_type'],
    registry=CACHE_WARMING_REGISTRY
)

DATA_COMPLETENESS = Gauge(
    'data_completeness_percent',
    'Data completeness percentage',
    ['symbol', 'interval_type'],
    registry=CACHE_WARMING_REGISTRY
)

DATA_GAP_DETECTED_LAST = Gauge(
    'data_gap_detected_last_timestamp',
    'Timestamp of last data gap detection',
    ['symbol', 'interval_type'],
    registry=CACHE_WARMING_REGISTRY
)

# Provider attribution and freshness metrics
PROVIDER_RESPONSE_TIME = Histogram(
    'provider_response_time_ms',
    'Provider response time in milliseconds',
    ['provider_name', 'provider_type', 'operation'],
    registry=CACHE_WARMING_REGISTRY
)

PROVIDER_SUCCESS_RATE = Gauge(
    'provider_success_rate',
    'Provider success rate percentage',
    ['provider_name', 'provider_type'],
    registry=CACHE_WARMING_REGISTRY
)

PROVIDER_FALLBACK_USAGE = Counter(
    'provider_fallback_usage_total',
    'Number of times fallback providers were used',
    ['primary_provider', 'fallback_provider', 'reason'],
    registry=CACHE_WARMING_REGISTRY
)

DATA_FRESHNESS_MINUTES = Gauge(
    'data_freshness_minutes',
    'Data freshness in minutes since last update',
    ['provider_name', 'symbol', 'data_type'],
    registry=CACHE_WARMING_REGISTRY
)

PROVIDER_CACHE_HIT_RATE = Gauge(
    'provider_cache_hit_rate',
    'Cache hit rate for each provider',
    ['provider_name', 'provider_type'],
    registry=CACHE_WARMING_REGISTRY
)

class CacheMetrics:
    """
    Cache metrics collector and reporter.
    """
    
    def __init__(self):
        self.enabled = True
        
    def record_cache_warming_job(
        self, 
        job_type: str, 
        duration: float, 
        total_symbols: int,
        successful_symbols: int
    ):
        """Record metrics for a cache warming job."""
        if not self.enabled:
            return
            
        try:
            CACHE_WARMING_DURATION.labels(job_type=job_type).observe(duration)
            CACHE_WARMING_SYMBOLS_TOTAL.labels(job_type=job_type).set(total_symbols)
            CACHE_WARMING_SYMBOLS_SUCCESS.labels(job_type=job_type).set(successful_symbols)
            
            success_rate = successful_symbols / total_symbols if total_symbols > 0 else 0
            CACHE_WARMING_SUCCESS_RATE.labels(job_type=job_type).set(success_rate)
            CACHE_WARMING_LAST_RUN.labels(job_type=job_type).set(time.time())
            
            logger.debug(f"Recorded cache warming metrics for {job_type}")
            
        except Exception as e:
            logger.error(f"Failed to record cache warming metrics: {e}")
    
    def record_cache_stats(self, stats: Dict):
        """Record general cache statistics."""
        if not self.enabled or not stats:
            return
            
        try:
            if 'hit_rate' in stats:
                CACHE_HIT_RATE.set(stats['hit_rate'])
                
            if 'used_memory' in stats:
                CACHE_MEMORY_USAGE.set(stats['used_memory'])
                
        except Exception as e:
            logger.error(f"Failed to record cache stats: {e}")
    
    def record_cache_operation(self, operation: str, result: str):
        """Record a cache operation (hit/miss/set/etc.)."""
        if not self.enabled:
            return
            
        try:
            CACHE_OPERATIONS.labels(operation=operation, result=result).inc()
        except Exception as e:
            logger.error(f"Failed to record cache operation: {e}")
    
    def record_api_cache_hit(self, endpoint: str, symbol: str):
        """Record an API request served from cache."""
        if not self.enabled:
            return
            
        try:
            API_CACHE_HITS.labels(endpoint=endpoint, symbol=symbol).inc()
        except Exception as e:
            logger.error(f"Failed to record API cache hit: {e}")
    
    def record_api_cache_miss(self, endpoint: str, symbol: str):
        """Record an API request that missed cache."""
        if not self.enabled:
            return
            
        try:
            API_CACHE_MISSES.labels(endpoint=endpoint, symbol=symbol).inc()
        except Exception as e:
            logger.error(f"Failed to record API cache miss: {e}")

    def record_data_gap(
        self, 
        symbol: str, 
        interval_type: str, 
        severity: str, 
        duration_seconds: float
    ):
        """Record a detected data gap."""
        if not self.enabled:
            return
            
        try:
            DATA_GAPS_TOTAL.labels(
                symbol=symbol, 
                interval_type=interval_type, 
                severity=severity
            ).inc()
            
            DATA_GAP_DURATION.labels(
                symbol=symbol, 
                interval_type=interval_type, 
                severity=severity
            ).observe(duration_seconds)
            
            DATA_GAP_DETECTED_LAST.labels(
                symbol=symbol, 
                interval_type=interval_type
            ).set(time.time())
            
        except Exception as e:
            logger.error(f"Failed to record data gap metrics: {e}")

    def record_data_quality(
        self, 
        symbol: str, 
        interval_type: str, 
        quality_score: float, 
        completeness: float
    ):
        """Record data quality metrics."""
        if not self.enabled:
            return
            
        try:
            DATA_QUALITY_SCORE.labels(
                symbol=symbol, 
                interval_type=interval_type
            ).set(quality_score)
            
            DATA_COMPLETENESS.labels(
                symbol=symbol, 
                interval_type=interval_type
            ).set(completeness)
            
        except Exception as e:
            logger.error(f"Failed to record data quality metrics: {e}")

    def record_data_gaps_batch(
        self, 
        gaps: List[Dict], 
        symbol: str, 
        interval_type: str
    ):
        """Record multiple data gaps in batch."""
        if not self.enabled or not gaps:
            return
            
        try:
            for gap in gaps:
                self.record_data_gap(
                    symbol=symbol,
                    interval_type=interval_type,
                    severity=gap.get('severity', 'unknown'),
                    duration_seconds=gap.get('duration_seconds', 0)
                )
                
        except Exception as e:
            logger.error(f"Failed to record data gaps batch: {e}")

    def record_provider_metrics(
        self,
        provider_name: str,
        provider_type: str,
        operation: str,
        response_time_ms: float,
        success: bool
    ):
        """Record provider performance metrics."""
        if not self.enabled:
            return
            
        try:
            # Record response time
            PROVIDER_RESPONSE_TIME.labels(
                provider_name=provider_name,
                provider_type=provider_type,
                operation=operation
            ).observe(response_time_ms)
            
            # Record success rate (this would need to be called multiple times to build the rate)
            if success:
                # Increment success counter (you might want to add a success counter metric)
                pass
            
        except Exception as e:
            logger.error(f"Failed to record provider metrics: {e}")

    def record_provider_fallback(
        self,
        primary_provider: str,
        fallback_provider: str,
        reason: str
    ):
        """Record when fallback providers are used."""
        if not self.enabled:
            return
            
        try:
            PROVIDER_FALLBACK_USAGE.labels(
                primary_provider=primary_provider,
                fallback_provider=fallback_provider,
                reason=reason
            ).inc()
            
        except Exception as e:
            logger.error(f"Failed to record provider fallback: {e}")

    def record_data_freshness(
        self,
        provider_name: str,
        symbol: str,
        data_type: str,
        freshness_minutes: int
    ):
        """Record data freshness metrics."""
        if not self.enabled:
            return
            
        try:
            DATA_FRESHNESS_MINUTES.labels(
                provider_name=provider_name,
                symbol=symbol,
                data_type=data_type
            ).set(freshness_minutes)
            
        except Exception as e:
            logger.error(f"Failed to record data freshness: {e}")

    def record_provider_cache_hit_rate(
        self,
        provider_name: str,
        provider_type: str,
        hit_rate: float
    ):
        """Record cache hit rate for providers."""
        if not self.enabled:
            return
            
        try:
            PROVIDER_CACHE_HIT_RATE.labels(
                provider_name=provider_name,
                provider_type=provider_type
            ).set(hit_rate)
            
        except Exception as e:
            logger.error(f"Failed to record provider cache hit rate: {e}")

    def get_current_metrics(self) -> Dict:
        """Get current metric values for dashboard/logging."""
        try:
            return {
                'cache_hit_rate': CACHE_HIT_RATE._value._value,
                'cache_memory_bytes': CACHE_MEMORY_USAGE._value._value,
                'last_warming_jobs': {
                    family.name: {
                        sample.labels['job_type']: sample.value 
                        for sample in family.samples
                    }
                    for family in CACHE_WARMING_REGISTRY.collect()
                    if family.name == 'cache_warming_last_run_timestamp'
                }
            }
        except Exception as e:
            logger.error(f"Failed to get current metrics: {e}")
            return {}


# Global metrics instance
cache_metrics = CacheMetrics()


def track_cache_operation(operation: str):
    """Decorator to track cache operations."""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            
            try:
                result = await func(*args, **kwargs)
                
                # Determine result status
                if result is not None:
                    cache_metrics.record_cache_operation(operation, 'success')
                else:
                    cache_metrics.record_cache_operation(operation, 'miss')
                    
                return result
                
            except Exception as e:
                cache_metrics.record_cache_operation(operation, 'error')
                raise e
                
        return wrapper
    return decorator


def track_api_cache_usage(endpoint: str):
    """Decorator to track API cache hits/misses."""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Extract symbol from args/kwargs
            symbol = kwargs.get('symbol') or (args[0] if args else 'unknown')
            
            try:
                result = await func(*args, **kwargs)
                
                # Check if result came from cache (implement based on your logic)
                if hasattr(result, '_from_cache') and result._from_cache:
                    cache_metrics.record_api_cache_hit(endpoint, symbol)
                else:
                    cache_metrics.record_api_cache_miss(endpoint, symbol)
                    
                return result
                
            except Exception as e:
                cache_metrics.record_api_cache_miss(endpoint, symbol)
                raise e
                
        return wrapper
    return decorator


class CacheMetricsServer:
    """
    HTTP server to expose Prometheus metrics.
    """
    
    def __init__(self, port: int = 8001):
        self.port = port
        self.server = None
        
    def start(self):
        """Start the metrics HTTP server."""
        try:
            self.server = start_http_server(
                self.port, 
                registry=CACHE_WARMING_REGISTRY
            )
            logger.info(f"Cache metrics server started on port {self.port}")
        except Exception as e:
            logger.error(f"Failed to start metrics server: {e}")
    
    def stop(self):
        """Stop the metrics HTTP server."""
        if self.server:
            self.server.shutdown()
            logger.info("Cache metrics server stopped")


# Grafana Dashboard Configuration (JSON)
GRAFANA_DASHBOARD_CONFIG = {
    "dashboard": {
        "title": "Market Data Cache Performance",
        "tags": ["cache", "redis", "market-data"],
        "timezone": "UTC",
        "panels": [
            {
                "title": "Cache Hit Rate",
                "type": "stat",
                "targets": [
                    {
                        "expr": "redis_cache_hit_rate",
                        "legendFormat": "Hit Rate %"
                    }
                ],
                "fieldConfig": {
                    "defaults": {
                        "unit": "percent",
                        "min": 0,
                        "max": 100
                    }
                }
            },
            {
                "title": "Cache Warming Success Rate",
                "type": "timeseries",
                "targets": [
                    {
                        "expr": "cache_warming_success_rate",
                        "legendFormat": "{{job_type}} Success Rate"
                    }
                ]
            },
            {
                "title": "Cache Memory Usage",
                "type": "timeseries",
                "targets": [
                    {
                        "expr": "redis_cache_memory_bytes",
                        "legendFormat": "Memory Usage"
                    }
                ],
                "fieldConfig": {
                    "defaults": {
                        "unit": "bytes"
                    }
                }
            },
            {
                "title": "API Cache Hit/Miss Ratio",
                "type": "piechart",
                "targets": [
                    {
                        "expr": "sum(api_cache_hits_total)",
                        "legendFormat": "Cache Hits"
                    },
                    {
                        "expr": "sum(api_cache_misses_total)", 
                        "legendFormat": "Cache Misses"
                    }
                ]
            },
            {
                "title": "Cache Warming Duration",
                "type": "timeseries",
                "targets": [
                    {
                        "expr": "cache_warming_duration_seconds",
                        "legendFormat": "{{job_type}} Duration"
                    }
                ],
                "fieldConfig": {
                    "defaults": {
                        "unit": "s"
                    }
                }
            },
            {
                "title": "Cache Operations by Type",
                "type": "barchart",
                "targets": [
                    {
                        "expr": "sum by (operation, result) (cache_operations_total)",
                        "legendFormat": "{{operation}} - {{result}}"
                    }
                ]
            }
        ]
    }
}


# Integration with cache warming scheduler
def integrate_with_cache_warming_scheduler(scheduler):
    """Integrate metrics collection with the cache warming scheduler."""
    try:
        # Monkey patch the scheduler to record metrics
        original_execute = scheduler._execute_cache_warming
        
        async def metrics_wrapped_execute():
            start_time = time.time()
            try:
                result = await original_execute()
                
                # Record metrics after successful execution
                duration = time.time() - start_time
                # Extract metrics from scheduler's results
                # This would need to be implemented based on your scheduler's structure
                
                return result
            except Exception as e:
                # Record failure metrics
                duration = time.time() - start_time
                cache_metrics.record_cache_warming_job(
                    'pre_market', duration, 0, 0
                )
                raise e
        
        scheduler._execute_cache_warming = metrics_wrapped_execute
        logger.info("Metrics integration with cache warming scheduler completed")
        
    except Exception as e:
        logger.error(f"Failed to integrate metrics with scheduler: {e}")


# Health check endpoint for metrics
def get_metrics_health() -> Dict:
    """Get health status of the metrics system."""
    try:
        current_metrics = cache_metrics.get_current_metrics()
        return {
            'status': 'healthy',
            'metrics_enabled': cache_metrics.enabled,
            'current_metrics': current_metrics,
            'timestamp': time.time()
        }
    except Exception as e:
        return {
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': time.time()
        } 