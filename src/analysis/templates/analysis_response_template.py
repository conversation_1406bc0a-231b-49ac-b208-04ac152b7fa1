#!/usr/bin/env python3
"""
Template for Analysis Response with Self-Evaluation
This module provides a template system for generating analysis responses with built-in evaluation
"""

from typing import Dict, Any, List
from dataclasses import dataclass
from abc import ABC, abstractmethod

@dataclass
class AnalysisResult:
    """Base class for analysis results"""
    symbol: str
    current_price: float
    targets: Dict[str, float]
    technical_context: Dict[str, Any]
    support_levels: List[float]
    resistance_levels: List[float]

class AnalysisEvaluator(ABC):
    """Abstract base class for analysis evaluators"""
    
    @abstractmethod
    def evaluate(self, analysis_result: AnalysisResult) -> Dict[str, Any]:
        """
        Evaluate an analysis result
        
        Args:
            analysis_result: The analysis result to evaluate
            
        Returns:
            Dictionary with evaluation metrics
        """
        pass

class ResponseTemplate(ABC):
    """Abstract base class for response templates"""
    
    @abstractmethod
    def format_response(self, analysis_result: AnalysisResult, evaluation: Dict[str, Any]) -> str:
        """
        Format an analysis result and evaluation into a response
        
        Args:
            analysis_result: The analysis result
            evaluation: The evaluation metrics
            
        Returns:
            Formatted response string
        """
        pass

# Concrete implementation for price target analysis
class PriceTargetEvaluator(AnalysisEvaluator):
    """Evaluator for price target analysis"""
    
    def evaluate(self, analysis_result: AnalysisResult) -> Dict[str, Any]:
        """
        Evaluate price target analysis
        """
        evaluation = {
            'confidence_score': 70,  # Default score
            'reliability_grade': 'B (Reliable)',
            'risk_assessment': 'Moderate Risk',
            'validation_points': [
                'Targets align with technical indicators',
                'Reasonable risk/reward ratio'
            ],
            'potential_issues': [],
            'recommendations': [
                'Monitor key support levels',
                'Use stop loss to manage risk'
            ]
        }
        
        # Customize based on technical context
        trend_strength = analysis_result.technical_context.get('trend_strength', 0.5)
        volatility = analysis_result.technical_context.get('volatility', 0.03)
        
        if trend_strength < 0.4:
            evaluation['potential_issues'].append('Weak trend detected')
            evaluation['recommendations'].append('Wait for trend confirmation')
            
        if volatility > 0.05:
            evaluation['potential_issues'].append('High volatility environment')
            evaluation['recommendations'].append('Reduce position size')
            
        # Adjust confidence score based on factors
        confidence_adjustments = 0
        if trend_strength < 0.4:
            confidence_adjustments -= 20
        if volatility > 0.05:
            confidence_adjustments -= 15
            
        evaluation['confidence_score'] = max(0, min(100, evaluation['confidence_score'] + confidence_adjustments))
        
        # Update grade based on confidence
        if evaluation['confidence_score'] >= 80:
            evaluation['reliability_grade'] = 'A (Highly Reliable)'
            evaluation['risk_assessment'] = 'Low Risk'
        elif evaluation['confidence_score'] >= 60:
            evaluation['reliability_grade'] = 'B (Reliable)'
            evaluation['risk_assessment'] = 'Moderate Risk'
        elif evaluation['confidence_score'] >= 40:
            evaluation['reliability_grade'] = 'C (Moderately Reliable)'
            evaluation['risk_assessment'] = 'Higher Risk'
        else:
            evaluation['reliability_grade'] = 'D (Low Reliability)'
            evaluation['risk_assessment'] = 'High Risk'
            
        return evaluation

class PriceTargetResponseTemplate(ResponseTemplate):
    """Response template for price target analysis"""
    
    def format_response(self, analysis_result: AnalysisResult, evaluation: Dict[str, Any]) -> str:
        """
        Format price target analysis response
        """
        lines = []
        
        # Header
        lines.append("🤖 ANALYSIS SUMMARY & SELF-EVALUATION")
        lines.append("=" * 50)
        
        # Price targets
        lines.append(f"\n🎯 {analysis_result.symbol} PRICE TARGETS:")
        lines.append(f"  Current Price: ${analysis_result.current_price:.2f}")
        
        for target_name, target_price in analysis_result.targets.items():
            pct_change = ((target_price / analysis_result.current_price) - 1) * 100
            lines.append(f"  {target_name.capitalize()}: ${target_price:.2f} ({pct_change:+.1f}%)")
        
        # Evaluation
        lines.append(f"\n📊 ANALYSIS EVALUATION:")
        lines.append(f"  Confidence Score: {evaluation['confidence_score']}/100")
        lines.append(f"  Reliability Grade: {evaluation['reliability_grade']}")
        lines.append(f"  Risk Assessment: {evaluation['risk_assessment']}")
        
        # Validation points
        if evaluation['validation_points']:
            lines.append(f"\n✅ VALIDATION POINTS:")
            for point in evaluation['validation_points']:
                lines.append(f"  • {point}")
        
        # Potential issues
        if evaluation['potential_issues']:
            lines.append(f"\n⚠️  POTENTIAL ISSUES:")
            for issue in evaluation['potential_issues']:
                lines.append(f"  • {issue}")
        
        # Recommendations
        if evaluation['recommendations']:
            lines.append(f"\n💡 RECOMMENDATIONS:")
            for rec in evaluation['recommendations']:
                lines.append(f"  • {rec}")
        
        # Technical context
        lines.append(f"\n🔍 TECHNICAL CONTEXT:")
        for key, value in analysis_result.technical_context.items():
            if isinstance(value, float):
                if 'volatility' in key:
                    lines.append(f"  {key.replace('_', ' ').title()}: {value:.2%}")
                else:
                    lines.append(f"  {key.replace('_', ' ').title()}: {value:.2f}")
            else:
                lines.append(f"  {key.replace('_', ' ').title()}: {value}")
        
        # Support/Resistance levels
        if analysis_result.support_levels:
            lines.append(f"\n📊 SUPPORT LEVELS:")
            for level in analysis_result.support_levels[:3]:
                lines.append(f"  • ${level:.2f}")
                
        if analysis_result.resistance_levels:
            lines.append(f"\n📊 RESISTANCE LEVELS:")
            for level in analysis_result.resistance_levels[:3]:
                lines.append(f"  • ${level:.2f}")
        
        # Footer
        lines.append("\n" + "=" * 50)
        lines.append("⚠️  DISCLOSURE: This analysis is algorithmically generated and should not be")
        lines.append("    considered financial advice. Always validate with additional research")
        lines.append("    and consider your risk tolerance before making investment decisions.")
        
        return "\n".join(lines)

# Factory for creating evaluators and templates
class AnalysisTemplateFactory:
    """Factory for creating analysis evaluators and response templates"""
    
    @staticmethod
    def create_evaluator(analysis_type: str) -> AnalysisEvaluator:
        """Create an evaluator for the specified analysis type"""
        if analysis_type == "price_targets":
            return PriceTargetEvaluator()
        else:
            raise ValueError(f"Unknown analysis type: {analysis_type}")
    
    @staticmethod
    def create_template(analysis_type: str) -> ResponseTemplate:
        """Create a response template for the specified analysis type"""
        if analysis_type == "price_targets":
            return PriceTargetResponseTemplate()
        else:
            raise ValueError(f"Unknown analysis type: {analysis_type}")

# Example usage
if __name__ == "__main__":
    # Example analysis result
    example_result = AnalysisResult(
        symbol="NVDA",
        current_price=177.82,
        targets={
            "conservative": 182.05,
            "moderate": 188.39,
            "aggressive": 198.96,
            "stop_loss": 173.59
        },
        technical_context={
            "trend_direction": "sideways",
            "trend_strength": 0.38,
            "volatility": 0.0472
        },
        support_levels=[173.59, 126.46, 122.86],
        resistance_levels=[182.05, 188.39, 198.96]
    )
    
    # Create evaluator and template
    evaluator = AnalysisTemplateFactory.create_evaluator("price_targets")
    template = AnalysisTemplateFactory.create_template("price_targets")
    
    # Evaluate and format response
    evaluation = evaluator.evaluate(example_result)
    response = template.format_response(example_result, evaluation)
    
    print(response)