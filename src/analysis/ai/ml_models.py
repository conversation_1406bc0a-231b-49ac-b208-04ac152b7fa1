"""
Machine Learning Models for Trading Analysis

Implements sophisticated ML models for price prediction, trend analysis,
and recommendation scoring to enhance the trading analysis engine.
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression, Ridge
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.model_selection import train_test_split, cross_val_score
import joblib
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)


@dataclass
class MLPrediction:
    """ML prediction result"""
    predicted_price: float
    confidence: float  # 0-100
    direction: str  # "UP", "DOWN", "SIDEWAYS"
    probability: float  # 0-1
    model_used: str
    features_used: List[str]
    prediction_horizon: str  # "1D", "5D", "1W", "1M"


@dataclass
class ModelPerformance:
    """Model performance metrics"""
    mse: float
    mae: float
    r2_score: float
    accuracy: float
    precision: float
    recall: float
    last_updated: datetime


class TradingMLModel:
    """
    Advanced ML model for trading predictions.
    
    Features:
    - Multiple model ensemble (Random Forest, Gradient Boosting, Linear)
    - Feature engineering from technical indicators
    - Model performance tracking and auto-retraining
    - Confidence scoring based on prediction consensus
    """
    
    def __init__(self, model_type: str = "ensemble"):
        """Initialize the ML model"""
        self.model_type = model_type
        self.models = {}
        self.scalers = {}
        self.performance_metrics = {}
        self.feature_importance = {}
        self.is_trained = False
        
        # Initialize models
        self._initialize_models()
        
        # Feature configuration
        self.technical_features = [
            'rsi', 'macd_signal', 'macd_histogram', 'bb_upper', 'bb_lower',
            'sma_20', 'sma_50', 'sma_200', 'ema_12', 'ema_26',
            'volume_sma', 'price_change_1d', 'price_change_5d',
            'volatility_20d', 'momentum_10d', 'stochastic_k', 'stochastic_d'
        ]
        
        logger.info(f"✅ TradingMLModel initialized with {model_type} approach")
    
    def _initialize_models(self):
        """Initialize the ML models"""
        self.models = {
            'random_forest': RandomForestRegressor(
                n_estimators=100,
                max_depth=10,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42
            ),
            'gradient_boosting': GradientBoostingRegressor(
                n_estimators=100,
                learning_rate=0.1,
                max_depth=6,
                random_state=42
            ),
            'linear_ridge': Ridge(
                alpha=1.0,
                random_state=42
            )
        }
        
        # Initialize scalers for each model
        for model_name in self.models.keys():
            self.scalers[model_name] = StandardScaler()
    
    def prepare_features(self, historical_data: Dict[str, Any], 
                        technical_indicators: Dict[str, Any]) -> pd.DataFrame:
        """
        Prepare feature matrix from historical data and technical indicators.
        
        Args:
            historical_data: Historical price and volume data
            technical_indicators: Technical analysis indicators
            
        Returns:
            Feature DataFrame ready for ML training/prediction
        """
        try:
            # Convert to DataFrame if needed
            if isinstance(historical_data, dict):
                # If all values are scalars, create a single-row DataFrame
                if all(isinstance(v, (int, float)) for v in historical_data.values()):
                    df = pd.DataFrame([historical_data])
                else:
                    df = pd.DataFrame(historical_data)
            else:
                df = historical_data.copy()
            
            # Ensure we have required columns
            required_cols = ['close', 'volume', 'high', 'low', 'open']
            for col in required_cols:
                if col not in df.columns:
                    logger.warning(f"Missing required column: {col}")
                    return pd.DataFrame()
            
            # Add technical indicators to DataFrame
            for indicator, value in technical_indicators.items():
                if isinstance(value, (int, float)):
                    df[indicator] = value
                elif isinstance(value, dict) and 'value' in value:
                    df[indicator] = value['value']
            
            # Feature engineering
            df = self._engineer_features(df)
            
            # Select only the features we want to use
            available_features = [f for f in self.technical_features if f in df.columns]
            
            if len(available_features) < 5:
                logger.warning(f"Insufficient features available: {len(available_features)}")
                return pd.DataFrame()
            
            # Return feature matrix
            feature_df = df[available_features].copy()
            
            # Handle missing values
            feature_df = feature_df.fillna(method='ffill').fillna(method='bfill')
            feature_df = feature_df.fillna(0)  # Final fallback
            
            return feature_df
            
        except Exception as e:
            logger.error(f"Error preparing features: {e}")
            return pd.DataFrame()
    
    def _engineer_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Engineer additional features from raw data"""
        try:
            # Price-based features
            df['price_change_1d'] = df['close'].pct_change(1)
            df['price_change_5d'] = df['close'].pct_change(5)
            df['price_change_20d'] = df['close'].pct_change(20)
            
            # Volatility features
            df['volatility_20d'] = df['close'].rolling(20).std()
            df['volatility_5d'] = df['close'].rolling(5).std()
            
            # Momentum features
            df['momentum_10d'] = df['close'] / df['close'].shift(10) - 1
            df['momentum_5d'] = df['close'] / df['close'].shift(5) - 1
            
            # Volume features
            df['volume_change'] = df['volume'].pct_change(1)
            df['volume_sma'] = df['volume'].rolling(20).mean()
            df['volume_ratio'] = df['volume'] / df['volume_sma']
            
            # Price position features
            df['high_low_ratio'] = (df['close'] - df['low']) / (df['high'] - df['low'])
            df['open_close_ratio'] = df['close'] / df['open'] - 1
            
            # Moving average features
            df['sma_20'] = df['close'].rolling(20).mean()
            df['sma_50'] = df['close'].rolling(50).mean()
            df['sma_200'] = df['close'].rolling(200).mean()
            df['ema_12'] = df['close'].ewm(span=12).mean()
            df['ema_26'] = df['close'].ewm(span=26).mean()
            
            # Relative position to moving averages
            df['price_vs_sma20'] = df['close'] / df['sma_20'] - 1
            df['price_vs_sma50'] = df['close'] / df['sma_50'] - 1
            
            return df
            
        except Exception as e:
            logger.error(f"Error engineering features: {e}")
            return df
    
    def train_models(self, training_data: List[Dict[str, Any]], 
                    target_horizon: str = "1D") -> Dict[str, ModelPerformance]:
        """
        Train all models on historical data.
        
        Args:
            training_data: List of historical data points with features and targets
            target_horizon: Prediction horizon ("1D", "5D", "1W", "1M")
            
        Returns:
            Performance metrics for each model
        """
        try:
            if len(training_data) < 50:
                logger.warning("Insufficient training data")
                return {}
            
            # Prepare training dataset
            X, y = self._prepare_training_data(training_data, target_horizon)
            
            if X.empty or len(y) == 0:
                logger.warning("Failed to prepare training data")
                return {}
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42, shuffle=False
            )
            
            performance_results = {}
            
            # Train each model
            for model_name, model in self.models.items():
                try:
                    logger.info(f"Training {model_name} model...")
                    
                    # Scale features
                    scaler = self.scalers[model_name]
                    X_train_scaled = scaler.fit_transform(X_train)
                    X_test_scaled = scaler.transform(X_test)
                    
                    # Train model
                    model.fit(X_train_scaled, y_train)
                    
                    # Make predictions
                    y_pred = model.predict(X_test_scaled)
                    
                    # Calculate performance metrics
                    performance = self._calculate_performance(y_test, y_pred, model_name)
                    performance_results[model_name] = performance
                    
                    # Store feature importance if available
                    if hasattr(model, 'feature_importances_'):
                        self.feature_importance[model_name] = dict(
                            zip(X.columns, model.feature_importances_)
                        )
                    
                    logger.info(f"✅ {model_name} trained - R²: {performance.r2_score:.3f}")
                    
                except Exception as e:
                    logger.error(f"Error training {model_name}: {e}")
                    continue
            
            self.performance_metrics = performance_results
            self.is_trained = len(performance_results) > 0
            
            if self.is_trained:
                logger.info(f"✅ ML models trained successfully ({len(performance_results)} models)")
            
            return performance_results
            
        except Exception as e:
            logger.error(f"Error training models: {e}")
            return {}
    
    def _prepare_training_data(self, training_data: List[Dict[str, Any]], 
                             target_horizon: str) -> Tuple[pd.DataFrame, List[float]]:
        """Prepare training data with features and targets"""
        try:
            features_list = []
            targets = []
            
            for data_point in training_data:
                # Extract features
                historical = data_point.get('historical', {})
                technical = data_point.get('technical', {})
                
                feature_df = self.prepare_features(historical, technical)
                
                if not feature_df.empty and len(feature_df) > 0:
                    # Use the last row of features
                    features_list.append(feature_df.iloc[-1])
                    
                    # Extract target (future price change)
                    target = data_point.get('target', 0.0)
                    targets.append(target)
            
            if features_list:
                X = pd.DataFrame(features_list)
                return X, targets
            else:
                return pd.DataFrame(), []
                
        except Exception as e:
            logger.error(f"Error preparing training data: {e}")
            return pd.DataFrame(), []
    
    def _calculate_performance(self, y_true: List[float], y_pred: List[float], 
                             model_name: str) -> ModelPerformance:
        """Calculate comprehensive performance metrics"""
        try:
            # Regression metrics
            mse = mean_squared_error(y_true, y_pred)
            mae = mean_absolute_error(y_true, y_pred)
            r2 = r2_score(y_true, y_pred)
            
            # Classification metrics (direction prediction)
            y_true_direction = [1 if y > 0 else 0 for y in y_true]
            y_pred_direction = [1 if y > 0 else 0 for y in y_pred]
            
            # Calculate accuracy, precision, recall
            tp = sum(1 for i in range(len(y_true_direction)) 
                    if y_true_direction[i] == 1 and y_pred_direction[i] == 1)
            fp = sum(1 for i in range(len(y_true_direction)) 
                    if y_true_direction[i] == 0 and y_pred_direction[i] == 1)
            fn = sum(1 for i in range(len(y_true_direction)) 
                    if y_true_direction[i] == 1 and y_pred_direction[i] == 0)
            tn = sum(1 for i in range(len(y_true_direction)) 
                    if y_true_direction[i] == 0 and y_pred_direction[i] == 0)
            
            accuracy = (tp + tn) / len(y_true_direction) if len(y_true_direction) > 0 else 0
            precision = tp / (tp + fp) if (tp + fp) > 0 else 0
            recall = tp / (tp + fn) if (tp + fn) > 0 else 0
            
            return ModelPerformance(
                mse=mse,
                mae=mae,
                r2_score=r2,
                accuracy=accuracy,
                precision=precision,
                recall=recall,
                last_updated=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"Error calculating performance for {model_name}: {e}")
            return ModelPerformance(0, 0, 0, 0, 0, 0, datetime.now())
    
    def predict(self, historical_data: Dict[str, Any], 
               technical_indicators: Dict[str, Any],
               prediction_horizon: str = "1D") -> MLPrediction:
        """
        Make ML prediction for price movement.
        
        Args:
            historical_data: Historical price and volume data
            technical_indicators: Technical analysis indicators
            prediction_horizon: Prediction timeframe
            
        Returns:
            ML prediction with confidence and direction
        """
        try:
            if not self.is_trained:
                return self._fallback_prediction(historical_data, technical_indicators)
            
            # Prepare features
            feature_df = self.prepare_features(historical_data, technical_indicators)
            
            if feature_df.empty:
                return self._fallback_prediction(historical_data, technical_indicators)
            
            # Get the latest feature vector
            features = feature_df.iloc[-1:].values
            
            # Make predictions with all models
            predictions = {}
            confidences = {}
            
            for model_name, model in self.models.items():
                try:
                    # Scale features
                    scaler = self.scalers[model_name]
                    features_scaled = scaler.transform(features)
                    
                    # Make prediction
                    pred = model.predict(features_scaled)[0]
                    predictions[model_name] = pred
                    
                    # Calculate confidence based on model performance
                    performance = self.performance_metrics.get(model_name)
                    if performance:
                        confidence = max(0, min(100, performance.r2_score * 100))
                        confidences[model_name] = confidence
                    else:
                        confidences[model_name] = 50  # Default confidence
                        
                except Exception as e:
                    logger.warning(f"Prediction failed for {model_name}: {e}")
                    continue
            
            if not predictions:
                return self._fallback_prediction(historical_data, technical_indicators)
            
            # Ensemble prediction (weighted average)
            total_weight = sum(confidences.values())
            if total_weight > 0:
                ensemble_pred = sum(
                    pred * confidences[model_name] 
                    for model_name, pred in predictions.items()
                ) / total_weight
                ensemble_confidence = sum(confidences.values()) / len(confidences)
            else:
                ensemble_pred = sum(predictions.values()) / len(predictions)
                ensemble_confidence = 50
            
            # Determine direction and probability
            direction = "UP" if ensemble_pred > 0.02 else "DOWN" if ensemble_pred < -0.02 else "SIDEWAYS"
            probability = min(0.95, abs(ensemble_pred) * 10)  # Convert to probability
            
            # Calculate predicted price (assuming ensemble_pred is percentage change)
            current_price = historical_data.get('close', [0])[-1] if isinstance(historical_data.get('close'), list) else historical_data.get('close', 0)
            predicted_price = current_price * (1 + ensemble_pred)
            
            return MLPrediction(
                predicted_price=predicted_price,
                confidence=ensemble_confidence,
                direction=direction,
                probability=probability,
                model_used="ensemble",
                features_used=list(feature_df.columns),
                prediction_horizon=prediction_horizon
            )
            
        except Exception as e:
            logger.error(f"Error making ML prediction: {e}")
            return self._fallback_prediction(historical_data, technical_indicators)
    
    def _fallback_prediction(self, historical_data: Dict[str, Any], 
                           technical_indicators: Dict[str, Any]) -> MLPrediction:
        """Fallback prediction when ML models are not available"""
        try:
            # Simple heuristic-based prediction
            current_price = historical_data.get('close', [0])[-1] if isinstance(historical_data.get('close'), list) else historical_data.get('close', 0)
            
            # Use RSI and MACD for simple prediction
            rsi = technical_indicators.get('rsi', 50)
            macd_histogram = technical_indicators.get('macd', {}).get('histogram', 0)
            
            # Simple scoring
            score = 0
            if rsi < 30:
                score += 0.05  # Oversold, expect bounce
            elif rsi > 70:
                score -= 0.05  # Overbought, expect decline
            
            if macd_histogram > 0:
                score += 0.02  # Bullish momentum
            else:
                score -= 0.02  # Bearish momentum
            
            direction = "UP" if score > 0.01 else "DOWN" if score < -0.01 else "SIDEWAYS"
            predicted_price = current_price * (1 + score)
            
            return MLPrediction(
                predicted_price=predicted_price,
                confidence=40,  # Lower confidence for fallback
                direction=direction,
                probability=min(0.7, abs(score) * 20),
                model_used="heuristic_fallback",
                features_used=["rsi", "macd"],
                prediction_horizon="1D"
            )
            
        except Exception as e:
            logger.error(f"Error in fallback prediction: {e}")
            return MLPrediction(
                predicted_price=0,
                confidence=0,
                direction="SIDEWAYS",
                probability=0,
                model_used="error_fallback",
                features_used=[],
                prediction_horizon="1D"
            )
    
    def get_model_summary(self) -> Dict[str, Any]:
        """Get comprehensive model summary"""
        return {
            'is_trained': self.is_trained,
            'model_type': self.model_type,
            'models_available': list(self.models.keys()),
            'performance_metrics': {
                name: {
                    'r2_score': perf.r2_score,
                    'accuracy': perf.accuracy,
                    'last_updated': perf.last_updated.isoformat()
                } for name, perf in self.performance_metrics.items()
            },
            'feature_importance': self.feature_importance,
            'features_used': self.technical_features
        }

    async def train_on_sample_data(self, symbols: List[str] = None) -> Dict[str, Any]:
        """
        Train models on sample historical data for immediate activation.

        This method collects recent historical data and trains the ML models
        so they can provide real predictions instead of fallback heuristics.

        Args:
            symbols: List of symbols to use for training. Defaults to major tech stocks.

        Returns:
            Training results and performance metrics
        """
        try:
            if symbols is None:
                symbols = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'META', 'NVDA', 'NFLX']

            logger.info(f"🚀 Starting ML model training on {len(symbols)} symbols")

            # Import here to avoid circular imports
            from src.shared.data_providers.aggregator import DataProviderAggregator
            from src.shared.technical_analysis.calculator import TechnicalAnalysisCalculator

            data_manager = DataProviderAggregator()
            tech_calc = TechnicalAnalysisCalculator()

            training_data = []

            for symbol in symbols:
                try:
                    logger.info(f"📊 Collecting training data for {symbol}")

                    # Get historical data (last 100 days for training)
                    historical_data = await data_manager.get_historical_data(
                        symbol=symbol,
                        days=100
                    )

                    # Check if data was successfully retrieved
                    if not historical_data or not historical_data.get('success', False):
                        logger.warning(f"Failed to get data for {symbol}: {historical_data.get('error', 'Unknown error')}")
                        continue

                    # Extract the actual data
                    data_obj = historical_data.get('data')
                    if not data_obj:
                        logger.warning(f"No data object for {symbol}, skipping")
                        continue

                    # Get price arrays
                    closes = data_obj.closes if hasattr(data_obj, 'closes') else []
                    opens = data_obj.opens if hasattr(data_obj, 'opens') else []
                    highs = data_obj.highs if hasattr(data_obj, 'highs') else []
                    lows = data_obj.lows if hasattr(data_obj, 'lows') else []
                    volumes = data_obj.volumes if hasattr(data_obj, 'volumes') else []

                    if len(closes) < 50:
                        logger.warning(f"Insufficient data for {symbol} ({len(closes)} days), skipping")
                        continue

                    # Create training samples (use last 10 days, predict next day)
                    for i in range(len(closes) - 10, len(closes) - 1):  # Last 10 days
                        if i < 20:  # Need at least 20 days for indicators
                            continue

                        # Current data point
                        current_data = {
                            'close': closes[i],
                            'volume': volumes[i] if i < len(volumes) else 0,
                            'high': highs[i] if i < len(highs) else closes[i],
                            'low': lows[i] if i < len(lows) else closes[i],
                            'open': opens[i] if i < len(opens) else closes[i]
                        }

                        # Future price (target)
                        future_price = closes[i + 1]
                        target = (future_price / closes[i]) - 1  # Percentage change

                        # Calculate simple technical indicators
                        simple_tech = {}
                        if i >= 20:  # Need at least 20 days for moving averages
                            # Simple moving averages
                            sma_5 = sum(closes[i-4:i+1]) / 5
                            sma_20 = sum(closes[i-19:i+1]) / 20
                            simple_tech = {
                                'sma_5': sma_5,
                                'sma_20': sma_20,
                                'rsi': 50.0,  # Placeholder
                                'macd': 0.0   # Placeholder
                            }

                        # Add to training data
                        training_data.append({
                            'historical': current_data,
                            'technical': simple_tech,
                            'target': target
                        })

                except Exception as e:
                    logger.warning(f"Failed to collect data for {symbol}: {e}")
                    continue

            if len(training_data) < 20:
                logger.error(f"Insufficient training data: {len(training_data)} samples")
                return {
                    'success': False,
                    'error': 'Insufficient training data',
                    'samples_collected': len(training_data)
                }

            logger.info(f"✅ Collected {len(training_data)} training samples")

            # Train the models
            performance_results = self.train_models(training_data)

            if performance_results:
                logger.info(f"🎯 ML models trained successfully!")
                for model_name, perf in performance_results.items():
                    logger.info(f"   📈 {model_name}: R²={perf.r2_score:.3f}, Accuracy={perf.accuracy:.3f}")

                return {
                    'success': True,
                    'models_trained': list(performance_results.keys()),
                    'training_samples': len(training_data),
                    'performance': {
                        name: {
                            'r2_score': perf.r2_score,
                            'accuracy': perf.accuracy,
                            'mse': perf.mse,
                            'mae': perf.mae
                        } for name, perf in performance_results.items()
                    },
                    'symbols_used': symbols
                }
            else:
                return {
                    'success': False,
                    'error': 'Training failed',
                    'samples_collected': len(training_data)
                }

        except Exception as e:
            logger.error(f"ML training failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'samples_collected': 0
            }


# Global ML model instance
trading_ml_model = TradingMLModel()
