from typing import Dict, List, Optional, Tuple
import logging
import numpy as np
from dataclasses import dataclass
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

@dataclass
class GrowthAnalysis:
    """Data class for growth analysis results"""
    revenue_growth: Optional[float]
    earnings_growth: Optional[float]
    growth_trend: str
    growth_strength: str
    confidence: float
    historical_growth_rates: Dict[str, float]

class GrowthCalculator:
    """Calculator for company growth metrics analysis"""
    
    def __init__(self):
        self.growth_benchmarks = {
            "technology": 0.15,  # 15% annual growth benchmark
            "healthcare": 0.10,
            "financial": 0.08,
            "consumer": 0.07,
            "industrial": 0.06,
            "energy": 0.05
        }
    
    def calculate_revenue_growth(self, current_revenue: float, previous_revenue: float) -> Optional[float]:
        """
        Calculate revenue growth rate
        
        Args:
            current_revenue: Current period revenue
            previous_revenue: Previous period revenue
            
        Returns:
            Growth rate as decimal or None if invalid
        """
        if previous_revenue <= 0 or current_revenue <= 0:
            return None
            
        return (current_revenue - previous_revenue) / previous_revenue
    
    def calculate_earnings_growth(self, current_earnings: float, previous_earnings: float) -> Optional[float]:
        """
        Calculate earnings growth rate
        
        Args:
            current_earnings: Current period earnings
            previous_earnings: Previous period earnings
            
        Returns:
            Growth rate as decimal or None if invalid
        """
        if previous_earnings == 0:  # Handle division by zero and negative earnings
            return None
        if previous_earnings < 0 and current_earnings > 0:  # Turning profitable
            return 1.0  # 100% growth (simplified)
            
        return (current_earnings - previous_earnings) / abs(previous_earnings)
    
    def analyze_growth(self, 
                      revenue_data: List[Tuple[datetime, float]],
                      earnings_data: List[Tuple[datetime, float]],
                      industry: str = None) -> GrowthAnalysis:
        """
        Comprehensive growth analysis
        
        Args:
            revenue_data: List of (date, revenue) tuples in chronological order
            earnings_data: List of (date, earnings) tuples in chronological order
            industry: Industry classification for benchmarking
            
        Returns:
            GrowthAnalysis object with detailed analysis
        """
        # Calculate most recent growth rates
        revenue_growth = self._calculate_most_recent_growth(revenue_data)
        earnings_growth = self._calculate_most_recent_growth(earnings_data)
        
        # Calculate historical growth rates
        historical_rates = self._calculate_historical_growth_rates(revenue_data, earnings_data)
        
        # Determine growth trend and strength
        growth_trend = self._determine_growth_trend(historical_rates)
        growth_strength = self._determine_growth_strength(revenue_growth, earnings_growth, industry)
        
        # Calculate confidence
        confidence = self._calculate_confidence(revenue_data, earnings_data)
        
        return GrowthAnalysis(
            revenue_growth=revenue_growth,
            earnings_growth=earnings_growth,
            growth_trend=growth_trend,
            growth_strength=growth_strength,
            confidence=confidence,
            historical_growth_rates=historical_rates
        )
    
    def _calculate_most_recent_growth(self, data: List[Tuple[datetime, float]]) -> Optional[float]:
        """
        Calculate growth rate for the most recent period
        
        Args:
            data: List of (date, value) tuples
            
        Returns:
            Most recent growth rate or None
        """
        if len(data) < 2:
            return None
            
        # Sort by date to ensure chronological order
        sorted_data = sorted(data, key=lambda x: x[0])
        current_value = sorted_data[-1][1]
        previous_value = sorted_data[-2][1]
        
        return self.calculate_revenue_growth(current_value, previous_value)
    
    def _calculate_historical_growth_rates(self, 
                                         revenue_data: List[Tuple[datetime, float]],
                                         earnings_data: List[Tuple[datetime, float]]) -> Dict[str, float]:
        """
        Calculate various historical growth rates
        
        Args:
            revenue_data: Revenue time series
            earnings_data: Earnings time series
            
        Returns:
            Dictionary of growth metrics
        """
        rates = {}
        
        # Revenue growth rates
        if len(revenue_data) >= 2:
            sorted_revenue = sorted(revenue_data, key=lambda x: x[0])
            rates['revenue_1y'] = self.calculate_revenue_growth(sorted_revenue[-1][1], sorted_revenue[-2][1])
            
            if len(revenue_data) >= 5:
                rates['revenue_3y_avg'] = self._calculate_multi_year_avg_growth(revenue_data, 3)
            if len(revenue_data) >= 9:
                rates['revenue_5y_avg'] = self._calculate_multi_year_avg_growth(revenue_data, 5)
        
        # Earnings growth rates
        if len(earnings_data) >= 2:
            sorted_earnings = sorted(earnings_data, key=lambda x: x[0])
            rates['earnings_1y'] = self.calculate_earnings_growth(sorted_earnings[-1][1], sorted_earnings[-2][1])
            
            if len(earnings_data) >= 5:
                rates['earnings_3y_avg'] = self._calculate_multi_year_avg_growth(earnings_data, 3)
            if len(earnings_data) >= 9:
                rates['earnings_5y_avg'] = self._calculate_multi_year_avg_growth(earnings_data, 5)
        
        return rates
    
    def _calculate_multi_year_avg_growth(self, data: List[Tuple[datetime, float]], years: int) -> Optional[float]:
        """
        Calculate average growth rate over multiple years
        
        Args:
            data: Time series data
            years: Number of years for average
            
        Returns:
            Average growth rate or None
        """
        if len(data) < years + 1:
            return None
            
        sorted_data = sorted(data, key=lambda x: x[0])
        growth_rates = []
        
        for i in range(years):
            if len(sorted_data) >= i + 2:
                current = sorted_data[-(i+1)][1]
                previous = sorted_data[-(i+2)][1]
                growth = self.calculate_revenue_growth(current, previous)
                if growth is not None:
                    growth_rates.append(growth)
        
        if not growth_rates:
            return None
            
        return sum(growth_rates) / len(growth_rates)
    
    def _determine_growth_trend(self, historical_rates: Dict[str, float]) -> str:
        """
        Determine overall growth trend
        
        Args:
            historical_rates: Dictionary of historical growth rates
            
        Returns:
            Growth trend description
        """
        if not historical_rates:
            return "UNKNOWN"
            
        # Check if we have recent growth rates
        recent_revenue = historical_rates.get('revenue_1y')
        recent_earnings = historical_rates.get('earnings_1y')
        
        if recent_revenue is None or recent_earnings is None:
            return "INSUFFICIENT_DATA"
            
        # Determine trend based on recent performance
        if recent_revenue > 0.2 and recent_earnings > 0.2:
            return "STRONG_GROWTH"
        elif recent_revenue > 0.1 and recent_earnings > 0.1:
            return "MODERATE_GROWTH"
        elif recent_revenue > 0 and recent_earnings > 0:
            return "SLOW_GROWTH"
        elif recent_revenue < 0 or recent_earnings < 0:
            return "DECLINING"
        else:
            return "STABLE"
    
    def _determine_growth_strength(self, revenue_growth: Optional[float], 
                                  earnings_growth: Optional[float], 
                                  industry: str) -> str:
        """
        Determine growth strength relative to industry
        
        Args:
            revenue_growth: Revenue growth rate
            earnings_growth: Earnings growth rate
            industry: Industry classification
            
        Returns:
            Growth strength description
        """
        if revenue_growth is None or earnings_growth is None:
            return "UNKNOWN"
            
        industry_benchmark = self.growth_benchmarks.get(industry.lower(), 0.08)
        
        # Check if growth exceeds industry benchmark
        if revenue_growth > industry_benchmark * 1.5 and earnings_growth > industry_benchmark * 1.5:
            return "OUTPERFORMING"
        elif revenue_growth > industry_benchmark and earnings_growth > industry_benchmark:
            return "ABOVE_AVERAGE"
        elif revenue_growth > 0 and earnings_growth > 0:
            return "AVERAGE"
        else:
            return "UNDERPERFORMING"
    
    def _calculate_confidence(self, revenue_data: List[Tuple[datetime, float]], 
                            earnings_data: List[Tuple[datetime, float]]) -> float:
        """
        Calculate analysis confidence
        
        Args:
            revenue_data: Revenue data
            earnings_data: Earnings data
            
        Returns:
            Confidence score (0-1)
        """
        confidence = 0.5  # Base confidence
        
        # Increase confidence with more data points
        if len(revenue_data) >= 5:
            confidence += 0.2
        if len(earnings_data) >= 5:
            confidence += 0.2
            
        # Additional confidence for longer history
        if len(revenue_data) >= 10:
            confidence += 0.1
        if len(earnings_data) >= 10:
            confidence += 0.1
            
        return min(1.0, max(0.0, confidence))

# Factory function
def create_growth_calculator() -> GrowthCalculator:
    """Create and return a GrowthCalculator instance"""
    return GrowthCalculator()