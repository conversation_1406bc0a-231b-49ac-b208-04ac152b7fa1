from typing import Dict, List, Optional, <PERSON>ple
import logging
import numpy as np
import pandas as pd
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class BetaAnalysis:
    """Data class for beta coefficient analysis results"""
    beta: Optional[float]
    alpha: Optional[float]
    r_squared: Optional[float]
    risk_profile: str
    confidence: float

class BetaCalculator:
    """Calculator for beta coefficient analysis (measure of stock volatility relative to market)"""
    
    def __init__(self):
        self.min_data_points = 20  # Minimum data points for reliable beta calculation
    
    def calculate_beta(self, stock_returns: List[float], market_returns: List[float]) -> Tuple[Optional[float], Optional[float], Optional[float]]:
        """
        Calculate beta, alpha, and R-squared using linear regression
        
        Args:
            stock_returns: List of daily stock returns
            market_returns: List of daily market returns (e.g., S&P 500)
            
        Returns:
            Tuple of (beta, alpha, r_squared) or (None, None, None) if insufficient data
        """
        if len(stock_returns) != len(market_returns) or len(stock_returns) < self.min_data_points:
            return None, None, None
            
        try:
            # Convert to numpy arrays for calculation
            market_returns_np = np.array(market_returns)
            stock_returns_np = np.array(stock_returns)
            
            # Calculate covariance and variance
            covariance = np.cov(stock_returns_np, market_returns_np)[0][1]
            market_variance = np.var(market_returns_np)
            
            if market_variance == 0:
                return None, None, None
                
            beta = covariance / market_variance
            
            # Calculate alpha (intercept) using average returns
            alpha = np.mean(stock_returns_np) - beta * np.mean(market_returns_np)
            
            # Calculate R-squared
            ss_res = np.sum((stock_returns_np - (alpha + beta * market_returns_np)) ** 2)
            ss_tot = np.sum((stock_returns_np - np.mean(stock_returns_np)) ** 2)
            
            if ss_tot == 0:
                r_squared = 0.0
            else:
                r_squared = 1 - (ss_res / ss_tot)
            
            return beta, alpha, r_squared
            
        except Exception as e:
            logger.error(f"Error calculating beta: {e}")
            return None, None, None
    
    def analyze_beta(self, stock_prices: List[float], market_prices: List[float]) -> BetaAnalysis:
        """
        Comprehensive beta analysis
        
        Args:
            stock_prices: List of daily stock closing prices
            market_prices: List of daily market index closing prices (e.g., SPY)
            
        Returns:
            BetaAnalysis object with detailed analysis
        """
        if len(stock_prices) != len(market_prices) or len(stock_prices) < 2:
            return BetaAnalysis(
                beta=None,
                alpha=None,
                r_squared=None,
                risk_profile="INSUFFICIENT_DATA",
                confidence=0.0
            )
        
        # Calculate daily returns
        stock_returns = self._calculate_returns(stock_prices)
        market_returns = self._calculate_returns(market_prices)
        
        beta, alpha, r_squared = self.calculate_beta(stock_returns, market_returns)
        
        # Determine risk profile
        risk_profile = self._determine_risk_profile(beta)
        
        # Calculate confidence
        confidence = self._calculate_confidence(stock_returns, market_returns, r_squared)
        
        return BetaAnalysis(
            beta=beta,
            alpha=alpha,
            r_squared=r_squared,
            risk_profile=risk_profile,
            confidence=confidence
        )
    
    def calculate_rolling_beta(self, stock_prices: List[float], market_prices: List[float], window: int = 60) -> List[Optional[float]]:
        """
        Calculate rolling beta over a specified window
        
        Args:
            stock_prices: List of stock prices
            market_prices: List of market prices
            window: Rolling window size in days
            
        Returns:
            List of rolling beta values
        """
        if len(stock_prices) != len(market_prices) or len(stock_prices) < window + 1:
            return [None] * len(stock_prices)
            
        stock_returns = self._calculate_returns(stock_prices)
        market_returns = self._calculate_returns(market_prices)
        
        rolling_betas = []
        for i in range(len(stock_returns)):
            if i < window:
                rolling_betas.append(None)
            else:
                window_stock_returns = stock_returns[i-window:i]
                window_market_returns = market_returns[i-window:i]
                beta, _, _ = self.calculate_beta(window_stock_returns, window_market_returns)
                rolling_betas.append(beta)
                
        return rolling_betas
    
    def _calculate_returns(self, prices: List[float]) -> List[float]:
        """
        Calculate daily returns from price data
        
        Args:
            prices: List of daily closing prices
            
        Returns:
            List of daily returns as decimals
        """
        returns = []
        for i in range(1, len(prices)):
            if prices[i-1] != 0:  # Avoid division by zero
                daily_return = (prices[i] - prices[i-1]) / prices[i-1]
                returns.append(daily_return)
        return returns
    
    def _determine_risk_profile(self, beta: Optional[float]) -> str:
        """
        Determine risk profile based on beta value
        
        Args:
            beta: Beta coefficient
            
        Returns:
            Risk profile string
        """
        if beta is None:
            return "UNKNOWN"
            
        if beta < 0.8:
            return "DEFENSIVE"
        elif beta < 1.2:
            return "MARKET_NEUTRAL"
        elif beta < 1.5:
            return "AGGRESSIVE"
        else:
            return "HIGHLY_VOLATILE"
    
    def _calculate_confidence(self, stock_returns: List[float], market_returns: List[float], r_squared: Optional[float]) -> float:
        """
        Calculate analysis confidence
        
        Args:
            stock_returns: Stock return data
            market_returns: Market return data
            r_squared: R-squared value from regression
            
        Returns:
            Confidence score (0-1)
        """
        if len(stock_returns) < self.min_data_points:
            return 0.3
            
        confidence = 0.7  # Base confidence
        
        # Adjust based on data length
        if len(stock_returns) >= 100:
            confidence += 0.2
        elif len(stock_returns) >= 50:
            confidence += 0.1
            
        # Adjust based on R-squared
        if r_squared is not None:
            if r_squared > 0.7:
                confidence += 0.1
            elif r_squared > 0.5:
                confidence += 0.05
            elif r_squared < 0.3:
                confidence -= 0.1
                
        return min(1.0, max(0.0, confidence))

# Factory function
def create_beta_calculator() -> BetaCalculator:
    """Create and return a BetaCalculator instance"""
    return BetaCalculator()