"""
Enhanced Risk Assessment System

Comprehensive risk analysis including beta calculation, correlation analysis,
VaR (Value at Risk), maximum drawdown, and advanced risk metrics.
"""

import logging
import numpy as np
import pandas as pd
import yfinance as yf
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)


@dataclass
class EnhancedRiskMetrics:
    """Enhanced risk assessment metrics"""
    # Basic risk metrics
    volatility: Optional[float]  # Annualized volatility
    beta: Optional[float]  # Beta vs market
    alpha: Optional[float]  # Alpha vs market
    sharpe_ratio: Optional[float]  # Risk-adjusted return
    
    # Advanced risk metrics
    var_95: Optional[float]  # 95% Value at Risk
    var_99: Optional[float]  # 99% Value at Risk
    max_drawdown: Optional[float]  # Maximum drawdown
    downside_deviation: Optional[float]  # Downside volatility
    
    # Correlation metrics
    market_correlation: Optional[float]  # Correlation with market
    sector_correlation: Optional[float]  # Correlation with sector
    
    # Risk warnings and levels
    risk_level: str  # "LOW", "MEDIUM", "HIGH", "EXTREME"
    risk_warnings: List[str]
    risk_score: float  # 0-100 (higher = riskier)
    
    # Metadata
    calculation_period: str
    market_benchmark: str
    last_updated: datetime


@dataclass
class PortfolioRiskMetrics:
    """Portfolio-level risk metrics"""
    portfolio_beta: float
    portfolio_volatility: float
    portfolio_var: float
    diversification_ratio: float
    concentration_risk: float
    correlation_matrix: Dict[str, Dict[str, float]]


class EnhancedRiskAssessment:
    """
    Enhanced risk assessment system with comprehensive metrics.
    
    Features:
    - Beta and alpha calculation vs market benchmarks
    - Value at Risk (VaR) calculation
    - Maximum drawdown analysis
    - Correlation analysis with market and sector
    - Downside risk metrics
    - Portfolio-level risk assessment
    """
    
    def __init__(self, market_benchmark: str = "SPY"):
        """Initialize the enhanced risk assessment system"""
        self.market_benchmark = market_benchmark
        self.sector_benchmarks = {
            'Technology': 'XLK',
            'Healthcare': 'XLV',
            'Financial': 'XLF',
            'Energy': 'XLE',
            'Consumer Discretionary': 'XLY',
            'Consumer Staples': 'XLP',
            'Industrials': 'XLI',
            'Materials': 'XLB',
            'Utilities': 'XLU',
            'Real Estate': 'XLRE',
            'Communication': 'XLC'
        }
        
        # Risk thresholds
        self.risk_thresholds = {
            'volatility': {'low': 0.15, 'medium': 0.25, 'high': 0.40},
            'beta': {'low': 0.8, 'medium': 1.2, 'high': 1.5},
            'max_drawdown': {'low': 0.10, 'medium': 0.20, 'high': 0.35},
            'var_95': {'low': 0.05, 'medium': 0.10, 'high': 0.15}
        }
        
        logger.info(f"✅ Enhanced risk assessment initialized with benchmark: {market_benchmark}")
    
    async def assess_comprehensive_risk(self, symbol: str, 
                                      historical_data: Dict[str, Any],
                                      period: str = "1y") -> EnhancedRiskMetrics:
        """
        Perform comprehensive risk assessment for a symbol.
        
        Args:
            symbol: Stock symbol to analyze
            historical_data: Historical price and volume data
            period: Analysis period ("1y", "2y", "5y")
            
        Returns:
            Enhanced risk metrics
        """
        try:
            # Convert historical data to DataFrame
            df = self._prepare_price_data(historical_data, symbol)
            
            if df.empty or len(df) < 30:
                return self._create_fallback_risk_metrics(symbol, "Insufficient data")
            
            # Get market benchmark data
            market_data = await self._get_benchmark_data(self.market_benchmark, period)
            
            # Calculate basic risk metrics
            volatility = self._calculate_volatility(df['returns'])
            
            # Calculate beta and alpha
            beta, alpha, market_correlation = self._calculate_beta_alpha(
                df['returns'], market_data['returns'] if not market_data.empty else None
            )
            
            # Calculate Sharpe ratio
            sharpe_ratio = self._calculate_sharpe_ratio(df['returns'])
            
            # Calculate Value at Risk
            var_95, var_99 = self._calculate_var(df['returns'])
            
            # Calculate maximum drawdown
            max_drawdown = self._calculate_max_drawdown(df['price'])
            
            # Calculate downside deviation
            downside_deviation = self._calculate_downside_deviation(df['returns'])
            
            # Get sector correlation if possible
            sector_correlation = await self._calculate_sector_correlation(symbol, df['returns'], period)
            
            # Determine risk level and warnings
            risk_level, risk_warnings, risk_score = self._assess_risk_level(
                volatility, beta, max_drawdown, var_95
            )
            
            return EnhancedRiskMetrics(
                volatility=volatility,
                beta=beta,
                alpha=alpha,
                sharpe_ratio=sharpe_ratio,
                var_95=var_95,
                var_99=var_99,
                max_drawdown=max_drawdown,
                downside_deviation=downside_deviation,
                market_correlation=market_correlation,
                sector_correlation=sector_correlation,
                risk_level=risk_level,
                risk_warnings=risk_warnings,
                risk_score=risk_score,
                calculation_period=period,
                market_benchmark=self.market_benchmark,
                last_updated=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"Enhanced risk assessment failed for {symbol}: {e}")
            return self._create_fallback_risk_metrics(symbol, str(e))
    
    def _prepare_price_data(self, historical_data: Dict[str, Any], symbol: str) -> pd.DataFrame:
        """Prepare price data for analysis"""
        try:
            if isinstance(historical_data, dict) and 'close' in historical_data:
                # Convert dict format to DataFrame
                prices = historical_data['close']
                if isinstance(prices, list) and len(prices) > 0:
                    df = pd.DataFrame({'price': prices})
                    df['returns'] = df['price'].pct_change().dropna()
                    return df
            
            # Try to get data from yfinance as fallback
            ticker = yf.Ticker(symbol)
            hist = ticker.history(period="1y")
            
            if not hist.empty:
                df = pd.DataFrame({
                    'price': hist['Close'],
                    'returns': hist['Close'].pct_change().dropna()
                })
                return df
            
            return pd.DataFrame()
            
        except Exception as e:
            logger.error(f"Error preparing price data for {symbol}: {e}")
            return pd.DataFrame()
    
    async def _get_benchmark_data(self, benchmark: str, period: str) -> pd.DataFrame:
        """Get benchmark data for comparison"""
        try:
            ticker = yf.Ticker(benchmark)
            hist = ticker.history(period=period)
            
            if not hist.empty:
                return pd.DataFrame({
                    'price': hist['Close'],
                    'returns': hist['Close'].pct_change().dropna()
                })
            
            return pd.DataFrame()
            
        except Exception as e:
            logger.error(f"Error getting benchmark data for {benchmark}: {e}")
            return pd.DataFrame()
    
    def _calculate_volatility(self, returns: pd.Series) -> Optional[float]:
        """Calculate annualized volatility"""
        try:
            if len(returns) < 10:
                return None
            
            # Calculate daily volatility
            daily_vol = returns.std()
            
            # Annualize (assuming 252 trading days)
            annual_vol = daily_vol * np.sqrt(252)
            
            return float(annual_vol)
            
        except Exception as e:
            logger.error(f"Volatility calculation error: {e}")
            return None
    
    def _calculate_beta_alpha(self, stock_returns: pd.Series, 
                            market_returns: Optional[pd.Series]) -> Tuple[Optional[float], Optional[float], Optional[float]]:
        """Calculate beta, alpha, and correlation with market"""
        try:
            if market_returns is None or len(stock_returns) < 30 or len(market_returns) < 30:
                return None, None, None
            
            # Align the series
            aligned_data = pd.DataFrame({
                'stock': stock_returns,
                'market': market_returns
            }).dropna()
            
            if len(aligned_data) < 30:
                return None, None, None
            
            # Calculate beta using linear regression
            slope, intercept, r_value, p_value, std_err = stats.linregress(
                aligned_data['market'], aligned_data['stock']
            )
            
            beta = slope
            alpha = intercept * 252  # Annualize alpha
            correlation = r_value
            
            return float(beta), float(alpha), float(correlation)
            
        except Exception as e:
            logger.error(f"Beta/Alpha calculation error: {e}")
            return None, None, None
    
    def _calculate_sharpe_ratio(self, returns: pd.Series, risk_free_rate: float = 0.02) -> Optional[float]:
        """Calculate Sharpe ratio"""
        try:
            if len(returns) < 10:
                return None
            
            # Calculate annualized return
            annual_return = (1 + returns.mean()) ** 252 - 1
            
            # Calculate annualized volatility
            annual_vol = returns.std() * np.sqrt(252)
            
            if annual_vol == 0:
                return None
            
            # Calculate Sharpe ratio
            sharpe = (annual_return - risk_free_rate) / annual_vol
            
            return float(sharpe)
            
        except Exception as e:
            logger.error(f"Sharpe ratio calculation error: {e}")
            return None
    
    def _calculate_var(self, returns: pd.Series) -> Tuple[Optional[float], Optional[float]]:
        """Calculate Value at Risk at 95% and 99% confidence levels"""
        try:
            if len(returns) < 30:
                return None, None
            
            # Calculate VaR using historical simulation
            var_95 = float(np.percentile(returns, 5))  # 5th percentile for 95% VaR
            var_99 = float(np.percentile(returns, 1))  # 1st percentile for 99% VaR
            
            return abs(var_95), abs(var_99)
            
        except Exception as e:
            logger.error(f"VaR calculation error: {e}")
            return None, None
    
    def _calculate_max_drawdown(self, prices: pd.Series) -> Optional[float]:
        """Calculate maximum drawdown"""
        try:
            if len(prices) < 10:
                return None
            
            # Calculate running maximum
            running_max = prices.expanding().max()
            
            # Calculate drawdown
            drawdown = (prices - running_max) / running_max
            
            # Find maximum drawdown
            max_dd = float(abs(drawdown.min()))
            
            return max_dd
            
        except Exception as e:
            logger.error(f"Max drawdown calculation error: {e}")
            return None
    
    def _calculate_downside_deviation(self, returns: pd.Series, target_return: float = 0) -> Optional[float]:
        """Calculate downside deviation"""
        try:
            if len(returns) < 10:
                return None
            
            # Calculate downside returns (below target)
            downside_returns = returns[returns < target_return]
            
            if len(downside_returns) == 0:
                return 0.0
            
            # Calculate downside deviation
            downside_dev = downside_returns.std() * np.sqrt(252)
            
            return float(downside_dev)
            
        except Exception as e:
            logger.error(f"Downside deviation calculation error: {e}")
            return None
    
    async def _calculate_sector_correlation(self, symbol: str, returns: pd.Series, period: str) -> Optional[float]:
        """Calculate correlation with sector benchmark"""
        try:
            # This is a simplified implementation
            # In practice, you'd need to determine the stock's sector first
            # For now, we'll use a default sector ETF
            sector_etf = "XLK"  # Technology sector as default
            
            sector_data = await self._get_benchmark_data(sector_etf, period)
            
            if sector_data.empty:
                return None
            
            # Align the series
            aligned_data = pd.DataFrame({
                'stock': returns,
                'sector': sector_data['returns']
            }).dropna()
            
            if len(aligned_data) < 30:
                return None
            
            correlation = aligned_data['stock'].corr(aligned_data['sector'])
            
            return float(correlation)
            
        except Exception as e:
            logger.error(f"Sector correlation calculation error: {e}")
            return None
    
    def _assess_risk_level(self, volatility: Optional[float], beta: Optional[float], 
                          max_drawdown: Optional[float], var_95: Optional[float]) -> Tuple[str, List[str], float]:
        """Assess overall risk level and generate warnings"""
        risk_score = 0
        warnings = []
        
        # Volatility assessment
        if volatility is not None:
            if volatility > self.risk_thresholds['volatility']['high']:
                risk_score += 30
                warnings.append(f"Very high volatility ({volatility:.1%})")
            elif volatility > self.risk_thresholds['volatility']['medium']:
                risk_score += 20
                warnings.append(f"High volatility ({volatility:.1%})")
            elif volatility > self.risk_thresholds['volatility']['low']:
                risk_score += 10
        
        # Beta assessment
        if beta is not None:
            if abs(beta) > self.risk_thresholds['beta']['high']:
                risk_score += 25
                warnings.append(f"Very high beta ({beta:.2f})")
            elif abs(beta) > self.risk_thresholds['beta']['medium']:
                risk_score += 15
                warnings.append(f"High beta ({beta:.2f})")
        
        # Maximum drawdown assessment
        if max_drawdown is not None:
            if max_drawdown > self.risk_thresholds['max_drawdown']['high']:
                risk_score += 25
                warnings.append(f"High maximum drawdown ({max_drawdown:.1%})")
            elif max_drawdown > self.risk_thresholds['max_drawdown']['medium']:
                risk_score += 15
                warnings.append(f"Moderate maximum drawdown ({max_drawdown:.1%})")
        
        # VaR assessment
        if var_95 is not None:
            if var_95 > self.risk_thresholds['var_95']['high']:
                risk_score += 20
                warnings.append(f"High Value at Risk ({var_95:.1%})")
            elif var_95 > self.risk_thresholds['var_95']['medium']:
                risk_score += 10
        
        # Determine risk level
        if risk_score >= 70:
            risk_level = "EXTREME"
        elif risk_score >= 50:
            risk_level = "HIGH"
        elif risk_score >= 25:
            risk_level = "MEDIUM"
        else:
            risk_level = "LOW"
        
        return risk_level, warnings, min(100, risk_score)
    
    def _create_fallback_risk_metrics(self, symbol: str, error_msg: str) -> EnhancedRiskMetrics:
        """Create fallback risk metrics when calculation fails"""
        return EnhancedRiskMetrics(
            volatility=None,
            beta=None,
            alpha=None,
            sharpe_ratio=None,
            var_95=None,
            var_99=None,
            max_drawdown=None,
            downside_deviation=None,
            market_correlation=None,
            sector_correlation=None,
            risk_level="UNKNOWN",
            risk_warnings=[f"Risk assessment failed: {error_msg}"],
            risk_score=50,  # Neutral score
            calculation_period="unknown",
            market_benchmark=self.market_benchmark,
            last_updated=datetime.now()
        )


# Global instance
enhanced_risk_assessment = EnhancedRiskAssessment()
