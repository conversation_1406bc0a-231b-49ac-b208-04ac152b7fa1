"""
Monte Carlo Simulation for Price Probability Analysis

This module provides Monte Carlo simulation capabilities for analyzing
price movement probabilities and generating probabilistic forecasts.
"""

import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
import asyncio
from concurrent.futures import ThreadPoolExecutor

logger = logging.getLogger(__name__)

@dataclass
class MonteCarloResult:
    """Results from Monte Carlo simulation"""
    symbol: str
    current_price: float
    target_price: float
    time_horizon_days: int
    simulation_runs: int
    
    # Probability results
    probability_of_reaching_target: float
    probability_bullish: float
    probability_bearish: float
    probability_neutral: float
    
    # Statistical measures
    mean_expected_price: float
    std_expected_price: float
    confidence_intervals: Dict[str, float]  # 25%, 50%, 75%, 90%, 95%
    
    # Risk metrics
    max_drawdown_probability: float
    volatility_forecast: float
    value_at_risk_95: float
    
    # Technical indicators used
    indicators_used: List[str]
    
    # Key insights
    key_insights: List[str]
    
    timestamp: str = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now().isoformat()

class MonteCarloSimulator:
    """Monte Carlo simulator for price movement analysis"""
    
    def __init__(self, max_workers: int = 4):
        self.max_workers = max_workers
        self.logger = logger
        
    def simulate_price_paths(self, 
                           current_price: float,
                           historical_returns: pd.Series,
                           time_horizon_days: int,
                           num_simulations: int = 10000,
                           volatility_adjustment: float = 1.0) -> np.ndarray:
        """
        Run Monte Carlo simulation for price paths
        
        Args:
            current_price: Starting price
            historical_returns: Historical daily returns
            time_horizon_days: Number of days to simulate
            num_simulations: Number of simulation runs
            volatility_adjustment: Factor to adjust volatility (1.0 = normal)
            
        Returns:
            Array of final prices from all simulations
        """
        try:
            # Calculate historical statistics
            mean_return = historical_returns.mean()
            std_return = historical_returns.std() * volatility_adjustment
            
            # Generate random returns for each simulation
            random_returns = np.random.normal(
                mean_return, 
                std_return, 
                (num_simulations, time_horizon_days)
            )
            
            # Calculate price paths
            price_paths = np.zeros((num_simulations, time_horizon_days + 1))
            price_paths[:, 0] = current_price
            
            for i in range(time_horizon_days):
                price_paths[:, i + 1] = price_paths[:, i] * (1 + random_returns[:, i])
            
            return price_paths
            
        except Exception as e:
            self.logger.error(f"Error in Monte Carlo simulation: {e}")
            return np.array([])
    
    def calculate_probabilities(self, 
                              final_prices: np.ndarray,
                              current_price: float,
                              target_price: float,
                              threshold_percent: float = 0.02) -> Dict[str, float]:
        """
        Calculate probabilities from simulation results
        
        Args:
            final_prices: Array of final prices from simulations
            current_price: Current price
            target_price: Target price to reach
            threshold_percent: Threshold for neutral scenario (2% = neutral if within 2% of current)
            
        Returns:
            Dictionary of probabilities
        """
        try:
            if len(final_prices) == 0:
                return {
                    'probability_of_reaching_target': 0.5,
                    'probability_bullish': 0.33,
                    'probability_bearish': 0.33,
                    'probability_neutral': 0.34
                }
            
            # Calculate probability of reaching target
            target_reached = np.sum(final_prices >= target_price)
            probability_of_reaching_target = target_reached / len(final_prices)
            
            # Calculate bullish/bearish/neutral probabilities
            threshold = current_price * threshold_percent
            bullish = np.sum(final_prices > current_price + threshold)
            bearish = np.sum(final_prices < current_price - threshold)
            neutral = len(final_prices) - bullish - bearish
            
            # Ensure probabilities don't exceed 100%
            total = len(final_prices)
            if total > 0:
                bullish = min(bullish, total)
                bearish = min(bearish, total)
                neutral = min(neutral, total)
            
            return {
                'probability_of_reaching_target': probability_of_reaching_target,
                'probability_bullish': bullish / len(final_prices),
                'probability_bearish': bearish / len(final_prices),
                'probability_neutral': neutral / len(final_prices)
            }
            
        except Exception as e:
            self.logger.error(f"Error calculating probabilities: {e}")
            return {
                'probability_of_reaching_target': 0.5,
                'probability_bullish': 0.33,
                'probability_bearish': 0.33,
                'probability_neutral': 0.34
            }
    
    def calculate_risk_metrics(self, 
                              price_paths: np.ndarray,
                              current_price: float) -> Dict[str, float]:
        """
        Calculate risk metrics from price paths
        
        Args:
            price_paths: Array of price paths (simulations x days)
            current_price: Current price
            
        Returns:
            Dictionary of risk metrics
        """
        try:
            if price_paths.size == 0:
                return {
                    'max_drawdown_probability': 0.1,
                    'volatility_forecast': 0.2,
                    'value_at_risk_95': current_price * 0.05
                }
            
            # Calculate daily returns for each path
            returns = np.diff(price_paths, axis=1) / price_paths[:, :-1]
            
            # Calculate volatility forecast (average of path volatilities)
            path_volatilities = np.std(returns, axis=1)
            volatility_forecast = np.mean(path_volatilities)
            
            # Calculate max drawdown for each path
            max_drawdowns = []
            for path in price_paths:
                peak = np.maximum.accumulate(path)
                drawdown = (peak - path) / peak
                max_drawdowns.append(np.max(drawdown))
            
            max_drawdown_probability = np.mean(max_drawdowns)
            
            # Calculate Value at Risk (95% confidence)
            final_prices = price_paths[:, -1]
            var_95 = np.percentile(final_prices, 5)
            value_at_risk_95 = current_price - var_95
            
            return {
                'max_drawdown_probability': max_drawdown_probability,
                'volatility_forecast': volatility_forecast,
                'value_at_risk_95': value_at_risk_95
            }
            
        except Exception as e:
            self.logger.error(f"Error calculating risk metrics: {e}")
            return {
                'max_drawdown_probability': 0.1,
                'volatility_forecast': 0.2,
                'value_at_risk_95': current_price * 0.05
            }
    
    def generate_insights(self, 
                         result: MonteCarloResult,
                         technical_indicators: Dict[str, Any]) -> List[str]:
        """
        Generate key insights from simulation results
        
        Args:
            result: Monte Carlo simulation result
            technical_indicators: Technical indicators data
            
        Returns:
            List of key insights
        """
        insights = []
        
        try:
            # Probability insights
            if result.probability_of_reaching_target > 0.7:
                insights.append(f"High probability ({result.probability_of_reaching_target:.1%}) of reaching target price")
            elif result.probability_of_reaching_target < 0.3:
                insights.append(f"Low probability ({result.probability_of_reaching_target:.1%}) of reaching target price")
            else:
                insights.append(f"Moderate probability ({result.probability_of_reaching_target:.1%}) of reaching target price")
            
            # Volatility insights
            if result.volatility_forecast > 0.03:
                insights.append(f"High volatility expected ({result.volatility_forecast:.1%} daily)")
            elif result.volatility_forecast < 0.01:
                insights.append(f"Low volatility expected ({result.volatility_forecast:.1%} daily)")
            
            # Risk insights
            if result.max_drawdown_probability > 0.2:
                insights.append(f"High drawdown risk ({result.max_drawdown_probability:.1%} max drawdown)")
            
            # Technical indicator insights
            if 'rsi' in technical_indicators:
                rsi = technical_indicators['rsi']
                if rsi > 70:
                    insights.append("RSI indicates overbought conditions")
                elif rsi < 30:
                    insights.append("RSI indicates oversold conditions")
            
            if 'macd' in technical_indicators:
                macd_signal = technical_indicators.get('macd_signal', 0)
                if macd_signal > 0:
                    insights.append("MACD shows bullish momentum")
                else:
                    insights.append("MACD shows bearish momentum")
            
            # Market regime insights
            if result.probability_bullish > 0.6:
                insights.append("Simulation favors bullish scenario")
            elif result.probability_bearish > 0.6:
                insights.append("Simulation favors bearish scenario")
            else:
                insights.append("Simulation shows mixed signals")
            
        except Exception as e:
            self.logger.error(f"Error generating insights: {e}")
            insights.append("Analysis completed with standard metrics")
        
        return insights
    
    async def run_analysis(self,
                          symbol: str,
                          current_price: float,
                          target_price: float,
                          historical_data: pd.DataFrame,
                          time_horizon_days: int = 5,
                          num_simulations: int = 10000,
                          technical_indicators: Optional[Dict[str, Any]] = None) -> MonteCarloResult:
        """
        Run complete Monte Carlo analysis
        
        Args:
            symbol: Stock symbol
            current_price: Current price
            target_price: Target price to analyze
            historical_data: Historical OHLCV data
            time_horizon_days: Days to simulate
            num_simulations: Number of simulation runs
            technical_indicators: Technical indicators data
            
        Returns:
            MonteCarloResult object
        """
        try:
            self.logger.info(f"Starting Monte Carlo analysis for {symbol}")
            
            # Calculate historical returns
            if 'close' in historical_data.columns:
                prices = historical_data['close']
            else:
                prices = historical_data.iloc[:, -1]  # Assume last column is price
            
            returns = prices.pct_change().dropna()
            
            if len(returns) < 10:
                self.logger.warning(f"Insufficient historical data for {symbol}")
                return self._create_default_result(symbol, current_price, target_price, time_horizon_days, num_simulations)
            
            # Run simulation in thread pool to avoid blocking
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                loop = asyncio.get_event_loop()
                price_paths = await loop.run_in_executor(
                    executor,
                    self.simulate_price_paths,
                    current_price,
                    returns,
                    time_horizon_days,
                    num_simulations
                )
            
            if price_paths.size == 0:
                return self._create_default_result(symbol, current_price, target_price, time_horizon_days, num_simulations)
            
            # Calculate probabilities
            probabilities = self.calculate_probabilities(price_paths, current_price, target_price)
            
            # Calculate risk metrics
            risk_metrics = self.calculate_risk_metrics(price_paths, current_price)
            
            # Calculate confidence intervals
            final_prices = price_paths[:, -1]
            confidence_intervals = {
                '25%': np.percentile(final_prices, 25),
                '50%': np.percentile(final_prices, 50),
                '75%': np.percentile(final_prices, 75),
                '90%': np.percentile(final_prices, 90),
                '95%': np.percentile(final_prices, 95)
            }
            
            # Generate insights
            technical_indicators = technical_indicators or {}
            insights = self.generate_insights(
                MonteCarloResult(
                    symbol=symbol,
                    current_price=current_price,
                    target_price=target_price,
                    time_horizon_days=time_horizon_days,
                    simulation_runs=num_simulations,
                    probability_of_reaching_target=probabilities['probability_of_reaching_target'],
                    probability_bullish=probabilities['probability_bullish'],
                    probability_bearish=probabilities['probability_bearish'],
                    probability_neutral=probabilities['probability_neutral'],
                    mean_expected_price=np.mean(final_prices),
                    std_expected_price=np.std(final_prices),
                    confidence_intervals=confidence_intervals,
                    max_drawdown_probability=risk_metrics['max_drawdown_probability'],
                    volatility_forecast=risk_metrics['volatility_forecast'],
                    value_at_risk_95=risk_metrics['value_at_risk_95'],
                    indicators_used=list(technical_indicators.keys()),
                    key_insights=[]
                ),
                technical_indicators
            )
            
            # Create final result
            result = MonteCarloResult(
                symbol=symbol,
                current_price=current_price,
                target_price=target_price,
                time_horizon_days=time_horizon_days,
                simulation_runs=num_simulations,
                probability_of_reaching_target=probabilities['probability_of_reaching_target'],
                probability_bullish=probabilities['probability_bullish'],
                probability_bearish=probabilities['probability_bearish'],
                probability_neutral=probabilities['probability_neutral'],
                mean_expected_price=np.mean(final_prices),
                std_expected_price=np.std(final_prices),
                confidence_intervals=confidence_intervals,
                max_drawdown_probability=risk_metrics['max_drawdown_probability'],
                volatility_forecast=risk_metrics['volatility_forecast'],
                value_at_risk_95=risk_metrics['value_at_risk_95'],
                indicators_used=list(technical_indicators.keys()),
                key_insights=insights
            )
            
            self.logger.info(f"Monte Carlo analysis completed for {symbol}")
            return result
            
        except Exception as e:
            self.logger.error(f"Error in Monte Carlo analysis for {symbol}: {e}")
            return self._create_default_result(symbol, current_price, target_price, time_horizon_days, num_simulations)
    
    def _create_default_result(self, 
                              symbol: str, 
                              current_price: float, 
                              target_price: float,
                              time_horizon_days: int,
                              num_simulations: int) -> MonteCarloResult:
        """Create default result when analysis fails"""
        return MonteCarloResult(
            symbol=symbol,
            current_price=current_price,
            target_price=target_price,
            time_horizon_days=time_horizon_days,
            simulation_runs=num_simulations,
            probability_of_reaching_target=0.5,
            probability_bullish=0.33,
            probability_bearish=0.33,
            probability_neutral=0.34,
            mean_expected_price=current_price,
            std_expected_price=current_price * 0.1,
            confidence_intervals={
                '25%': current_price * 0.9,
                '50%': current_price,
                '75%': current_price * 1.1,
                '90%': current_price * 1.2,
                '95%': current_price * 1.3
            },
            max_drawdown_probability=0.1,
            volatility_forecast=0.02,
            value_at_risk_95=current_price * 0.05,
            indicators_used=[],
            key_insights=["Analysis completed with default parameters due to insufficient data"]
        )
