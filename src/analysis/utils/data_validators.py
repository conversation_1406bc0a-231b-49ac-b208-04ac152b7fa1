from typing import Dict, List, Optional, Any, Union
import logging
import re
from datetime import datetime, date
import numpy as np

logger = logging.getLogger(__name__)

class DataValidator:
    """Validator for financial data quality and consistency"""
    
    def __init__(self):
        self.symbol_pattern = re.compile(r'^[A-Z]{1,5}$')  # Basic stock symbol pattern
        self.price_pattern = re.compile(r'^\d+(\.\d{1,4})?$')  # Price format
    
    def validate_stock_symbol(self, symbol: str) -> bool:
        """
        Validate stock symbol format
        
        Args:
            symbol: Stock symbol to validate
            
        Returns:
            True if valid, False otherwise
        """
        if not symbol or not isinstance(symbol, str):
            return False
        return bool(self.symbol_pattern.match(symbol.upper()))
    
    def validate_price(self, price: Union[float, str]) -> bool:
        """
        Validate price value
        
        Args:
            price: Price to validate
            
        Returns:
            True if valid, False otherwise
        """
        if price is None:
            return False
            
        try:
            price_float = float(price)
            return price_float > 0 and price_float < 10000  # Reasonable price range
        except (ValueError, TypeError):
            return False
    
    def validate_percentage(self, percentage: Union[float, str]) -> bool:
        """
        Validate percentage value (-100% to 1000% range)
        
        Args:
            percentage: Percentage to validate
            
        Returns:
            True if valid, False otherwise
        """
        if percentage is None:
            return False
            
        try:
            pct_float = float(percentage)
            return -100 <= pct_float <= 1000  # Allow for extreme moves
        except (ValueError, TypeError):
            return False
    
    def validate_date(self, date_str: str, date_format: str = '%Y-%m-%d') -> bool:
        """
        Validate date string format
        
        Args:
            date_str: Date string to validate
            date_format: Expected date format
            
        Returns:
            True if valid, False otherwise
        """
        if not date_str or not isinstance(date_str, str):
            return False
            
        try:
            datetime.strptime(date_str, date_format)
            return True
        except ValueError:
            return False
    
    def validate_financial_data(self, data: Dict[str, Any]) -> Dict[str, bool]:
        """
        Validate comprehensive financial data
        
        Args:
            data: Dictionary of financial data
            
        Returns:
            Dictionary of validation results for each field
        """
        validation_results = {}
        
        # Validate basic fields
        validation_results['symbol'] = self.validate_stock_symbol(data.get('symbol', ''))
        validation_results['price'] = self.validate_price(data.get('price'))
        validation_results['volume'] = self._validate_volume(data.get('volume'))
        
        # Validate financial metrics
        validation_results['pe_ratio'] = self._validate_pe_ratio(data.get('pe_ratio'))
        validation_results['eps'] = self._validate_eps(data.get('eps'))
        validation_results['market_cap'] = self._validate_market_cap(data.get('market_cap'))
        
        # Validate growth rates
        validation_results['revenue_growth'] = self.validate_percentage(data.get('revenue_growth'))
        validation_results['earnings_growth'] = self.validate_percentage(data.get('earnings_growth'))
        
        return validation_results
    
    def validate_technical_data(self, data: Dict[str, Any]) -> Dict[str, bool]:
        """
        Validate technical analysis data
        
        Args:
            data: Dictionary of technical data
            
        Returns:
            Dictionary of validation results for each field
        """
        validation_results = {}
        
        # Validate technical indicators
        validation_results['rsi'] = self._validate_rsi(data.get('rsi'))
        validation_results['macd'] = self._validate_macd(data.get('macd'))
        validation_results['moving_averages'] = self._validate_moving_averages(data.get('moving_averages', {}))
        
        # Validate volatility measures
        validation_results['volatility'] = self.validate_percentage(data.get('volatility'))
        validation_results['beta'] = self._validate_beta(data.get('beta'))
        
        return validation_results
    
    def _validate_volume(self, volume: Optional[int]) -> bool:
        """Validate trading volume"""
        if volume is None:
            return False
        return isinstance(volume, int) and volume >= 0
    
    def _validate_pe_ratio(self, pe_ratio: Optional[float]) -> bool:
        """Validate P/E ratio"""
        if pe_ratio is None:
            return True  # Some companies may not have P/E
        try:
            pe = float(pe_ratio)
            return pe > 0 and pe < 1000  # Reasonable P/E range
        except (ValueError, TypeError):
            return False
    
    def _validate_eps(self, eps: Optional[float]) -> bool:
        """Validate Earnings Per Share"""
        if eps is None:
            return True  # Some companies may have negative EPS
        try:
            eps_float = float(eps)
            return abs(eps_float) < 1000  # Reasonable EPS range
        except (ValueError, TypeError):
            return False
    
    def _validate_market_cap(self, market_cap: Optional[float]) -> bool:
        """Validate market capitalization"""
        if market_cap is None:
            return False
        try:
            cap = float(market_cap)
            return cap > 0 and cap < 10**12  # Up to $1 trillion
        except (ValueError, TypeError):
            return False
    
    def _validate_rsi(self, rsi: Optional[float]) -> bool:
        """Validate RSI value"""
        if rsi is None:
            return True
        try:
            rsi_float = float(rsi)
            return 0 <= rsi_float <= 100
        except (ValueError, TypeError):
            return False
    
    def _validate_macd(self, macd: Optional[float]) -> bool:
        """Validate MACD value"""
        if macd is None:
            return True
        try:
            macd_float = float(macd)
            return abs(macd_float) < 100  # Reasonable MACD range
        except (ValueError, TypeError):
            return False
    
    def _validate_moving_averages(self, moving_averages: Dict[str, float]) -> bool:
        """Validate moving averages"""
        if not isinstance(moving_averages, dict):
            return False
        
        for key, value in moving_averages.items():
            if not self.validate_price(value):
                return False
        return True
    
    def _validate_beta(self, beta: Optional[float]) -> bool:
        """Validate beta coefficient"""
        if beta is None:
            return True
        try:
            beta_float = float(beta)
            return 0 <= beta_float <= 5  # Reasonable beta range
        except (ValueError, TypeError):
            return False
    
    def calculate_data_quality_score(self, validation_results: Dict[str, bool]) -> float:
        """
        Calculate overall data quality score
        
        Args:
            validation_results: Dictionary of validation results
            
        Returns:
            Data quality score (0-1)
        """
        if not validation_results:
            return 0.0
            
        valid_count = sum(1 for is_valid in validation_results.values() if is_valid)
        total_count = len(validation_results)
        
        return valid_count / total_count if total_count > 0 else 0.0

# Factory function
def create_data_validator() -> DataValidator:
    """Create and return a DataValidator instance"""
    return DataValidator()