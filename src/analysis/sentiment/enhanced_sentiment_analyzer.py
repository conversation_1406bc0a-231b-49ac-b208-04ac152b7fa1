"""
Enhanced Sentiment Analysis for Trading

Comprehensive sentiment analysis from multiple sources including news,
social media, market sentiment, and AI-powered text analysis.
"""

import logging
import asyncio
import aiohttp
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import re
import json
from textblob import TextBlob
import yfinance as yf

logger = logging.getLogger(__name__)


@dataclass
class SentimentScore:
    """Sentiment analysis result"""
    score: float  # -1.0 to 1.0 (negative to positive)
    confidence: float  # 0.0 to 1.0
    magnitude: float  # 0.0 to 1.0 (strength of sentiment)
    source: str
    timestamp: datetime
    details: Dict[str, Any]


@dataclass
class ComprehensiveSentiment:
    """Comprehensive sentiment analysis result"""
    overall_score: float  # -1.0 to 1.0
    overall_confidence: float  # 0.0 to 1.0
    sentiment_label: str  # "VERY_POSITIVE", "POSITIVE", "NEUTRAL", "NEGATIVE", "VERY_NEGATIVE"
    source_scores: Dict[str, SentimentScore]
    market_sentiment: Optional[float]
    news_sentiment: Optional[float]
    social_sentiment: Optional[float]
    technical_sentiment: Optional[float]
    timestamp: datetime


class EnhancedSentimentAnalyzer:
    """
    Enhanced sentiment analyzer with multiple data sources.
    
    Features:
    - News sentiment analysis
    - Social media sentiment (Reddit, Twitter-like)
    - Market sentiment indicators
    - Technical sentiment from price action
    - AI-powered text analysis
    """
    
    def __init__(self):
        """Initialize the enhanced sentiment analyzer"""
        self.session = None
        
        # Sentiment keywords for different sources
        self.bullish_keywords = [
            'bullish', 'buy', 'long', 'moon', 'rocket', 'pump', 'surge', 'rally',
            'breakout', 'uptrend', 'strong', 'positive', 'growth', 'earnings beat',
            'upgrade', 'outperform', 'target raised', 'momentum', 'catalyst'
        ]
        
        self.bearish_keywords = [
            'bearish', 'sell', 'short', 'crash', 'dump', 'decline', 'drop',
            'breakdown', 'downtrend', 'weak', 'negative', 'loss', 'earnings miss',
            'downgrade', 'underperform', 'target lowered', 'resistance', 'risk'
        ]
        
        # Market sentiment indicators
        self.fear_greed_weights = {
            'vix': 0.3,  # Volatility index
            'put_call_ratio': 0.2,
            'market_momentum': 0.2,
            'safe_haven_demand': 0.15,
            'junk_bond_demand': 0.15
        }
        
        logger.info("✅ Enhanced sentiment analyzer initialized")
    
    async def analyze_comprehensive_sentiment(self, symbol: str, 
                                            include_sources: List[str] = None) -> ComprehensiveSentiment:
        """
        Perform comprehensive sentiment analysis from multiple sources.
        
        Args:
            symbol: Stock symbol to analyze
            include_sources: List of sources to include ['news', 'social', 'market', 'technical']
            
        Returns:
            Comprehensive sentiment analysis result
        """
        if include_sources is None:
            include_sources = ['news', 'social', 'market', 'technical']
        
        source_scores = {}
        
        try:
            # Initialize session if needed
            if not self.session:
                self.session = aiohttp.ClientSession()
            
            # Analyze different sources concurrently
            tasks = []
            
            if 'news' in include_sources:
                tasks.append(self._analyze_news_sentiment(symbol))
            if 'social' in include_sources:
                tasks.append(self._analyze_social_sentiment(symbol))
            if 'market' in include_sources:
                tasks.append(self._analyze_market_sentiment(symbol))
            if 'technical' in include_sources:
                tasks.append(self._analyze_technical_sentiment(symbol))
            
            # Execute all tasks
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Process results
            source_names = [s for s in include_sources if s in ['news', 'social', 'market', 'technical']]
            for i, result in enumerate(results):
                if isinstance(result, SentimentScore):
                    source_scores[source_names[i]] = result
                elif isinstance(result, Exception):
                    logger.warning(f"Sentiment analysis failed for {source_names[i]}: {result}")
            
            # Calculate overall sentiment
            overall_score, overall_confidence = self._calculate_overall_sentiment(source_scores)
            sentiment_label = self._get_sentiment_label(overall_score)
            
            # Extract individual sentiment scores
            news_sentiment = source_scores.get('news', SentimentScore(0, 0, 0, 'news', datetime.now(), {})).score
            social_sentiment = source_scores.get('social', SentimentScore(0, 0, 0, 'social', datetime.now(), {})).score
            market_sentiment = source_scores.get('market', SentimentScore(0, 0, 0, 'market', datetime.now(), {})).score
            technical_sentiment = source_scores.get('technical', SentimentScore(0, 0, 0, 'technical', datetime.now(), {})).score
            
            return ComprehensiveSentiment(
                overall_score=overall_score,
                overall_confidence=overall_confidence,
                sentiment_label=sentiment_label,
                source_scores=source_scores,
                market_sentiment=market_sentiment,
                news_sentiment=news_sentiment,
                social_sentiment=social_sentiment,
                technical_sentiment=technical_sentiment,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"Comprehensive sentiment analysis failed for {symbol}: {e}")
            return self._create_fallback_sentiment(symbol)
    
    async def _analyze_news_sentiment(self, symbol: str) -> SentimentScore:
        """Analyze sentiment from news sources"""
        try:
            # Simulate news sentiment analysis
            # In production, this would integrate with news APIs like NewsAPI, Alpha Vantage News, etc.
            
            # Mock news headlines for demonstration
            mock_headlines = [
                f"{symbol} reports strong quarterly earnings",
                f"Analysts upgrade {symbol} price target",
                f"{symbol} announces new product launch",
                f"Market volatility affects {symbol} trading"
            ]
            
            sentiment_scores = []
            for headline in mock_headlines:
                # Use TextBlob for basic sentiment analysis
                blob = TextBlob(headline)
                sentiment_scores.append(blob.sentiment.polarity)
            
            # Calculate average sentiment
            avg_sentiment = sum(sentiment_scores) / len(sentiment_scores) if sentiment_scores else 0
            
            # Calculate confidence based on consistency
            if sentiment_scores:
                variance = sum((s - avg_sentiment) ** 2 for s in sentiment_scores) / len(sentiment_scores)
                confidence = max(0.1, 1.0 - variance)
            else:
                confidence = 0.1
            
            # Enhance with keyword analysis
            keyword_sentiment = self._analyze_keywords_sentiment(mock_headlines)
            final_sentiment = (avg_sentiment * 0.7) + (keyword_sentiment * 0.3)
            
            return SentimentScore(
                score=final_sentiment,
                confidence=confidence,
                magnitude=abs(final_sentiment),
                source="news",
                timestamp=datetime.now(),
                details={
                    'headlines_analyzed': len(mock_headlines),
                    'avg_polarity': avg_sentiment,
                    'keyword_sentiment': keyword_sentiment,
                    'variance': variance if sentiment_scores else 0
                }
            )
            
        except Exception as e:
            logger.error(f"News sentiment analysis failed: {e}")
            return SentimentScore(0, 0.1, 0, "news", datetime.now(), {"error": str(e)})
    
    async def _analyze_social_sentiment(self, symbol: str) -> SentimentScore:
        """Analyze sentiment from social media sources"""
        try:
            # Simulate social media sentiment analysis
            # In production, this would integrate with Reddit API, Twitter API, etc.
            
            mock_social_posts = [
                f"$${symbol} looking strong today! 🚀",
                f"Thinking of buying more {symbol} on this dip",
                f"{symbol} chart looks bullish to me",
                f"Not sure about {symbol} with current market conditions",
                f"$${symbol} to the moon! 📈"
            ]
            
            sentiment_scores = []
            for post in mock_social_posts:
                # Analyze with TextBlob
                blob = TextBlob(post)
                base_sentiment = blob.sentiment.polarity
                
                # Enhance with emoji and symbol analysis
                emoji_boost = 0.1 if any(emoji in post for emoji in ['🚀', '📈', '💎']) else 0
                emoji_penalty = -0.1 if any(emoji in post for emoji in ['📉', '💀', '😰']) else 0
                
                final_sentiment = base_sentiment + emoji_boost + emoji_penalty
                sentiment_scores.append(max(-1, min(1, final_sentiment)))
            
            # Calculate weighted average (more recent posts have higher weight)
            weights = [1.0 - (i * 0.1) for i in range(len(sentiment_scores))]
            weighted_sentiment = sum(s * w for s, w in zip(sentiment_scores, weights)) / sum(weights)
            
            # Calculate confidence based on volume and consistency
            confidence = min(0.9, 0.3 + (len(sentiment_scores) * 0.1))
            
            return SentimentScore(
                score=weighted_sentiment,
                confidence=confidence,
                magnitude=abs(weighted_sentiment),
                source="social",
                timestamp=datetime.now(),
                details={
                    'posts_analyzed': len(mock_social_posts),
                    'weighted_sentiment': weighted_sentiment,
                    'confidence_factors': {
                        'volume': len(sentiment_scores),
                        'recency_weighted': True
                    }
                }
            )
            
        except Exception as e:
            logger.error(f"Social sentiment analysis failed: {e}")
            return SentimentScore(0, 0.1, 0, "social", datetime.now(), {"error": str(e)})
    
    async def _analyze_market_sentiment(self, symbol: str) -> SentimentScore:
        """Analyze overall market sentiment indicators"""
        try:
            # Get market sentiment indicators
            market_indicators = await self._get_market_sentiment_indicators()
            
            # Calculate composite market sentiment
            sentiment_score = 0
            total_weight = 0
            
            for indicator, value in market_indicators.items():
                weight = self.fear_greed_weights.get(indicator, 0.1)
                sentiment_score += value * weight
                total_weight += weight
            
            if total_weight > 0:
                sentiment_score /= total_weight
            
            # Normalize to -1 to 1 range
            sentiment_score = max(-1, min(1, sentiment_score))
            
            # Confidence based on number of indicators available
            confidence = min(0.8, len(market_indicators) * 0.15)
            
            return SentimentScore(
                score=sentiment_score,
                confidence=confidence,
                magnitude=abs(sentiment_score),
                source="market",
                timestamp=datetime.now(),
                details={
                    'indicators': market_indicators,
                    'composite_score': sentiment_score,
                    'indicators_count': len(market_indicators)
                }
            )
            
        except Exception as e:
            logger.error(f"Market sentiment analysis failed: {e}")
            return SentimentScore(0, 0.1, 0, "market", datetime.now(), {"error": str(e)})
    
    async def _analyze_technical_sentiment(self, symbol: str) -> SentimentScore:
        """Analyze sentiment from technical indicators and price action"""
        try:
            # Get recent price data
            ticker = yf.Ticker(symbol)
            hist = ticker.history(period="1mo")
            
            if hist.empty:
                return SentimentScore(0, 0.1, 0, "technical", datetime.now(), {"error": "No price data"})
            
            # Calculate technical sentiment factors
            sentiment_factors = []
            
            # Price momentum (last 5 days vs previous 5 days)
            if len(hist) >= 10:
                recent_avg = hist['Close'][-5:].mean()
                previous_avg = hist['Close'][-10:-5].mean()
                momentum = (recent_avg - previous_avg) / previous_avg
                sentiment_factors.append(min(1, max(-1, momentum * 10)))
            
            # Volume trend
            if len(hist) >= 10:
                recent_volume = hist['Volume'][-5:].mean()
                previous_volume = hist['Volume'][-10:-5].mean()
                if previous_volume > 0:
                    volume_trend = (recent_volume - previous_volume) / previous_volume
                    sentiment_factors.append(min(0.5, max(-0.5, volume_trend * 2)))
            
            # Price vs moving averages
            if len(hist) >= 20:
                current_price = hist['Close'][-1]
                sma_20 = hist['Close'][-20:].mean()
                ma_sentiment = (current_price - sma_20) / sma_20
                sentiment_factors.append(min(1, max(-1, ma_sentiment * 5)))
            
            # Calculate overall technical sentiment
            if sentiment_factors:
                technical_sentiment = sum(sentiment_factors) / len(sentiment_factors)
                confidence = min(0.8, len(sentiment_factors) * 0.25)
            else:
                technical_sentiment = 0
                confidence = 0.1
            
            return SentimentScore(
                score=technical_sentiment,
                confidence=confidence,
                magnitude=abs(technical_sentiment),
                source="technical",
                timestamp=datetime.now(),
                details={
                    'momentum': sentiment_factors[0] if len(sentiment_factors) > 0 else None,
                    'volume_trend': sentiment_factors[1] if len(sentiment_factors) > 1 else None,
                    'ma_position': sentiment_factors[2] if len(sentiment_factors) > 2 else None,
                    'factors_count': len(sentiment_factors)
                }
            )
            
        except Exception as e:
            logger.error(f"Technical sentiment analysis failed: {e}")
            return SentimentScore(0, 0.1, 0, "technical", datetime.now(), {"error": str(e)})
    
    async def _get_market_sentiment_indicators(self) -> Dict[str, float]:
        """Get market sentiment indicators"""
        try:
            # Simulate market sentiment indicators
            # In production, this would fetch real data from various sources
            
            indicators = {
                'vix': -0.2,  # Low VIX = positive sentiment
                'put_call_ratio': 0.1,  # Low put/call ratio = positive sentiment
                'market_momentum': 0.3,  # Positive market momentum
                'safe_haven_demand': -0.1,  # Low safe haven demand = risk-on sentiment
                'junk_bond_demand': 0.2  # High junk bond demand = risk-on sentiment
            }
            
            return indicators
            
        except Exception as e:
            logger.error(f"Error getting market sentiment indicators: {e}")
            return {}
    
    def _analyze_keywords_sentiment(self, texts: List[str]) -> float:
        """Analyze sentiment based on keywords"""
        try:
            bullish_count = 0
            bearish_count = 0
            
            for text in texts:
                text_lower = text.lower()
                
                for keyword in self.bullish_keywords:
                    if keyword in text_lower:
                        bullish_count += 1
                
                for keyword in self.bearish_keywords:
                    if keyword in text_lower:
                        bearish_count += 1
            
            total_keywords = bullish_count + bearish_count
            if total_keywords == 0:
                return 0
            
            # Calculate sentiment score
            sentiment = (bullish_count - bearish_count) / total_keywords
            return max(-1, min(1, sentiment))
            
        except Exception as e:
            logger.error(f"Keyword sentiment analysis failed: {e}")
            return 0
    
    def _calculate_overall_sentiment(self, source_scores: Dict[str, SentimentScore]) -> Tuple[float, float]:
        """Calculate overall sentiment from multiple sources"""
        if not source_scores:
            return 0, 0.1
        
        # Weights for different sources
        source_weights = {
            'news': 0.35,
            'market': 0.30,
            'technical': 0.25,
            'social': 0.10
        }
        
        weighted_score = 0
        total_weight = 0
        total_confidence = 0
        
        for source, score in source_scores.items():
            weight = source_weights.get(source, 0.1)
            confidence_weight = weight * score.confidence
            
            weighted_score += score.score * confidence_weight
            total_weight += confidence_weight
            total_confidence += score.confidence
        
        if total_weight > 0:
            overall_score = weighted_score / total_weight
            overall_confidence = min(0.95, total_confidence / len(source_scores))
        else:
            overall_score = 0
            overall_confidence = 0.1
        
        return overall_score, overall_confidence
    
    def _get_sentiment_label(self, score: float) -> str:
        """Convert sentiment score to label"""
        if score >= 0.6:
            return "VERY_POSITIVE"
        elif score >= 0.2:
            return "POSITIVE"
        elif score <= -0.6:
            return "VERY_NEGATIVE"
        elif score <= -0.2:
            return "NEGATIVE"
        else:
            return "NEUTRAL"
    
    def _create_fallback_sentiment(self, symbol: str) -> ComprehensiveSentiment:
        """Create fallback sentiment when analysis fails"""
        fallback_score = SentimentScore(0, 0.1, 0, "fallback", datetime.now(), {})
        
        return ComprehensiveSentiment(
            overall_score=0,
            overall_confidence=0.1,
            sentiment_label="NEUTRAL",
            source_scores={"fallback": fallback_score},
            market_sentiment=0,
            news_sentiment=0,
            social_sentiment=0,
            technical_sentiment=0,
            timestamp=datetime.now()
        )
    
    async def close(self):
        """Close the session"""
        if self.session:
            await self.session.close()


# Global instance
enhanced_sentiment_analyzer = EnhancedSentimentAnalyzer()
