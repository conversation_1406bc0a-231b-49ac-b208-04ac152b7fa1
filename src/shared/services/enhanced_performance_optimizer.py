"""
Enhanced Performance Optimization Service
Provides comprehensive performance optimization including database query optimization,
aggressive caching strategies, connection pooling, and performance monitoring.
"""

import asyncio
import time
import json
import hashlib
import os
from typing import Dict, Any, Optional, List, Tuple, Union, Callable
from datetime import datetime, timedelta, timezone
from dataclasses import dataclass, asdict
from contextlib import asynccontextmanager
import logging
from collections import defaultdict, deque
import psutil
import aiohttp

from src.shared.error_handling.logging import get_logger
from src.core.error_handling.fallback import handle_error_with_fallback
from src.shared.cache.cache_service import CacheService
from src.shared.redis.redis_manager import get_redis_client
from src.shared.monitoring.performance_monitor import Timer, performance_monitor

logger = get_logger(__name__)


@dataclass
class PerformanceMetrics:
    """Performance metrics data structure"""
    operation: str
    execution_time: float
    memory_usage: float
    cpu_usage: float
    cache_hit_rate: float
    database_queries: int
    api_calls: int
    timestamp: datetime
    success: bool
    error: Optional[str] = None


@dataclass
class OptimizationConfig:
    """Configuration for performance optimizations"""
    enable_aggressive_caching: bool = True
    cache_ttl_seconds: int = 1800  # 30 minutes
    max_concurrent_operations: int = 50
    database_pool_size: int = 20
    redis_pool_size: int = 10
    enable_query_optimization: bool = True
    enable_connection_pooling: bool = True
    enable_memory_optimization: bool = True
    enable_async_processing: bool = True
    performance_monitoring_interval: int = 60  # seconds
    memory_cleanup_threshold: float = 0.8  # 80% memory usage
    cpu_cleanup_threshold: float = 0.8  # 80% CPU usage


class QueryOptimizer:
    """Database query optimization service"""
    
    def __init__(self):
        self.query_cache: Dict[str, Any] = {}
        self.query_stats: Dict[str, Dict[str, Any]] = defaultdict(lambda: {
            'execution_count': 0,
            'total_time': 0.0,
            'avg_time': 0.0,
            'cache_hits': 0,
            'last_execution': None
        })
        self.optimization_rules: List[Dict[str, Any]] = []
        self.logger = get_logger("query_optimizer")
    
    def optimize_query(self, query: str, params: Dict[str, Any] = None) -> Tuple[str, Dict[str, Any]]:
        """Optimize database query"""
        # Generate query hash for caching
        query_hash = self._generate_query_hash(query, params or {})
        
        # Check if query is already optimized
        if query_hash in self.query_cache:
            self.query_stats[query_hash]['cache_hits'] += 1
            return self.query_cache[query_hash]
        
        # Apply optimization rules
        optimized_query = query
        optimized_params = params or {}
        
        # Rule 1: Add LIMIT if not present and not a count query
        if 'LIMIT' not in query.upper() and 'COUNT' not in query.upper():
            optimized_query += ' LIMIT 1000'
        
        # Rule 2: Add ORDER BY for consistent results
        if 'ORDER BY' not in query.upper():
            # Try to find a primary key or timestamp column
            if 'id' in query.lower():
                optimized_query += ' ORDER BY id'
            elif 'created_at' in query.lower() or 'timestamp' in query.lower():
                optimized_query += ' ORDER BY created_at DESC'
        
        # Rule 3: Optimize parameter binding
        if params:
            optimized_params = self._optimize_parameters(params)
        
        # Cache optimized query
        result = (optimized_query, optimized_params)
        self.query_cache[query_hash] = result
        
        # Update stats
        self.query_stats[query_hash]['last_execution'] = datetime.now(timezone.utc)
        
        return result
    
    def _generate_query_hash(self, query: str, params: Dict[str, Any]) -> str:
        """Generate hash for query caching"""
        query_data = {
            'query': query.strip(),
            'params': sorted(params.items()) if params else {}
        }
        query_string = json.dumps(query_data, sort_keys=True)
        return hashlib.sha256(query_string.encode()).hexdigest()[:16]
    
    def _optimize_parameters(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Optimize query parameters"""
        optimized = {}
        for key, value in params.items():
            # Convert None to NULL for SQL
            if value is None:
                optimized[key] = 'NULL'
            # Convert lists to tuples for better performance
            elif isinstance(value, list):
                optimized[key] = tuple(value)
            else:
                optimized[key] = value
        return optimized
    
    def get_query_stats(self) -> Dict[str, Any]:
        """Get query optimization statistics"""
        return dict(self.query_stats)
    
    def add_optimization_rule(self, rule: Dict[str, Any]) -> None:
        """Add custom optimization rule"""
        self.optimization_rules.append(rule)
        self.logger.info(f"Added optimization rule: {rule.get('name', 'unnamed')}")


class ConnectionPoolManager:
    """Advanced connection pool management"""
    
    def __init__(self, config: OptimizationConfig):
        self.config = config
        self.pools: Dict[str, List[Any]] = defaultdict(list)
        self.pool_stats: Dict[str, Dict[str, Any]] = defaultdict(lambda: {
            'total_connections': 0,
            'active_connections': 0,
            'idle_connections': 0,
            'connection_requests': 0,
            'connection_wait_time': 0.0
        })
        self._lock = asyncio.Lock()
        self.logger = get_logger("connection_pool_manager")
    
    async def get_connection(self, pool_type: str) -> Optional[Any]:
        """Get connection from pool"""
        async with self._lock:
            pool = self.pools[pool_type]
            stats = self.pool_stats[pool_type]
            
            # Look for available connection
            for i, conn in enumerate(pool):
                if not conn.get('in_use', False):
                    conn['in_use'] = True
                    conn['last_used'] = time.time()
                    stats['active_connections'] += 1
                    stats['idle_connections'] -= 1
                    return conn['connection']
            
            # Create new connection if under limit
            max_connections = self._get_max_connections(pool_type)
            if len(pool) < max_connections:
                connection = await self._create_connection(pool_type)
                if connection:
                    conn_entry = {
                        'connection': connection,
                        'in_use': True,
                        'created_at': time.time(),
                        'last_used': time.time()
                    }
                    pool.append(conn_entry)
                    stats['total_connections'] += 1
                    stats['active_connections'] += 1
                    return connection
                else:
                    stats['connection_requests'] += 1
                    return None
    
    async def return_connection(self, pool_type: str, connection: Any) -> None:
        """Return connection to pool"""
        async with self._lock:
            pool = self.pools[pool_type]
            stats = self.pool_stats[pool_type]
            
            for conn in pool:
                if conn['connection'] == connection:
                    conn['in_use'] = False
                    conn['last_used'] = time.time()
                    stats['active_connections'] -= 1
                    stats['idle_connections'] += 1
                    break
    
    def _get_max_connections(self, pool_type: str) -> int:
        """Get maximum connections for pool type"""
        limits = {
            'database': self.config.database_pool_size,
            'redis': self.config.redis_pool_size,
            'api': 20,
            'default': 10
        }
        return limits.get(pool_type, limits['default'])
    
    async def _create_connection(self, pool_type: str) -> Optional[Any]:
        """Create new connection based on type"""
        try:
            if pool_type == 'database':
                # Placeholder for database connection
                return {'type': 'database', 'created_at': time.time()}
            elif pool_type == 'redis':
                redis_client = await get_redis_client()
                return redis_client
            elif pool_type == 'api':
                # Placeholder for API connection
                return {'type': 'api', 'created_at': time.time()}
            else:
                return None
        except Exception as e:
            self.logger.error(f"Failed to create {pool_type} connection: {e}")
        return None
    
    async def cleanup_expired_connections(self) -> None:
        """Clean up expired connections"""
        async with self._lock:
            current_time = time.time()
            max_idle_time = 300  # 5 minutes
            
            for pool_type, pool in self.pools.items():
                expired_connections = []
                for conn in pool:
                    if (not conn['in_use'] and 
                        current_time - conn['last_used'] > max_idle_time):
                        expired_connections.append(conn)
                
                for conn in expired_connections:
                    pool.remove(conn)
                    self.pool_stats[pool_type]['total_connections'] -= 1
                    self.pool_stats[pool_type]['idle_connections'] -= 1
    
    def get_pool_stats(self) -> Dict[str, Any]:
        """Get connection pool statistics"""
        return dict(self.pool_stats)


class MemoryOptimizer:
    """Memory usage optimization service"""
    
    def __init__(self, config: OptimizationConfig):
        self.config = config
        self.memory_threshold = config.memory_cleanup_threshold
        self.cpu_threshold = config.cpu_cleanup_threshold
        self.cleanup_callbacks: List[Callable] = []
        self.logger = get_logger("memory_optimizer")
    
    def add_cleanup_callback(self, callback: Callable) -> None:
        """Add memory cleanup callback"""
        self.cleanup_callbacks.append(callback)
    
    async def check_memory_usage(self) -> bool:
        """Check if memory cleanup is needed"""
        try:
            memory_percent = psutil.virtual_memory().percent / 100
            cpu_percent = psutil.cpu_percent() / 100
            
            if memory_percent > self.memory_threshold or cpu_percent > self.cpu_threshold:
                await self.perform_cleanup()
                return True
            return False
        except Exception as e:
            self.logger.error(f"Memory check failed: {e}")
        return False

    async def perform_cleanup(self) -> None:
        """Perform memory cleanup"""
        self.logger.info("Performing memory cleanup...")
        
        # Run cleanup callbacks
        for callback in self.cleanup_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback()
                else:
                    callback()
            except Exception as e:
                self.logger.error(f"Cleanup callback failed: {e}")
        
        # Force garbage collection
        import gc
        gc.collect()
        
        self.logger.info("Memory cleanup completed")


class EnhancedPerformanceOptimizer:
    """Main enhanced performance optimization service"""

    def __init__(self, config: Optional[OptimizationConfig] = None):
        self.config = config or OptimizationConfig()
        self.query_optimizer = QueryOptimizer()
        self.connection_pool = ConnectionPoolManager(self.config)
        self.memory_optimizer = MemoryOptimizer(self.config)
        self.cache_service = CacheService()
        
        self.performance_metrics: deque = deque(maxlen=10000)
        self.operation_timers: Dict[str, float] = {}
        self._monitoring_task: Optional[asyncio.Task] = None
        self._cleanup_task: Optional[asyncio.Task] = None
        self._running = False
        
        self.logger = get_logger("enhanced_performance_optimizer")
    
    async def start(self) -> None:
        """Start performance optimization service"""
        if self._running:
            return
        
        self._running = True
        
        # Start monitoring task
        self._monitoring_task = asyncio.create_task(self._monitoring_loop())
        
        # Start cleanup task
        self._cleanup_task = asyncio.create_task(self._cleanup_loop())
        
        # Initialize cache service
        await self.cache_service.initialize()
        
        self.logger.info("Enhanced Performance Optimizer started")
    
    async def stop(self) -> None:
        """Stop performance optimization service"""
        self._running = False
        
        if self._monitoring_task:
            self._monitoring_task.cancel()
            try:
                await self._monitoring_task
            except asyncio.CancelledError:
                pass
        
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        await self.cache_service.close()
        self.logger.info("Enhanced Performance Optimizer stopped")
    
    async def _monitoring_loop(self) -> None:
        """Background monitoring loop"""
        while self._running:
            try:
                # Check memory usage
                await self.memory_optimizer.check_memory_usage()
                
                # Record system metrics
                await self._record_system_metrics()
                
                await asyncio.sleep(self.config.performance_monitoring_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Monitoring loop error: {e}")
                await asyncio.sleep(60)
    
    async def _cleanup_loop(self) -> None:
        """Background cleanup loop"""
        while self._running:
            try:
                # Cleanup expired connections
                await self.connection_pool.cleanup_expired_connections()
                
                # Cleanup old metrics
                await self._cleanup_old_metrics()
                
                await asyncio.sleep(300)  # Cleanup every 5 minutes
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Cleanup loop error: {e}")
                await asyncio.sleep(300)
    
    async def _record_system_metrics(self) -> None:
        """Record system performance metrics"""
        try:
            memory_percent = psutil.virtual_memory().percent
            cpu_percent = psutil.cpu_percent()
            
            metric = PerformanceMetrics(
                operation='system_monitoring',
                execution_time=0.0,
                memory_usage=memory_percent,
                cpu_usage=cpu_percent,
                cache_hit_rate=0.0,
                database_queries=0,
                api_calls=0,
                timestamp=datetime.now(timezone.utc),
                success=True
            )

            self.performance_metrics.append(metric)

        except Exception as e:
            self.logger.error(f"Failed to record system metrics: {e}")
    
    async def _cleanup_old_metrics(self) -> None:
        """Clean up old performance metrics"""
        cutoff_time = datetime.now(timezone.utc) - timedelta(hours=1)
        original_count = len(self.performance_metrics)
        
        # Remove old metrics
        while (self.performance_metrics and 
               self.performance_metrics[0].timestamp < cutoff_time):
            self.performance_metrics.popleft()
        
        removed_count = original_count - len(self.performance_metrics)
        if removed_count > 0:
            self.logger.debug(f"Cleaned up {removed_count} old metrics")

    @asynccontextmanager
    async def optimized_operation(self, operation_name: str):
        """Context manager for optimized operations"""
        start_time = time.time()
        start_memory = psutil.virtual_memory().used
        
        try:
            yield
            success = True
            error = None
        except Exception as e:
            success = False
            error = str(e)
            raise
        finally:
            execution_time = time.time() - start_time
            end_memory = psutil.virtual_memory().used
            memory_usage = (end_memory - start_memory) / 1024 / 1024  # MB
            
            # Record performance metric
            await self._record_operation_metric(
                operation_name, execution_time, memory_usage, success, error
            )
    
    async def _record_operation_metric(
        self, 
        operation: str, 
        execution_time: float, 
        memory_usage: float, 
        success: bool, 
        error: Optional[str] = None
    ) -> None:
        """Record operation performance metric"""
        try:
            cpu_usage = psutil.cpu_percent()
            
            # Get cache hit rate (simplified)
            cache_hit_rate = 0.8  # Placeholder
            
            metric = PerformanceMetrics(
                operation=operation,
                execution_time=execution_time,
                memory_usage=memory_usage,
                cpu_usage=cpu_usage,
                cache_hit_rate=cache_hit_rate,
                database_queries=0,  # Would be tracked in real implementation
                api_calls=0,  # Would be tracked in real implementation
                timestamp=datetime.now(timezone.utc),
                success=success,
                error=error
            )
            
            self.performance_metrics.append(metric)
            
            # Also record in performance monitor
            await performance_monitor.record_metric(
                operation, execution_time, success, False, error
            )
            
        except Exception as e:
            self.logger.error(f"Failed to record operation metric: {e}")
    
    async def optimize_database_query(
        self, 
        query: str, 
        params: Dict[str, Any] = None
    ) -> Tuple[str, Dict[str, Any]]:
        """Optimize database query"""
        return self.query_optimizer.optimize_query(query, params)
    
    async def get_connection(self, pool_type: str) -> Optional[Any]:
        """Get optimized connection from pool"""
        return await self.connection_pool.get_connection(pool_type)
    
    async def return_connection(self, pool_type: str, connection: Any) -> None:
        """Return connection to pool"""
        await self.connection_pool.return_connection(pool_type, connection)
    
    def get_performance_report(self) -> Dict[str, Any]:
        """Get comprehensive performance report"""
        # Calculate metrics from recent data
        recent_metrics = [m for m in self.performance_metrics 
                         if m.timestamp > datetime.now(timezone.utc) - timedelta(hours=1)]

        if not recent_metrics:
            return {'error': 'No recent metrics available'}
        
        # Calculate averages
        avg_execution_time = sum(m.execution_time for m in recent_metrics) / len(recent_metrics)
        avg_memory_usage = sum(m.memory_usage for m in recent_metrics) / len(recent_metrics)
        avg_cpu_usage = sum(m.cpu_usage for m in recent_metrics) / len(recent_metrics)
        success_rate = sum(1 for m in recent_metrics if m.success) / len(recent_metrics) * 100
        
        # Get operation breakdown
        operation_stats = defaultdict(lambda: {'count': 0, 'total_time': 0.0, 'successes': 0})
        for metric in recent_metrics:
            op_stats = operation_stats[metric.operation]
            op_stats['count'] += 1
            op_stats['total_time'] += metric.execution_time
            if metric.success:
                op_stats['successes'] += 1
        
        # Calculate operation averages
        operation_breakdown = {}
        for operation, stats in operation_stats.items():
            operation_breakdown[operation] = {
                'count': stats['count'],
                'avg_time': stats['total_time'] / stats['count'],
                'success_rate': stats['successes'] / stats['count'] * 100
            }

        return {
            'summary': {
                'total_operations': len(recent_metrics),
                'avg_execution_time': avg_execution_time,
                'avg_memory_usage': avg_memory_usage,
                'avg_cpu_usage': avg_cpu_usage,
                'success_rate': success_rate,
                'time_period': '1 hour'
            },
            'operation_breakdown': operation_breakdown,
            'query_optimization': self.query_optimizer.get_query_stats(),
            'connection_pools': self.connection_pool.get_pool_stats(),
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
    
    def add_memory_cleanup_callback(self, callback: Callable) -> None:
        """Add memory cleanup callback"""
        self.memory_optimizer.add_cleanup_callback(callback)
    
    def add_query_optimization_rule(self, rule: Dict[str, Any]) -> None:
        """Add query optimization rule"""
        self.query_optimizer.add_optimization_rule(rule)


# Global enhanced performance optimizer instance
enhanced_performance_optimizer = EnhancedPerformanceOptimizer()

async def get_optimized_connection(pool_type: str) -> Optional[Any]:
    """Convenience function to get optimized connection"""
    return await enhanced_performance_optimizer.get_connection(pool_type)

async def return_optimized_connection(pool_type: str, connection: Any) -> None:
    """Convenience function to return connection"""
    await enhanced_performance_optimizer.return_connection(pool_type, connection)

def get_enhanced_performance_report() -> Dict[str, Any]:
    """Convenience function to get performance report"""
    return enhanced_performance_optimizer.get_performance_report()