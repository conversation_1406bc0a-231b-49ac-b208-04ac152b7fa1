"""
Shared Services Package

This package contains centralized services for optimization, performance monitoring,
and other shared functionality across the trading bot system.
"""

from .optimization_service import (
    CentralizedCache,
    ConnectionPool,
    PerformanceOptimizer,
    optimization_service,
    get_optimized_result,
    get_performance_stats
)

from .performance_monitor import (
    PerformanceMetric,
    PerformanceStats,
    PerformanceMonitor,
    performance_monitor,
    record_performance,
    get_performance_summary,
    get_optimization_recommendations
)

from .enhanced_performance_optimizer import (
    PerformanceMetrics,
    OptimizationConfig,
    QueryOptimizer,
    ConnectionPoolManager,
    MemoryOptimizer,
    EnhancedPerformanceOptimizer,
    enhanced_performance_optimizer,
    get_optimized_connection,
    return_optimized_connection,
    get_enhanced_performance_report
)

__all__ = [
    # Optimization Service
    'CentralizedCache',
    'ConnectionPool', 
    'PerformanceOptimizer',
    'optimization_service',
    'get_optimized_result',
    'get_performance_stats',
    
    # Performance Monitor
    'PerformanceMetric',
    'PerformanceStats',
    'PerformanceMonitor',
    'performance_monitor',
    'record_performance',
    'get_performance_summary',
    'get_optimization_recommendations',
    
    # Enhanced Performance Optimizer
    'PerformanceMetrics',
    'OptimizationConfig',
    'QueryOptimizer',
    'ConnectionPoolManager',
    'MemoryOptimizer',
    'EnhancedPerformanceOptimizer',
    'enhanced_performance_optimizer',
    'get_optimized_connection',
    'return_optimized_connection',
    'get_enhanced_performance_report'
]