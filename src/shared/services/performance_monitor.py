"""
Real Performance Monitoring Service
Tracks actual performance metrics and provides optimizations
"""

import asyncio
import time
import statistics
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import json
import os

from src.shared.error_handling.logging import get_logger

logger = get_logger(__name__)

@dataclass
class PerformanceMetric:
    """Performance metric data"""
    operation: str
    execution_time: float
    timestamp: datetime
    success: bool
    cache_hit: bool = False
    error_message: Optional[str] = None

@dataclass
class PerformanceStats:
    """Performance statistics"""
    total_operations: int = 0
    successful_operations: int = 0
    failed_operations: int = 0
    cache_hits: int = 0
    cache_misses: int = 0
    avg_execution_time: float = 0.0
    min_execution_time: float = float('inf')
    max_execution_time: float = 0.0
    p95_execution_time: float = 0.0
    p99_execution_time: float = 0.0

class PerformanceMonitor:
    """Real performance monitoring and optimization service"""
    
    def __init__(self):
        self.metrics: List[PerformanceMetric] = []
        self.stats: Dict[str, PerformanceStats] = {}
        self.optimization_rules: Dict[str, Any] = {}
        self.performance_thresholds = {
            'slow_query_threshold': 1.0,  # seconds
            'cache_hit_rate_threshold': 0.8,  # 80%
            'error_rate_threshold': 0.05,  # 5%
            'memory_usage_threshold': 0.8,  # 80%
            'cpu_usage_threshold': 0.8  # 80%
        }
        self._lock = asyncio.Lock()
        self._cleanup_task: Optional[asyncio.Task] = None
        self._running = False
        
        logger.info("PerformanceMonitor initialized")
    
    async def start(self) -> None:
        """Start performance monitoring"""
        if self._running:
            return
        
        self._running = True
        self._cleanup_task = asyncio.create_task(self._cleanup_loop())
        logger.info("PerformanceMonitor started")
    
    async def stop(self) -> None:
        """Stop performance monitoring"""
        self._running = False
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        logger.info("PerformanceMonitor stopped")
    
    async def record_metric(
        self, 
        operation: str, 
        execution_time: float, 
        success: bool = True,
        cache_hit: bool = False,
        error_message: Optional[str] = None
    ) -> None:
        """Record a performance metric"""
        async with self._lock:
            metric = PerformanceMetric(
                operation=operation,
                execution_time=execution_time,
                timestamp=datetime.now(),
                success=success,
                cache_hit=cache_hit,
                error_message=error_message
            )
            
            self.metrics.append(metric)
            
            # Update stats for this operation
            if operation not in self.stats:
                self.stats[operation] = PerformanceStats()
            
            stats = self.stats[operation]
            stats.total_operations += 1
            
            if success:
                stats.successful_operations += 1
            else:
                stats.failed_operations += 1
            
            if cache_hit:
                stats.cache_hits += 1
            else:
                stats.cache_misses += 1
            
            # Update execution time statistics
            if success:
                stats.min_execution_time = min(stats.min_execution_time, execution_time)
                stats.max_execution_time = max(stats.max_execution_time, execution_time)
                
                # Calculate average
                total_time = stats.avg_execution_time * (stats.successful_operations - 1) + execution_time
                stats.avg_execution_time = total_time / stats.successful_operations
                
                # Calculate percentiles (simplified)
                successful_times = [m.execution_time for m in self.metrics
                                  if m.operation == operation and m.success]
                if len(successful_times) >= 10:  # Need enough data for percentiles
                    stats.p95_execution_time = statistics.quantiles(successful_times, n=20)[18]  # 95th percentile
                    stats.p99_execution_time = statistics.quantiles(successful_times, n=100)[98]  # 99th percentile
    
    async def _cleanup_loop(self) -> None:
        """Background cleanup loop to remove old metrics"""
        while self._running:
            try:
                await self._cleanup_old_metrics()
                await asyncio.sleep(300)  # Cleanup every 5 minutes
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Cleanup loop error: {e}")
                await asyncio.sleep(300)
    
    async def _cleanup_old_metrics(self) -> None:
        """Remove metrics older than 1 hour"""
        async with self._lock:
            cutoff_time = datetime.now() - timedelta(hours=1)
            original_count = len(self.metrics)
            self.metrics = [m for m in self.metrics if m.timestamp > cutoff_time]
            removed_count = original_count - len(self.metrics)
            
            if removed_count > 0:
                logger.debug(f"Cleaned up {removed_count} old metrics")
    
    def get_operation_stats(self, operation: str) -> Optional[PerformanceStats]:
        """Get performance statistics for a specific operation"""
        return self.stats.get(operation)
    
    def get_all_stats(self) -> Dict[str, PerformanceStats]:
        """Get performance statistics for all operations"""
        return self.stats.copy()
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get overall performance summary"""
        total_operations = sum(stats.total_operations for stats in self.stats.values())
        total_successful = sum(stats.successful_operations for stats in self.stats.values())
        total_failed = sum(stats.failed_operations for stats in self.stats.values())
        total_cache_hits = sum(stats.cache_hits for stats in self.stats.values())
        total_cache_misses = sum(stats.cache_misses for stats in self.stats.values())
        
        overall_success_rate = (total_successful / total_operations * 100) if total_operations > 0 else 0
        overall_cache_hit_rate = (total_cache_hits / (total_cache_hits + total_cache_misses) * 100) if (total_cache_hits + total_cache_misses) > 0 else 0
        
        # Find slowest operations
        slowest_operations = []
        for operation, stats in self.stats.items():
            if stats.avg_execution_time > self.performance_thresholds['slow_query_threshold']:
                slowest_operations.append({
                    'operation': operation,
                    'avg_time': stats.avg_execution_time,
                    'total_calls': stats.total_operations
                })
        
        slowest_operations.sort(key=lambda x: x['avg_time'], reverse=True)
        
        return {
            'total_operations': total_operations,
            'successful_operations': total_successful,
            'failed_operations': total_failed,
            'success_rate': overall_success_rate,
            'cache_hit_rate': overall_cache_hit_rate,
            'slowest_operations': slowest_operations[:5],  # Top 5 slowest
            'operation_count': len(self.stats),
            'timestamp': datetime.now().isoformat()
        }
    
    def get_optimization_recommendations(self) -> List[Dict[str, Any]]:
        """Get performance optimization recommendations"""
        recommendations = []
        
        for operation, stats in self.stats.items():
            # Check for slow operations
            if stats.avg_execution_time > self.performance_thresholds['slow_query_threshold']:
                recommendations.append({
                    'type': 'slow_operation',
                    'operation': operation,
                    'avg_time': stats.avg_execution_time,
                    'threshold': self.performance_thresholds['slow_query_threshold'],
                    'recommendation': f"Consider optimizing {operation} - avg time {stats.avg_execution_time:.2f}s exceeds threshold"
                })
            
            # Check for low cache hit rate
            total_cache_requests = stats.cache_hits + stats.cache_misses
            if total_cache_requests > 0:
                cache_hit_rate = stats.cache_hits / total_cache_requests
                if cache_hit_rate < self.performance_thresholds['cache_hit_rate_threshold']:
                    recommendations.append({
                        'type': 'low_cache_hit_rate',
                        'operation': operation,
                        'cache_hit_rate': cache_hit_rate,
                        'threshold': self.performance_thresholds['cache_hit_rate_threshold'],
                        'recommendation': f"Consider improving caching for {operation} - hit rate {cache_hit_rate:.2%} below threshold"
                    })
            
            # Check for high error rate
            if stats.total_operations > 0:
                error_rate = stats.failed_operations / stats.total_operations
                if error_rate > self.performance_thresholds['error_rate_threshold']:
                    recommendations.append({
                        'type': 'high_error_rate',
                        'operation': operation,
                        'error_rate': error_rate,
                        'threshold': self.performance_thresholds['error_rate_threshold'],
                        'recommendation': f"Investigate errors in {operation} - error rate {error_rate:.2%} exceeds threshold"
                    })
        
        return recommendations
    
    def add_optimization_rule(self, rule_name: str, rule: Dict[str, Any]) -> None:
        """Add optimization rule"""
        self.optimization_rules[rule_name] = rule
        logger.info(f"Added optimization rule: {rule_name}")
    
    def get_optimization_rules(self) -> Dict[str, Any]:
        """Get all optimization rules"""
        return self.optimization_rules.copy()
    
    def export_metrics(self, filepath: str) -> bool:
        """Export metrics to JSON file"""
        try:
            export_data = {
                'metrics': [
                    {
                        'operation': m.operation,
                        'execution_time': m.execution_time,
                        'timestamp': m.timestamp.isoformat(),
                        'success': m.success,
                        'cache_hit': m.cache_hit,
                        'error_message': m.error_message
                    }
                    for m in self.metrics
                ],
                'stats': {
                    operation: {
                'total_operations': stats.total_operations,
                        'successful_operations': stats.successful_operations,
                        'failed_operations': stats.failed_operations,
                        'cache_hits': stats.cache_hits,
                        'cache_misses': stats.cache_misses,
                'avg_execution_time': stats.avg_execution_time,
                        'min_execution_time': stats.min_execution_time if stats.min_execution_time != float('inf') else 0,
                'max_execution_time': stats.max_execution_time,
                'p95_execution_time': stats.p95_execution_time,
                'p99_execution_time': stats.p99_execution_time
                    }
                    for operation, stats in self.stats.items()
                },
                'export_timestamp': datetime.now().isoformat()
            }
            
            with open(filepath, 'w') as f:
                json.dump(export_data, f, indent=2)
            
            logger.info(f"Metrics exported to {filepath}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to export metrics: {e}")
            return False
    
    def clear_metrics(self) -> None:
        """Clear all metrics and stats"""
        async def _clear():
            async with self._lock:
                self.metrics.clear()
                self.stats.clear()
                logger.info("All metrics cleared")
        
        asyncio.create_task(_clear())

# Global performance monitor instance
performance_monitor = PerformanceMonitor()

async def record_performance(
    operation: str, 
    execution_time: float, 
    success: bool = True,
    cache_hit: bool = False,
    error_message: Optional[str] = None
) -> None:
    """Convenience function to record performance metrics"""
    await performance_monitor.record_metric(operation, execution_time, success, cache_hit, error_message)

def get_performance_summary() -> Dict[str, Any]:
    """Convenience function to get performance summary"""
    return performance_monitor.get_performance_summary()

def get_optimization_recommendations() -> List[Dict[str, Any]]:
    """Convenience function to get optimization recommendations"""
    return performance_monitor.get_optimization_recommendations()