"""
Centralized Optimization Service
Manages caching, connection pooling, and performance optimizations across the bot
"""

import asyncio
import time
import logging
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime, timedelta
import hashlib
import json

logger = logging.getLogger(__name__)

class CentralizedCache:
    """Centralized cache with intelligent eviction and TTL management"""
    
    def __init__(self, max_size: int = 2000, default_ttl: int = 600):
        self.max_size = max_size
        self.default_ttl = default_ttl
        self._cache: Dict[str, Dict[str, Any]] = {}
        self._access_order: List[str] = []
        self._lock = asyncio.Lock()
        self.stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'total_sets': 0
        }
    
    def _generate_key(self, prefix: str, data: Dict[str, Any]) -> str:
        """Generate cache key from prefix and data"""
        key_data = {
            'prefix': prefix,
            'data': data
        }
        key_string = json.dumps(key_data, sort_keys=True)
        return hashlib.sha256(key_string.encode()).hexdigest()[:20]
    
    async def get(self, prefix: str, data: Dict[str, Any]) -> Optional[Any]:
        """Get cached result"""
        async with self._lock:
            key = self._generate_key(prefix, data)
            
            if key not in self._cache:
                self.stats['misses'] += 1
                return None
            
            entry = self._cache[key]
            
            # Check TTL
            if time.time() > entry['expires_at']:
                del self._cache[key]
                self._access_order.remove(key)
                self.stats['misses'] += 1
                return None
            
            # Update access order
            if key in self._access_order:
                self._access_order.remove(key)
            self._access_order.append(key)
            
            self.stats['hits'] += 1
            return entry['value']
    
    async def set(self, prefix: str, data: Dict[str, Any], value: Any, ttl: Optional[int] = None) -> None:
        """Set cached result"""
        async with self._lock:
            key = self._generate_key(prefix, data)
            ttl = ttl or self.default_ttl
            
            # Evict if at capacity
            if len(self._cache) >= self.max_size and key not in self._cache:
                await self._evict_lru()
            
            self._cache[key] = {
                'value': value,
                'expires_at': time.time() + ttl,
                'created_at': time.time()
            }
            
            if key in self._access_order:
                self._access_order.remove(key)
                self._access_order.append(key)
            
            self.stats['total_sets'] += 1
    
    async def _evict_lru(self) -> None:
        """Evict least recently used entry"""
        if not self._access_order:
            return
        
        lru_key = self._access_order[0]
        if lru_key in self._cache:
            del self._cache[lru_key]
            self._access_order.remove(lru_key)
            self.stats['evictions'] += 1
    
    async def clear(self) -> None:
        """Clear all cache entries"""
        async with self._lock:
            self._cache.clear()
            self._access_order.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        total_requests = self.stats['hits'] + self.stats['misses']
        hit_rate = (self.stats['hits'] / total_requests * 100) if total_requests > 0 else 0
        
        return {
            **self.stats,
            'hit_rate': hit_rate,
            'current_size': len(self._cache),
            'max_size': self.max_size
        }

class ConnectionPool:
    """Connection pool for external services"""
    
    def __init__(self, max_connections: int = 10, max_idle_time: int = 300):
        self.max_connections = max_connections
        self.max_idle_time = max_idle_time
        self._connections: List[Dict[str, Any]] = []
        self._lock = asyncio.Lock()
        self.stats = {
            'total_created': 0,
            'total_destroyed': 0,
            'active_connections': 0,
            'pool_hits': 0,
            'pool_misses': 0
        }
    
    async def get_connection(self, service_type: str) -> Optional[Any]:
        """Get connection from pool or create new one"""
        async with self._lock:
            # Look for available connection
            for conn in self._connections:
                if (conn['service_type'] == service_type and 
                    conn['in_use'] == False and 
                    time.time() - conn['last_used'] < self.max_idle_time):
                    conn['in_use'] = True
                    conn['last_used'] = time.time()
                    self.stats['pool_hits'] += 1
                    return conn['connection']
            
            # Create new connection if under limit
            if len(self._connections) < self.max_connections:
                connection = await self._create_connection(service_type)
                if connection:
                    conn_entry = {
                        'connection': connection,
                        'service_type': service_type,
                        'in_use': True,
                        'created_at': time.time(),
                        'last_used': time.time()
                    }
                    self._connections.append(conn_entry)
                    self.stats['total_created'] += 1
                    self.stats['pool_misses'] += 1
                    return connection
            
            self.stats['pool_misses'] += 1
            return None
    
    async def return_connection(self, connection: Any) -> None:
        """Return connection to pool"""
        async with self._lock:
            for conn in self._connections:
                if conn['connection'] == connection:
                    conn['in_use'] = False
                    conn['last_used'] = time.time()
                    break
    
    async def _create_connection(self, service_type: str) -> Optional[Any]:
        """Create new connection based on service type"""
        try:
            if service_type == 'redis':
                import redis.asyncio as redis
                return redis.Redis(host='localhost', port=6379, decode_responses=True)
            elif service_type == 'database':
                # Placeholder for database connection
                return {'type': 'database', 'created_at': time.time()}
            elif service_type == 'api':
                # Placeholder for API connection
                return {'type': 'api', 'created_at': time.time()}
            else:
                logger.warning(f"Unknown service type: {service_type}")
                return None
        except Exception as e:
            logger.error(f"Failed to create connection for {service_type}: {e}")
            return None
    
    async def cleanup_expired(self) -> None:
        """Clean up expired connections"""
        async with self._lock:
            current_time = time.time()
            expired_connections = []
            
            for conn in self._connections:
                if (not conn['in_use'] and 
                    current_time - conn['last_used'] > self.max_idle_time):
                    expired_connections.append(conn)
            
            for conn in expired_connections:
                self._connections.remove(conn)
                self.stats['total_destroyed'] += 1
    
    def get_stats(self) -> Dict[str, Any]:
        """Get pool statistics"""
        return {
            **self.stats,
            'current_connections': len(self._connections),
            'max_connections': self.max_connections,
            'available_connections': len([c for c in self._connections if not c['in_use']])
        }

class PerformanceOptimizer:
    """Main performance optimization service"""
    
    def __init__(self):
        self.cache = CentralizedCache()
        self.connection_pool = ConnectionPool()
        self.optimization_rules: List[Dict[str, Any]] = []
        self.metrics: Dict[str, Any] = {
            'cache_performance': {},
            'connection_performance': {},
            'overall_performance': {}
        }
        self._cleanup_task: Optional[asyncio.Task] = None
        self._running = False
        
        logger.info("PerformanceOptimizer initialized")
    
    async def start(self) -> None:
        """Start optimization service"""
        if self._running:
            return
        
        self._running = True
        self._cleanup_task = asyncio.create_task(self._cleanup_loop())
        logger.info("PerformanceOptimizer started")
    
    async def stop(self) -> None:
        """Stop optimization service"""
        self._running = False
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        await self.cache.clear()
        logger.info("PerformanceOptimizer stopped")
    
    async def _cleanup_loop(self) -> None:
        """Background cleanup loop"""
        while self._running:
            try:
                await self.connection_pool.cleanup_expired()
                await asyncio.sleep(60)  # Cleanup every minute
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Cleanup loop error: {e}")
                await asyncio.sleep(60)
    
    async def optimize_query(self, query_type: str, query_data: Dict[str, Any]) -> Any:
        """Optimize and execute query with caching and connection pooling"""
        start_time = time.time()
        
        try:
            # Check cache first
            cached_result = await self.cache.get(query_type, query_data)
            if cached_result is not None:
                logger.debug(f"Cache hit for {query_type}")
                return cached_result
            
            # Get connection from pool
            connection = await self.connection_pool.get_connection('database')
            if not connection:
                logger.warning("No available database connections")
                return None
            
            try:
                # Execute query (placeholder)
                result = await self._execute_query(connection, query_type, query_data)
                
                # Cache result
                await self.cache.set(query_type, query_data, result)
                
                return result
            finally:
                await self.connection_pool.return_connection(connection)
        
        except Exception as e:
            logger.error(f"Query optimization failed: {e}")
            return None
        finally:
            execution_time = time.time() - start_time
            self._record_metrics(query_type, execution_time, 'success' if 'result' in locals() else 'error')
    
    async def _execute_query(self, connection: Any, query_type: str, query_data: Dict[str, Any]) -> Any:
        """Execute query (placeholder implementation)"""
        # Simulate query execution
        await asyncio.sleep(0.1)
        return {
            'query_type': query_type,
            'data': query_data,
            'result': 'simulated_result',
            'timestamp': datetime.now().isoformat()
        }
    
    def _record_metrics(self, query_type: str, execution_time: float, status: str) -> None:
        """Record performance metrics"""
        if query_type not in self.metrics:
            self.metrics[query_type] = {
                'total_queries': 0,
                'total_time': 0.0,
                'avg_time': 0.0,
                'success_count': 0,
                'error_count': 0
            }
        
        metrics = self.metrics[query_type]
        metrics['total_queries'] += 1
        metrics['total_time'] += execution_time
        metrics['avg_time'] = metrics['total_time'] / metrics['total_queries']
        
        if status == 'success':
            metrics['success_count'] += 1
        else:
            metrics['error_count'] += 1
    
    def add_optimization_rule(self, rule: Dict[str, Any]) -> None:
        """Add optimization rule"""
        self.optimization_rules.append(rule)
        logger.info(f"Added optimization rule: {rule.get('name', 'unnamed')}")
    
    def get_performance_report(self) -> Dict[str, Any]:
        """Get comprehensive performance report"""
        cache_stats = self.cache.get_stats()
        pool_stats = self.connection_pool.get_stats()
        
        return {
            'cache': cache_stats,
            'connection_pool': pool_stats,
            'query_metrics': self.metrics,
            'optimization_rules': len(self.optimization_rules),
            'timestamp': datetime.now().isoformat()
        }

# Global optimization service instance
optimization_service = PerformanceOptimizer()

async def get_optimized_result(query_type: str, query_data: Dict[str, Any]) -> Any:
    """Convenience function for optimized queries"""
    return await optimization_service.optimize_query(query_type, query_data)

def get_performance_stats() -> Dict[str, Any]:
    """Convenience function for performance statistics"""
    return optimization_service.get_performance_report()