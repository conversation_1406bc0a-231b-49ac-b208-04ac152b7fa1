"""
Enhanced Technical Analysis Calculator
Works with HistoricalData objects and provides comprehensive technical indicators
"""

import logging
from typing import Dict, List, Any, Optional, Union
import numpy as np
import pandas as pd
from dataclasses import dataclass

from src.shared.data_providers.unified_base import HistoricalData
from src.shared.error_handling.logging import get_logger

logger = get_logger(__name__)

@dataclass
class TechnicalIndicators:
    """Container for technical analysis results"""
    symbol: str
    indicators: Dict[str, Any]
    metadata: Dict[str, Any]
    data_points: int
    calculation_time: float

class EnhancedTechnicalCalculator:
    """
    Enhanced technical analysis calculator that works with HistoricalData objects
    """
    
    def __init__(self):
        self.logger = get_logger(__name__)
    
    async def calculate_indicators_from_historical_data(self, 
                                                      historical_data: HistoricalData) -> TechnicalIndicators:
        """
        Calculate technical indicators from HistoricalData object
        
        Args:
            historical_data: HistoricalData object containing OHLCV data
            
        Returns:
            TechnicalIndicators object with calculated indicators
        """
        import time
        start_time = time.time()
        
        try:
            # Convert HistoricalData to pandas DataFrame
            df = self._historical_data_to_dataframe(historical_data)
            
            if df.empty or len(df) < 20:
                self.logger.warning(f"Insufficient data for technical analysis: {len(df)} points")
                return TechnicalIndicators(
                    symbol=historical_data.symbol,
                    indicators={},
                    metadata={"error": "Insufficient data"},
                    data_points=len(df),
                    calculation_time=time.time() - start_time
                )
            
            # Calculate all indicators
            indicators = await self._calculate_all_indicators(df)
            
            calculation_time = time.time() - start_time
            
            return TechnicalIndicators(
                symbol=historical_data.symbol,
                indicators=indicators,
                metadata={
                    "data_points": len(df),
                    "date_range": f"{df.index[0]} to {df.index[-1]}" if not df.empty else "N/A",
                    "calculation_time": calculation_time
                },
                data_points=len(df),
                calculation_time=calculation_time
            )
            
        except Exception as e:
            self.logger.error(f"Error calculating technical indicators: {e}")
            return TechnicalIndicators(
                symbol=historical_data.symbol,
                indicators={},
                metadata={"error": str(e)},
                data_points=0,
                calculation_time=time.time() - start_time
            )
    
    def _historical_data_to_dataframe(self, historical_data: HistoricalData) -> pd.DataFrame:
        """Convert HistoricalData object to pandas DataFrame"""
        try:
            # Create DataFrame from HistoricalData
            data = {
                'Open': historical_data.opens,
                'High': historical_data.highs,
                'Low': historical_data.lows,
                'Close': historical_data.closes,
                'Volume': historical_data.volumes
            }
            
            # Use dates as index if available
            if historical_data.dates:
                df = pd.DataFrame(data, index=pd.to_datetime(historical_data.dates))
            else:
                df = pd.DataFrame(data)
            
            # Ensure numeric types
            for col in ['Open', 'High', 'Low', 'Close']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            df['Volume'] = pd.to_numeric(df['Volume'], errors='coerce')
            
            # Remove any rows with NaN values
            df = df.dropna()
            
            return df
            
        except Exception as e:
            self.logger.error(f"Error converting HistoricalData to DataFrame: {e}")
            return pd.DataFrame()
    
    async def _calculate_all_indicators(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Calculate all technical indicators"""
        indicators = {}
        
        try:
            # Moving Averages
            indicators.update(self._calculate_moving_averages(df))
            
            # Momentum Indicators
            indicators.update(self._calculate_momentum_indicators(df))
            
            # Volatility Indicators
            indicators.update(self._calculate_volatility_indicators(df))
            
            # Volume Indicators
            indicators.update(self._calculate_volume_indicators(df))
            
            # Trend Indicators
            indicators.update(self._calculate_trend_indicators(df))
            
            # Support and Resistance
            indicators.update(self._calculate_support_resistance(df))
            
        except Exception as e:
            self.logger.error(f"Error calculating indicators: {e}")
            indicators["error"] = str(e)
        
        return indicators
    
    def _calculate_moving_averages(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Calculate moving averages"""
        indicators = {}
        
        try:
            close = df['Close']
            
            # Simple Moving Averages
            indicators['sma_5'] = close.rolling(window=5).mean().iloc[-1] if len(close) >= 5 else None
            indicators['sma_10'] = close.rolling(window=10).mean().iloc[-1] if len(close) >= 10 else None
            indicators['sma_20'] = close.rolling(window=20).mean().iloc[-1] if len(close) >= 20 else None
            indicators['sma_50'] = close.rolling(window=50).mean().iloc[-1] if len(close) >= 50 else None
            indicators['sma_200'] = close.rolling(window=200).mean().iloc[-1] if len(close) >= 200 else None
            
            # Exponential Moving Averages
            indicators['ema_12'] = close.ewm(span=12).mean().iloc[-1] if len(close) >= 12 else None
            indicators['ema_26'] = close.ewm(span=26).mean().iloc[-1] if len(close) >= 26 else None
            
        except Exception as e:
            self.logger.error(f"Error calculating moving averages: {e}")
        
        return indicators
    
    def _calculate_momentum_indicators(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Calculate momentum indicators"""
        indicators = {}
        
        try:
            close = df['Close']
            high = df['High']
            low = df['Low']
            
            # RSI (Relative Strength Index)
            if len(close) >= 14:
                delta = close.diff()
                gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
                loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
                rs = gain / loss
                rsi = 100 - (100 / (1 + rs))
                indicators['rsi'] = rsi.iloc[-1] if not rsi.empty else None
            else:
                indicators['rsi'] = None
            
            # MACD
            if len(close) >= 26:
                ema_12 = close.ewm(span=12).mean()
                ema_26 = close.ewm(span=26).mean()
                macd_line = ema_12 - ema_26
                signal_line = macd_line.ewm(span=9).mean()
                histogram = macd_line - signal_line
                
                indicators['macd'] = macd_line.iloc[-1] if not macd_line.empty else None
                indicators['macd_signal'] = signal_line.iloc[-1] if not signal_line.empty else None
                indicators['macd_histogram'] = histogram.iloc[-1] if not histogram.empty else None
            else:
                indicators['macd'] = None
                indicators['macd_signal'] = None
                indicators['macd_histogram'] = None
            
            # Stochastic Oscillator
            if len(close) >= 14:
                lowest_low = low.rolling(window=14).min()
                highest_high = high.rolling(window=14).max()
                k_percent = 100 * ((close - lowest_low) / (highest_high - lowest_low))
                d_percent = k_percent.rolling(window=3).mean()
                
                indicators['stoch_k'] = k_percent.iloc[-1] if not k_percent.empty else None
                indicators['stoch_d'] = d_percent.iloc[-1] if not d_percent.empty else None
            else:
                indicators['stoch_k'] = None
                indicators['stoch_d'] = None
            
        except Exception as e:
            self.logger.error(f"Error calculating momentum indicators: {e}")
        
        return indicators
    
    def _calculate_volatility_indicators(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Calculate volatility indicators"""
        indicators = {}
        
        try:
            close = df['Close']
            high = df['High']
            low = df['Low']
            
            # Bollinger Bands
            if len(close) >= 20:
                sma_20 = close.rolling(window=20).mean()
                std_20 = close.rolling(window=20).std()
                upper_band = sma_20 + (std_20 * 2)
                lower_band = sma_20 - (std_20 * 2)
                
                indicators['bb_upper'] = upper_band.iloc[-1] if not upper_band.empty else None
                indicators['bb_middle'] = sma_20.iloc[-1] if not sma_20.empty else None
                indicators['bb_lower'] = lower_band.iloc[-1] if not lower_band.empty else None
                indicators['bb_width'] = (upper_band - lower_band).iloc[-1] if not upper_band.empty else None
            else:
                indicators['bb_upper'] = None
                indicators['bb_middle'] = None
                indicators['bb_lower'] = None
                indicators['bb_width'] = None
            
            # Average True Range (ATR)
            if len(close) >= 14:
                high_low = high - low
                high_close = np.abs(high - close.shift())
                low_close = np.abs(low - close.shift())
                true_range = np.maximum(high_low, np.maximum(high_close, low_close))
                atr = true_range.rolling(window=14).mean()
                indicators['atr'] = atr.iloc[-1] if not atr.empty else None
            else:
                indicators['atr'] = None
            
        except Exception as e:
            self.logger.error(f"Error calculating volatility indicators: {e}")
        
        return indicators
    
    def _calculate_volume_indicators(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Calculate volume indicators"""
        indicators = {}
        
        try:
            close = df['Close']
            volume = df['Volume']
            
            # Volume Moving Average
            if len(volume) >= 20:
                indicators['volume_sma_20'] = volume.rolling(window=20).mean().iloc[-1]
            else:
                indicators['volume_sma_20'] = None
            
            # On-Balance Volume (OBV)
            if len(close) >= 2:
                obv = np.where(close > close.shift(), volume, 
                             np.where(close < close.shift(), -volume, 0)).cumsum()
                indicators['obv'] = obv[-1] if len(obv) > 0 else None
            else:
                indicators['obv'] = None
            
        except Exception as e:
            self.logger.error(f"Error calculating volume indicators: {e}")
        
        return indicators
    
    def _calculate_trend_indicators(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Calculate trend indicators"""
        indicators = {}
        
        try:
            close = df['Close']
            high = df['High']
            low = df['Low']
            
            # ADX (Average Directional Index)
            if len(close) >= 14:
                high_diff = high.diff()
                low_diff = low.diff()
                
                plus_dm = np.where((high_diff > low_diff) & (high_diff > 0), high_diff, 0)
                minus_dm = np.where((low_diff > high_diff) & (low_diff > 0), low_diff, 0)
                
                plus_di = 100 * (pd.Series(plus_dm).rolling(window=14).mean() / 
                               (high - low).rolling(window=14).mean())
                minus_di = 100 * (pd.Series(minus_dm).rolling(window=14).mean() / 
                                (high - low).rolling(window=14).mean())
                
                dx = 100 * np.abs(plus_di - minus_di) / (plus_di + minus_di)
                adx = dx.rolling(window=14).mean()
                
                indicators['adx'] = adx.iloc[-1] if not adx.empty else None
                indicators['plus_di'] = plus_di.iloc[-1] if not plus_di.empty else None
                indicators['minus_di'] = minus_di.iloc[-1] if not minus_di.empty else None
            else:
                indicators['adx'] = None
                indicators['plus_di'] = None
                indicators['minus_di'] = None
            
        except Exception as e:
            self.logger.error(f"Error calculating trend indicators: {e}")
        
        return indicators
    
    def _calculate_support_resistance(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Calculate support and resistance levels"""
        indicators = {}
        
        try:
            high = df['High']
            low = df['Low']
            close = df['Close']
            
            # Recent highs and lows
            recent_highs = high.tail(20)
            recent_lows = low.tail(20)
            
            indicators['recent_high'] = recent_highs.max() if not recent_highs.empty else None
            indicators['recent_low'] = recent_lows.min() if not recent_lows.empty else None
            
            # Pivot points
            if len(close) >= 1:
                current_close = close.iloc[-1]
                current_high = high.iloc[-1]
                current_low = low.iloc[-1]
                
                pivot = (current_high + current_low + current_close) / 3
                resistance_1 = 2 * pivot - current_low
                support_1 = 2 * pivot - current_high
                resistance_2 = pivot + (current_high - current_low)
                support_2 = pivot - (current_high - current_low)
                
                indicators['pivot_point'] = pivot
                indicators['resistance_1'] = resistance_1
                indicators['support_1'] = support_1
                indicators['resistance_2'] = resistance_2
                indicators['support_2'] = support_2
            
        except Exception as e:
            self.logger.error(f"Error calculating support/resistance: {e}")
        
        return indicators
