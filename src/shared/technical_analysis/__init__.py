"""
Unified Technical Analysis Module
Consolidated from multiple implementations to provide comprehensive technical analysis.
Provides both function-based and class-based APIs for maximum compatibility.
"""

# Core calculator classes
from .calculator import TechnicalAnalysisCalculator
from .unified_calculator import UnifiedTechnicalAnalyzer, TechnicalIndicatorsCalculator, unified_analyzer

# Configuration system
from .config import get_technical_config, UnifiedTechnicalConfig, get_tech_config, TechnicalConfig

# Function-based indicators
from .indicators import (
    calculate_sma,
    calculate_ema,
    calculate_rsi,
    calculate_macd,
    calculate_bollinger_bands,
    calculate_atr,
    calculate_vwap,
    calculate_volume_analysis
)

# Try to import specialized analyzers (may not exist in all environments)
try:
    from .signal_generator import SignalGenerator
    SIGNAL_GENERATOR_AVAILABLE = True
except ImportError:
    SignalGenerator = None
    SIGNAL_GENERATOR_AVAILABLE = False

try:
    from .volume_analyzer import VolumeAnalyzer
    VOLUME_ANALYZER_AVAILABLE = True
except ImportError:
    VolumeAnalyzer = None
    VOLUME_ANALYZER_AVAILABLE = False

try:
    from .zones import supply_demand_detector
    ZONES_AVAILABLE = True
except ImportError:
    supply_demand_detector = None
    ZONES_AVAILABLE = False

__all__ = [
    # Main calculator classes
    'TechnicalAnalysisCalculator',
    'UnifiedTechnicalAnalyzer',
    'TechnicalIndicatorsCalculator',  # Backward compatibility
    'unified_analyzer',

    # Configuration
    'get_technical_config',
    'UnifiedTechnicalConfig',
    'get_tech_config',  # Backward compatibility
    'TechnicalConfig',  # Backward compatibility

    # Function-based indicators
    'calculate_sma',
    'calculate_ema',
    'calculate_rsi',
    'calculate_macd',
    'calculate_bollinger_bands',
    'calculate_atr',
    'calculate_vwap',
    'calculate_volume_analysis'
]

# Add optional components if available
if SIGNAL_GENERATOR_AVAILABLE:
    __all__.append('SignalGenerator')

if VOLUME_ANALYZER_AVAILABLE:
    __all__.append('VolumeAnalyzer')

if ZONES_AVAILABLE:
    __all__.append('supply_demand_detector')