"""
Enhanced Technical Indicators Calculator

Provides advanced technical indicators beyond basic RSI/MACD:
- Fibonacci retracements and extensions
- Elliott Wave analysis (basic)
- Ichimoku Cloud
- Stochastic Oscillator
- Williams %R
- Commodity Channel Index (CCI)
- Average True Range (ATR)
- Volume Weighted Average Price (VWAP)
"""

import math
import logging
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class FibonacciLevel(Enum):
    """Fibonacci retracement and extension levels."""
    RETRACEMENT_236 = 0.236
    RETRACEMENT_382 = 0.382
    RETRACEMENT_500 = 0.500
    RETRACEMENT_618 = 0.618
    RETRACEMENT_786 = 0.786
    EXTENSION_1272 = 1.272
    EXTENSION_1618 = 1.618
    EXTENSION_2618 = 2.618


@dataclass
class FibonacciLevels:
    """Fibonacci retracement and extension levels."""
    retracements: Dict[str, float]
    extensions: Dict[str, float]
    swing_high: float
    swing_low: float
    swing_range: float


@dataclass
class IchimokuCloud:
    """Ichimoku Cloud components."""
    tenkan_sen: float  # Conversion line (9-period)
    kijun_sen: float   # Base line (26-period)
    senkou_span_a: float  # Leading span A
    senkou_span_b: float  # Leading span B
    chikou_span: float    # Lagging span
    cloud_top: float      # Top of cloud
    cloud_bottom: float   # Bottom of cloud
    cloud_color: str      # "green" or "red"


@dataclass
class ElliottWave:
    """Elliott Wave pattern analysis."""
    wave_count: int
    waves: List[Dict[str, Any]]
    pattern_type: str  # "impulse", "corrective", "triangle", etc.
    confidence: float  # 0.0 to 1.0
    next_target: Optional[float]


class EnhancedTechnicalIndicators:
    """Enhanced technical indicator calculator."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def calculate_fibonacci_retracements(self, highs: List[float], lows: List[float]) -> FibonacciLevels:
        """Calculate Fibonacci retracement and extension levels."""
        try:
            if not highs or not lows:
                return None
            
            swing_high = max(highs)
            swing_low = min(lows)
            swing_range = swing_high - swing_low
            
            if swing_range == 0:
                return None
            
            # Calculate retracement levels
            retracements = {
                "0.0": swing_high,
                "0.236": swing_high - (0.236 * swing_range),
                "0.382": swing_high - (0.382 * swing_range),
                "0.500": swing_high - (0.500 * swing_range),
                "0.618": swing_high - (0.618 * swing_range),
                "0.786": swing_high - (0.786 * swing_range),
                "1.0": swing_low
            }
            
            # Calculate extension levels
            extensions = {
                "1.272": swing_low - (0.272 * swing_range),
                "1.618": swing_low - (0.618 * swing_range),
                "2.618": swing_low - (1.618 * swing_range)
            }
            
            return FibonacciLevels(
                retracements=retracements,
                extensions=extensions,
                swing_high=swing_high,
                swing_low=swing_low,
                swing_range=swing_range
            )
            
        except Exception as e:
            self.logger.error(f"Error calculating Fibonacci levels: {e}")
            return None
    
    def calculate_ichimoku_cloud(self, highs: List[float], lows: List[float], 
                                closes: List[float]) -> IchimokuCloud:
        """Calculate Ichimoku Cloud components."""
        try:
            if len(closes) < 52:  # Need at least 52 periods for all components
                return None
            
            # Tenkan-sen (Conversion Line): (9-period high + 9-period low) / 2
            period_9_high = max(highs[-9:])
            period_9_low = min(lows[-9:])
            tenkan_sen = (period_9_high + period_9_low) / 2
            
            # Kijun-sen (Base Line): (26-period high + 26-period low) / 2
            period_26_high = max(highs[-26:])
            period_26_low = min(lows[-26:])
            kijun_sen = (period_26_high + period_26_low) / 2
            
            # Senkou Span A (Leading Span A): (Tenkan-sen + Kijun-sen) / 2
            senkou_span_a = (tenkan_sen + kijun_sen) / 2
            
            # Senkou Span B (Leading Span B): (52-period high + 52-period low) / 2
            period_52_high = max(highs[-52:])
            period_52_low = min(lows[-52:])
            senkou_span_b = (period_52_high + period_52_low) / 2
            
            # Chikou Span (Lagging Span): Current close shifted back 26 periods
            chikou_span = closes[-26] if len(closes) >= 26 else closes[0]
            
            # Cloud boundaries
            cloud_top = max(senkou_span_a, senkou_span_b)
            cloud_bottom = min(senkou_span_a, senkou_span_b)
            
            # Cloud color (green if Span A > Span B, red otherwise)
            cloud_color = "green" if senkou_span_a > senkou_span_b else "red"
            
            return IchimokuCloud(
                tenkan_sen=tenkan_sen,
                kijun_sen=kijun_sen,
                senkou_span_a=senkou_span_a,
                senkou_span_b=senkou_span_b,
                chikou_span=chikou_span,
                cloud_top=cloud_top,
                cloud_bottom=cloud_bottom,
                cloud_color=cloud_color
            )
            
        except Exception as e:
            self.logger.error(f"Error calculating Ichimoku Cloud: {e}")
            return None
    
    def calculate_stochastic_oscillator(self, highs: List[float], lows: List[float], 
                                      closes: List[float], k_period: int = 14, 
                                      d_period: int = 3) -> Dict[str, float]:
        """Calculate Stochastic Oscillator (%K and %D)."""
        try:
            if len(closes) < k_period:
                return None
            
            # Calculate %K
            current_close = closes[-1]
            period_low = min(lows[-k_period:])
            period_high = max(highs[-k_period:])
            
            if period_high == period_low:
                k_percent = 50.0
            else:
                k_percent = ((current_close - period_low) / (period_high - period_low)) * 100
            
            # Calculate %D (SMA of %K)
            k_values = []
            for i in range(k_period):
                if i < len(closes):
                    close_val = closes[-(i+1)]
                    
                    # Calculate the start and end indices for the period
                    start_idx = max(0, len(closes) - (i+1) - k_period)
                    end_idx = len(closes) - (i+1)
                    
                    if start_idx < end_idx and start_idx < len(lows) and end_idx <= len(lows):
                        period_low_val = min(lows[start_idx:end_idx])
                        period_high_val = max(highs[start_idx:end_idx])
                        
                        if period_high_val != period_low_val:
                            k_val = ((close_val - period_low_val) / (period_high_val - period_low_val)) * 100
                        else:
                            k_val = 50.0
                    else:
                        k_val = 50.0  # Default value if insufficient data
                    
                    k_values.append(k_val)
            
            d_percent = sum(k_values) / len(k_values) if k_values else 50.0
            
            return {
                "k_percent": k_percent,
                "d_percent": d_percent,
                "overbought": k_percent > 80,
                "oversold": k_percent < 20
            }
            
        except Exception as e:
            self.logger.error(f"Error calculating Stochastic Oscillator: {e}")
            return None
    
    def calculate_williams_r(self, highs: List[float], lows: List[float], 
                            closes: List[float], period: int = 14) -> Dict[str, float]:
        """Calculate Williams %R oscillator."""
        try:
            if len(closes) < period:
                return None
            
            current_close = closes[-1]
            period_high = max(highs[-period:])
            period_low = min(lows[-period:])
            
            if period_high == period_low:
                williams_r = -50.0
            else:
                williams_r = ((period_high - current_close) / (period_high - period_low)) * -100
            
            return {
                "williams_r": williams_r,
                "overbought": williams_r > -20,
                "oversold": williams_r < -80
            }
            
        except Exception as e:
            self.logger.error(f"Error calculating Williams %R: {e}")
            return None
    
    def calculate_cci(self, highs: List[float], lows: List[float], 
                     closes: List[float], period: int = 20) -> Dict[str, float]:
        """Calculate Commodity Channel Index (CCI)."""
        try:
            if len(closes) < period:
                return None
            
            # Calculate typical price
            typical_prices = []
            for i in range(period):
                if i < len(closes):
                    typical_price = (highs[-(i+1)] + lows[-(i+1)] + closes[-(i+1)]) / 3
                    typical_prices.append(typical_price)
            
            if not typical_prices:
                return None
            
            # Calculate SMA of typical price
            sma_tp = sum(typical_prices) / len(typical_prices)
            
            # Calculate mean deviation
            mean_deviation = sum(abs(tp - sma_tp) for tp in typical_prices) / len(typical_prices)
            
            # Calculate CCI
            current_tp = (highs[-1] + lows[-1] + closes[-1]) / 3
            cci = (current_tp - sma_tp) / (0.015 * mean_deviation) if mean_deviation != 0 else 0
            
            return {
                "cci": cci,
                "overbought": cci > 100,
                "oversold": cci < -100,
                "neutral": -100 <= cci <= 100
            }
            
        except Exception as e:
            self.logger.error(f"Error calculating CCI: {e}")
            return None
    
    def calculate_atr(self, highs: List[float], lows: List[float], 
                     closes: List[float], period: int = 14) -> Dict[str, float]:
        """Calculate Average True Range (ATR)."""
        try:
            if len(closes) < period + 1:
                return None
            
            true_ranges = []
            
            for i in range(1, len(closes)):
                high_low = highs[i] - lows[i]
                high_close_prev = abs(highs[i] - closes[i-1])
                low_close_prev = abs(lows[i] - closes[i-1])
                
                true_range = max(high_low, high_close_prev, low_close_prev)
                true_ranges.append(true_range)
            
            if len(true_ranges) < period:
                return None
            
            # Calculate ATR as SMA of true ranges
            atr = sum(true_ranges[-period:]) / period
            
            # Calculate ATR percentage
            current_price = closes[-1]
            atr_percentage = (atr / current_price) * 100 if current_price != 0 else 0
            
            return {
                "atr": atr,
                "atr_percentage": atr_percentage,
                "volatility_level": self._classify_volatility(atr_percentage)
            }
            
        except Exception as e:
            self.logger.error(f"Error calculating ATR: {e}")
            return None
    
    def calculate_vwap(self, prices: List[float], volumes: List[float]) -> Dict[str, float]:
        """Calculate Volume Weighted Average Price (VWAP)."""
        try:
            if len(prices) != len(volumes) or len(prices) == 0:
                return None
            
            # Calculate VWAP
            cumulative_tpv = 0  # Total Price * Volume
            cumulative_volume = 0
            
            for price, volume in zip(prices, volumes):
                cumulative_tpv += price * volume
                cumulative_volume += volume
            
            if cumulative_volume == 0:
                return None
            
            vwap = cumulative_tpv / cumulative_volume
            
            # Calculate VWAP bands (similar to Bollinger Bands)
            if len(prices) >= 20:
                # Calculate standard deviation of prices
                price_deviations = [(price - vwap) ** 2 for price in prices[-20:]]
                std_dev = math.sqrt(sum(price_deviations) / len(price_deviations))
                
                upper_band = vwap + (2 * std_dev)
                lower_band = vwap - (2 * std_dev)
            else:
                upper_band = vwap * 1.02  # 2% above VWAP
                lower_band = vwap * 0.98  # 2% below VWAP
            
            # Current price position relative to VWAP
            current_price = prices[-1] if prices else vwap
            vwap_position = ((current_price - vwap) / vwap) * 100 if vwap != 0 else 0
            
            return {
                "vwap": vwap,
                "upper_band": upper_band,
                "lower_band": lower_band,
                "vwap_position": vwap_position,
                "above_vwap": current_price > vwap,
                "below_vwap": current_price < vwap
            }
            
        except Exception as e:
            self.logger.error(f"Error calculating VWAP: {e}")
            return None
    
    def calculate_elliott_wave(self, prices: List[float], highs: List[float], 
                              lows: List[float]) -> ElliottWave:
        """Basic Elliott Wave pattern analysis."""
        try:
            if len(prices) < 20:
                return None
            
            # This is a simplified Elliott Wave analysis
            # In practice, this would be much more complex
            
            # Find significant swing points
            swing_points = self._find_swing_points(prices, highs, lows)
            
            if len(swing_points) < 5:
                return None
            
            # Basic wave counting (simplified)
            wave_count = min(len(swing_points), 8)  # Max 8 waves
            
            # Determine pattern type based on wave structure
            pattern_type = self._classify_elliott_pattern(swing_points)
            
            # Calculate confidence based on pattern clarity
            confidence = self._calculate_pattern_confidence(swing_points, pattern_type)
            
            # Estimate next target
            next_target = self._estimate_next_target(swing_points, pattern_type)
            
            # Create wave objects
            waves = []
            for i, point in enumerate(swing_points[:wave_count]):
                waves.append({
                    "wave_number": i + 1,
                    "price": point["price"],
                    "type": point["type"],  # "high" or "low"
                    "timestamp": point.get("timestamp", None)
                })
            
            return ElliottWave(
                wave_count=wave_count,
                waves=waves,
                pattern_type=pattern_type,
                confidence=confidence,
                next_target=next_target
            )
            
        except Exception as e:
            self.logger.error(f"Error calculating Elliott Wave: {e}")
            return None
    
    def calculate_momentum_indicators(self, prices: List[float], 
                                    volumes: List[float]) -> Dict[str, Any]:
        """Calculate momentum-based indicators."""
        try:
            if len(prices) < 14:
                return None
            
            momentum_indicators = {}
            
            # Rate of Change (ROC)
            if len(prices) >= 14:
                current_price = prices[-1]
                price_14_periods_ago = prices[-14]
                roc = ((current_price - price_14_periods_ago) / price_14_periods_ago) * 100
                momentum_indicators["roc"] = roc
            
            # Money Flow Index (MFI)
            if len(prices) >= 14 and len(volumes) >= 14:
                mfi = self._calculate_mfi(prices, volumes, 14)
                momentum_indicators["mfi"] = mfi
                momentum_indicators["mfi_overbought"] = mfi > 80
                momentum_indicators["mfi_oversold"] = mfi < 20
            
            # On-Balance Volume (OBV)
            if len(prices) >= 2 and len(volumes) >= 2:
                obv = self._calculate_obv(prices, volumes)
                momentum_indicators["obv"] = obv
            
            # Accumulation/Distribution Line
            if len(prices) >= 2 and len(volumes) >= 2:
                ad_line = self._calculate_ad_line(prices, volumes)
                momentum_indicators["ad_line"] = ad_line
            
            return momentum_indicators
            
        except Exception as e:
            self.logger.error(f"Error calculating momentum indicators: {e}")
            return None
    
    def _classify_volatility(self, atr_percentage: float) -> str:
        """Classify volatility level based on ATR percentage."""
        if atr_percentage < 1.0:
            return "low"
        elif atr_percentage < 3.0:
            return "medium"
        else:
            return "high"
    
    def _find_swing_points(self, prices: List[float], highs: List[float], 
                           lows: List[float]) -> List[Dict[str, Any]]:
        """Find significant swing high and low points."""
        swing_points = []
        
        # Look for local maxima and minima
        for i in range(1, len(prices) - 1):
            # Swing high
            if prices[i] > prices[i-1] and prices[i] > prices[i+1]:
                swing_points.append({
                    "price": prices[i],
                    "type": "high",
                    "index": i
                })
            
            # Swing low
            elif prices[i] < prices[i-1] and prices[i] < prices[i+1]:
                swing_points.append({
                    "price": prices[i],
                    "type": "low",
                    "index": i
                })
        
        # Sort by index
        swing_points.sort(key=lambda x: x["index"])
        
        return swing_points
    
    def _classify_elliott_pattern(self, swing_points: List[Dict[str, Any]]) -> str:
        """Classify Elliott Wave pattern type."""
        if len(swing_points) < 5:
            return "incomplete"
        
        # Simple pattern classification
        first_wave = swing_points[0]
        second_wave = swing_points[1]
        
        if first_wave["type"] == "high" and second_wave["type"] == "low":
            return "impulse"
        elif first_wave["type"] == "low" and second_wave["type"] == "high":
            return "impulse"
        else:
            return "corrective"
    
    def _calculate_pattern_confidence(self, swing_points: List[Dict[str, Any]], 
                                    pattern_type: str) -> float:
        """Calculate confidence in the Elliott Wave pattern."""
        if len(swing_points) < 5:
            return 0.0
        
        # Base confidence on number of waves and pattern clarity
        base_confidence = min(len(swing_points) / 8.0, 1.0)
        
        # Adjust for pattern type
        if pattern_type == "impulse":
            pattern_multiplier = 1.0
        elif pattern_type == "corrective":
            pattern_multiplier = 0.8
        else:
            pattern_multiplier = 0.6
        
        return base_confidence * pattern_multiplier
    
    def _estimate_next_target(self, swing_points: List[Dict[str, Any]], 
                             pattern_type: str) -> Optional[float]:
        """Estimate next price target based on Elliott Wave pattern."""
        if len(swing_points) < 3:
            return None
        
        try:
            if pattern_type == "impulse":
                # For impulse waves, target is often 1.618 extension
                wave_1_range = abs(swing_points[1]["price"] - swing_points[0]["price"])
                wave_3_end = swing_points[2]["price"]
                
                if swing_points[1]["type"] == "low":  # Bullish impulse
                    target = wave_3_end + (wave_1_range * 1.618)
                else:  # Bearish impulse
                    target = wave_3_end - (wave_1_range * 1.618)
                
                return target
            else:
                # For corrective waves, target is often 0.618 retracement
                trend_range = abs(swing_points[-1]["price"] - swing_points[0]["price"])
                current_price = swing_points[-1]["price"]
                
                if swing_points[-1]["type"] == "high":  # Correcting down
                    target = current_price - (trend_range * 0.618)
                else:  # Correcting up
                    target = current_price + (trend_range * 0.618)
                
                return target
                
        except Exception as e:
            self.logger.error(f"Error estimating next target: {e}")
            return None
    
    def _calculate_mfi(self, prices: List[float], volumes: List[float], period: int) -> float:
        """Calculate Money Flow Index."""
        try:
            if len(prices) < period + 1:
                return 50.0
            
            positive_money_flow = 0
            negative_money_flow = 0
            
            for i in range(1, len(prices)):
                if i < len(volumes):
                    price_change = prices[i] - prices[i-1]
                    volume = volumes[i]
                    
                    if price_change > 0:
                        positive_money_flow += price_change * volume
                    elif price_change < 0:
                        negative_money_flow += abs(price_change) * volume
            
            if negative_money_flow == 0:
                return 100.0
            
            money_ratio = positive_money_flow / negative_money_flow
            mfi = 100 - (100 / (1 + money_ratio))
            
            return mfi
            
        except Exception as e:
            self.logger.error(f"Error calculating MFI: {e}")
            return 50.0
    
    def _calculate_obv(self, prices: List[float], volumes: List[float]) -> float:
        """Calculate On-Balance Volume."""
        try:
            if len(prices) < 2 or len(volumes) < 2:
                return 0.0
            
            obv = 0.0
            
            for i in range(1, len(prices)):
                if i < len(volumes):
                    if prices[i] > prices[i-1]:
                        obv += volumes[i]
                    elif prices[i] < prices[i-1]:
                        obv -= volumes[i]
            
            return obv
            
        except Exception as e:
            self.logger.error(f"Error calculating OBV: {e}")
            return 0.0
    
    def _calculate_ad_line(self, prices: List[float], volumes: List[float]) -> float:
        """Calculate Accumulation/Distribution Line."""
        try:
            if len(prices) < 2 or len(volumes) < 2:
                return 0.0
            
            ad_line = 0.0
            
            for i in range(len(prices)):
                if i < len(volumes):
                    high = prices[i]  # Simplified - should use actual high/low
                    low = prices[i]   # Simplified - should use actual high/low
                    close = prices[i]
                    volume = volumes[i]
                    
                    if high != low:
                        money_flow_multiplier = ((close - low) - (high - close)) / (high - low)
                    else:
                        money_flow_multiplier = 0
                    
                    ad_line += money_flow_multiplier * volume
            
            return ad_line
            
        except Exception as e:
            self.logger.error(f"Error calculating AD Line: {e}")
            return 0.0


# Convenience function for creating enhanced indicators calculator
def create_enhanced_indicators() -> EnhancedTechnicalIndicators:
    """Create a new enhanced technical indicators calculator instance."""
    return EnhancedTechnicalIndicators() 