"""
Volume Analysis Engine

Analyzes volume patterns and detects market anomalies:
- Volume profile analysis
- Unusual volume detection
- Volume clusters and zones
- Volume trend analysis
- Volume-based indicators
"""

import logging
import math
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import numpy as np

logger = logging.getLogger(__name__)


@dataclass
class VolumeZone:
    """Represents a volume zone at a specific price level."""
    price_level: float
    volume: float
    volume_percentage: float
    price_range: Tuple[float, float]
    zone_type: str  # "high_volume", "low_volume", "normal"
    significance: float  # 0.0 to 1.0


@dataclass
class VolumeAnomaly:
    """Represents a volume anomaly detection."""
    anomaly_type: str  # "spike", "dip", "climax", "divergence"
    severity: float  # 0.0 to 1.0
    description: str
    timestamp: datetime
    price_level: float
    volume_ratio: float
    confidence: float


@dataclass
class VolumeProfile:
    """Complete volume profile analysis."""
    vwap: float
    volume_zones: List[VolumeZone]
    high_volume_nodes: List[float]
    low_volume_nodes: List[float]
    volume_trend: str
    unusual_volume: Optional[VolumeAnomaly]
    volume_indicators: Dict[str, float]


class VolumeAnalyzer:
    """Analyzes volume patterns and detects market anomalies."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Configuration
        self.volume_spike_threshold = 2.0  # Volume spike is 2x average
        self.volume_dip_threshold = 0.5    # Volume dip is 0.5x average
        self.zone_tolerance = 0.02         # 2% price tolerance for zones
        self.min_zone_volume = 0.05        # Minimum 5% of total volume for zone
    
    def analyze_volume_profile(self, prices: List[float], volumes: List[float], 
                              timestamps: Optional[List[datetime]] = None) -> VolumeProfile:
        """Analyze complete volume profile."""
        try:
            if len(prices) != len(volumes) or len(prices) < 20:
                return None
            
            # Calculate VWAP
            vwap = self._calculate_vwap(prices, volumes)
            
            # Find volume zones
            volume_zones = self._find_volume_zones(prices, volumes)
            
            # Identify high and low volume nodes
            high_volume_nodes = self._find_high_volume_nodes(volume_zones)
            low_volume_nodes = self._find_low_volume_nodes(volume_zones)
            
            # Analyze volume trend
            volume_trend = self._analyze_volume_trend(volumes)
            
            # Detect unusual volume
            unusual_volume = self._detect_unusual_volume(volumes, prices, timestamps)
            
            # Calculate volume indicators
            volume_indicators = self._calculate_volume_indicators(prices, volumes)
            
            return VolumeProfile(
                vwap=vwap,
                volume_zones=volume_zones,
                high_volume_nodes=high_volume_nodes,
                low_volume_nodes=low_volume_nodes,
                volume_trend=volume_trend,
                unusual_volume=unusual_volume,
                volume_indicators=volume_indicators
            )
            
        except Exception as e:
            self.logger.error(f"Error analyzing volume profile: {e}")
            return None
    
    def _calculate_vwap(self, prices: List[float], volumes: List[float]) -> float:
        """Calculate Volume Weighted Average Price."""
        try:
            if len(prices) != len(volumes) or len(prices) == 0:
                return 0.0
            
            total_volume = sum(volumes)
            if total_volume == 0:
                return sum(prices) / len(prices)  # Fallback to simple average
            
            vwap = sum(p * v for p, v in zip(prices, volumes)) / total_volume
            return vwap
            
        except Exception as e:
            self.logger.error(f"Error calculating VWAP: {e}")
            return 0.0
    
    def _find_volume_zones(self, prices: List[float], volumes: List[float]) -> List[VolumeZone]:
        """Find volume zones at different price levels."""
        try:
            if len(prices) < 20:
                return []
            
            # Create price-volume histogram
            price_volume_map = {}
            
            for price, volume in zip(prices, volumes):
                # Round price to create bins
                price_bin = round(price / self.zone_tolerance) * self.zone_tolerance
                
                if price_bin not in price_volume_map:
                    price_volume_map[price_bin] = 0
                price_volume_map[price_bin] += volume
            
            # Calculate total volume
            total_volume = sum(price_volume_map.values())
            
            # Create volume zones
            volume_zones = []
            for price_level, volume in price_volume_map.items():
                volume_percentage = volume / total_volume if total_volume > 0 else 0
                
                # Determine zone type
                if volume_percentage > 0.1:  # More than 10% of total volume
                    zone_type = "high_volume"
                    significance = min(volume_percentage * 10, 1.0)
                elif volume_percentage < 0.02:  # Less than 2% of total volume
                    zone_type = "low_volume"
                    significance = 1.0 - (volume_percentage * 50)
                else:
                    zone_type = "normal"
                    significance = 0.5
                
                # Define price range for zone
                price_range = (price_level - self.zone_tolerance/2, price_level + self.zone_tolerance/2)
                
                zone = VolumeZone(
                    price_level=price_level,
                    volume=volume,
                    volume_percentage=volume_percentage,
                    price_range=price_range,
                    zone_type=zone_type,
                    significance=significance
                )
                
                volume_zones.append(zone)
            
            # Sort zones by significance
            volume_zones.sort(key=lambda x: x.significance, reverse=True)
            
            return volume_zones
            
        except Exception as e:
            self.logger.error(f"Error finding volume zones: {e}")
            return []
    
    def _find_high_volume_nodes(self, volume_zones: List[VolumeZone]) -> List[float]:
        """Find high volume price nodes."""
        try:
            high_volume_nodes = []
            
            for zone in volume_zones:
                if zone.zone_type == "high_volume" and zone.significance > 0.7:
                    high_volume_nodes.append(zone.price_level)
            
            return high_volume_nodes
            
        except Exception as e:
            self.logger.error(f"Error finding high volume nodes: {e}")
            return []
    
    def _find_low_volume_nodes(self, volume_zones: List[VolumeZone]) -> List[float]:
        """Find low volume price nodes (potential support/resistance)."""
        try:
            low_volume_nodes = []
            
            for zone in volume_zones:
                if zone.zone_type == "low_volume" and zone.significance > 0.8:
                    low_volume_nodes.append(zone.price_level)
            
            return low_volume_nodes
            
        except Exception as e:
            self.logger.error(f"Error finding low volume nodes: {e}")
            return []
    
    def _analyze_volume_trend(self, volumes: List[float]) -> str:
        """Analyze overall volume trend."""
        try:
            if len(volumes) < 20:
                return "insufficient_data"
            
            # Calculate volume moving averages
            recent_volume = sum(volumes[-5:]) / 5
            historical_volume = sum(volumes[-20:]) / 20
            
            if recent_volume > historical_volume * 1.2:
                return "increasing"
            elif recent_volume < historical_volume * 0.8:
                return "decreasing"
            else:
                return "stable"
                
        except Exception as e:
            self.logger.error(f"Error analyzing volume trend: {e}")
            return "unknown"
    
    def _detect_unusual_volume(self, volumes: List[float], prices: List[float], 
                               timestamps: Optional[List[datetime]] = None) -> Optional[VolumeAnomaly]:
        """Detect unusual volume patterns."""
        try:
            if len(volumes) < 25:  # Need more data for this approach
                return None
            
            # Calculate volume statistics
            recent_volumes = volumes[-5:]  # Last 5 volumes
            historical_volumes = volumes[-25:-5]  # Volumes 6-25 (excluding recent)
            
            recent_avg = sum(recent_volumes) / len(recent_volumes)
            historical_avg = sum(historical_volumes) / len(historical_volumes)
            historical_std = self._calculate_standard_deviation(historical_volumes)
            
            # Debug logging
            self.logger.debug(f"Recent avg: {recent_avg:,.0f}, Historical avg: {historical_avg:,.0f}, Std: {historical_std:,.0f}")
            self.logger.debug(f"Threshold: {historical_avg + (1.5 * historical_std):,.0f}")
            
            # Detect volume spike (reduced threshold from 2x to 1.5x std)
            if recent_avg > historical_avg + (1.5 * historical_std):
                volume_ratio = recent_avg / historical_avg
                severity = min((volume_ratio - 1) / 2, 1.0)
                
                return VolumeAnomaly(
                    anomaly_type="spike",
                    severity=severity,
                    description=f"Volume spike detected: {volume_ratio:.1f}x average",
                    timestamp=timestamps[-1] if timestamps else datetime.now(),
                    price_level=prices[-1] if prices else 0.0,
                    volume_ratio=volume_ratio,
                    confidence=min(severity * 1.5, 1.0)
                )
            
            # Detect volume dip (reduced threshold from 2x to 1.5x std)
            elif recent_avg < historical_avg - (1.5 * historical_std):
                volume_ratio = recent_avg / historical_avg
                severity = min((1 - volume_ratio) / 0.5, 1.0)
                
                return VolumeAnomaly(
                    anomaly_type="dip",
                    severity=severity,
                    description=f"Volume dip detected: {volume_ratio:.1f}x average",
                    timestamp=timestamps[-1] if timestamps else datetime.now(),
                    price_level=prices[-1] if prices else 0.0,
                    volume_ratio=volume_ratio,
                    confidence=min(severity * 1.5, 1.0)
                )
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error detecting unusual volume: {e}")
            return None
    
    def _calculate_volume_indicators(self, prices: List[float], volumes: List[float]) -> Dict[str, float]:
        """Calculate various volume-based indicators."""
        try:
            indicators = {}
            
            if len(prices) >= 14 and len(volumes) >= 14:
                # On-Balance Volume (OBV)
                indicators["obv"] = self._calculate_obv(prices, volumes)
                
                # Money Flow Index (MFI)
                indicators["mfi"] = self._calculate_mfi(prices, volumes, 14)
                
                # Volume Rate of Change
                indicators["volume_roc"] = self._calculate_volume_roc(volumes, 14)
                
                # Accumulation/Distribution Line
                indicators["ad_line"] = self._calculate_ad_line(prices, volumes)
                
                # Volume Weighted Average Price
                indicators["vwap"] = self._calculate_vwap(prices, volumes)
                
                # Volume SMA
                indicators["volume_sma"] = sum(volumes[-20:]) / 20 if len(volumes) >= 20 else 0
                
                # Volume ratio (current vs average)
                if indicators["volume_sma"] > 0:
                    indicators["volume_ratio"] = volumes[-1] / indicators["volume_sma"]
                else:
                    indicators["volume_ratio"] = 1.0
            
            return indicators
            
        except Exception as e:
            self.logger.error(f"Error calculating volume indicators: {e}")
            return {}
    
    def _calculate_obv(self, prices: List[float], volumes: List[float]) -> float:
        """Calculate On-Balance Volume."""
        try:
            if len(prices) < 2 or len(volumes) < 2:
                return 0.0
            
            obv = 0.0
            
            for i in range(1, len(prices)):
                if i < len(volumes):
                    if prices[i] > prices[i-1]:
                        obv += volumes[i]
                    elif prices[i] < prices[i-1]:
                        obv -= volumes[i]
            
            return obv
            
        except Exception as e:
            self.logger.error(f"Error calculating OBV: {e}")
            return 0.0
    
    def _calculate_mfi(self, prices: List[float], volumes: List[float], period: int) -> float:
        """Calculate Money Flow Index."""
        try:
            if len(prices) < period + 1:
                return 50.0
            
            positive_money_flow = 0
            negative_money_flow = 0
            
            for i in range(1, len(prices)):
                if i < len(volumes):
                    price_change = prices[i] - prices[i-1]
                    volume = volumes[i]
                    
                    if price_change > 0:
                        positive_money_flow += price_change * volume
                    elif price_change < 0:
                        negative_money_flow += abs(price_change) * volume
            
            if negative_money_flow == 0:
                return 100.0
            
            money_ratio = positive_money_flow / negative_money_flow
            mfi = 100 - (100 / (1 + money_ratio))
            
            return mfi
            
        except Exception as e:
            self.logger.error(f"Error calculating MFI: {e}")
            return 50.0
    
    def _calculate_volume_roc(self, volumes: List[float], period: int) -> float:
        """Calculate Volume Rate of Change."""
        try:
            if len(volumes) < period + 1:
                return 0.0
            
            current_volume = volumes[-1]
            volume_periods_ago = volumes[-period-1]
            
            if volume_periods_ago == 0:
                return 0.0
            
            roc = ((current_volume - volume_periods_ago) / volume_periods_ago) * 100
            return roc
            
        except Exception as e:
            self.logger.error(f"Error calculating Volume ROC: {e}")
            return 0.0
    
    def _calculate_ad_line(self, prices: List[float], volumes: List[float]) -> float:
        """Calculate Accumulation/Distribution Line."""
        try:
            if len(prices) < 2 or len(volumes) < 2:
                return 0.0
            
            ad_line = 0.0
            
            for i in range(len(prices)):
                if i < len(volumes):
                    # Simplified calculation - in practice you'd use actual high/low
                    high = prices[i] + 1
                    low = prices[i] - 1
                    close = prices[i]
                    volume = volumes[i]
                    
                    if high != low:
                        money_flow_multiplier = ((close - low) - (high - close)) / (high - low)
                    else:
                        money_flow_multiplier = 0
                    
                    ad_line += money_flow_multiplier * volume
            
            return ad_line
            
        except Exception as e:
            self.logger.error(f"Error calculating AD Line: {e}")
            return 0.0
    
    def _calculate_standard_deviation(self, values: List[float]) -> float:
        """Calculate standard deviation of a list of values."""
        try:
            if len(values) < 2:
                return 0.0
            
            mean = sum(values) / len(values)
            squared_diff_sum = sum((x - mean) ** 2 for x in values)
            variance = squared_diff_sum / len(values)
            
            return math.sqrt(variance)
            
        except Exception as e:
            self.logger.error(f"Error calculating standard deviation: {e}")
            return 0.0
    
    def detect_volume_divergence(self, prices: List[float], volumes: List[float]) -> Optional[VolumeAnomaly]:
        """Detect volume-price divergence."""
        try:
            if len(prices) < 20 or len(volumes) < 20:
                return None
            
            # Calculate price and volume trends
            price_trend = self._calculate_trend_slope(prices[-20:])
            volume_trend = self._calculate_trend_slope(volumes[-20:])
            
            # Detect divergence
            if price_trend > 0.01 and volume_trend < -0.01:  # Price up, volume down
                return VolumeAnomaly(
                    anomaly_type="divergence",
                    severity=0.7,
                    description="Bearish divergence: Price rising, volume declining",
                    timestamp=datetime.now(),
                    price_level=prices[-1],
                    volume_ratio=volumes[-1] / (sum(volumes[-20:]) / 20),
                    confidence=0.7
                )
            elif price_trend < -0.01 and volume_trend > 0.01:  # Price down, volume up
                return VolumeAnomaly(
                    anomaly_type="divergence",
                    severity=0.7,
                    description="Bullish divergence: Price declining, volume increasing",
                    timestamp=datetime.now(),
                    price_level=prices[-1],
                    volume_ratio=volumes[-1] / (sum(volumes[-20:]) / 20),
                    confidence=0.7
                )
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error detecting volume divergence: {e}")
            return None
    
    def _calculate_trend_slope(self, values: List[float]) -> float:
        """Calculate trend slope using linear regression."""
        try:
            if len(values) < 5:
                return 0.0
            
            n = len(values)
            x_values = list(range(n))
            
            # Calculate means
            x_mean = sum(x_values) / n
            y_mean = sum(values) / n
            
            # Calculate slope
            numerator = sum((x - x_mean) * (y - y_mean) for x, y in zip(x_values, values))
            denominator = sum((x - x_mean) ** 2 for x in x_values)
            
            if denominator == 0:
                return 0.0
            
            slope = numerator / denominator
            return slope
            
        except Exception as e:
            self.logger.error(f"Error calculating trend slope: {e}")
            return 0.0
    
    def get_volume_summary(self, volume_profile: VolumeProfile) -> Dict[str, Any]:
        """Get a summary of volume analysis."""
        try:
            if not volume_profile:
                return {}
            
            summary = {
                "vwap": volume_profile.vwap,
                "volume_trend": volume_profile.volume_trend,
                "high_volume_nodes_count": len(volume_profile.high_volume_nodes),
                "low_volume_nodes_count": len(volume_profile.low_volume_nodes),
                "total_volume_zones": len(volume_profile.volume_zones),
                "unusual_volume_detected": volume_profile.unusual_volume is not None
            }
            
            if volume_profile.unusual_volume:
                summary["unusual_volume_type"] = volume_profile.unusual_volume.anomaly_type
                summary["unusual_volume_severity"] = volume_profile.unusual_volume.severity
            
            # Add key volume indicators
            for key, value in volume_profile.volume_indicators.items():
                summary[f"indicator_{key}"] = value
            
            return summary
            
        except Exception as e:
            self.logger.error(f"Error getting volume summary: {e}")
            return {}


# Convenience function for creating volume analyzer
def create_volume_analyzer() -> VolumeAnalyzer:
    """Create a new volume analyzer instance."""
    return VolumeAnalyzer() 