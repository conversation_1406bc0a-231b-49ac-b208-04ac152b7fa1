"""
Test script for technical analysis indicators.
This script demonstrates how to use the TechnicalAnalysisCalculator with sample data.
"""

import pandas as pd
import numpy as np
import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..'))

from src.shared.technical_analysis.calculator import TechnicalAnalysisCalculator

def create_sample_data():
    """Create sample OHLCV data for testing"""
    np.random.seed(42)  # For reproducible results
    
    # Generate sample price data with some trend and noise
    dates = pd.date_range(start='2024-01-01', periods=100, freq='D')
    base_price = 100
    trend = np.linspace(0, 20, 100)  # Upward trend
    noise = np.random.normal(0, 2, 100)  # Random noise
    
    close = base_price + trend + noise
    high = close + np.random.uniform(0.5, 2.0, 100)  # High is close + random
    low = close - np.random.uniform(0.5, 2.0, 100)   # Low is close - random
    volume = np.random.randint(100000, 1000000, 100)  # Random volume
    
    df = pd.DataFrame({
        'open': close - np.random.uniform(0.1, 1.0, 100),  # Open slightly different from close
        'high': high,
        'low': low,
        'close': close,
        'volume': volume
    }, index=dates)
    
    return df

def test_indicators():
    """Test all indicator calculations"""
    print("Testing Technical Analysis Indicators...")
    print("=" * 50)
    
    # Create sample data
    df = create_sample_data()
    print(f"Sample data shape: {df.shape}")
    print(f"Latest close price: {df['close'].iloc[-1]:.2f}")
    print()
    
    # Initialize calculator
    calculator = TechnicalAnalysisCalculator()
    
    # Calculate all indicators
    results = calculator.calculate_all_indicators(df, symbol="TEST")
    
    # Display results
    print("Indicator Results:")
    print("-" * 30)
    for key, value in results.items():
        if value is not None and isinstance(value, (int, float)):
            print(f"{key:20}: {value:.4f}")
        else:
            print(f"{key:20}: {value}")
    
    print("\nConfiguration:")
    print("-" * 30)
    config = calculator.get_configuration()
    for key, value in config.items():
        print(f"{key:20}: {value}")

if __name__ == "__main__":
    test_indicators()