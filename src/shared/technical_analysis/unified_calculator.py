"""
Unified Technical Analysis Calculator
Consolidates functionality from both src/analysis/technical/ and src/shared/technical_analysis/
Provides both function-based and class-based APIs for maximum compatibility.
"""

import logging
from typing import Dict, List, Optional, Any, Union

# Use lazy imports for heavy dependencies
from src.shared.utils.lazy_import import get_lazy_module, lazy_import

# Register heavy modules for lazy loading
lazy_import("pandas", "pandas")
lazy_import("numpy", "numpy")

from .config import get_technical_config
from .calculator import TechnicalAnalysisCalculator
from .indicators import (
    calculate_sma, calculate_ema, calculate_rsi, calculate_macd,
    calculate_bollinger_bands, calculate_atr, calculate_vwap
)

logger = logging.getLogger(__name__)

# Lazy load modules
def _get_pd():
    """Get pandas module with lazy loading."""
    return get_lazy_module("pandas")

def _get_np():
    """Get numpy module with lazy loading."""
    return get_lazy_module("numpy")

class UnifiedTechnicalAnalyzer:
    """
    Unified Technical Analysis Calculator that consolidates all functionality.
    Provides both class-based and function-based APIs for maximum compatibility.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize unified technical analyzer.
        
        Args:
            config: Optional configuration override
        """
        self.config = get_technical_config()
        self.calculator = TechnicalAnalysisCalculator()
        self.logger = logging.getLogger(__name__)
        
        # Override config if provided
        if config:
            for key, value in config.items():
                if hasattr(self.config, key):
                    setattr(self.config, key, value)
    
    def calculate_rsi(self, prices: Union[List[float], Any], period: Optional[int] = None) -> Optional[float]:
        """
        Calculate RSI with unified interface.
        Supports both list and pandas Series input.
        """
        try:
            period = period or self.config.rsi_period
            
            if isinstance(prices, list):
                pd = _get_pd()
                prices = pd.Series(prices)
            
            return calculate_rsi(prices, period)
            
        except Exception as e:
            self.logger.error(f"RSI calculation error: {e}")
            return None
    
    def calculate_macd(self, prices: Union[List[float], Any], 
                      fast: Optional[int] = None, slow: Optional[int] = None, 
                      signal: Optional[int] = None) -> Optional[Dict[str, float]]:
        """
        Calculate MACD with unified interface.
        """
        try:
            fast = fast or self.config.macd_fast_period
            slow = slow or self.config.macd_slow_period
            signal = signal or self.config.macd_signal_period
            
            if isinstance(prices, list):
                pd = _get_pd()
                prices = pd.Series(prices)
            
            return calculate_macd(prices, fast, slow, signal)
            
        except Exception as e:
            self.logger.error(f"MACD calculation error: {e}")
            return None
    
    def calculate_sma(self, prices: Union[List[float], Any], window: Optional[int] = None) -> Optional[float]:
        """Calculate SMA with unified interface."""
        try:
            window = window or self.config.sma_window
            
            pd = _get_pd()
            if isinstance(prices, list):
                prices = pd.Series(prices)
            
            return calculate_sma(prices, window)
            
        except Exception as e:
            self.logger.error(f"SMA calculation error: {e}")
            return None
    
    def calculate_ema(self, prices: Union[List[float], Any], span: Optional[int] = None) -> Optional[float]:
        """Calculate EMA with unified interface."""
        try:
            span = span or self.config.ema_span
            
            if isinstance(prices, list):
                pd = _get_pd()
                prices = pd.Series(prices)
            
            return calculate_ema(prices, span)
            
        except Exception as e:
            self.logger.error(f"EMA calculation error: {e}")
            return None
    
    def calculate_bollinger_bands(self, prices: Union[List[float], Any],
                                 window: Optional[int] = None,
                                 std_dev: Optional[float] = None) -> Optional[Dict[str, float]]:
        """Calculate Bollinger Bands with unified interface."""
        try:
            window = window or self.config.bb_window
            std_dev = std_dev or self.config.bb_std
            
            if isinstance(prices, list):
                pd = _get_pd()
                prices = pd.Series(prices)
            
            return calculate_bollinger_bands(prices, window, std_dev)
            
        except Exception as e:
            self.logger.error(f"Bollinger Bands calculation error: {e}")
            return None
    
    def full_analysis(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Perform comprehensive technical analysis.
        
        Args:
            data: Market data dictionary with 'close', 'high', 'low', 'volume' keys
            
        Returns:
            Dictionary containing all calculated indicators
        """
        try:
            results = {}
            
            # Extract price series
            close_prices = data.get('close', [])
            high_prices = data.get('high', [])
            low_prices = data.get('low', [])
            volume = data.get('volume', [])
            
            if not close_prices:
                return results
            
            pd = _get_pd()
            close_series = pd.Series(close_prices)
            
            # Calculate all indicators
            results['rsi'] = self.calculate_rsi(close_series)
            results['macd'] = self.calculate_macd(close_series)
            results['sma_20'] = self.calculate_sma(close_series, 20)
            results['sma_50'] = self.calculate_sma(close_series, 50)
            results['ema_12'] = self.calculate_ema(close_series, 12)
            results['ema_26'] = self.calculate_ema(close_series, 26)
            results['bollinger_bands'] = self.calculate_bollinger_bands(close_series)
            
            # Calculate ATR if high/low data available
            if high_prices and low_prices:
                pd = _get_pd()
                high_series = pd.Series(high_prices)
                low_series = pd.Series(low_prices)
                results['atr'] = calculate_atr(high_series, low_series, close_series, self.config.atr_period)

            # Calculate VWAP if volume data available
            if volume and high_prices and low_prices:
                pd = _get_pd()
                high_series = pd.Series(high_prices)
                low_series = pd.Series(low_prices)
                volume_series = pd.Series(volume)
                results['vwap'] = calculate_vwap(high_series, low_series, close_series, volume_series)
            
            # Add configuration used
            results['config'] = self.config.to_dict()
            
            return results
            
        except Exception as e:
            self.logger.error(f"Full analysis error: {e}")
            return {}
    
    def get_signals(self, data: Dict[str, Any]) -> Dict[str, str]:
        """
        Generate trading signals based on technical indicators.
        
        Returns:
            Dictionary of signal types and their values (BUY/SELL/HOLD)
        """
        try:
            analysis = self.full_analysis(data)
            signals = {}
            
            # RSI signals
            rsi = analysis.get('rsi')
            if rsi is not None:
                if rsi > self.config.rsi_overbought:
                    signals['rsi'] = 'SELL'
                elif rsi < self.config.rsi_oversold:
                    signals['rsi'] = 'BUY'
                else:
                    signals['rsi'] = 'HOLD'
            
            # MACD signals
            macd = analysis.get('macd')
            if macd and isinstance(macd, dict):
                macd_line = macd.get('macd', 0)
                signal_line = macd.get('signal', 0)
                if macd_line > signal_line:
                    signals['macd'] = 'BUY'
                elif macd_line < signal_line:
                    signals['macd'] = 'SELL'
                else:
                    signals['macd'] = 'HOLD'
            
            # Moving average signals
            sma_20 = analysis.get('sma_20')
            sma_50 = analysis.get('sma_50')
            if sma_20 is not None and sma_50 is not None:
                if sma_20 > sma_50:
                    signals['ma_trend'] = 'BUY'
                elif sma_20 < sma_50:
                    signals['ma_trend'] = 'SELL'
                else:
                    signals['ma_trend'] = 'HOLD'
            
            return signals
            
        except Exception as e:
            self.logger.error(f"Signal generation error: {e}")
            return {}


# Backward compatibility class (matches old src/analysis/technical/indicators.py interface)
class TechnicalIndicatorsCalculator(UnifiedTechnicalAnalyzer):
    """
    Backward compatibility class for src/analysis/technical/indicators.py
    """
    
    def __init__(self):
        """Initialize with default configuration"""
        super().__init__()
        self.logger.info("TechnicalIndicatorsCalculator initialized (compatibility mode)")


# Global instance for easy access
unified_analyzer = UnifiedTechnicalAnalyzer()
