"""
AI Chat Processor wrapper for backward compatibility.
"""
import logging
from typing import Dict, Any, Optional
from .ai_processor_robust import QueryResult

from src.shared.error_handling.logging import get_logger
logger = get_logger(__name__)

class AIChatProcessorWrapper:
    """
    Fixed AI Chat Processor wrapper without circular imports.
    """
    
    def __init__(self, context: Optional[Any] = None):
        """
        Initialize the AI Chat Processor wrapper.
        
        Args:
            context: Optional context object with pipeline information
        """
        self.context = context
        self.pipeline_id = getattr(context, 'pipeline_id', 'unknown') if context else 'unknown'
        self.logger = logging.getLogger(__name__)
        
        # Initialize components directly to avoid circular imports
        self._initialize_components()
        
        self.logger.info(f"Initialized AIChatProcessorWrapper for pipeline: {self.pipeline_id}")
    
    def _initialize_components(self):
        """Initialize components without circular imports."""
        try:
            # Use the robust financial analyzer directly (no wrapper layers)
            from src.shared.ai_services.unified_ai_processor import UnifiedAIProcessor as RobustFinancialAnalyzer
            self.processor = RobustFinancialAnalyzer()
        except ImportError as e:
            self.logger.error(f"Could not import RobustFinancialAnalyzer: {e}")
            self.processor = None

    async def analyze_query_intent(self, query: str) -> Optional[QueryResult]:
        """Analyze query intent using the robust financial analyzer."""
        if not self.processor:
            self.logger.warning("No processor available for query intent analysis.")
            return None

        try:
            # Use the robust analyzer to answer the general question
            response = await self.processor.answer_general_question(query)

            # Create a QueryResult for backward compatibility
            return QueryResult(
                intent="general_question",
                symbols=[],
                needs_data=False,
                response=response
            )
        except Exception as e:
            self.logger.error(f"Error analyzing query intent: {e}")
            return None

    async def process_query(self, query: str, user_id: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """
        Process a user query - backward compatible method.
        
        Args:
            query: The user's query string
            user_id: Optional user ID for context
            **kwargs: Additional arguments like 'initial_analysis'
            
        Returns:
            Dictionary with processed response
        """
        if not self.processor:
            return {
                'response': 'AI service temporarily unavailable. Please try again later.',
                'status': 'error',
                'data_quality': 0
            }
        
        try:
            # Check if we have market data in kwargs
            market_data = kwargs.get('initial_analysis', {}).get('market_data')

            if market_data:
                # Use market data analysis
                response = await self.processor.analyze_market_data(market_data)
                intent = "stock_analysis"
                symbol = market_data.get('symbol')
            else:
                # Use general question answering
                response = await self.processor.answer_general_question(query)
                intent = "general_question"
                symbol = None

            return {
                'response': response,
                'status': 'success',
                'data_quality': 85,  # High quality since using robust analyzer
                'intent': intent,
                'symbol': symbol
            }
        except Exception as e:
            self.logger.error(f"Error processing query: {e}")
            return {
                'response': 'Error processing your request. Please try again.',
                'status': 'error',
                'data_quality': 0
            }
    

    async def process(self, query: str, **kwargs) -> Dict[str, Any]:
        """Main processing method."""
        return await self.process_query(query, **kwargs)
    
    def _generate_final_response(self, ai_response: Dict[str, Any]) -> str:
        """Legacy method alias - now handled by ResponseFormatter."""
        logger.warning("_generate_final_response is deprecated. Use process() method instead.")
        return ai_response

# Create a global instance for backward compatibility
processor = AIChatProcessorWrapper()

# Export the main class and instance
__all__ = ['AIChatProcessorWrapper', 'processor', 'create_processor']


def create_processor(context: Optional[Any] = None) -> AIChatProcessorWrapper:
    """
    Factory function to create a new processor instance.
    
    Args:
        context: Optional context object
        
    Returns:
        AIChatProcessorWrapper instance
    """
    return AIChatProcessorWrapper(context)

# Export the factory function
__all__.append('create_processor')
