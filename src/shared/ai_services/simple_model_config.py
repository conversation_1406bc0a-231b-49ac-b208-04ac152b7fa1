"""
Simple AI Model Configuration
Clean, centralized model configuration without over-engineering.
"""

import os
import yaml
import logging
from typing import Dict, Any, Optional
from pathlib import Path
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class ModelConfig:
    """Simple model configuration."""
    model_id: str
    description: str
    max_tokens: int
    temperature: float
    timeout_seconds: int
    jobs: list

class SimpleModelConfig:
    """
    Simple, clean model configuration manager.
    No over-engineering, just what we need.
    """
    
    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path or self._get_default_config_path()
        self.config = None
        self.models = {}
        self.job_routing = {}
        self._load_config()
    
    def _get_default_config_path(self) -> str:
        """Get the default config file path."""
        # Try multiple locations
        possible_paths = [
            '/app/config/ai_models_simple.yaml',  # Docker
            'config/ai_models_simple.yaml',       # Relative
            Path(__file__).parent.parent.parent.parent / 'config' / 'ai_models_simple.yaml'  # Absolute
        ]
        
        for path in possible_paths:
            if Path(path).exists():
                return str(path)
        
        # Fallback
        return 'config/ai_models_simple.yaml'
    
    def _load_config(self):
        """Load configuration from YAML file."""
        try:
            with open(self.config_path, 'r') as f:
                self.config = yaml.safe_load(f)
            
            self._load_models()
            self._load_job_routing()
            
            logger.info(f"✅ Loaded simple AI config from {self.config_path}")
            
        except Exception as e:
            logger.error(f"❌ Failed to load AI config: {e}")
            self._load_fallback_config()
    
    def _load_models(self):
        """Load model configurations."""
        models_config = self.config.get('models', {})
        environment = os.getenv('ENVIRONMENT', 'development')
        
        # Apply environment overrides
        env_overrides = self.config.get('environments', {}).get(environment, {})
        
        for model_name, model_data in models_config.items():
            # Apply environment override if exists
            if model_name in env_overrides:
                model_data.update(env_overrides[model_name])
            
            self.models[model_name] = ModelConfig(
                model_id=model_data.get('model_id'),
                description=model_data.get('description', ''),
                max_tokens=model_data.get('max_tokens', 2000),
                temperature=model_data.get('temperature', 0.3),
                timeout_seconds=model_data.get('timeout_seconds', 30),
                jobs=model_data.get('jobs', [])
            )
    
    def _load_job_routing(self):
        """Load job to model routing."""
        self.job_routing = self.config.get('job_routing', {})
    
    def _load_fallback_config(self):
        """Load minimal fallback configuration."""
        logger.warning("Using fallback AI configuration")
        
        fallback_model = os.getenv('AI_MODEL_FALLBACK', 'gpt-4o-mini')
        
        self.models = {
            'quick': ModelConfig(
                model_id=fallback_model,
                description='Fallback model',
                max_tokens=2000,
                temperature=0.3,
                timeout_seconds=30,
                jobs=['all']
            )
        }
        
        self.job_routing = {
            'symbol_extraction': 'quick',
            'intent_classification': 'quick',
            'market_analysis': 'quick',
            'technical_analysis': 'quick',
            'risk_assessment': 'quick',
            'user_explanations': 'quick',
            'fallback': 'quick'
        }
    
    def get_model_for_job(self, job: str) -> ModelConfig:
        """Get the appropriate model for a specific job."""
        model_name = self.job_routing.get(job, 'fallback')
        model = self.models.get(model_name)
        
        if not model:
            logger.warning(f"Model '{model_name}' not found for job '{job}', using fallback")
            model = self.models.get('fallback') or list(self.models.values())[0]
        
        return model
    
    def get_model_by_name(self, name: str) -> Optional[ModelConfig]:
        """Get a model by name."""
        return self.models.get(name)
    
    def get_all_models(self) -> Dict[str, ModelConfig]:
        """Get all available models."""
        return self.models.copy()
    
    def get_provider_config(self) -> Dict[str, Any]:
        """Get provider configuration."""
        return self.config.get('provider', {
            'base_url': 'https://openrouter.ai/api/v1',
            'timeout_seconds': 30,
            'max_retries': 3
        })

# Global instance
_config_instance = None

def get_model_config() -> SimpleModelConfig:
    """Get the global model configuration instance."""
    global _config_instance
    if _config_instance is None:
        _config_instance = SimpleModelConfig()
    return _config_instance

def get_model_for_job(job: str) -> ModelConfig:
    """Convenience function to get model for a job."""
    return get_model_config().get_model_for_job(job)

def get_model_id_for_job(job: str) -> str:
    """Convenience function to get model ID for a job."""
    return get_model_for_job(job).model_id