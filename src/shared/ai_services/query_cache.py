"""
Simple Query Cache
Provides caching for simple price queries to reduce AI service load
"""

import asyncio
import time
import json
import logging
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

@dataclass
class CachedQuery:
    """Represents a cached query result"""
    query: str
    response: str
    user_id: str
    timestamp: float
    ttl: int  # Time to live in seconds
    symbols: list
    intent: str
    
    def is_expired(self) -> bool:
        """Check if the cached query has expired"""
        return time.time() > (self.timestamp + self.ttl)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return asdict(self)

class SimpleQueryCache:
    """Cache for simple queries to reduce AI service load"""
    
    def __init__(self, max_size: int = 1000):
        self.max_size = max_size
        self.cache: Dict[str, CachedQuery] = {}
        self.access_order: list = []
        self.lock = asyncio.Lock()
        self.hits = 0
        self.misses = 0
        
    def _generate_cache_key(self, query: str, user_id: str) -> str:
        """Generate a cache key from query and user ID"""
        # Normalize query for better cache hits
        normalized_query = query.lower().strip()
        return f"{normalized_query}:{user_id}"
    
    async def get(self, query: str, user_id: str) -> Optional[str]:
        """Get cached response for a query"""
        async with self.lock:
            cache_key = self._generate_cache_key(query, user_id)
            
            if cache_key in self.cache:
                cached_entry = self.cache[cache_key]
                
                # Check if expired
                if cached_entry.is_expired():
                    del self.cache[cache_key]
                    if cache_key in self.access_order:
                        self.access_order.remove(cache_key)
                    self.misses += 1
                    return None
                
                # Update access order (LRU)
                if cache_key in self.access_order:
                    self.access_order.remove(cache_key)
                self.access_order.append(cache_key)
                
                self.hits += 1
                logger.debug(f"Cache HIT for query: {query[:50]}...")
                return cached_entry.response
            
            self.misses += 1
            logger.debug(f"Cache MISS for query: {query[:50]}...")
            return None
    
    async def set(self, query: str, response: str, user_id: str, 
                  ttl: int = 300, symbols: list = None, intent: str = "price_check") -> bool:
        """Set cached response for a query"""
        async with self.lock:
            # Evict oldest entries if cache is full
            if len(self.cache) >= self.max_size:
                await self._evict_oldest()
            
            cache_key = self._generate_cache_key(query, user_id)
            cached_entry = CachedQuery(
                query=query,
                response=response,
                user_id=user_id,
                timestamp=time.time(),
                ttl=ttl,
                symbols=symbols or [],
                intent=intent
            )
            
            self.cache[cache_key] = cached_entry
            
            # Update access order (LRU)
            if cache_key in self.access_order:
                self.access_order.remove(cache_key)
            self.access_order.append(cache_key)
            
            logger.debug(f"Cache SET for query: {query[:50]}...")
            return True
    
    async def _evict_oldest(self) -> None:
        """Evict the oldest cached entries"""
        if self.access_order:
            oldest_key = self.access_order.pop(0)
            if oldest_key in self.cache:
                del self.cache[oldest_key]
                logger.debug(f"Evicted oldest cache entry: {oldest_key}")
    
    async def clear_expired(self) -> int:
        """Clear expired cache entries and return count"""
        async with self.lock:
            expired_keys = [
                key for key, entry in self.cache.items()
                if entry.is_expired()
            ]
            
            for key in expired_keys:
                del self.cache[key]
                if key in self.access_order:
                    self.access_order.remove(key)
            
            if expired_keys:
                logger.debug(f"Cleared {len(expired_keys)} expired cache entries")
            
            return len(expired_keys)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        total_requests = self.hits + self.misses
        hit_rate = 0.0
        if total_requests > 0:
            hit_rate = self.hits / total_requests
            
        return {
            "size": len(self.cache),
            "max_size": self.max_size,
            "hits": self.hits,
            "misses": self.misses,
            "hit_rate": hit_rate,
            "total_requests": total_requests
        }

# Global cache instance
simple_query_cache = SimpleQueryCache(max_size=1000)

# Convenience functions
async def get_cached_response(query: str, user_id: str) -> Optional[str]:
    """Get cached response for a query"""
    return await simple_query_cache.get(query, user_id)

async def cache_response(query: str, response: str, user_id: str, 
                        ttl: int = 300, symbols: list = None, intent: str = "price_check") -> bool:
    """Cache a response"""
    return await simple_query_cache.set(query, response, user_id, ttl, symbols, intent)

async def clear_expired_cache() -> int:
    """Clear expired cache entries"""
    return await simple_query_cache.clear_expired()

def get_cache_stats() -> Dict[str, Any]:
    """Get cache statistics"""
    return simple_query_cache.get_stats()