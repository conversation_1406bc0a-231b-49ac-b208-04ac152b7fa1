"""
Response Synthesizer

This module provides intelligent response synthesis for different types of financial queries,
creating conversational, contextual, and informative responses.
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
import json

logger = logging.getLogger(__name__)

class ResponseSynthesizer:
    """
    Intelligent response synthesizer that creates contextual, conversational responses
    for different types of financial queries.
    """
    
    def __init__(self):
        """Initialize the response synthesizer."""
        self.logger = logger
        
        # Response templates for different query types
        self.response_templates = {
            "price_check": {
                "header": "💰 **Current Price Information**",
                "format": "**{symbol}**: ${price:.2f}",
                "footer": "Price data as of {timestamp}"
            },
            "technical_analysis": {
                "header": "📊 **Technical Analysis**",
                "sections": [
                    "**Price Action**: {price_summary}",
                    "**Key Indicators**: {indicators}",
                    "**Trend Analysis**: {trend}",
                    "**Support/Resistance**: {levels}"
                ],
                "footer": "Technical analysis based on {timeframe} data"
            },
            "options_analysis": {
                "header": "📈 **Options Analysis**",
                "sections": [
                    "**Current Options**: {options_summary}",
                    "**Greeks**: {greeks}",
                    "**Volatility**: {volatility}",
                    "**Recommendations**: {recommendations}"
                ],
                "footer": "Options analysis for {symbol}"
            },
            "comparison": {
                "header": "⚖️ **Comparison Analysis**",
                "sections": [
                    "**Symbols Compared**: {symbols}",
                    "**Key Metrics**: {metrics}",
                    "**Performance**: {performance}",
                    "**Recommendation**: {recommendation}"
                ],
                "footer": "Comparative analysis completed"
            },
            "recommendation": {
                "header": "💡 **Trading Recommendations**",
                "sections": [
                    "**Analysis Summary**: {summary}",
                    "**Key Insights**: {insights}",
                    "**Recommendations**: {recommendations}",
                    "**Risk Factors**: {risks}"
                ],
                "footer": "Recommendations based on current market conditions"
            }
        }
    
    def synthesize_response(self, 
                          query: str, 
                          query_type: str, 
                          complexity: str,
                          analysis_data: Dict[str, Any],
                          symbols: List[str],
                          user_context: Optional[Dict[str, Any]] = None) -> str:
        """
        Synthesize a comprehensive response based on query analysis and data.
        
        Args:
            query: Original user query
            query_type: Type of query (price_check, technical_analysis, etc.)
            complexity: Query complexity level
            analysis_data: Analysis results from processing steps
            symbols: List of symbols analyzed
            user_context: Optional user context information
            
        Returns:
            Formatted response string
        """
        try:
            # Start building the response
            response_parts = []
            
            # Add header based on query type
            if query_type in self.response_templates:
                template = self.response_templates[query_type]
                response_parts.append(template["header"])
            else:
                response_parts.append("📊 **Financial Analysis**")
            
            # Add symbol information
            if symbols:
                symbol_list = ", ".join(symbols)
                response_parts.append(f"**Symbols**: {symbol_list}")
            
            # Add main content based on query type
            if query_type == "price_check":
                response_parts.extend(self._synthesize_price_response(analysis_data, symbols))
            elif query_type == "technical_analysis":
                response_parts.extend(self._synthesize_technical_response(analysis_data, symbols))
            elif query_type == "options_analysis":
                response_parts.extend(self._synthesize_options_response(analysis_data, symbols))
            elif query_type == "comparison":
                response_parts.extend(self._synthesize_comparison_response(analysis_data, symbols))
            elif query_type == "recommendation":
                response_parts.extend(self._synthesize_recommendation_response(analysis_data, symbols))
            else:
                response_parts.extend(self._synthesize_general_response(analysis_data, symbols))
            
            # Add AI-generated insights if available
            if "recommendations" in analysis_data:
                response_parts.append(f"\n💡 **AI Insights:**\n{analysis_data['recommendations']}")
            
            # Add risk warning
            response_parts.append(self._add_risk_warning())
            
            # Add timestamp
            response_parts.append(f"\n⏰ *Analysis completed at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*")
            
            return "\n\n".join(response_parts)
            
        except Exception as e:
            self.logger.error(f"Error synthesizing response: {e}")
            return f"I apologize, but I encountered an error while generating the response: {str(e)}"
    
    def _synthesize_price_response(self, analysis_data: Dict[str, Any], symbols: List[str]) -> List[str]:
        """Synthesize response for price check queries."""
        parts = []
        
        # Add current prices
        market_data = analysis_data.get('market_data', {})
        for symbol in symbols:
            if symbol in market_data and 'error' not in market_data[symbol]:
                try:
                    price_data = market_data[symbol]
                    if isinstance(price_data, dict) and 'close' in price_data:
                        current_price = price_data['close'][-1] if isinstance(price_data['close'], list) else price_data['close']
                        parts.append(f"💰 **{symbol}**: ${current_price:.2f}")
                except:
                    parts.append(f"💰 **{symbol}**: Price data unavailable")
        
        return parts
    
    def _synthesize_technical_response(self, analysis_data: Dict[str, Any], symbols: List[str]) -> List[str]:
        """Synthesize response for technical analysis queries."""
        parts = []
        
        # Add technical analysis summary
        technical_analysis = analysis_data.get('technical_analysis', {})
        for symbol in symbols:
            if symbol in technical_analysis and 'error' not in technical_analysis[symbol]:
                parts.append(f"🔍 **{symbol} Technical Analysis:**")
                parts.append("• Technical indicators calculated")
                parts.append("• Trend analysis completed")
                parts.append("• Support/resistance levels identified")
        
        return parts
    
    def _synthesize_options_response(self, analysis_data: Dict[str, Any], symbols: List[str]) -> List[str]:
        """Synthesize response for options analysis queries."""
        parts = []
        
        # Add options analysis summary
        options_analysis = analysis_data.get('options_analysis', {})
        if options_analysis and options_analysis != "Options analysis not yet implemented":
            parts.append("📈 **Options Analysis:**")
            parts.append("• Options data retrieved")
            parts.append("• Greeks calculated")
            parts.append("• Volatility analysis completed")
        else:
            parts.append("📈 **Options Analysis:**")
            parts.append("• Options analysis feature coming soon")
            parts.append("• Currently analyzing underlying stock data")
        
        return parts
    
    def _synthesize_comparison_response(self, analysis_data: Dict[str, Any], symbols: List[str]) -> List[str]:
        """Synthesize response for comparison queries."""
        parts = []
        
        if len(symbols) > 1:
            parts.append(f"⚖️ **Comparing {', '.join(symbols)}:**")
            parts.append("• Comparative metrics calculated")
            parts.append("• Performance analysis completed")
            parts.append("• Relative strength analysis done")
        else:
            parts.append("⚖️ **Comparison Analysis:**")
            parts.append("• Single symbol analysis completed")
            parts.append("• Market comparison available upon request")
        
        return parts
    
    def _synthesize_recommendation_response(self, analysis_data: Dict[str, Any], symbols: List[str]) -> List[str]:
        """Synthesize response for recommendation queries."""
        parts = []
        
        parts.append("💡 **Trading Recommendations:**")
        parts.append("• Analysis completed")
        parts.append("• Risk factors assessed")
        parts.append("• Recommendations generated")
        
        return parts
    
    def _synthesize_general_response(self, analysis_data: Dict[str, Any], symbols: List[str]) -> List[str]:
        """Synthesize response for general queries."""
        parts = []
        
        parts.append("📊 **General Analysis:**")
        parts.append("• Query processed successfully")
        parts.append("• Relevant data gathered")
        parts.append("• Analysis completed")
        
        return parts
    
    def _add_risk_warning(self) -> str:
        """Add standard risk warning to responses."""
        return """⚠️ **Important Disclaimer:**
This analysis is for educational purposes only and does not constitute financial advice. 
Past performance doesn't guarantee future results. Always do your own research and 
consult with a financial advisor before making investment decisions."""
    
    def create_conversational_response(self, 
                                     query: str, 
                                     analysis_data: Dict[str, Any],
                                     symbols: List[str],
                                     user_context: Optional[Dict[str, Any]] = None) -> str:
        """
        Create a more conversational, chat-like response.
        
        Args:
            query: Original user query
            analysis_data: Analysis results
            symbols: List of symbols
            user_context: Optional user context
            
        Returns:
            Conversational response string
        """
        try:
            # Start with a conversational greeting
            greeting = self._get_conversational_greeting(query, symbols)
            
            # Add main analysis content
            main_content = self._get_conversational_content(analysis_data, symbols)
            
            # Add follow-up suggestions
            follow_up = self._get_follow_up_suggestions(query, symbols)
            
            # Combine all parts
            response_parts = [greeting, main_content, follow_up, self._add_risk_warning()]
            
            return "\n\n".join(response_parts)
            
        except Exception as e:
            self.logger.error(f"Error creating conversational response: {e}")
            return f"Hey! I ran into an issue while analyzing your query. Let me try again or you can rephrase your question."
    
    def _get_conversational_greeting(self, query: str, symbols: List[str]) -> str:
        """Get a conversational greeting based on the query."""
        if symbols:
            symbol_list = ", ".join(symbols)
            return f"Hey! I've analyzed {symbol_list} for you. Here's what I found:"
        else:
            return "Hey! I've processed your query. Here's what I discovered:"
    
    def _get_conversational_content(self, analysis_data: Dict[str, Any], symbols: List[str]) -> str:
        """Get conversational content based on analysis data."""
        content_parts = []
        
        # Add price information
        market_data = analysis_data.get('market_data', {})
        for symbol in symbols:
            if symbol in market_data and 'error' not in market_data[symbol]:
                try:
                    price_data = market_data[symbol]
                    if isinstance(price_data, dict) and 'close' in price_data:
                        current_price = price_data['close'][-1] if isinstance(price_data['close'], list) else price_data['close']
                        content_parts.append(f"💰 **{symbol}** is currently trading at **${current_price:.2f}**")
                except:
                    content_parts.append(f"💰 **{symbol}** price data is currently unavailable")
        
        # Add analysis summary
        if analysis_data.get('technical_analysis'):
            content_parts.append("📊 I've run a technical analysis and calculated key indicators")
        
        if analysis_data.get('recommendations'):
            content_parts.append(f"💡 Here are my insights: {analysis_data['recommendations']}")
        
        return "\n".join(content_parts) if content_parts else "I've completed the analysis for you."
    
    def _get_follow_up_suggestions(self, query: str, symbols: List[str]) -> str:
        """Get follow-up suggestions based on the query."""
        suggestions = []
        
        if symbols:
            symbol_list = ", ".join(symbols)
            suggestions.append(f"Want to dive deeper into {symbol_list}? Try asking about:")
            suggestions.append("• Technical indicators")
            suggestions.append("• Price targets")
            suggestions.append("• Risk analysis")
        
        suggestions.append("Need help with something else? I can analyze other stocks, compare symbols, or help with portfolio questions!")
        
        return "\n".join(suggestions)