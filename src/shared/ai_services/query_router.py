"""
Intelligent Query Router

This module provides intelligent routing for different types of financial queries,
ensuring each query is processed by the most appropriate handler.
"""

import re
import logging
from typing import Dict, List, Any, Optional, Tuple
from enum import Enum
from dataclasses import dataclass

logger = logging.getLogger(__name__)

class QueryType(Enum):
    """Types of financial queries"""
    PRICE_CHECK = "price_check"
    TECHNICAL_ANALYSIS = "technical_analysis"
    FUNDAMENTAL_ANALYSIS = "fundamental_analysis"
    OPTIONS_ANALYSIS = "options_analysis"
    PORTFOLIO_ANALYSIS = "portfolio_analysis"
    COMPARISON = "comparison"
    RECOMMENDATION = "recommendation"
    NEWS_ANALYSIS = "news_analysis"
    RISK_ASSESSMENT = "risk_assessment"
    GENERAL_QUESTION = "general_question"

class QueryComplexity(Enum):
    """Query complexity levels"""
    SIMPLE = "simple"
    MODERATE = "moderate"
    COMPLEX = "complex"
    ADVANCED = "advanced"

@dataclass
class QueryContext:
    """Context information for a query"""
    symbols: List[str]
    timeframes: List[str]
    indicators: List[str]
    options_terms: List[str]
    urgency: str
    user_experience: str
    previous_queries: List[str]

class QueryRouter:
    """
    Intelligent query router that determines the best processing path
    for different types of financial queries.
    """
    
    def __init__(self):
        """Initialize the query router."""
        self.logger = logger
        
        # Define query patterns and their handlers
        self.query_patterns = {
            QueryType.PRICE_CHECK: [
                r"price of",
                r"current price",
                r"how much is",
                r"what's the price",
                r"show me the price"
            ],
            QueryType.TECHNICAL_ANALYSIS: [
                r"analyze",
                r"technical analysis",
                r"chart",
                r"indicators",
                r"trend",
                r"support",
                r"resistance",
                r"moving average",
                r"rsi",
                r"macd"
            ],
            QueryType.OPTIONS_ANALYSIS: [
                r"options",
                r"calls?",
                r"puts?",
                r"strike",
                r"expiration",
                r"greeks",
                r"implied volatility",
                r"option chain"
            ],
            QueryType.PORTFOLIO_ANALYSIS: [
                r"portfolio",
                r"diversification",
                r"allocation",
                r"rebalance",
                r"holdings"
            ],
            QueryType.COMPARISON: [
                r"compare",
                r"vs",
                r"versus",
                r"better",
                r"which is",
                r"difference between"
            ],
            QueryType.RECOMMENDATION: [
                r"should i",
                r"recommend",
                r"advice",
                r"opinion",
                r"what do you think",
                r"buy or sell"
            ],
            QueryType.RISK_ASSESSMENT: [
                r"risk",
                r"volatility",
                r"downside",
                r"safe",
                r"dangerous"
            ]
        }
        
        # Define complexity indicators
        self.complexity_indicators = {
            QueryComplexity.SIMPLE: [
                "price", "current", "what is", "how much"
            ],
            QueryComplexity.MODERATE: [
                "analyze", "compare", "should i", "recommend"
            ],
            QueryComplexity.COMPLEX: [
                "strategy", "portfolio", "diversification", "timeline"
            ],
            QueryComplexity.ADVANCED: [
                "options", "greeks", "volatility", "hedging"
            ]
        }
    
    def route_query(self, query: str, context: Optional[QueryContext] = None) -> Dict[str, Any]:
        """
        Route a query to the appropriate handler.
        
        Args:
            query: The user's query
            context: Optional context information
            
        Returns:
            Routing information including query type, complexity, and processing steps
        """
        try:
            # Extract basic information
            symbols = self._extract_symbols(query)
            query_type = self._classify_query_type(query)
            complexity = self._assess_complexity(query, symbols)
            
            # Determine processing steps
            processing_steps = self._determine_processing_steps(query_type, complexity, symbols)
            
            # Calculate priority
            priority = self._calculate_priority(query_type, complexity, context)
            
            # Determine response style
            response_style = self._determine_response_style(complexity, context)
            
            return {
                "query_type": query_type,
                "complexity": complexity,
                "symbols": symbols,
                "processing_steps": processing_steps,
                "priority": priority,
                "response_style": response_style,
                "estimated_duration": self._estimate_duration(processing_steps),
                "requires_data": self._requires_market_data(query_type, symbols),
                "requires_ai": self._requires_ai_reasoning(query_type, complexity)
            }
            
        except Exception as e:
            self.logger.error(f"Error routing query: {e}")
            return {
                "query_type": QueryType.GENERAL_QUESTION,
                "complexity": QueryComplexity.SIMPLE,
                "symbols": [],
                "processing_steps": ["general_response"],
                "priority": 1,
                "response_style": "conversational",
                "estimated_duration": 1.0,
                "requires_data": False,
                "requires_ai": True,
                "error": str(e)
            }
    
    def _extract_symbols(self, query: str) -> List[str]:
        """Extract stock symbols from the query."""
        # Pattern for $SYMBOL format
        symbol_pattern = r'\$([A-Z]{1,5})'
        symbols = re.findall(symbol_pattern, query)
        
        # Also look for symbols without $ prefix
        word_pattern = r'\b([A-Z]{1,5})\b'
        potential_symbols = re.findall(word_pattern, query)
        
        # Filter out common words that aren't symbols
        common_words = {'THE', 'AND', 'OR', 'FOR', 'WITH', 'FROM', 'THIS', 'THAT', 'WHAT', 'WHEN', 'WHERE', 'WHY', 'HOW'}
        symbols.extend([s for s in potential_symbols if s not in common_words])
        
        return list(set(symbols))  # Remove duplicates
    
    def _classify_query_type(self, query: str) -> QueryType:
        """Classify the type of query based on keywords and patterns."""
        query_lower = query.lower()
        
        # Check each query type pattern
        for query_type, patterns in self.query_patterns.items():
            for pattern in patterns:
                if re.search(pattern, query_lower):
                    return query_type
        
        # Default to general question if no specific type is detected
        return QueryType.GENERAL_QUESTION
    
    def _assess_complexity(self, query: str, symbols: List[str]) -> QueryComplexity:
        """Assess the complexity of the query."""
        query_lower = query.lower()
        
        # Count complexity indicators
        scores = {}
        for complexity, indicators in self.complexity_indicators.items():
            scores[complexity] = sum(1 for indicator in indicators if indicator in query_lower)
        
        # Determine base complexity
        base_complexity = max(scores, key=scores.get)
        
        # Adjust based on additional factors
        if len(symbols) > 1:
            if base_complexity == QueryComplexity.SIMPLE:
                base_complexity = QueryComplexity.MODERATE
            elif base_complexity == QueryComplexity.MODERATE:
                base_complexity = QueryComplexity.COMPLEX
        
        # Check for multi-step indicators
        multi_step_indicators = ["and", "also", "then", "next", "after", "followed by"]
        if any(indicator in query_lower for indicator in multi_step_indicators):
            if base_complexity == QueryComplexity.SIMPLE:
                base_complexity = QueryComplexity.MODERATE
            elif base_complexity == QueryComplexity.MODERATE:
                base_complexity = QueryComplexity.COMPLEX
        
        # Check for advanced financial terms
        advanced_terms = ["greeks", "volatility", "hedging", "arbitrage", "derivatives"]
        if any(term in query_lower for term in advanced_terms):
            base_complexity = QueryComplexity.ADVANCED
        
        return base_complexity
    
    def _determine_processing_steps(self, query_type: QueryType, complexity: QueryComplexity, symbols: List[str]) -> List[str]:
        """Determine the required processing steps based on query analysis."""
        steps = []
        
        # Always start with basic steps
        steps.extend(["symbol_validation", "intent_analysis"])
        
        # Add data gathering if symbols are present
        if symbols:
            steps.append("data_gathering")
        
        # Add analysis steps based on query type
        if query_type == QueryType.TECHNICAL_ANALYSIS:
            steps.extend(["technical_indicators", "chart_analysis", "trend_analysis"])
        
        elif query_type == QueryType.OPTIONS_ANALYSIS:
            steps.extend(["options_data", "greeks_calculation", "volatility_analysis"])
        
        elif query_type == QueryType.PORTFOLIO_ANALYSIS:
            steps.extend(["portfolio_data", "allocation_analysis", "risk_metrics"])
        
        elif query_type == QueryType.COMPARISON:
            steps.extend(["multi_symbol_analysis", "comparative_metrics"])
        
        # Add complexity-based steps
        if complexity in [QueryComplexity.COMPLEX, QueryComplexity.ADVANCED]:
            steps.extend(["risk_assessment", "scenario_analysis"])
        
        if complexity == QueryComplexity.ADVANCED:
            steps.extend(["advanced_analytics", "machine_learning_insights"])
        
        # Always end with response generation
        steps.extend(["recommendation_generation", "response_synthesis"])
        
        return steps
    
    def _calculate_priority(self, query_type: QueryType, complexity: QueryComplexity, context: Optional[QueryContext]) -> int:
        """Calculate the priority of the query (1-10, higher is more urgent)."""
        priority = 5  # Base priority
        
        # Adjust based on query type
        type_priorities = {
            QueryType.PRICE_CHECK: 3,
            QueryType.TECHNICAL_ANALYSIS: 6,
            QueryType.OPTIONS_ANALYSIS: 7,
            QueryType.PORTFOLIO_ANALYSIS: 8,
            QueryType.RISK_ASSESSMENT: 9,
            QueryType.RECOMMENDATION: 8
        }
        
        if query_type in type_priorities:
            priority = type_priorities[query_type]
        
        # Adjust based on complexity
        complexity_adjustments = {
            QueryComplexity.SIMPLE: -1,
            QueryComplexity.MODERATE: 0,
            QueryComplexity.COMPLEX: 1,
            QueryComplexity.ADVANCED: 2
        }
        
        priority += complexity_adjustments.get(complexity, 0)
        
        # Adjust based on context
        if context:
            if context.urgency == "high":
                priority += 2
            elif context.urgency == "low":
                priority -= 1
        
        return max(1, min(10, priority))  # Clamp between 1 and 10
    
    def _determine_response_style(self, complexity: QueryComplexity, context: Optional[QueryContext]) -> str:
        """Determine the appropriate response style."""
        if complexity == QueryComplexity.SIMPLE:
            return "concise"
        elif complexity == QueryComplexity.MODERATE:
            return "detailed"
        elif complexity == QueryComplexity.COMPLEX:
            return "comprehensive"
        else:  # ADVANCED
            return "expert"
    
    def _estimate_duration(self, processing_steps: List[str]) -> float:
        """Estimate the duration of processing in seconds."""
        step_durations = {
            "symbol_validation": 0.5,
            "intent_analysis": 1.0,
            "data_gathering": 2.0,
            "technical_indicators": 2.0,
            "chart_analysis": 1.5,
            "trend_analysis": 1.0,
            "options_data": 3.0,
            "greeks_calculation": 2.0,
            "volatility_analysis": 1.5,
            "portfolio_data": 2.5,
            "allocation_analysis": 2.0,
            "risk_metrics": 1.5,
            "multi_symbol_analysis": 3.0,
            "comparative_metrics": 2.0,
            "risk_assessment": 2.0,
            "scenario_analysis": 3.0,
            "advanced_analytics": 4.0,
            "machine_learning_insights": 5.0,
            "recommendation_generation": 2.0,
            "response_synthesis": 1.0
        }
        
        total_duration = sum(step_durations.get(step, 1.0) for step in processing_steps)
        return total_duration
    
    def _requires_market_data(self, query_type: QueryType, symbols: List[str]) -> bool:
        """Determine if the query requires market data."""
        data_required_types = {
            QueryType.PRICE_CHECK,
            QueryType.TECHNICAL_ANALYSIS,
            QueryType.OPTIONS_ANALYSIS,
            QueryType.PORTFOLIO_ANALYSIS,
            QueryType.COMPARISON,
            QueryType.RISK_ASSESSMENT
        }
        
        return query_type in data_required_types and len(symbols) > 0
    
    def _requires_ai_reasoning(self, query_type: QueryType, complexity: QueryComplexity) -> bool:
        """Determine if the query requires AI reasoning."""
        ai_required_types = {
            QueryType.RECOMMENDATION,
            QueryType.PORTFOLIO_ANALYSIS,
            QueryType.RISK_ASSESSMENT
        }
        
        return query_type in ai_required_types or complexity in [QueryComplexity.COMPLEX, QueryComplexity.ADVANCED]

# Global instance
query_router = QueryRouter()