"""
Intelligent Text Parser Service

Simplified AI-powered text processing for financial text parsing.
"""

import asyncio
import logging
import re
import time
import json
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
from enum import Enum

from src.shared.error_handling.logging import get_logger

logger = get_logger(__name__)

class ParseType(Enum):
    """Types of parsing operations"""
    SYMBOL_EXTRACTION = "symbol_extraction"
    PRICE_EXTRACTION = "price_extraction"
    PERCENTAGE_EXTRACTION = "percentage_extraction"
    INTENT_DETECTION = "intent_detection"
    SENTIMENT_ANALYSIS = "sentiment_analysis"
    NUMERICAL_EXTRACTION = "numerical_extraction"
    DATE_EXTRACTION = "date_extraction"
    ENTITY_EXTRACTION = "entity_extraction"

@dataclass
class ParseResult:
    """Result of AI-powered parsing"""
    parse_type: ParseType
    extracted_data: List[Dict[str, Any]]
    confidence: float
    raw_text: str
    processing_time: float
    fallback_used: bool = False

@dataclass
class ParsedAlert:
    """Parsed TradingView alert data"""
    symbol: str
    alert_type: str
    signal: str
    timeframe: str
    entry_price: Optional[float] = None
    tp1_price: Optional[float] = None
    tp2_price: Optional[float] = None
    tp3_price: Optional[float] = None
    sl_price: Optional[float] = None
    raw_text: str = ""
    timestamp: float = 0.0

class IntelligentTextParser:
    """
    Simplified AI-powered text parser for financial text processing
    """
    
    def __init__(self):
        # Fallback regex patterns for when AI fails
        self.fallback_patterns = {
            ParseType.SYMBOL_EXTRACTION: [
                r'\$([A-Z]{1,5})\b',
                r'\b([A-Z]{2,5})\b',
                r'([A-Z]{1,5})\s+stock',
                r'ticker\s+([A-Z]{1,5})'
            ],
            ParseType.PRICE_EXTRACTION: [
                r'\$(\d+(?:\.\d{2})?)',
                r'(\d+(?:\.\d+)?)\s*(?:dollars?|USD)',
                r'price.*?(\d+(?:\.\d{2})?)',
                r'(\d+(?:\.\d+)?)\s*%'
            ],
            ParseType.NUMERICAL_EXTRACTION: [
                r'(\d+(?:\.\d+)?)\s*%',
                r'(\d+(?:\.\d+)?)\s*(?:million|billion|trillion)',
                r'(\d+(?:\.\d+)?)\s*(?:K|M|B|T)'
            ]
        }

    async def parse_text(self, text: str, parse_type: ParseType, 
                        use_ai: bool = True, fallback_on_failure: bool = True) -> ParseResult:
        """
        Parse text using AI-powered processing with regex fallback
        """
        start_time = asyncio.get_event_loop().time()
        
        if use_ai:
            try:
                # Try AI-powered parsing first
                result = await self._ai_parse(text, parse_type)
                if result and result.extracted_data:
                    processing_time = asyncio.get_event_loop().time() - start_time
                    result.processing_time = processing_time
                    return result
                
                logger.debug(f"AI parsing returned no results for {parse_type.value}")
                
            except Exception as e:
                logger.warning(f"AI parsing failed for {parse_type.value}: {e}")
        
        # Use regex fallback
        if fallback_on_failure or not use_ai:
            result = await self._regex_fallback(text, parse_type)
            result.fallback_used = True
            processing_time = asyncio.get_event_loop().time() - start_time
            result.processing_time = processing_time
            return result
        
        # Return empty result if both AI and fallback disabled
        processing_time = asyncio.get_event_loop().time() - start_time
        return ParseResult(
            parse_type=parse_type,
            extracted_data=[],
            confidence=0.0,
            raw_text=text,
            processing_time=processing_time,
            fallback_used=False
        )
    
    async def _ai_parse(self, text: str, parse_type: ParseType) -> ParseResult:
        """Perform AI-powered parsing with simplified prompts"""
        # Simplified AI prompts for basic parsing
        prompts = {
            ParseType.SYMBOL_EXTRACTION: f"Extract stock symbols from: {text}",
            ParseType.PRICE_EXTRACTION: f"Extract price values from: {text}",
            ParseType.PERCENTAGE_EXTRACTION: f"Extract percentage values from: {text}",
            ParseType.INTENT_DETECTION: f"Analyze intent of: {text}",
            ParseType.SENTIMENT_ANALYSIS: f"Analyze sentiment of: {text}",
            ParseType.NUMERICAL_EXTRACTION: f"Extract numerical values from: {text}"
        }
        
        prompt = prompts.get(parse_type, f"Extract {parse_type.value} from: {text}")
        
        try:
            # For now, use regex fallback as AI is not available
            logger.debug(f"AI parsing not available, using regex fallback for {parse_type.value}")
            return None
            
        except Exception as e:
            logger.error(f"AI parsing error for {parse_type.value}: {e}")
            return None
    
    async def _regex_fallback(self, text: str, parse_type: ParseType) -> ParseResult:
        """Fallback to regex-based parsing"""
        extracted_data = []
        
        if parse_type in self.fallback_patterns:
            patterns = self.fallback_patterns[parse_type]
            
            for pattern in patterns:
                matches = re.finditer(pattern, text, re.IGNORECASE)
                for match in matches:
                    if parse_type == ParseType.SYMBOL_EXTRACTION:
                        symbol = match.group(1) if match.groups() else match.group(0)
                        extracted_data.append({
                            "symbol": symbol.upper(),
                            "confidence": 0.7,
                            "context": match.group(0),
                            "position": match.span()
                        })
                    
                    elif parse_type == ParseType.PRICE_EXTRACTION:
                        value = match.group(1) if match.groups() else match.group(0)
                        try:
                            numeric_value = float(value.replace('$', '').replace(',', ''))
                            extracted_data.append({
                                "value": numeric_value,
                                "currency": "USD" if '$' in match.group(0) else "unknown",
                                "confidence": 0.6,
                                "context": match.group(0),
                                "position": match.span()
                            })
                        except ValueError:
                            continue
                    
                    elif parse_type == ParseType.NUMERICAL_EXTRACTION:
                        value = match.group(1) if match.groups() else match.group(0)
                        try:
                            numeric_value = float(value)
                            unit = "%" if "%" in match.group(0) else "number"
                            extracted_data.append({
                                "value": numeric_value,
                                "type": "percentage" if unit == "%" else "number",
                                "unit": unit,
                                "confidence": 0.6,
                                "context": match.group(0),
                                "position": match.span()
                            })
                        except ValueError:
                            continue
        
        # Remove duplicates and sort by confidence
        extracted_data = self._deduplicate_results(extracted_data)
        
        # Calculate overall confidence
        confidence = sum(item.get('confidence', 0) for item in extracted_data) / len(extracted_data) if extracted_data else 0.0
        
        return ParseResult(
            parse_type=parse_type,
            extracted_data=extracted_data,
            confidence=confidence,
            raw_text=text,
            processing_time=0.0  # Will be set by caller
        )
    
    def _deduplicate_results(self, results: List[Dict]) -> List[Dict]:
        """Remove duplicate results and keep highest confidence"""
        if not results:
            return results
        
        # Group by key field (symbol, value, etc.)
        groups = {}
        for result in results:
            key = None
            if 'symbol' in result:
                key = result['symbol']
            elif 'value' in result:
                key = str(result['value'])
            elif 'intent' in result:
                key = result['intent']
            
            if key:
                if key not in groups or result.get('confidence', 0) > groups[key].get('confidence', 0):
                    groups[key] = result
        
        return list(groups.values())
    
    # Convenience methods for common parsing tasks
    async def extract_symbols(self, text: str, use_ai: bool = True) -> List[str]:
        """Extract stock symbols from text"""
        result = await self.parse_text(text, ParseType.SYMBOL_EXTRACTION, use_ai)
        return [item.get('symbol', '') for item in result.extracted_data if 'symbol' in item]
    
    async def extract_prices(self, text: str, use_ai: bool = True) -> List[float]:
        """Extract price values from text"""
        result = await self.parse_text(text, ParseType.PRICE_EXTRACTION, use_ai)
        return [item.get('value', 0.0) for item in result.extracted_data if 'value' in item]
    
    async def extract_percentages(self, text: str, use_ai: bool = True) -> List[Dict[str, Any]]:
        """Extract percentage values from text"""
        result = await self.parse_text(text, ParseType.PERCENTAGE_EXTRACTION, use_ai=use_ai, fallback_on_failure=True)
        return result.extracted_data
    
    async def detect_intent(self, text: str, use_ai: bool = True) -> Optional[str]:
        """Detect the intent of the text"""
        result = await self.parse_text(text, ParseType.INTENT_DETECTION, use_ai)
        if result.extracted_data and isinstance(result.extracted_data[0], dict):
            return result.extracted_data[0].get('intent')
        return None
    
    async def analyze_sentiment(self, text: str, use_ai: bool = True) -> Optional[str]:
        """Analyze sentiment of the text"""
        result = await self.parse_text(text, ParseType.SENTIMENT_ANALYSIS, use_ai)
        if result.extracted_data and isinstance(result.extracted_data[0], dict):
            return result.extracted_data[0].get('sentiment')
        return None
    
    async def extract_numbers(self, text: str, use_ai: bool = True) -> List[Dict[str, Any]]:
        """Extract numerical values from text"""
        result = await self.parse_text(text, ParseType.NUMERICAL_EXTRACTION, use_ai)
        return result.extracted_data

class PineScriptAlertParser:
    """Parser specifically for TradingView PineScript alerts"""
    
    def __init__(self):
        self.parser = IntelligentTextParser()
    
    def parse_multiple_alerts(self, text: str) -> List[ParsedAlert]:
        """Parse multiple alerts from text"""
        alerts = []
        
        # Split by common alert separators
        alert_texts = re.split(r'\n\s*---\s*\n|\n\s*###\s*\n', text)
        
        for alert_text in alert_texts:
            if alert_text.strip():
                alert = self.parse_single_alert(alert_text.strip())
                if alert:
                    alerts.append(alert)
        
        return alerts
    
    def parse_single_alert(self, text: str) -> Optional[ParsedAlert]:
        """Parse a single alert from text"""
        try:
            # Extract symbol
            symbols = asyncio.run(self.parser.extract_symbols(text))
            symbol = symbols[0] if symbols else "UNKNOWN"
            
            # Extract prices
            prices = asyncio.run(self.parser.extract_prices(text))
            
            # Determine alert type and signal
            alert_type = "price_alert"
            signal = "neutral"
            
            if "buy" in text.lower() or "long" in text.lower():
                signal = "buy"
            elif "sell" in text.lower() or "short" in text.lower():
                signal = "sell"
            
            # Extract timeframe
            timeframe = "1D"
            if "1h" in text.lower() or "hourly" in text.lower():
                timeframe = "1H"
            elif "4h" in text.lower():
                timeframe = "4H"
            elif "1d" in text.lower() or "daily" in text.lower():
                timeframe = "1D"
            
            # Create parsed alert
            alert = ParsedAlert(
                symbol=symbol,
                alert_type=alert_type,
                signal=signal,
                timeframe=timeframe,
                entry_price=prices[0] if prices else None,
                raw_text=text,
                timestamp=time.time()
            )
            
            return alert
            
        except Exception as e:
            logger.error(f"Failed to parse alert: {e}")
            return None

# Global instances for easy access
intelligent_parser = IntelligentTextParser()
PineScriptAlertParser = PineScriptAlertParser
