"""
Unified AI Processor - Single Source of Truth for AI Processing

Consolidates functionality from:
- ai_chat_processor.py (interface layer)
- ai_processor_robust.py (core engine)
- ai_service_wrapper.py (legacy wrapper)
- Enhanced with infrastructure components

This is the new canonical AI processor that replaces all fragmented implementations.
"""

import logging
import asyncio
import json
import os
from typing import Dict, Any, List, Optional, Tuple, Union
from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum

# Import infrastructure components (now relative imports)
from .timeout_manager import timeout_manager
from .circuit_breaker import ai_circuit_breaker_manager
from .query_cache import simple_query_cache
from .smart_model_router import router

# Import specialized services
from .enhanced_symbol_extractor import enhanced_symbol_extractor
from .enhanced_intent_detector import enhanced_intent_detector
from .intelligent_text_parser import intelligent_parser
from .ai_processor_robust import RobustFinancialAnalyzer # Explicitly imported

logger = logging.getLogger(__name__)


class ProcessingMode(Enum):
    """AI processing modes"""
    MARKET_ANALYSIS = "market_analysis"
    GENERAL_QUESTION = "general_question"
    SYMBOL_EXTRACTION = "symbol_extraction"
    INTENT_DETECTION = "intent_detection"


class ValidationMode(Enum):
    """Response validation modes"""
    STRICT = "strict"           # Zero AI generation, pure data only
    VALIDATED = "validated"     # AI generation with validation
    PERMISSIVE = "permissive"   # Standard AI generation


@dataclass
class ProcessingResult:
    """Unified result structure for all AI processing"""
    response: str
    status: str = "success"
    data_quality: int = 85
    intent: Optional[str] = None
    symbol: Optional[str] = None
    confidence: float = 0.8
    processing_mode: ProcessingMode = ProcessingMode.GENERAL_QUESTION
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class TechnicalIndicators:
    """Container for all calculated technical indicators"""
    rsi: Optional[float] = None
    macd: Optional[Dict[str, float]] = None
    sma_20: Optional[float] = None
    sma_50: Optional[float] = None
    support_levels: Optional[List[float]] = None
    resistance_levels: Optional[List[float]] = None
    bollinger_upper: Optional[float] = None
    bollinger_lower: Optional[float] = None
    volume_avg: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary, excluding None values"""
        return {k: v for k, v in asdict(self).items() if v is not None}


class UnifiedAIProcessor:
    """
    Unified AI Processor - Single source of truth for all AI processing.
    
    Consolidates functionality from multiple fragmented AI services into
    a single, reliable, and maintainable interface.
    """
    
    def __init__(self, context: Optional[Any] = None):
        """
        Initialize the unified AI processor.
        
        Args:
            context: Optional context object with pipeline information
        """
        self.context = context
        self.pipeline_id = getattr(context, 'pipeline_id', 'unknown') if context else 'unknown'
        self.logger = logging.getLogger(__name__)
        
        # Initialize core components
        self._initialize_core_engine()
        self._initialize_infrastructure()
        self._initialize_specialized_services()
        
        self.logger.info(f"✅ Unified AI Processor initialized for pipeline: {self.pipeline_id}")
    
    def _initialize_core_engine(self):
        """Initialize the core AI analysis engine"""
        try:
            # Import and initialize the robust financial analyzer
            self.core_engine = RobustFinancialAnalyzer()
            self.logger.info("✅ Core AI engine initialized")
        except ImportError as e:
            self.logger.error(f"❌ Failed to initialize core engine: {e}")
            self.core_engine = None
    
    def _initialize_infrastructure(self):
        """Initialize infrastructure components"""
        self.timeout_manager = timeout_manager
        self.circuit_breaker = ai_circuit_breaker_manager
        self.cache = simple_query_cache
        self.model_router = router
        self.logger.info("✅ Infrastructure components initialized")
    
    def _initialize_specialized_services(self):
        """Initialize specialized AI services"""
        self.symbol_extractor = enhanced_symbol_extractor
        self.intent_detector = enhanced_intent_detector
        self.text_parser = intelligent_parser
        self.logger.info("✅ Specialized services initialized")
    
    # ==================== PRIMARY INTERFACE METHODS ====================
    
    async def process(self, query: str, **kwargs) -> Dict[str, Any]:
        """
        Primary processing method - handles all types of AI requests.
        
        This is the main entry point that replaces all fragmented interfaces.
        
        Args:
            query: User query or request
            **kwargs: Additional context (market_data, initial_analysis, etc.)
            
        Returns:
            Unified response dictionary
        """
        try:
            # Check cache first (with user_id for compatibility)
            cache_key = self.cache._generate_cache_key(query, str(kwargs))
            try:
                cached_result = await self.cache.get(cache_key, user_id="unified_processor")
                if cached_result and not cached_result.is_expired():
                    self.logger.info("📋 Returning cached result")
                    return cached_result.to_dict()
            except Exception as cache_error:
                self.logger.debug(f"Cache access failed: {cache_error}")
                # Continue without cache
            
            # Determine processing mode
            processing_mode = self._determine_processing_mode(query, kwargs)
            
            # Route to appropriate handler with circuit breaker protection
            with self.circuit_breaker.get_breaker("unified_ai_processor"):
                result = await self._route_processing(query, processing_mode, kwargs)
            
            # Cache the result
            try:
                await self.cache.set(cache_key, result.response, user_id="unified_processor", metadata=result.metadata)
            except Exception as cache_error:
                self.logger.debug(f"Cache set failed: {cache_error}")
                # Continue without caching
            
            # Convert to legacy format for backward compatibility
            return self._to_legacy_format(result)
            
        except Exception as e:
            self.logger.error(f"❌ Processing error: {e}")
            return self._create_error_response(str(e))
    
    async def analyze_market_data(self, market_data: Dict[str, Any]) -> str:
        """
        Analyze market data - direct interface for market analysis.
        
        Args:
            market_data: Market data dictionary
            
        Returns:
            Analysis response string
        """
        if not self.core_engine:
            return "❌ Core AI engine not available"
        
        try:
            symbol = market_data.get('symbol', 'UNKNOWN')
            return await self.core_engine.analyze_market_data(market_data)
        except Exception as e:
            self.logger.error(f"❌ Market analysis error: {e}")
            return f"❌ Unable to analyze market data: {str(e)}"
    
    async def answer_general_question(self, query: str) -> str:
        """
        Answer general questions - direct interface for Q&A.
        
        Args:
            query: User's question
            
        Returns:
            Answer string
        """
        if not self.core_engine:
            return "❌ Core AI engine not available"
        
        try:
            return await self.core_engine.answer_general_question(query)
        except Exception as e:
            self.logger.error(f"❌ General question error: {e}")
            return f"❌ Unable to answer question: {str(e)}"
    
    async def process_query(self, query: str, context=None) -> 'ProcessingResult':
        """
        Process query and return structured result.
        
        Args:
            query: User query
            context: Optional context
            
        Returns:
            ProcessingResult object
        """
        try:
            # Use the primary process method
            result_dict = await self.process(query, context=context)
            
            # Convert to ProcessingResult
            return ProcessingResult(
                response=result_dict.get('response', ''),
                status=result_dict.get('status', 'success'),
                data_quality=result_dict.get('data_quality', 85),
                intent=result_dict.get('intent'),
                symbol=result_dict.get('symbol'),
                confidence=result_dict.get('confidence', 0.8),
                processing_mode=ProcessingMode.GENERAL_QUESTION,
                metadata=result_dict.get('metadata', {})
            )
        except Exception as e:
            self.logger.error(f"❌ Query processing error: {e}")
            return ProcessingResult(
                response=f"❌ Error processing query: {str(e)}",
                status="error",
                data_quality=0,
                confidence=0.0
            )
    
    # ==================== SPECIALIZED METHODS ====================
    
    async def extract_symbols(self, text: str, use_ai: bool = True) -> List[str]:
        """Extract symbols from text using AI enhancement"""
        try:
            if use_ai and self.symbol_extractor:
                return await self.symbol_extractor.extract_symbols_simple(text, use_ai=True)
            else:
                # Fallback to basic extraction
                return self._basic_symbol_extraction(text)
        except Exception as e:
            self.logger.error(f"❌ Symbol extraction error: {e}")
            return []
    
    async def detect_intent(self, query: str) -> Dict[str, Any]:
        """Detect query intent using AI"""
        try:
            if self.intent_detector:
                result = await self.intent_detector.analyze_intent(query)
                return {
                    'intent': result.primary_intent.value if result.primary_intent else 'unknown',
                    'confidence': result.confidence,
                    'entities': result.entities
                }
            else:
                return {'intent': 'unknown', 'confidence': 0.5, 'entities': {}}
        except Exception as e:
            self.logger.error(f"❌ Intent detection error: {e}")
            return {'intent': 'unknown', 'confidence': 0.0, 'entities': {}}

    # ==================== INTERNAL HELPER METHODS ====================

    def _determine_processing_mode(self, query: str, kwargs: Dict[str, Any]) -> ProcessingMode:
        """Determine the appropriate processing mode for the query"""
        # Check if we have market data
        if 'market_data' in kwargs or 'initial_analysis' in kwargs:
            return ProcessingMode.MARKET_ANALYSIS

        # Check if this is an explicit symbol extraction request (not general questions about stocks)
        query_lower = query.lower()

        # Only route to symbol extraction for explicit extraction requests
        explicit_extraction_patterns = [
            'extract symbol', 'find symbol', 'get symbol', 'what symbol',
            'extract ticker', 'find ticker', 'get ticker', 'what ticker',
            'symbol for', 'ticker for'
        ]

        if any(pattern in query_lower for pattern in explicit_extraction_patterns):
            return ProcessingMode.SYMBOL_EXTRACTION

        # Questions about stocks, analysis, recommendations should be general questions
        # Don't route to symbol extraction just because they mention "stock"
        return ProcessingMode.GENERAL_QUESTION

    async def _route_processing(self, query: str, mode: ProcessingMode, kwargs: Dict[str, Any]) -> ProcessingResult:
        """Route processing based on mode"""
        if mode == ProcessingMode.MARKET_ANALYSIS:
            return await self._handle_market_analysis(query, kwargs)
        elif mode == ProcessingMode.SYMBOL_EXTRACTION:
            return await self._handle_symbol_extraction(query)
        else:
            return await self._handle_general_question(query)

    async def _handle_market_analysis(self, query: str, kwargs: Dict[str, Any]) -> ProcessingResult:
        """Handle market analysis requests"""
        try:
            # Extract market data
            market_data = kwargs.get('market_data') or kwargs.get('initial_analysis', {}).get('market_data')

            if not market_data:
                return ProcessingResult(
                    response="❌ No market data provided for analysis",
                    status="error",
                    processing_mode=ProcessingMode.MARKET_ANALYSIS
                )

            # Perform analysis
            response = await self.analyze_market_data(market_data)
            symbol = market_data.get('symbol')

            return ProcessingResult(
                response=response,
                status="success",
                data_quality=90,
                intent="market_analysis",
                symbol=symbol,
                processing_mode=ProcessingMode.MARKET_ANALYSIS,
                metadata={'market_data_keys': list(market_data.keys())}
            )

        except Exception as e:
            self.logger.error(f"❌ Market analysis error: {e}")
            return ProcessingResult(
                response=f"❌ Market analysis failed: {str(e)}",
                status="error",
                processing_mode=ProcessingMode.MARKET_ANALYSIS
            )

    async def _handle_symbol_extraction(self, query: str) -> ProcessingResult:
        """Handle symbol extraction requests"""
        try:
            symbols = await self.extract_symbols(query, use_ai=True)

            if symbols:
                response = f"📊 Extracted symbols: {', '.join(symbols)}"
                symbol = symbols[0]  # Primary symbol
            else:
                response = "❌ No symbols found in the query"
                symbol = None

            return ProcessingResult(
                response=response,
                status="success",
                data_quality=85,
                intent="symbol_extraction",
                symbol=symbol,
                processing_mode=ProcessingMode.SYMBOL_EXTRACTION,
                metadata={'extracted_symbols': symbols}
            )

        except Exception as e:
            self.logger.error(f"❌ Symbol extraction error: {e}")
            return ProcessingResult(
                response=f"❌ Symbol extraction failed: {str(e)}",
                status="error",
                processing_mode=ProcessingMode.SYMBOL_EXTRACTION
            )

    async def _handle_general_question(self, query: str) -> ProcessingResult:
        """Handle general questions"""
        try:
            # Detect intent first
            intent_info = await self.detect_intent(query)

            # Answer the question
            response = await self.answer_general_question(query)

            return ProcessingResult(
                response=response,
                status="success",
                data_quality=85,
                intent=intent_info.get('intent', 'general'),
                confidence=intent_info.get('confidence', 0.8),
                processing_mode=ProcessingMode.GENERAL_QUESTION,
                metadata={'intent_analysis': intent_info}
            )

        except Exception as e:
            self.logger.error(f"❌ General question error: {e}")
            return ProcessingResult(
                response=f"❌ Unable to answer question: {str(e)}",
                status="error",
                processing_mode=ProcessingMode.GENERAL_QUESTION
            )

    def _basic_symbol_extraction(self, text: str) -> List[str]:
        """Basic symbol extraction fallback"""
        import re
        # Simple regex for $SYMBOL format
        pattern = r'\$([A-Z]{1,10})\b'
        matches = re.findall(pattern, text)
        return list(set(matches))  # Remove duplicates

    def _to_legacy_format(self, result: ProcessingResult) -> Dict[str, Any]:
        """Convert ProcessingResult to legacy format for backward compatibility"""
        return {
            'response': result.response,
            'status': result.status,
            'data_quality': result.data_quality,
            'intent': result.intent,
            'symbol': result.symbol,
            'confidence': result.confidence,
            'metadata': result.metadata
        }

    def _create_error_response(self, error_message: str) -> Dict[str, Any]:
        """Create standardized error response"""
        return {
            'response': f"❌ Error: {error_message}",
            'status': 'error',
            'data_quality': 0,
            'intent': None,
            'symbol': None,
            'confidence': 0.0,
            'metadata': {'error': error_message}
        }


# ==================== FACTORY FUNCTIONS ====================

def create_unified_processor(context: Optional[Any] = None) -> UnifiedAIProcessor:
    """
    Factory function to create a unified AI processor.

    This replaces all the various create_processor functions scattered
    across the AI service files.
    """
    return UnifiedAIProcessor(context)


# ==================== BACKWARD COMPATIBILITY ====================

# Legacy aliases for backward compatibility
AIChatProcessor = UnifiedAIProcessor
AIChatProcessorWrapper = UnifiedAIProcessor
CleanAIProcessor = UnifiedAIProcessor

# Legacy factory functions
create_processor = create_unified_processor

# Legacy async function
async def process_query(query: str, context=None) -> Dict[str, Any]:
    """Legacy async function for backward compatibility"""
    processor = create_unified_processor(context)
    result = await processor.process_query(query, context)
    return {
        'response': result.response,
        'status': result.status,
        'data_quality': result.data_quality,
        'intent': result.intent,
        'symbol': result.symbol
    }


# ==================== GLOBAL INSTANCE ====================

_unified_ai_processor_instance = None

def get_unified_ai_processor():
    """Get the global singleton instance of the UnifiedAIProcessor."""
    global _unified_ai_processor_instance
    if _unified_ai_processor_instance is None:
        _unified_ai_processor_instance = UnifiedAIProcessor()
    return _unified_ai_processor_instance

# For backward compatibility, other modules might be importing 'processor'
processor = get_unified_ai_processor()
unified_ai_processor = processor