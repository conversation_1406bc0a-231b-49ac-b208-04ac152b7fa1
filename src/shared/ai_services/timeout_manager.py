"""
Timeout Manager for AI Services
Provides centralized timeout management for all AI service calls
"""

import asyncio
import logging
from typing import Any, Callable, Optional
from functools import wraps
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class TimeoutConfig:
    """Configuration for timeout settings"""
    default_timeout: float = 15.0  # 15 seconds default
    ai_service_timeout: float = 20.0  # 20 seconds for AI services
    data_fetch_timeout: float = 10.0  # 10 seconds for data fetching
    batch_processing_timeout: float = 30.0  # 30 seconds for batch processing
    
    def get_timeout(self, service_type: str) -> float:
        """Get timeout for a specific service type"""
        timeouts = {
            'ai_service': self.ai_service_timeout,
            'data_fetch': self.data_fetch_timeout,
            'batch_processing': self.batch_processing_timeout
        }
        return timeouts.get(service_type, self.default_timeout)

class TimeoutManager:
    """Centralized timeout management for AI services"""
    
    def __init__(self):
        self.config = TimeoutConfig()
        self.active_operations = {}
        
    def timeout_wrapper(self, service_type: str = 'default'):
        """
        Decorator to add timeout to async functions
        
        Args:
            service_type: Type of service to determine timeout value
        """
        def decorator(func: Callable) -> Callable:
            @wraps(func)
            async def wrapper(*args, **kwargs) -> Any:
                timeout = self.config.get_timeout(service_type)
                
                try:
                    # Create a task for the function call
                    task = asyncio.create_task(func(*args, **kwargs))
                    
                    # Generate operation ID
                    op_id = f"{func.__name__}_{id(task)}"
                    self.active_operations[op_id] = task
                    
                    # Wait for completion with timeout
                    result = await asyncio.wait_for(task, timeout=timeout)
                    
                    # Clean up
                    if op_id in self.active_operations:
                        del self.active_operations[op_id]
                        
                    return result
                    
                except asyncio.TimeoutError:
                    logger.error(f"Timeout exceeded for {func.__name__} after {timeout} seconds")
                    # Cancel the task if it's still running
                    if 'op_id' in locals() and op_id in self.active_operations:
                        task = self.active_operations[op_id]
                        if not task.done():
                            task.cancel()
                        del self.active_operations[op_id]
                    raise
                except Exception as e:
                    # Clean up on other exceptions
                    if 'op_id' in locals() and op_id in self.active_operations:
                        if op_id in self.active_operations:
                            del self.active_operations[op_id]
                    raise
                    
            return wrapper
        return decorator
    
    async def cancel_all_operations(self):
        """Cancel all active operations"""
        for op_id, task in self.active_operations.items():
            if not task.done():
                task.cancel()
                logger.info(f"Cancelled operation: {op_id}")
        self.active_operations.clear()

# Global timeout manager instance
timeout_manager = TimeoutManager()

# Convenience decorators
ai_service_timeout = timeout_manager.timeout_wrapper('ai_service')
data_fetch_timeout = timeout_manager.timeout_wrapper('data_fetch')
batch_processing_timeout = timeout_manager.timeout_wrapper('batch_processing')