"""
Circuit Breaker for AI Services
Prevents cascading failures when AI services are slow or unavailable
"""

import asyncio
import time
import logging
from enum import Enum
from typing import Dict, Any, Optional, Callable
from dataclasses import dataclass
from functools import wraps

logger = logging.getLogger(__name__)

class CircuitState(Enum):
    """Circuit breaker states"""
    CLOSED = "closed"      # Normal operation
    OPEN = "open"          # Tripped, rejecting requests
    HALF_OPEN = "half_open" # Testing if service recovered

@dataclass
class CircuitBreakerConfig:
    """Configuration for circuit breaker"""
    failure_threshold: int = 3      # Number of failures to trip circuit
    timeout: float = 60.0           # Time to wait before retry (seconds)
    recovery_timeout: float = 30.0  # Time to wait in half-open state
    expected_exceptions: tuple = (Exception,)  # Exceptions that count as failures

class CircuitBreaker:
    """Circuit breaker implementation for AI services"""
    
    def __init__(self, name: str, config: Optional[CircuitBreakerConfig] = None):
        self.name = name
        self.config = config or CircuitBreakerConfig()
        self.state = CircuitState.CLOSED
        self.failure_count = 0
        self.last_failure_time = None
        self.lock = asyncio.Lock()
        
    def __enter__(self):
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        pass
        
    async def __aenter__(self):
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        async with self.lock:
            if exc_type is not None:
                # Check if exception is one we should count as a failure
                if issubclass(exc_type, self.config.expected_exceptions):
                    await self._record_failure()
            else:
                # Successful call, reset failure count
                self._reset()
                
    def _reset(self):
        """Reset circuit breaker to closed state"""
        self.state = CircuitState.CLOSED
        self.failure_count = 0
        self.last_failure_time = None
        logger.info(f"Circuit breaker '{self.name}' reset to CLOSED state")
        
    async def _record_failure(self):
        """Record a failure and potentially trip the circuit"""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        logger.warning(f"Circuit breaker '{self.name}' recorded failure #{self.failure_count}")
        
        if self.failure_count >= self.config.failure_threshold:
            self.state = CircuitState.OPEN
            logger.error(f"Circuit breaker '{self.name}' TRIPPED to OPEN state after {self.failure_count} failures")
            
    async def can_execute(self) -> bool:
        """Check if a request can be executed"""
        async with self.lock:
            current_time = time.time()
            
            # If circuit is open, check if we should move to half-open
            if self.state == CircuitState.OPEN:
                if (self.last_failure_time and 
                    current_time - self.last_failure_time >= self.config.timeout):
                    self.state = CircuitState.HALF_OPEN
                    logger.info(f"Circuit breaker '{self.name}' moved to HALF_OPEN state")
                    return True
                return False
                
            # If circuit is half-open, only allow one request
            elif self.state == CircuitState.HALF_OPEN:
                return True
                
            # Circuit is closed, allow all requests
            return True
            
    async def success(self):
        """Record a successful request"""
        async with self.lock:
            self._reset()
            
    async def failure(self):
        """Record a failed request"""
        async with self.lock:
            await self._record_failure()

class AICircuitBreakerManager:
    """Manages circuit breakers for different AI services"""
    
    def __init__(self):
        self.breakers: Dict[str, CircuitBreaker] = {}
        self.default_config = CircuitBreakerConfig(
            failure_threshold=3,
            timeout=60.0,
            recovery_timeout=30.0,
            expected_exceptions=(Exception,)
        )
        
    def get_breaker(self, service_name: str, config: Optional[CircuitBreakerConfig] = None) -> CircuitBreaker:
        """Get or create a circuit breaker for a service"""
        if service_name not in self.breakers:
            config = config or self.default_config
            self.breakers[service_name] = CircuitBreaker(service_name, config)
            logger.info(f"Created circuit breaker for service '{service_name}'")
        return self.breakers[service_name]
        
    def circuit_breaker_decorator(self, service_name: str, config: Optional[CircuitBreakerConfig] = None):
        """Decorator to wrap functions with circuit breaker protection"""
        def decorator(func: Callable) -> Callable:
            breaker = self.get_breaker(service_name, config)
            
            @wraps(func)
            async def wrapper(*args, **kwargs) -> Any:
                # Check if we can execute
                if not await breaker.can_execute():
                    raise Exception(f"Circuit breaker for '{service_name}' is OPEN. Service temporarily unavailable.")
                
                try:
                    # Execute the function
                    result = await func(*args, **kwargs)
                    # Record success
                    await breaker.success()
                    return result
                except Exception as e:
                    # Record failure
                    await breaker.failure()
                    raise
                    
            return wrapper
        return decorator

# Global circuit breaker manager
ai_circuit_breaker_manager = AICircuitBreakerManager()

# Convenience decorator
def ai_circuit_breaker(service_name: str, config: Optional[CircuitBreakerConfig] = None):
    """Decorator to protect AI service calls with circuit breaker"""
    return ai_circuit_breaker_manager.circuit_breaker_decorator(service_name, config)