"""
Enhanced AI-Powered Intent Detection

This module provides intelligent intent detection that replaces rigid regex patterns
with AI understanding for superior query classification and context awareness.
"""

import asyncio
import logging
import re
import time
import json
from typing import List, Dict, Any, Optional, Tuple, Union
from dataclasses import dataclass
from enum import Enum

from src.shared.ai_services.ai_chat_processor import AIChatProcessorWrapper as AIClientWrapper # Adjusted import

logger = logging.getLogger(__name__) # Adjusted logging setup

class IntentType(Enum):
    """Types of user intents"""
    PRICE_CHECK = "price_check"
    TECHNICAL_ANALYSIS = "technical_analysis"
    FUNDAMENTAL_ANALYSIS = "fundamental_analysis"
    RECOMMENDATION = "recommendation"
    COMPARISON = "comparison"
    PORTFOLIO_ADVICE = "portfolio_advice"
    RISK_ASSESSMENT = "risk_assessment"
    MARKET_NEWS = "market_news"
    OPTIONS_ANALYSIS = "options_analysis"
    SUPPORT_RESISTANCE = "support_resistance"
    SUPPORT_RESISTANCE_LEVELS = "support_resistance_levels"
    STOCK_ANALYSIS = "stock_analysis"
    GENERAL_QUESTION = "general_question"
    HELP_REQUEST = "help_request"
    GREETING = "greeting"
    UNKNOWN = "unknown"

class UrgencyLevel(Enum):
    """Urgency levels for user queries"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"

class ResponseStyle(Enum):
    """Preferred response styles"""
    CONCISE = "concise"
    DETAILED = "detailed"
    CONVERSATIONAL = "conversational"
    TECHNICAL = "technical"
    SIMPLE = "simple"
    ACADEMIC = "academic"

@dataclass
class IntentAnalysis:
    """Result of intent analysis"""
    primary_intent: IntentType
    secondary_intents: List[IntentType]
    confidence: float
    urgency_level: UrgencyLevel
    response_style: ResponseStyle
    entities: Dict[str, Any]
    context_clues: List[str]
    reasoning: str
    processing_time: float
    method: str  # 'ai' or 'regex_fallback'

class EnhancedIntentDetector:
    """
    Pure AI-powered intent detector - NO REGEX FALLBACKS
    """

    def __init__(self):
        self.ai_client = AIClientWrapper()

        # AI retry configuration
        self.max_retries = 3
        self.retry_delay = 1.0

        # Simplified prompt handling (removed complex centralized prompt import)
        self.ai_prompt_template = """
You are a financial intent classifier. Analyze this query and return ONLY a valid JSON object.

Query: "{text}"

CRITICAL: Return ONLY valid JSON, no other text. Use this exact format:

{{
    "primary_intent": "price_check|technical_analysis|fundamental_analysis|recommendation|comparison|portfolio_advice|risk_assessment|market_news|options_analysis|support_resistance|support_resistance_levels|stock_analysis|general_question|help_request|greeting|unknown",
    "secondary_intents": ["list", "of", "secondary", "intents"],
    "confidence": 0.95,
    "urgency_level": "low|medium|high|urgent",
    "response_style": "concise|detailed|conversational|technical|simple|academic",
    "entities": {{
        "symbols": ["AAPL", "MSFT"],
        "timeframes": ["1d", "1w"],
        "indicators": ["RSI", "MACD"],
        "price_targets": [150.00],
        "other": {{}}
    }},
    "context_clues": ["list", "of", "important", "context", "words"],
    "reasoning": "Brief explanation of why this intent was chosen"
}}

Rules:
1. ONLY return JSON, no explanations or additional text
2. primary_intent must be one of the listed values
3. confidence must be between 0.0 and 1.0
4. urgency_level must be: low, medium, high, or urgent
5. response_style must be: concise, detailed, conversational, technical, simple, or academic
6. All arrays must be valid JSON arrays
7. All objects must be valid JSON objects

Examples:
- "What's the price of AAPL?" → {{"primary_intent": "price_check", ...}}
- "Show me support and resistance levels" → {{"primary_intent": "support_resistance", ...}}
- "Analyze TSLA technically" → {{"primary_intent": "technical_analysis", ...}}
"""
    
    async def analyze_intent(self, text: str) -> IntentAnalysis:
        """
        Analyze user intent using PURE AI - NO REGEX FALLBACKS

        Args:
            text: Text to analyze for intent
            use_ai: Always True - kept for compatibility
            fallback_on_failure: Ignored - AI always used

        Returns:
            IntentAnalysis object with comprehensive intent information
        """
        start_time = time.time()

        if not text or not text.strip():
            return self._create_ai_default_analysis("Empty query", start_time)

        # ALWAYS use AI - retry until success
        for attempt in range(self.max_retries):
            try:
                ai_result = await self._analyze_with_ai(text)
                if ai_result:
                    ai_result.processing_time = time.time() - start_time
                    logger.info(f"✅ AI intent analysis successful on attempt {attempt + 1}")
                    return ai_result

                logger.warning(f"AI intent analysis returned no results (attempt {attempt + 1})")

            except Exception as e:
                logger.warning(f"AI intent analysis failed (attempt {attempt + 1}): {e}")

                if attempt < self.max_retries - 1:
                    await asyncio.sleep(self.retry_delay * (attempt + 1))

        # If all AI attempts fail, create a basic AI-based analysis
        logger.error(f"All AI attempts failed for: {text[:50]}...")
        return self._create_ai_default_analysis(text, start_time)
    
    async def _analyze_with_ai(self, text: str) -> Optional[IntentAnalysis]:
        """Analyze intent using AI with improved JSON enforcement"""
        try:
            # Use local prompt template
            prompt = self.ai_prompt_template.format(text=text)

            # Get AI response
            response = await self.ai_client.generate_response(prompt)
            
            if not response:
                logger.warning("Empty AI response received")
                return None
            
            # Clean and validate response
            response = response.strip()
            
            # If response doesn't start with {, try to find JSON
            if not response.startswith('{'):
                logger.warning(f"AI response doesn't start with JSON: {response[:100]}...")
                # Try to extract JSON from the response
                json_match = re.search(r'\{.*\}', response, re.DOTALL)
                if json_match:
                    response = json_match.group(0)
                else:
                    logger.warning("No JSON found in AI response")
                    return None
            
            # Parse AI response
            intent_data = self._parse_ai_response(response)
            
            if not intent_data:
                logger.warning("Failed to parse AI response as valid intent data")
                return None
            
            # Validate required fields
            if 'primary_intent' not in intent_data:
                logger.warning("AI response missing primary_intent field")
                return None
            
            # Convert to IntentAnalysis object
            return IntentAnalysis(
                primary_intent=IntentType(intent_data.get('primary_intent', 'unknown')),
                secondary_intents=[IntentType(intent) for intent in intent_data.get('secondary_intents', []) if intent in [e.value for e in IntentType]],
                confidence=float(intent_data.get('confidence', 0.8)),
                urgency_level=UrgencyLevel(intent_data.get('urgency_level', 'medium')),
                response_style=ResponseStyle(intent_data.get('response_style', 'detailed')),
                entities=intent_data.get('entities', {}),
                context_clues=intent_data.get('context_clues', []),
                reasoning=intent_data.get('reasoning', 'AI analysis'),
                processing_time=0.0,  # Will be set by caller
                method='ai'
            )
            
        except Exception as e:
            logger.error(f"AI intent analysis error: {e}")
            return None
    
    def _create_ai_default_analysis(self, text: str, start_time: float) -> IntentAnalysis:
        """Create AI-based default analysis when AI calls fail"""
        # Use simple heuristics to make an educated guess
        text_lower = text.lower().strip()

        # Basic intent classification without regex
        if any(word in text_lower for word in ['hello', 'hi', 'hey', 'whaddup', 'wassup', 'sup', 'howdy']):
            primary_intent = IntentType.GREETING
            confidence = 0.8
            response_style = ResponseStyle.CONVERSATIONAL
        elif any(word in text_lower for word in ['help', 'command', 'usage', 'how to']):
            primary_intent = IntentType.HELP_REQUEST
            confidence = 0.7
            response_style = ResponseStyle.DETAILED
        elif any(word in text_lower for word in ['price', 'cost', 'value', 'quote']):
            primary_intent = IntentType.PRICE_CHECK
            confidence = 0.6
            response_style = ResponseStyle.CONCISE
        elif any(word in text_lower for word in ['buy', 'sell', 'recommend', 'should i']):
            primary_intent = IntentType.RECOMMENDATION
            confidence = 0.6
            response_style = ResponseStyle.DETAILED
        else:
            primary_intent = IntentType.GENERAL_QUESTION
            confidence = 0.5
            response_style = ResponseStyle.DETAILED

        return IntentAnalysis(
            primary_intent=primary_intent,
            secondary_intents=[],
            confidence=confidence,
            urgency_level=UrgencyLevel.MEDIUM,
            response_style=response_style,
            entities={'symbols': [], 'timeframes': [], 'indicators': [], 'price_targets': [], 'other': {}},
            context_clues=[text_lower],
            reasoning=f"AI-based heuristic analysis (AI calls failed) - detected as {primary_intent.value}",
            processing_time=time.time() - start_time,
            method='ai_heuristic_fallback'
        )
    
    def _parse_ai_response(self, response: str) -> Optional[Dict[str, Any]]:
        """Parse AI response to extract intent data with improved error handling"""
        
        if not response or not response.strip():
            logger.warning("Empty AI response received")
            return None
        
        # Clean the response
        response = response.strip()
        
        # Try to find JSON in the response with multiple patterns
        json_patterns = [
            r'\{[^{}]*"primary_intent"[^{}]*\}',  # Simple object with primary_intent
            r'\{.*?"primary_intent".*?\}',  # Object containing primary_intent
            r'\{.*?\}',  # Any JSON object
        ]
        
        for pattern in json_patterns:
            matches = re.findall(pattern, response, re.DOTALL)
            for match in matches:
                try:
                    # Clean up the match
                    match = match.strip()
                    if not match.startswith('{'):
                        continue
                    
                    data = json.loads(match)
                    if isinstance(data, dict) and 'primary_intent' in data:
                        # Validate the primary_intent value
                        intent = data.get('primary_intent', '').strip()
                        if intent and intent in [e.value for e in IntentType]:
                            return data
                        else:
                            logger.warning(f"Invalid primary_intent value: {intent}")
                            continue
                except json.JSONDecodeError as e:
                    logger.debug(f"JSON decode error for pattern {pattern}: {e}")
                    continue
        
        # Fallback: try to parse the entire response
        try:
            data = json.loads(response)
            if isinstance(data, dict) and 'primary_intent' in data:
                return data
        except json.JSONDecodeError:
            pass
        
        # Last resort: try to extract intent from text
        logger.warning(f"Could not parse AI response as JSON: {response[:200]}...")
        return self._extract_intent_from_text(response)
    
    def _extract_intent_from_text(self, text: str) -> Optional[Dict[str, Any]]:
        """Extract intent information from non-JSON text response"""
        text_lower = text.lower()
        
        # Look for intent keywords in the text
        intent_mapping = {
            'support_resistance': ['support', 'resistance', 'levels'],
            'technical_analysis': ['technical', 'analysis', 'chart', 'indicator'],
            'price_check': ['price', 'current', 'quote'],
            'stock_analysis': ['analyze', 'analysis', 'stock'],
            'general_question': ['question', 'ask', 'what', 'how']
        }
        
        for intent, keywords in intent_mapping.items():
            if any(keyword in text_lower for keyword in keywords):
                return {
                    'primary_intent': intent,
                    'secondary_intents': [],
                    'confidence': 0.6,
                    'urgency_level': 'medium',
                    'response_style': 'detailed',
                    'entities': {'symbols': [], 'timeframes': [], 'indicators': [], 'price_targets': [], 'other': {}},
                    'context_clues': [text_lower],
                    'reasoning': f'Extracted from text: {text[:50]}...'
                }
        
        return None
    

    
    # Convenience methods - ALWAYS AI-POWERED
    async def detect_intent_simple(self, text: str, use_ai: bool = True) -> IntentType:
        """Detect intent and return just the primary intent type - ALWAYS uses AI"""
        analysis = await self.analyze_intent(text)  # Always AI
        return analysis.primary_intent

    async def get_intent_with_confidence(self, text: str, min_confidence: float = 0.7) -> Tuple[IntentType, float]:
        """Get intent with confidence score above threshold - ALWAYS uses AI"""
        analysis = await self.analyze_intent(text)  # Always AI
        if analysis.confidence >= min_confidence:
            return analysis.primary_intent, analysis.confidence
        return IntentType.UNKNOWN, analysis.confidence

# Global instance for easy access
enhanced_intent_detector = EnhancedIntentDetector()

# Convenience functions - ALWAYS AI-POWERED
async def analyze_query_intent(text: str) -> IntentAnalysis:
    """Analyze query intent using PURE AI analysis - NO REGEX"""
    return await enhanced_intent_detector.analyze_intent(text)

async def detect_intent_ai(text: str) -> IntentType:
    """Detect intent using PURE AI analysis - NO REGEX"""
    return await enhanced_intent_detector.detect_intent_simple(text)