"""
Simple Query Analyzer
Quickly identifies simple price queries to bypass full AI pipeline
"""

import re
import logging
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class QueryIntent(Enum):
    """Query intent types"""
    PRICE_CHECK = "price_check"
    STOCK_ANALYSIS = "stock_analysis"
    MARKET_OVERVIEW = "market_overview"
    GENERAL_QUESTION = "general_question"
    HELP_REQUEST = "help_request"

class ProcessingRoute(Enum):
    """Processing routes"""
    QUICK_RESPONSE = "quick_response"  # Simple queries that can be answered quickly
    FULL_PIPELINE = "full_pipeline"     # Complex queries requiring full AI processing

@dataclass
class SymbolInfo:
    """Information about a detected symbol"""
    text: str
    confidence: float
    position: Tuple[int, int]  # start, end positions

@dataclass
class QueryAnalysis:
    """Query analysis results"""
    intent: QueryIntent
    confidence: float
    symbols: List[SymbolInfo]
    processing_route: ProcessingRoute
    is_simple_price_query: bool
    extracted_text: str

class SimpleQueryAnalyzer:
    """Simple query analyzer for fast identification of simple price queries"""
    
    def __init__(self):
        # Patterns for simple price queries
        self.simple_price_patterns = [
            r"(?:what(?:'s| is) )?(?:the )?(?:current |latest )?(?:price|value) (?:of |for )?(?:\$)?([A-Z]{1,5})",  # "What is the price of $AAPL"
            r"(?:how much is|what(?:'s| is)) (?:\$)?([A-Z]{1,5})(?: worth)?",  # "How much is AAPL worth"
            r"(?:\$)?([A-Z]{1,5}) (?:price|value)",  # "AAPL price"
            r"price (?:of |for )?(?:\$)?([A-Z]{1,5})",  # "Price of AAPL"
            r"(?:\$)?([A-Z]{1,5})$",  # Just "$AAPL" or "AAPL"
        ]
        
        # Keywords that indicate simple price queries
        self.price_keywords = {
            "price", "value", "worth", "cost", "current", "latest", 
            "stock", "shares", "equity", "security"
        }
        
        # Keywords that indicate complex queries (should bypass simple detection)
        self.complex_keywords = {
            "analyze", "analysis", "recommend", "strategy", "trade", 
            "buy", "sell", "hold", "invest", "portfolio", "diversify",
            "risk", "volatility", "trend", "pattern", "support", 
            "resistance", "indicator", "rsi", "macd", "moving average",
            "technical", "fundamental", "sentiment", "news", "earnings"
        }
        
        # Common symbols that might appear in queries
        self.common_symbols = {
            "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA", "META", "NVDA", 
            "AMD", "INTC", "NFLX", "DIS", "BA", "JPM", "GS", "BAC",
            "BTC", "ETH", "XRP", "ADA", "SOL"
        }

    def analyze_query(self, query: str, context: Optional[Dict[str, Any]] = None) -> QueryAnalysis:
        """
        Quickly analyze a query to determine if it's a simple price query.
        
        Args:
            query: User query string
            context: Optional context information
            
        Returns:
            QueryAnalysis with intent, symbols, and processing route
        """
        query_clean = query.strip().lower()
        
        # Extract symbols from query
        symbols = self._extract_symbols(query)
        
        # Check for complex keywords that would require full processing
        if any(keyword in query_clean for keyword in self.complex_keywords):
            return QueryAnalysis(
                intent=QueryIntent.STOCK_ANALYSIS,
                confidence=0.9,
                symbols=symbols,
                processing_route=ProcessingRoute.FULL_PIPELINE,
                is_simple_price_query=False,
                extracted_text=query
            )
        
        # Check for simple price patterns
        is_simple_price_query = self._is_simple_price_query(query_clean, symbols)
        
        if is_simple_price_query and symbols:
            return QueryAnalysis(
                intent=QueryIntent.PRICE_CHECK,
                confidence=0.95,
                symbols=symbols,
                processing_route=ProcessingRoute.QUICK_RESPONSE,
                is_simple_price_query=True,
                extracted_text=query
            )
        
        # Check for help requests
        if any(word in query_clean for word in ["help", "command", "usage", "how to"]):
            return QueryAnalysis(
                intent=QueryIntent.HELP_REQUEST,
                confidence=0.8,
                symbols=[],
                processing_route=ProcessingRoute.FULL_PIPELINE,
                is_simple_price_query=False,
                extracted_text=query
            )
        
        # Default to general question with full pipeline
        return QueryAnalysis(
            intent=QueryIntent.GENERAL_QUESTION,
            confidence=0.5,
            symbols=symbols,
            processing_route=ProcessingRoute.FULL_PIPELINE,
            is_simple_price_query=False,
            extracted_text=query
        )

    def _extract_symbols(self, query: str) -> List[SymbolInfo]:
        """
        Extract stock/crypto symbols from query.
        
        Args:
            query: Query string
            
        Returns:
            List of SymbolInfo objects
        """
        symbols = []
        
        # Pattern for $SYMBOL format
        dollar_pattern = r'\$([A-Z]{1,5})'
        matches = re.finditer(dollar_pattern, query)
        
        for match in matches:
            symbol_text = match.group(1)
            start_pos, end_pos = match.span(1)
            symbols.append(SymbolInfo(
                text=symbol_text,
                confidence=0.95,
                position=(start_pos, end_pos)
            ))
        
        # Pattern for standalone uppercase symbols (more conservative)
        # Only match standalone words that look like stock symbols
        standalone_pattern = r'\b([A-Z]{1,5})\b'
        matches = re.finditer(standalone_pattern, query)
        
        for match in matches:
            symbol_text = match.group(1)
            # Only consider common symbols or those that are likely to be stock symbols
            if symbol_text in self.common_symbols or len(symbol_text) >= 2:
                start_pos, end_pos = match.span(1)
                # Check if this symbol is already captured by $SYMBOL pattern
                already_captured = any(
                    sym.position[0] <= start_pos <= sym.position[1] or 
                    sym.position[0] <= end_pos <= sym.position[1]
                    for sym in symbols
                )
                
                if not already_captured:
                    symbols.append(SymbolInfo(
                        text=symbol_text,
                        confidence=0.7,  # Lower confidence for standalone symbols
                        position=(start_pos, end_pos)
                    ))
        
        return symbols

    def _is_simple_price_query(self, query: str, symbols: List[SymbolInfo]) -> bool:
        """
        Determine if query is a simple price query.
        
        Args:
            query: Cleaned query string
            symbols: Extracted symbols
            
        Returns:
            True if simple price query, False otherwise
        """
        if not symbols:
            return False
            
        # Check for price-related keywords
        has_price_keyword = any(keyword in query for keyword in self.price_keywords)
        
        # Check for simple price patterns
        for pattern in self.simple_price_patterns:
            if re.search(pattern, query, re.IGNORECASE):
                return True
        
        # Special case: Just a symbol or symbol with "price"
        query_words = query.split()
        if len(query_words) <= 3:
            # Check if it's just a symbol
            if len(query_words) == 1 and symbols:
                return True
            # Check if it's "symbol price" or "price symbol"
            elif len(query_words) == 2:
                symbol_word = next((sym.text.lower() for sym in symbols), "")
                if symbol_word in query_words and ("price" in query_words or "value" in query_words):
                    return True
        
        return False

# Global analyzer instance
query_analyzer = SimpleQueryAnalyzer()

# Convenience function
def analyze_simple_query(query: str, context: Optional[Dict[str, Any]] = None) -> QueryAnalysis:
    """Analyze a query for simple price detection"""
    return query_analyzer.analyze_query(query, context)