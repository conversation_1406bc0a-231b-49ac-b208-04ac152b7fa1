"""
Local Fallback AI Service
========================

Provides intelligent fallback responses when external AI services are unavailable.
Uses pattern matching, heuristics, and local processing to maintain functionality.
"""

import re
import logging
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from datetime import datetime # Added datetime import for consistency

logger = logging.getLogger(__name__)

@dataclass
class LocalAIResponse:
    """Response from local AI fallback"""
    symbols: List[str]
    confidence: float
    method: str
    reasoning: str

class LocalFallbackAI:
    """Local AI fallback for symbol extraction and basic analysis"""
    
    def __init__(self):
        # Enhanced company to ticker mappings with broader coverage
        self.company_mappings = {
            # Major tech companies
            'apple': 'AAPL', 'microsoft': 'MSFT', 'google': 'GOOGL', 'alphabet': 'GOOGL',
            'amazon': 'AMZN', 'meta': 'META', 'facebook': 'META', 'netflix': 'NFLX',
            'tesla': 'TSLA', 'nvidia': 'NVDA', 'amd': 'AMD', 'intel': 'INTC',
            'cisco': 'CSCO', 'oracle': 'ORCL', 'ibm': 'IBM', 'salesforce': 'CRM',
            'adobe': 'ADBE', 'shopify': 'SHOP', 'snowflake': 'SNOW', 'zoom': 'ZM',

            # Automotive companies
            'ford': 'F', 'general motors': 'GM', 'gm': 'GM', 'toyota': 'TM',
            'volkswagen': 'VWAGY', 'bmw': 'BMWYY', 'mercedes': 'DDAIF',
            'honda': 'HMC', 'hyundai': 'HYMTF', 'tesla': 'TSLA',

            # Financial services
            'jpmorgan': 'JPM', 'jp morgan': 'JPM', 'bank of america': 'BAC',
            'wells fargo': 'WFC', 'goldman sachs': 'GS', 'morgan stanley': 'MS',
            'visa': 'V', 'mastercard': 'MA', 'paypal': 'PYPL', 'american express': 'AXP',
            'citigroup': 'C', 'hsbc': 'HSBC', 'blackrock': 'BLK', 'fidelity': 'FIS',

            # Other major companies
            'berkshire hathaway': 'BRK.B', 'johnson & johnson': 'JNJ', 'procter & gamble': 'PG',
            'coca cola': 'KO', 'pepsi': 'PEP', 'walmart': 'WMT', 'home depot': 'HD',
            'disney': 'DIS', 'nike': 'NKE', 'mcdonald': 'MCD', 'starbucks': 'SBUX',
            'costco': 'COST', 'target': 'TGT', 'best buy': 'BBY', 'lowes': 'LOW',
            'pfizer': 'PFE', 'moderna': 'MRNA', 'johnson and johnson': 'JNJ',

            # ETFs and indices
            'spy': 'SPY', 's&p 500': 'SPY', 'sp500': 'SPY', 'sp 500': 'SPY',
            'qqq': 'QQQ', 'nasdaq': 'QQQ', 'nasdaq 100': 'QQQ',
            'vti': 'VTI', 'voo': 'VOO', 'iwm': 'IWM', 'russell 2000': 'IWM',
            'dia': 'DIA', 'dow jones': 'DIA', 'arkk': 'ARKK', 'ark': 'ARKK',
            'vug': 'VUG', 'schd': 'SCHD', 'vxus': 'VXUS', 'vea': 'VEA',

            # Crypto and digital assets (for completeness)
            'bitcoin': 'BTC-USD', 'ethereum': 'ETH-USD', 'btc': 'BTC-USD', 'eth': 'ETH-USD'
        }
        
        # Stock-related keywords for context detection
        self.stock_keywords = {
            'price', 'stock', 'ticker', 'share', 'shares', 'market', 'trading', 'trade',
            'invest', 'investment', 'buy', 'sell', 'portfolio', 'earnings', 'dividend',
            'analysis', 'chart', 'performance', 'compare', 'vs', 'versus', 'financial',
            'quote', 'value', 'valuation', 'revenue', 'profit', 'growth', 'forecast',
            'doing', 'performing', 'about'  # Added common query words
        }
        
        # Common non-ticker words to filter out
        self.non_tickers = {
            'THE', 'AND', 'FOR', 'ARE', 'BUT', 'NOT', 'YOU', 'ALL', 'CAN', 'HAD',
            'HER', 'WAS', 'ONE', 'OUR', 'OUT', 'DAY', 'GET', 'HAS', 'HIM', 'HIS',
            'HOW', 'ITS', 'MAY', 'NEW', 'NOW', 'OLD', 'SEE', 'TWO', 'WHO', 'BOY',
            'DID', 'LET', 'PUT', 'SAY', 'SHE', 'TOO', 'USE', 'WAY', 'WHY', 'WHAT',
            'WHEN', 'WHERE', 'WILL', 'WITH', 'WOULD', 'COULD', 'SHOULD', 'MIGHT',
            'MUST', 'THAN', 'THEN', 'THEM', 'THEY', 'THIS', 'THAT', 'THESE', 'THOSE'
        }
    
    async def extract_symbols(self, text: str) -> LocalAIResponse:
        """Extract symbols using local intelligence with enhanced reasoning"""
        if not text or not text.strip():
            return LocalAIResponse([], 0.0, "empty_input", "No text provided")

        symbols = []
        methods_used = []
        reasoning_parts = []
        analysis_type = self._classify_query_type(text)

        # 1. Extract dollar-prefixed symbols (highest confidence)
        dollar_symbols = self._extract_dollar_symbols(text)
        if dollar_symbols:
            symbols.extend(dollar_symbols)
            methods_used.append("dollar_prefix")
            reasoning_parts.append(f"Found ${', $'.join(dollar_symbols)} with dollar prefix")

        # 2. Extract from company name mappings (high confidence)
        company_symbols = self._extract_company_symbols(text)
        if company_symbols:
            symbols.extend(company_symbols)
            methods_used.append("company_mapping")
            reasoning_parts.append(f"Mapped company names to {', '.join(company_symbols)}")

        # 3. Extract validated ticker symbols if query seems stock-focused
        if self._is_stock_focused_query(text):
            ticker_symbols = self._extract_ticker_symbols(text)
            if ticker_symbols:
                symbols.extend(ticker_symbols)
                methods_used.append("ticker_validation")
                reasoning_parts.append(f"Found validated tickers {', '.join(ticker_symbols)}")

        # 4. Enhanced context-aware extraction for comparisons
        if ' vs ' in text.lower() or ' versus ' in text.lower() or ' compared to ' in text.lower():
            comparison_symbols = self._extract_comparison_symbols(text)
            if comparison_symbols:
                symbols.extend(comparison_symbols)
                methods_used.append("comparison_extraction")
                reasoning_parts.append(f"Extracted symbols from comparison context: {', '.join(comparison_symbols)}")

        # Remove duplicates while preserving order
        unique_symbols = []
        seen = set()
        for symbol in symbols:
            if symbol not in seen:
                unique_symbols.append(symbol)
                seen.add(symbol)

        # Calculate confidence based on methods used, context, and query type
        confidence = self._calculate_enhanced_confidence(unique_symbols, methods_used, text, analysis_type)

        # Generate comprehensive reasoning
        reasoning = self._generate_detailed_reasoning(unique_symbols, methods_used, reasoning_parts, analysis_type, text)

        return LocalAIResponse(
            symbols=unique_symbols,
            confidence=confidence,
            method="+".join(methods_used) if methods_used else "no_extraction",
            reasoning=reasoning
        )

    def _classify_query_type(self, text: str) -> str:
        """Classify the type of query for better symbol extraction"""
        text_lower = text.lower()

        # Analysis queries
        if any(word in text_lower for word in ['analysis', 'analyze', 'technical', 'chart', 'rsi', 'macd', 'moving average']):
            return 'analysis'

        # Comparison queries
        if any(phrase in text_lower for phrase in [' vs ', ' versus ', ' compared to ', 'compare', 'comparison']):
            return 'comparison'

        # Price/lookup queries
        if any(word in text_lower for word in ['price', 'quote', 'how much', 'cost', 'value', 'doing']):
            return 'price_lookup'

        # General queries
        return 'general'

    def _extract_comparison_symbols(self, text: str) -> List[str]:
        """Extract symbols from comparison queries like 'AAPL vs MSFT'"""
        symbols = []

        # Look for patterns like "SYMBOL vs SYMBOL"
        comparison_patterns = [
            r'\b([A-Z]{2,5})\s+vs?\s+([A-Z]{2,5})\b',
            r'\b([A-Z]{2,5})\s+(?:versus|compared to|against)\s+([A-Z]{2,5})\b',
            r'\b([A-Z]{2,5})\s+and\s+([A-Z]{2,5})\b'
        ]

        for pattern in comparison_patterns:
            matches = re.findall(pattern, text.upper())
            for match in matches:
                for symbol in match:
                    if self._is_valid_ticker_format(symbol):
                        symbols.append(symbol)

        # Also try company name mapping for comparisons
        for company, ticker in self.company_mappings.items():
            if company in text.lower():
                symbols.append(ticker)

        return list(set(symbols))  # Remove duplicates

    def _calculate_enhanced_confidence(self, symbols: List[str], methods: List[str], text: str, analysis_type: str) -> float:
        """Calculate confidence with enhanced logic based on query type and context"""
        if not symbols:
            return 0.0

        base_confidence = self._calculate_confidence(symbols, methods, text)

        # Boost confidence for specific query types
        if analysis_type == 'analysis':
            base_confidence = min(base_confidence + 0.15, 1.0)
        elif analysis_type == 'comparison':
            base_confidence = min(base_confidence + 0.20, 1.0)
        elif analysis_type == 'price_lookup':
            base_confidence = min(base_confidence + 0.10, 1.0)

        # Boost for multi-method extraction
        if len(methods) > 1:
            base_confidence = min(base_confidence + 0.05, 1.0)

        return round(base_confidence, 2)

    def _generate_detailed_reasoning(self, symbols: List[str], methods: List[str], reasoning_parts: List[str],
                                   analysis_type: str, original_text: str) -> str:
        """Generate detailed reasoning about symbol extraction process"""
        if not symbols:
            if analysis_type == 'analysis':
                return f"Query appears to be about technical analysis but no specific symbols were identified in: '{original_text[:50]}...'"
            elif analysis_type == 'comparison':
                return f"Comparison query detected but no clear symbols found in: '{original_text[:50]}...'"
            else:
                return f"No symbols identified in query: '{original_text[:50]}...'"

        if reasoning_parts:
            reasoning = "; ".join(reasoning_parts)
        else:
            reasoning = "Symbols extracted through pattern matching"

        # Add analysis type context
        if analysis_type == 'analysis':
            reasoning += f"; Query classified as analysis type"
        elif analysis_type == 'comparison':
            reasoning += f"; Query classified as comparison type"
        elif analysis_type == 'price_lookup':
            reasoning += f"; Query classified as price lookup type"

        return reasoning
    
    def _extract_dollar_symbols(self, text: str) -> List[str]:
        """Extract symbols with $ prefix"""
        pattern = r'\$([A-Z]{1,10})\b'
        matches = re.findall(pattern, text)
        return [match for match in matches if self._is_valid_ticker_format(match)]
    
    def _extract_company_symbols(self, text: str) -> List[str]:
        """Extract symbols by mapping company names"""
        text_lower = text.lower()
        found_symbols = []
        
        for company, ticker in self.company_mappings.items():
            if company in text_lower:
                # Make sure it's a word boundary match
                pattern = r'\b' + re.escape(company) + r'\b'
                if re.search(pattern, text_lower):
                    found_symbols.append(ticker)
        
        return found_symbols
    
    def _extract_ticker_symbols(self, text: str) -> List[str]:
        """Extract potential ticker symbols with validation"""
        # Pattern for standalone uppercase words (case-insensitive)
        pattern = r'\b([A-Za-z]{2,5})\b'
        potential_tickers = re.findall(pattern, text)

        validated_tickers = []
        for ticker in potential_tickers:
            ticker_upper = ticker.upper()
            if (self._is_valid_ticker_format(ticker_upper) and
                ticker_upper not in self.non_tickers and
                self._looks_like_ticker(ticker_upper)):
                validated_tickers.append(ticker_upper)

        return validated_tickers
    
    def _is_stock_focused_query(self, text: str) -> bool:
        """Determine if query is about stocks/financial topics"""
        text_lower = text.lower()
        
        # Check for stock-related keywords
        stock_keyword_count = sum(1 for keyword in self.stock_keywords if keyword in text_lower)
        
        # Check for financial question patterns
        financial_patterns = [
            r'\bprice of\b', r'\bstock price\b', r'\bhow.*doing\b', r'\bperformance\b',
            r'\bcompare.*and\b', r'\banalysis\b', r'\bearnings\b', r'\bdividend\b',
            r'\bmarket\b', r'\btrade\b', r'\binvest\b', r'\bbuy.*sell\b'
        ]
        
        pattern_matches = sum(1 for pattern in financial_patterns if re.search(pattern, text_lower))
        
        # Query is stock-focused if it has stock keywords or financial patterns
        return stock_keyword_count >= 1 or pattern_matches >= 1
    
    def _is_valid_ticker_format(self, symbol: str) -> bool:
        """Check if symbol has valid ticker format"""
        if not symbol or len(symbol) < 1 or len(symbol) > 10:
            return False
        return symbol.isalpha() and symbol.isupper()
    
    def _looks_like_ticker(self, symbol: str) -> bool:
        """Heuristic to determine if a symbol looks like a ticker"""
        # Length check (most tickers are 2-5 characters)
        if len(symbol) < 2 or len(symbol) > 5:
            return False
        
        # Check for common ticker patterns
        # - Contains consonants (not all vowels)
        # - Not a common English word
        vowels = set('AEIOU')
        consonants = set(symbol) - vowels
        
        # Must have at least one consonant
        if not consonants:
            return False
        
        # Common short words that aren't tickers
        common_words = {
            'THE', 'AND', 'FOR', 'ARE', 'BUT', 'NOT', 'YOU', 'ALL', 'CAN', 'HAD',
            'WAS', 'ONE', 'OUR', 'OUT', 'DAY', 'GET', 'HAS', 'HIM', 'HIS', 'HOW',
            'ITS', 'MAY', 'NEW', 'NOW', 'OLD', 'SEE', 'TWO', 'WHO', 'BOY', 'DID',
            'LET', 'PUT', 'SAY', 'SHE', 'TOO', 'USE', 'WAY', 'WHY', 'YES', 'YET'
        }
        
        return symbol not in common_words
    
    def _calculate_confidence(self, symbols: List[str], methods: List[str], text: str) -> float:
        """Calculate confidence score for extraction"""
        if not symbols:
            return 0.0
        
        base_confidence = 0.0
        
        # Confidence based on extraction methods
        if "dollar_prefix" in methods:
            base_confidence = max(base_confidence, 0.95)
        if "company_mapping" in methods:
            base_confidence = max(base_confidence, 0.85)
        if "ticker_validation" in methods:
            base_confidence = max(base_confidence, 0.70)
        
        # Boost confidence if query is clearly stock-focused
        if self._is_stock_focused_query(text):
            base_confidence = min(base_confidence + 0.1, 1.0)
        
        # Reduce confidence if too many symbols found (might be false positives)
        if len(symbols) > 5:
            base_confidence *= 0.8
        
        return round(base_confidence, 2)

# Global instance
local_fallback_ai = LocalFallbackAI()

# Convenience function
async def extract_symbols_locally(text: str) -> List[str]:
    """Extract symbols using local AI fallback"""
    response = await local_fallback_ai.extract_symbols(text)
    return response.symbols