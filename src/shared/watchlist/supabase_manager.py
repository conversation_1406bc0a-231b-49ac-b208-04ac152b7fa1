"""
Supabase Manager for Watchlist Operations

Provides database operations for watchlist management using Supabase.
This is a simplified implementation for watchlist functionality.
"""

import logging
import os
from typing import Dict, Any, List, Optional
from datetime import datetime
import json

logger = logging.getLogger(__name__)

class SupabaseWatchlistManager:
    """Manages watchlist operations with Supabase database"""
    
    def __init__(self, supabase_url: str = None, supabase_key: str = None):
        """
        Initialize the Supabase watchlist manager.
        
        Args:
            supabase_url: Supabase project URL
            supabase_key: Supabase anon key
        """
        self.supabase_url = supabase_url or os.getenv('SUPABASE_URL')
        self.supabase_key = supabase_key or os.getenv('SUPABASE_KEY')
        self.client = None
        self._initialized = False
        
        if self.supabase_url and self.supabase_key:
            try:
                self._initialize_client()
            except Exception as e:
                logger.warning(f"⚠️ Failed to initialize Supabase client: {e}")
    
    def _initialize_client(self):
        """Initialize Supabase client"""
        try:
            from supabase import create_client, Client
            self.client: Client = create_client(self.supabase_url, self.supabase_key)
            self._initialized = True
            logger.info("✅ Supabase client initialized for watchlist")
        except ImportError:
            logger.warning("⚠️ Supabase package not available, using fallback storage")
            self._initialized = False
        except Exception as e:
            logger.error(f"❌ Failed to initialize Supabase client: {e}")
            self._initialized = False
    
    async def get_watchlist(self, user_id: str) -> Optional[Dict[str, Any]]:
        """Get user's watchlist from database"""
        try:
            if not self._initialized:
                return await self._get_fallback_watchlist(user_id)
            
            # Query Supabase for user's watchlist
            response = self.client.table('watchlists').select('*').eq('user_id', user_id).single().execute()
            
            if response.data:
                return {
                    'user_id': user_id,
                    'symbols': response.data.get('symbols', []),
                    'created_at': response.data.get('created_at'),
                    'updated_at': response.data.get('updated_at'),
                    'metadata': response.data.get('metadata', {})
                }
            else:
                return None
                
        except Exception as e:
            logger.error(f"❌ Failed to get watchlist for user {user_id}: {e}")
            return await self._get_fallback_watchlist(user_id)
    
    async def create_watchlist(self, user_id: str, symbols: List[str], metadata: Dict[str, Any] = None) -> bool:
        """Create a new watchlist for user"""
        try:
            if not self._initialized:
                return await self._create_fallback_watchlist(user_id, symbols, metadata)
            
            now = datetime.now().isoformat()
            data = {
                'user_id': user_id,
                'symbols': symbols,
                'created_at': now,
                'updated_at': now,
                'metadata': metadata or {}
            }
            
            response = self.client.table('watchlists').insert(data).execute()
            
            if response.data:
                logger.info(f"✅ Created watchlist for user {user_id}")
                return True
            else:
                logger.error(f"❌ Failed to create watchlist for user {user_id}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Failed to create watchlist for user {user_id}: {e}")
            return await self._create_fallback_watchlist(user_id, symbols, metadata)
    
    async def update_watchlist(self, user_id: str, symbols: List[str], metadata: Dict[str, Any] = None) -> bool:
        """Update user's watchlist"""
        try:
            if not self._initialized:
                return await self._update_fallback_watchlist(user_id, symbols, metadata)
            
            now = datetime.now().isoformat()
            data = {
                'symbols': symbols,
                'updated_at': now,
                'metadata': metadata or {}
            }
            
            response = self.client.table('watchlists').update(data).eq('user_id', user_id).execute()
            
            if response.data:
                logger.info(f"✅ Updated watchlist for user {user_id}")
                return True
            else:
                # Try to create if update failed (watchlist doesn't exist)
                return await self.create_watchlist(user_id, symbols, metadata)
                
        except Exception as e:
            logger.error(f"❌ Failed to update watchlist for user {user_id}: {e}")
            return await self._update_fallback_watchlist(user_id, symbols, metadata)
    
    async def add_symbol(self, user_id: str, symbol: str) -> bool:
        """Add a symbol to user's watchlist"""
        try:
            watchlist = await self.get_watchlist(user_id)
            if not watchlist:
                # Create new watchlist with the symbol
                return await self.create_watchlist(user_id, [symbol])
            
            symbols = watchlist.get('symbols', [])
            if symbol.upper() not in [s.upper() for s in symbols]:
                symbols.append(symbol.upper())
                return await self.update_watchlist(user_id, symbols)
            
            logger.info(f"ℹ️ Symbol {symbol} already in watchlist for user {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to add symbol {symbol} to watchlist for user {user_id}: {e}")
            return False
    
    async def remove_symbol(self, user_id: str, symbol: str) -> bool:
        """Remove a symbol from user's watchlist"""
        try:
            watchlist = await self.get_watchlist(user_id)
            if not watchlist:
                logger.warning(f"⚠️ No watchlist found for user {user_id}")
                return False
            
            symbols = watchlist.get('symbols', [])
            symbol_upper = symbol.upper()
            filtered_symbols = [s for s in symbols if s.upper() != symbol_upper]
            
            if len(filtered_symbols) < len(symbols):
                return await self.update_watchlist(user_id, filtered_symbols)
            else:
                logger.info(f"ℹ️ Symbol {symbol} not found in watchlist for user {user_id}")
                return True
                
        except Exception as e:
            logger.error(f"❌ Failed to remove symbol {symbol} from watchlist for user {user_id}: {e}")
            return False
    
    async def get_watchlist_symbols(self, user_id: str) -> List[str]:
        """Get just the symbols from user's watchlist"""
        watchlist = await self.get_watchlist(user_id)
        return watchlist.get('symbols', []) if watchlist else []
    
    async def watchlist_exists(self, user_id: str) -> bool:
        """Check if user has a watchlist"""
        watchlist = await self.get_watchlist(user_id)
        return watchlist is not None
    
    # Fallback methods for when Supabase is not available
    async def _get_fallback_watchlist(self, user_id: str) -> Optional[Dict[str, Any]]:
        """Get watchlist from local file storage"""
        try:
            import os
            watchlist_file = f"data/watchlists/{user_id}_watchlist.json"
            
            if os.path.exists(watchlist_file):
                with open(watchlist_file, 'r') as f:
                    data = json.load(f)
                    return {
                        'user_id': user_id,
                        'symbols': data.get('symbols', []),
                        'created_at': data.get('created_at'),
                        'updated_at': data.get('updated_at'),
                        'metadata': data.get('metadata', {})
                    }
            return None
            
        except Exception as e:
            logger.error(f"❌ Fallback watchlist read failed for user {user_id}: {e}")
            return None
    
    async def _create_fallback_watchlist(self, user_id: str, symbols: List[str], metadata: Dict[str, Any] = None) -> bool:
        """Create watchlist in local file storage"""
        try:
            import os
            os.makedirs('data/watchlists', exist_ok=True)
            
            watchlist_file = f"data/watchlists/{user_id}_watchlist.json"
            now = datetime.now().isoformat()
            
            data = {
                'user_id': user_id,
                'symbols': symbols,
                'created_at': now,
                'updated_at': now,
                'metadata': metadata or {}
            }
            
            with open(watchlist_file, 'w') as f:
                json.dump(data, f, indent=2)
            
            logger.info(f"✅ Created fallback watchlist for user {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Fallback watchlist creation failed for user {user_id}: {e}")
            return False
    
    async def _update_fallback_watchlist(self, user_id: str, symbols: List[str], metadata: Dict[str, Any] = None) -> bool:
        """Update watchlist in local file storage"""
        try:
            watchlist_file = f"data/watchlists/{user_id}_watchlist.json"
            
            if os.path.exists(watchlist_file):
                with open(watchlist_file, 'r') as f:
                    existing_data = json.load(f)
            else:
                existing_data = {}
            
            now = datetime.now().isoformat()
            data = {
                'user_id': user_id,
                'symbols': symbols,
                'created_at': existing_data.get('created_at', now),
                'updated_at': now,
                'metadata': metadata or existing_data.get('metadata', {})
            }
            
            with open(watchlist_file, 'w') as f:
                json.dump(data, f, indent=2)
            
            logger.info(f"✅ Updated fallback watchlist for user {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Fallback watchlist update failed for user {user_id}: {e}")
            return False

# Global instance
_supabase_manager: Optional[SupabaseWatchlistManager] = None

def get_supabase_manager() -> SupabaseWatchlistManager:
    """Get global Supabase watchlist manager instance"""
    global _supabase_manager
    if _supabase_manager is None:
        _supabase_manager = SupabaseWatchlistManager()
    return _supabase_manager

def cleanup_supabase_manager():
    """Cleanup global Supabase manager"""
    global _supabase_manager
    _supabase_manager = None