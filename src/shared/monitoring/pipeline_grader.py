"""
Pipeline Performance Monitoring and Grading System

Enhanced pipeline grading system that integrates with observability:
- Track pipeline execution performance and quality
- Grade individual steps and overall pipeline execution
- Integrate with observability for comprehensive monitoring
- Generate performance reports and alerts
- Support for pipeline optimization recommendations
"""

import time
import logging
import statistics
from enum import Enum
from typing import Dict, List, Any, Optional, Callable, Union, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import json
import os
from pathlib import Path

from src.shared.error_handling.logging import get_logger
from src.shared.monitoring.observability import observability_manager

logger = get_logger(__name__)

class GradeLevel(Enum):
    """Grade levels for pipeline steps and overall pipeline execution."""
    A_PLUS = "A+"
    A = "A"
    A_MINUS = "A-"
    B_PLUS = "B+"
    B = "B"
    B_MINUS = "B-"
    C_PLUS = "C+"
    C = "C"
    C_MINUS = "C-"
    D_PLUS = "D+"
    D = "D"
    D_MINUS = "D-"
    F = "F"

    @classmethod
    def from_score(cls, score: float) -> 'GradeLevel':
        """Convert a numeric score (0-100) to a letter grade."""
        if score >= 97:
            return cls.A_PLUS
        elif score >= 93:
            return cls.A
        elif score >= 90:
            return cls.A_MINUS
        elif score >= 87:
            return cls.B_PLUS
        elif score >= 83:
            return cls.B
        elif score >= 80:
            return cls.B_MINUS
        elif score >= 77:
            return cls.C_PLUS
        elif score >= 73:
            return cls.C
        elif score >= 70:
            return cls.C_MINUS
        elif score >= 67:
            return cls.D_PLUS
        elif score >= 63:
            return cls.D
        elif score >= 60:
            return cls.D_MINUS
        else:
            return cls.F

@dataclass
class StepGrade:
    """Detailed grade for a single pipeline step with observability integration."""
    step_name: str
    execution_time: float
    success: bool
    error_message: Optional[str] = None
    data_quality_score: Optional[float] = None
    performance_score: Optional[float] = None
    reliability_score: Optional[float] = None
    overall_score: Optional[float] = None
    grade: Optional[GradeLevel] = None
    metrics: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
    correlation_id: Optional[str] = None
    observability_id: Optional[str] = None

    def calculate_overall_score(self) -> float:
        """Calculate the overall score based on component scores with emphasis on response quality."""
        scores = []
        weights = []
        
        if self.data_quality_score is not None:
            scores.append(self.data_quality_score)
            weights.append(0.7)  # 70% weight for data quality (response quality)
            
        if self.performance_score is not None:
            scores.append(self.performance_score)
            weights.append(0.2)  # 20% weight for performance
            
        if self.reliability_score is not None:
            scores.append(self.reliability_score)
            weights.append(0.1)  # 10% weight for reliability
        
        # If no component scores are available, base on success
        if not scores:
            return 100.0 if self.success else 0.0
        
        # Calculate weighted average
        total_weight = sum(weights)
        weighted_sum = sum(score * weight for score, weight in zip(scores, weights))
        
        return weighted_sum / total_weight if total_weight > 0 else 0.0
    
    def assign_grade(self) -> GradeLevel:
        """Assign a letter grade based on the overall score."""
        if self.overall_score is None:
            self.overall_score = self.calculate_overall_score()
        
        return GradeLevel.from_score(self.overall_score)
    
    def finalize(self) -> 'StepGrade':
        """Calculate final scores and grades if not already done."""
        if self.overall_score is None:
            self.overall_score = self.calculate_overall_score()
        
        if self.grade is None:
            self.grade = self.assign_grade()
            
        return self

@dataclass
class PipelineGrade:
    """Overall grade for an entire pipeline execution with observability integration."""
    pipeline_id: str
    pipeline_name: str
    start_time: datetime
    end_time: Optional[datetime] = None
    execution_time: Optional[float] = None
    step_grades: List[StepGrade] = field(default_factory=list)
    overall_score: Optional[float] = None
    grade: Optional[GradeLevel] = None
    success_rate: Optional[float] = None
    avg_step_time: Optional[float] = None
    total_errors: int = 0
    metadata: Dict[str, Any] = field(default_factory=dict)
    correlation_id: Optional[str] = None
    observability_id: Optional[str] = None

    def add_step_grade(self, step_grade: StepGrade) -> None:
        """Add a step grade to the pipeline grade."""
        self.step_grades.append(step_grade)
        
        # Update error count
        if not step_grade.success:
            self.total_errors += 1
    
    def calculate_metrics(self) -> None:
        """Calculate aggregate metrics based on step grades."""
        if not self.step_grades:
            return
            
        # Calculate success rate
        successful_steps = sum(1 for grade in self.step_grades if grade.success)
        self.success_rate = (successful_steps / len(self.step_grades)) * 100
        
        # Calculate average step time
        step_times = [grade.execution_time for grade in self.step_grades]
        self.avg_step_time = statistics.mean(step_times) if step_times else 0
        
        # Ensure all step grades are finalized
        for step in self.step_grades:
            step.finalize()
    
    def calculate_overall_score(self) -> float:
        """Calculate the overall pipeline score with emphasis on response quality."""
        if not self.step_grades:
            return 0.0
            
        # Calculate metrics if not already done
        if self.success_rate is None:
            self.calculate_metrics()
        
        # Component scores with weights - prioritize response quality over technical metrics
        components = [
            (self.success_rate or 0, 0.2),  # 20% weight for success rate
            (100 - min(100, self.total_errors * 10), 0.1),  # 10% weight for error count
        ]
        
        # Add average step score if available - this now includes response quality
        step_scores = [step.overall_score for step in self.step_grades if step.overall_score is not None]
        if step_scores:
            avg_step_score = statistics.mean(step_scores)
            components.append((avg_step_score, 0.7))  # 70% weight for step scores
        
        # Calculate weighted average
        weighted_sum = sum(score * weight for score, weight in components)
        total_weight = sum(weight for _, weight in components)
        
        return weighted_sum / total_weight if total_weight > 0 else 0.0
    
    def assign_grade(self) -> GradeLevel:
        """Assign a letter grade based on the overall score."""
        if self.overall_score is None:
            self.overall_score = self.calculate_overall_score()
        
        return GradeLevel.from_score(self.overall_score)
    
    def finalize(self) -> 'PipelineGrade':
        """Finalize the pipeline grade by calculating all metrics and scores."""
        if self.end_time is None:
            self.end_time = datetime.now()
            
        if self.execution_time is None and self.start_time and self.end_time:
            self.execution_time = (self.end_time - self.start_time).total_seconds()
        
        self.calculate_metrics()
        
        if self.overall_score is None:
            self.overall_score = self.calculate_overall_score()
            
        if self.grade is None:
            self.grade = self.assign_grade()
            
        return self
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert the pipeline grade to a dictionary."""
        result = {
            "pipeline_id": self.pipeline_id,
            "pipeline_name": self.pipeline_name,
            "start_time": self.start_time.isoformat(),
            "end_time": self.end_time.isoformat() if self.end_time else None,
            "execution_time": self.execution_time,
            "overall_score": self.overall_score,
            "grade": self.grade.value if self.grade else None,
            "success_rate": self.success_rate,
            "avg_step_time": self.avg_step_time,
            "total_errors": self.total_errors,
            "metadata": self.metadata,
            "correlation_id": self.correlation_id,
            "observability_id": self.observability_id,
            "steps": []
        }
        
        for step in self.step_grades:
            step_dict = {
                "step_name": step.step_name,
                "execution_time": step.execution_time,
                "success": step.success,
                "error_message": step.error_message,
                "data_quality_score": step.data_quality_score,
                "performance_score": step.performance_score,
                "reliability_score": step.reliability_score,
                "overall_score": step.overall_score,
                "grade": step.grade.value if step.grade else None,
                "metrics": step.metrics,
                "timestamp": step.timestamp.isoformat(),
                "correlation_id": step.correlation_id,
                "observability_id": step.observability_id
            }
            result["steps"].append(step_dict)
            
        return result
    
    def save_to_file(self, directory: str = "logs/pipeline_grades") -> str:
        """Save the pipeline grade to a JSON file with observability integration."""
        try:
            os.makedirs(directory, exist_ok=True)
            
            filename = f"{self.pipeline_name}_{self.pipeline_id}_{self.start_time.strftime('%Y%m%d_%H%M%S')}.json"
            filepath = os.path.join(directory, filename)
            
            with open(filepath, 'w') as f:
                json.dump(self.to_dict(), f, indent=2)
                
            # Log to observability
            observability_manager.record_metric(
                name="pipeline_grade_saved",
                value=1,
                metric_type=MetricType.COUNTER,
                tags={
                    'pipeline': self.pipeline_name,
                    'grade': self.grade.value if self.grade else 'N/A',
                    'success_rate': str(self.success_rate) if self.success_rate else '0'
                }
            )
            
            logger.info(f"Pipeline grade saved to {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"Error saving pipeline grade: {e}")
            raise

class PipelineGrader:
    """
    Enhanced pipeline grading system that integrates with observability and monitoring.
    """

    def __init__(self, 
                 pipeline_name: str, 
                 save_grades: bool = True,
                 grade_directory: str = "logs/pipeline_grades",
                 correlation_id: Optional[str] = None):
        """
        Initialize a new pipeline grader with observability integration.
        
        Args:
            pipeline_name: Name of the pipeline being graded
            save_grades: Whether to automatically save grades to disk
            grade_directory: Directory to save grade files
            correlation_id: Correlation ID for observability tracking
        """
        self.pipeline_name = pipeline_name
        self.pipeline_id = f"{pipeline_name}_{int(time.time())}_{uuid.uuid4().hex[:8]}"
        self.start_time = datetime.now()
        self.save_grades = save_grades
        self.grade_directory = grade_directory
        self.step_grades: List[StepGrade] = []
        self.current_step_start: Optional[float] = None
        self.current_step_name: Optional[str] = None
        self.metadata: Dict[str, Any] = {}
        self.correlation_id = correlation_id or str(uuid.uuid4())
        self.observability_id = observability_manager.start_operation(
            operation_name=f"pipeline_grading_{pipeline_name}",
            correlation_id=self.correlation_id,
            metadata={'pipeline_id': self.pipeline_id}
        )
        
        # Initialize pipeline grade
        self.pipeline_grade = PipelineGrade(
            pipeline_id=self.pipeline_id,
            pipeline_name=pipeline_name,
            start_time=self.start_time,
            correlation_id=self.correlation_id,
            observability_id=self.observability_id
        )
        
        logger.info(f"Started grading pipeline: {pipeline_name} (ID: {self.pipeline_id})")
    
    def start_step(self, step_name: str, correlation_id: Optional[str] = None) -> None:
        """
        Start timing a new pipeline step with observability tracking.
        
        Args:
            step_name: Name of the step being started
            correlation_id: Correlation ID for this step
        """
        self.current_step_name = step_name
        self.current_step_start = time.time()
        
        # Start observability tracking for this step
        step_correlation_id = correlation_id or self.correlation_id
        step_observability_id = observability_manager.start_operation(
            operation_name=f"pipeline_step_{step_name}",
            correlation_id=step_correlation_id,
            metadata={
                'pipeline_id': self.pipeline_id,
                'step_name': step_name
            }
        )
        
        logger.debug(f"Started pipeline step: {step_name} (correlation: {step_correlation_id})")
    
    def end_step(self,
                 success: bool = True,
                 error_message: Optional[str] = None,
                 data_quality_score: Optional[float] = None,
                 performance_score: Optional[float] = None,
                 reliability_score: Optional[float] = None,
                 metrics: Optional[Dict[str, Any]] = None,
                 correlation_id: Optional[str] = None) -> StepGrade:
        """
        End timing for the current step and record its grade with observability integration.
        
        Args:
            success: Whether the step completed successfully
            error_message: Error message if the step failed
            data_quality_score: Score for data quality (0-100)
            performance_score: Score for performance (0-100)
            reliability_score: Score for reliability (0-100)
            metrics: Additional metrics to record for this step
            correlation_id: Correlation ID for this step
        
        Returns:
            The grade for this step
        """
        if self.current_step_start is None or self.current_step_name is None:
            logger.warning(f"end_step called without active step. Creating mock step: {success}")
            if self.current_step_name is None:
                self.current_step_name = "unknown_step"
            if self.current_step_start is None:
                self.current_step_start = time.time() - 0.1  # 100ms ago

        execution_time = time.time() - self.current_step_start
        
        step_correlation_id = correlation_id or self.correlation_id
        
        step_grade = StepGrade(
            step_name=self.current_step_name,
            execution_time=execution_time,
            success=success,
            error_message=error_message,
            data_quality_score=data_quality_score,
            performance_score=performance_score,
            reliability_score=reliability_score,
            metrics=metrics or {},
            correlation_id=step_correlation_id,
            observability_id=observability_manager.start_operation(
                operation_name=f"grade_step_{self.current_step_name}",
                correlation_id=step_correlation_id,
                metadata={
                    'pipeline_id': self.pipeline_id,
                    'step_name': self.current_step_name
                }
            )
        )
        
        # Calculate scores and grade
        step_grade.finalize()
        
        # Add to pipeline grade
        self.pipeline_grade.add_step_grade(step_grade)
        
        # Log result with observability
        grade_str = step_grade.grade.value if step_grade.grade else "N/A"
        log_level = logging.INFO if success else logging.WARNING
        
        observability_manager.log_structured(
            level="INFO" if success else "WARNING",
            message=f"Step '{self.current_step_name}' completed in {execution_time:.2f}s with grade {grade_str}",
            correlation_id=step_correlation_id,
            component="pipeline_grader",
            operation=f"grade_step_{self.current_step_name}",
            metadata={
                'pipeline_id': self.pipeline_id,
                'step_name': self.current_step_name,
                'execution_time': execution_time,
                'success': success,
                'grade': grade_str,
                'overall_score': step_grade.overall_score
            },
            metrics={
                'execution_time': execution_time,
                'data_quality': step_grade.data_quality_score or 0,
                'performance': step_grade.performance_score or 0,
                'reliability': step_grade.reliability_score or 0,
                'overall_score': step_grade.overall_score or 0
            }
        )
        
        # End observability tracking for this step
        observability_manager.end_operation(
            operation_id=step_grade.observability_id,
            success=success,
            error_message=error_message
        )
        
        # Log to standard logger
        if success:
            logger.info(f"Step '{self.current_step_name}' completed in {execution_time:.2f}s with grade {grade_str}")
        else:
            logger.warning(f"Step '{self.current_step_name}' failed after {execution_time:.2f}s with grade {grade_str}: {error_message}")
        
        # Reset current step tracking
        self.current_step_name = None
        self.current_step_start = None
        
        return step_grade
    
    def add_metadata(self, key: str, value: Any) -> None:
        """
        Add metadata to the pipeline grade.
        
        Args:
            key: Metadata key
            value: Metadata value
        """
        self.pipeline_grade.metadata[key] = value
    
    def finalize_grade(self) -> PipelineGrade:
        """
        Finalize the pipeline grade and return the result with observability integration.
        
        Returns:
            The final pipeline grade
        """
        # Calculate execution time
        end_time = datetime.now()
        if isinstance(self.start_time, (int, float)):
            execution_time = time.time() - self.start_time
            start_time = datetime.fromtimestamp(self.start_time)
        else:
            execution_time = (end_time - self.start_time).total_seconds()
            start_time = self.start_time
        
        self.pipeline_grade.execution_time = execution_time
        
        # Finalize the grade
        self.pipeline_grade.finalize()
        
        # Log final result with observability
        grade_str = self.pipeline_grade.grade.value if self.pipeline_grade.grade else "N/A"
        observability_manager.log_structured(
            level="INFO",
            message=f"Pipeline '{self.pipeline_name}' completed in {execution_time:.2f}s with grade {grade_str}",
            correlation_id=self.correlation_id,
            component="pipeline_grader",
            operation="finalize_pipeline_grade",
            metadata={
                'pipeline_id': self.pipeline_id,
                'execution_time': execution_time,
                'success_rate': self.pipeline_grade.success_rate,
                'total_errors': self.pipeline_grade.total_errors,
                'overall_score': self.pipeline_grade.overall_score
            },
            metrics={
                'execution_time': execution_time,
                'success_rate': self.pipeline_grade.success_rate or 0,
                'avg_step_time': self.pipeline_grade.avg_step_time or 0,
                'overall_score': self.pipeline_grade.overall_score or 0
            }
        )
        
        # End observability tracking for pipeline
        observability_manager.end_operation(
            operation_id=self.observability_id,
            success=self.pipeline_grade.success_rate > 80 if self.pipeline_grade.success_rate else True
        )
        
        # Log to standard logger
        logger.info(f"Pipeline '{self.pipeline_name}' completed in {execution_time:.2f}s with grade {grade_str}")
        
        # Save to file if enabled
        if self.save_grades:
            filepath = self.pipeline_grade.save_to_file(self.grade_directory)
            logger.info(f"Pipeline grade saved to {filepath}")
            
        return self.pipeline_grade

# Example grading functions for common metrics with observability integration

def grade_data_quality(data: Any, 
                       required_fields: List[str] = None,
                       expected_length: Optional[int] = None,
                       validation_fn: Optional[Callable[[Any], bool]] = None,
                       correlation_id: Optional[str] = None) -> float:
    """
    Grade the quality of data produced by a pipeline step with observability tracking.
    
    Args:
        data: The data to grade
        required_fields: List of required fields in the data
        expected_length: Expected length of the data (for lists/arrays)
        validation_fn: Custom validation function
        correlation_id: Correlation ID for observability
        
    Returns:
        A score from 0-100
    """
    score = 100.0
    deductions = []
    
    # Check if data exists
    if data is None:
        observability_manager.record_metric(
            name="data_quality_check",
            value=0,
            metric_type=MetricType.GAUGE,
            tags={'check': 'data_exists', 'result': 'failed'},
            correlation_id=correlation_id
        )
        return 0.0
    
    # Check required fields
    if required_fields:
        if isinstance(data, dict):
            missing_fields = [field for field in required_fields if field not in data]
            if missing_fields:
                deduction = len(missing_fields) / len(required_fields) * 50
                deductions.append(deduction)
                observability_manager.record_metric(
                    name="data_quality_check",
                    value=deduction,
                    metric_type=MetricType.GAUGE,
                    tags={'check': 'required_fields', 'result': 'failed', 'missing_count': len(missing_fields)},
                    correlation_id=correlation_id
                )
                logger.debug(f"Missing required fields: {missing_fields}")
        else:
            deductions.append(50.0)
            observability_manager.record_metric(
                name="data_quality_check",
                value=50.0,
                metric_type=MetricType.GAUGE,
                tags={'check': 'required_fields', 'result': 'failed', 'reason': 'not_dict'},
                correlation_id=correlation_id
            )
            logger.debug("Data is not a dictionary, cannot check required fields")
    
    # Check expected length
    if expected_length is not None:
        if hasattr(data, '__len__'):
            actual_length = len(data)
            if actual_length != expected_length:
                # Deduct based on percentage difference
                diff_pct = abs(actual_length - expected_length) / expected_length
                deduction = min(40.0, diff_pct * 100)
                deductions.append(deduction)
                observability_manager.record_metric(
                    name="data_quality_check",
                    value=deduction,
                    metric_type=MetricType.GAUGE,
                    tags={
                        'check': 'expected_length',
                        'result': 'failed',
                        'expected': expected_length,
                        'actual': actual_length
                    },
                    correlation_id=correlation_id
                )
                logger.debug(f"Length mismatch: expected {expected_length}, got {actual_length}")
        else:
            deductions.append(30.0)
            observability_manager.record_metric(
                name="data_quality_check",
                value=30.0,
                metric_type=MetricType.GAUGE,
                tags={'check': 'expected_length', 'result': 'failed', 'reason': 'no_length'},
                correlation_id=correlation_id
            )
            logger.debug("Data has no length, cannot check expected length")
    
    # Run custom validation
    if validation_fn:
        try:
            if not validation_fn(data):
                deductions.append(40.0)
                observability_manager.record_metric(
                    name="data_quality_check",
                    value=40.0,
                    metric_type=MetricType.GAUGE,
                    tags={'check': 'custom_validation', 'result': 'failed'},
                    correlation_id=correlation_id
                )
                logger.debug("Custom validation failed")
        except Exception as e:
            deductions.append(50.0)
            observability_manager.record_metric(
                name="data_quality_check",
                value=50.0,
                metric_type=MetricType.GAUGE,
                tags={'check': 'custom_validation', 'result': 'error', 'error': str(e)},
                correlation_id=correlation_id
            )
            logger.debug(f"Custom validation error: {str(e)}")
    
    # Apply deductions
    for deduction in deductions:
        score -= deduction
    
    # Record final score
    observability_manager.record_metric(
        name="data_quality_score",
        value=score,
        metric_type=MetricType.GAUGE,
        tags={'final_score': score, 'deductions_count': len(deductions)},
        correlation_id=correlation_id
    )
    
    return max(0.0, score)

def grade_performance(execution_time: float,
                      target_time: float,
                      critical_threshold: Optional[float] = None,
                      correlation_id: Optional[str] = None) -> float:
    """
    Grade the performance of a pipeline step based on execution time with observability.
    
    Args:
        execution_time: Actual execution time in seconds
        target_time: Target execution time in seconds
        critical_threshold: Critical threshold in seconds (fails if exceeded)
        correlation_id: Correlation ID for observability
        
    Returns:
        A score from 0-100
    """
    # If critical threshold is exceeded, return 0
    if critical_threshold and execution_time > critical_threshold:
        observability_manager.record_metric(
            name="performance_check",
            value=0,
            metric_type=MetricType.GAUGE,
            tags={'check': 'critical_threshold', 'result': 'failed', 'threshold': critical_threshold},
            correlation_id=correlation_id
        )
        return 0.0
    
    # If execution time is below target, perfect score
    if execution_time <= target_time:
        observability_manager.record_metric(
            name="performance_check",
            value=100,
            metric_type=MetricType.GAUGE,
            tags={'check': 'target_time', 'result': 'passed', 'target': target_time},
            correlation_id=correlation_id
        )
        return 100.0
    
    # Calculate score based on how much the execution time exceeds the target
    ratio = target_time / execution_time
    score = ratio * 100
    
    observability_manager.record_metric(
        name="performance_check",
        value=score,
        metric_type=MetricType.GAUGE,
        tags={
            'check': 'target_time',
            'result': 'exceeded',
            'target': target_time,
            'actual': execution_time,
            'ratio': ratio
        },
        correlation_id=correlation_id
    )
    
    return max(0.0, min(100.0, score))

def grade_reliability(success: bool,
                      error_count: int = 0,
                      retry_count: int = 0,
                      max_retries: int = 3,
                      correlation_id: Optional[str] = None) -> float:
    """
    Grade the reliability of a pipeline step with observability tracking.
    
    Args:
        success: Whether the step completed successfully
        error_count: Number of errors encountered
        retry_count: Number of retries needed
        max_retries: Maximum allowed retries
        correlation_id: Correlation ID for observability
        
    Returns:
        A score from 0-100
    """
    if not success:
        observability_manager.record_metric(
            name="reliability_check",
            value=0,
            metric_type=MetricType.GAUGE,
            tags={'check': 'success', 'result': 'failed'},
            correlation_id=correlation_id
        )
        return 0.0
    
    score = 100.0
    
    # Deduct for errors
    if error_count > 0:
        deduction = min(70, error_count * 15)
        score -= deduction
        observability_manager.record_metric(
            name="reliability_check",
            value=deduction,
            metric_type=MetricType.GAUGE,
            tags={'check': 'error_count', 'result': 'deducted', 'errors': error_count},
            correlation_id=correlation_id
        )
    
    # Deduct for retries
    if retry_count > 0:
        retry_penalty = (retry_count / max_retries) * 30
        score -= retry_penalty
        observability_manager.record_metric(
            name="reliability_check",
            value=retry_penalty,
            metric_type=MetricType.GAUGE,
            tags={'check': 'retry_count', 'result': 'deducted', 'retries': retry_count},
            correlation_id=correlation_id
        )
    
    observability_manager.record_metric(
        name="reliability_score",
        value=score,
        metric_type=MetricType.GAUGE,
        tags={'final_score': score, 'error_count': error_count, 'retry_count': retry_count},
        correlation_id=correlation_id
    )
    
    return max(0.0, score)