"""
Performance Monitor
Tracks execution times and identifies performance bottlenecks
"""

import asyncio
import time
import logging
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from collections import defaultdict, deque

logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetric:
    """Performance metric data"""
    operation: str
    execution_time: float
    timestamp: datetime
    success: bool
    error: Optional[str] = None
    user_id: Optional[str] = None
    correlation_id: Optional[str] = None

class PerformanceMonitor:
    """Monitors and tracks performance metrics"""
    
    def __init__(self, max_metrics: int = 10000):
        self.max_metrics = max_metrics
        self.metrics: deque = deque(maxlen=max_metrics)
        self.operation_stats: Dict[str, List[float]] = defaultdict(list)
        self.lock = asyncio.Lock()
        
    async def record_metric(self, metric: PerformanceMetric) -> None:
        """Record a performance metric"""
        async with self.lock:
            self.metrics.append(metric)
            if metric.success:
                self.operation_stats[metric.operation].append(metric.execution_time)
                
                # Keep only last 1000 timings per operation
                if len(self.operation_stats[metric.operation]) > 1000:
                    self.operation_stats[metric.operation] = self.operation_stats[metric.operation][-1000:]
    
    def get_stats(self, operation: Optional[str] = None) -> Dict[str, Any]:
        """Get performance statistics"""
        stats = {}
        
        operations = [operation] if operation else list(self.operation_stats.keys())
        
        for op in operations:
            timings = self.operation_stats.get(op, [])
            if timings:
                stats[op] = {
                    "count": len(timings),
                    "avg_time": sum(timings) / len(timings),
                    "min_time": min(timings),
                    "max_time": max(timings),
                    "p50": self._percentile(timings, 50),
                    "p90": self._percentile(timings, 90),
                    "p95": self._percentile(timings, 95),
                    "p99": self._percentile(timings, 99)
                }
            else:
                stats[op] = {
                    "count": 0,
                    "avg_time": 0,
                    "min_time": 0,
                    "max_time": 0,
                    "p50": 0,
                    "p90": 0,
                    "p95": 0,
                    "p99": 0
                }
                
        return stats
    
    def _percentile(self, data: List[float], percentile: float) -> float:
        """Calculate percentile of data"""
        if not data:
            return 0.0
            
        sorted_data = sorted(data)
        index = int(len(sorted_data) * percentile / 100)
        return sorted_data[min(index, len(sorted_data) - 1)]
    
    def get_slow_operations(self, threshold: float = 5.0) -> List[Dict[str, Any]]:
        """Get operations that exceed the threshold time"""
        slow_ops = []
        
        for operation, timings in self.operation_stats.items():
            avg_time = sum(timings) / len(timings) if timings else 0
            if avg_time > threshold:
                slow_ops.append({
                    "operation": operation,
                    "avg_time": avg_time,
                    "count": len(timings),
                    "max_time": max(timings) if timings else 0
                })
                
        return sorted(slow_ops, key=lambda x: x["avg_time"], reverse=True)
    
    def get_recent_errors(self, hours: int = 1) -> List[PerformanceMetric]:
        """Get recent errors"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        return [m for m in self.metrics if not m.success and m.timestamp > cutoff_time]

# Global performance monitor instance
performance_monitor = PerformanceMonitor()

# Context manager for easy timing
class Timer:
    """Context manager for timing operations"""
    
    def __init__(self, operation: str, user_id: Optional[str] = None, 
                 correlation_id: Optional[str] = None):
        self.operation = operation
        self.user_id = user_id
        self.correlation_id = correlation_id
        self.start_time = None
        self.metric = None
        
    async def __aenter__(self):
        self.start_time = time.time()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        execution_time = time.time() - self.start_time
        success = exc_type is None
        error = str(exc_val) if exc_val else None
        
        self.metric = PerformanceMetric(
            operation=self.operation,
            execution_time=execution_time,
            timestamp=datetime.now(),
            success=success,
            error=error,
            user_id=self.user_id,
            correlation_id=self.correlation_id
        )
        
        await performance_monitor.record_metric(self.metric)
        
        # Log slow operations
        if execution_time > 5.0:  # Log operations taking more than 5 seconds
            logger.warning(f"SLOW OPERATION: {self.operation} took {execution_time:.2f}s")
            
        return False  # Don't suppress exceptions

# Convenience functions
async def record_performance_metric(metric: PerformanceMetric) -> None:
    """Record a performance metric"""
    await performance_monitor.record_metric(metric)

def get_performance_stats(operation: Optional[str] = None) -> Dict[str, Any]:
    """Get performance statistics"""
    return performance_monitor.get_stats(operation)

def get_slow_operations(threshold: float = 5.0) -> List[Dict[str, Any]]:
    """Get operations that exceed the threshold time"""
    return performance_monitor.get_slow_operations(threshold)

def get_recent_errors(hours: int = 1) -> List[PerformanceMetric]:
    """Get recent errors"""
    return performance_monitor.get_recent_errors(hours)