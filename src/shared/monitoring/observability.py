"""
Comprehensive Observability System for ASK Pipeline

This module provides a unified observability system that combines:
- Structured logging with correlation tracking
- Metrics collection and aggregation
- Pipeline performance grading
- Health monitoring and alerting
- Distributed tracing support
"""

import time
import logging
import asyncio
import json
import statistics
from enum import Enum
from typing import Dict, List, Any, Optional, Callable, Union, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import defaultdict, deque
import threading
import uuid

from src.shared.error_handling.logging import get_logger

logger = get_logger(__name__)

class MetricType(Enum):
    """Types of metrics that can be collected"""
    COUNTER = "counter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"
    TIMER = "timer"

class LogLevel(Enum):
    """Structured log levels"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"

@dataclass
class Metric:
    """Represents a single metric measurement"""
    name: str
    value: Union[int, float]
    timestamp: datetime
    tags: Dict[str, str] = field(default_factory=dict)
    metric_type: MetricType = MetricType.GAUGE

@dataclass
class StructuredLog:
    """Structured log entry with correlation tracking"""
    level: LogLevel
    message: str
    timestamp: datetime
    correlation_id: str
    component: str
    operation: str
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    metrics: Dict[str, Union[int, float]] = field(default_factory=dict)
    trace_id: Optional[str] = None
    span_id: Optional[str] = None

@dataclass
class PerformanceMetrics:
    """Aggregated performance metrics"""
    operation_name: str
    count: int = 0
    total_time: float = 0.0
    min_time: float = float('inf')
    max_time: float = 0.0
    avg_time: float = 0.0
    p95_time: float = 0.0
    p99_time: float = 0.0
    error_count: int = 0
    success_rate: float = 100.0
    samples: List[float] = field(default_factory=list)

class ObservabilityManager:
    """
    Comprehensive observability manager that handles logging, metrics, and monitoring
    """

    def __init__(self):
        self.metrics_store: Dict[str, PerformanceMetrics] = {}
        self.active_operations: Dict[str, Dict[str, Any]] = {}
        self.alerts: List[Dict[str, Any]] = []
        self.health_checks: Dict[str, Callable] = {}

        # Configuration
        self.max_samples_per_metric = 1000
        self.alert_thresholds = {
            'error_rate': 0.05,  # 5% error rate
            'response_time_p95': 3.0,  # 3 seconds
            'success_rate': 0.95  # 95% success rate
        }

        # Thread safety
        self._lock = threading.RLock()

        logger.info("✅ Observability manager initialized")

    def start_operation(self, operation_name: str, correlation_id: str,
                       user_id: Optional[str] = None, metadata: Dict[str, Any] = None) -> str:
        """Start tracking an operation"""
        operation_id = str(uuid.uuid4())

        with self._lock:
            self.active_operations[operation_id] = {
                'operation_name': operation_name,
                'correlation_id': correlation_id,
                'user_id': user_id,
                'start_time': time.time(),
                'metadata': metadata or {},
                'trace_id': correlation_id,
                'span_id': operation_id
            }

        self.log_structured(
            level=LogLevel.DEBUG,
            message=f"Started operation: {operation_name}",
            correlation_id=correlation_id,
            component="observability",
            operation=operation_name,
            user_id=user_id,
            metadata={'operation_id': operation_id}
        )

        return operation_id

    def end_operation(self, operation_id: str, success: bool = True,
                     error_message: Optional[str] = None) -> None:
        """End tracking an operation and record metrics"""
        with self._lock:
            if operation_id not in self.active_operations:
                logger.warning(f"Attempted to end unknown operation: {operation_id}")
                return

            operation_data = self.active_operations.pop(operation_id)
            end_time = time.time()
            duration = end_time - operation_data['start_time']

            # Record metrics
            self._record_operation_metrics(
                operation_data['operation_name'],
                duration,
                success,
                operation_data['metadata']
            )

            # Log completion
            log_level = LogLevel.INFO if success else LogLevel.ERROR
            message = f"Completed operation: {operation_data['operation_name']} in {duration:.3f}s"
            if not success and error_message:
                message += f" - Error: {error_message}"

            self.log_structured(
                level=log_level,
                message=message,
                correlation_id=operation_data['correlation_id'],
                component="observability",
                operation=operation_data['operation_name'],
                user_id=operation_data['user_id'],
                metadata={
                    'operation_id': operation_id,
                    'duration': duration,
                    'success': success
                },
                metrics={'duration': duration}
            )

    def log_structured(self, level: LogLevel, message: str, correlation_id: str,
                      component: str, operation: str, user_id: Optional[str] = None,
                      session_id: Optional[str] = None, metadata: Dict[str, Any] = None,
                      metrics: Dict[str, Union[int, float]] = None,
                      trace_id: Optional[str] = None, span_id: Optional[str] = None) -> None:
        """Log a structured log entry"""

        log_entry = StructuredLog(
            level=level,
            message=message,
            timestamp=datetime.now(),
            correlation_id=correlation_id,
            component=component,
            operation=operation,
            user_id=user_id,
            session_id=session_id,
            metadata=metadata or {},
            metrics=metrics or {},
            trace_id=trace_id,
            span_id=span_id
        )

        # Convert to log record
        extra_data = {
            'correlation_id': correlation_id,
            'component': component,
            'operation': operation,
            'structured_log': True
        }

        if user_id:
            extra_data['user_id'] = user_id
        if session_id:
            extra_data['session_id'] = session_id
        if trace_id:
            extra_data['trace_id'] = trace_id
        if span_id:
            extra_data['span_id'] = span_id
        if metadata:
            extra_data.update(metadata)
        if metrics:
            extra_data['metrics'] = metrics

        # Log using standard logger
        log_method = getattr(logger, level.value.lower())
        log_method(message, extra=extra_data)

    def record_metric(self, name: str, value: Union[int, float],
                     metric_type: MetricType = MetricType.GAUGE,
                     tags: Dict[str, str] = None) -> None:
        """Record a metric measurement"""

        metric = Metric(
            name=name,
            value=value,
            timestamp=datetime.now(),
            tags=tags or {},
            metric_type=metric_type
        )

        # Store in local cache for aggregation (avoiding async cache in sync method)
        cache_key = f"metric:{name}:{datetime.now().strftime('%Y%m%d%H%M')}"
        if not hasattr(self, '_metric_cache'):
            self._metric_cache = {}

        existing = self._metric_cache.get(cache_key, [])
        existing.append(metric)
        self._metric_cache[cache_key] = existing

        # Check for alerts
        self._check_metric_alerts(metric)

    def _record_operation_metrics(self, operation_name: str, duration: float,
                                success: bool, metadata: Dict[str, Any]) -> None:
        """Record metrics for a completed operation"""

        with self._lock:
            if operation_name not in self.metrics_store:
                self.metrics_store[operation_name] = PerformanceMetrics(operation_name)

            metrics = self.metrics_store[operation_name]

            metrics.count += 1
            metrics.total_time += duration
            metrics.min_time = min(metrics.min_time, duration)
            metrics.max_time = max(metrics.max_time, duration)
            metrics.samples.append(duration)

            if not success:
                metrics.error_count += 1

            # Maintain sample size limit
            if len(metrics.samples) > self.max_samples_per_metric:
                metrics.samples = metrics.samples[-self.max_samples_per_metric:]

            # Recalculate aggregates
            metrics.avg_time = metrics.total_time / metrics.count
            metrics.success_rate = ((metrics.count - metrics.error_count) / metrics.count) * 100

            if len(metrics.samples) >= 10:  # Need minimum samples for percentiles
                sorted_samples = sorted(metrics.samples)
                metrics.p95_time = sorted_samples[int(len(sorted_samples) * 0.95)]
                metrics.p99_time = sorted_samples[int(len(sorted_samples) * 0.99)]

    def get_operation_metrics(self, operation_name: str) -> Optional[PerformanceMetrics]:
        """Get performance metrics for an operation"""
        with self._lock:
            return self.metrics_store.get(operation_name)

    def get_all_metrics(self) -> Dict[str, PerformanceMetrics]:
        """Get all operation metrics"""
        with self._lock:
            return self.metrics_store.copy()

    def _check_metric_alerts(self, metric: Metric) -> None:
        """Check if a metric triggers any alerts"""

        # Check response time alerts
        if 'response_time' in metric.name and metric.metric_type == MetricType.TIMER:
            if metric.value > self.alert_thresholds['response_time_p95']:
                self._trigger_alert(
                    alert_type="high_response_time",
                    message=f"High response time: {metric.value:.2f}s for {metric.name}",
                    severity="warning",
                    metadata={'metric': metric.name, 'value': metric.value}
                )

        # Check error rate alerts
        elif 'error_rate' in metric.name and metric.metric_type == MetricType.GAUGE:
            if metric.value > self.alert_thresholds['error_rate']:
                self._trigger_alert(
                    alert_type="high_error_rate",
                    message=f"High error rate: {metric.value:.2%} for {metric.name}",
                    severity="error",
                    metadata={'metric': metric.name, 'value': metric.value}
                )

    def _trigger_alert(self, alert_type: str, message: str, severity: str,
                      metadata: Dict[str, Any] = None) -> None:
        """Trigger an alert"""

        alert = {
            'alert_type': alert_type,
            'message': message,
            'severity': severity,
            'timestamp': datetime.now(),
            'metadata': metadata or {}
        }

        self.alerts.append(alert)

        # Keep only recent alerts (last 100)
        if len(self.alerts) > 100:
            self.alerts = self.alerts[-100:]

        # Log the alert
        log_level = LogLevel.WARNING if severity == "warning" else LogLevel.ERROR
        self.log_structured(
            level=log_level,
            message=f"ALERT: {message}",
            correlation_id="system",
            component="observability",
            operation="alert",
            metadata=alert
        )

    def register_health_check(self, name: str, check_func: Callable) -> None:
        """Register a health check function"""
        self.health_checks[name] = check_func

    async def run_health_checks(self) -> Dict[str, Any]:
        """Run all registered health checks"""
        results = {}

        for name, check_func in self.health_checks.items():
            try:
                start_time = time.time()
                result = await check_func()
                duration = time.time() - start_time

                results[name] = {
                    'status': 'healthy' if result else 'unhealthy',
                    'duration': duration,
                    'timestamp': datetime.now()
                }
            except Exception as e:
                results[name] = {
                    'status': 'error',
                    'error': str(e),
                    'timestamp': datetime.now()
                }

        return results

    def get_recent_alerts(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent alerts"""
        return self.alerts[-limit:] if self.alerts else []

    def reset_metrics(self, operation_name: Optional[str] = None) -> None:
        """Reset metrics for an operation or all operations"""
        with self._lock:
            if operation_name:
                if operation_name in self.metrics_store:
                    del self.metrics_store[operation_name]
            else:
                self.metrics_store.clear()

# Global observability manager instance
observability_manager = ObservabilityManager()

# Convenience functions
def start_operation_tracking(operation_name: str, correlation_id: str,
                           user_id: Optional[str] = None, metadata: Dict[str, Any] = None) -> str:
    """Start tracking an operation"""
    return observability_manager.start_operation(operation_name, correlation_id, user_id, metadata)

def end_operation_tracking(operation_id: str, success: bool = True, error_message: Optional[str] = None) -> None:
    """End tracking an operation"""
    observability_manager.end_operation(operation_id, success, error_message)

def log_performance(operation_name: str, duration: float, success: bool = True,
                   correlation_id: str = "system", metadata: Dict[str, Any] = None) -> None:
    """Log performance metrics"""
    operation_id = start_operation_tracking(operation_name, correlation_id, metadata=metadata)
    end_operation_tracking(operation_id, success)

def get_operation_metrics(operation_name: str) -> Optional[PerformanceMetrics]:
    """Get performance metrics for an operation"""
    return observability_manager.get_operation_metrics(operation_name)