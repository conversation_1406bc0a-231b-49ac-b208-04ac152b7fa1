"""
Simple Pipeline Monitor
"""

import logging

logger = logging.getLogger(__name__)

class PipelineMonitor:
    """Simple Pipeline Monitor"""
    
    def __init__(self, name: str):
        self.name = name
        logger.info(f"PipelineMonitor {name} initialized")
    
    def record_stage_execution(self, stage: str, execution_time: float, success: bool):
        """Record stage execution"""
        logger.debug(f"Stage {stage}: {execution_time:.2f}s, success: {success}")
