import logging
from datetime import datetime
from typing import Dict, Any
from enum import Enum

logger = logging.getLogger(__name__)

class ResponseQuality(Enum):
    EXCELLENT = "A+"
    GOOD = "A"
    SATISFACTORY = "B"
    NEEDS_IMPROVEMENT = "C"
    POOR = "D"
    FAILED = "F"

class StepLogger:
    def __init__(self, pipeline_id: str, step_name: str):
        self.pipeline_id = pipeline_id
        self.step_name = step_name
        self.start_time = datetime.now()
        self.metrics = {}
        self.errors = []
        self.warnings = []
        
    def log_step_start(self, context: Dict[str, Any] = None):
        self.metrics['start_time'] = self.start_time.isoformat()
        self.metrics['context'] = context or {}
        logger.info(f"Starting {self.step_name} step for pipeline {self.pipeline_id}")
        
    def log_step_completion(self, result: Any = None, success: bool = True):
        end_time = datetime.now()
        execution_time = (end_time - self.start_time).total_seconds()
        
        self.metrics.update({
            'end_time': end_time.isoformat(),
            'execution_time': execution_time,
            'success': success,
            'result_preview': str(result)[:200] if result else None
        })
        
        status = "SUCCESS" if success else "FAILED"
        logger.info(f"{status} Completed {self.step_name} in {execution_time:.2f}s")
        
        return self._generate_step_grade()
    
    def _generate_step_grade(self) -> Dict[str, Any]:
        execution_time = self.metrics.get('execution_time', 0)
        success = self.metrics.get('success', False)
        error_count = len(self.errors)
        warning_count = len(self.warnings)
        
        reliability_score = 100 if success and error_count == 0 else max(0, 100 - (error_count * 25))
        performance_score = max(0, 100 - (execution_time * 2))
        quality_score = max(0, 100 - (warning_count * 10))
        
        overall_score = (reliability_score + performance_score + quality_score) / 3
        
        if overall_score >= 90:
            grade = ResponseQuality.EXCELLENT.value
        elif overall_score >= 80:
            grade = ResponseQuality.GOOD.value
        elif overall_score >= 70:
            grade = ResponseQuality.SATISFACTORY.value
        elif overall_score >= 60:
            grade = ResponseQuality.NEEDS_IMPROVEMENT.value
        elif overall_score >= 50:
            grade = ResponseQuality.POOR.value
        else:
            grade = ResponseQuality.FAILED.value
        
        return {
            'step_name': self.step_name,
            'execution_time': execution_time,
            'success': success,
            'overall_score': overall_score,
            'grade': grade
        }

class ResponseQualityGrader:
    def __init__(self):
        self.response_history = []
        
    def grade_response(self, query: str, response: str, step_context: Dict[str, Any] = None) -> Dict[str, Any]:
        relevance_score = self._grade_relevance(query, response)
        completeness_score = self._grade_completeness(response)
        disclaimer_appropriateness = self._grade_disclaimer_appropriateness(query, response)
        
        overall_score = (relevance_score + completeness_score + disclaimer_appropriateness) / 3
        
        if overall_score >= 90:
            grade = ResponseQuality.EXCELLENT.value
        elif overall_score >= 80:
            grade = ResponseQuality.GOOD.value
        elif overall_score >= 70:
            grade = ResponseQuality.SATISFACTORY.value
        elif overall_score >= 60:
            grade = ResponseQuality.NEEDS_IMPROVEMENT.value
        elif overall_score >= 50:
            grade = ResponseQuality.POOR.value
        else:
            grade = ResponseQuality.FAILED.value
        
        grade_result = {
            'query': query,
            'response_length': len(response),
            'relevance_score': relevance_score,
            'completeness_score': completeness_score,
            'disclaimer_appropriateness': disclaimer_appropriateness,
            'overall_score': overall_score,
            'grade': grade,
            'timestamp': datetime.now().isoformat()
        }
        
        self.response_history.append(grade_result)
        return grade_result
    
    def _grade_relevance(self, query: str, response: str) -> float:
        query_lower = query.lower()
        response_lower = response.lower()
        
        if any(word in response_lower for word in query_lower.split()):
            base_score = 70
        else:
            base_score = 30
        
        if 'disclaimer' in response_lower and len(response.strip()) < 100:
            return 20
        
        if len(response.strip()) > 50:
            base_score += 20
        
        return min(100, base_score)
    
    def _grade_completeness(self, response: str) -> float:
        if not response or len(response.strip()) < 10:
            return 0
        
        if len(response) > 200:
            return 90
        elif len(response) > 100:
            return 70
        elif len(response) > 50:
            return 50
        else:
            return 30
    
    def _grade_disclaimer_appropriateness(self, query: str, response: str) -> float:
        query_lower = query.lower()
        response_lower = response.lower()
        
        has_disclaimer = 'disclaimer' in response_lower
        simple_queries = ['test', 'hello', 'hi', 'hey', 'status', 'help']
        is_simple_query = any(simple in query_lower for simple in simple_queries)
        
        if is_simple_query and has_disclaimer:
            return 20
        elif not is_simple_query and not has_disclaimer:
            return 80
        elif not is_simple_query and has_disclaimer:
            return 100
        else:
            return 90

response_grader = ResponseQualityGrader()

def get_step_logger(pipeline_id: str, step_name: str) -> StepLogger:
    return StepLogger(pipeline_id, step_name)

def grade_response_quality(query: str, response: str, step_context: Dict[str, Any] = None) -> Dict[str, Any]:
    return response_grader.grade_response(query, response, step_context)
