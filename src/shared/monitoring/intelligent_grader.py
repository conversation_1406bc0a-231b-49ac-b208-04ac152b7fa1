import asyncio
from datetime import datetime
from typing import Dict, Any
from enum import Enum

class ResponseQuality(Enum):
    EXCELLENT = "A+"
    GOOD = "A"
    SATISFACTORY = "B"
    NEEDS_IMPROVEMENT = "C"
    POOR = "D"
    FAILED = "F"

class IntelligentResponseGrader:
    def __init__(self):
        self.response_history = []
        self.performance_trends = {}
    
    async def grade_response(self, query: str, response: str, query_type: str, response_time: float, disclaimer_added: bool) -> Dict[str, Any]:
        relevance_score = self._calculate_relevance(query, response, query_type)
        completeness_score = self._calculate_completeness(query, response, query_type)
        clarity_score = self._calculate_clarity(response)
        helpfulness_score = self._calculate_helpfulness(query, response, query_type)
        disclaimer_appropriate = self._check_disclaimer_appropriateness(query_type, disclaimer_added)
        
        overall_score = (
            relevance_score * 0.3 +
            completeness_score * 0.2 +
            clarity_score * 0.2 +
            helpfulness_score * 0.3
        )
        
        if disclaimer_appropriate:
            overall_score += 0.05
        
        if response_time > 10.0:
            overall_score -= 0.1
        elif response_time > 5.0:
            overall_score -= 0.05
        
        overall_score = min(0.95, max(0.0, overall_score))
        
        if overall_score >= 0.9:
            grade = ResponseQuality.EXCELLENT
        elif overall_score >= 0.8:
            grade = ResponseQuality.GOOD
        elif overall_score >= 0.7:
            grade = ResponseQuality.SATISFACTORY
        elif overall_score >= 0.6:
            grade = ResponseQuality.NEEDS_IMPROVEMENT
        elif overall_score >= 0.5:
            grade = ResponseQuality.POOR
        else:
            grade = ResponseQuality.FAILED
        
        recommendations = []
        if relevance_score < 0.7:
            recommendations.append("Focus on directly addressing the user's question")
        if completeness_score < 0.7:
            recommendations.append("Provide more comprehensive information")
        if clarity_score < 0.7:
            recommendations.append("Improve response structure and clarity")
        if helpfulness_score < 0.7:
            recommendations.append("Include more actionable insights")
        if not disclaimer_appropriate:
            recommendations.append("Review disclaimer usage guidelines")
        if response_time > 5.0:
            recommendations.append("Optimize response time for better user experience")
        
        result = {
            'timestamp': datetime.now().isoformat(),
            'query': query[:100],
            'query_type': query_type,
            'response_length': len(response),
            'response_time': response_time,
            'grade': grade.value,
            'overall_score': overall_score,
            'metrics': {
                'relevance': relevance_score,
                'completeness': completeness_score,
                'clarity': clarity_score,
                'helpfulness': helpfulness_score,
                'disclaimer_appropriate': disclaimer_appropriate
            },
            'recommendations': recommendations
        }
        
        self.response_history.append(result)
        return result
    
    def _calculate_relevance(self, query: str, response: str, query_type: str) -> float:
        query_lower = query.lower()
        response_lower = response.lower()
        
        query_terms = set(query_lower.split())
        response_terms = set(response_lower.split())
        term_overlap = len(query_terms.intersection(response_terms)) / max(len(query_terms), 1)
        
        # Check for completely irrelevant responses
        irrelevant_phrases = [
            "i don't know", "i don't know about", "maybe check", "check google", 
            "weather is nice", "coffee shop", "sunny days", "i like"
        ]
        if any(phrase in response_lower for phrase in irrelevant_phrases):
            return 0.1  # Very low relevance for completely off-topic responses
        
        type_relevance = 1.0
        if query_type == 'price_query':
            price_indicators = ['price', '$', 'usd', 'cost', 'value', 'worth', 'trading', 'stock']
            type_relevance = 0.9 if any(indicator in response_lower for indicator in price_indicators) else 0.2
        elif query_type == 'analysis_query':
            analysis_indicators = ['analysis', 'indicator', 'trend', 'pattern', 'technical', 'fundamental', 'chart', 'data']
            type_relevance = 0.9 if any(indicator in response_lower for indicator in analysis_indicators) else 0.2
        elif query_type == 'status_query':
            type_relevance = 0.9 if len(response) < 200 and 'status' in response_lower else 0.3
        
        return min(0.95, (term_overlap * 0.6 + type_relevance * 0.4))
    
    def _calculate_completeness(self, query: str, response: str, query_type: str) -> float:
        base_score = min(1.0, len(response) / 100)
        
        if query_type == 'price_query':
            return min(0.95, base_score * 0.8 + 0.2)
        elif query_type == 'analysis_query':
            return min(0.95, base_score * 1.2)
        elif query_type == 'status_query':
            return min(0.95, base_score * 0.6 + 0.4)
        
        return min(0.95, base_score)
    
    def _calculate_clarity(self, response: str) -> float:
        structure_score = 0.0
        if '**' in response or '##' in response:
            structure_score += 0.3
        if '\n' in response:
            structure_score += 0.2
        if any(char in response for char in ['•', '-', '*']):
            structure_score += 0.2
        
        clarity_indicators = ['clearly', 'specifically', 'in summary', 'to answer your question']
        clarity_score = 0.3 if any(indicator in response.lower() for indicator in clarity_indicators) else 0.0
        
        length_score = 0.5
        if 50 <= len(response) <= 1000:
            length_score = 0.8
        elif len(response) < 50:
            length_score = 0.3
        elif len(response) > 2000:
            length_score = 0.6
        
        return min(0.95, structure_score + clarity_score + length_score)
    
    def _calculate_helpfulness(self, query: str, response: str, query_type: str) -> float:
        helpfulness_score = 0.3  # Start lower
        
        # Check for completely unhelpful responses
        unhelpful_phrases = [
            "i don't know", "maybe check", "check google", "i don't know about stocks",
            "weather is nice", "coffee shop", "sunny days"
        ]
        if any(phrase in response.lower() for phrase in unhelpful_phrases):
            return 0.1  # Very low helpfulness for unhelpful responses
        
        action_indicators = ['you should', 'recommend', 'suggest', 'consider', 'try', 'use', 'based on', 'analysis shows']
        if any(indicator in response.lower() for indicator in action_indicators):
            helpfulness_score += 0.3
        
        # Reward specific data and numbers
        if any(char.isdigit() for char in response):
            helpfulness_score += 0.2
        
        # Reward explanations and context
        context_indicators = ['because', 'since', 'due to', 'as a result', 'therefore', 'this means', 'indicates']
        if any(indicator in response.lower() for indicator in context_indicators):
            helpfulness_score += 0.2
        
        # Reward additional insights
        followup_indicators = ['also', 'additionally', 'furthermore', 'you might also', 'consider also', 'key points', 'important']
        if any(indicator in response.lower() for indicator in followup_indicators):
            helpfulness_score += 0.2
        
        # Reward structured responses with formatting
        if '**' in response or '##' in response or '•' in response:
            helpfulness_score += 0.1
        
        return min(0.95, helpfulness_score)
    
    def _check_disclaimer_appropriateness(self, query_type: str, disclaimer_added: bool) -> bool:
        if query_type in ['price_query', 'status_query', 'help_query', 'time_query']:
            return not disclaimer_added
        elif query_type in ['analysis_query', 'recommendation_query', 'batch_query']:
            return disclaimer_added
        return True
    
    def get_performance_summary(self) -> Dict[str, Any]:
        if not self.response_history:
            return {'message': 'No responses graded yet'}
        
        total_responses = len(self.response_history)
        recent_responses = self.response_history[-10:]
        
        recent_scores = [r['overall_score'] for r in recent_responses]
        recent_avg = sum(recent_scores) / len(recent_scores) if recent_scores else 0
        
        grade_dist = {}
        for result in self.response_history:
            grade = result['grade']
            grade_dist[grade] = grade_dist.get(grade, 0) + 1
        
        overall_grade = 'A+' if recent_avg >= 0.9 else 'A' if recent_avg >= 0.8 else 'B' if recent_avg >= 0.7 else 'C' if recent_avg >= 0.6 else 'D' if recent_avg >= 0.5 else 'F'
        
        return {
            'total_responses': total_responses,
            'recent_average_score': recent_avg,
            'grade_distribution': grade_dist,
            'overall_grade': overall_grade
        }

intelligent_grader = IntelligentResponseGrader()
