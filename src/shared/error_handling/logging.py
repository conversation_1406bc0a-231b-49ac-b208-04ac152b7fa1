"""
Structured logging configuration for the application.
"""

import os
import sys
import uuid
import logging
import datetime
from typing import Any, Dict, Optional, Union

# Configure basic logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

# Default log level
DEFAULT_LOG_LEVEL = logging.DEBUG  # Set to DEBUG for development

# Environment-based configuration
LOG_LEVEL = os.environ.get("LOG_LEVEL", "DEBUG")  # Default to DEBUG for development
LOG_FORMAT = os.environ.get("LOG_FORMAT", "text")  # text or json
LOG_FILE = os.environ.get("LOG_FILE", "")

# Set log level based on environment
if LOG_LEVEL == "DEBUG":
    DEFAULT_LOG_LEVEL = logging.DEBUG
elif LOG_LEVEL == "INFO":
    DEFAULT_LOG_LEVEL = logging.INFO
elif LOG_LEVEL == "WARNING":
    DEFAULT_LOG_LEVEL = logging.WARNING
elif LOG_LEVEL == "ERROR":
    DEFAULT_LOG_LEVEL = logging.ERROR
elif LOG_LEVEL == "CRITICAL":
    DEFAULT_LOG_LEVEL = logging.CRITICAL

# Add file handler if LOG_FILE is specified, or use default development log
log_file = LOG_FILE or "development.log"
file_handler = logging.FileHandler(log_file)
file_handler.setFormatter(logging.Formatter(
    "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
))
logging.getLogger().addHandler(file_handler)

# Also add console handler for full visibility
console_handler = logging.StreamHandler(sys.stdout)
console_handler.setFormatter(logging.Formatter(
    "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
))
logging.getLogger().addHandler(console_handler)

def generate_correlation_id() -> str:
    """Generate a unique correlation ID for request tracing"""
    return str(uuid.uuid4())

def get_logger(name: str, level: Optional[int] = None) -> logging.Logger:
    """
    Get a logger with the specified name and level.
    
    Args:
        name: Logger name
        level: Log level (defaults to LOG_LEVEL from environment)
        
    Returns:
        Configured logger
    """
    logger = logging.getLogger(name)
    logger.setLevel(level or DEFAULT_LOG_LEVEL)
    return logger

def get_trading_logger(symbol: str = None) -> logging.Logger:
    """
    Get a logger specifically for trading operations.
    
    Args:
        symbol: Trading symbol (optional)
        
    Returns:
        Configured logger for trading
    """
    logger_name = "trading"
    if symbol:
        logger_name = f"trading.{symbol.lower()}"
    
    logger = get_logger(logger_name)
    return logger

def get_pipeline_logger(pipeline_name: str) -> logging.Logger:
    """
    Get a logger specifically for pipeline operations.
    
    Args:
        pipeline_name: Name of the pipeline
        
    Returns:
        Configured logger for pipeline
    """
    logger_name = f"pipeline.{pipeline_name.lower()}"
    logger = get_logger(logger_name)
    return logger

def log_request(logger: logging.Logger, method: str, url: str, status_code: int, 
                elapsed_time: float, correlation_id: str = None) -> None:
    """
    Log an HTTP request with standard format.
    
    Args:
        logger: Logger to use
        method: HTTP method
        url: Request URL
        status_code: Response status code
        elapsed_time: Request duration in seconds
        correlation_id: Request correlation ID
    """
    log_data = {
        "method": method,
        "url": url,
        "status_code": status_code,
        "elapsed_ms": round(elapsed_time * 1000, 2),
    }
    
    if correlation_id:
        log_data["correlation_id"] = correlation_id
    
    logger.info(f"HTTP {method} {url} completed with status {status_code} in {log_data['elapsed_ms']}ms", 
                extra={"http_request": log_data})

def log_error(logger: logging.Logger, error: Exception, context: Dict[str, Any] = None) -> None:
    """
    Log an error with context.
    
    Args:
        logger: Logger to use
        error: Exception to log
        context: Additional context information
    """
    log_data = {
        "error_type": error.__class__.__name__,
        "error_message": str(error),
    }
    
    if context:
        log_data["context"] = context
    
    logger.error(f"Error: {error}", extra={"error": log_data}, exc_info=True)

def configure_logging(log_level: str = None, log_format: str = None, log_file: str = None) -> None:
    """
    Configure global logging settings.
    
    Args:
        log_level: Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_format: Log format (text, json)
        log_file: Log file path
    """
    global DEFAULT_LOG_LEVEL
    
    # Set log level
    if log_level:
        if log_level == "DEBUG":
            DEFAULT_LOG_LEVEL = logging.DEBUG
        elif log_level == "INFO":
            DEFAULT_LOG_LEVEL = logging.INFO
        elif log_level == "WARNING":
            DEFAULT_LOG_LEVEL = logging.WARNING
        elif log_level == "ERROR":
            DEFAULT_LOG_LEVEL = logging.ERROR
        elif log_level == "CRITICAL":
            DEFAULT_LOG_LEVEL = logging.CRITICAL
    
    # Set root logger level
    logging.getLogger().setLevel(DEFAULT_LOG_LEVEL)
    
    # Configure log format
    formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
    if log_format == "json":
        # Simple JSON formatter for demonstration
        # In production, consider using a library like python-json-logger
        class JsonFormatter(logging.Formatter):
            def format(self, record):
                log_data = {
                    "timestamp": datetime.datetime.now().isoformat(),
                    "level": record.levelname,
                    "name": record.name,
                    "message": record.getMessage(),
                }
                
                # Add extra fields
                if hasattr(record, "exc_info") and record.exc_info:
                    log_data["exception"] = self.formatException(record.exc_info)
                
                if hasattr(record, "__dict__"):
                    for key, value in record.__dict__.items():
                        if key not in ["args", "exc_info", "exc_text", "stack_info", "lineno", 
                                      "funcName", "created", "msecs", "relativeCreated", 
                                      "levelname", "levelno", "pathname", "filename", 
                                      "module", "name", "thread", "threadName", 
                                      "processName", "process", "message"]:
                            log_data[key] = value
                
                import json
                return json.dumps(log_data)
        
        formatter = JsonFormatter()
    
    # Update all handlers
    for handler in logging.getLogger().handlers:
        handler.setFormatter(formatter)
    
    # Add file handler if specified
    if log_file:
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(formatter)
        logging.getLogger().addHandler(file_handler)

# Initialize logging
configure_logging()