"""
Fallback mechanisms for handling failures in external services.
"""

import time
import asyncio
import random
from typing import Any, Callable, Dict, List, Optional, TypeVar, Union
from functools import wraps

from .logging import get_logger

# Create logger
logger = get_logger(__name__)

# Type variables for generic functions
T = TypeVar('T')
R = TypeVar('R')

# Custom exceptions
class FallbackError(Exception):
    """Base exception for fallback errors"""
    pass

class NoFallbackAvailableError(FallbackError):
    """Raised when no fallback is available"""
    pass

class AllFallbacksFailedError(FallbackError):
    """Raised when all fallbacks have failed"""
    pass

class ValidationError(Exception):
    """Raised when validation fails"""
    pass

class ConfigValidationError(ValidationError):
    """Raised when configuration validation fails"""
    pass

class MarketDataError(Exception):
    """Base exception for market data errors"""
    pass

class ProviderUnavailableError(MarketDataError):
    """Raised when a data provider is unavailable"""
    pass

class RateLimitError(MarketDataError):
    """Raised when a rate limit is exceeded"""
    pass

class DatabaseError(Exception):
    """Base exception for database errors"""
    pass

class ConfigurationError(Exception):
    """Base exception for configuration errors"""
    pass

class PipelineError(Exception):
    """Base exception for pipeline errors"""
    pass

class FallbackManager:
    """
    Manages fallback mechanisms for handling failures in external services.
    """
    
    def __init__(self):
        """Initialize the fallback manager"""
        self.fallbacks: Dict[str, List[Callable]] = {}
        self.error_counts: Dict[str, int] = {}
        self.error_thresholds: Dict[str, int] = {}
        self.cooldown_periods: Dict[str, int] = {}
        self.last_failures: Dict[str, float] = {}
    
    def register_fallback(self, service_name: str, fallback_func: Callable, 
                         error_threshold: int = 3, cooldown_period: int = 60) -> None:
        """
        Register a fallback function for a service.
        
        Args:
            service_name: Name of the service
            fallback_func: Fallback function to call
            error_threshold: Number of errors before using fallback
            cooldown_period: Cooldown period in seconds before retrying primary
        """
        if service_name not in self.fallbacks:
            self.fallbacks[service_name] = []
            self.error_counts[service_name] = 0
            self.error_thresholds[service_name] = error_threshold
            self.cooldown_periods[service_name] = cooldown_period
        
        self.fallbacks[service_name].append(fallback_func)
        logger.info(f"Registered fallback for {service_name}")
    
    def increment_error_count(self, service_name: str) -> None:
        """
        Increment the error count for a service.
        
        Args:
            service_name: Name of the service
        """
        if service_name in self.error_counts:
            self.error_counts[service_name] += 1
            self.last_failures[service_name] = time.time()
            logger.warning(f"Incremented error count for {service_name} to {self.error_counts[service_name]}")
    
    def should_use_fallback(self, service_name: str) -> bool:
        """
        Check if a fallback should be used for a service.
        
        Args:
            service_name: Name of the service
            
        Returns:
            True if fallback should be used
        """
        if service_name not in self.error_counts:
            return False
        
        # Check if we've exceeded the error threshold
        if self.error_counts[service_name] >= self.error_thresholds[service_name]:
            # Check if we're still in the cooldown period
            if service_name in self.last_failures:
                cooldown_end = self.last_failures[service_name] + self.cooldown_periods[service_name]
                if time.time() < cooldown_end:
                    logger.info(f"Using fallback for {service_name} (in cooldown period)")
                    return True
                else:
                    # Reset error count after cooldown period
                    logger.info(f"Cooldown period ended for {service_name}, resetting error count")
                    self.error_counts[service_name] = 0
                    return False
            
            return True
        
        return False
    
    async def execute_with_fallback(self, service_name: str, primary_func: Callable, 
                                  *args, **kwargs) -> Any:
        """
        Execute a function with fallback if it fails.
        
        Args:
            service_name: Name of the service
            primary_func: Primary function to call
            args: Arguments to pass to the function
            kwargs: Keyword arguments to pass to the function
            
        Returns:
            Result of the function
            
        Raises:
            AllFallbacksFailedError: If all fallbacks fail
        """
        # Check if we should use fallback immediately
        if self.should_use_fallback(service_name):
            return await self._execute_fallbacks(service_name, *args, **kwargs)
        
        # Try primary function
        try:
            if asyncio.iscoroutinefunction(primary_func):
                result = await primary_func(*args, **kwargs)
            else:
                result = primary_func(*args, **kwargs)
            
            return result
        
        except Exception as e:
            logger.error(f"Primary function for {service_name} failed: {e}")
            self.increment_error_count(service_name)
            
            # Try fallbacks
            return await self._execute_fallbacks(service_name, *args, **kwargs)
    
    async def _execute_fallbacks(self, service_name: str, *args, **kwargs) -> Any:
        """
        Execute fallback functions for a service.
        
        Args:
            service_name: Name of the service
            args: Arguments to pass to the fallback
            kwargs: Keyword arguments to pass to the fallback
            
        Returns:
            Result of a successful fallback
            
        Raises:
            NoFallbackAvailableError: If no fallback is available
            AllFallbacksFailedError: If all fallbacks fail
        """
        if service_name not in self.fallbacks or not self.fallbacks[service_name]:
            raise NoFallbackAvailableError(f"No fallback available for {service_name}")
        
        errors = []
        
        # Try each fallback
        for fallback in self.fallbacks[service_name]:
            try:
                if asyncio.iscoroutinefunction(fallback):
                    result = await fallback(*args, **kwargs)
                else:
                    result = fallback(*args, **kwargs)
                
                logger.info(f"Fallback succeeded for {service_name}")
                return result
            
            except Exception as e:
                logger.error(f"Fallback for {service_name} failed: {e}")
                errors.append(str(e))
        
        # All fallbacks failed
        error_msg = f"All fallbacks failed for {service_name}: {', '.join(errors)}"
        logger.error(error_msg)
        raise AllFallbacksFailedError(error_msg)

# Global fallback manager instance
fallback_manager = FallbackManager()

def with_fallback(service_name: str, fallback_func: Optional[Callable] = None,
                error_threshold: int = 3, cooldown_period: int = 60):
    """
    Decorator to add fallback to a function.
    
    Args:
        service_name: Name of the service
        fallback_func: Fallback function to call
        error_threshold: Number of errors before using fallback
        cooldown_period: Cooldown period in seconds before retrying primary
    """
    def decorator(func):
        # Register fallback if provided
        if fallback_func:
            fallback_manager.register_fallback(
                service_name, fallback_func, 
                error_threshold, cooldown_period
            )
        
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            return await fallback_manager.execute_with_fallback(
                service_name, func, *args, **kwargs
            )
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            # For synchronous functions, we need to run in an event loop
            loop = asyncio.get_event_loop()
            return loop.run_until_complete(
                fallback_manager.execute_with_fallback(
                    service_name, func, *args, **kwargs
                )
            )
        
        # Return the appropriate wrapper based on function type
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator

async def safe_execute(func: Callable, *args, **kwargs) -> Union[Any, None]:
    """
    Safely execute a function and return None if it fails.
    
    Args:
        func: Function to execute
        args: Arguments to pass to the function
        kwargs: Keyword arguments to pass to the function
        
    Returns:
        Result of the function or None if it fails
    """
    try:
        if asyncio.iscoroutinefunction(func):
            return await func(*args, **kwargs)
        else:
            return func(*args, **kwargs)
    except Exception as e:
        logger.error(f"Function execution failed: {e}")
        return None

def log_error(logger, error: Exception, context: Dict[str, Any] = None) -> None:
    """
    Log an error with context.
    
    Args:
        logger: Logger to use
        error: Exception to log
        context: Additional context information
    """
    log_data = {
        "error_type": error.__class__.__name__,
        "error_message": str(error),
    }
    
    if context:
        log_data["context"] = context
    
    logger.error(f"Error: {error}", extra={"error": log_data}, exc_info=True)

def handle_error_with_fallback(error: Exception, fallback_value: Any = None) -> Any:
    """
    Handle an error with a fallback value.

    Args:
        error: Exception that occurred
        fallback_value: Value to return as fallback

    Returns:
        Fallback value or raises exception
    """
    logger.warning(f"Error occurred, using fallback: {error}")
    if fallback_value is not None:
        return fallback_value
    else:
        # Return a default fallback based on error type
        if isinstance(error, (ConnectionError, TimeoutError)):
            return 0  # Default latency for connection errors
        return None

def create_error_response(error: Exception, status_code: int = 500) -> Dict[str, Any]:
    """
    Create a standardized error response.

    Args:
        error: Exception to create response for
        status_code: HTTP status code

    Returns:
        Error response dictionary
    """
    return {
        "error": True,
        "status_code": status_code,
        "message": str(error),
        "error_type": error.__class__.__name__,
        "timestamp": time.time()
    }