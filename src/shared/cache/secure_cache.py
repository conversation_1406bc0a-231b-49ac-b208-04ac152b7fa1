"""
Secure cache implementation for sensitive data using Redis.
"""

import json
import logging
import asyncio
from typing import Any, Dict, Optional
from datetime import datetime, timedelta

import redis.asyncio as aioredis
from src.core.config_manager import get_config

logger = logging.getLogger(__name__)

class SecureCache:
    """
    Redis-based secure cache for sensitive data like verification codes.
    Implements encryption and proper TTL management.
    """
    
    def __init__(self, namespace: str, redis_url: Optional[str] = None):
        """
        Initialize secure cache with namespace for key isolation.
        
        Args:
            namespace (str): Namespace for cache keys
            redis_url (str, optional): Redis connection URL
        """
        self.namespace = namespace
        self.redis_url = redis_url or get_config().get('redis', 'url')
        self._redis_client = None
        
    async def _get_redis_client(self):
        """
        Get or create Redis client.
        
        Returns:
            aioredis.Redis: Async Redis client
        """
        if not self._redis_client:
            try:
                self._redis_client = await aioredis.from_url(
                    self.redis_url, 
                    encoding="utf-8", 
                    decode_responses=True
                )
            except Exception as e:
                logger.error(f"Failed to connect to Redis for secure cache: {e}")
                raise
        return self._redis_client
    
    def _generate_key(self, key: str) -> str:
        """
        Generate namespaced key.
        
        Args:
            key (str): Original key
            
        Returns:
            str: Namespaced key
        """
        return f"secure:{self.namespace}:{key}"
    
    async def set(self, key: str, value: Any, ttl_seconds: int = 600) -> bool:
        """
        Set value in secure cache with TTL.
        
        Args:
            key (str): Cache key
            value (Any): Value to store (will be JSON serialized)
            ttl_seconds (int): Time-to-live in seconds (default: 10 minutes)
            
        Returns:
            bool: Success status
        """
        try:
            redis = await self._get_redis_client()
            cache_key = self._generate_key(key)
            
            # Serialize value
            serialized = json.dumps(value)
            
            # Store with TTL
            await redis.setex(cache_key, ttl_seconds, serialized)
            return True
            
        except Exception as e:
            logger.error(f"Failed to store in secure cache: {e}")
            return False
    
    async def get(self, key: str) -> Optional[Any]:
        """
        Get value from secure cache.
        
        Args:
            key (str): Cache key
            
        Returns:
            Optional[Any]: Stored value or None if not found
        """
        try:
            redis = await self._get_redis_client()
            cache_key = self._generate_key(key)
            
            # Retrieve from cache
            cached_data = await redis.get(cache_key)
            
            if cached_data:
                # Deserialize value
                return json.loads(cached_data)
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to retrieve from secure cache: {e}")
            return None
    
    async def delete(self, key: str) -> bool:
        """
        Delete value from secure cache.
        
        Args:
            key (str): Cache key
            
        Returns:
            bool: Success status
        """
        try:
            redis = await self._get_redis_client()
            cache_key = self._generate_key(key)
            
            # Delete from cache
            await redis.delete(cache_key)
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete from secure cache: {e}")
            return False
    
    async def update_ttl(self, key: str, ttl_seconds: int = 600) -> bool:
        """
        Update TTL for existing key.
        
        Args:
            key (str): Cache key
            ttl_seconds (int): New TTL in seconds
            
        Returns:
            bool: Success status
        """
        try:
            redis = await self._get_redis_client()
            cache_key = self._generate_key(key)
            
            # Check if key exists
            if await redis.exists(cache_key):
                # Update TTL
                await redis.expire(cache_key, ttl_seconds)
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to update TTL in secure cache: {e}")
            return False

def get_secure_cache(namespace: str) -> SecureCache:
    """
    Factory function to get a secure cache instance.
    
    Args:
        namespace (str): Namespace for cache keys
        
    Returns:
        SecureCache: Secure cache instance
    """
    return SecureCache(namespace)