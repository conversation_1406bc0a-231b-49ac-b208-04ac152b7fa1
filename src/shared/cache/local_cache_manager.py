"""
Cache management for data providers
"""

import time
import json
import os
import logging
from typing import Any, Dict, List, Optional, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta

# Create logger
logger = logging.getLogger(__name__)

# Default cache settings
DEFAULT_CACHE_TTL = 300  # 5 minutes
DEFAULT_CACHE_SIZE = 1000  # Maximum number of entries

@dataclass
class CacheConfig:
    """Configuration for cache behavior"""
    ttl: int = DEFAULT_CACHE_TTL
    max_size: int = DEFAULT_CACHE_SIZE
    enabled: bool = True
    persist: bool = False
    persist_path: str = "data/cache"

@dataclass
class CacheEntry:
    """Individual cache entry with metadata"""
    key: str
    value: Any
    created_at: float = field(default_factory=time.time)
    expires_at: float = None
    access_count: int = 0
    last_accessed: float = None
    
    def __post_init__(self):
        if self.expires_at is None:
            self.expires_at = self.created_at + DEFAULT_CACHE_TTL
        if self.last_accessed is None:
            self.last_accessed = self.created_at
    
    def is_expired(self) -> bool:
        """Check if the entry is expired"""
        return time.time() > self.expires_at
    
    def access(self) -> None:
        """Record an access to this entry"""
        self.access_count += 1
        self.last_accessed = time.time()
    
    def extend(self, ttl: int = DEFAULT_CACHE_TTL) -> None:
        """Extend the expiration time"""
        self.expires_at = time.time() + ttl
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            "key": self.key,
            "value": self.value,
            "created_at": self.created_at,
            "expires_at": self.expires_at,
            "access_count": self.access_count,
            "last_accessed": self.last_accessed
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "CacheEntry":
        """Create from dictionary"""
        return cls(
            key=data["key"],
            value=data["value"],
            created_at=data["created_at"],
            expires_at=data["expires_at"],
            access_count=data["access_count"],
            last_accessed=data["last_accessed"]
        )

class CacheManager:
    """
    Manages caching of data with TTL, size limits, and persistence
    """
    
    def __init__(self, config: Optional[CacheConfig] = None):
        """Initialize the cache manager"""
        self.config = config or CacheConfig()
        self.cache: Dict[str, CacheEntry] = {}
        self.stats = {
            "hits": 0,
            "misses": 0,
            "evictions": 0,
            "expirations": 0
        }
        
        # Create persist directory if needed
        if self.config.persist and not os.path.exists(self.config.persist_path):
            os.makedirs(self.config.persist_path, exist_ok=True)
        
        # Load persisted cache if enabled
        if self.config.persist:
            self._load_cache()
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        Get a value from the cache
        
        Args:
            key: Cache key
            default: Default value if key not found
            
        Returns:
            Cached value or default
        """
        if not self.config.enabled:
            self.stats["misses"] += 1
            return default
        
        entry = self.cache.get(key)
        
        # Cache miss
        if entry is None:
            self.stats["misses"] += 1
            return default
        
        # Expired entry
        if entry.is_expired():
            self.stats["expirations"] += 1
            del self.cache[key]
            return default
        
        # Cache hit
        self.stats["hits"] += 1
        entry.access()
        return entry.value
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """
        Set a value in the cache
        
        Args:
            key: Cache key
            value: Value to cache
            ttl: Time to live in seconds (None for default)
        """
        if not self.config.enabled:
            return
        
        # Enforce cache size limit
        if len(self.cache) >= self.config.max_size and key not in self.cache:
            self._evict_entry()
        
        # Calculate expiration
        expires_at = time.time() + (ttl if ttl is not None else self.config.ttl)
        
        # Create or update entry
        if key in self.cache:
            entry = self.cache[key]
            entry.value = value
            entry.expires_at = expires_at
            entry.access()
        else:
            entry = CacheEntry(key=key, value=value, expires_at=expires_at)
            self.cache[key] = entry
        
        # Persist if enabled
        if self.config.persist:
            self._save_cache()
    
    def delete(self, key: str) -> bool:
        """
        Delete a key from the cache
        
        Args:
            key: Cache key
            
        Returns:
            True if key was found and deleted
        """
        if key in self.cache:
            del self.cache[key]
            
            # Persist if enabled
            if self.config.persist:
                self._save_cache()
                
            return True
        return False
    
    def clear(self) -> None:
        """Clear the entire cache"""
        self.cache.clear()
        
        # Persist if enabled
        if self.config.persist:
            self._save_cache()
    
    def cleanup(self) -> int:
        """
        Remove expired entries
        
        Returns:
            Number of entries removed
        """
        expired_keys = [
            key for key, entry in self.cache.items() 
            if entry.is_expired()
        ]
        
        for key in expired_keys:
            del self.cache[key]
            self.stats["expirations"] += 1
        
        # Persist if enabled and entries were removed
        if self.config.persist and expired_keys:
            self._save_cache()
            
        return len(expired_keys)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        return {
            **self.stats,
            "size": len(self.cache),
            "max_size": self.config.max_size,
            "hit_ratio": self._calculate_hit_ratio(),
            "enabled": self.config.enabled
        }
    
    def _calculate_hit_ratio(self) -> float:
        """Calculate cache hit ratio"""
        total = self.stats["hits"] + self.stats["misses"]
        if total == 0:
            return 0.0
        return self.stats["hits"] / total
    
    def _evict_entry(self) -> None:
        """Evict an entry based on LRU policy"""
        if not self.cache:
            return
            
        # Find least recently used entry
        lru_key = min(
            self.cache.keys(),
            key=lambda k: self.cache[k].last_accessed
        )
        
        # Remove it
        del self.cache[lru_key]
        self.stats["evictions"] += 1
    
    def _save_cache(self) -> None:
        """Save cache to disk"""
        try:
            # Convert to serializable format
            cache_data = {
                key: entry.to_dict() 
                for key, entry in self.cache.items()
            }
            
            # Save to file
            cache_file = os.path.join(self.config.persist_path, "cache.json")
            with open(cache_file, "w") as f:
                json.dump(cache_data, f)
                
            logger.debug(f"Cache saved to {cache_file}")
        except Exception as e:
            logger.error(f"Failed to save cache: {e}")
    
    def _load_cache(self) -> None:
        """Load cache from disk"""
        try:
            cache_file = os.path.join(self.config.persist_path, "cache.json")
            
            if not os.path.exists(cache_file):
                logger.debug(f"No cache file found at {cache_file}")
                return
                
            with open(cache_file, "r") as f:
                cache_data = json.load(f)
            
            # Convert to CacheEntry objects
            self.cache = {
                key: CacheEntry.from_dict(entry_data)
                for key, entry_data in cache_data.items()
            }
            
            # Cleanup expired entries
            self.cleanup()
            
            logger.debug(f"Loaded {len(self.cache)} entries from cache")
        except Exception as e:
            logger.error(f"Failed to load cache: {e}")
            # Start with empty cache on error
            self.cache = {}

# Global cache manager instance
cache_manager = CacheManager()