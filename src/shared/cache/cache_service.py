"""
Redis-based Cache Service
Provides a unified interface for caching across the application.
"""

import json
import asyncio
import os
import logging
import time
import hashlib
from enum import Enum
import redis.asyncio as redis
from typing import Any, Optional, Dict, Union, List, Callable
from datetime import timedelta, datetime
from src.shared.config.config_manager import config # Assuming this path is correct

logger = logging.getLogger(__name__) # Adjusted logging setup

class CacheStrategy(Enum):
    """Caching strategies for different data types"""
    LRU = "lru"  # Least Recently Used
    LFU = "lfu"  # Least Frequently Used
    TTL = "ttl"  # Time-based expiration
    ADAPTIVE = "adaptive"  # Adaptive based on access patterns

class CachePriority(Enum):
    """Cache priority levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class CacheService:
    """Enhanced Redis-based cache service with intelligent TTL and multi-level caching."""

    def __init__(self):
        self.redis_client: Optional[redis.Redis] = None
        self.memory_cache: Dict[str, Any] = {}  # L1 cache
        self._connection_attempted = False
        self._fallback_mode = False

        # Intelligent TTL management
        self.ttl_strategies = {
            'intent_cache': {'base_ttl': 300, 'max_ttl': 1800, 'strategy': CacheStrategy.ADAPTIVE},
            'response_cache': {'base_ttl': 600, 'max_ttl': 3600, 'strategy': CacheStrategy.LRU},
            'config_cache': {'base_ttl': 1800, 'max_ttl': 7200, 'strategy': CacheStrategy.TTL},
            'market_data': {'base_ttl': 60, 'max_ttl': 300, 'strategy': CacheStrategy.TTL},
            'ai_responses': {'base_ttl': 900, 'max_ttl': 3600, 'strategy': CacheStrategy.LFU}
        }

        # Cache performance tracking
        self.cache_stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0,
            'evictions': 0,
            'last_reset': time.time()
        }

        # Cache warming strategies
        self.warmup_strategies: Dict[str, Callable] = {}
    
    async def initialize(self):
        """Initialize the cache service."""
        if self._connection_attempted:
            return
        
        self._connection_attempted = True
        
        try:
            redis_url = config.get('cache.redis_url')
            redis_password = config.get('cache.redis_password')
            
            if redis_url:
                # Try URL method first, fallback to direct connection
                try:
                    # Use the Redis URL directly if it's complete
                    if redis_url.startswith('redis://'):
                        final_url = redis_url
                    else:
                        # Construct URL from components
                        final_url = f"redis://:{redis_password}@{redis_url}" if redis_password else f"redis://{redis_url}"
                    
                    # Create Redis connection using URL
                    self.redis_client = redis.Redis.from_url(
                        final_url,
                        decode_responses=True,
                        socket_connect_timeout=int(os.getenv('REDIS_CONNECT_TIMEOUT', '5')),
                        socket_timeout=int(os.getenv('REDIS_SOCKET_TIMEOUT', '5')),
                        retry_on_timeout=True
                    )
                except Exception as url_error:
                    logger.warning(f"URL method failed: {url_error}, trying direct connection")
                    # Fallback to direct connection method
                    redis_host = os.getenv('REDIS_HOST', config.get('cache.redis_host', 'redis'))
                    redis_port = int(os.getenv('REDIS_PORT', config.get('cache.redis_port', 6379)))
                    redis_db = int(os.getenv('REDIS_DB', config.get('cache.redis_db', 0)))
                    
                    self.redis_client = redis.Redis(
                        host=redis_host,
                        port=redis_port,
                        password=redis_password,
                        db=redis_db,
                        decode_responses=True,
                        socket_connect_timeout=int(os.getenv('REDIS_CONNECT_TIMEOUT', '5')),
                        socket_timeout=int(os.getenv('REDIS_SOCKET_TIMEOUT', '5')),
                        retry_on_timeout=True
                    )
                
                # Test connection
                await self.redis_client.ping()
                logger.info("✅ Redis cache service initialized")
                
            else:
                logger.warning("⚠️ No Redis URL configured, using in-memory cache")
                self._fallback_mode = True
                
        except Exception as e:
            logger.warning(f"⚠️ Failed to connect to Redis: {e}, using in-memory cache")
            self._fallback_mode = True
            self.redis_client = None
    
    def _calculate_intelligent_ttl(self, key: str, data_type: str = None) -> int:
        """Calculate intelligent TTL based on data type and access patterns."""
        # Extract data type from key prefix
        if data_type is None:
            data_type = key.split(':')[0] if ':' in key else 'default'

        strategy = self.ttl_strategies.get(data_type, self.ttl_strategies.get('default', {}))
        base_ttl = strategy.get('base_ttl', 300)
        max_ttl = strategy.get('max_ttl', 1800)
        strategy_type = strategy.get('strategy', CacheStrategy.TTL)

        if strategy_type == CacheStrategy.ADAPTIVE:
            # Adaptive TTL based on hit rate and data freshness
            hit_rate = self._get_hit_rate()
            if hit_rate > 0.8:  # High hit rate, increase TTL
                return min(max_ttl, base_ttl * 2)
            elif hit_rate < 0.3:  # Low hit rate, decrease TTL
                return max(60, base_ttl // 2)
        elif strategy_type == CacheStrategy.LFU:
            # For frequently accessed data, use higher TTL
            access_count = getattr(self, '_access_counts', {}).get(key, 0)
            return min(max_ttl, base_ttl + (access_count * 60))

        return base_ttl

    def _get_hit_rate(self) -> float:
        """Calculate current cache hit rate."""
        total_requests = self.cache_stats['hits'] + self.cache_stats['misses']
        return self.cache_stats['hits'] / total_requests if total_requests > 0 else 0.0

    async def get(self, key: str, track_access: bool = True) -> Optional[Any]:
        """Get value from cache with performance tracking."""
        # Try L1 cache first (memory)
        if key in self.memory_cache:
            if track_access:
                self.cache_stats['hits'] += 1
                # Update access tracking for LFU
                if not hasattr(self, '_access_counts'):
                    self._access_counts = {}
                self._access_counts[key] = self._access_counts.get(key, 0) + 1
            return self.memory_cache[key]

        # Try L2 cache (Redis)
        if not self._fallback_mode and self.redis_client:
            try:
                value = await self.redis_client.get(key)
                if value:
                    parsed_value = json.loads(value)
                    # Promote to L1 cache
                    self.memory_cache[key] = parsed_value
                    if track_access:
                        self.cache_stats['hits'] += 1
                    return parsed_value
            except Exception as e:
                logger.warning(f"Redis get error for key {key}: {e}")

        if track_access:
            self.cache_stats['misses'] += 1
        return None
    
    async def set(self, key: str, value: Any, ttl: Optional[Union[int, timedelta]] = None,
                  data_type: str = None, priority: CachePriority = CachePriority.MEDIUM) -> bool:
        """Set value in cache with intelligent TTL and multi-level caching."""
        try:
            self.cache_stats['sets'] += 1

            # Calculate intelligent TTL if not provided
            if ttl is None:
                ttl = self._calculate_intelligent_ttl(key, data_type)
            elif isinstance(ttl, timedelta):
                ttl = int(ttl.total_seconds())

            # Store in L1 cache (memory) with priority-based eviction
            self.memory_cache[key] = value

            # Apply priority-based memory management
            if len(self.memory_cache) > 1000:  # Configurable limit
                self._evict_low_priority_items()

            # Store in L2 cache (Redis) if available
            if not self._fallback_mode and self.redis_client:
                try:
                    await self.redis_client.setex(key, ttl, json.dumps(value))

                    # Add metadata for cache management
                    metadata_key = f"{key}:metadata"
                    metadata = {
                        'ttl': ttl,
                        'data_type': data_type,
                        'priority': priority.value,
                        'created_at': time.time(),
                        'access_count': 0
                    }
                    await self.redis_client.setex(metadata_key, ttl, json.dumps(metadata))

                    return True
                except Exception as e:
                    logger.warning(f"Redis set error for key {key}: {e}")
                    # Continue with memory-only caching

            return True

        except Exception as e:
            logger.warning(f"Cache set error for key {key}: {e}")
            return False

    def _evict_low_priority_items(self):
        """Evict low priority items from memory cache when limit is reached."""
        if not self.memory_cache:
            return

        # Simple LRU eviction for now - could be enhanced with priority-based eviction
        # Remove oldest 10% of items
        items_to_remove = len(self.memory_cache) // 10
        keys_to_remove = list(self.memory_cache.keys())[:items_to_remove]

        for key in keys_to_remove:
            del self.memory_cache[key]
            self.cache_stats['evictions'] += 1

        logger.debug(f"Evicted {items_to_remove} items from memory cache")
    
    async def delete(self, key: str) -> bool:
        """Delete value from cache."""
        try:
            if not self._fallback_mode and self.redis_client:
                result = await self.redis_client.delete(key)
                return result > 0
            else:
                return self.memory_cache.pop(key, None) is not None
        except Exception as e:
            logger.warning(f"Cache delete error for key {key}: {e}")
            return False
    
    async def exists(self, key: str) -> bool:
        """Check if key exists in cache."""
        try:
            if not self._fallback_mode and self.redis_client:
                return await self.redis_client.exists(key) > 0
            else:
                return key in self.memory_cache
        except Exception as e:
            logger.warning(f"Cache exists error for key {key}: {e}")
            return False
    
    async def clear(self, pattern: str = "*") -> int:
        """Clear cache entries matching pattern."""
        try:
            if not self._fallback_mode and self.redis_client:
                keys = await self.redis_client.keys(pattern)
                if keys:
                    return await self.redis_client.delete(*keys)
                return 0
            else:
                # For memory cache, clear all if pattern is "*"
                if pattern == "*":
                    count = len(self.memory_cache)
                    self.memory_cache.clear()
                    return count
                return 0
        except Exception as e:
            logger.warning(f"Cache clear error for pattern {pattern}: {e}")
            return 0
    
    async def invalidate_pattern(self, pattern: str, reason: str = "manual") -> int:
        """Invalidate cache entries matching pattern with reason tracking."""
        try:
            deleted_count = 0

            # Clear from L1 cache
            keys_to_remove = [k for k in self.memory_cache.keys() if pattern.replace('*', '') in k]
            for key in keys_to_remove:
                del self.memory_cache[key]
                deleted_count += 1

            # Clear from L2 cache
            if not self._fallback_mode and self.redis_client:
                try:
                    redis_keys = await self.redis_client.keys(pattern)
                    if redis_keys:
                        await self.redis_client.delete(*redis_keys)
                        deleted_count += len(redis_keys)

                        # Also delete metadata keys
                        metadata_keys = [f"{key}:metadata" for key in redis_keys]
                        if metadata_keys:
                            await self.redis_client.delete(*metadata_keys)

                except Exception as e:
                    logger.warning(f"Redis pattern invalidation error for {pattern}: {e}")

            self.cache_stats['deletes'] += deleted_count
            logger.info(f"Invalidated {deleted_count} cache entries matching '{pattern}' (reason: {reason})")
            return deleted_count

        except Exception as e:
            logger.warning(f"Cache invalidation error for pattern {pattern}: {e}")
            return 0

    async def warmup_cache(self, strategy_name: str) -> bool:
        """Execute cache warming strategy."""
        if strategy_name not in self.warmup_strategies:
            logger.warning(f"Cache warming strategy '{strategy_name}' not found")
            return False

        try:
            await self.warmup_strategies[strategy_name]()
            logger.info(f"Cache warming strategy '{strategy_name}' executed successfully")
            return True
        except Exception as e:
            logger.error(f"Cache warming strategy '{strategy_name}' failed: {e}")
            return False

    def register_warmup_strategy(self, name: str, strategy_func: Callable):
        """Register a cache warming strategy."""
        self.warmup_strategies[name] = strategy_func
        logger.info(f"Cache warming strategy '{name}' registered")

    async def get_stats(self) -> Dict[str, Any]:
        """Get comprehensive cache statistics."""
        try:
            base_stats = {
                'cache_performance': {
                    'hits': self.cache_stats['hits'],
                    'misses': self.cache_stats['misses'],
                    'sets': self.cache_stats['sets'],
                    'deletes': self.cache_stats['deletes'],
                    'evictions': self.cache_stats['evictions'],
                    'hit_rate': self._get_hit_rate(),
                    'last_reset': self.cache_stats['last_reset']
                },
                'memory_cache': {
                    'l1_entries': len(self.memory_cache),
                    'l1_size_bytes': len(str(self.memory_cache))
                },
                'ttl_strategies': self.ttl_strategies
            }

            if not self._fallback_mode and self.redis_client:
                info = await self.redis_client.info()
                redis_stats = {
                    'type': 'redis',
                    'connected': True,
                    'redis_memory_used': info.get('used_memory_human', 'Unknown'),
                    'redis_keys': info.get('db0', {}).get('keys', 0),
                    'redis_hits': info.get('keyspace_hits', 0),
                    'redis_misses': info.get('keyspace_misses', 0),
                    'redis_evictions': info.get('evicted_keys', 0)
                }
                base_stats.update(redis_stats)
            else:
                base_stats.update({
                    'type': 'memory_fallback',
                    'connected': False
                })

            return base_stats

        except Exception as e:
            logger.warning(f"Cache stats error: {e}")
            return {
                'type': 'error',
                'connected': False,
                'error': str(e)
            }
    
    async def close(self):
        """Close the cache service."""
        if self.redis_client:
            await self.redis_client.close()
            logger.info("✅ Enhanced Redis cache service closed")

    async def health_check(self) -> Dict[str, Any]:
        """Perform cache service health check."""
        health_status = {
            'service': 'cache',
            'healthy': True,
            'checks': {}
        }

        # Check Redis connectivity
        if not self._fallback_mode and self.redis_client:
            try:
                await self.redis_client.ping()
                health_status['checks']['redis'] = {'status': 'healthy', 'latency_ms': 0}
            except Exception as e:
                health_status['checks']['redis'] = {'status': 'unhealthy', 'error': str(e)}
                health_status['healthy'] = False
        else:
            health_status['checks']['redis'] = {'status': 'disabled', 'fallback_mode': True}

        # Check memory cache
        health_status['checks']['memory'] = {
            'status': 'healthy',
            'entries': len(self.memory_cache)
        }

        # Performance metrics
        health_status['metrics'] = {
            'hit_rate': self._get_hit_rate(),
            'total_operations': sum(self.cache_stats.values())
        }

        return health_status

    def reset_stats(self):
        """Reset cache performance statistics."""
        self.cache_stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0,
            'evictions': 0,
            'last_reset': time.time()
        }
        logger.info("Cache statistics reset")

    async def preload_common_data(self) -> bool:
        """Preload commonly accessed data into cache."""
        try:
            # This would be customized based on application needs
            # For example, preload common intent patterns, config data, etc.
            logger.info("Cache preloading completed")
            return True
        except Exception as e:
            logger.error(f"Cache preloading failed: {e}")
            return False

# Global cache service instance
cache_service = CacheService()

def get_cache_stats() -> Dict[str, Any]:
    """Get cache statistics for monitoring"""
    cache = get_cache()
    try:
        # Handle both real cache and mock objects
        if hasattr(cache, '_cache') and hasattr(cache._cache, '__len__'):
            total_entries = len(cache._cache)
            memory_usage = f"{len(str(cache._cache))} bytes"
        else:
            total_entries = 0
            memory_usage = "N/A"

        hit_rate = getattr(cache, 'hit_rate', 0.0)
        max_size = getattr(cache, 'max_size', 1000)
        ttl_seconds = getattr(cache, 'default_ttl', 300)
    except (TypeError, AttributeError):
        total_entries = 0
        memory_usage = "N/A"
        hit_rate = 0.0
        max_size = 1000
        ttl_seconds = 300

    return {
        "total_entries": total_entries,
        "hit_rate": hit_rate,
        "memory_usage": memory_usage,
        "max_size": max_size,
        "ttl_seconds": ttl_seconds
    }

def get_cache() -> CacheService:
    """Get the global cache service instance"""
    return cache_service