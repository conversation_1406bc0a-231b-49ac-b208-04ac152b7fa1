"""
AI Chat Models
Data models for AI chat components.
"""

from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from datetime import datetime

@dataclass
class AIAskResult:
    """Result from AI ask operation"""
    
    # Core response data
    response: str
    confidence: float
    
    # Metadata
    model_used: str
    tokens_used: int
    processing_time: float
    timestamp: datetime
    
    # Context and reasoning
    reasoning: Optional[str] = None
    sources: Optional[List[str]] = None
    context_used: Optional[Dict[str, Any]] = None
    
    # Error handling
    error: Optional[str] = None
    fallback_used: bool = False
    
    # Performance metrics
    cache_hit: bool = False
    retry_count: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'response': self.response,
            'confidence': self.confidence,
            'model_used': self.model_used,
            'tokens_used': self.tokens_used,
            'processing_time': self.processing_time,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None,
            'reasoning': self.reasoning,
            'sources': self.sources,
            'context_used': self.context_used,
            'error': self.error,
            'fallback_used': self.fallback_used,
            'cache_hit': self.cache_hit,
            'retry_count': self.retry_count
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AIAskResult':
        """Create from dictionary"""
        timestamp = None
        if data.get('timestamp'):
            timestamp = datetime.fromisoformat(data['timestamp'])
        
        return cls(
            response=data['response'],
            confidence=data['confidence'],
            model_used=data['model_used'],
            tokens_used=data['tokens_used'],
            processing_time=data['processing_time'],
            timestamp=timestamp,
            reasoning=data.get('reasoning'),
            sources=data.get('sources'),
            context_used=data.get('context_used'),
            error=data.get('error'),
            fallback_used=data.get('fallback_used', False),
            cache_hit=data.get('cache_hit', False),
            retry_count=data.get('retry_count', 0)
        )
    
    @property
    def is_success(self) -> bool:
        """Check if the result is successful"""
        return self.error is None
    
    @property
    def is_high_confidence(self) -> bool:
        """Check if the result has high confidence"""
        return self.confidence >= 0.8

@dataclass
class AIRequest:
    """Request to AI service"""
    
    query: str
    context: Optional[Dict[str, Any]] = None
    model: Optional[str] = None
    max_tokens: Optional[int] = None
    temperature: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'query': self.query,
            'context': self.context,
            'model': self.model,
            'max_tokens': self.max_tokens,
            'temperature': self.temperature
        }

@dataclass
class AIResponse:
    """Response from AI service"""
    
    content: str
    model: str
    tokens_used: int
    finish_reason: str
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'content': self.content,
            'model': self.model,
            'tokens_used': self.tokens_used,
            'finish_reason': self.finish_reason
        }
