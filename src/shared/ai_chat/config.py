"""
AI Chat Configuration Module
Provides configuration for AI chat components.
"""

import os
from typing import Dict, Any, Optional
from dataclasses import dataclass

@dataclass
class Config:
    """Configuration for AI chat components"""
    
    # AI Model settings
    model_name: str = "gpt-4o-mini"
    max_tokens: int = 4000
    temperature: float = 0.7
    
    # API settings
    api_timeout: int = 30
    max_retries: int = 3
    
    # Rate limiting
    requests_per_minute: int = 60
    
    # Circuit breaker settings
    failure_threshold: int = 5
    recovery_timeout: int = 60
    
    @classmethod
    def from_env(cls) -> 'Config':
        """Create config from environment variables"""
        return cls(
            model_name=os.getenv('AI_MODEL_NAME', 'gpt-4o-mini'),
            max_tokens=int(os.getenv('AI_MAX_TOKENS', '4000')),
            temperature=float(os.getenv('AI_TEMPERATURE', '0.7')),
            api_timeout=int(os.getenv('AI_API_TIMEOUT', '30')),
            max_retries=int(os.getenv('AI_MAX_RETRIES', '3')),
            requests_per_minute=int(os.getenv('AI_REQUESTS_PER_MINUTE', '60')),
            failure_threshold=int(os.getenv('AI_FAILURE_THRESHOLD', '5')),
            recovery_timeout=int(os.getenv('AI_RECOVERY_TIMEOUT', '60'))
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert config to dictionary"""
        return {
            'model_name': self.model_name,
            'max_tokens': self.max_tokens,
            'temperature': self.temperature,
            'api_timeout': self.api_timeout,
            'max_retries': self.max_retries,
            'requests_per_minute': self.requests_per_minute,
            'failure_threshold': self.failure_threshold,
            'recovery_timeout': self.recovery_timeout
        }

    @classmethod
    def get(cls, section: str, key: str, default: Any = None) -> Any:
        """Get config value (compatibility method)"""
        config = get_config()
        if section == 'pipeline' and key == 'model':
            return config.model_name
        return default

# Global config instance
_config: Optional[Config] = None

def get_config() -> Config:
    """Get global config instance"""
    global _config
    if _config is None:
        _config = Config.from_env()
    return _config

def set_config(config: Config) -> None:
    """Set global config instance"""
    global _config
    _config = config
