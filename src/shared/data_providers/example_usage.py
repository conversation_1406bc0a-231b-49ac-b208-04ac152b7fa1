"""
Example Usage of Enhanced Data Fetcher

This file demonstrates how to use the enhanced data fetcher in various scenarios
within the current bot architecture.
"""

import asyncio
from typing import List, Dict, Any
from src.shared.data_providers import enhanced_data_fetcher, EnhancedDataResult

async def example_basic_usage():
    """Basic usage example"""
    print("=== Basic Usage Example ===")
    
    # Fetch data for multiple symbols
    symbols = ['AAPL', 'MSFT', 'GOOGL']
    results = await enhanced_data_fetcher.fetch_comprehensive_data(symbols)
    
    # Process results
    for symbol, result in results.items():
        if result.success:
            print(f"\n{symbol}:")
            print(f"  Price: {result.data.get('price', {}).get('price', 'N/A')}")
            print(f"  Quality Score: {result.quality_metrics.overall_quality:.2f}")
            print(f"  Data Points: {result.quality_metrics.data_points}")
            print(f"  Sources Used: {result.quality_metrics.data_sources_used}")
        else:
            print(f"\n{symbol}: FAILED - {result.error_message}")

async def example_ai_guided_fetching():
    """AI-guided fetching example"""
    print("\n=== AI-Guided Fetching Example ===")
    
    # Simulate AI analysis results
    symbols = ['AAPL', 'TSLA']
    ai_intent = 'stock_analysis'
    query_context = 'full analysis of Apple and Tesla stocks'
    
    results = await enhanced_data_fetcher.fetch_with_ai_guidance(
        symbols=symbols,
        ai_intent=ai_intent,
        query_context=query_context
    )
    
    # Process results with quality assessment
    for symbol, result in results.items():
        if result.success:
            print(f"\n{symbol} Analysis:")
            print(f"  Quality: {result.quality_metrics.overall_quality:.2f}")
            print(f"  Completeness: {result.quality_metrics.completeness_score:.2f}")
            print(f"  Freshness: {result.quality_metrics.freshness_score:.2f}")
            
            # Check for warnings
            if result.quality_metrics.validation_warnings:
                print(f"  Warnings: {result.quality_metrics.validation_warnings}")
            
            # Access specific data
            if 'price' in result.data:
                price_data = result.data['price']
                print(f"  Current Price: ${price_data.get('price', 'N/A')}")
            
            if 'indicators' in result.data:
                indicators = result.data['indicators']
                print(f"  SMA 20: {indicators.get('sma_20', 'N/A')}")
                print(f"  RSI: {indicators.get('rsi', 'N/A'):.2f}")

async def example_quality_assessment():
    """Quality assessment example"""
    print("\n=== Quality Assessment Example ===")
    
    symbols = ['AAPL', 'MSFT', 'GOOGL', 'INVALID_SYMBOL']
    results = await enhanced_data_fetcher.fetch_comprehensive_data(symbols)
    
    # Get quality summary
    summary = enhanced_data_fetcher.get_quality_summary(results)
    
    print(f"Total Symbols: {summary['total_symbols']}")
    print(f"Successful Fetches: {summary['successful_fetches']}")
    print(f"Success Rate: {summary['success_rate']:.2%}")
    print(f"Average Quality Score: {summary['average_quality_score']:.2f}")
    print(f"Total Warnings: {summary['total_warnings']}")
    
    if summary['common_warnings']:
        print(f"Common Warnings: {summary['common_warnings']}")
    
    print(f"Data Sources Used: {summary['data_sources_used']}")

async def example_error_handling():
    """Error handling example"""
    print("\n=== Error Handling Example ===")
    
    # Try to fetch data for invalid symbols
    symbols = ['INVALID1', 'INVALID2', 'AAPL']
    results = await enhanced_data_fetcher.fetch_comprehensive_data(symbols)
    
    for symbol, result in results.items():
        print(f"\n{symbol}:")
        if result.success:
            print(f"  ✅ Success - Quality: {result.quality_metrics.overall_quality:.2f}")
        else:
            print(f"  ❌ Failed - Error: {result.error_message}")
            
            # Handle different types of errors
            if "not found" in result.error_message.lower():
                print("  → Symbol not found, check spelling")
            elif "rate limit" in result.error_message.lower():
                print("  → Rate limit exceeded, try again later")
            elif "timeout" in result.error_message.lower():
                print("  → Request timeout, check network connection")

async def example_caching():
    """Caching example"""
    print("\n=== Caching Example ===")
    
    symbols = ['AAPL']
    
    # First fetch (will be cached)
    print("First fetch (will be cached):")
    start_time = asyncio.get_event_loop().time()
    results1 = await enhanced_data_fetcher.fetch_comprehensive_data(symbols)
    time1 = asyncio.get_event_loop().time() - start_time
    print(f"  Time taken: {time1:.2f}s")
    
    # Second fetch (should use cache)
    print("\nSecond fetch (should use cache):")
    start_time = asyncio.get_event_loop().time()
    results2 = await enhanced_data_fetcher.fetch_comprehensive_data(symbols)
    time2 = asyncio.get_event_loop().time() - start_time
    print(f"  Time taken: {time2:.2f}s")
    
    # Force refresh
    print("\nForce refresh (bypass cache):")
    start_time = asyncio.get_event_loop().time()
    results3 = await enhanced_data_fetcher.fetch_comprehensive_data(symbols, force_refresh=True)
    time3 = asyncio.get_event_loop().time() - start_time
    print(f"  Time taken: {time3:.2f}s")

async def example_integration_with_bot():
    """Example of integration with Discord bot commands"""
    print("\n=== Bot Integration Example ===")
    
    # Simulate a Discord command handler
    async def handle_analyze_command(symbols: List[str], user_id: str):
        """Handle /analyze command"""
        print(f"User {user_id} requested analysis for: {symbols}")
        
        # Fetch data with AI guidance
        results = await enhanced_data_fetcher.fetch_with_ai_guidance(
            symbols=symbols,
            ai_intent='stock_analysis',
            query_context='comprehensive stock analysis'
        )
        
        # Process results for Discord response
        response_data = []
        for symbol, result in results.items():
            if result.success:
                quality = result.quality_metrics
                response_data.append({
                    'symbol': symbol,
                    'price': result.data.get('price', {}).get('price', 'N/A'),
                    'quality': quality.overall_quality,
                    'warnings': quality.validation_warnings
                })
            else:
                response_data.append({
                    'symbol': symbol,
                    'error': result.error_message
                })
        
        return response_data
    
    # Test the command handler
    symbols = ['AAPL', 'MSFT']
    user_id = '123456789'
    
    response = await handle_analyze_command(symbols, user_id)
    
    print("Bot Response Data:")
    for item in response:
        if 'error' in item:
            print(f"  {item['symbol']}: ERROR - {item['error']}")
        else:
            print(f"  {item['symbol']}: ${item['price']} (Quality: {item['quality']:.2f})")
            if item['warnings']:
                print(f"    Warnings: {item['warnings']}")

async def example_advanced_usage():
    """Advanced usage example with custom data types"""
    print("\n=== Advanced Usage Example ===")
    
    # Fetch only price data for quick checks
    symbols = ['AAPL', 'MSFT', 'GOOGL']
    results = await enhanced_data_fetcher.fetch_comprehensive_data(
        symbols=symbols,
        data_types=['price'],  # Only fetch price data
        force_refresh=True
    )
    
    print("Price-only data:")
    for symbol, result in results.items():
        if result.success:
            price_data = result.data.get('price', {})
            print(f"  {symbol}: ${price_data.get('price', 'N/A')} "
                  f"(Quality: {result.quality_metrics.overall_quality:.2f})")
    
    # Fetch comprehensive data for analysis
    print("\nComprehensive data:")
    results = await enhanced_data_fetcher.fetch_comprehensive_data(
        symbols=['AAPL'],
        data_types=['price', 'historical', 'indicators'],
        force_refresh=True
    )
    
    for symbol, result in results.items():
        if result.success:
            print(f"\n{symbol} Comprehensive Analysis:")
            print(f"  Quality Score: {result.quality_metrics.overall_quality:.2f}")
            print(f"  Data Points: {result.quality_metrics.data_points}")
            print(f"  Sources: {result.quality_metrics.data_sources_used}")
            
            # Access price data
            if 'price' in result.data:
                price = result.data['price']
                print(f"  Price: ${price.get('price', 'N/A')}")
                print(f"  Volume: {price.get('volume', 'N/A')}")
            
            # Access historical data
            if 'historical' in result.data:
                historical = result.data['historical']
                if 'closes' in historical:
                    closes = historical['closes']
                    print(f"  Historical Range: ${min(closes):.2f} - ${max(closes):.2f}")
            
            # Access indicators
            if 'indicators' in result.data:
                indicators = result.data['indicators']
                print(f"  SMA 20: {indicators.get('sma_20', 'N/A')}")
                print(f"  SMA 50: {indicators.get('sma_50', 'N/A')}")
                print(f"  RSI: {indicators.get('rsi', 'N/A'):.2f}")
                print(f"  Volatility: {indicators.get('volatility', 'N/A'):.4f}")

async def main():
    """Run all examples"""
    print("Enhanced Data Fetcher Examples")
    print("=" * 50)
    
    try:
        await example_basic_usage()
        await example_ai_guided_fetching()
        await example_quality_assessment()
        await example_error_handling()
        await example_caching()
        await example_integration_with_bot()
        await example_advanced_usage()
        
        print("\n" + "=" * 50)
        print("All examples completed successfully!")
        
    except Exception as e:
        print(f"\nError running examples: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
