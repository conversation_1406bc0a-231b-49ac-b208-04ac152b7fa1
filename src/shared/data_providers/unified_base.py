"""
Unified base classes for shared data providers.
Consolidated from src/data/providers/base.py to provide comprehensive
base classes and interfaces for all data providers.
"""

import asyncio
import time
import logging
from enum import Enum, auto
from typing import Dict, List, Optional, Any, Union, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import numpy as np
import pandas as pd

from src.shared.error_handling.logging import get_logger

logger = get_logger(__name__)


class ProviderError(Exception):
    """Exception raised for provider errors."""

    def __init__(self, message: str = "Data provider error", provider_name: str = "unknown"):
        self.provider_name = provider_name
        self.message = f"{provider_name} provider error: {message}"
        super().__init__(self.message)


class ProviderTimeoutError(ProviderError):
    """Exception raised for provider timeout errors."""

    def __init__(self, message: str = "Provider timeout", provider_name: str = "unknown"):
        super().__init__(message, provider_name)


class ProviderRateLimitError(ProviderError):
    """Exception raised for provider rate limit errors."""

    def __init__(self, message: str = "Provider rate limit exceeded", provider_name: str = "unknown"):
        super().__init__(message, provider_name)


class ProviderType(Enum):
    """Types of data providers"""
    YAHOO_FINANCE = "yahoo_finance"
    POLYGON = "polygon"
    FINNHUB = "finnhub"
    ALPHA_VANTAGE = "alpha_vantage"
    ALPACA = "alpaca"
    MOCK = "mock"
    CUSTOM = "custom"


class ProviderStatus(Enum):
    """Status of a data provider"""
    INITIALIZING = "initializing"
    READY = "ready"
    RATE_LIMITED = "rate_limited"
    ERROR = "error"
    DISABLED = "disabled"


class TimeFrame(Enum):
    """Time frames for market data"""
    MINUTE = "1m"
    MINUTE_5 = "5m"
    MINUTE_15 = "15m"
    MINUTE_30 = "30m"
    HOUR = "1h"
    HOUR_4 = "4h"
    DAY = "1d"
    WEEK = "1w"
    MONTH = "1M"


class MarketDataType(Enum):
    """Types of market data"""
    QUOTE = "quote"
    HISTORICAL_PRICES = "historical_prices"
    COMPANY_INFO = "company_info"
    FINANCIALS = "financials"
    NEWS = "news"
    OPTIONS = "options"


class QualityScore(Enum):
    """Quality score levels for market data."""
    A_PLUS = "A+"
    A = "A"
    A_MINUS = "A-"
    B_PLUS = "B+"
    B = "B"
    B_MINUS = "B-"
    C_PLUS = "C+"
    C = "C"
    C_MINUS = "C-"
    D = "D"
    F = "F"

    @classmethod
    def from_score(cls, score: float) -> 'QualityScore':
        """Map a 0-100 score to a QualityScore level."""
        if score >= 97:
            return cls.A_PLUS
        if score >= 93:
            return cls.A
        if score >= 90:
            return cls.A_MINUS
        if score >= 87:
            return cls.B_PLUS
        if score >= 83:
            return cls.B
        if score >= 80:
            return cls.B_MINUS
        if score >= 77:
            return cls.C_PLUS
        if score >= 73:
            return cls.C
        if score >= 70:
            return cls.C_MINUS
        if score >= 60:
            return cls.D
        return cls.F


@dataclass
class DataProviderConfig:
    """Configuration for data providers."""
    name: str
    api_key: Optional[str] = None
    api_secret: Optional[str] = None
    base_url: Optional[str] = None
    rate_limit: int = 60  # requests per minute
    timeout: float = 10.0  # seconds
    retry_attempts: int = 3
    retry_delay: float = 1.0  # seconds
    additional_params: Dict[str, Any] = field(default_factory=dict)


@dataclass
class MarketDataRequest:
    """Request for market data."""
    symbol: str
    data_type: MarketDataType
    timeframe: Optional[TimeFrame] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    limit: Optional[int] = None
    additional_params: Dict[str, Any] = field(default_factory=dict)


@dataclass
class MarketDataResponse:
    """Response containing market data."""
    symbol: str
    data_type: MarketDataType
    data: Dict[str, Any]
    timestamp: datetime = field(default_factory=datetime.now)
    provider_name: str = ""
    quality_score: Optional[QualityScore] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class HistoricalData:
    """Historical market data structure."""
    symbol: str
    dates: List[str] = field(default_factory=list)
    opens: List[float] = field(default_factory=list)
    closes: List[float] = field(default_factory=list)
    highs: List[float] = field(default_factory=list)
    lows: List[float] = field(default_factory=list)
    volumes: List[int] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    additional_fields: Optional[Dict[str, Any]] = None


class BaseDataProvider:
    """Base class for all data providers."""

    def __init__(self, config: DataProviderConfig):
        """Initialize the data provider with configuration."""
        self.config = config
        self.name = config.name
        self.status = ProviderStatus.INITIALIZING
        self.last_request_time = 0
        self.request_count = 0
        self.error_count = 0
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")

        # Initialize the provider
        self._initialize()

    def _initialize(self):
        """Initialize the provider. Override in subclasses."""
        self.status = ProviderStatus.READY

    async def get_current_price(self, symbol: str) -> Dict[str, Any]:
        """Get current price for a symbol. Must be implemented by subclasses."""
        raise NotImplementedError("Subclasses must implement get_current_price")

    async def get_historical_data(self, symbol: str, *args, **kwargs) -> Any:
        """Get historical data for a symbol. Must be implemented by subclasses."""
        raise NotImplementedError("Subclasses must implement get_historical_data")

    async def get_market_data(self, request: MarketDataRequest) -> MarketDataResponse:
        """Get market data based on request. Can be overridden by subclasses."""
        if request.data_type == MarketDataType.QUOTE:
            data = await self.get_current_price(request.symbol)
        elif request.data_type == MarketDataType.HISTORICAL_PRICES:
            data = await self.get_historical_data(request.symbol)
        else:
            raise NotImplementedError(f"Data type {request.data_type} not supported")

        return MarketDataResponse(
            symbol=request.symbol,
            data_type=request.data_type,
            data=data,
            provider_name=self.name
        )

    def _check_rate_limit(self):
        """Check if rate limit allows a new request."""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        min_interval = 60.0 / self.config.rate_limit  # seconds between requests

        if time_since_last < min_interval:
            raise ProviderRateLimitError(
                f"Rate limit exceeded. Wait {min_interval - time_since_last:.2f} seconds",
                self.name
            )

        self.last_request_time = current_time
        self.request_count += 1


class UnifiedDataProvider(BaseDataProvider):
    """Unified data provider with enhanced functionality."""

    def __init__(self, provider_name: str, provider_type: str, config: Optional[Dict[str, Any]] = None):
        """Initialize unified data provider."""
        # Create DataProviderConfig from dict
        if isinstance(config, dict):
            provider_config = DataProviderConfig(
                name=provider_name,
                api_key=config.get('api_key'),
                base_url=config.get('base_url'),
                rate_limit=config.get('rate_limit', 60),
                timeout=config.get('timeout', 10.0),
                retry_attempts=config.get('max_retries', 3),
                retry_delay=config.get('retry_delay', 1.0),
                additional_params=config or {}
            )
        elif isinstance(config, DataProviderConfig):
            provider_config = config
        else:
            provider_config = DataProviderConfig(name=provider_name)

        super().__init__(provider_config)
        self.provider_name = provider_name
        self.provider_type = provider_type
        self.is_configured = True


__all__ = [
    'BaseDataProvider',
    'UnifiedDataProvider',
    'DataProviderConfig',
    'MarketDataRequest',
    'MarketDataResponse',
    'HistoricalData',
    'ProviderError',
    'ProviderTimeoutError',
    'ProviderRateLimitError',
    'ProviderType',
    'ProviderStatus',
    'TimeFrame',
    'MarketDataType',
    'QualityScore'
]