"""
Data Providers Package

This package provides comprehensive data fetching capabilities for market data,
including multiple data sources, intelligent fallbacks, and enhanced data quality assessment.

Main Components:
- DataProviderAggregator: Manages multiple providers with fallback logic
- EnhancedDataFetcher: Advanced data fetching with AI integration and quality assessment
- Individual Providers: Specific implementations for different data sources
"""

from .aggregator import data_provider_aggregator, DataProviderAggregator
from .enhanced_data_fetcher import enhanced_data_fetcher, EnhancedDataFetcher, DataQualityMetrics, EnhancedDataResult

__all__ = [
    'data_provider_aggregator',
    'DataProviderAggregator',
    'enhanced_data_fetcher',
    'EnhancedDataFetcher',
    'DataQualityMetrics',
    'EnhancedDataResult'
]
