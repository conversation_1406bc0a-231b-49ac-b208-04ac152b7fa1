"""
Enhanced Error Handler for Data Providers

Provides comprehensive error handling, fallback mechanisms, and resilience
for data provider operations. Includes circuit breaker patterns, retry logic,
and intelligent fallback strategies.
"""

import asyncio
import logging
import time
from typing import Dict, Any, List, Optional, Callable, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import json

logger = logging.getLogger(__name__)


class ErrorSeverity(Enum):
    """Error severity levels"""
    LOW = "low"           # Minor issues, continue operation
    MEDIUM = "medium"     # Significant issues, try fallback
    HIGH = "high"         # Critical issues, circuit breaker
    CRITICAL = "critical" # System-wide failure


class ProviderState(Enum):
    """Provider circuit breaker states"""
    CLOSED = "closed"       # Normal operation
    OPEN = "open"          # Provider disabled due to failures
    HALF_OPEN = "half_open" # Testing if provider recovered


@dataclass
class ErrorMetrics:
    """Error tracking metrics for a provider"""
    total_requests: int = 0
    failed_requests: int = 0
    consecutive_failures: int = 0
    last_failure_time: Optional[datetime] = None
    last_success_time: Optional[datetime] = None
    error_types: Dict[str, int] = field(default_factory=dict)
    
    @property
    def failure_rate(self) -> float:
        """Calculate failure rate percentage"""
        if self.total_requests == 0:
            return 0.0
        return (self.failed_requests / self.total_requests) * 100
    
    @property
    def is_healthy(self) -> bool:
        """Check if provider is healthy based on metrics"""
        return (
            self.failure_rate < 50 and  # Less than 50% failure rate
            self.consecutive_failures < 5 and  # Less than 5 consecutive failures
            (self.last_success_time is None or 
             (datetime.now() - self.last_success_time).total_seconds() < 3600)  # Success within last hour
        )


@dataclass
class CircuitBreakerConfig:
    """Configuration for circuit breaker"""
    failure_threshold: int = 5  # Number of failures to open circuit
    recovery_timeout: int = 60  # Seconds before trying half-open
    success_threshold: int = 3  # Successes needed to close circuit
    max_failure_rate: float = 80.0  # Maximum failure rate percentage


class EnhancedErrorHandler:
    """
    Enhanced error handler with circuit breaker pattern and intelligent fallbacks.
    
    Features:
    - Circuit breaker pattern for failing providers
    - Intelligent retry logic with exponential backoff
    - Error classification and severity assessment
    - Comprehensive metrics tracking
    - Fallback provider management
    """
    
    def __init__(self, config: Optional[CircuitBreakerConfig] = None):
        """Initialize the enhanced error handler"""
        self.config = config or CircuitBreakerConfig()
        
        # Provider state tracking
        self.provider_states: Dict[str, ProviderState] = {}
        self.provider_metrics: Dict[str, ErrorMetrics] = {}
        self.circuit_open_times: Dict[str, datetime] = {}
        
        # Fallback configuration
        self.fallback_chains: Dict[str, List[str]] = {
            'market_data': ['yfinance', 'polygon', 'finnhub', 'alpha_vantage', 'fallback'],
            'historical_data': ['polygon', 'yfinance', 'alpha_vantage', 'fallback'],
            'real_time': ['polygon', 'finnhub', 'yfinance', 'fallback']
        }
        
        logger.info("✅ Enhanced error handler initialized")
    
    async def execute_with_fallback(
        self,
        operation: str,
        providers: List[str],
        operation_func: Callable,
        *args,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Execute an operation with automatic fallback to alternative providers.
        
        Args:
            operation: Name of the operation (for logging)
            providers: List of provider names in priority order
            operation_func: Function to execute (should accept provider_name as first arg)
            *args, **kwargs: Arguments to pass to operation_func
            
        Returns:
            Dict containing result data and metadata
        """
        start_time = time.time()
        last_error = None
        
        for provider_name in providers:
            # Check circuit breaker state
            if not self._is_provider_available(provider_name):
                logger.debug(f"⚠️ Provider {provider_name} circuit breaker is open, skipping")
                continue
            
            try:
                # Execute operation with retry logic
                result = await self._execute_with_retry(
                    provider_name, operation_func, provider_name, *args, **kwargs
                )
                
                # Record success
                self._record_success(provider_name)
                
                duration = time.time() - start_time
                logger.info(f"✅ {operation} successful via {provider_name} ({duration:.2f}s)")
                
                return {
                    'success': True,
                    'data': result,
                    'provider': provider_name,
                    'duration': duration,
                    'attempts': 1,
                    'timestamp': datetime.now().isoformat()
                }
                
            except Exception as e:
                last_error = e
                severity = self._classify_error(e)
                
                # Record failure
                self._record_failure(provider_name, str(e), severity)
                
                logger.warning(f"⚠️ {operation} failed via {provider_name}: {e}")
                
                # If critical error, don't try other providers
                if severity == ErrorSeverity.CRITICAL:
                    break
                
                continue
        
        # All providers failed
        duration = time.time() - start_time
        logger.error(f"❌ {operation} failed on all providers ({duration:.2f}s)")
        
        return {
            'success': False,
            'data': None,
            'provider': None,
            'duration': duration,
            'error': str(last_error) if last_error else 'All providers failed',
            'timestamp': datetime.now().isoformat()
        }
    
    async def _execute_with_retry(
        self,
        provider_name: str,
        func: Callable,
        *args,
        max_retries: int = 3,
        **kwargs
    ) -> Any:
        """Execute function with exponential backoff retry logic"""
        last_exception = None
        
        for attempt in range(max_retries):
            try:
                if attempt > 0:
                    # Exponential backoff: 1s, 2s, 4s
                    delay = 2 ** (attempt - 1)
                    logger.debug(f"Retrying {provider_name} in {delay}s (attempt {attempt + 1})")
                    await asyncio.sleep(delay)
                
                result = await func(*args, **kwargs)
                return result
                
            except Exception as e:
                last_exception = e
                logger.debug(f"Attempt {attempt + 1} failed for {provider_name}: {e}")
                
                # Don't retry on certain error types
                if self._is_non_retryable_error(e):
                    break
        
        # All retries exhausted
        raise last_exception
    
    def _is_provider_available(self, provider_name: str) -> bool:
        """Check if provider is available (circuit breaker logic)"""
        state = self.provider_states.get(provider_name, ProviderState.CLOSED)
        
        if state == ProviderState.CLOSED:
            return True
        elif state == ProviderState.OPEN:
            # Check if recovery timeout has passed
            open_time = self.circuit_open_times.get(provider_name)
            if open_time and (datetime.now() - open_time).total_seconds() > self.config.recovery_timeout:
                # Move to half-open state
                self.provider_states[provider_name] = ProviderState.HALF_OPEN
                logger.info(f"🔄 Provider {provider_name} circuit breaker moved to HALF_OPEN")
                return True
            return False
        elif state == ProviderState.HALF_OPEN:
            return True
        
        return False
    
    def _record_success(self, provider_name: str):
        """Record successful operation for a provider"""
        if provider_name not in self.provider_metrics:
            self.provider_metrics[provider_name] = ErrorMetrics()
        
        metrics = self.provider_metrics[provider_name]
        metrics.total_requests += 1
        metrics.consecutive_failures = 0
        metrics.last_success_time = datetime.now()
        
        # Circuit breaker logic for success
        state = self.provider_states.get(provider_name, ProviderState.CLOSED)
        if state == ProviderState.HALF_OPEN:
            # Check if we have enough successes to close circuit
            if metrics.consecutive_failures == 0:  # This success resets consecutive failures
                self.provider_states[provider_name] = ProviderState.CLOSED
                logger.info(f"✅ Provider {provider_name} circuit breaker CLOSED (recovered)")
    
    def _record_failure(self, provider_name: str, error: str, severity: ErrorSeverity):
        """Record failed operation for a provider"""
        if provider_name not in self.provider_metrics:
            self.provider_metrics[provider_name] = ErrorMetrics()
        
        metrics = self.provider_metrics[provider_name]
        metrics.total_requests += 1
        metrics.failed_requests += 1
        metrics.consecutive_failures += 1
        metrics.last_failure_time = datetime.now()
        
        # Track error types
        error_type = type(error).__name__ if hasattr(error, '__class__') else 'Unknown'
        metrics.error_types[error_type] = metrics.error_types.get(error_type, 0) + 1
        
        # Circuit breaker logic for failure
        if (metrics.consecutive_failures >= self.config.failure_threshold or
            metrics.failure_rate > self.config.max_failure_rate):
            
            self.provider_states[provider_name] = ProviderState.OPEN
            self.circuit_open_times[provider_name] = datetime.now()
            logger.warning(f"🔴 Provider {provider_name} circuit breaker OPENED due to failures")
    
    def _classify_error(self, error: Exception) -> ErrorSeverity:
        """Classify error severity based on error type and message"""
        error_str = str(error).lower()
        error_type = type(error).__name__
        
        # Critical errors - don't try other providers
        if any(keyword in error_str for keyword in ['authentication', 'unauthorized', 'forbidden']):
            return ErrorSeverity.CRITICAL
        
        # High severity - circuit breaker candidate
        if any(keyword in error_str for keyword in ['timeout', 'connection', 'network']):
            return ErrorSeverity.HIGH
        
        # Medium severity - try fallback
        if any(keyword in error_str for keyword in ['rate limit', 'quota', 'throttle']):
            return ErrorSeverity.MEDIUM
        
        # Default to medium severity
        return ErrorSeverity.MEDIUM
    
    def _is_non_retryable_error(self, error: Exception) -> bool:
        """Check if error should not be retried"""
        error_str = str(error).lower()
        
        # Don't retry authentication or permission errors
        non_retryable_keywords = [
            'authentication', 'unauthorized', 'forbidden', 'invalid api key',
            'permission denied', 'access denied', 'bad request'
        ]
        
        return any(keyword in error_str for keyword in non_retryable_keywords)
    
    def get_provider_health_report(self) -> Dict[str, Any]:
        """Get comprehensive health report for all providers"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'providers': {},
            'summary': {
                'total_providers': len(self.provider_metrics),
                'healthy_providers': 0,
                'unhealthy_providers': 0,
                'circuit_breaker_open': 0
            }
        }
        
        for provider_name, metrics in self.provider_metrics.items():
            state = self.provider_states.get(provider_name, ProviderState.CLOSED)
            is_healthy = metrics.is_healthy and state != ProviderState.OPEN
            
            report['providers'][provider_name] = {
                'state': state.value,
                'healthy': is_healthy,
                'metrics': {
                    'total_requests': metrics.total_requests,
                    'failed_requests': metrics.failed_requests,
                    'failure_rate': round(metrics.failure_rate, 2),
                    'consecutive_failures': metrics.consecutive_failures,
                    'last_success': metrics.last_success_time.isoformat() if metrics.last_success_time else None,
                    'last_failure': metrics.last_failure_time.isoformat() if metrics.last_failure_time else None,
                    'error_types': metrics.error_types
                }
            }
            
            # Update summary
            if is_healthy:
                report['summary']['healthy_providers'] += 1
            else:
                report['summary']['unhealthy_providers'] += 1
            
            if state == ProviderState.OPEN:
                report['summary']['circuit_breaker_open'] += 1
        
        return report
    
    def reset_provider_metrics(self, provider_name: str):
        """Reset metrics for a specific provider"""
        if provider_name in self.provider_metrics:
            self.provider_metrics[provider_name] = ErrorMetrics()
            self.provider_states[provider_name] = ProviderState.CLOSED
            if provider_name in self.circuit_open_times:
                del self.circuit_open_times[provider_name]
            logger.info(f"🔄 Reset metrics for provider {provider_name}")


# Global instance
enhanced_error_handler = EnhancedErrorHandler()
