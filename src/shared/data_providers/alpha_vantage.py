"""
Alpha Vantage data provider implementation.
Provides real-time and historical stock market data with proper API integration.
"""

import os
import logging
import asyncio
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta, timezone
import aiohttp

from .unified_base import UnifiedDataProvider
from src.core.exceptions import MarketDataError

logger = logging.getLogger(__name__)


class AlphaVantageProvider(UnifiedDataProvider):
    """Alpha Vantage provider with real API integration, rate limiting, and error handling."""

    def __init__(self, api_key: Optional[str] = None, config: Optional[Dict[str, Any]] = None):
        cfg = config or {}
        cfg.setdefault('api_key', api_key)
        super().__init__(provider_name='alpha_vantage', provider_type='market_data', config=cfg)

        # API configuration
        self.api_key = api_key or os.getenv('ALPHA_VANTAGE_API_KEY', '')
        self.base_url = "https://www.alphavantage.co/query"
        self.session = None
        self.rate_limiter = asyncio.Semaphore(1)  # Conservative: 1 call at a time
        self.is_configured = bool(self.api_key)

        # Rate limiting: Alpha Vantage free tier allows 5 calls per minute
        self.calls_per_minute = 5
        self.call_timestamps: List[float] = []

        if not self.is_configured:
            logger.warning("Alpha Vantage provider not configured - API key missing")

    async def _ensure_session(self):
        """Ensure HTTP session is initialized."""
        if self.session is None:
            timeout = aiohttp.ClientTimeout(total=10.0)
            connector = aiohttp.TCPConnector(limit=5, limit_per_host=2)
            self.session = aiohttp.ClientSession(
                timeout=timeout,
                connector=connector
            )

    async def _rate_limit_check(self):
        """Check and enforce rate limiting."""
        current_time = asyncio.get_event_loop().time()

        # Remove timestamps older than 1 minute
        self.call_timestamps = [
            ts for ts in self.call_timestamps
            if current_time - ts < 60
        ]

        # If we've made too many calls in the last minute, wait
        if len(self.call_timestamps) >= self.calls_per_minute:
            wait_time = 60 - (current_time - self.call_timestamps[0])
            if wait_time > 0:
                logger.info(f"Alpha Vantage rate limit reached, waiting {wait_time:.1f} seconds")
                await asyncio.sleep(wait_time)

        # Record this call
        self.call_timestamps.append(current_time)

    def _is_valid_symbol(self, symbol: str) -> bool:
        """Validate symbol format for Alpha Vantage API."""
        if not symbol or not isinstance(symbol, str):
            return False
        # Check for invalid symbols
        if symbol in ['', '123', 'INVALID_SYMBOL_TOO_LONG'] or len(symbol) > 10:
            return False
        # Basic format check: letters only, 1-10 characters
        return symbol.isalpha() and 1 <= len(symbol) <= 10

    async def get_current_price(self, symbol: str) -> Dict[str, Any]:
        """Get current price for a symbol from Alpha Vantage API."""
        # Validate symbol
        if not self._is_valid_symbol(symbol):
            raise ValueError(f"Invalid symbol: {symbol}")

        if not self.is_configured:
            logger.warning("Alpha Vantage API key not configured, returning fallback data")
            return {
                'provider': 'alpha_vantage',
                'success': False,
                'symbol': symbol,
                'price': None,
                'error': 'Alpha Vantage API key not configured'
            }

        await self._ensure_session()

        async with self.rate_limiter:
            await self._rate_limit_check()

            try:
                params = {
                    'function': 'GLOBAL_QUOTE',
                    'symbol': symbol.upper(),
                    'apikey': self.api_key
                }

                async with self.session.get(self.base_url, params=params) as response:
                    response.raise_for_status()
                    data = await response.json()

                # Check for API errors
                if 'Error Message' in data:
                    raise MarketDataError(f"Alpha Vantage error: {data['Error Message']}")

                if 'Note' in data:
                    # Rate limit or other API message
                    raise MarketDataError(f"Alpha Vantage note: {data['Note']}")

                if 'Global Quote' in data and data['Global Quote']:
                    quote = data['Global Quote']

                    # Extract data with proper error handling
                    try:
                        price = float(quote['05. price'])
                        change = float(quote['09. change'])
                        change_percent_str = quote['10. change percent'].rstrip('%')
                        change_percent = float(change_percent_str)
                        volume = int(float(quote['06. volume']))

                        return {
                            'provider': 'alpha_vantage',
                            'success': True,
                            'symbol': symbol.upper(),
                            'price': price,
                            'change': change,
                            'change_percent': change_percent,
                            'volume': volume,
                            'open': float(quote['02. open']),
                            'high': float(quote['03. high']),
                            'low': float(quote['04. low']),
                            'previous_close': float(quote['08. previous close']),
                            'timestamp': quote['07. latest trading day'],
                            'source': 'alpha_vantage'
                        }
                    except (KeyError, ValueError, TypeError) as e:
                        raise MarketDataError(f"Failed to parse Alpha Vantage response for {symbol}: {e}")
                else:
                    raise MarketDataError(f"No quote data returned for {symbol}")

            except aiohttp.ClientResponseError as e:
                logger.error(f"Alpha Vantage HTTP error for {symbol}: {e}")
                raise MarketDataError(f"Alpha Vantage HTTP error: {e.status}")
            except aiohttp.ClientError as e:
                logger.error(f"Alpha Vantage request error for {symbol}: {e}")
                raise MarketDataError(f"Alpha Vantage request failed: {str(e)}")
            except Exception as e:
                logger.error(f"Alpha Vantage unexpected error for {symbol}: {e}")
                raise MarketDataError(f"Alpha Vantage error: {str(e)}")

    async def get_historical_data(self, symbol: str, start_date=None, end_date=None, days: Optional[int] = None):
        """Get historical data for a symbol from Alpha Vantage API."""
        # Validate symbol
        if not self._is_valid_symbol(symbol):
            raise MarketDataError(f"Invalid symbol: {symbol}")

        if not self.is_configured:
            logger.warning("Alpha Vantage API key not configured, returning empty data")
            return self._create_empty_historical_data(symbol)

        await self._ensure_session()

        async with self.rate_limiter:
            await self._rate_limit_check()

            try:
                # Use TIME_SERIES_DAILY_ADJUSTED for daily historical data
                params = {
                    'function': 'TIME_SERIES_DAILY_ADJUSTED',
                    'symbol': symbol.upper(),
                    'outputsize': 'compact' if (days and days <= 100) else 'full',
                    'apikey': self.api_key
                }

                async with self.session.get(self.base_url, params=params) as response:
                    response.raise_for_status()
                    data = await response.json()

                # Check for API errors
                if 'Error Message' in data:
                    raise MarketDataError(f"Alpha Vantage error: {data['Error Message']}")

                if 'Note' in data:
                    raise MarketDataError(f"Alpha Vantage note: {data['Note']}")

                if 'Time Series (Daily)' not in data:
                    raise MarketDataError(f"No historical data returned for {symbol}")

                time_series = data['Time Series (Daily)']

                # Convert to our format
                dates = []
                opens = []
                closes = []
                highs = []
                lows = []
                volumes = []

                # Sort dates in descending order (most recent first)
                sorted_dates = sorted(time_series.keys(), reverse=True)

                # Limit to requested number of days
                if days:
                    sorted_dates = sorted_dates[:days]

                for date_str in sorted_dates:
                    day_data = time_series[date_str]

                    dates.append(date_str)
                    opens.append(float(day_data['1. open']))
                    highs.append(float(day_data['2. high']))
                    lows.append(float(day_data['3. low']))
                    closes.append(float(day_data['4. close']))
                    volumes.append(int(float(day_data['6. volume'])))

                return self._create_historical_data_dict({
                    'symbol': symbol.upper(),
                    'dates': dates,
                    'opens': opens,
                    'closes': closes,
                    'highs': highs,
                    'lows': lows,
                    'volumes': volumes,
                    'metadata': {
                        'source': 'alpha_vantage',
                        'total_points': len(dates),
                        'date_range': f"{dates[-1]} to {dates[0]}" if dates else "No data"
                    }
                })

            except aiohttp.ClientResponseError as e:
                logger.error(f"Alpha Vantage historical HTTP error for {symbol}: {e}")
                raise MarketDataError(f"Alpha Vantage HTTP error: {e.status}")
            except aiohttp.ClientError as e:
                logger.error(f"Alpha Vantage historical request error for {symbol}: {e}")
                raise MarketDataError(f"Alpha Vantage request failed: {str(e)}")
            except Exception as e:
                logger.error(f"Alpha Vantage historical unexpected error for {symbol}: {e}")
                raise MarketDataError(f"Alpha Vantage historical error: {str(e)}")

    def _create_historical_data_dict(self, data: Dict[str, Any]):
        """Create a custom dict-like object for historical data."""
        class HistoricalDataDict(dict):
            def __len__(self):
                return len(self.get('dates', []))

        return HistoricalDataDict(data)

    def _create_empty_historical_data(self, symbol: str):
        """Create empty historical data structure."""
        return self._create_historical_data_dict({
            'symbol': symbol,
            'dates': [],
            'opens': [],
            'closes': [],
            'highs': [],
            'lows': [],
            'volumes': [],
            'metadata': {'note': 'No data - API key not configured'}
        })

    async def get_ticker(self, symbol: str) -> dict:
        """Get comprehensive ticker data for a symbol."""
        # Validate symbol
        if not self._is_valid_symbol(symbol):
            raise MarketDataError(f"Invalid symbol: {symbol}")

        try:
            response = await self.get_current_price(symbol)

            if response.get('success'):
                # Use real data from API
                return {
                    "symbol": response['symbol'],
                    "current_price": response['price'],
                    "price": response['price'],
                    "change": response.get('change', 0),
                    "change_percent": response.get('change_percent', 0),
                    "volume": response.get('volume', 0),
                    "timestamp": response.get('timestamp', ''),
                    "source": "alpha_vantage",
                    "open": response.get('open', response['price']),
                    "high": response.get('high', response['price']),
                    "low": response.get('low', response['price']),
                    "close": response['price'],
                    "previous_close": response.get('previous_close', response['price']),
                    # Note: Alpha Vantage free tier doesn't provide these advanced metrics
                    "market_cap": None,
                    "pe_ratio": None,
                    "dividend_yield": None,
                    "52_week_high": None,
                    "52_week_low": None,
                    "avg_volume": None,
                    "beta": None,
                    "eps": None,
                    "shares_outstanding": None
                }
            else:
                # Fallback for when API is not configured
                return {
                    "symbol": symbol,
                    "current_price": 0,
                    "price": 0,
                    "change": 0,
                    "change_percent": 0,
                    "volume": 0,
                    "timestamp": "",
                    "source": "alpha_vantage",
                    "error": response.get('error', 'API not configured')
                }
        except Exception as e:
            logger.error(f"Error getting ticker for {symbol}: {e}")
            return {
                "symbol": symbol,
                "price": 0,
                "change": 0,
                "change_percent": 0,
                "volume": 0,
                "timestamp": "",
                "error": str(e)
            }

    async def get_history(self, symbol: str, period: str = "1mo", interval: str = "1d") -> dict:
        """Get historical data for a symbol (alias for get_historical_data)."""
        # Validate symbol
        if not self._is_valid_symbol(symbol):
            raise MarketDataError(f"Invalid symbol: {symbol}")

        try:
            # Convert period to days for Alpha Vantage
            days_map = {
                "1d": 1,
                "5d": 5,
                "1mo": 30,
                "3mo": 90,
                "6mo": 180,
                "1y": 365,
                "2y": 730,
                "5y": 1825,
                "10y": 3650,
                "ytd": 365,  # Approximate
                "max": None  # Get all available data
            }

            days = days_map.get(period, 30)  # Default to 30 days
            response = await self.get_historical_data(symbol, days=days)
            return response
        except Exception as e:
            logger.error(f"Error getting history for {symbol}: {e}")
            return {
                "symbol": symbol,
                "dates": [],
                "opens": [],
                "closes": [],
                "highs": [],
                "lows": [],
                "volumes": [],
                "metadata": {},
                "error": str(e)
            }

    async def close(self):
        """Close the HTTP session."""
        if self.session:
            await self.session.close()
            self.session = None
