"""
Abstract Base Classes for Data Providers

This module defines the standard interface for all data providers,
ensuring consistency and interchangeability.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from datetime import datetime
import pandas as pd

class BaseDataProvider(ABC):
    """
    Abstract base class for all market data providers.
    Defines the common interface for fetching various types of market data.
    """

    def __init__(self, provider_name: str):
        """
        Initialize the data provider.

        Args:
            provider_name: The name of the data provider (e.g., 'polygon', 'yfinance').
        """
        self.provider_name = provider_name

    @abstractmethod
    async def get_current_price(self, symbol: str) -> Optional[float]:
        """
        Fetch the current price for a given symbol.

        Args:
            symbol: The stock or asset symbol.

        Returns:
            The current price as a float, or None if not found.
        """
        pass

    @abstractmethod
    async def get_historical_data(
        self,
        symbol: str,
        start_date: datetime,
        end_date: datetime,
        interval: str = '1d'
    ) -> Optional[pd.DataFrame]:
        """
        Fetch historical OHLCV data for a given symbol and date range.

        Args:
            symbol: The stock or asset symbol.
            start_date: The start date for the historical data.
            end_date: The end date for the historical data.
            interval: The data interval (e.g., '1d', '1h', '5m').

        Returns:
            A pandas DataFrame with the historical data, or None if an error occurs.
        """
        pass

    @abstractmethod
    async def check_health(self) -> Dict[str, Any]:
        """
        Check the health and connectivity of the data provider.

        Returns:
            A dictionary containing the health status.
        """
        pass