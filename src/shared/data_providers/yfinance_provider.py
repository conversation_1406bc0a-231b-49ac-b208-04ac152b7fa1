"""
Enhanced yfinance data provider with caching, rate limiting, and comprehensive market data.
"""
from functools import lru_cache
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List, Union
import logging

# Use lazy imports for heavy dependencies
from src.shared.utils.lazy_import import get_lazy_module, lazy_import

# Register yfinance for lazy loading
lazy_import("yfinance", "yfinance")

try:
    from ratelimit import limits, sleep_and_retry
except ImportError:
    # Fallback decorators if ratelimit is not available
    def limits(calls, period):
        def decorator(func):
            return func
        return decorator
    
    def sleep_and_retry(func):
        return func

from .unified_base import UnifiedDataProvider, HistoricalData

# Lazy load yfinance components
def _get_yf():
    """Get yfinance module with lazy loading."""
    return get_lazy_module("yfinance")

def _get_yf_exceptions():
    """Get yfinance exceptions with fallback handling."""
    try:
        yf = _get_yf()
        return getattr(yf, 'utils', None) or getattr(yf, 'exceptions', None)
    except:
        # If neither works, create a custom exception
        class YFNoDataError(Exception):
            pass
        return type('yf_exceptions', (), {'YFNoDataError': YFNoDataError})()

logger = logging.getLogger(__name__)


class YFinanceProvider(UnifiedDataProvider):
    """Enhanced yfinance provider with caching, rate limiting, and comprehensive market data."""
    
    # Rate limiting: 5 calls per second (yfinance's public API limit)
    CALLS = 5
    PERIOD = 1
    CACHE_TTL = 300  # 5 minutes cache TTL

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        cfg = config or {}
        super().__init__(provider_name='yfinance', provider_type='market_data', config=cfg)
        self._last_request_time = 0
        self._request_lock = None
        
    @property
    def _cache_key(self) -> str:
        """Generate a cache key based on the current minute to implement TTL."""
        return str(int(datetime.utcnow().timestamp() / self.CACHE_TTL))
    
    @lru_cache(maxsize=128)
    def _get_cached_ticker(self, symbol: str, cache_key: str):
        """Get a cached ticker or create a new one with TTL-based caching."""
        yf = _get_yf()
        return yf.Ticker(symbol)
    
    @sleep_and_retry
    @limits(calls=CALLS, period=PERIOD)
    def _rate_limited_fetch(self, symbol: str, method: str, **kwargs) -> Any:
        """Make rate-limited API calls to yfinance."""
        ticker = self._get_cached_ticker(symbol, self._cache_key)
        if method == 'history':
            return ticker.history(**kwargs)
        elif method == 'info':
            return ticker.info
        elif method == 'actions':
            return ticker.actions
        elif method == 'dividends':
            return ticker.dividends
        elif method == 'splits':
            return ticker.splits
        elif method == 'financials':
            return ticker.financials
        elif method == 'quarterly_financials':
            return ticker.quarterly_financials
        elif method == 'balance_sheet':
            return ticker.balance_sheet
        elif method == 'quarterly_balance_sheet':
            return ticker.quarterly_balance_sheet
        elif method == 'cashflow':
            return ticker.cashflow
        elif method == 'quarterly_cashflow':
            return ticker.quarterly_cashflow
        elif method == 'recommendations':
            return ticker.recommendations
        elif method == 'calendar':
            return ticker.calendar
        else:
            raise ValueError(f"Unknown method: {method}")

    async def get_current_price(self, symbol: str) -> Dict[str, Any]:
        """Get current price and basic market data for a symbol."""
        try:
            # Get yfinance exceptions
            yf_exceptions = _get_yf_exceptions()
            YFNoDataError = getattr(yf_exceptions, 'YFNoDataError', Exception)
            
            # Get historical data for today
            hist = self._rate_limited_fetch(symbol, 'history', period="1d")
            
            if hist.empty:
                raise YFNoDataError(f"No data available for {symbol}")
            
            # Get additional info
            info = self._rate_limited_fetch(symbol, 'info')
            
            # Calculate metrics
            current_price = hist['Close'].iloc[-1]
            open_price = hist['Open'].iloc[-1]
            change = float(current_price - open_price)
            change_percent = (change / open_price * 100) if open_price != 0 else 0
            
            return {
                'provider': 'yfinance',
                'success': True,
                'symbol': symbol,
                'price': float(current_price),
                'open': float(open_price),
                'high': float(hist['High'].iloc[-1]),
                'low': float(hist['Low'].iloc[-1]),
                'close': float(hist['Close'].iloc[-1]),
                'volume': int(hist['Volume'].iloc[-1]),
                'change': change,
                'change_percent': change_percent,
                'timestamp': hist.index[-1].isoformat(),
                'market_cap': info.get('marketCap'),
                'pe_ratio': info.get('trailingPE'),
                'dividend_yield': info.get('dividendYield'),
                'fifty_two_week': {
                    'high': info.get('fiftyTwoWeekHigh'),
                    'low': info.get('fiftyTwoWeekLow')
                },
                'source': 'yfinance',
                'cached': False
            }
            
        except YFNoDataError as e:
            logger.warning(f"No data available for {symbol}: {str(e)}")
            return {
                'provider': 'yfinance',
                'success': False,
                'symbol': symbol,
                'price': None,
                'error': str(e)
            }
        except Exception as e:
            logger.error(f"Error fetching {symbol}: {str(e)}", exc_info=True)
            return {
                'provider': 'yfinance',
                'success': False,
                'symbol': symbol,
                'price': None,
                'error': 'Failed to fetch data. Please try again later.'
            }
    
    async def get_ticker(self, symbol: str) -> Dict[str, Any]:
        """Get comprehensive ticker data."""
        return await self.get_current_price(symbol)
    
    async def get_historical_data(self, symbol: str, period: str = "1y", interval: str = "1d") -> Dict[str, Any]:
        """Get historical price data for a symbol."""
        try:
            hist = self._rate_limited_fetch(
                symbol, 
                'history',
                period=period,
                interval=interval,
                prepost=False,
                auto_adjust=True
            )
            
            if hist.empty:
                raise YFNoDataError(f"No historical data available for {symbol}")
            
            # Convert to our HistoricalData format
            historical_data = HistoricalData(
                symbol=symbol,
                dates=hist.index.strftime('%Y-%m-%d').tolist(),
                opens=hist['Open'].fillna(0).astype(float).round(2).tolist(),
                closes=hist['Close'].fillna(0).astype(float).round(2).tolist(),
                highs=hist['High'].fillna(0).astype(float).round(2).tolist(),
                lows=hist['Low'].fillna(0).astype(float).round(2).tolist(),
                volumes=hist['Volume'].fillna(0).astype(int).tolist(),
                metadata={
                    'period': period,
                    'interval': interval,
                    'data_points': len(hist)
                }
            )
            
            return {
                'provider': 'yfinance',
                'success': True,
                'symbol': symbol,
                'data': historical_data,
                'period': period,
                'interval': interval,
                'data_points': len(hist)
            }
            
        except YFNoDataError as e:
            logger.warning(f"No historical data for {symbol}: {str(e)}")
            return {
                'provider': 'yfinance',
                'success': False,
                'symbol': symbol,
                'error': str(e)
            }
        except Exception as e:
            logger.error(f"Error fetching historical data for {symbol}: {str(e)}", exc_info=True)
            return {
                'provider': 'yfinance',
                'success': False,
                'symbol': symbol,
                'error': 'Failed to fetch historical data. Please try again later.'
            }
    
    async def get_fundamentals(self, symbol: str) -> Dict[str, Any]:
        """Get fundamental data for a symbol."""
        try:
            info = self._rate_limited_fetch(symbol, 'info')
            financials = self._rate_limited_fetch(symbol, 'financials')
            balance_sheet = self._rate_limited_fetch(symbol, 'balance_sheet')
            cashflow = self._rate_limited_fetch(symbol, 'cashflow')
            
            return {
                'provider': 'yfinance',
                'success': True,
                'symbol': symbol,
                'info': info,
                'financials': financials.to_dict() if financials is not None else {},
                'balance_sheet': balance_sheet.to_dict() if balance_sheet is not None else {},
                'cashflow': cashflow.to_dict() if cashflow is not None else {}
            }
            
        except Exception as e:
            logger.error(f"Error fetching fundamentals for {symbol}: {str(e)}", exc_info=True)
            return {
                'provider': 'yfinance',
                'success': False,
                'symbol': symbol,
                'error': 'Failed to fetch fundamental data.'
            }