# Data Fetcher Migration Guide

This guide explains how to migrate from the old `data_fetcher.py` to the new enhanced data fetching architecture.

## Overview

The old `data_fetcher.py` from `src(old)/shared/ai_chat/data_fetcher.py` has been replaced with a more modular and integrated approach:

- **Old**: Single `DataFetcher` class with all functionality
- **New**: Modular architecture with `DataProviderAggregator` and `EnhancedDataFetcher`

## Key Changes

### 1. Architecture Changes

**Old Architecture:**
```python
from src.shared.ai_chat.data_fetcher import DataFetcher

fetcher = DataFetcher()
data, tool_results = await fetcher.fetch(validated, query)
```

**New Architecture:**
```python
from src.shared.data_providers import enhanced_data_fetcher

results = await enhanced_data_fetcher.fetch_comprehensive_data(symbols, data_types)
```

### 2. Data Structure Changes

**Old Result:**
```python
# Tuple of (data, tool_results)
data = {
    'AAPL': {
        'price': 150.0,
        'historical': {...},
        'indicators': {...}
    }
}
tool_results = {
    'AAPL_technical': {...},
    'AAPL_signals': {...}
}
```

**New Result:**
```python
# Dictionary of EnhancedDataResult objects
results = {
    'AAPL': EnhancedDataResult(
        symbol='AAPL',
        data={...},
        quality_metrics=DataQualityMetrics(...),
        fetch_timestamp=datetime.utcnow(),
        processing_time=1.5,
        success=True
    )
}
```

## Migration Steps

### Step 1: Update Imports

**Before:**
```python
from src.shared.ai_chat.data_fetcher import DataFetcher
from src.shared.ai_chat.models import AIAskResult
```

**After:**
```python
from src.shared.data_providers import enhanced_data_fetcher, EnhancedDataResult
```

### Step 2: Update Data Fetching Logic

**Before:**
```python
async def fetch_data(validated: AIAskResult, query: str = ""):
    fetcher = DataFetcher()
    data, tool_results = await fetcher.fetch(validated, query)
    return data, tool_results
```

**After:**
```python
async def fetch_data(symbols: List[str], ai_intent: str, query: str = ""):
    results = await enhanced_data_fetcher.fetch_with_ai_guidance(
        symbols=symbols,
        ai_intent=ai_intent,
        query_context=query
    )
    return results
```

### Step 3: Handle New Data Structure

**Before:**
```python
# Access data directly
for symbol, symbol_data in data.items():
    price = symbol_data.get('price', 0)
    historical = symbol_data.get('historical', {})
```

**After:**
```python
# Access data through EnhancedDataResult
for symbol, result in results.items():
    if result.success:
        price = result.data.get('price', {}).get('price', 0)
        historical = result.data.get('historical', {})
        quality = result.quality_metrics.overall_quality
    else:
        logger.error(f"Failed to fetch data for {symbol}: {result.error_message}")
```

### Step 4: Use Quality Metrics

**New Feature - Data Quality Assessment:**
```python
for symbol, result in results.items():
    if result.success:
        quality = result.quality_metrics
        
        # Check data quality
        if quality.overall_quality < 0.7:
            logger.warning(f"Low quality data for {symbol}: {quality.overall_quality}")
        
        # Check for warnings
        if quality.validation_warnings:
            logger.warning(f"Data warnings for {symbol}: {quality.validation_warnings}")
        
        # Check data completeness
        if quality.completeness_score < 0.8:
            logger.warning(f"Incomplete data for {symbol}: {quality.completeness_score}")
```

## New Features

### 1. Data Quality Assessment

The new system provides comprehensive data quality metrics:

```python
quality = result.quality_metrics
print(f"Data Points: {quality.data_points}")
print(f"Completeness: {quality.completeness_score}")
print(f"Freshness: {quality.freshness_score}")
print(f"Consistency: {quality.consistency_score}")
print(f"Overall Quality: {quality.overall_quality}")
print(f"Warnings: {quality.validation_warnings}")
```

### 2. Enhanced Caching

The new system integrates with the current caching architecture:

```python
# Force refresh (bypass cache)
results = await enhanced_data_fetcher.fetch_comprehensive_data(
    symbols, 
    force_refresh=True
)

# Use cache (default)
results = await enhanced_data_fetcher.fetch_comprehensive_data(symbols)
```

### 3. AI-Guided Data Fetching

The new system supports AI-guided data selection:

```python
# AI determines what data to fetch based on intent
results = await enhanced_data_fetcher.fetch_with_ai_guidance(
    symbols=['AAPL', 'MSFT'],
    ai_intent='stock_analysis',  # or 'price_check', etc.
    query_context='full analysis of Apple stock'
)
```

### 4. Quality Summary

Get a summary of data quality across all symbols:

```python
results = await enhanced_data_fetcher.fetch_comprehensive_data(symbols)
summary = enhanced_data_fetcher.get_quality_summary(results)

print(f"Success Rate: {summary['success_rate']}")
print(f"Average Quality: {summary['average_quality_score']}")
print(f"Common Warnings: {summary['common_warnings']}")
```

## Backward Compatibility

For gradual migration, you can create a compatibility wrapper:

```python
class LegacyDataFetcherWrapper:
    """Wrapper to maintain compatibility with old DataFetcher interface"""
    
    def __init__(self):
        self.enhanced_fetcher = enhanced_data_fetcher
    
    async def fetch(self, validated, query: str = ""):
        """Maintain old interface"""
        symbols = validated.symbols
        intent = validated.intent
        
        results = await self.enhanced_fetcher.fetch_with_ai_guidance(
            symbols=symbols,
            ai_intent=intent,
            query_context=query
        )
        
        # Convert to old format
        data = {}
        tool_results = {}
        
        for symbol, result in results.items():
            if result.success:
                data[symbol] = result.data
                # Add quality metrics as tool results
                tool_results[f"{symbol}_quality"] = {
                    'success': True,
                    'data': result.quality_metrics.__dict__
                }
            else:
                data[symbol] = {'error': result.error_message}
        
        return data, tool_results
```

## Performance Improvements

The new architecture provides several performance improvements:

1. **Better Caching**: Integrated with Redis-based caching system
2. **Concurrent Fetching**: Multiple symbols fetched concurrently
3. **Quality Assessment**: Only fetch additional data when needed
4. **Provider Health**: Automatic provider health monitoring
5. **Rate Limiting**: Built-in rate limiting to prevent API limits

## Error Handling

The new system provides better error handling:

```python
results = await enhanced_data_fetcher.fetch_comprehensive_data(symbols)

for symbol, result in results.items():
    if not result.success:
        logger.error(f"Failed to fetch {symbol}: {result.error_message}")
        # Handle error appropriately
    elif result.quality_metrics.overall_quality < 0.5:
        logger.warning(f"Low quality data for {symbol}")
        # Consider fallback or retry
```

## Testing

The new system is designed to be easily testable:

```python
import pytest
from src.shared.data_providers import enhanced_data_fetcher

@pytest.mark.asyncio
async def test_enhanced_data_fetching():
    results = await enhanced_data_fetcher.fetch_comprehensive_data(['AAPL'])
    
    assert 'AAPL' in results
    assert results['AAPL'].success
    assert results['AAPL'].quality_metrics.overall_quality > 0
```

## Conclusion

The new enhanced data fetching system provides:

- **Better Architecture**: Modular and maintainable
- **Enhanced Features**: Quality assessment, AI integration, better caching
- **Improved Performance**: Concurrent fetching, better error handling
- **Easier Testing**: Clear interfaces and data structures
- **Future-Proof**: Extensible design for new features

The migration should be straightforward, and the new system provides significant improvements over the old implementation.
