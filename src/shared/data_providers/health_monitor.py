"""
Data Provider Health Monitoring System

Continuously monitors the health and performance of all data providers,
providing real-time status updates, alerts, and performance metrics.
"""

import asyncio
import logging
import time
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import json

from .enhanced_error_handler import enhanced_error_handler, ProviderState

logger = logging.getLogger(__name__)


class HealthStatus(Enum):
    """Overall health status levels"""
    EXCELLENT = "excellent"  # All providers working perfectly
    GOOD = "good"           # Most providers working
    DEGRADED = "degraded"   # Some providers failing
    CRITICAL = "critical"   # Most/all providers failing


@dataclass
class HealthCheckResult:
    """Result of a health check operation"""
    provider_name: str
    success: bool
    response_time: float
    error: Optional[str] = None
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            'provider_name': self.provider_name,
            'success': self.success,
            'response_time': self.response_time,
            'error': self.error,
            'timestamp': self.timestamp.isoformat()
        }


@dataclass
class ProviderHealthMetrics:
    """Comprehensive health metrics for a provider"""
    provider_name: str
    is_available: bool = True
    last_check_time: Optional[datetime] = None
    avg_response_time: float = 0.0
    success_rate_24h: float = 100.0
    total_checks: int = 0
    successful_checks: int = 0
    recent_errors: List[str] = field(default_factory=list)
    uptime_percentage: float = 100.0
    
    def update_from_check(self, result: HealthCheckResult):
        """Update metrics from a health check result"""
        self.last_check_time = result.timestamp
        self.total_checks += 1
        
        if result.success:
            self.successful_checks += 1
            # Update average response time (exponential moving average)
            if self.avg_response_time == 0:
                self.avg_response_time = result.response_time
            else:
                self.avg_response_time = (self.avg_response_time * 0.8) + (result.response_time * 0.2)
        else:
            # Track recent errors (keep last 10)
            self.recent_errors.append(result.error or "Unknown error")
            if len(self.recent_errors) > 10:
                self.recent_errors.pop(0)
        
        # Update success rate
        self.success_rate_24h = (self.successful_checks / self.total_checks) * 100
        
        # Update availability
        self.is_available = result.success
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            'provider_name': self.provider_name,
            'is_available': self.is_available,
            'last_check_time': self.last_check_time.isoformat() if self.last_check_time else None,
            'avg_response_time': round(self.avg_response_time, 3),
            'success_rate_24h': round(self.success_rate_24h, 2),
            'total_checks': self.total_checks,
            'successful_checks': self.successful_checks,
            'recent_errors': self.recent_errors[-5:],  # Last 5 errors
            'uptime_percentage': round(self.uptime_percentage, 2)
        }


class DataProviderHealthMonitor:
    """
    Comprehensive health monitoring system for data providers.
    
    Features:
    - Continuous health checks
    - Performance metrics tracking
    - Alert generation for failures
    - Historical health data
    - Integration with error handler
    """
    
    def __init__(self, check_interval: int = 300):  # 5 minutes default
        """Initialize the health monitor"""
        self.check_interval = check_interval
        self.provider_metrics: Dict[str, ProviderHealthMetrics] = {}
        self.is_monitoring = False
        self.monitor_task: Optional[asyncio.Task] = None
        
        # Health check functions for each provider
        self.health_check_functions: Dict[str, Callable] = {}
        
        # Alert thresholds
        self.alert_thresholds = {
            'success_rate_threshold': 80.0,  # Alert if success rate < 80%
            'response_time_threshold': 10.0,  # Alert if avg response time > 10s
            'consecutive_failures_threshold': 3  # Alert after 3 consecutive failures
        }
        
        logger.info("✅ Data provider health monitor initialized")
    
    def register_provider(self, provider_name: str, health_check_func: Callable):
        """Register a provider with its health check function"""
        self.provider_metrics[provider_name] = ProviderHealthMetrics(provider_name)
        self.health_check_functions[provider_name] = health_check_func
        logger.info(f"📋 Registered provider for monitoring: {provider_name}")
    
    async def start_monitoring(self):
        """Start continuous health monitoring"""
        if self.is_monitoring:
            logger.warning("Health monitoring is already running")
            return
        
        self.is_monitoring = True
        self.monitor_task = asyncio.create_task(self._monitoring_loop())
        logger.info(f"🔄 Started health monitoring (interval: {self.check_interval}s)")
    
    async def stop_monitoring(self):
        """Stop health monitoring"""
        if not self.is_monitoring:
            return
        
        self.is_monitoring = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        
        logger.info("⏹️ Stopped health monitoring")
    
    async def _monitoring_loop(self):
        """Main monitoring loop"""
        while self.is_monitoring:
            try:
                await self._perform_health_checks()
                await asyncio.sleep(self.check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ Error in monitoring loop: {e}")
                await asyncio.sleep(60)  # Wait 1 minute before retrying
    
    async def _perform_health_checks(self):
        """Perform health checks on all registered providers"""
        logger.debug("🔍 Performing health checks on all providers")
        
        # Run health checks concurrently
        tasks = []
        for provider_name, health_check_func in self.health_check_functions.items():
            task = asyncio.create_task(self._check_provider_health(provider_name, health_check_func))
            tasks.append(task)
        
        if tasks:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Process results and generate alerts
            for result in results:
                if isinstance(result, HealthCheckResult):
                    await self._process_health_check_result(result)
                elif isinstance(result, Exception):
                    logger.error(f"❌ Health check failed with exception: {result}")
    
    async def _check_provider_health(self, provider_name: str, health_check_func: Callable) -> HealthCheckResult:
        """Perform health check on a single provider"""
        start_time = time.time()
        
        try:
            # Execute health check with timeout
            result = await asyncio.wait_for(health_check_func(), timeout=30.0)
            response_time = time.time() - start_time
            
            # Consider it successful if we got any result
            success = result is not None
            error = None if success else "No data returned"
            
            return HealthCheckResult(
                provider_name=provider_name,
                success=success,
                response_time=response_time,
                error=error
            )
            
        except asyncio.TimeoutError:
            response_time = time.time() - start_time
            return HealthCheckResult(
                provider_name=provider_name,
                success=False,
                response_time=response_time,
                error="Health check timeout"
            )
        except Exception as e:
            response_time = time.time() - start_time
            return HealthCheckResult(
                provider_name=provider_name,
                success=False,
                response_time=response_time,
                error=str(e)
            )
    
    async def _process_health_check_result(self, result: HealthCheckResult):
        """Process health check result and update metrics"""
        provider_name = result.provider_name
        
        if provider_name not in self.provider_metrics:
            self.provider_metrics[provider_name] = ProviderHealthMetrics(provider_name)
        
        # Update metrics
        metrics = self.provider_metrics[provider_name]
        metrics.update_from_check(result)
        
        # Log result
        if result.success:
            logger.debug(f"✅ {provider_name} health check passed ({result.response_time:.2f}s)")
        else:
            logger.warning(f"❌ {provider_name} health check failed: {result.error}")
        
        # Check for alerts
        await self._check_alerts(provider_name, metrics)
    
    async def _check_alerts(self, provider_name: str, metrics: ProviderHealthMetrics):
        """Check if any alert conditions are met"""
        alerts = []
        
        # Success rate alert
        if metrics.success_rate_24h < self.alert_thresholds['success_rate_threshold']:
            alerts.append(f"Low success rate: {metrics.success_rate_24h:.1f}%")
        
        # Response time alert
        if metrics.avg_response_time > self.alert_thresholds['response_time_threshold']:
            alerts.append(f"High response time: {metrics.avg_response_time:.2f}s")
        
        # Provider unavailable
        if not metrics.is_available:
            alerts.append("Provider currently unavailable")
        
        # Generate alerts
        for alert in alerts:
            await self._generate_alert(provider_name, alert, metrics)
    
    async def _generate_alert(self, provider_name: str, alert_message: str, metrics: ProviderHealthMetrics):
        """Generate an alert for a provider issue"""
        alert = {
            'timestamp': datetime.now().isoformat(),
            'provider': provider_name,
            'severity': 'warning',
            'message': alert_message,
            'metrics': metrics.to_dict()
        }
        
        logger.warning(f"🚨 ALERT [{provider_name}]: {alert_message}")
        
        # Here you could integrate with external alerting systems
        # e.g., send to Discord, email, Slack, etc.
    
    def get_overall_health_status(self) -> HealthStatus:
        """Get overall health status of all providers"""
        if not self.provider_metrics:
            return HealthStatus.CRITICAL
        
        available_count = sum(1 for m in self.provider_metrics.values() if m.is_available)
        total_count = len(self.provider_metrics)
        availability_percentage = (available_count / total_count) * 100
        
        if availability_percentage >= 90:
            return HealthStatus.EXCELLENT
        elif availability_percentage >= 70:
            return HealthStatus.GOOD
        elif availability_percentage >= 40:
            return HealthStatus.DEGRADED
        else:
            return HealthStatus.CRITICAL
    
    def get_health_report(self) -> Dict[str, Any]:
        """Get comprehensive health report"""
        overall_status = self.get_overall_health_status()
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'overall_status': overall_status.value,
            'monitoring_active': self.is_monitoring,
            'check_interval': self.check_interval,
            'providers': {},
            'summary': {
                'total_providers': len(self.provider_metrics),
                'available_providers': 0,
                'unavailable_providers': 0,
                'avg_response_time': 0.0,
                'overall_success_rate': 0.0
            }
        }
        
        total_response_time = 0
        total_success_rate = 0
        
        for provider_name, metrics in self.provider_metrics.items():
            report['providers'][provider_name] = metrics.to_dict()
            
            if metrics.is_available:
                report['summary']['available_providers'] += 1
            else:
                report['summary']['unavailable_providers'] += 1
            
            total_response_time += metrics.avg_response_time
            total_success_rate += metrics.success_rate_24h
        
        # Calculate averages
        if self.provider_metrics:
            report['summary']['avg_response_time'] = round(
                total_response_time / len(self.provider_metrics), 3
            )
            report['summary']['overall_success_rate'] = round(
                total_success_rate / len(self.provider_metrics), 2
            )
        
        return report
    
    async def manual_health_check(self, provider_name: Optional[str] = None) -> Dict[str, Any]:
        """Perform manual health check on specific provider or all providers"""
        if provider_name:
            if provider_name not in self.health_check_functions:
                return {'error': f'Provider {provider_name} not registered'}
            
            health_check_func = self.health_check_functions[provider_name]
            result = await self._check_provider_health(provider_name, health_check_func)
            await self._process_health_check_result(result)
            
            return {
                'provider': provider_name,
                'result': result.to_dict(),
                'metrics': self.provider_metrics[provider_name].to_dict()
            }
        else:
            # Check all providers
            await self._perform_health_checks()
            return self.get_health_report()


# Global instance
health_monitor = DataProviderHealthMonitor()
