from pydantic import BaseModel, Field, validator
from typing import List, Any, Dict

class AIAskResult(BaseModel):
    intent: str = Field(default="general_question")
    symbols: List[str] = Field(default_factory=list)
    needs_data: bool = False
    response: str = ""

    @classmethod
    def from_ai_response(cls, response_dict: Dict[str, Any]) -> 'AIAskResult':
        # Validate and normalize intent
        valid_intents = ["stock_analysis", "market_overview", "price_check", "general_question"]
        intent = response_dict.get("intent", "general_question")
        if intent not in valid_intents:
            intent = "general_question"
        
        # Validate and normalize symbols
        symbols = response_dict.get("symbols", [])
        if not isinstance(symbols, list):
            symbols = []
        # Ensure all symbols are strings and uppercase
        symbols = [str(s).upper() for s in symbols if s]
        
        # Validate needs_data
        needs_data = bool(response_dict.get("needs_data", False))
        
        # Validate response
        response = response_dict.get("response", "")
        if not isinstance(response, str):
            response = str(response)
        
        return cls(intent=intent, symbols=symbols, needs_data=needs_data, response=response)

    def dict(self) -> Dict[str, Any]:
        return self.model_dump()