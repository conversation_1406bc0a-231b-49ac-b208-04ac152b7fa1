"""
Hybrid MCP Data Provider that combines Alpha Vantage MCP with existing providers.
Provides intelligent fallback and best-of-both-worlds data access.
"""

import asyncio
import logging
from typing import Dict, Any, Optional, List, Union
from datetime import datetime, timedelta

from .alpha_vantage_mcp import AlphaVantageMCPClient
from .aggregator import DataProviderAggregator
from .unified_base import UnifiedDataProvider, HistoricalData

logger = logging.getLogger(__name__)

class HybridMCPProvider(UnifiedDataProvider):
    """
    Hybrid provider that uses Alpha Vantage MCP as primary source
    with existing providers as fallbacks.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize the hybrid provider."""
        cfg = config or {}
        super().__init__(provider_name='hybrid_mcp', provider_type='market_data', config=cfg)
        
        # Initialize MCP client
        from src.mcp_server.trading_mcp_client import TradingMCPClient
        self.mcp_client = TradingMCPClient()
        
        # Initialize fallback providers
        self.fallback_aggregator = DataProviderAggregator()
        
        # Configuration
        self.use_mcp_for_ai = cfg.get('use_mcp_for_ai', True)
        self.use_mcp_for_technical = cfg.get('use_mcp_for_technical', True)
        self.use_mcp_for_realtime = cfg.get('use_mcp_for_realtime', True)
        self.fallback_on_mcp_failure = cfg.get('fallback_on_mcp_failure', True)
        
        # MCP availability tracking
        self.mcp_available = self.mcp_client.is_configured
        self.mcp_failure_count = 0
        self.max_mcp_failures = 3
        
        logger.info(f"Hybrid MCP Provider initialized - MCP available: {self.mcp_available}")
    
    async def _should_use_mcp(self, operation_type: str = "general") -> bool:
        """Determine if MCP should be used for this operation."""
        if not self.mcp_available or self.mcp_failure_count >= self.max_mcp_failures:
            return False
        
        # Use MCP for AI operations and technical analysis
        if operation_type in ["ai_query", "technical_analysis", "realtime"]:
            return True
        
        # Use MCP for real-time data if configured
        if operation_type == "realtime" and self.use_mcp_for_realtime:
            return True
        
        return False
    
    async def _handle_mcp_failure(self, error: Exception):
        """Handle MCP failure and update availability."""
        self.mcp_failure_count += 1
        logger.warning(f"MCP failure #{self.mcp_failure_count}: {error}")
        
        if self.mcp_failure_count >= self.max_mcp_failures:
            self.mcp_available = False
            logger.warning("MCP marked as unavailable due to repeated failures")
    
    async def get_current_price(self, symbol: str) -> Dict[str, Any]:
        """Get current price with MCP primary, fallback to existing providers."""
        if await self._should_use_mcp("realtime"):
            try:
                quote_data = await self.mcp_client.get_stock_price(symbol)
                
                # Parse MCP response format
                if "Global Quote" in quote_data:
                    quote = quote_data["Global Quote"]
                    return {
                        "symbol": symbol,
                        "price": float(quote.get("05. price", 0)),
                        "change": float(quote.get("09. change", 0)),
                        "change_percent": float(quote.get("10. change percent", 0).replace("%", "")),
                        "volume": int(quote.get("06. volume", 0)),
                        "high": float(quote.get("03. high", 0)),
                        "low": float(quote.get("04. low", 0)),
                        "open": float(quote.get("02. open", 0)),
                        "previous_close": float(quote.get("08. previous close", 0)),
                        "timestamp": datetime.now().isoformat(),
                        "source": "alpha_vantage_mcp"
                    }
                else:
                    raise ValueError("Invalid MCP response format")
            
            except Exception as e:
                await self._handle_mcp_failure(e)
                if self.fallback_on_mcp_failure:
                    logger.info(f"Falling back to existing providers for {symbol}")
                    return await self.fallback_aggregator.get_ticker(symbol)
                else:
                    raise
        
        # Use fallback providers
        return await self.fallback_aggregator.get_ticker(symbol)
    
    async def get_historical_data(self, symbol: str, period: str = "1mo") -> Dict[str, Any]:
        """Get historical data with MCP primary, fallback to existing providers."""
        if await self._should_use_mcp("historical"):
            try:
                # Map period to Alpha Vantage format
                outputsize = "full" if period in ["1y", "2y", "5y", "max"] else "compact"
                
                hist_data = await self.mcp_client.get_historical_data(symbol, period)
                
                if "Time Series (Daily)" in hist_data:
                    time_series = hist_data["Time Series (Daily)"]
                    
                    # Convert to HistoricalData format
                    dates = []
                    opens = []
                    highs = []
                    lows = []
                    closes = []
                    volumes = []
                    
                    for date_str, data in time_series.items():
                        dates.append(date_str)
                        opens.append(float(data["1. open"]))
                        highs.append(float(data["2. high"]))
                        lows.append(float(data["3. low"]))
                        closes.append(float(data["4. close"]))
                        volumes.append(int(data["5. volume"]))
                    
                    # Sort by date (oldest first)
                    sorted_data = sorted(zip(dates, opens, highs, lows, closes, volumes))
                    dates, opens, highs, lows, closes, volumes = zip(*sorted_data)
                    
                    historical_data = HistoricalData(
                        symbol=symbol,
                        dates=list(dates),
                        opens=list(opens),
                        highs=list(highs),
                        lows=list(lows),
                        closes=list(closes),
                        volumes=list(volumes),
                        metadata={"source": "alpha_vantage_mcp"}
                    )
                    
                    return {
                        "success": True,
                        "data": historical_data,
                        "source": "alpha_vantage_mcp"
                    }
                else:
                    raise ValueError("Invalid MCP historical data format")
            
            except Exception as e:
                await self._handle_mcp_failure(e)
                if self.fallback_on_mcp_failure:
                    logger.info(f"Falling back to existing providers for historical data: {symbol}")
                    return await self.fallback_aggregator.get_history(symbol, period)
                else:
                    raise
        
        # Use fallback providers
        return await self.fallback_aggregator.get_history(symbol, period)
    
    async def get_technical_indicators(self, symbol: str, indicators: List[str] = None) -> Dict[str, Any]:
        """Get technical indicators using MCP."""
        if not await self._should_use_mcp("technical_analysis"):
            # Fallback to existing technical analysis
            hist_data = await self.get_historical_data(symbol)
            if hist_data.get("success") and "data" in hist_data:
                from ..technical_analysis.enhanced_calculator import EnhancedTechnicalCalculator
                calculator = EnhancedTechnicalCalculator()
                return await calculator.calculate_indicators_from_historical_data(hist_data["data"])
            else:
                return {"error": "Failed to get historical data for technical analysis"}
        
        try:
            if indicators is None:
                indicators = ["RSI", "MACD", "BBANDS", "SMA", "EMA", "STOCH"]
            
            results = await self.mcp_client.get_technical_analysis(symbol, indicators)
            
            # Parse and format results
            formatted_results = {}
            for indicator, data in results.items():
                if "error" not in data:
                    formatted_results[indicator] = self._format_technical_indicator(indicator, data)
                else:
                    formatted_results[indicator] = {"error": data["error"]}
            
            return {
                "success": True,
                "indicators": formatted_results,
                "symbol": symbol,
                "source": "alpha_vantage_mcp"
            }
        
        except Exception as e:
            await self._handle_mcp_failure(e)
            logger.warning(f"Technical analysis failed for {symbol}: {e}")
            return {"error": str(e)}
    
    def _format_technical_indicator(self, indicator: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Format technical indicator data for consistency."""
        if indicator == "RSI":
            return {
                "values": data.get("Technical Analysis: RSI", {}),
                "latest": self._get_latest_value(data.get("Technical Analysis: RSI", {}))
            }
        elif indicator == "MACD":
            return {
                "macd": data.get("Technical Analysis: MACD", {}),
                "signal": data.get("Technical Analysis: MACD", {}),
                "histogram": data.get("Technical Analysis: MACD", {}),
                "latest": self._get_latest_value(data.get("Technical Analysis: MACD", {}))
            }
        elif indicator == "BBANDS":
            return {
                "upper": data.get("Technical Analysis: BBANDS", {}),
                "middle": data.get("Technical Analysis: BBANDS", {}),
                "lower": data.get("Technical Analysis: BBANDS", {}),
                "latest": self._get_latest_value(data.get("Technical Analysis: BBANDS", {}))
            }
        else:
            return {
                "values": data.get(f"Technical Analysis: {indicator}", {}),
                "latest": self._get_latest_value(data.get(f"Technical Analysis: {indicator}", {}))
            }
    
    def _get_latest_value(self, values_dict: Dict[str, Any]) -> Any:
        """Get the most recent value from a time series."""
        if not values_dict:
            return None
        
        # Get the most recent date
        latest_date = max(values_dict.keys())
        return values_dict[latest_date]
    
    async def get_ai_optimized_data(self, symbol: str, query_type: str = "comprehensive") -> Dict[str, Any]:
        """Get data optimized for AI consumption."""
        if not await self._should_use_mcp("ai_query"):
            # Fallback to basic data
            price_data = await self.get_current_price(symbol)
            hist_data = await self.get_historical_data(symbol)
            return {
                "price": price_data,
                "historical": hist_data,
                "source": "fallback_providers"
            }
        
        try:
            if query_type == "comprehensive":
                return await self.mcp_client.get_comprehensive_analysis(symbol)
            elif query_type == "technical":
                return await self.mcp_client.get_technical_analysis(symbol)
            elif query_type == "fundamental":
                return await self.mcp_client.get_company_overview(symbol)
            else:
                return await self.mcp_client.get_comprehensive_analysis(symbol)
        
        except Exception as e:
            await self._handle_mcp_failure(e)
            logger.warning(f"AI optimized data failed for {symbol}: {e}")
            
            # Fallback to basic data
            price_data = await self.get_current_price(symbol)
            hist_data = await self.get_historical_data(symbol)
            return {
                "price": price_data,
                "historical": hist_data,
                "source": "fallback_providers",
                "mcp_error": str(e)
            }
    
    async def get_news_sentiment(self, symbol: str) -> Dict[str, Any]:
        """Get news sentiment analysis."""
        if await self._should_use_mcp("ai_query"):
            try:
                return await self.mcp_client.analyze_market_sentiment()
            except Exception as e:
                await self._handle_mcp_failure(e)
                logger.warning(f"News sentiment failed for {symbol}: {e}")
        
        # Fallback: return empty sentiment
        return {
            "sentiment": "neutral",
            "confidence": 0.5,
            "source": "fallback",
            "message": "MCP unavailable, using fallback"
        }
    
    async def close(self):
        """Close all connections."""
        if self.mcp_client:
            await self.mcp_client.close()
        
        if hasattr(self.fallback_aggregator, 'close'):
            await self.fallback_aggregator.close()
    
    def __del__(self):
        """Cleanup on deletion."""
        if hasattr(self, 'mcp_client') and self.mcp_client:
            try:
                loop = asyncio.get_running_loop()
                loop.create_task(self.mcp_client.close())
            except RuntimeError:
                # No running event loop, cleanup will be handled by garbage collector
                pass
