"""
Fallback Data Provider

This provider returns mock data when all other providers fail,
ensuring the system can still provide responses for testing.
"""

import logging
import random
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import json

logger = logging.getLogger(__name__)


class FallbackProvider:
    """Fallback data provider that returns mock data for testing."""
    
    def __init__(self):
        self.name = "fallback"
        self.is_configured = True
        logger.info("Fallback provider initialized")
    
    def get_current_price(self, symbol: str) -> Dict[str, Any]:
        """Get mock current price data - only for development/test environments."""
        import os

        environment = os.getenv('ENVIRONMENT', 'production').lower()
        if environment in ['production', 'prod']:
            logger.error(f"Fallback provider should not be used in production for {symbol}")
            return {
                "error": "Fallback provider disabled in production",
                "symbol": symbol,
                "provider": "fallback"
            }

        logger.warning(f"Using fallback mock data for {symbol} in {environment} environment")

        # Generate realistic mock data
        base_price = random.uniform(10, 500)
        change = random.uniform(-0.05, 0.05)  # ±5% change
        price_change = base_price * change

        return {
            "symbol": symbol,
            "price": round(base_price, 2),
            "change": round(price_change, 2),
            "change_percent": round(change * 100, 2),
            "volume": random.randint(1000000, 10000000),
            "high": round(base_price * 1.02, 2),
            "low": round(base_price * 0.98, 2),
            "open": round(base_price * (1 + random.uniform(-0.01, 0.01)), 2),
            "previous_close": round(base_price - price_change, 2),
            "timestamp": datetime.now().isoformat(),
            "provider": "fallback",
            "mock_data": True  # Flag to indicate this is mock data
        }
    
    def get_historical_data(self, symbol: str, timeframe: str = "1d", limit: int = 30) -> Dict[str, Any]:
        """Get mock historical data - only for development/test environments."""
        import os

        environment = os.getenv('ENVIRONMENT', 'production').lower()
        if environment in ['production', 'prod']:
            logger.error(f"Fallback provider should not be used in production for historical data: {symbol}")
            return {
                "error": "Fallback provider disabled in production",
                "symbol": symbol,
                "provider": "fallback"
            }

        logger.warning(f"Using fallback mock historical data for {symbol} in {environment} environment")

        base_price = random.uniform(10, 500)
        data = []

        for i in range(limit):
            date = datetime.now() - timedelta(days=limit-i)
            price = base_price * (1 + random.uniform(-0.1, 0.1))
            volume = random.randint(1000000, 10000000)

            data.append({
                "date": date.strftime("%Y-%m-%d"),
                "open": round(price * (1 + random.uniform(-0.01, 0.01)), 2),
                "high": round(price * (1 + random.uniform(0, 0.03)), 2),
                "low": round(price * (1 - random.uniform(0, 0.03)), 2),
                "close": round(price, 2),
                "volume": volume
            })

        return {
            "symbol": symbol,
            "timeframe": timeframe,
            "data": data,
            "provider": "fallback",
            "mock_data": True  # Flag to indicate this is mock data
        }
    
    def get_company_info(self, symbol: str) -> Dict[str, Any]:
        """Get mock company information."""
        return {
            "symbol": symbol,
            "name": f"{symbol} Corporation",
            "sector": random.choice(["Technology", "Healthcare", "Finance", "Consumer", "Energy"]),
            "industry": random.choice(["Software", "Biotech", "Banking", "Retail", "Oil & Gas"]),
            "market_cap": random.randint(**********, **********000),
            "employees": random.randint(1000, 100000),
            "website": f"https://www.{symbol.lower()}.com",
            "description": f"{symbol} is a leading company in its industry.",
            "provider": "fallback"
        }
    
    def get_technical_indicators(self, symbol: str) -> Dict[str, Any]:
        """Get mock technical indicators."""
        return {
            "symbol": symbol,
            "rsi": round(random.uniform(20, 80), 2),
            "macd": round(random.uniform(-2, 2), 4),
            "macd_signal": round(random.uniform(-2, 2), 4),
            "macd_histogram": round(random.uniform(-1, 1), 4),
            "sma_20": round(random.uniform(10, 500), 2),
            "sma_50": round(random.uniform(10, 500), 2),
            "ema_12": round(random.uniform(10, 500), 2),
            "ema_26": round(random.uniform(10, 500), 2),
            "bollinger_upper": round(random.uniform(10, 500), 2),
            "bollinger_lower": round(random.uniform(10, 500), 2),
            "bollinger_middle": round(random.uniform(10, 500), 2),
            "provider": "fallback"
        }
    
    def is_available(self) -> bool:
        """Check if the provider is available."""
        return True