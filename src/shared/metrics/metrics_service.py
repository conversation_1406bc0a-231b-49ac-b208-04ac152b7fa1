"""
Metrics Service
Provides centralized metrics collection and monitoring capabilities.
"""

import time
import asyncio
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field
from collections import defaultdict, deque
from src.shared.error_handling.logging import get_logger
from src.shared.config.config_manager import config

logger = get_logger(__name__)

@dataclass
class MetricPoint:
    """A single metric data point."""
    name: str
    value: float
    timestamp: float
    tags: Dict[str, str] = field(default_factory=dict)

@dataclass
class CounterMetric:
    """Counter metric that only increases."""
    name: str
    value: int = 0
    tags: Dict[str, str] = field(default_factory=dict)

@dataclass
class GaugeMetric:
    """Gauge metric that can increase or decrease."""
    name: str
    value: float = 0.0
    tags: Dict[str, str] = field(default_factory=dict)

@dataclass
class HistogramMetric:
    """Histogram metric for tracking value distributions."""
    name: str
    values: deque = field(default_factory=lambda: deque(maxlen=1000))
    tags: Dict[str, str] = field(default_factory=dict)

class MetricsService:
    """Centralized metrics collection service."""
    
    def __init__(self):
        self.counters: Dict[str, CounterMetric] = {}
        self.gauges: Dict[str, GaugeMetric] = {}
        self.histograms: Dict[str, HistogramMetric] = {}
        self.timers: Dict[str, List[float]] = defaultdict(list)
        self._lock = asyncio.Lock()
        self._start_time = time.time()
    
    async def increment_counter(self, name: str, value: int = 1, tags: Optional[Dict[str, str]] = None):
        """Increment a counter metric."""
        async with self._lock:
            key = self._get_metric_key(name, tags)
            if key not in self.counters:
                self.counters[key] = CounterMetric(name, tags=tags or {})
            self.counters[key].value += value
    
    async def set_gauge(self, name: str, value: float, tags: Optional[Dict[str, str]] = None):
        """Set a gauge metric value."""
        async with self._lock:
            key = self._get_metric_key(name, tags)
            if key not in self.gauges:
                self.gauges[key] = GaugeMetric(name, tags=tags or {})
            self.gauges[key].value = value
    
    async def add_to_histogram(self, name: str, value: float, tags: Optional[Dict[str, str]] = None):
        """Add a value to a histogram metric."""
        async with self._lock:
            key = self._get_metric_key(name, tags)
            if key not in self.histograms:
                self.histograms[key] = HistogramMetric(name, tags=tags or {})
            self.histograms[key].values.append(value)
    
    async def record_timing(self, name: str, duration: float, tags: Optional[Dict[str, str]] = None):
        """Record a timing metric."""
        await self.add_to_histogram(f"{name}_duration", duration, tags)
        await self.increment_counter(f"{name}_count", tags=tags)
    
    async def time_function(self, name: str, tags: Optional[Dict[str, str]] = None):
        """Context manager for timing function execution."""
        return TimingContext(self, name, tags)
    
    def _get_metric_key(self, name: str, tags: Optional[Dict[str, str]]) -> str:
        """Generate a unique key for a metric with tags."""
        if not tags:
            return name
        tag_str = "_".join(f"{k}={v}" for k, v in sorted(tags.items()))
        return f"{name}_{tag_str}"
    
    async def get_metrics_summary(self) -> Dict[str, Any]:
        """Get a summary of all metrics."""
        async with self._lock:
            uptime = time.time() - self._start_time
            
            # Process counters
            counter_summary = {}
            for key, counter in self.counters.items():
                counter_summary[key] = {
                    'type': 'counter',
                    'value': counter.value,
                    'tags': counter.tags
                }
            
            # Process gauges
            gauge_summary = {}
            for key, gauge in self.gauges.items():
                gauge_summary[key] = {
                    'type': 'gauge',
                    'value': gauge.value,
                    'tags': gauge.tags
                }
            
            # Process histograms
            histogram_summary = {}
            for key, histogram in self.histograms.items():
                values = list(histogram.values)
                if values:
                    histogram_summary[key] = {
                        'type': 'histogram',
                        'count': len(values),
                        'min': min(values),
                        'max': max(values),
                        'avg': sum(values) / len(values),
                        'p50': self._percentile(values, 50),
                        'p95': self._percentile(values, 95),
                        'p99': self._percentile(values, 99),
                        'tags': histogram.tags
                    }
                else:
                    histogram_summary[key] = {
                        'type': 'histogram',
                        'count': 0,
                        'tags': histogram.tags
                    }
            
            return {
                'uptime_seconds': uptime,
                'counters': counter_summary,
                'gauges': gauge_summary,
                'histograms': histogram_summary,
                'total_metrics': len(counter_summary) + len(gauge_summary) + len(histogram_summary)
            }
    
    def _percentile(self, values: List[float], percentile: int) -> float:
        """Calculate percentile of values."""
        if not values:
            return 0.0
        sorted_values = sorted(values)
        index = int((percentile / 100) * len(sorted_values))
        return sorted_values[min(index, len(sorted_values) - 1)]
    
    async def export_prometheus_format(self) -> str:
        """Export metrics in Prometheus format."""
        summary = await self.get_metrics_summary()
        lines = []
        
        # Add uptime
        lines.append(f"# HELP bot_uptime_seconds Bot uptime in seconds")
        lines.append(f"# TYPE bot_uptime_seconds gauge")
        lines.append(f"bot_uptime_seconds {summary['uptime_seconds']}")
        
        # Export counters
        for key, data in summary['counters'].items():
            name = key.replace('.', '_').replace('-', '_')
            tags = "_".join(f'{k}="{v}"' for k, v in data['tags'].items())
            tag_str = f"{{{tags}}}" if tags else ""
            lines.append(f"{name}_total{tag_str} {data['value']}")
        
        # Export gauges
        for key, data in summary['gauges'].items():
            name = key.replace('.', '_').replace('-', '_')
            tags = "_".join(f'{k}="{v}"' for k, v in data['tags'].items())
            tag_str = f"{{{tags}}}" if tags else ""
            lines.append(f"{name}{tag_str} {data['value']}")
        
        # Export histograms
        for key, data in summary['histograms'].items():
            name = key.replace('.', '_').replace('-', '_')
            tags = "_".join(f'{k}="{v}"' for k, v in data['tags'].items())
            tag_str = f"{{{tags}}}" if tags else ""
            
            if data['count'] > 0:
                lines.append(f"{name}_count{tag_str} {data['count']}")
                lines.append(f"{name}_sum{tag_str} {data['avg'] * data['count']}")
                lines.append(f"{name}_min{tag_str} {data['min']}")
                lines.append(f"{name}_max{tag_str} {data['max']}")
                lines.append(f"{name}_avg{tag_str} {data['avg']}")
                lines.append(f"{name}_p50{tag_str} {data['p50']}")
                lines.append(f"{name}_p95{tag_str} {data['p95']}")
                lines.append(f"{name}_p99{tag_str} {data['p99']}")
        
        return "\n".join(lines)

class TimingContext:
    """Context manager for timing function execution."""
    
    def __init__(self, metrics_service: MetricsService, name: str, tags: Optional[Dict[str, str]]):
        self.metrics_service = metrics_service
        self.name = name
        self.tags = tags
        self.start_time = None
    
    async def __aenter__(self):
        self.start_time = time.time()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.start_time:
            duration = time.time() - self.start_time
            await self.metrics_service.record_timing(self.name, duration, self.tags)

# Global metrics service instance
metrics_service = MetricsService()
