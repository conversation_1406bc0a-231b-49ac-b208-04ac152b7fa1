"""Data Buffering and Validation Manager for Real-Time Streaming

Handles buffering of live market data updates, validation against schemas,
and on-the-fly computation of technical indicators from buffered data.

Uses collections.deque for efficient fixed-size window and Pydantic for validation.
Integrates with WebSocket handler by providing add_update method as callback.

Dependencies: pip install pydantic (likely already in requirements.txt).
"""

import logging
from typing import Dict, List, Optional, Any
from collections import deque
from datetime import datetime, timedelta
import numpy as np
from pydantic import BaseModel, validator, ValidationError

from src.shared.error_handling.logging import get_trading_logger

logger = get_trading_logger(__name__)

class LiveDataUpdate(BaseModel):
    """Pydantic model for validating live data updates from WebSocket."""
    symbol: str
    price: float
    size: Optional[int] = None
    timestamp: str  # ISO format
    type: str  # 'T' or 'Q'
    provider: str = "polygon_live"

    @validator('price')
    def price_must_be_positive(cls, v):
        if v <= 0:
            raise ValueError('Price must be positive')
        return v

    @validator('timestamp')
    def timestamp_recent(cls, v):
        dt = datetime.fromisoformat(v.replace('Z', '+00:00'))
        if datetime.utcnow() - dt > timedelta(minutes=1):
            raise ValueError('Timestamp too old')
        return v

class DataBuffer:
    """
    Buffer manager for live market data.
    
    Maintains a fixed-size deque of validated updates per symbol.
    Provides latest snapshot and basic indicator computation.
    
    Usage:
        buffer = DataBuffer(max_size=100)
        buffer.add_update(live_data_dict)
        latest = buffer.get_latest('AAPL')
        indicators = buffer.compute_indicators('AAPL', window=20)
    """
    
    def __init__(self, max_size: int = 100):
        self.max_size = max_size
        self.buffers: Dict[str, deque] = {}  # symbol -> deque of validated updates
        self.indicator_cache: Dict[str, Dict] = {}  # Simple cache for computed indicators
    
    def add_update(self, update: Dict[str, Any]) -> bool:
        """Add and validate a live update. Returns True if successful."""
        symbol = update.get('symbol')
        if not symbol:
            logger.warning("Update missing symbol; discarded.")
            return False
        
        try:
            # Validate with Pydantic
            validated = LiveDataUpdate(**update).dict()
            
            # Initialize buffer if new symbol
            if symbol not in self.buffers:
                self.buffers[symbol] = deque(maxlen=self.max_size)
                logger.info(f"Initialized buffer for {symbol}")
            
            # Append to deque
            self.buffers[symbol].append(validated)
            
            # Invalidate cache for this symbol
            if symbol in self.indicator_cache:
                del self.indicator_cache[symbol]
            
            logger.debug(f"Buffered update for {symbol}: price={validated['price']}")
            return True
            
        except ValidationError as e:
            logger.warning(f"Validation failed for {symbol}: {e}")
            return False
        except Exception as e:
            logger.error(f"Error buffering update for {symbol}: {e}")
            return False
    
    def get_latest(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get the most recent validated update for a symbol."""
        if symbol in self.buffers and self.buffers[symbol]:
            latest = self.buffers[symbol][-1]
            # Merge with pipeline format
            return {
                'live_price': latest['price'],
                'live_size': latest['size'],
                'live_timestamp': latest['timestamp'],
                'live_type': latest['type'],
                'buffer_size': len(self.buffers[symbol])
            }
        logger.debug(f"No buffered data for {symbol}")
        return None
    
    def get_buffer(self, symbol: str) -> List[Dict[str, Any]]:
        """Get full buffer history for symbol (for indicators)."""
        if symbol in self.buffers:
            return list(self.buffers[symbol])
        return []
    
    def compute_indicators(self, symbol: str, window: int = 20) -> Optional[Dict[str, float]]:
        """Compute basic technical indicators from buffer (e.g., SMA, volatility).
        
        Requires at least 'window' data points. Caches results.
        """
        if symbol in self.indicator_cache:
            return self.indicator_cache[symbol]
        
        buffer = self.get_buffer(symbol)
        if len(buffer) < window:
            logger.debug(f"Insufficient data for indicators on {symbol}: {len(buffer)} < {window}")
            return None
        
        prices = [item['price'] for item in buffer[-window:]]
        
        # Simple Moving Average (SMA)
        sma = np.mean(prices)
        
        # Volatility (std dev)
        volatility = np.std(prices)
        
        # Recent change %
        if len(prices) >= 2:
            change_pct = (prices[-1] - prices[0]) / prices[0] * 100
        else:
            change_pct = 0.0
        
        indicators = {
            'sma': round(float(sma), 4),
            'volatility': round(float(volatility), 4),
            'change_pct': round(change_pct, 2)
        }
        
        self.indicator_cache[symbol] = indicators
        logger.debug(f"Computed indicators for {symbol}: {indicators}")
        return indicators
    
    def clear_buffer(self, symbol: str):
        """Clear buffer for a symbol (e.g., on unsubscribe)."""
        if symbol in self.buffers:
            self.buffers[symbol].clear()
            if symbol in self.indicator_cache:
                del self.indicator_cache[symbol]
            logger.info(f"Cleared buffer for {symbol}")


# Example usage (for testing)
def example_usage():
    buffer = DataBuffer(max_size=5)
    sample_update = {
        "symbol": "AAPL",
        "price": 150.25,
        "size": 100,
        "timestamp": datetime.utcnow().isoformat() + "Z",
        "type": "T",
        "provider": "polygon_live"
    }
    buffer.add_update(sample_update)
    latest = buffer.get_latest("AAPL")
    print(f"Latest: {latest}")
    indicators = buffer.compute_indicators("AAPL", window=1)
    print(f"Indicators: {indicators}")

if __name__ == "__main__":
    example_usage()