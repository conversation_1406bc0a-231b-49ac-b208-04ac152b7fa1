"""
Data validation and gap detection for market data quality assessment.

This module provides comprehensive data quality validation including:
- Gap detection in time series data
- Data completeness analysis
- Quality scoring and reporting
"""

import logging
from datetime import datetime, timedelta, time
from typing import List, Dict, Optional, Tuple, Union, Any
from dataclasses import dataclass
from enum import Enum
import pandas as pd
import numpy as np
import pytz

# Import market calendar for proper trading day detection
from src.core.market_calendar import MarketCalendar, Exchange

logger = logging.getLogger(__name__)


class GapSeverity(Enum):
    """Severity levels for data gaps."""
    MINOR = "minor"      # ≤5 minutes missing
    MODERATE = "moderate"  # 5-30 minutes missing
    MAJOR = "major"      # 30+ minutes missing
    CRITICAL = "critical"  # 2+ hours missing


@dataclass
class DataGap:
    """Represents a detected data gap in time series data."""
    symbol: str
    start_time: datetime
    end_time: datetime
    duration_seconds: float
    severity: GapSeverity
    interval_type: str  # '1m', '5m', '1h', '1d', etc.
    expected_count: int
    actual_count: int
    missing_count: int
    gap_percentage: float
    
    def __post_init__(self):
        """Calculate derived fields after initialization."""
        if not hasattr(self, 'duration_seconds'):
            self.duration_seconds = (self.end_time - self.start_time).total_seconds()
        
        if not hasattr(self, 'gap_percentage'):
            if self.expected_count > 0:
                self.gap_percentage = (self.missing_count / self.expected_count) * 100
            else:
                self.gap_percentage = 0.0


@dataclass
class DataQualityReport:
    """Comprehensive data quality assessment report."""
    symbol: str
    timeframe: str
    analysis_period: Tuple[datetime, datetime]
    total_expected_points: int
    total_actual_points: int
    completeness_percentage: float
    gaps: List[DataGap]
    quality_score: float  # 0-100
    recommendations: List[str]
    analysis_timestamp: datetime
    
    def __post_init__(self):
        """Calculate derived fields after initialization."""
        if not hasattr(self, 'completeness_percentage'):
            if self.total_expected_points > 0:
                self.completeness_percentage = (self.total_actual_points / self.total_expected_points) * 100
            else:
                self.completeness_percentage = 0.0


class DataValidator:
    """
    Comprehensive data validation and gap detection system.
    
    Provides methods for:
    - Detecting gaps in time series data
    - Assessing data completeness
    - Generating quality reports
    - Recommending data quality improvements
    """
    
    def __init__(self, market_calendar: Optional[MarketCalendar] = None):
        """
        Initialize the data validator.
        
        Args:
        market_calendar: Market calendar instance for trading day detection
        """
        self.market_calendar = market_calendar or MarketCalendar()
        self.logger = logging.getLogger(__name__)
        
        # Gap detection thresholds (in minutes)
        self.gap_thresholds = {
        GapSeverity.MINOR: 5,
        GapSeverity.MODERATE: 30,
        GapSeverity.MAJOR: 120,
        GapSeverity.CRITICAL: 240
        }
        
        self.logger.info("DataValidator initialized")
    
    def detect_gaps(
        self, 
        data: pd.DataFrame, 
        symbol: str,
        timeframe: str,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None
    ) -> List[DataGap]:
        """
        Detect gaps in time series data.
        
        Args:
        data: DataFrame with datetime index and OHLCV data
        symbol: Symbol being analyzed
        timeframe: Data timeframe (e.g., '1m', '5m', '1h', '1d')
        start_time: Analysis start time (defaults to data start)
        end_time: Analysis end time (defaults to data end)
        
        Returns:
        List of detected DataGap objects
        """
        if data.empty:
            self.logger.warning(f"No data provided for gap detection: {symbol}")
            return []
        
        # Set analysis period
        if start_time is None:
            start_time = data.index.min()
        elif end_time is None:
            end_time = data.index.max()
        
        # Generate expected time series
        expected_times = self._generate_expected_timeseries(
        start_time, end_time, timeframe, symbol
            )
        
        # Find gaps
        gaps = []
        actual_times = set(data.index)
        
        gap_start = None
        for expected_time in expected_times:
            if expected_time not in actual_times:
                if gap_start is None:
                    gap_start = expected_time
            else:
                if gap_start is not None:
                    # End of gap
                    gap = self._create_gap(
                        symbol, gap_start, expected_time, 
                    timeframe, expected_times
                )
                if gap:
                    gaps.append(gap)
                gap_start = None
        
        # Handle gap at end of period
        if gap_start is not None:
            gap = self._create_gap(
                symbol, gap_start, end_time, 
                timeframe, expected_times
            )
            if gap:
                gaps.append(gap)
        
        self.logger.info(f"Detected {len(gaps)} gaps for {symbol} ({timeframe})")
        return gaps
    
    def _generate_expected_timeseries(
        self, 
        start_time: datetime, 
        end_time: datetime, 
        timeframe: str, 
        symbol: str
    ) -> List[datetime]:
        """Generate expected time series based on timeframe and trading hours."""
        if timeframe.endswith('m'):  # Minute data
            minutes = int(timeframe[:-1])
            freq = f'{minutes}min'
        elif timeframe.endswith('h'):  # Hour data
            hours = int(timeframe[:-1])
            freq = f'{hours}h'
        elif timeframe.endswith('d'):  # Daily data
            days = int(timeframe[:-1])
            freq = f'{days}D'
        else:  # Unsupported timeframe
            raise ValueError(f"Unsupported timeframe: {timeframe}")
        
        # Generate full time series
        full_series = pd.date_range(start=start_time, end=end_time, freq=freq)
        
        # Filter to trading hours if intraday data
        if timeframe.endswith('m') or timeframe.endswith('h'):
            trading_times = []
            for dt in full_series:
                if self._is_trading_time(dt, symbol):
                    trading_times.append(dt)
            return trading_times
        
        return full_series.tolist()
    
    def _is_trading_time(self, dt: datetime, symbol: str) -> bool:
        """Check if datetime falls within trading hours for symbol."""
        try:
            # Get exchange for symbol (simplified - in reality would map symbols to exchanges)
            exchange = self._get_exchange_for_symbol(symbol)
            return self.market_calendar.is_trading_time(dt, exchange)
        except Exception:
            # Fallback to basic market hours (9:30 AM - 4:00 PM ET)
            market_open = time(9, 30)
            market_close = time(16, 0)
            dt_time = dt.time()
            return market_open <= dt_time <= market_close
    
    def _get_exchange_for_symbol(self, symbol: str) -> Exchange:
        """Get exchange for a symbol (simplified mapping)."""
        # In reality, this would be a comprehensive mapping
        if symbol in ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA']:
            return Exchange.NASDAQ
        else:
            return Exchange.NYSE
    
    def _create_gap(
        self, 
        symbol: str, 
        start_time: datetime, 
        end_time: datetime, 
        timeframe: str, 
        expected_times: List[datetime]
    ) -> Optional[DataGap]:
        """Create a DataGap object from gap parameters."""
        duration_minutes = (end_time - start_time).total_seconds() / 60
        
        # Determine severity
        severity = GapSeverity.MINOR
        for sev, threshold in self.gap_thresholds.items():
            if duration_minutes >= threshold:
                severity = sev
        
        # Calculate expected and actual counts
        expected_count = len([t for t in expected_times if start_time <= t < end_time])
        actual_count = 0  # No data in gap
        missing_count = expected_count
        
        gap_percentage = (missing_count / expected_count * 100) if expected_count > 0 else 0
        
        return DataGap(
        symbol=symbol,
        start_time=start_time,
        end_time=end_time,
        duration_seconds=duration_minutes * 60,
        severity=severity,
        interval_type=timeframe,
        expected_count=expected_count,
        actual_count=actual_count,
        missing_count=missing_count,
        gap_percentage=gap_percentage
        )
    
    def assess_data_quality(
        self, 
        data: pd.DataFrame, 
        symbol: str,
        timeframe: str,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None
    ) -> DataQualityReport:
        """
        Perform comprehensive data quality assessment.
        
        Args:
        data: DataFrame with datetime index and OHLCV data
        symbol: Symbol being analyzed
        timeframe: Data timeframe
        start_time: Analysis start time
        end_time: Analysis end time
        
        Returns:
        DataQualityReport with comprehensive analysis
        """
        if data.empty:
            return DataQualityReport(
                symbol=symbol,
                timeframe=timeframe,
                analysis_period=(start_time or datetime.now(), end_time or datetime.now()),
                total_expected_points=0,
                total_actual_points=0,
                completeness_percentage=0.0,
                gaps=[],
                quality_score=0.0,
                recommendations=["No data available for analysis"],
                analysis_timestamp=datetime.now()
            )
        
        # Set analysis period
        if start_time is None:
            start_time = data.index.min()
        elif end_time is None:
            end_time = data.index.max()
        
        # Generate expected time series
        expected_times = self._generate_expected_timeseries(
        start_time, end_time, timeframe, symbol
        )
        
        # Detect gaps
        gaps = self.detect_gaps(data, symbol, timeframe, start_time, end_time)
        
        # Calculate metrics
        total_expected = len(expected_times)
        total_actual = len(data)
        completeness = (total_actual / total_expected * 100) if total_expected > 0 else 0
        
        # Calculate quality score
        quality_score = self._calculate_quality_score(completeness, gaps)
        
        # Generate recommendations
        recommendations = self._generate_recommendations(gaps, completeness, quality_score)
        
        return DataQualityReport(
        symbol=symbol,
        timeframe=timeframe,
        analysis_period=(start_time, end_time),
        total_expected_points=total_expected,
        total_actual_points=total_actual,
        completeness_percentage=completeness,
        gaps=gaps,
        quality_score=quality_score,
        recommendations=recommendations,
        analysis_timestamp=datetime.now()
        )
    
    def _calculate_quality_score(self, completeness: float, gaps: List[DataGap]) -> float:
        """Calculate overall data quality score (0-100)."""
        # Base score from completeness
        base_score = completeness
        
        # Penalty for gaps based on severity
        gap_penalty = 0
        for gap in gaps:
            if gap.severity == GapSeverity.CRITICAL:
                gap_penalty += 20
            elif gap.severity == GapSeverity.MAJOR:
                gap_penalty += 10
            elif gap.severity == GapSeverity.MODERATE:
                gap_penalty += 5
            elif gap.severity == GapSeverity.MINOR:
                gap_penalty += 1
        
        # Apply penalty
        quality_score = max(0, base_score - gap_penalty)
        
        return min(100, quality_score)
    
    def _generate_recommendations(
        self, 
        gaps: List[DataGap], 
        completeness: float, 
        quality_score: float
    ) -> List[str]:
        """Generate data quality improvement recommendations."""
        recommendations = []
        
        # Completeness recommendations
        if completeness < 95:
            recommendations.append(f"Data completeness is {completeness:.1f}%. Consider improving data collection reliability.")
        
        if completeness < 80:
            recommendations.append("Data completeness is critically low. Immediate attention required.")
        
        # Gap-specific recommendations
        critical_gaps = [g for g in gaps if g.severity == GapSeverity.CRITICAL]
        major_gaps = [g for g in gaps if g.severity == GapSeverity.MAJOR]
        
        if critical_gaps:
            recommendations.append(f"Found {len(critical_gaps)} critical data gaps. Review data source reliability.")
        
        if major_gaps:
            recommendations.append(f"Found {len(major_gaps)} major data gaps. Consider implementing gap filling strategies.")
        
        # Quality score recommendations
        if quality_score < 70:
            recommendations.append("Overall data quality is poor. Comprehensive data quality review recommended.")
        elif quality_score < 90:
            recommendations.append("Data quality is acceptable but could be improved.")
        else:
            recommendations.append("Data quality is excellent.")
        
        return recommendations

    def validate_ohlcv_data(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Validate OHLCV data structure and content.
        
        Args:
            data: DataFrame with OHLCV columns
            
        Returns:
            Dictionary with validation results
        """
        validation_results = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'statistics': {}
        }
        
        # Check required columns 
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        missing_columns = [col for col in required_columns if col not in data.columns]
        
        if missing_columns:
            validation_results['is_valid'] = False
            validation_results['errors'].append(f"Missing required columns: {missing_columns}")
            return validation_results

        # Check for negative prices
        price_columns = ['open', 'high', 'low', 'close']
        for col in price_columns:
            negative_prices = (data[col] <= 0).sum()
            if negative_prices > 0:
                validation_results['warnings'].append(f"Found {negative_prices} non-positive prices in {col}")

        # Check for negative volume
        negative_volume = (data['volume'] < 0).sum()
        if negative_volume > 0:
            validation_results['warnings'].append(f"Found {negative_volume} negative volume entries")
            
        # Check OHLC relationships
        invalid_ohlc = (
            (data['high'] < data['low']) |
            (data['high'] < data['open']) |
            (data['high'] < data['close']) |
            (data['low'] > data['open']) |
            (data['low'] > data['close'])
        ).sum()

        if invalid_ohlc > 0:
            validation_results['warnings'].append(f"Found {invalid_ohlc} invalid OHLC relationships")
            
        # Calculate statistics
        validation_results['statistics'] = {
            'total_rows': len(data),
            'date_range': (data.index.min(), data.index.max()) if not data.empty else None,
            'price_range': {
                'min': data[price_columns].min().min(),
                'max': data[price_columns].max().max()
            },
            'volume_stats': {
                'min': data['volume'].min(),
                'max': data['volume'].max(),
                'mean': data['volume'].mean()
            }
        }
        
        return validation_results


# Global validator instance
data_validator = DataValidator()

# Convenience function for external imports
def assess_data_quality(data: pd.DataFrame, symbol: str, timeframe: str, 
                       start_time: Optional[datetime] = None, 
                       end_time: Optional[datetime] = None) -> DataQualityReport:
    """Convenience function to assess data quality using the global validator."""
    return data_validator.assess_data_quality(data, symbol, timeframe, start_time, end_time)


def validate_market_data(
    data: pd.DataFrame, 
    symbol: str, 
    timeframe: str,
    start_time: Optional[datetime] = None,
    end_time: Optional[datetime] = None
) -> DataQualityReport:
    """
    Convenience function for data quality assessment.
    
    Args:
        data: DataFrame with datetime index and OHLCV data
        symbol: Symbol being analyzed
        timeframe: Data timeframe
        start_time: Analysis start time
        end_time: Analysis end time
        
    Returns:
        DataQualityReport with comprehensive analysis
    """
    return data_validator.assess_data_quality(data, symbol, timeframe, start_time, end_time)


def detect_data_gaps(
    data: pd.DataFrame, 
    symbol: str,
    timeframe: str,
    start_time: Optional[datetime] = None,
    end_time: Optional[datetime] = None
) -> List[DataGap]:
    """
    Convenience function for gap detection.
    
    Args:
        data: DataFrame with datetime index and OHLCV data
        symbol: Symbol being analyzed
        timeframe: Data timeframe
        start_time: Analysis start time
        end_time: Analysis end time
        
    Returns:
        List of detected DataGap objects
    """
    return data_validator.detect_gaps(data, symbol, timeframe, start_time, end_time)