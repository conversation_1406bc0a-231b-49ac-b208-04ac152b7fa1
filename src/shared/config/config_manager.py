"""
Centralized Configuration Manager
Provides a unified interface for accessing configuration across the application.
"""

import os
import yaml
from typing import Dict, Any, Optional
from pathlib import Path
from src.shared.error_handling.logging import get_logger

logger = get_logger(__name__)

class ConfigManager:
    """Centralized configuration manager for the application."""
    
    def __init__(self):
        self._config: Dict[str, Any] = {}
        self._load_config()
    
    def _load_config(self):
        """Load configuration from environment variables and config files."""
        # Load from environment variables
        self._load_from_env()
        
        # Load from config files if they exist
        self._load_from_yaml()
        
        # Set defaults for missing values
        self._set_defaults()
        
        logger.info(f"✅ Configuration loaded with {len(self._config)} settings")
    
    def _load_from_env(self):
        """Load configuration from environment variables."""
        env_mappings = {
            # Database
            'SUPABASE_URL': 'database.supabase_url',
            'SUPABASE_KEY': 'database.supabase_key',
            'USE_SUPABASE': 'database.use_supabase',
            
            # Discord
            'DISCORD_BOT_TOKEN': 'discord.bot_token',
            'DISCORD_GUILD_ID': 'discord.guild_id',
            
            # AI Services
            'OPENROUTER_API_KEY': 'ai.openrouter_api_key',
            'AI_MODEL': 'ai.model',
            'AI_MAX_TOKENS': 'ai.max_tokens',
            
            # Data Providers
            'ALPHA_VANTAGE_API_KEY': 'data_providers.alpha_vantage_api_key',
            'POLYGON_API_KEY': 'data_providers.polygon_api_key',
            'FINNHUB_API_KEY': 'data_providers.finnhub_api_key',
            
            # Redis
            'REDIS_URL': 'cache.redis_url',
            'REDIS_PASSWORD': 'cache.redis_password',
            
            # Performance
            'QUICK_PRICE_TIMEOUT': 'performance.quick_price_timeout',
            'ASK_CACHE_TTL': 'performance.ask_cache_ttl',
            'ASK_CACHE_MAX_SIZE': 'performance.ask_cache_max_size',
            'AI_REQUEST_TIMEOUT': 'performance.ai_request_timeout',
            
            # Rate Limiting
            'RATE_LIMIT_REQUESTS_PER_HOUR': 'rate_limiting.requests_per_hour',
            'RATE_LIMIT_BURST_SIZE': 'rate_limiting.burst_size',
        }
        
        for env_var, config_path in env_mappings.items():
            value = os.getenv(env_var)
            if value is not None:
                self._set_nested_value(config_path, self._parse_env_value(value))
    
    def _load_from_yaml(self):
        """Load configuration from YAML files."""
        config_files = [
            'config.yaml',
            'config.yml',
            'config/application.yaml',
            'config/application.yml'
        ]
        
        for config_file in config_files:
            config_path = Path(config_file)
            if config_path.exists():
                try:
                    with open(config_path, 'r') as f:
                        yaml_config = yaml.safe_load(f)
                        if yaml_config:
                            self._merge_config(yaml_config)
                            logger.info(f"✅ Loaded config from {config_file}")
                except Exception as e:
                    logger.warning(f"⚠️ Failed to load {config_file}: {e}")
    
    def _set_defaults(self):
        """Set default values for missing configuration."""
        defaults = {
            'ai': {
                'model': 'moonshotai/kimi-k2-0905',
                'max_tokens': 1000,
                'temperature': 0.7
            },
            'performance': {
                'quick_price_timeout': 2.0,
                'ask_cache_ttl': 60,  # 1 minute
                'ask_cache_max_size': 1000,
                'ai_request_timeout': 15.0
            },
            'rate_limiting': {
                'requests_per_hour': 10,
                'burst_size': 3
            },
            'cache': {
                'enabled': True,
                'default_ttl': 300  # 5 minutes
            }
        }
        
        for key, value in defaults.items():
            if key not in self._config:
                self._config[key] = value
            elif isinstance(value, dict):
                for sub_key, sub_value in value.items():
                    if sub_key not in self._config[key]:
                        self._config[key][sub_key] = sub_value
    
    def _parse_env_value(self, value: str) -> Any:
        """Parse environment variable value to appropriate type."""
        # Boolean values
        if value.lower() in ('true', 'false'):
            return value.lower() == 'true'
        
        # Numeric values
        if value.isdigit():
            return int(value)
        
        try:
            # Try float
            return float(value)
        except ValueError:
            pass
        
        # String value
        return value
    
    def _set_nested_value(self, path: str, value: Any):
        """Set a nested configuration value using dot notation."""
        keys = path.split('.')
        current = self._config
        
        for key in keys[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]
        
        current[keys[-1]] = value
    
    def _merge_config(self, new_config: Dict[str, Any]):
        """Merge new configuration into existing config."""
        def merge_dicts(d1: Dict[str, Any], d2: Dict[str, Any]) -> Dict[str, Any]:
            for key, value in d2.items():
                if key in d1 and isinstance(d1[key], dict) and isinstance(value, dict):
                    merge_dicts(d1[key], value)
                else:
                    d1[key] = value
            return d1
        
        self._config = merge_dicts(self._config, new_config)
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value using dot notation."""
        keys = key.split('.')
        current = self._config
        
        try:
            for key_part in keys:
                current = current[key_part]
            return current
        except (KeyError, TypeError):
            return default
    
    def get_section(self, section: str) -> Dict[str, Any]:
        """Get an entire configuration section."""
        return self.get(section, {})
    
    def set(self, key: str, value: Any):
        """Set a configuration value using dot notation."""
        self._set_nested_value(key, value)
    
    def all(self) -> Dict[str, Any]:
        """Get all configuration."""
        return self._config.copy()
    
    def reload(self):
        """Reload configuration from sources."""
        self._config = {}
        self._load_config()
        logger.info("🔄 Configuration reloaded")

# Global configuration instance
config = ConfigManager()
