"""
Shared Configuration Module

This module provides centralized configuration management for the trading bot.
It exports the main configuration manager and utility functions.
"""

from .config_manager import ConfigManager, config

# Export the main configuration instance and manager
__all__ = ['get_config', 'ConfigManager', 'config']

def get_config():
    """Get the global configuration manager instance"""
    return config