"""
Deprecation tracking utilities.

This module provides functions for tracking usage of deprecated code.
"""

import logging

# Configure deprecation logger
deprecation_logger = logging.getLogger("deprecation")

def record_deprecation(module_name: str, caller_info: str):
    """
    Record usage of deprecated modules for monitoring purposes.
    
    Args:
        module_name: Name of the deprecated module
        caller_info: Information about the caller (file:line)
    """
    deprecation_logger.warning(f"Deprecated module {module_name} used by {caller_info}")
    # In a real implementation, this might write to a database or metrics system 