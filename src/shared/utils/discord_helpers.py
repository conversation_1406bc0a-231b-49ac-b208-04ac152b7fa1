"""
Discord Helper Utilities

Shared utilities for Discord bot operations, including message length enforcement,
formatting, and response handling.
"""

import logging
from typing import Optional, Union, List
import discord

logger = logging.getLogger(__name__)

# Discord message limits
DISCORD_MESSAGE_LIMIT = 2000
DISCORD_EMBED_TITLE_LIMIT = 256
DISCORD_EMBED_DESCRIPTION_LIMIT = 4096
DISCORD_EMBED_FIELD_VALUE_LIMIT = 1024


class DiscordMessageHelper:
    """Helper class for Discord message operations"""
    
    @staticmethod
    def enforce_message_limit(
        message: str, 
        limit: int = DISCORD_MESSAGE_LIMIT,
        truncation_suffix: str = "\n\n... (message truncated due to length)"
    ) -> str:
        """
        Enforce Discord message length limits with smart truncation.
        
        Args:
            message: The message to check/truncate
            limit: Character limit (default: 2000 for Discord messages)
            truncation_suffix: Text to append when truncating
            
        Returns:
            Message that fits within the limit
        """
        if not message:
            return ""
            
        if len(message) <= limit:
            return message
            
        # Calculate available space for content
        available_space = limit - len(truncation_suffix)
        
        if available_space <= 0:
            # Suffix is too long, just truncate without suffix
            return message[:limit]
            
        # Try to truncate at a natural break point (sentence, paragraph, etc.)
        truncated = message[:available_space]
        
        # Look for natural break points in reverse order of preference
        break_points = ['\n\n', '\n', '. ', '! ', '? ', ', ', ' ']
        
        for break_point in break_points:
            last_break = truncated.rfind(break_point)
            if last_break > available_space * 0.7:  # Don't truncate too aggressively
                truncated = truncated[:last_break + len(break_point)]
                break
                
        return truncated + truncation_suffix
    
    @staticmethod
    async def safe_send_message(
        interaction: discord.Interaction,
        content: str,
        ephemeral: bool = False
    ) -> bool:
        """
        Safely send a message with automatic length enforcement.
        
        Args:
            interaction: Discord interaction object
            content: Message content to send
            ephemeral: Whether message should be ephemeral
            
        Returns:
            True if message was sent successfully, False otherwise
        """
        try:
            # Enforce message length limit
            safe_content = DiscordMessageHelper.enforce_message_limit(content)

            logger.debug(f"Sending Discord message (len={len(safe_content)})")

            # Check interaction state FIRST to determine the proper method
            try:
                response_done = interaction.response.is_done()
            except Exception:
                # If we can't check the state, assume it's done and use followup
                response_done = True
            
            # If interaction hasn't been responded to yet, use initial response
            if not response_done:
                try:
                    await interaction.response.send_message(safe_content, ephemeral=ephemeral)
                    logger.info("Sent message via initial response")
                    return True
                except Exception as e:
                    logger.warning(f"Initial response failed: {e}")
                    # Fall through to followup method
            
            # If interaction was already deferred/responded to, use followup
            try:
                await interaction.followup.send(safe_content, ephemeral=ephemeral)
                logger.info("Sent message via followup")
                return True
            except Exception as e:
                logger.error(f"Followup failed: {e}")
                return False

        except Exception as e:
            logger.error(f"Discord send error: {e}")
            return False

        except discord.HTTPException as e:
            logger.error(f"Discord HTTP error sending message: {e}")

            # Try to send a fallback error message
            try:
                error_msg = "❌ An error occurred while sending the response. Please try again."
                try:
                    response_done = interaction.response.is_done()
                except Exception:
                    response_done = False

                logger.debug(
                    "Attempting to send fallback error message",
                    extra={"interaction_response_done": response_done}
                )

                # For fallback, prefer initial response if possible
                if response_done:
                    await interaction.followup.send(error_msg, ephemeral=True)
                else:
                    await interaction.response.send_message(error_msg, ephemeral=True)
            except Exception as fallback_error:
                logger.error(f"Failed to send fallback error message: {fallback_error}")

            return False

        except Exception as e:
            logger.error(f"Unexpected error sending message: {e}")
            return False
    
    @staticmethod
    def create_safe_embed(
        title: str,
        description: str = "",
        color: int = 0x00ff00,
        fields: Optional[List[dict]] = None
    ) -> discord.Embed:
        """
        Create a Discord embed with automatic length enforcement.
        
        Args:
            title: Embed title
            description: Embed description
            color: Embed color (default: green)
            fields: List of field dictionaries with 'name', 'value', 'inline' keys
            
        Returns:
            Discord embed object with enforced limits
        """
        # Enforce title limit
        safe_title = title[:DISCORD_EMBED_TITLE_LIMIT] if title else ""
        
        # Enforce description limit
        safe_description = DiscordMessageHelper.enforce_message_limit(
            description, 
            DISCORD_EMBED_DESCRIPTION_LIMIT,
            "... (description truncated)"
        ) if description else ""
        
        embed = discord.Embed(
            title=safe_title,
            description=safe_description,
            color=color
        )
        
        # Add fields with length enforcement
        if fields:
            for field in fields:
                field_name = field.get('name', '')[:256]  # Field name limit
                field_value = DiscordMessageHelper.enforce_message_limit(
                    field.get('value', ''),
                    DISCORD_EMBED_FIELD_VALUE_LIMIT,
                    "... (field truncated)"
                )
                field_inline = field.get('inline', False)
                
                if field_name and field_value:
                    embed.add_field(
                        name=field_name,
                        value=field_value,
                        inline=field_inline
                    )
        
        return embed
    
    @staticmethod
    def format_code_block(content: str, language: str = "") -> str:
        """
        Format content as a Discord code block with length enforcement.
        
        Args:
            content: Content to format
            language: Programming language for syntax highlighting
            
        Returns:
            Formatted code block
        """
        if not content:
            return ""
            
        # Account for code block formatting
        code_block_overhead = len(f"```{language}\n```")
        available_space = DISCORD_MESSAGE_LIMIT - code_block_overhead
        
        # Truncate content if necessary
        if len(content) > available_space:
            content = content[:available_space - 20] + "\n... (truncated)"
            
        return f"```{language}\n{content}\n```"
    
    @staticmethod
    def split_long_message(message: str, limit: int = DISCORD_MESSAGE_LIMIT) -> List[str]:
        """
        Split a long message into multiple parts that fit Discord limits.
        
        Args:
            message: Message to split
            limit: Character limit per part
            
        Returns:
            List of message parts
        """
        if len(message) <= limit:
            return [message]
            
        parts = []
        current_part = ""
        
        # Split by lines to avoid breaking in the middle of sentences
        lines = message.split('\n')
        
        for line in lines:
            # If adding this line would exceed the limit
            if len(current_part) + len(line) + 1 > limit:
                if current_part:
                    parts.append(current_part.strip())
                    current_part = ""
                
                # If a single line is too long, split it
                if len(line) > limit:
                    while len(line) > limit:
                        parts.append(line[:limit])
                        line = line[limit:]
                    if line:
                        current_part = line
                else:
                    current_part = line
            else:
                if current_part:
                    current_part += '\n' + line
                else:
                    current_part = line
        
        if current_part:
            parts.append(current_part.strip())
            
        return parts


# Convenience functions
def safe_send_message(
    interaction: discord.Interaction,
    content: str,
    ephemeral: bool = False,
    use_followup: bool = False
) -> bool:
    """Convenience function for safe message sending"""
    return DiscordMessageHelper.safe_send_message(interaction, content, ephemeral, use_followup)


def enforce_discord_limit(message: str) -> str:
    """Convenience function for message length enforcement"""
    return DiscordMessageHelper.enforce_message_limit(message)


def create_safe_embed(title: str, description: str = "", **kwargs) -> discord.Embed:
    """Convenience function for safe embed creation"""
    return DiscordMessageHelper.create_safe_embed(title, description, **kwargs)
