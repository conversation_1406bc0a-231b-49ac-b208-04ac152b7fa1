"""
Unified Symbol Extraction Utilities

Consolidated from multiple symbol extraction implementations to provide single source of truth.
Features merged from:
- src/shared/utils/symbol_extraction.py (unified extractor)
- src/bot/pipeline/commands/ask/stages/symbol_validator.py (validation features)
- src/bot/pipeline/commands/ask/stages/ai_symbol_extractor.py (AI-powered extraction)

Key principles:
- Multiple extraction strategies (regex, AI, validation)
- Configurable strictness levels
- Comprehensive ticker validation
- Consistent behavior across all bot commands
"""

import re
import os
import json
import logging
import asyncio
from typing import List, Optional, Tuple, Set, Dict, Any
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class ExtractionStrategy(Enum):
    """Symbol extraction strategies"""
    DOLLAR_PREFIX = "dollar_prefix"      # $AAPL format
    REGEX_PATTERN = "regex_pattern"      # Pattern matching
    AI_POWERED = "ai_powered"            # AI-based extraction
    COMPANY_MAPPING = "company_mapping"  # Company name to ticker
    VALIDATION_ONLY = "validation_only"  # Validate existing symbols

@dataclass
class ExtractedSymbol:
    """Represents an extracted symbol with comprehensive metadata"""
    symbol: str
    confidence: float
    context: str  # How it was found
    original_text: str  # Original text that matched
    strategy: ExtractionStrategy  # Extraction strategy used
    is_valid_ticker: bool = True  # Validation result
    alternatives: List[str] = None  # Alternative suggestions

    def __post_init__(self):
        if self.alternatives is None:
            self.alternatives = []

@dataclass
class SymbolSuggestion:
    """Symbol suggestion with confidence metrics (backward compatibility)"""
    text: str
    confidence: float
    context: str
    is_likely_ticker: bool
    suggestions: List[str]


class UnifiedSymbolExtractor:
    """
    Unified symbol extraction for all bot commands.
    Consolidates multiple extraction strategies and validation features.

    Implements singleton pattern to avoid redundant ticker loading.
    """

    # Known non-ticker words to filter out
    NON_TICKERS = {
        'USD', 'CAD', 'EUR', 'GBP', 'JPY', 'CNY', 'AUD', 'CHF',  # Currencies
        'THE', 'AND', 'FOR', 'YOU', 'ARE', 'BUT', 'NOT', 'ALL',  # Common words
        'BUY', 'SELL', 'HOLD', 'LONG', 'SHORT', 'CALL', 'PUT',   # Trading terms
        'PRICE', 'STOCK', 'MARKET', 'TRADE', 'INVEST', 'MONEY'   # Finance terms
    }

    _instance = None
    _initialized = False

    def __new__(cls, enable_ai: bool = False, enable_validation: bool = True):
        """Singleton pattern to avoid multiple ticker loadings"""
        if cls._instance is None:
            cls._instance = super(UnifiedSymbolExtractor, cls).__new__(cls)
        return cls._instance

    def __init__(self, enable_ai: bool = False, enable_validation: bool = True):
        """
        Initialize the unified extractor with configurable features.

        Args:
            enable_ai: Enable AI-powered extraction (requires AI client)
            enable_validation: Enable ticker validation against known tickers
        """
        # Only initialize once
        if self._initialized:
            return

        self.symbol_pattern = re.compile(r'^[A-Z]{1,10}$')
        self.enable_ai = enable_ai
        self.enable_validation = enable_validation

        # Initialize validation database
        self.valid_tickers = set()
        if enable_validation:
            self._load_ticker_data()

        # Initialize company mappings (merged from ai_symbol_extractor.py)
        self._initialize_company_mappings()

        # Lazy initialize AI client to avoid startup rate limiting
        self.ai_client = None
        self._ai_client_initialized = False
        self._initialized = True

    @classmethod
    def reset_instance(cls):
        """Reset singleton instance (for testing purposes)"""
        cls._instance = None
        cls._initialized = False

    def _load_ticker_data(self):
        """Load ticker database for validation (merged from symbol_validator.py)"""
        try:
            ticker_file = "data/tickers/all_tickers.txt"
            if os.path.exists(ticker_file):
                with open(ticker_file, 'r') as f:
                    self.valid_tickers = {line.strip().upper() for line in f if line.strip()}
                logger.info(f"Loaded {len(self.valid_tickers)} valid tickers")
            else:
                logger.warning(f"Ticker database not found: {ticker_file}")
                # Fallback to common tickers
                self.valid_tickers = {
                    'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'NVDA', 'META', 'SPY', 'QQQ',
                    'NFLX', 'AMD', 'INTC', 'ORCL', 'CRM', 'ADBE', 'PYPL', 'UBER', 'LYFT'
                }
        except Exception as e:
            logger.error(f"Failed to load ticker data: {e}")
            self.valid_tickers = set()

    def _initialize_company_mappings(self):
        """Initialize company to ticker mappings (merged from ai_symbol_extractor.py)"""
        self.company_tickers = {
            'tesla': 'TSLA',
            'microsoft': 'MSFT',
            'apple': 'AAPL',
            'google': 'GOOGL',
            'alphabet': 'GOOGL',
            'amazon': 'AMZN',
            'nvidia': 'NVDA',
            'meta': 'META',
            'facebook': 'META',
            'netflix': 'NFLX',
            'amd': 'AMD',
            'intel': 'INTC',
            'oracle': 'ORCL',
            'salesforce': 'CRM',
            'adobe': 'ADBE',
            'paypal': 'PYPL',
            'uber': 'UBER',
            'lyft': 'LYFT',
            'spotify': 'SPOT',
            'zoom': 'ZM'
        }
        
    def extract_symbols(self, text: str, require_dollar_prefix: bool = False,
                       strategies: Optional[List[ExtractionStrategy]] = None) -> List[ExtractedSymbol]:
        """
        Extract stock symbols using multiple strategies.

        Args:
            text: Text to extract symbols from
            require_dollar_prefix: If True, only extract symbols with $ prefix
            strategies: List of strategies to use (default: all available)

        Returns:
            List of ExtractedSymbol objects sorted by confidence
        """
        if not text:
            return []

        # Default strategies based on configuration - AI FIRST approach
        if strategies is None:
            strategies = []

            # Prioritize AI extraction when available (it works incredibly well)
            if self.enable_ai:
                strategies.append(ExtractionStrategy.AI_POWERED)

            # Always include dollar prefix (high confidence)
            strategies.append(ExtractionStrategy.DOLLAR_PREFIX)

            # Add other strategies as fallbacks
            if not require_dollar_prefix:
                strategies.extend([
                    ExtractionStrategy.COMPANY_MAPPING,
                    ExtractionStrategy.REGEX_PATTERN  # Last resort
                ])

        extracted = []

        # Apply each strategy
        for strategy in strategies:
            if strategy == ExtractionStrategy.DOLLAR_PREFIX:
                extracted.extend(self._extract_dollar_prefix_symbols(text))
            elif strategy == ExtractionStrategy.REGEX_PATTERN:
                extracted.extend(self._extract_regex_symbols(text))
            elif strategy == ExtractionStrategy.COMPANY_MAPPING:
                extracted.extend(self._extract_company_symbols(text))
            elif strategy == ExtractionStrategy.AI_POWERED and self.enable_ai:
                extracted.extend(self._extract_ai_symbols(text))

        # Remove duplicates while preserving highest confidence
        symbol_map = {}
        for symbol in extracted:
            key = symbol.symbol
            if key not in symbol_map or symbol.confidence > symbol_map[key].confidence:
                symbol_map[key] = symbol

        # Sort by confidence (highest first)
        unique_extracted = sorted(symbol_map.values(), key=lambda x: x.confidence, reverse=True)

        return unique_extracted
    
    def _extract_dollar_prefix_symbols(self, text: str) -> List[ExtractedSymbol]:
        """Extract symbols with $ prefix (e.g., $AAPL)"""
        symbols = []

        # Pattern: $SYMBOL (1-10 uppercase letters after $)
        pattern = r'\$([A-Z]{1,10})\b'
        matches = re.finditer(pattern, text)

        for match in matches:
            symbol = match.group(1)
            if self._is_valid_symbol(symbol):
                is_valid = self._validate_ticker(symbol) if self.enable_validation else True
                symbols.append(ExtractedSymbol(
                    symbol=symbol,
                    confidence=0.95 if is_valid else 0.7,  # Lower confidence for invalid tickers
                    context="dollar_prefix",
                    original_text=match.group(0),
                    strategy=ExtractionStrategy.DOLLAR_PREFIX,
                    is_valid_ticker=is_valid
                ))
                
        return symbols
    
    def _extract_exchange_notation_symbols(self, text: str) -> List[ExtractedSymbol]:
        """Extract symbols with exchange notation (e.g., AAPL.NASDAQ)"""
        symbols = []
        
        # Pattern: SYMBOL.EXCHANGE
        pattern = r'\b([A-Z]{1,10})\.([A-Z]{1,10})\b'
        matches = re.finditer(pattern, text)
        
        for match in matches:
            symbol = match.group(1)
            exchange = match.group(2)
            
            # Validate both symbol and exchange
            if self._is_valid_symbol(symbol) and self._is_valid_exchange(exchange):
                symbols.append(ExtractedSymbol(
                    symbol=symbol,
                    confidence=0.85,  # Good confidence for exchange notation
                    context=f"exchange_notation_{exchange}",
                    original_text=match.group(0)
                ))
                
        return symbols
    
    def _is_valid_symbol(self, symbol: str) -> bool:
        """Validate if a string is a valid stock symbol"""
        if not symbol or len(symbol) < 1 or len(symbol) > 10:
            return False
            
        # Must be all uppercase letters
        if not self.symbol_pattern.match(symbol):
            return False
            
        # Filter out known non-tickers
        if symbol in self.NON_TICKERS:
            return False
            
        return True
    
    def _is_valid_exchange(self, exchange: str) -> bool:
        """Validate if a string is a valid exchange identifier"""
        valid_exchanges = {
            'NYSE', 'NASDAQ', 'AMEX', 'TSX', 'LSE', 'HKEX', 'ASX', 'BSE', 'NSE'
        }
        return exchange in valid_exchanges
    
    def extract_symbols_simple(self, text: str) -> List[str]:
        """
        Simple extraction that returns just symbol strings (for backward compatibility).

        Args:
            text: Text to extract symbols from

        Returns:
            List of symbol strings
        """
        extracted = self.extract_symbols(text, require_dollar_prefix=False)
        return [symbol.symbol for symbol in extracted]

    def _extract_regex_symbols(self, text: str) -> List[ExtractedSymbol]:
        """Extract symbols using regex patterns (without $ prefix)"""
        symbols = []

        # Pattern: standalone uppercase words (1-10 letters)
        pattern = r'\b([A-Z]{1,10})\b'
        matches = re.finditer(pattern, text)

        for match in matches:
            symbol = match.group(1)
            if self._is_valid_symbol(symbol):
                is_valid = self._validate_ticker(symbol) if self.enable_validation else True

                # Only include if it's a valid ticker or has high confidence characteristics
                if is_valid or self._has_ticker_characteristics(symbol):
                    confidence = 0.8 if is_valid else 0.4
                    symbols.append(ExtractedSymbol(
                        symbol=symbol,
                        confidence=confidence,
                        context="regex_pattern",
                        original_text=match.group(0),
                        strategy=ExtractionStrategy.REGEX_PATTERN,
                        is_valid_ticker=is_valid
                    ))

        return symbols

    def _extract_company_symbols(self, text: str) -> List[ExtractedSymbol]:
        """Extract symbols by mapping company names to tickers"""
        symbols = []
        text_lower = text.lower()

        for company, ticker in self.company_tickers.items():
            if company in text_lower:
                is_valid = self._validate_ticker(ticker) if self.enable_validation else True
                symbols.append(ExtractedSymbol(
                    symbol=ticker,
                    confidence=0.8 if is_valid else 0.5,
                    context=f"company_mapping:{company}",
                    original_text=company,
                    strategy=ExtractionStrategy.COMPANY_MAPPING,
                    is_valid_ticker=is_valid
                ))

        return symbols

    def _ensure_ai_client(self):
        """Lazy initialize AI client only when needed"""
        if not self._ai_client_initialized and self.enable_ai:
            try:
                from src.shared.ai_services.ai_client import AIClientWrapper
                self.ai_client = AIClientWrapper()
                logger.info("AI client initialized for symbol extraction")
            except ImportError:
                logger.warning("AI client not available, disabling AI extraction")
                self.enable_ai = False
            finally:
                self._ai_client_initialized = True

    def _extract_ai_symbols(self, text: str) -> List[ExtractedSymbol]:
        """Extract symbols using AI-powered analysis"""
        symbols = []

        # Lazy initialize AI client
        self._ensure_ai_client()

        if not self.ai_client:
            return symbols

        try:
            # Import the intelligent text parser
            import asyncio
            from src.shared.ai_services.intelligent_text_parser import intelligent_parser

            # Run AI symbol extraction
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # If we're already in an async context, create a task
                task = asyncio.create_task(intelligent_parser.extract_symbols(text, use_ai=True))
                # For now, we'll use a simple approach - in production you'd handle this better
                ai_symbols = []
            else:
                # If not in async context, run the coroutine
                ai_symbols = loop.run_until_complete(intelligent_parser.extract_symbols(text, use_ai=True))

            # Convert AI results to ExtractedSymbol format
            for symbol in ai_symbols:
                symbols.append(ExtractedSymbol(
                    symbol=symbol,
                    confidence=0.9,  # High confidence for AI extraction
                    context="ai_powered",
                    original_text=text,
                    strategy=ExtractionStrategy.AI_POWERED,
                    is_valid_ticker=True  # AI should validate this
                ))

            logger.info(f"AI extracted {len(symbols)} symbols: {[s.symbol for s in symbols]}")
            return symbols

        except Exception as e:
            logger.error(f"AI symbol extraction failed: {e}")
            return symbols

    def _validate_ticker(self, symbol: str) -> bool:
        """Validate ticker against known ticker database"""
        if not self.enable_validation or not self.valid_tickers:
            return True
        return symbol.upper() in self.valid_tickers


# Backward compatibility classes
class SymbolValidator(UnifiedSymbolExtractor):
    """Backward compatibility for symbol_validator.py"""

    def __init__(self):
        super().__init__(enable_ai=False, enable_validation=True)
        logger.info("SymbolValidator initialized (compatibility mode)")

    def extract_symbols_from_query(self, query: str) -> List[SymbolSuggestion]:
        """Extract symbols with legacy interface"""
        extracted = self.extract_symbols(query, require_dollar_prefix=True)
        suggestions = []

        for symbol in extracted:
            suggestions.append(SymbolSuggestion(
                text=symbol.symbol,
                confidence=symbol.confidence,
                context=symbol.context,
                is_likely_ticker=symbol.is_valid_ticker,
                suggestions=symbol.alternatives
            ))

        return suggestions


# Global instance for easy access
unified_symbol_extractor = UnifiedSymbolExtractor()


# Convenience functions for backward compatibility
def extract_symbols_from_query(query: str) -> List[str]:
    """
    Extract symbols from query - AI-first implementation for understanding context.

    This uses AI to understand the intent and context, not just regex patterns.
    """
    try:
        # Create AI-enabled extractor for intelligent understanding
        ai_extractor = UnifiedSymbolExtractor(enable_ai=True, enable_validation=True)

        # Use AI-first approach with intelligent fallbacks
        import asyncio
        loop = asyncio.get_event_loop()

        if loop.is_running():
            # In async context - use AI with smart fallbacks
            return _extract_symbols_async_context(ai_extractor, query)
        else:
            # Not in async context - run AI extraction
            return loop.run_until_complete(_extract_symbols_with_ai(ai_extractor, query))

    except Exception as e:
        logger.warning(f"AI symbol extraction failed, using smart fallback: {e}")
        # Smart fallback that only extracts high-confidence symbols
        return _extract_symbols_smart_fallback(query)


async def _extract_symbols_with_ai(extractor: UnifiedSymbolExtractor, query: str) -> List[str]:
    """Extract symbols using AI understanding of context and intent with rate limit handling"""
    try:
        # Use rate limit handler for reliable AI access
        from src.shared.ai_services.rate_limit_handler import rate_limit_handler

        async def ai_extraction_function(**kwargs):
            # Use the existing enhanced symbol extractor (it works incredibly well!)
            from src.shared.ai_services.enhanced_symbol_extractor import enhanced_symbol_extractor

            # Get AI-powered extraction results
            extraction_results = await enhanced_symbol_extractor.extract_symbols(query, use_ai=True)

            # Extract just the symbol strings
            symbols = [result.symbol for result in extraction_results if result.confidence >= 0.7]
            return symbols

        # Execute with rate limit handling and provider fallbacks
        symbols = await rate_limit_handler.execute_with_rate_limit_handling(
            ai_extraction_function,
            preferred_provider="openrouter",
            fallback_providers=["anthropic", "google"]
        )

        # Check if we got a fallback response (dict with 'fallback' key)
        if isinstance(symbols, dict) and symbols.get('fallback'):
            logger.info("AI services unavailable, using smart fallback")
            return _extract_symbols_smart_fallback(query)

        logger.info(f"AI extracted symbols with high confidence: {symbols}")
        return symbols if isinstance(symbols, list) else []

    except Exception as e:
        logger.warning(f"AI symbol extraction failed: {e}")
        # Fall back to smart pattern matching
        return _extract_symbols_smart_fallback(query)


def _extract_symbols_async_context(extractor: UnifiedSymbolExtractor, query: str) -> List[str]:
    """Handle symbol extraction in async context with AI understanding"""
    # For async context, use smart fallback with validation
    # In production, you'd implement proper async AI calls here
    return _extract_symbols_smart_fallback(query)


def _extract_symbols_smart_fallback(query: str) -> List[str]:
    """Smart fallback using local AI intelligence for reliable symbol extraction"""
    try:
        # Use local fallback AI for intelligent extraction
        import asyncio
        from src.shared.ai_services.local_fallback_ai import local_fallback_ai

        # Run local AI extraction
        loop = asyncio.get_event_loop()
        if loop.is_running():
            # Create a task for async context
            task = asyncio.create_task(local_fallback_ai.extract_symbols(query))
            # For now, use the basic fallback - in production you'd handle this better
            return _extract_symbols_basic_fallback(query)
        else:
            # Run the local AI extraction
            response = loop.run_until_complete(local_fallback_ai.extract_symbols(query))
            logger.info(f"Local AI extraction: {response.symbols} (confidence: {response.confidence}, method: {response.method})")
            return response.symbols

    except Exception as e:
        logger.warning(f"Local AI fallback failed: {e}")
        return _extract_symbols_basic_fallback(query)


def _extract_symbols_basic_fallback(query: str) -> List[str]:
    """Basic pattern-based fallback when all AI methods fail"""
    symbols = []

    # Only extract symbols with $ prefix (highest confidence)
    dollar_pattern = r'\$([A-Z]{1,10})\b'
    dollar_matches = re.findall(dollar_pattern, query)
    symbols.extend(dollar_matches)

    # Extract from company name mappings (high confidence)
    extractor = UnifiedSymbolExtractor(enable_ai=False, enable_validation=True)
    company_symbols = extractor._extract_company_symbols(query)
    symbols.extend([s.symbol for s in company_symbols if s.is_valid_ticker])

    # Only extract standalone uppercase words if they're validated tickers
    # AND the query seems to be asking about specific stocks
    if _query_seems_stock_focused(query):
        pattern = r'\b([A-Z]{2,5})\b'
        potential_symbols = re.findall(pattern, query)

        for symbol in potential_symbols:
            if (extractor._validate_ticker(symbol) and
                symbol not in extractor.NON_TICKERS and
                len(symbol) >= 2):
                symbols.append(symbol)

    # Remove duplicates while preserving order
    seen = set()
    unique_symbols = []
    for symbol in symbols:
        if symbol not in seen:
            seen.add(symbol)
            unique_symbols.append(symbol)

    return unique_symbols


def _query_seems_stock_focused(query: str) -> bool:
    """Determine if a query is asking about stocks/financial data"""
    stock_keywords = {
        'stock', 'price', 'ticker', 'share', 'market', 'trading', 'invest',
        'buy', 'sell', 'portfolio', 'earnings', 'dividend', 'analysis',
        'chart', 'performance', 'compare', 'vs', 'versus', 'financial'
    }

    query_lower = query.lower()
    return any(keyword in query_lower for keyword in stock_keywords)


def _parse_ai_symbol_response(response: str) -> List[str]:
    """Parse AI response to extract symbol list"""
    try:
        import json
        # Try to parse as JSON array
        if '[' in response and ']' in response:
            start = response.find('[')
            end = response.rfind(']') + 1
            json_part = response[start:end]
            symbols = json.loads(json_part)
            return [s.upper().strip() for s in symbols if isinstance(s, str)]
    except:
        pass

    # Fallback: extract uppercase words from response
    symbols = re.findall(r'\b([A-Z]{2,10})\b', response)
    return [s for s in symbols if len(s) <= 6]  # Reasonable ticker length


def extract_symbols_with_metadata(query: str) -> List[ExtractedSymbol]:
    """
    Extract symbols with full metadata.
    
    Use this when you need confidence scores and context information.
    """
    return unified_symbol_extractor.extract_symbols(query)


def validate_symbol_format(symbol: str) -> Tuple[str, bool, str]:
    """
    Validate and sanitize a symbol format.
    
    Args:
        symbol: Symbol to validate
        
    Returns:
        Tuple of (sanitized_symbol, is_valid, error_message)
    """
    if not symbol:
        return "", False, "Symbol cannot be empty"
    
    # Basic sanitization
    sanitized = symbol.upper().strip()
    
    # Remove $ prefix if present
    if sanitized.startswith('$'):
        sanitized = sanitized[1:]
    
    # Use the unified validator
    extractor = UnifiedSymbolExtractor()
    if extractor._is_valid_symbol(sanitized):
        return sanitized, True, ""
    else:
        return sanitized, False, "Invalid symbol format or known non-ticker"
