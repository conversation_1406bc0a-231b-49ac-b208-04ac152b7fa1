#!/usr/bin/env python3
"""
Test script to verify bot commands work without running Discord bot
"""

import asyncio
import sys
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add src to path
sys.path.insert(0, 'src')

async def test_ask_pipeline():
    """Test the ASK pipeline directly"""
    print("🧪 Testing ASK Pipeline...")
    
    try:
        # Test tracer import first
        from src.bot.pipeline.commands.ask.observability import get_tracer
        tracer = get_tracer()
        print(f"🔍 Tracer type: {type(tracer)}")
        print(f"🔍 Tracer: {tracer}")

        if tracer is None:
            print("❌ Tracer is None - this will cause the pipeline to fail")
            return False

        from src.bot.pipeline.commands.ask.core.controller import AskPipelineController
        from src.shared.error_handling.logging import generate_correlation_id

        # Initialize pipeline
        pipeline_controller = AskPipelineController()
        print("✅ Pipeline controller initialized")
        
        # Test query
        test_query = "What is the current market sentiment?"
        correlation_id = generate_correlation_id()
        user_id = "test_user_123"
        
        print(f"📝 Testing query: '{test_query}'")
        
        # Execute pipeline
        result = await pipeline_controller.process(
            query=test_query,
            user_id=user_id,
            correlation_id=correlation_id
        )
        
        print(f"✅ Pipeline execution completed")
        print(f"📊 Result success: {result.success}")
        if result.success:
            print(f"📝 Response length: {len(result.response or '')} characters")
            print(f"🔗 Correlation ID: {result.correlation_id}")
            print(f"⚡ Execution time: {result.execution_time:.2f}s")
            if result.response:
                print(f"📄 Response preview: {result.response[:200]}...")
        else:
            print(f"❌ Error: {result.error}")
            
        return result.success
        
    except Exception as e:
        print(f"❌ Pipeline test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_health_checks():
    """Test health check functionality"""
    print("\n🏥 Testing Health Checks...")
    
    try:
        from src.shared.monitoring.observability import observability_manager
        
        # Run health checks
        health = await observability_manager.run_health_checks()
        print("✅ Health checks completed")
        
        for service, check in health.items():
            status = check.get('status', 'unknown')
            emoji = "🟢" if status == 'healthy' else "🔴"
            print(f"{emoji} {service}: {status}")
            
        return True
        
    except Exception as e:
        print(f"❌ Health check test failed: {e}")
        return False

async def test_cache_service():
    """Test cache service functionality"""
    print("\n💾 Testing Cache Service...")
    
    try:
        from src.shared.cache.cache_service import cache_service
        
        # Test cache operations
        test_key = "test_key_123"
        test_value = {"test": "data", "timestamp": "2025-01-01"}
        
        # Set value
        await cache_service.set(test_key, test_value, ttl=60)
        print("✅ Cache set operation completed")
        
        # Get value
        retrieved = await cache_service.get(test_key)
        print(f"✅ Cache get operation completed: {retrieved is not None}")
        
        # Get stats
        stats = await cache_service.get_stats()
        print(f"📊 Cache stats: {stats}")
        
        return True
        
    except Exception as e:
        print(f"❌ Cache test failed: {e}")
        return False

async def main():
    """Run all tests"""
    print("🚀 Starting Bot Command Tests\n")
    
    # Test environment variables
    print("🔧 Checking Environment Variables...")
    required_vars = ['DISCORD_BOT_TOKEN', 'OPENROUTER_API_KEY', 'SUPABASE_URL', 'SUPABASE_KEY']
    all_set = True
    
    for var in required_vars:
        value = os.getenv(var)
        status = "✅ SET" if value else "❌ NOT SET"
        print(f"  {var}: {status}")
        if not value:
            all_set = False
    
    if not all_set:
        print("❌ Some required environment variables are missing!")
        return False
    
    print("✅ All required environment variables are set\n")
    
    # Run tests
    tests = [
        ("ASK Pipeline", test_ask_pipeline),
        ("Health Checks", test_health_checks),
        ("Cache Service", test_cache_service),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n📋 Test Summary:")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Results: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! Bot commands should work correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Check the errors above.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
