"""
Unified AI Intent Detection System

This module provides a comprehensive intent detection system that replaces rigid command parsing
with intelligent natural language understanding and context-aware processing.
"""

import asyncio
import json
import time
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta

from src.shared.error_handling.logging import get_logger
from src.shared.ai_services.unified_ai_processor import UnifiedAIProcessor
from src.shared.ai_services.enhanced_intent_detector import enhanced_intent_detector, IntentType
from src.shared.utils.symbol_extraction import extract_symbols_from_query

logger = get_logger(__name__)

class UnifiedIntent(Enum):
    """Unified intent types for all bot interactions"""
    # Core Trading Intents
    PRICE_CHECK = "price_check"
    STOCK_ANALYSIS = "stock_analysis"
    TECHNICAL_ANALYSIS = "technical_analysis"
    FUNDAMENTAL_ANALYSIS = "fundamental_analysis"
    RECOMMENDATION = "recommendation"
    RISK_ASSESSMENT = "risk_assessment"
    
    # Portfolio & Management
    PORTFOLIO_ANALYSIS = "portfolio_analysis"
    WATCHLIST_MANAGEMENT = "watchlist_management"
    ALERT_MANAGEMENT = "alert_management"
    
    # Comparative & Research
    COMPARISON = "comparison"
    MARKET_NEWS = "market_news"
    SECTOR_ANALYSIS = "sector_analysis"
    
    # Advanced Trading
    OPTIONS_ANALYSIS = "options_analysis"
    ZONES_ANALYSIS = "zones_analysis"
    BATCH_ANALYSIS = "batch_analysis"
    
    # Utility & Support
    HELP_REQUEST = "help_request"
    STATUS_CHECK = "status_check"
    GENERAL_QUESTION = "general_question"
    GREETING = "greeting"
    
    # Meta Intents
    CONTEXT_CONTINUATION = "context_continuation"
    CLARIFICATION_REQUEST = "clarification_request"
    UNKNOWN = "unknown"

class ProcessingPipeline(Enum):
    """Available processing pipelines"""
    ASK_PIPELINE = "ask_pipeline"
    ANALYZE_PIPELINE = "analyze_pipeline"
    ZONES_PIPELINE = "zones_pipeline"
    BATCH_PIPELINE = "batch_pipeline"
    PORTFOLIO_PIPELINE = "portfolio_pipeline"
    WATCHLIST_PIPELINE = "watchlist_pipeline"
    ALERT_PIPELINE = "alert_pipeline"
    UTILITY_PIPELINE = "utility_pipeline"
    HELP_PIPELINE = "help_pipeline"

class ParameterType(Enum):
    """Types of parameters that can be extracted"""
    SYMBOL = "symbol"
    SYMBOLS_LIST = "symbols_list"
    TIMEFRAME = "timeframe"
    INDICATORS = "indicators"
    PRICE_TARGET = "price_target"
    PERCENTAGE = "percentage"
    DATE = "date"
    AMOUNT = "amount"
    TEXT = "text"
    BOOLEAN = "boolean"

@dataclass
class ExtractedParameter:
    """Extracted parameter from natural language"""
    name: str
    value: Any
    type: ParameterType
    confidence: float
    source_text: str
    alternatives: List[Any] = field(default_factory=list)

@dataclass
class ConversationContext:
    """Context for ongoing conversation"""
    user_id: str
    last_intent: Optional[UnifiedIntent] = None
    last_symbols: List[str] = field(default_factory=list)
    last_timeframe: Optional[str] = None
    last_parameters: Dict[str, Any] = field(default_factory=dict)
    conversation_history: List[Dict[str, Any]] = field(default_factory=list)
    session_start: datetime = field(default_factory=datetime.now)
    preferences: Dict[str, Any] = field(default_factory=dict)

@dataclass
class IntentAnalysisResult:
    """Comprehensive intent analysis result"""
    # Core Intent Information
    primary_intent: UnifiedIntent
    secondary_intents: List[UnifiedIntent]
    confidence: float
    
    # Processing Information
    recommended_pipeline: ProcessingPipeline
    processing_priority: int  # 1-10, higher = more urgent
    
    # Extracted Parameters
    parameters: Dict[str, ExtractedParameter]
    missing_parameters: List[str]
    
    # Context Information
    requires_context: bool
    context_continuation: bool
    references_previous: bool
    
    # Response Guidance
    response_style: str  # "concise", "detailed", "technical", etc.
    urgency_level: str   # "low", "medium", "high", "urgent"
    
    # Metadata
    processing_time: float
    method: str  # "ai", "hybrid", "fallback"
    reasoning: str
    suggestions: List[str] = field(default_factory=list)

class UnifiedIntentDetector:
    """
    Unified AI-powered intent detection system that replaces rigid command parsing
    with intelligent natural language understanding and context-aware processing.
    """
    
    def __init__(self):
        self.ai_processor = UnifiedAIProcessor()
        self.conversation_contexts: Dict[str, ConversationContext] = {}
        self.context_timeout = timedelta(hours=2)  # Context expires after 2 hours
        
        # Intent to pipeline mapping
        self.intent_pipeline_mapping = {
            UnifiedIntent.PRICE_CHECK: ProcessingPipeline.ASK_PIPELINE,
            UnifiedIntent.STOCK_ANALYSIS: ProcessingPipeline.ANALYZE_PIPELINE,
            UnifiedIntent.TECHNICAL_ANALYSIS: ProcessingPipeline.ANALYZE_PIPELINE,
            UnifiedIntent.FUNDAMENTAL_ANALYSIS: ProcessingPipeline.ANALYZE_PIPELINE,
            UnifiedIntent.RECOMMENDATION: ProcessingPipeline.ASK_PIPELINE,
            UnifiedIntent.RISK_ASSESSMENT: ProcessingPipeline.ANALYZE_PIPELINE,
            UnifiedIntent.PORTFOLIO_ANALYSIS: ProcessingPipeline.PORTFOLIO_PIPELINE,
            UnifiedIntent.WATCHLIST_MANAGEMENT: ProcessingPipeline.WATCHLIST_PIPELINE,
            UnifiedIntent.ALERT_MANAGEMENT: ProcessingPipeline.ALERT_PIPELINE,
            UnifiedIntent.COMPARISON: ProcessingPipeline.BATCH_PIPELINE,
            UnifiedIntent.ZONES_ANALYSIS: ProcessingPipeline.ZONES_PIPELINE,
            UnifiedIntent.BATCH_ANALYSIS: ProcessingPipeline.BATCH_PIPELINE,
            UnifiedIntent.OPTIONS_ANALYSIS: ProcessingPipeline.ANALYZE_PIPELINE,
            UnifiedIntent.HELP_REQUEST: ProcessingPipeline.HELP_PIPELINE,
            UnifiedIntent.STATUS_CHECK: ProcessingPipeline.UTILITY_PIPELINE,
            UnifiedIntent.GENERAL_QUESTION: ProcessingPipeline.ASK_PIPELINE,
        }
        
        # Parameter extraction patterns
        self.parameter_patterns = {
            ParameterType.SYMBOL: [
                r'\b([A-Z]{1,5})\b',  # Stock symbols
                r'\b(Apple|Microsoft|Tesla|Google|Amazon|Meta)\b'  # Company names
            ],
            ParameterType.TIMEFRAME: [
                r'\b(\d+[dwmy])\b',  # 1d, 1w, 1m, 1y
                r'\b(daily|weekly|monthly|yearly)\b',
                r'\b(today|yesterday|last week|last month)\b'
            ],
            ParameterType.INDICATORS: [
                r'\b(RSI|MACD|SMA|EMA|Bollinger|Stochastic)\b',
                r'\b(moving average|relative strength|momentum)\b'
            ]
        }
        
        logger.info("✅ Unified Intent Detection System initialized")
    
    async def analyze_intent(
        self, 
        text: str, 
        user_id: str,
        interaction_context: Optional[Dict[str, Any]] = None
    ) -> IntentAnalysisResult:
        """
        Analyze user intent with comprehensive natural language understanding
        
        Args:
            text: User input text
            user_id: Unique user identifier
            interaction_context: Additional context from Discord interaction
            
        Returns:
            IntentAnalysisResult with comprehensive analysis
        """
        start_time = time.time()
        
        try:
            # Get or create conversation context
            context = self._get_conversation_context(user_id)
            
            # Perform AI-powered intent analysis
            intent_analysis = await enhanced_intent_detector.analyze_intent(text, use_ai=True)
            
            # Map to unified intent system
            unified_intent = self._map_to_unified_intent(intent_analysis.primary_intent)
            secondary_intents = [self._map_to_unified_intent(intent) for intent in intent_analysis.secondary_intents]
            
            # Determine processing pipeline
            pipeline = self._determine_pipeline(unified_intent, intent_analysis)
            
            # Extract parameters using AI
            parameters = await self._extract_parameters_ai(text, unified_intent, context)
            
            # Analyze context requirements
            context_analysis = self._analyze_context_requirements(text, context, intent_analysis)
            
            # Update conversation context
            self._update_conversation_context(context, unified_intent, parameters, text)
            
            # Generate processing recommendations
            suggestions = self._generate_suggestions(unified_intent, parameters, context)
            
            processing_time = time.time() - start_time
            
            result = IntentAnalysisResult(
                primary_intent=unified_intent,
                secondary_intents=secondary_intents,
                confidence=intent_analysis.confidence,
                recommended_pipeline=pipeline,
                processing_priority=self._calculate_priority(unified_intent, intent_analysis.urgency_level),
                parameters=parameters,
                missing_parameters=self._identify_missing_parameters(unified_intent, parameters),
                requires_context=context_analysis['requires_context'],
                context_continuation=context_analysis['context_continuation'],
                references_previous=context_analysis['references_previous'],
                response_style=intent_analysis.response_style.value,
                urgency_level=intent_analysis.urgency_level.value,
                processing_time=processing_time,
                method="ai_unified",
                reasoning=intent_analysis.reasoning,
                suggestions=suggestions
            )
            
            logger.info(f"🎯 Intent analysis complete: {unified_intent.value} "
                       f"(confidence: {intent_analysis.confidence:.2f}, "
                       f"pipeline: {pipeline.value}, time: {processing_time:.3f}s)")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Intent analysis failed: {e}")
            return self._create_fallback_result(text, user_id, start_time)
    
    def _map_to_unified_intent(self, intent_type: IntentType) -> UnifiedIntent:
        """Map enhanced intent detector types to unified intent system"""
        mapping = {
            IntentType.PRICE_CHECK: UnifiedIntent.PRICE_CHECK,
            IntentType.TECHNICAL_ANALYSIS: UnifiedIntent.TECHNICAL_ANALYSIS,
            IntentType.FUNDAMENTAL_ANALYSIS: UnifiedIntent.FUNDAMENTAL_ANALYSIS,
            IntentType.RECOMMENDATION: UnifiedIntent.RECOMMENDATION,
            IntentType.COMPARISON: UnifiedIntent.COMPARISON,
            IntentType.PORTFOLIO_ADVICE: UnifiedIntent.PORTFOLIO_ANALYSIS,
            IntentType.RISK_ASSESSMENT: UnifiedIntent.RISK_ASSESSMENT,
            IntentType.MARKET_NEWS: UnifiedIntent.MARKET_NEWS,
            IntentType.OPTIONS_ANALYSIS: UnifiedIntent.OPTIONS_ANALYSIS,
            IntentType.GENERAL_QUESTION: UnifiedIntent.GENERAL_QUESTION,
            IntentType.HELP_REQUEST: UnifiedIntent.HELP_REQUEST,
            IntentType.GREETING: UnifiedIntent.GREETING,
            IntentType.UNKNOWN: UnifiedIntent.UNKNOWN
        }
        return mapping.get(intent_type, UnifiedIntent.UNKNOWN)
    
    def _get_conversation_context(self, user_id: str) -> ConversationContext:
        """Get or create conversation context for user"""
        # Clean up expired contexts
        current_time = datetime.now()
        expired_users = [
            uid for uid, ctx in self.conversation_contexts.items()
            if current_time - ctx.session_start > self.context_timeout
        ]
        for uid in expired_users:
            del self.conversation_contexts[uid]
        
        # Get or create context
        if user_id not in self.conversation_contexts:
            self.conversation_contexts[user_id] = ConversationContext(user_id=user_id)
        
        return self.conversation_contexts[user_id]

    def _determine_pipeline(self, intent: UnifiedIntent, intent_analysis) -> ProcessingPipeline:
        """Determine the best processing pipeline for the intent"""
        # Check for explicit pipeline hints in the analysis
        if hasattr(intent_analysis, 'entities') and intent_analysis.entities:
            entities = intent_analysis.entities

            # Multi-symbol analysis suggests batch pipeline
            if 'symbols' in entities and len(entities['symbols']) > 1:
                return ProcessingPipeline.BATCH_PIPELINE

            # Options-related entities suggest specialized handling
            if any(key in entities for key in ['options', 'calls', 'puts', 'strike_price']):
                return ProcessingPipeline.ANALYZE_PIPELINE

        # Use default mapping
        return self.intent_pipeline_mapping.get(intent, ProcessingPipeline.ASK_PIPELINE)

    async def _extract_parameters_ai(
        self,
        text: str,
        intent: UnifiedIntent,
        context: ConversationContext
    ) -> Dict[str, ExtractedParameter]:
        """Extract parameters using AI-powered analysis"""
        parameters = {}

        try:
            # Use AI to extract structured parameters
            extraction_prompt = f"""
            Extract trading-related parameters from this user query: "{text}"

            Intent: {intent.value}
            Previous symbols: {context.last_symbols}
            Previous timeframe: {context.last_timeframe}

            Return JSON with extracted parameters:
            {{
                "symbols": ["AAPL", "MSFT"],
                "timeframe": "1d",
                "indicators": ["RSI", "MACD"],
                "price_target": 150.00,
                "percentage": 5.0,
                "amount": 1000.00,
                "other_params": {{}}
            }}
            """

            ai_result = await self.ai_processor.process(
                extraction_prompt,
                mode="parameter_extraction",
                max_tokens=500
            )

            if ai_result and ai_result.content:
                try:
                    extracted_data = json.loads(ai_result.content)

                    # Convert to ExtractedParameter objects
                    for param_name, value in extracted_data.items():
                        if value and param_name != 'other_params':
                            param_type = self._determine_parameter_type(param_name, value)
                            parameters[param_name] = ExtractedParameter(
                                name=param_name,
                                value=value,
                                type=param_type,
                                confidence=0.8,  # AI extraction confidence
                                source_text=text
                            )
                except json.JSONDecodeError:
                    logger.warning("Failed to parse AI parameter extraction result")

        except Exception as e:
            logger.warning(f"AI parameter extraction failed: {e}")

        # Fallback to regex-based extraction
        if not parameters:
            parameters = self._extract_parameters_regex(text, intent, context)

        return parameters

    def _extract_parameters_regex(
        self,
        text: str,
        intent: UnifiedIntent,
        context: ConversationContext
    ) -> Dict[str, ExtractedParameter]:
        """Fallback regex-based parameter extraction"""
        parameters = {}

        # Extract symbols
        symbols = extract_symbols_from_query(text)
        if symbols:
            parameters['symbols'] = ExtractedParameter(
                name='symbols',
                value=symbols,
                type=ParameterType.SYMBOLS_LIST,
                confidence=0.9,
                source_text=text
            )
        elif context.last_symbols:
            # Use context symbols if none found
            parameters['symbols'] = ExtractedParameter(
                name='symbols',
                value=context.last_symbols,
                type=ParameterType.SYMBOLS_LIST,
                confidence=0.6,
                source_text="from context"
            )

        # Extract timeframe
        import re
        timeframe_patterns = [
            r'\b(\d+[dwmy])\b',
            r'\b(daily|weekly|monthly|yearly)\b',
            r'\b(today|yesterday|last\s+week|last\s+month)\b'
        ]

        for pattern in timeframe_patterns:
            match = re.search(pattern, text.lower())
            if match:
                parameters['timeframe'] = ExtractedParameter(
                    name='timeframe',
                    value=match.group(1),
                    type=ParameterType.TIMEFRAME,
                    confidence=0.8,
                    source_text=match.group(0)
                )
                break

        return parameters

    def _determine_parameter_type(self, param_name: str, value: Any) -> ParameterType:
        """Determine parameter type based on name and value"""
        type_mapping = {
            'symbols': ParameterType.SYMBOLS_LIST,
            'symbol': ParameterType.SYMBOL,
            'timeframe': ParameterType.TIMEFRAME,
            'indicators': ParameterType.INDICATORS,
            'price_target': ParameterType.PRICE_TARGET,
            'percentage': ParameterType.PERCENTAGE,
            'amount': ParameterType.AMOUNT
        }
        return type_mapping.get(param_name, ParameterType.TEXT)

    def _analyze_context_requirements(
        self,
        text: str,
        context: ConversationContext,
        intent_analysis
    ) -> Dict[str, bool]:
        """Analyze if the query requires or references context"""
        text_lower = text.lower()

        # Check for context continuation words
        continuation_words = ['also', 'too', 'and', 'what about', 'how about', 'compare']
        context_continuation = any(word in text_lower for word in continuation_words)

        # Check for references to previous queries
        reference_words = ['it', 'that', 'this', 'them', 'those', 'same']
        references_previous = any(word in text_lower for word in reference_words)

        # Check if query is incomplete without context
        requires_context = (
            context_continuation or
            references_previous or
            (len(text.split()) < 3 and not any(char in text for char in ['?', '!']))
        )

        return {
            'requires_context': requires_context,
            'context_continuation': context_continuation,
            'references_previous': references_previous
        }

    def _update_conversation_context(
        self,
        context: ConversationContext,
        intent: UnifiedIntent,
        parameters: Dict[str, ExtractedParameter],
        text: str
    ):
        """Update conversation context with new information"""
        context.last_intent = intent

        # Update symbols
        if 'symbols' in parameters:
            context.last_symbols = parameters['symbols'].value
        elif 'symbol' in parameters:
            context.last_symbols = [parameters['symbol'].value]

        # Update timeframe
        if 'timeframe' in parameters:
            context.last_timeframe = parameters['timeframe'].value

        # Update parameters
        context.last_parameters = {name: param.value for name, param in parameters.items()}

        # Add to conversation history
        context.conversation_history.append({
            'timestamp': datetime.now().isoformat(),
            'text': text,
            'intent': intent.value,
            'parameters': context.last_parameters
        })

        # Keep only last 10 interactions
        if len(context.conversation_history) > 10:
            context.conversation_history = context.conversation_history[-10:]

    def _calculate_priority(self, intent: UnifiedIntent, urgency_level) -> int:
        """Calculate processing priority (1-10, higher = more urgent)"""
        base_priority = {
            UnifiedIntent.PRICE_CHECK: 8,  # High priority for quick responses
            UnifiedIntent.STOCK_ANALYSIS: 7,
            UnifiedIntent.TECHNICAL_ANALYSIS: 6,
            UnifiedIntent.FUNDAMENTAL_ANALYSIS: 5,
            UnifiedIntent.RECOMMENDATION: 7,
            UnifiedIntent.RISK_ASSESSMENT: 6,
            UnifiedIntent.PORTFOLIO_ANALYSIS: 5,
            UnifiedIntent.WATCHLIST_MANAGEMENT: 4,
            UnifiedIntent.ALERT_MANAGEMENT: 6,
            UnifiedIntent.COMPARISON: 5,
            UnifiedIntent.ZONES_ANALYSIS: 5,
            UnifiedIntent.BATCH_ANALYSIS: 4,
            UnifiedIntent.OPTIONS_ANALYSIS: 6,
            UnifiedIntent.HELP_REQUEST: 3,
            UnifiedIntent.STATUS_CHECK: 2,
            UnifiedIntent.GENERAL_QUESTION: 3,
            UnifiedIntent.GREETING: 1,
        }.get(intent, 3)

        # Adjust based on urgency level
        urgency_modifier = {
            'urgent': 3,
            'high': 2,
            'medium': 0,
            'low': -1
        }.get(str(urgency_level).lower(), 0)

        return min(10, max(1, base_priority + urgency_modifier))

    def _identify_missing_parameters(
        self,
        intent: UnifiedIntent,
        parameters: Dict[str, ExtractedParameter]
    ) -> List[str]:
        """Identify missing required parameters for the intent"""
        required_params = {
            UnifiedIntent.PRICE_CHECK: ['symbols'],
            UnifiedIntent.STOCK_ANALYSIS: ['symbols'],
            UnifiedIntent.TECHNICAL_ANALYSIS: ['symbols'],
            UnifiedIntent.FUNDAMENTAL_ANALYSIS: ['symbols'],
            UnifiedIntent.COMPARISON: ['symbols'],  # Should have multiple symbols
            UnifiedIntent.ZONES_ANALYSIS: ['symbols'],
            UnifiedIntent.BATCH_ANALYSIS: ['symbols'],
            UnifiedIntent.OPTIONS_ANALYSIS: ['symbols'],
        }.get(intent, [])

        missing = []
        for param in required_params:
            if param not in parameters:
                missing.append(param)
            elif param == 'symbols' and intent == UnifiedIntent.COMPARISON:
                # Comparison needs multiple symbols
                symbols = parameters.get('symbols')
                if symbols and len(symbols.value) < 2:
                    missing.append('additional_symbols')

        return missing

    def _generate_suggestions(
        self,
        intent: UnifiedIntent,
        parameters: Dict[str, ExtractedParameter],
        context: ConversationContext
    ) -> List[str]:
        """Generate helpful suggestions for the user"""
        suggestions = []

        # Intent-specific suggestions
        if intent == UnifiedIntent.TECHNICAL_ANALYSIS:
            if 'indicators' not in parameters:
                suggestions.append("Consider specifying indicators like RSI, MACD, or moving averages")
            if 'timeframe' not in parameters:
                suggestions.append("You might want to specify a timeframe (e.g., 1d, 1w, 1m)")

        elif intent == UnifiedIntent.COMPARISON:
            symbols = parameters.get('symbols')
            if not symbols or len(symbols.value) < 2:
                suggestions.append("For comparison, please specify at least two stocks")

        elif intent == UnifiedIntent.PORTFOLIO_ANALYSIS:
            suggestions.append("I can help analyze your portfolio performance and risk metrics")

        elif intent == UnifiedIntent.ZONES_ANALYSIS:
            if 'timeframe' not in parameters:
                suggestions.append("Specify a timeframe for more accurate support/resistance zones")

        # Context-based suggestions
        if context.last_symbols and 'symbols' not in parameters:
            suggestions.append(f"Continue with {', '.join(context.last_symbols[:2])}?")

        return suggestions

    def _create_fallback_result(self, text: str, user_id: str, start_time: float) -> IntentAnalysisResult:
        """Create fallback result when AI analysis fails"""
        processing_time = time.time() - start_time

        return IntentAnalysisResult(
            primary_intent=UnifiedIntent.GENERAL_QUESTION,
            secondary_intents=[],
            confidence=0.5,
            recommended_pipeline=ProcessingPipeline.ASK_PIPELINE,
            processing_priority=3,
            parameters={},
            missing_parameters=[],
            requires_context=False,
            context_continuation=False,
            references_previous=False,
            response_style="detailed",
            urgency_level="medium",
            processing_time=processing_time,
            method="fallback",
            reasoning="AI analysis failed, using fallback classification",
            suggestions=["Please try rephrasing your question for better understanding"]
        )

# Global instance for easy access
unified_intent_detector = UnifiedIntentDetector()

# Convenience functions
async def analyze_user_intent(text: str, user_id: str, context: Optional[Dict[str, Any]] = None) -> IntentAnalysisResult:
    """Analyze user intent using the unified system"""
    return await unified_intent_detector.analyze_intent(text, user_id, context)

async def get_processing_pipeline(text: str, user_id: str) -> ProcessingPipeline:
    """Get recommended processing pipeline for user input"""
    result = await unified_intent_detector.analyze_intent(text, user_id)
    return result.recommended_pipeline
