"""
Context-Aware Processing System

This module provides context-aware processing that maintains conversation state
and understands user intent across multiple interactions, enabling natural
conversational flow with the trading bot.
"""

import asyncio
import json
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timed<PERSON><PERSON>
from enum import Enum

from src.shared.error_handling.logging import get_logger
from src.bot.ai.unified_intent_system import (
    UnifiedIntentDetector, IntentAnalysisResult, ConversationContext,
    UnifiedIntent, ProcessingPipeline, ExtractedParameter
)

logger = get_logger(__name__)

class ContextType(Enum):
    """Types of context that can be maintained"""
    CONVERSATION = "conversation"
    SESSION = "session"
    USER_PREFERENCES = "user_preferences"
    MARKET_STATE = "market_state"
    ANALYSIS_HISTORY = "analysis_history"

class ContextPriority(Enum):
    """Priority levels for context information"""
    CRITICAL = "critical"      # Must be preserved (user preferences, active positions)
    HIGH = "high"             # Important for continuity (recent symbols, timeframes)
    MEDIUM = "medium"         # Helpful for UX (conversation flow, suggestions)
    LOW = "low"              # Nice to have (general history, patterns)

@dataclass
class ContextItem:
    """Individual context item with metadata"""
    key: str
    value: Any
    context_type: ContextType
    priority: ContextPriority
    created_at: datetime
    last_accessed: datetime
    access_count: int = 0
    expires_at: Optional[datetime] = None

@dataclass
class ProcessingContext:
    """Enhanced processing context for AI operations"""
    # User Information
    user_id: str
    session_id: str
    
    # Current Request
    current_query: str
    current_intent: UnifiedIntent
    current_parameters: Dict[str, ExtractedParameter]
    
    # Historical Context
    conversation_context: ConversationContext
    recent_analyses: List[Dict[str, Any]]
    user_preferences: Dict[str, Any]
    
    # Market Context
    market_session: str  # "pre_market", "market_hours", "after_hours", "closed"
    market_conditions: Dict[str, Any]
    
    # Processing Metadata
    context_confidence: float
    context_sources: List[str]
    processing_hints: List[str]

class ContextAwareProcessor:
    """
    Context-aware processing system that maintains conversation state
    and provides intelligent context for AI operations.
    """
    
    def __init__(self):
        self.intent_detector = UnifiedIntentDetector()
        self.context_store: Dict[str, Dict[str, ContextItem]] = {}
        self.session_contexts: Dict[str, ProcessingContext] = {}
        
        # Context retention policies
        self.retention_policies = {
            ContextPriority.CRITICAL: timedelta(days=30),
            ContextPriority.HIGH: timedelta(days=7),
            ContextPriority.MEDIUM: timedelta(days=1),
            ContextPriority.LOW: timedelta(hours=6)
        }
        
        # Context relevance scoring
        self.relevance_weights = {
            'recency': 0.3,      # How recent is the context
            'frequency': 0.2,    # How often it's accessed
            'similarity': 0.3,   # How similar to current query
            'importance': 0.2    # Inherent importance level
        }
        
        logger.info("✅ Context-Aware Processor initialized")
    
    async def process_with_context(
        self, 
        query: str, 
        user_id: str,
        session_id: Optional[str] = None,
        interaction_context: Optional[Dict[str, Any]] = None
    ) -> Tuple[IntentAnalysisResult, ProcessingContext]:
        """
        Process user query with full context awareness
        
        Args:
            query: User input query
            user_id: Unique user identifier
            session_id: Session identifier (optional)
            interaction_context: Additional context from interaction
            
        Returns:
            Tuple of (IntentAnalysisResult, ProcessingContext)
        """
        session_id = session_id or f"{user_id}_{datetime.now().strftime('%Y%m%d_%H')}"
        
        try:
            # Clean up expired context
            await self._cleanup_expired_context()
            
            # Get or create processing context
            processing_context = await self._build_processing_context(
                query, user_id, session_id, interaction_context
            )
            
            # Enhance query with context
            enhanced_query = await self._enhance_query_with_context(query, processing_context)
            
            # Perform intent analysis with context
            intent_result = await self.intent_detector.analyze_intent(
                enhanced_query, user_id, interaction_context
            )
            
            # Update processing context with results
            processing_context.current_intent = intent_result.primary_intent
            processing_context.current_parameters = intent_result.parameters
            
            # Store context for future use
            await self._store_context(user_id, session_id, processing_context, intent_result)
            
            # Generate context-aware processing hints
            processing_context.processing_hints = self._generate_processing_hints(
                intent_result, processing_context
            )
            
            logger.info(f"🧠 Context-aware processing complete: {intent_result.primary_intent.value} "
                       f"(context confidence: {processing_context.context_confidence:.2f})")
            
            return intent_result, processing_context
            
        except Exception as e:
            logger.error(f"❌ Context-aware processing failed: {e}")
            # Fallback to basic intent analysis
            intent_result = await self.intent_detector.analyze_intent(query, user_id)
            basic_context = ProcessingContext(
                user_id=user_id,
                session_id=session_id,
                current_query=query,
                current_intent=intent_result.primary_intent,
                current_parameters=intent_result.parameters,
                conversation_context=self.intent_detector._get_conversation_context(user_id),
                recent_analyses=[],
                user_preferences={},
                market_session="unknown",
                market_conditions={},
                context_confidence=0.5,
                context_sources=["fallback"],
                processing_hints=[]
            )
            return intent_result, basic_context
    
    async def _build_processing_context(
        self, 
        query: str, 
        user_id: str, 
        session_id: str,
        interaction_context: Optional[Dict[str, Any]]
    ) -> ProcessingContext:
        """Build comprehensive processing context"""
        
        # Get conversation context
        conversation_context = self.intent_detector._get_conversation_context(user_id)
        
        # Get user context
        user_context = self._get_user_context(user_id)
        
        # Get recent analyses
        recent_analyses = self._get_recent_analyses(user_id, limit=5)
        
        # Determine market session
        market_session = self._get_market_session()
        
        # Calculate context confidence
        context_confidence = self._calculate_context_confidence(
            conversation_context, user_context, recent_analyses
        )
        
        # Identify context sources
        context_sources = []
        if conversation_context.conversation_history:
            context_sources.append("conversation_history")
        if user_context:
            context_sources.append("user_preferences")
        if recent_analyses:
            context_sources.append("analysis_history")
        if interaction_context:
            context_sources.append("interaction_context")
        
        return ProcessingContext(
            user_id=user_id,
            session_id=session_id,
            current_query=query,
            current_intent=UnifiedIntent.UNKNOWN,  # Will be set after analysis
            current_parameters={},  # Will be set after analysis
            conversation_context=conversation_context,
            recent_analyses=recent_analyses,
            user_preferences=user_context.get('preferences', {}),
            market_session=market_session,
            market_conditions=self._get_market_conditions(),
            context_confidence=context_confidence,
            context_sources=context_sources,
            processing_hints=[]
        )
    
    async def _enhance_query_with_context(
        self, 
        query: str, 
        context: ProcessingContext
    ) -> str:
        """Enhance query with relevant context information"""
        
        # If query is very short or uses pronouns, add context
        if len(query.split()) < 3 or any(word in query.lower() for word in ['it', 'that', 'this', 'them']):
            
            # Add recent symbols if available
            if context.conversation_context.last_symbols:
                symbols_context = f" (referring to {', '.join(context.conversation_context.last_symbols)})"
                query += symbols_context
            
            # Add recent intent context
            if context.conversation_context.last_intent:
                intent_context = f" (previous context: {context.conversation_context.last_intent.value})"
                query += intent_context
        
        return query
    
    def _get_user_context(self, user_id: str) -> Dict[str, Any]:
        """Get stored user context"""
        user_contexts = self.context_store.get(user_id, {})
        
        # Filter for user preference contexts
        preferences = {}
        for key, context_item in user_contexts.items():
            if context_item.context_type == ContextType.USER_PREFERENCES:
                preferences[key] = context_item.value
                context_item.last_accessed = datetime.now()
                context_item.access_count += 1
        
        return {'preferences': preferences}
    
    def _get_recent_analyses(self, user_id: str, limit: int = 5) -> List[Dict[str, Any]]:
        """Get recent analysis history for user"""
        user_contexts = self.context_store.get(user_id, {})
        
        analyses = []
        for key, context_item in user_contexts.items():
            if context_item.context_type == ContextType.ANALYSIS_HISTORY:
                analyses.append({
                    'timestamp': context_item.created_at,
                    'data': context_item.value
                })
        
        # Sort by timestamp and return most recent
        analyses.sort(key=lambda x: x['timestamp'], reverse=True)
        return analyses[:limit]
    
    def _get_market_session(self) -> str:
        """Determine current market session"""
        now = datetime.now()
        hour = now.hour
        
        # US market hours (EST/EDT)
        if 9 <= hour < 16:
            return "market_hours"
        elif 4 <= hour < 9:
            return "pre_market"
        elif 16 <= hour < 20:
            return "after_hours"
        else:
            return "closed"
    
    def _get_market_conditions(self) -> Dict[str, Any]:
        """Get current market conditions (placeholder)"""
        return {
            'volatility': 'medium',
            'trend': 'neutral',
            'volume': 'normal'
        }
    
    def _calculate_context_confidence(
        self, 
        conversation_context: ConversationContext,
        user_context: Dict[str, Any],
        recent_analyses: List[Dict[str, Any]]
    ) -> float:
        """Calculate confidence in available context"""
        confidence = 0.0
        
        # Conversation history contributes to confidence
        if conversation_context.conversation_history:
            confidence += 0.3 * min(1.0, len(conversation_context.conversation_history) / 5)
        
        # Recent symbols and parameters
        if conversation_context.last_symbols:
            confidence += 0.2
        if conversation_context.last_parameters:
            confidence += 0.2
        
        # User preferences
        if user_context.get('preferences'):
            confidence += 0.15
        
        # Recent analyses
        if recent_analyses:
            confidence += 0.15 * min(1.0, len(recent_analyses) / 3)
        
        return min(1.0, confidence)

    async def _cleanup_expired_context(self):
        """Clean up expired context items"""
        current_time = datetime.now()

        for user_id in list(self.context_store.keys()):
            user_contexts = self.context_store[user_id]
            expired_keys = []

            for key, context_item in user_contexts.items():
                # Check if item has expired
                if context_item.expires_at and current_time > context_item.expires_at:
                    expired_keys.append(key)
                    continue

                # Check retention policy
                retention_period = self.retention_policies.get(
                    context_item.priority,
                    timedelta(hours=1)
                )
                if current_time - context_item.created_at > retention_period:
                    expired_keys.append(key)

            # Remove expired items
            for key in expired_keys:
                del user_contexts[key]

            # Remove user if no contexts remain
            if not user_contexts:
                del self.context_store[user_id]

    async def _store_context(
        self,
        user_id: str,
        session_id: str,
        processing_context: ProcessingContext,
        intent_result: IntentAnalysisResult
    ):
        """Store context information for future use"""
        if user_id not in self.context_store:
            self.context_store[user_id] = {}

        user_contexts = self.context_store[user_id]
        current_time = datetime.now()

        # Store current analysis
        analysis_key = f"analysis_{current_time.strftime('%Y%m%d_%H%M%S')}"
        user_contexts[analysis_key] = ContextItem(
            key=analysis_key,
            value={
                'query': processing_context.current_query,
                'intent': intent_result.primary_intent.value,
                'parameters': {name: param.value for name, param in intent_result.parameters.items()},
                'confidence': intent_result.confidence,
                'pipeline': intent_result.recommended_pipeline.value
            },
            context_type=ContextType.ANALYSIS_HISTORY,
            priority=ContextPriority.MEDIUM,
            created_at=current_time,
            last_accessed=current_time,
            expires_at=current_time + timedelta(days=1)
        )

        # Store session information
        session_key = f"session_{session_id}"
        user_contexts[session_key] = ContextItem(
            key=session_key,
            value={
                'session_id': session_id,
                'start_time': current_time,
                'query_count': len(processing_context.conversation_context.conversation_history),
                'primary_intents': [intent_result.primary_intent.value]
            },
            context_type=ContextType.SESSION,
            priority=ContextPriority.HIGH,
            created_at=current_time,
            last_accessed=current_time,
            expires_at=current_time + timedelta(hours=6)
        )

        # Update user preferences based on usage patterns
        await self._update_user_preferences(user_id, intent_result, processing_context)

    async def _update_user_preferences(
        self,
        user_id: str,
        intent_result: IntentAnalysisResult,
        processing_context: ProcessingContext
    ):
        """Update user preferences based on usage patterns"""
        user_contexts = self.context_store[user_id]

        # Track preferred response style
        style_key = "preferred_response_style"
        if style_key not in user_contexts:
            user_contexts[style_key] = ContextItem(
                key=style_key,
                value=intent_result.response_style,
                context_type=ContextType.USER_PREFERENCES,
                priority=ContextPriority.CRITICAL,
                created_at=datetime.now(),
                last_accessed=datetime.now()
            )
        else:
            # Update based on frequency
            user_contexts[style_key].value = intent_result.response_style
            user_contexts[style_key].last_accessed = datetime.now()

        # Track frequently used symbols
        if intent_result.parameters and 'symbols' in intent_result.parameters:
            symbols = intent_result.parameters['symbols'].value
            for symbol in symbols:
                symbol_key = f"frequent_symbol_{symbol}"
                if symbol_key not in user_contexts:
                    user_contexts[symbol_key] = ContextItem(
                        key=symbol_key,
                        value={'symbol': symbol, 'count': 1},
                        context_type=ContextType.USER_PREFERENCES,
                        priority=ContextPriority.HIGH,
                        created_at=datetime.now(),
                        last_accessed=datetime.now()
                    )
                else:
                    user_contexts[symbol_key].value['count'] += 1
                    user_contexts[symbol_key].last_accessed = datetime.now()

    def _generate_processing_hints(
        self,
        intent_result: IntentAnalysisResult,
        processing_context: ProcessingContext
    ) -> List[str]:
        """Generate processing hints based on context"""
        hints = []

        # Context-based hints
        if processing_context.context_confidence > 0.8:
            hints.append("high_context_confidence")

        if processing_context.conversation_context.last_symbols:
            hints.append("has_symbol_context")

        if processing_context.recent_analyses:
            hints.append("has_analysis_history")

        # Intent-specific hints
        if intent_result.primary_intent in [UnifiedIntent.TECHNICAL_ANALYSIS, UnifiedIntent.STOCK_ANALYSIS]:
            hints.append("fetch_market_data")
            hints.append("calculate_indicators")

        if intent_result.primary_intent == UnifiedIntent.COMPARISON:
            hints.append("multi_symbol_analysis")
            hints.append("comparative_metrics")

        # Market session hints
        if processing_context.market_session == "after_hours":
            hints.append("after_hours_context")
        elif processing_context.market_session == "pre_market":
            hints.append("pre_market_context")

        # Missing parameter hints
        if intent_result.missing_parameters:
            hints.append("request_missing_parameters")

        return hints

# Global instance for easy access
context_aware_processor = ContextAwareProcessor()

# Convenience functions
async def process_with_context(
    query: str,
    user_id: str,
    session_id: Optional[str] = None,
    context: Optional[Dict[str, Any]] = None
) -> Tuple[IntentAnalysisResult, ProcessingContext]:
    """Process query with full context awareness"""
    return await context_aware_processor.process_with_context(query, user_id, session_id, context)
