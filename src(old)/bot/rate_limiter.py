"""
Enhanced Rate Limiter for Discord Bot

Provides advanced rate limiting with per-user and per-guild limits,
burst protection, and tiered access controls.
"""

import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set, Tuple, Any
import logging
from enum import Enum

logger = logging.getLogger(__name__)

class RateLimitTier(Enum):
    """Rate limit tiers with different quotas"""
    FREE = "free"
    PAID = "paid"
    ADMIN = "admin"
    UNLIMITED = "unlimited"

class RateLimitConfig:
    """Configuration for different rate limit tiers"""
    
    # Default rate limits per tier (requests per time window)
    DEFAULT_LIMITS = {
        RateLimitTier.FREE: {"requests": 20, "window": 3600},  # 20 requests per hour
        RateLimitTier.PAID: {"requests": 100, "window": 3600},  # 100 requests per hour
        RateLimitTier.ADMIN: {"requests": 200, "window": 3600},  # 200 requests per hour
        RateLimitTier.UNLIMITED: {"requests": 9999, "window": 3600}  # Effectively unlimited
    }
    
    # Burst protection (max requests in short time window)
    BURST_LIMITS = {
        RateLimitTier.FREE: {"requests": 5, "window": 60},  # 5 requests per minute
        RateLimitTier.PAID: {"requests": 10, "window": 60},  # 10 requests per minute
        RateLimitTier.ADMIN: {"requests": 20, "window": 60},  # 20 requests per minute
        RateLimitTier.UNLIMITED: {"requests": 100, "window": 60}  # 100 requests per minute
    }
    
    # Guild-wide limits (shared across all users in a guild)
    GUILD_LIMITS = {
        RateLimitTier.FREE: {"requests": 50, "window": 3600},  # 50 requests per hour per guild
        RateLimitTier.PAID: {"requests": 200, "window": 3600},  # 200 requests per hour per guild
        RateLimitTier.ADMIN: {"requests": 500, "window": 3600},  # 500 requests per hour per guild
        RateLimitTier.UNLIMITED: {"requests": 9999, "window": 3600}  # Effectively unlimited
    }
    
    @classmethod
    def get_user_limit(cls, tier: RateLimitTier) -> Dict[str, int]:
        """Get rate limit config for a user tier"""
        return cls.DEFAULT_LIMITS.get(tier, cls.DEFAULT_LIMITS[RateLimitTier.FREE])
    
    @classmethod
    def get_burst_limit(cls, tier: RateLimitTier) -> Dict[str, int]:
        """Get burst limit config for a user tier"""
        return cls.BURST_LIMITS.get(tier, cls.BURST_LIMITS[RateLimitTier.FREE])
    
    @classmethod
    def get_guild_limit(cls, tier: RateLimitTier) -> Dict[str, int]:
        """Get guild-wide limit config for a tier"""
        return cls.GUILD_LIMITS.get(tier, cls.GUILD_LIMITS[RateLimitTier.FREE])


class EnhancedRateLimiter:
    """
    Enhanced rate limiter with per-user and per-guild limits,
    burst protection, and tiered access controls.
    """
    
    def __init__(self):
        # Track requests by user ID
        self.user_requests = {}
        
        # Track requests by guild ID
        self.guild_requests = {}
        
        # Track burst requests (short time window)
        self.user_burst_requests = {}
        
        # Track user tiers
        self.user_tiers = {}
        
        # Track guild tiers
        self.guild_tiers = {}
        
        # Track suspicious activity
        self.suspicious_activity = {}
        
        # Last cleanup time
        self.last_cleanup = datetime.now()
    
    def set_user_tier(self, user_id: str, tier: RateLimitTier) -> None:
        """Set rate limit tier for a user"""
        self.user_tiers[user_id] = tier
        logger.info(f"Set user {user_id} rate limit tier to {tier.value}")
    
    def set_guild_tier(self, guild_id: str, tier: RateLimitTier) -> None:
        """Set rate limit tier for a guild"""
        self.guild_tiers[guild_id] = tier
        logger.info(f"Set guild {guild_id} rate limit tier to {tier.value}")
    
    def get_user_tier(self, user_id: str) -> RateLimitTier:
        """Get rate limit tier for a user"""
        return self.user_tiers.get(user_id, RateLimitTier.FREE)
    
    def get_guild_tier(self, guild_id: str) -> RateLimitTier:
        """Get rate limit tier for a guild"""
        return self.guild_tiers.get(guild_id, RateLimitTier.FREE)
    
    def can_make_request(self, user_id: str, guild_id: Optional[str] = None) -> Tuple[bool, str]:
        """
        Check if a user can make a request
        
        Args:
            user_id: Discord user ID
            guild_id: Discord guild ID (optional)
            
        Returns:
            Tuple of (can_make_request, reason)
        """
        current_time = datetime.now()
        
        # Periodic cleanup of old requests
        self._cleanup_old_requests(current_time)
        
        # Get user tier
        user_tier = self.get_user_tier(user_id)
        
        # Check user-specific rate limit
        user_limit = RateLimitConfig.get_user_limit(user_tier)
        if not self._check_user_limit(user_id, user_limit, current_time):
            return False, f"Rate limit exceeded. Try again in {self.get_remaining_time(user_id)} seconds."
        
        # Check burst protection
        burst_limit = RateLimitConfig.get_burst_limit(user_tier)
        if not self._check_burst_limit(user_id, burst_limit, current_time):
            return False, f"Too many requests in a short time. Please wait a minute and try again."
        
        # Check guild-wide rate limit if applicable
        if guild_id:
            guild_tier = self.get_guild_tier(guild_id)
            guild_limit = RateLimitConfig.get_guild_limit(guild_tier)
            if not self._check_guild_limit(guild_id, guild_limit, current_time):
                return False, f"This server has reached its request limit. Try again later."
        
        return True, ""
    
    def _check_user_limit(self, user_id: str, limit: Dict[str, int], current_time: datetime) -> bool:
        """Check if user is within their rate limit"""
        if user_id not in self.user_requests:
            return True
        
        # Remove old requests outside the time window
        self.user_requests[user_id] = [
            t for t in self.user_requests[user_id]
            if (current_time - t).total_seconds() < limit["window"]
        ]
        
        # Check if under the limit
        return len(self.user_requests[user_id]) < limit["requests"]
    
    def _check_burst_limit(self, user_id: str, limit: Dict[str, int], current_time: datetime) -> bool:
        """Check if user is within burst limit"""
        if user_id not in self.user_burst_requests:
            return True
        
        # Remove old requests outside the burst window
        self.user_burst_requests[user_id] = [
            t for t in self.user_burst_requests[user_id]
            if (current_time - t).total_seconds() < limit["window"]
        ]
        
        # Check if under the burst limit
        return len(self.user_burst_requests[user_id]) < limit["requests"]
    
    def _check_guild_limit(self, guild_id: str, limit: Dict[str, int], current_time: datetime) -> bool:
        """Check if guild is within its rate limit"""
        if guild_id not in self.guild_requests:
            return True
        
        # Remove old requests outside the time window
        self.guild_requests[guild_id] = [
            t for t in self.guild_requests[guild_id]
            if (current_time - t).total_seconds() < limit["window"]
        ]
        
        # Check if under the limit
        return len(self.guild_requests[guild_id]) < limit["requests"]
    
    def record_request(self, user_id: str, guild_id: Optional[str] = None) -> None:
        """
        Record a user's request
        
        Args:
            user_id: Discord user ID
            guild_id: Discord guild ID (optional)
        """
        current_time = datetime.now()
        
        # Record user request
        if user_id not in self.user_requests:
            self.user_requests[user_id] = []
        self.user_requests[user_id].append(current_time)
        
        # Record burst request
        if user_id not in self.user_burst_requests:
            self.user_burst_requests[user_id] = []
        self.user_burst_requests[user_id].append(current_time)
        
        # Record guild request if applicable
        if guild_id:
            if guild_id not in self.guild_requests:
                self.guild_requests[guild_id] = []
            self.guild_requests[guild_id].append(current_time)
        
        # Check for suspicious activity
        self._check_suspicious_activity(user_id, current_time)

    def record_user_query(self, user_id: str) -> None:
        """
        Record a user's query (backward compatibility method)

        Args:
            user_id: Discord user ID
        """
        self.record_request(user_id, None)

    def _check_suspicious_activity(self, user_id: str, current_time: datetime) -> None:
        """Check for suspicious activity patterns"""
        if user_id not in self.suspicious_activity:
            self.suspicious_activity[user_id] = {
                "failed_attempts": 0,
                "last_reset": current_time
            }
        
        # Reset counter if it's been a while
        if (current_time - self.suspicious_activity[user_id]["last_reset"]).total_seconds() > 3600:
            self.suspicious_activity[user_id]["failed_attempts"] = 0
            self.suspicious_activity[user_id]["last_reset"] = current_time
        
        # Check for rapid successive requests
        burst_count = len([t for t in self.user_burst_requests.get(user_id, [])
                          if (current_time - t).total_seconds() < 10])
        
        if burst_count > 10:
            self.suspicious_activity[user_id]["failed_attempts"] += 1
            logger.warning(f"Suspicious activity detected for user {user_id}: "
                          f"{burst_count} requests in 10 seconds")
            
            # Take action if too many suspicious activities
            if self.suspicious_activity[user_id]["failed_attempts"] >= 3:
                logger.error(f"Potential abuse detected for user {user_id}. "
                           f"Consider temporary ban.")
                # In a real implementation, you might want to temporarily ban the user
                # or implement additional security measures
    
    def get_remaining_time(self, user_id: str) -> int:
        """
        Get remaining time in seconds until user can make another request
        
        Args:
            user_id: Discord user ID
            
        Returns:
            Seconds until next request is allowed
        """
        if user_id not in self.user_requests or not self.user_requests[user_id]:
            return 0
        
        # Get user tier and limit
        user_tier = self.get_user_tier(user_id)
        user_limit = RateLimitConfig.get_user_limit(user_tier)
        
        current_time = datetime.now()
        
        # Clean up old requests
        self.user_requests[user_id] = [
            t for t in self.user_requests[user_id]
            if (current_time - t).total_seconds() < user_limit["window"]
        ]
        
        # If under the limit, no waiting needed
        if len(self.user_requests[user_id]) < user_limit["requests"]:
            return 0
        
        # Calculate when the oldest request will expire
        oldest_request = min(self.user_requests[user_id])
        expiry_time = oldest_request + timedelta(seconds=user_limit["window"])
        remaining_seconds = (expiry_time - current_time).total_seconds()
        
        return max(0, int(remaining_seconds))
    
    def get_guild_remaining_time(self, guild_id: str) -> int:
        """
        Get remaining time in seconds until guild can make another request
        
        Args:
            guild_id: Discord guild ID
            
        Returns:
            Seconds until next request is allowed
        """
        if guild_id not in self.guild_requests or not self.guild_requests[guild_id]:
            return 0
        
        # Get guild tier and limit
        guild_tier = self.get_guild_tier(guild_id)
        guild_limit = RateLimitConfig.get_guild_limit(guild_tier)
        
        current_time = datetime.now()
        
        # Clean up old requests
        self.guild_requests[guild_id] = [
            t for t in self.guild_requests[guild_id]
            if (current_time - t).total_seconds() < guild_limit["window"]
        ]
        
        # If under the limit, no waiting needed
        if len(self.guild_requests[guild_id]) < guild_limit["requests"]:
            return 0
        
        # Calculate when the oldest request will expire
        oldest_request = min(self.guild_requests[guild_id])
        expiry_time = oldest_request + timedelta(seconds=guild_limit["window"])
        remaining_seconds = (expiry_time - current_time).total_seconds()
        
        return max(0, int(remaining_seconds))
    
    def _cleanup_old_requests(self, current_time: datetime) -> None:
        """Periodically clean up old requests to prevent memory bloat"""
        # Only clean up every 10 minutes
        if (current_time - self.last_cleanup).total_seconds() < 600:
            return
        
        self.last_cleanup = current_time
        logger.info("Performing rate limiter cleanup")
        
        # Maximum age to keep (1 day)
        max_age = 86400
        
        # Clean up user requests
        for user_id in list(self.user_requests.keys()):
            self.user_requests[user_id] = [
                t for t in self.user_requests[user_id]
                if (current_time - t).total_seconds() < max_age
            ]
            if not self.user_requests[user_id]:
                del self.user_requests[user_id]
        
        # Clean up burst requests
        for user_id in list(self.user_burst_requests.keys()):
            self.user_burst_requests[user_id] = [
                t for t in self.user_burst_requests[user_id]
                if (current_time - t).total_seconds() < max_age
            ]
            if not self.user_burst_requests[user_id]:
                del self.user_burst_requests[user_id]
        
        # Clean up guild requests
        for guild_id in list(self.guild_requests.keys()):
            self.guild_requests[guild_id] = [
                t for t in self.guild_requests[guild_id]
                if (current_time - t).total_seconds() < max_age
            ]
            if not self.guild_requests[guild_id]:
                del self.guild_requests[guild_id]
        
        # Clean up suspicious activity
        for user_id in list(self.suspicious_activity.keys()):
            if (current_time - self.suspicious_activity[user_id]["last_reset"]).total_seconds() > max_age:
                del self.suspicious_activity[user_id]


# Global instance for convenience
rate_limiter = EnhancedRateLimiter()
