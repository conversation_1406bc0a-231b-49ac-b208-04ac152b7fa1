"""
Token Validator for Discord Bot

Provides secure token validation for paid tiers and premium features.
Uses JWT tokens with expiration and signature verification.
"""

import os
import time
import json
import logging
import hmac
import hashlib
import base64
from typing import Dict, Any, Optional, Tuple
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class TokenValidator:
    """
    Validates access tokens for premium features with secure JWT-like implementation.
    """
    
    def __init__(self):
        # Get secret key from environment or use a default for development
        self.secret_key = os.getenv('TOKEN_SECRET_KEY', 'development_secret_key')
        if self.secret_key == 'development_secret_key':
            logger.warning("Using development secret key. Set TOKEN_SECRET_KEY environment variable in production.")
        
        # Token cache to prevent excessive validation (user_id -> expiration_time)
        self.token_cache = {}
        
        # Last cleanup time
        self.last_cleanup = datetime.now()
    
    def validate_token(self, user_id: str, token: str) -> Tuple[bool, str, Optional[Dict[str, Any]]]:
        """
        Validate a user's access token
        
        Args:
            user_id: Discord user ID
            token: Access token to validate
            
        Returns:
            Tuple of (is_valid, reason, payload)
        """
        # Check cache first
        if user_id in self.token_cache and self.token_cache[user_id]["expiration"] > time.time():
            return True, "Token valid (cached)", self.token_cache[user_id]["payload"]
        
        # Clean up expired cache entries periodically
        self._cleanup_cache()
        
        try:
            # Split token into parts
            parts = token.split('.')
            if len(parts) != 3:
                return False, "Invalid token format", None
            
            header_b64, payload_b64, signature_b64 = parts
            
            # Decode header and payload
            try:
                header = json.loads(base64.urlsafe_b64decode(header_b64 + '=' * (4 - len(header_b64) % 4)).decode('utf-8'))
                payload = json.loads(base64.urlsafe_b64decode(payload_b64 + '=' * (4 - len(payload_b64) % 4)).decode('utf-8'))
            except Exception as e:
                return False, f"Invalid token encoding: {str(e)}", None
            
            # Verify token is for this user
            if str(payload.get('sub')) != str(user_id):
                return False, "Token subject mismatch", None
            
            # Check if token has expired
            if 'exp' in payload and payload['exp'] < time.time():
                return False, "Token has expired", None
            
            # Verify signature
            expected_signature = self._generate_signature(header_b64, payload_b64)
            actual_signature = base64.urlsafe_b64decode(signature_b64 + '=' * (4 - len(signature_b64) % 4))
            
            if not hmac.compare_digest(expected_signature, actual_signature):
                return False, "Invalid token signature", None
            
            # Cache valid token
            if 'exp' in payload:
                self.token_cache[user_id] = {
                    "expiration": payload['exp'],
                    "payload": payload
                }
            
            return True, "Token valid", payload
            
        except Exception as e:
            logger.error(f"Token validation error: {str(e)}")
            return False, f"Token validation error: {str(e)}", None
    
    def generate_token(self, user_id: str, tier: str, duration_days: int = 30) -> str:
        """
        Generate a new access token for a user
        
        Args:
            user_id: Discord user ID
            tier: Access tier (e.g., 'paid', 'premium')
            duration_days: Token validity in days
            
        Returns:
            JWT-like token string
        """
        # Create header
        header = {
            "alg": "HS256",
            "typ": "JWT"
        }
        
        # Create payload
        expiration = int(time.time() + duration_days * 86400)
        payload = {
            "sub": str(user_id),
            "tier": tier,
            "iat": int(time.time()),
            "exp": expiration
        }
        
        # Encode header and payload
        header_b64 = base64.urlsafe_b64encode(json.dumps(header).encode('utf-8')).decode('utf-8').rstrip('=')
        payload_b64 = base64.urlsafe_b64encode(json.dumps(payload).encode('utf-8')).decode('utf-8').rstrip('=')
        
        # Generate signature
        signature = self._generate_signature(header_b64, payload_b64)
        signature_b64 = base64.urlsafe_b64encode(signature).decode('utf-8').rstrip('=')
        
        # Combine parts
        token = f"{header_b64}.{payload_b64}.{signature_b64}"
        
        # Cache token
        self.token_cache[user_id] = {
            "expiration": expiration,
            "payload": payload
        }
        
        return token
    
    def _generate_signature(self, header_b64: str, payload_b64: str) -> bytes:
        """Generate HMAC signature for token"""
        message = f"{header_b64}.{payload_b64}".encode('utf-8')
        return hmac.new(
            self.secret_key.encode('utf-8'),
            message,
            hashlib.sha256
        ).digest()
    
    def _cleanup_cache(self) -> None:
        """Clean up expired cache entries"""
        current_time = datetime.now()
        
        # Only clean up every hour
        if (current_time - self.last_cleanup).total_seconds() < 3600:
            return
        
        self.last_cleanup = current_time
        current_timestamp = time.time()
        
        # Remove expired entries
        expired_users = [
            user_id for user_id, data in self.token_cache.items()
            if data["expiration"] < current_timestamp
        ]
        
        for user_id in expired_users:
            del self.token_cache[user_id]
        
        if expired_users:
            logger.info(f"Cleaned up {len(expired_users)} expired token cache entries")


# Global instance for convenience
token_validator = TokenValidator()
