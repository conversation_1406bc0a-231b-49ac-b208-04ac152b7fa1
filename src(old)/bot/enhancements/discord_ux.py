"""
Discord Bot UX Enhancements
Advanced user experience improvements for the trading bot
"""

import discord
import asyncio
import time
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class TypingIndicator:
    """Smart typing indicator management"""
    
    def __init__(self, channel: discord.TextChannel, duration: float = 5.0):
        self.channel = channel
        self.duration = duration
        self._task: Optional[asyncio.Task] = None
        self._active = False
    
    async def start(self):
        """Start typing indicator"""
        if self._active:
            return
        
        self._active = True
        self._task = asyncio.create_task(self._typing_loop())
    
    async def stop(self):
        """Stop typing indicator"""
        self._active = False
        if self._task:
            self._task.cancel()
            try:
                await self._task
            except asyncio.CancelledError:
                pass
    
    async def _typing_loop(self):
        """Maintain typing indicator"""
        try:
            while self._active:
                async with self.channel.typing():
                    await asyncio.sleep(min(self.duration, 8))  # Discord limit is ~10s
        except asyncio.CancelledError:
            pass

class ProgressTracker:
    """Visual progress tracking for long operations"""
    
    def __init__(self, interaction: discord.Interaction, title: str = "Processing..."):
        self.interaction = interaction
        self.title = title
        self.message: Optional[discord.Message] = None
        self.progress = 0
        self.total_steps = 100
        self.current_step = ""
        self.start_time = time.time()
    
    async def start(self, total_steps: int = 100):
        """Initialize progress tracking"""
        self.total_steps = total_steps
        embed = self._create_progress_embed()
        
        if self.interaction.response.is_done():
            self.message = await self.interaction.followup.send(embed=embed)
        else:
            await self.interaction.response.send_message(embed=embed)
            self.message = await self.interaction.original_response()
    
    async def update(self, progress: int, step_description: str = ""):
        """Update progress"""
        self.progress = min(progress, self.total_steps)
        self.current_step = step_description
        
        if self.message:
            embed = self._create_progress_embed()
            try:
                await self.message.edit(embed=embed)
            except discord.NotFound:
                pass  # Message was deleted
    
    async def complete(self, final_message: str = "✅ Complete!"):
        """Mark as complete"""
        self.progress = self.total_steps
        self.current_step = final_message
        
        if self.message:
            embed = self._create_progress_embed(completed=True)
            try:
                await self.message.edit(embed=embed)
            except discord.NotFound:
                pass
    
    def _create_progress_embed(self, completed: bool = False) -> discord.Embed:
        """Create progress embed"""
        percentage = (self.progress / self.total_steps) * 100
        bar_length = 20
        filled_length = int(bar_length * self.progress // self.total_steps)
        
        bar = "█" * filled_length + "░" * (bar_length - filled_length)
        
        color = discord.Color.green() if completed else discord.Color.blue()
        
        embed = discord.Embed(
            title=self.title,
            color=color,
            timestamp=datetime.utcnow()
        )
        
        embed.add_field(
            name="Progress",
            value=f"`{bar}` {percentage:.1f}%",
            inline=False
        )
        
        if self.current_step:
            embed.add_field(
                name="Current Step",
                value=self.current_step,
                inline=False
            )
        
        elapsed = time.time() - self.start_time
        embed.add_field(
            name="Elapsed Time",
            value=f"{elapsed:.1f}s",
            inline=True
        )
        
        if not completed and self.progress > 0:
            eta = (elapsed / self.progress) * (self.total_steps - self.progress)
            embed.add_field(
                name="ETA",
                value=f"{eta:.1f}s",
                inline=True
            )
        
        return embed

class InteractiveEmbeds:
    """Enhanced interactive embeds with reactions and buttons"""
    
    @staticmethod
    def create_analysis_embed(symbol: str, data: Dict[str, Any], user: discord.User) -> discord.Embed:
        """Create rich analysis embed with enhanced formatting"""
        
        # Determine color based on sentiment
        sentiment = data.get('sentiment', 'neutral').lower()
        color_map = {
            'bullish': discord.Color.green(),
            'bearish': discord.Color.red(),
            'neutral': discord.Color.blue()
        }
        color = color_map.get(sentiment, discord.Color.blue())
        
        embed = discord.Embed(
            title=f"📊 {symbol} Analysis",
            color=color,
            timestamp=datetime.utcnow()
        )
        
        # Price information
        current_price = data.get('current_price')
        if current_price:
            embed.add_field(
                name="💰 Current Price",
                value=f"${current_price:,.2f}",
                inline=True
            )
        
        # Price change
        change = data.get('price_change')
        change_percent = data.get('change_percent')
        if change is not None:
            change_emoji = "📈" if change >= 0 else "📉"
            change_text = f"{change_emoji} ${change:+,.2f}"
            if change_percent is not None:
                change_text += f" ({change_percent:+.2f}%)"
            
            embed.add_field(
                name="📊 24h Change",
                value=change_text,
                inline=True
            )
        
        # Sentiment
        sentiment_emoji = {
            'bullish': '🐂',
            'bearish': '🐻',
            'neutral': '⚖️'
        }
        embed.add_field(
            name="🎯 Sentiment",
            value=f"{sentiment_emoji.get(sentiment, '⚖️')} {sentiment.title()}",
            inline=True
        )
        
        # Technical indicators
        indicators = data.get('indicators', {})
        if indicators:
            indicator_text = []
            for name, value in indicators.items():
                if isinstance(value, (int, float)):
                    indicator_text.append(f"**{name}**: {value:.2f}")
                else:
                    indicator_text.append(f"**{name}**: {value}")
            
            if indicator_text:
                embed.add_field(
                    name="📈 Technical Indicators",
                    value="\n".join(indicator_text[:5]),  # Limit to 5
                    inline=False
                )
        
        # Analysis summary
        analysis = data.get('analysis', data.get('response', ''))
        if analysis:
            # Truncate if too long
            if len(analysis) > 1000:
                analysis = analysis[:997] + "..."
            
            embed.add_field(
                name="🔍 Analysis",
                value=analysis,
                inline=False
            )
        
        # Footer
        embed.set_footer(
            text=f"Requested by {user.display_name}",
            icon_url=user.display_avatar.url
        )
        
        return embed
    
    @staticmethod
    def create_error_embed(error_message: str, error_id: str = None) -> discord.Embed:
        """Create user-friendly error embed"""
        embed = discord.Embed(
            title="❌ Error",
            description=error_message,
            color=discord.Color.red(),
            timestamp=datetime.utcnow()
        )
        
        if error_id:
            embed.set_footer(text=f"Error ID: {error_id}")
        
        return embed
    
    @staticmethod
    def create_help_embed() -> discord.Embed:
        """Create comprehensive help embed"""
        embed = discord.Embed(
            title="🤖 Trading Bot Commands",
            description="Your AI-powered trading assistant",
            color=discord.Color.blue(),
            timestamp=datetime.utcnow()
        )
        
        commands = [
            ("📊 /analyze", "Get detailed technical analysis for a stock symbol"),
            ("❓ /ask", "Ask any trading or market-related question"),
            ("👀 /watchlist", "Manage your personal watchlists"),
            ("📈 /price", "Get current price and basic info for a symbol"),
            ("🔍 /search", "Search for stocks by name or sector"),
            ("⚙️ /settings", "Configure your bot preferences"),
            ("📊 /portfolio", "View portfolio analysis and insights"),
            ("🚨 /alerts", "Set up price and indicator alerts")
        ]
        
        for name, description in commands:
            embed.add_field(
                name=name,
                value=description,
                inline=False
            )
        
        embed.add_field(
            name="💡 Tips",
            value="• Use specific stock symbols (e.g., AAPL, TSLA)\n"
                  "• Ask natural language questions\n"
                  "• Set up watchlists for quick access\n"
                  "• Enable alerts for important price levels",
            inline=False
        )
        
        embed.set_footer(text="Need help? Ask me anything about trading!")
        
        return embed

class SmartNotifications:
    """Intelligent notification system"""
    
    def __init__(self):
        self.user_preferences: Dict[str, Dict[str, Any]] = {}
        self.notification_history: Dict[str, List[datetime]] = {}
    
    async def should_notify(self, user_id: str, notification_type: str) -> bool:
        """Check if user should receive notification based on preferences and history"""
        
        # Check user preferences
        prefs = self.user_preferences.get(user_id, {})
        if not prefs.get(f"notify_{notification_type}", True):
            return False
        
        # Check rate limiting
        history = self.notification_history.get(user_id, [])
        recent_notifications = [
            dt for dt in history 
            if datetime.now() - dt < timedelta(hours=1)
        ]
        
        max_per_hour = prefs.get("max_notifications_per_hour", 10)
        if len(recent_notifications) >= max_per_hour:
            return False
        
        return True
    
    async def send_notification(self, user: discord.User, notification_type: str, 
                             embed: discord.Embed) -> bool:
        """Send notification if allowed"""
        
        if not await self.should_notify(str(user.id), notification_type):
            return False
        
        try:
            await user.send(embed=embed)
            
            # Record notification
            user_id = str(user.id)
            if user_id not in self.notification_history:
                self.notification_history[user_id] = []
            
            self.notification_history[user_id].append(datetime.now())
            
            # Clean old history
            cutoff = datetime.now() - timedelta(days=7)
            self.notification_history[user_id] = [
                dt for dt in self.notification_history[user_id] if dt > cutoff
            ]
            
            return True
            
        except discord.Forbidden:
            logger.warning(f"Cannot send DM to user {user.id}")
            return False
        except Exception as e:
            logger.error(f"Error sending notification to user {user.id}: {e}")
            return False

class ResponseOptimizer:
    """Optimize response delivery and formatting"""
    
    @staticmethod
    async def send_chunked_response(interaction: discord.Interaction, content: str, 
                                  chunk_size: int = 2000) -> List[discord.Message]:
        """Send long content in chunks"""
        messages = []
        
        if len(content) <= chunk_size:
            if interaction.response.is_done():
                msg = await interaction.followup.send(content)
            else:
                await interaction.response.send_message(content)
                msg = await interaction.original_response()
            return [msg]
        
        # Split into chunks
        chunks = []
        current_chunk = ""
        
        for line in content.split('\n'):
            if len(current_chunk) + len(line) + 1 > chunk_size:
                if current_chunk:
                    chunks.append(current_chunk)
                    current_chunk = line
                else:
                    # Line is too long, force split
                    chunks.append(line[:chunk_size])
                    current_chunk = line[chunk_size:]
            else:
                if current_chunk:
                    current_chunk += '\n' + line
                else:
                    current_chunk = line
        
        if current_chunk:
            chunks.append(current_chunk)
        
        # Send chunks
        for i, chunk in enumerate(chunks):
            if i == 0:
                if interaction.response.is_done():
                    msg = await interaction.followup.send(chunk)
                else:
                    await interaction.response.send_message(chunk)
                    msg = await interaction.original_response()
            else:
                msg = await interaction.followup.send(chunk)
            
            messages.append(msg)
            
            # Small delay between chunks
            if i < len(chunks) - 1:
                await asyncio.sleep(0.5)
        
        return messages

# Global instances
smart_notifications = SmartNotifications()

async def enhance_interaction(interaction: discord.Interaction, 
                            operation_name: str = "Processing") -> ProgressTracker:
    """Create enhanced interaction with progress tracking"""
    progress = ProgressTracker(interaction, operation_name)
    await progress.start()
    return progress

def create_enhanced_embed(title: str, description: str = None, 
                         color: discord.Color = None) -> discord.Embed:
    """Create enhanced embed with consistent styling"""
    embed = discord.Embed(
        title=title,
        description=description,
        color=color or discord.Color.blue(),
        timestamp=datetime.utcnow()
    )
    
    return embed
