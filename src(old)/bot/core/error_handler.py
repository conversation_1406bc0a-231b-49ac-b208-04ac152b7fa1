"""
Global error handler for application commands.
"""

import discord
from discord.ext import commands
from discord import app_commands
import traceback
from src.shared.error_handling.logging import get_logger

logger = get_logger(__name__)

async def on_app_command_error(interaction: discord.Interaction, error: app_commands.AppCommandError):
    """Global handler for application command errors."""
    command_name = interaction.command.name if interaction.command else 'unknown command'
    
    # Log with maximum detail
    logger.error(f"🚨 APP COMMAND ERROR in /{command_name}")
    logger.error(f"Error type: {type(error)}")
    logger.error(f"Error message: {error}")
    
    # Get full traceback
    tb_str = ''.join(traceback.format_exception(type(error), error, error.__traceback__))
    logger.error(f"Full traceback for /{command_name}:\n{tb_str}")
    
    # Send user-friendly error message
    try:
        error_message = f"❌ An error occurred while processing the `/{command_name}` command. Please try again later."
        if interaction.response.is_done():
            await interaction.followup.send(error_message, ephemeral=True)
        else:
            await interaction.response.send_message(error_message, ephemeral=True)
    except Exception as e:
        logger.error(f"Failed to send error message: {e}")

def setup_error_handler(bot: commands.Bot):
    """Setup the global error handler."""
    bot.tree.error(on_app_command_error)
    logger.info("✅ Global error handler registered")
