"""
Update imports to use the monitored versions of components.

This script updates the imports in the bot modules to use the monitored
versions of the components.
"""

import os
import re

def update_file(filepath, replacements):
    """Update imports in a file"""
    if not os.path.exists(filepath):
        print(f"File not found: {filepath}")
        return False
    
    with open(filepath, 'r') as f:
        content = f.read()
    
    updated_content = content
    for old, new in replacements:
        updated_content = re.sub(old, new, updated_content)
    
    if updated_content != content:
        with open(filepath, 'w') as f:
            f.write(updated_content)
        return True
    
    return False

# Update imports in main bot file
bot_file = "src/bot/main.py"
bot_replacements = [
    (r'from src\.bot\.client import TradingBot', 'from src.bot.client_with_monitoring import MonitoredTradingBot as TradingBot'),
]
if update_file(bot_file, bot_replacements):
    print(f"Updated imports in {bot_file}")

# Update imports in pipeline modules
pipeline_file = "src/bot/pipeline/commands/ask/__init__.py"
pipeline_replacements = [
    (r'from \.executor import execute_ask_pipeline', 'from .executor_with_grading import execute_ask_pipeline'),
]
if update_file(pipeline_file, pipeline_replacements):
    print(f"Updated imports in {pipeline_file}")

# Update any other files that need to use the monitored versions
# ...

print("Import updates completed")
