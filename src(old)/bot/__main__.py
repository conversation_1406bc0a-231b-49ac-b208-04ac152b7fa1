"""
Main entry point for the TradingView Automation Bot
"""

import asyncio
import os
import sys
import logging
from typing import Optional

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.shared.error_handling.logging import get_logger, setup_logging
from src.shared.ai_services.unified_ai_processor import UnifiedAIProcessor as AIServiceWrapper
from src.shared.ai_services.timeout_manager import timeout_manager
from src.shared.ai_services.circuit_breaker import ai_circuit_breaker_manager
from src.shared.monitoring.performance_monitor import performance_monitor

# Setup logging first
setup_logging()

logger = get_logger(__name__)

async def initialize_services():
    """Initialize all required services"""
    logger.info("Initializing bot services...")
    
    try:
        # Initialize AI service wrapper
        ai_service = AIServiceWrapper()
        logger.info("✅ AI service wrapper initialized")
        
        # Initialize timeout manager
        logger.info("✅ Timeout manager initialized")
        
        # Initialize circuit breaker manager
        logger.info("✅ Circuit breaker manager initialized")
        
        # Initialize performance monitor
        logger.info("✅ Performance monitor initialized")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to initialize services: {e}")
        return False

async def main():
    """Main entry point"""
    logger.info("🚀 Starting TradingView Automation Bot...")
    
    # Initialize services
    services_initialized = await initialize_services()
    if not services_initialized:
        logger.error("❌ Failed to initialize required services")
        return 1
    
    try:
        # Import and create bot
        from .client import create_bot
        
        bot = create_bot()
        if not bot:
            logger.error("❌ Failed to create bot instance")
            return 1
            
        logger.info("✅ Bot instance created successfully")
        
        # Get token from environment
        token = os.getenv('DISCORD_BOT_TOKEN')
        if not token:
            logger.error("❌ DISCORD_BOT_TOKEN not set in environment variables")
            return 1
            
        logger.info("🔐 Discord token loaded successfully")
        
        # Start bot
        logger.info("🔄 Starting bot...")
        await bot.start_bot(token)
        
        return 0
        
    except KeyboardInterrupt:
        logger.info("🛑 Bot shutdown requested by user")
        return 0
    except Exception as e:
        logger.error(f"❌ Bot failed to start: {e}", exc_info=True)
        return 1
    finally:
        # Cleanup
        logger.info("🧹 Cleaning up resources...")
        await timeout_manager.cancel_all_operations()

if __name__ == "__main__":
    # Run the bot
    exit_code = asyncio.run(main())
    sys.exit(exit_code)