"""
Watchlist Alerts Module

Provides functionality to monitor watchlists and send alerts when stocks meet certain criteria.
Integrates with the scheduler to periodically check watchlists and trigger notifications.
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional, Set, Tuple
from datetime import datetime, timedelta
import json

from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.interval import IntervalTrigger

from src.shared.error_handling.logging import get_logger
from src.shared.watchlist.supabase_manager import SupabaseWatchlistManager
from src.shared.data_providers.aggregator import DataProviderAggregator
from src.shared.technical_analysis.signal_generator import SignalGenerator
from src.analysis.risk.calculators.volatility_calculator import VolatilityCalculator

# Configure logging
logger = get_logger(__name__)

# Alert types
class AlertType:
    """Types of alerts that can be triggered"""
    PRICE_CHANGE = "price_change"  # Significant price change
    VOLUME_SPIKE = "volume_spike"  # Unusual volume activity
    TECHNICAL_SIGNAL = "technical_signal"  # Technical indicator signal
    SUPPORT_RESISTANCE = "support_resistance"  # Price near support/resistance
    VOLATILITY = "volatility"  # Volatility change
    EARNINGS = "earnings"  # Earnings announcement
    NEWS = "news"  # News event


class AlertCondition:
    """Alert condition configuration"""
    def __init__(self, 
                 alert_type: str,
                 threshold: float,
                 direction: str = "both",
                 timeframe: str = "1d",
                 enabled: bool = True):
        self.alert_type = alert_type
        self.threshold = threshold
        self.direction = direction  # "up", "down", or "both"
        self.timeframe = timeframe
        self.enabled = enabled
        self.last_triggered = None


class WatchlistAlertManager:
    """
    Manages alerts for watchlists and integrates with the scheduler
    to periodically check for alert conditions.
    """
    
    def __init__(self, watchlist_manager: SupabaseWatchlistManager):
        """Initialize the watchlist alert manager"""
        self.watchlist_manager = watchlist_manager
        self.data_provider = DataProviderAggregator()
        self.signal_generator = SignalGenerator()
        self.volatility_calculator = VolatilityCalculator()
        
        # Default alert conditions
        self.default_alert_conditions = {
            AlertType.PRICE_CHANGE: AlertCondition(AlertType.PRICE_CHANGE, 5.0),  # 5% price change
            AlertType.VOLUME_SPIKE: AlertCondition(AlertType.VOLUME_SPIKE, 2.0),  # 2x average volume
            AlertType.TECHNICAL_SIGNAL: AlertCondition(AlertType.TECHNICAL_SIGNAL, 0.7),  # 70% signal strength
            AlertType.SUPPORT_RESISTANCE: AlertCondition(AlertType.SUPPORT_RESISTANCE, 2.0),  # 2% from level
            AlertType.VOLATILITY: AlertCondition(AlertType.VOLATILITY, 1.5),  # 1.5x normal volatility
        }
        
        # User alert preferences (user_id -> symbol -> alert_conditions)
        self.user_alert_preferences = {}
        
        # Alert history to prevent duplicate alerts (user_id -> symbol -> alert_type -> last_alert_time)
        self.alert_history = {}
        
        # Alert cooldown period (in hours)
        self.alert_cooldown = 12
        
        # Notification queue
        self.notification_queue = asyncio.Queue()
        
        # Scheduler
        self.scheduler = None
        
    async def initialize(self):
        """Initialize the alert manager and load user preferences"""
        try:
            # Load user alert preferences from database
            await self._load_user_preferences()
            
            # Start the notification processor
            asyncio.create_task(self._process_notifications())
            
            logger.info("Watchlist alert manager initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize watchlist alert manager: {e}")
    
    async def _load_user_preferences(self):
        """Load user alert preferences from database"""
        try:
            # Get all watchlists
            all_users = await self.watchlist_manager.get_all_users()
            
            for user_id in all_users:
                # Get user watchlists
                watchlists = await self.watchlist_manager.get_user_watchlists(user_id)
                
                # Initialize user preferences
                self.user_alert_preferences[user_id] = {}
                
                # Set default alert conditions for each symbol in watchlists
                for watchlist in watchlists:
                    for symbol_item in watchlist.symbols:
                        symbol = symbol_item.symbol
                        
                        # Check if custom alert settings exist in notes
                        custom_settings = {}
                        if symbol_item.notes and "{" in symbol_item.notes:
                            try:
                                # Try to parse JSON from notes
                                start_idx = symbol_item.notes.find("{")
                                end_idx = symbol_item.notes.rfind("}") + 1
                                if start_idx >= 0 and end_idx > start_idx:
                                    json_str = symbol_item.notes[start_idx:end_idx]
                                    custom_settings = json.loads(json_str)
                            except json.JSONDecodeError:
                                pass
                        
                        # Set alert conditions for this symbol
                        self.user_alert_preferences[user_id][symbol] = {}
                        
                        # Apply default conditions, overridden by custom settings
                        for alert_type, default_condition in self.default_alert_conditions.items():
                            condition = AlertCondition(
                                alert_type=alert_type,
                                threshold=custom_settings.get(f"{alert_type}_threshold", default_condition.threshold),
                                direction=custom_settings.get(f"{alert_type}_direction", default_condition.direction),
                                timeframe=custom_settings.get(f"{alert_type}_timeframe", default_condition.timeframe),
                                enabled=custom_settings.get(f"{alert_type}_enabled", True)
                            )
                            self.user_alert_preferences[user_id][symbol][alert_type] = condition
            
            logger.info(f"Loaded alert preferences for {len(self.user_alert_preferences)} users")
        except Exception as e:
            logger.error(f"Failed to load user alert preferences: {e}")
    
    def register_with_scheduler(self, scheduler: AsyncIOScheduler):
        """Register alert checking with the scheduler"""
        self.scheduler = scheduler
        
        # Add job to check watchlist alerts every 30 minutes
        scheduler.add_job(
            self.check_all_watchlists,
            trigger=IntervalTrigger(minutes=30),
            id='watchlist_alerts_job',
            replace_existing=True
        )
        
        logger.info("Watchlist alerts registered with scheduler")
    
    async def check_all_watchlists(self):
        """Check all watchlists for alert conditions"""
        logger.info("Checking all watchlists for alert conditions")
        
        try:
            # Get all users with watchlists
            for user_id, symbol_preferences in self.user_alert_preferences.items():
                # Check each symbol for this user
                for symbol, alert_conditions in symbol_preferences.items():
                    await self._check_symbol_alerts(user_id, symbol, alert_conditions)
        except Exception as e:
            logger.error(f"Error checking watchlists: {e}")
    
    async def _check_symbol_alerts(self, user_id: str, symbol: str, alert_conditions: Dict[str, AlertCondition]):
        """Check a single symbol for alert conditions"""
        try:
            # Get market data for the symbol
            market_data = await self.data_provider.get_ticker(symbol)
            
            if not market_data or 'error' in market_data:
                logger.warning(f"Failed to get market data for {symbol}")
                return
            
            # Get historical data for analysis
            historical_data = await self.data_provider.get_history(symbol, period="1mo", interval="1d")
            
            if not historical_data or 'error' in historical_data:
                logger.warning(f"Failed to get historical data for {symbol}")
                return
            
            # Check each alert condition
            triggered_alerts = []
            
            # Current price and previous close
            current_price = market_data.get('current_price', 0)
            previous_close = market_data.get('previous_close', current_price)
            
            # Check price change alert
            if AlertType.PRICE_CHANGE in alert_conditions:
                condition = alert_conditions[AlertType.PRICE_CHANGE]
                if condition.enabled:
                    price_change_pct = ((current_price - previous_close) / previous_close) * 100
                    
                    # Check if price change exceeds threshold in the specified direction
                    if (condition.direction == "both" and abs(price_change_pct) >= condition.threshold) or \
                       (condition.direction == "up" and price_change_pct >= condition.threshold) or \
                       (condition.direction == "down" and price_change_pct <= -condition.threshold):
                        
                        # Check cooldown
                        if self._check_alert_cooldown(user_id, symbol, AlertType.PRICE_CHANGE):
                            triggered_alerts.append({
                                "type": AlertType.PRICE_CHANGE,
                                "message": f"Price change alert: {symbol} has moved {price_change_pct:.2f}% {'up' if price_change_pct > 0 else 'down'}",
                                "data": {
                                    "symbol": symbol,
                                    "price_change": price_change_pct,
                                    "current_price": current_price,
                                    "previous_close": previous_close
                                }
                            })
            
            # Check volume spike alert
            if AlertType.VOLUME_SPIKE in alert_conditions:
                condition = alert_conditions[AlertType.VOLUME_SPIKE]
                if condition.enabled and 'volume' in market_data and 'average_volume' in market_data:
                    volume = market_data['volume']
                    avg_volume = market_data['average_volume']
                    
                    if avg_volume > 0 and (volume / avg_volume) >= condition.threshold:
                        # Check cooldown
                        if self._check_alert_cooldown(user_id, symbol, AlertType.VOLUME_SPIKE):
                            triggered_alerts.append({
                                "type": AlertType.VOLUME_SPIKE,
                                "message": f"Volume spike alert: {symbol} volume is {volume / avg_volume:.1f}x above average",
                                "data": {
                                    "symbol": symbol,
                                    "volume": volume,
                                    "average_volume": avg_volume,
                                    "volume_ratio": volume / avg_volume
                                }
                            })
            
            # Check technical signal alert
            if AlertType.TECHNICAL_SIGNAL in alert_conditions:
                condition = alert_conditions[AlertType.TECHNICAL_SIGNAL]
                if condition.enabled:
                    # Generate technical signals
                    signals = await self.signal_generator.generate_signals(symbol, historical_data)
                    
                    # Find strongest signal
                    strongest_signal = None
                    strongest_strength = 0
                    
                    for signal in signals:
                        if signal['strength'] > strongest_strength:
                            strongest_signal = signal
                            strongest_strength = signal['strength']
                    
                    if strongest_signal and strongest_strength >= condition.threshold:
                        # Check cooldown
                        if self._check_alert_cooldown(user_id, symbol, AlertType.TECHNICAL_SIGNAL):
                            triggered_alerts.append({
                                "type": AlertType.TECHNICAL_SIGNAL,
                                "message": f"Technical signal alert: {symbol} shows {strongest_signal['signal']} signal ({strongest_strength:.1f})",
                                "data": {
                                    "symbol": symbol,
                                    "signal": strongest_signal['signal'],
                                    "strength": strongest_strength,
                                    "indicators": strongest_signal['indicators']
                                }
                            })
            
            # Check support/resistance alert
            if AlertType.SUPPORT_RESISTANCE in alert_conditions:
                condition = alert_conditions[AlertType.SUPPORT_RESISTANCE]
                if condition.enabled:
                    # Get support/resistance levels
                    levels = market_data.get('support_resistance_levels', {})
                    support_levels = levels.get('support', [])
                    resistance_levels = levels.get('resistance', [])
                    
                    # Find closest level
                    closest_level = None
                    closest_distance_pct = float('inf')
                    level_type = None
                    
                    for level in support_levels:
                        distance_pct = abs((current_price - level) / current_price) * 100
                        if distance_pct < closest_distance_pct:
                            closest_distance_pct = distance_pct
                            closest_level = level
                            level_type = "support"
                    
                    for level in resistance_levels:
                        distance_pct = abs((current_price - level) / current_price) * 100
                        if distance_pct < closest_distance_pct:
                            closest_distance_pct = distance_pct
                            closest_level = level
                            level_type = "resistance"
                    
                    if closest_level and closest_distance_pct <= condition.threshold:
                        # Check cooldown
                        if self._check_alert_cooldown(user_id, symbol, AlertType.SUPPORT_RESISTANCE):
                            triggered_alerts.append({
                                "type": AlertType.SUPPORT_RESISTANCE,
                                "message": f"Support/Resistance alert: {symbol} is within {closest_distance_pct:.1f}% of {level_type} level (${closest_level:.2f})",
                                "data": {
                                    "symbol": symbol,
                                    "level": closest_level,
                                    "level_type": level_type,
                                    "distance_pct": closest_distance_pct
                                }
                            })
            
            # Check volatility alert
            if AlertType.VOLATILITY in alert_conditions:
                condition = alert_conditions[AlertType.VOLATILITY]
                if condition.enabled:
                    # Calculate current volatility
                    current_volatility = self.volatility_calculator.calculate_volatility(historical_data)
                    avg_volatility = self.volatility_calculator.calculate_average_volatility(historical_data)
                    
                    if avg_volatility > 0 and (current_volatility / avg_volatility) >= condition.threshold:
                        # Check cooldown
                        if self._check_alert_cooldown(user_id, symbol, AlertType.VOLATILITY):
                            triggered_alerts.append({
                                "type": AlertType.VOLATILITY,
                                "message": f"Volatility alert: {symbol} volatility is {current_volatility / avg_volatility:.1f}x above average",
                                "data": {
                                    "symbol": symbol,
                                    "current_volatility": current_volatility,
                                    "average_volatility": avg_volatility,
                                    "volatility_ratio": current_volatility / avg_volatility
                                }
                            })
            
            # Queue notifications for triggered alerts
            for alert in triggered_alerts:
                await self.notification_queue.put({
                    "user_id": user_id,
                    "alert": alert,
                    "timestamp": datetime.now()
                })
                
                # Update alert history
                self._update_alert_history(user_id, symbol, alert["type"])
                
        except Exception as e:
            logger.error(f"Error checking alerts for {symbol}: {e}")
    
    def _check_alert_cooldown(self, user_id: str, symbol: str, alert_type: str) -> bool:
        """Check if an alert is in cooldown period"""
        # Initialize history for this user if not exists
        if user_id not in self.alert_history:
            self.alert_history[user_id] = {}
        
        # Initialize history for this symbol if not exists
        if symbol not in self.alert_history[user_id]:
            self.alert_history[user_id][symbol] = {}
        
        # Check if alert type exists and is within cooldown period
        if alert_type in self.alert_history[user_id][symbol]:
            last_alert_time = self.alert_history[user_id][symbol][alert_type]
            cooldown_end = last_alert_time + timedelta(hours=self.alert_cooldown)
            
            if datetime.now() < cooldown_end:
                return False
        
        return True
    
    def _update_alert_history(self, user_id: str, symbol: str, alert_type: str):
        """Update alert history with current timestamp"""
        # Initialize history for this user if not exists
        if user_id not in self.alert_history:
            self.alert_history[user_id] = {}
        
        # Initialize history for this symbol if not exists
        if symbol not in self.alert_history[user_id]:
            self.alert_history[user_id][symbol] = {}
        
        # Update last alert time
        self.alert_history[user_id][symbol][alert_type] = datetime.now()
    
    async def _process_notifications(self):
        """Process notifications from the queue"""
        while True:
            try:
                # Get notification from queue
                notification = await self.notification_queue.get()
                
                # Process notification
                await self._send_notification(notification)
                
                # Mark task as done
                self.notification_queue.task_done()
            except Exception as e:
                logger.error(f"Error processing notification: {e}")
            
            # Sleep to prevent high CPU usage
            await asyncio.sleep(0.1)
    
    async def _send_notification(self, notification: Dict[str, Any]):
        """Send notification to user"""
        try:
            user_id = notification["user_id"]
            alert = notification["alert"]
            
            logger.info(f"Sending notification to user {user_id}: {alert['message']}")
            
            # In a real implementation, this would send the notification to the user
            # via Discord, email, or other channels
            
            # For now, just log it
            logger.info(f"ALERT: {alert['message']}")
            
            # Store notification in database for later retrieval
            await self._store_notification(notification)
        except Exception as e:
            logger.error(f"Error sending notification: {e}")
    
    async def _store_notification(self, notification: Dict[str, Any]):
        """Store notification in database"""
        try:
            # In a real implementation, this would store the notification in a database
            # For now, just log it
            logger.info(f"Storing notification: {notification}")
        except Exception as e:
            logger.error(f"Error storing notification: {e}")
    
    async def get_user_notifications(self, user_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent notifications for a user"""
        # In a real implementation, this would retrieve notifications from a database
        # For now, return an empty list
        return []
    
    async def update_alert_preferences(self, user_id: str, symbol: str, alert_type: str, 
                                      threshold: float = None, direction: str = None, 
                                      timeframe: str = None, enabled: bool = None):
        """Update alert preferences for a user"""
        try:
            # Initialize user preferences if not exists
            if user_id not in self.user_alert_preferences:
                self.user_alert_preferences[user_id] = {}
            
            # Initialize symbol preferences if not exists
            if symbol not in self.user_alert_preferences[user_id]:
                self.user_alert_preferences[user_id][symbol] = {}
            
            # Initialize alert condition if not exists
            if alert_type not in self.user_alert_preferences[user_id][symbol]:
                self.user_alert_preferences[user_id][symbol][alert_type] = AlertCondition(
                    alert_type=alert_type,
                    threshold=self.default_alert_conditions[alert_type].threshold,
                    direction=self.default_alert_conditions[alert_type].direction,
                    timeframe=self.default_alert_conditions[alert_type].timeframe,
                    enabled=True
                )
            
            # Update alert condition
            condition = self.user_alert_preferences[user_id][symbol][alert_type]
            
            if threshold is not None:
                condition.threshold = threshold
            
            if direction is not None:
                condition.direction = direction
            
            if timeframe is not None:
                condition.timeframe = timeframe
            
            if enabled is not None:
                condition.enabled = enabled
            
            # Save preferences to database
            await self._save_alert_preferences(user_id, symbol, alert_type, condition)
            
            logger.info(f"Updated alert preferences for user {user_id}, symbol {symbol}, alert type {alert_type}")
        except Exception as e:
            logger.error(f"Error updating alert preferences: {e}")
    
    async def _save_alert_preferences(self, user_id: str, symbol: str, alert_type: str, condition: AlertCondition):
        """Save alert preferences to database"""
        try:
            # Get watchlists for this user
            watchlists = await self.watchlist_manager.get_user_watchlists(user_id)
            
            # Find watchlist containing this symbol
            for watchlist in watchlists:
                for symbol_item in watchlist.symbols:
                    if symbol_item.symbol == symbol:
                        # Update notes with alert preferences
                        notes = symbol_item.notes or ""
                        
                        # Extract existing JSON if present
                        settings = {}
                        if "{" in notes:
                            try:
                                start_idx = notes.find("{")
                                end_idx = notes.rfind("}") + 1
                                if start_idx >= 0 and end_idx > start_idx:
                                    json_str = notes[start_idx:end_idx]
                                    settings = json.loads(json_str)
                            except json.JSONDecodeError:
                                pass
                        
                        # Update settings
                        settings[f"{alert_type}_threshold"] = condition.threshold
                        settings[f"{alert_type}_direction"] = condition.direction
                        settings[f"{alert_type}_timeframe"] = condition.timeframe
                        settings[f"{alert_type}_enabled"] = condition.enabled
                        
                        # Create new notes with updated settings
                        if "{" in notes:
                            new_notes = notes[:notes.find("{")] + json.dumps(settings) + notes[notes.rfind("}") + 1:]
                        else:
                            new_notes = notes + "\n" + json.dumps(settings)
                        
                        # Update symbol notes
                        await self.watchlist_manager.update_symbol_notes(watchlist.id, symbol, new_notes)
                        return
            
            logger.warning(f"Symbol {symbol} not found in any watchlist for user {user_id}")
        except Exception as e:
            logger.error(f"Error saving alert preferences: {e}")


# Singleton instance
watchlist_alert_manager = None

async def initialize_watchlist_alerts(watchlist_manager: SupabaseWatchlistManager) -> WatchlistAlertManager:
    """Initialize the watchlist alert manager"""
    global watchlist_alert_manager
    
    if watchlist_alert_manager is None:
        watchlist_alert_manager = WatchlistAlertManager(watchlist_manager)
        await watchlist_alert_manager.initialize()
    
    return watchlist_alert_manager

def register_with_scheduler(scheduler: AsyncIOScheduler):
    """Register watchlist alerts with the scheduler"""
    global watchlist_alert_manager
    
    if watchlist_alert_manager is not None:
        watchlist_alert_manager.register_with_scheduler(scheduler)
    else:
        logger.error("Watchlist alert manager not initialized")
