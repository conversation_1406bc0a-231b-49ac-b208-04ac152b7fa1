"""
Enhanced Discord Bot Client with Pipeline Monitoring

This module extends the TradingBot class with pipeline monitoring and grading.
"""

import time
import asyncio
from typing import Dict, Any, Optional, List, Union
import os
import logging
import traceback

from discord.ext import commands
import discord

from src.shared.monitoring import PipelineGrader
from src.core.monitoring_pkg.bot_monitor import SystemMonitor, response_metrics_tracker
from src.bot.pipeline.commands.ask.executor_with_grading import execute_ask_pipeline_with_grading

# Import the original TradingBot class
from src.bot.client import TradingBot as OriginalTradingBot

class MonitoredTradingBot(OriginalTradingBot):
    """
    Enhanced TradingBot with pipeline monitoring and grading.
    This class extends the original TradingBot with monitoring capabilities.
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.system_monitor = SystemMonitor()
        self.logger.info("Initialized MonitoredTradingBot with pipeline grading")
    
    async def process_command(self, ctx, command_name, handler_func, *args, **kwargs):
        """
        Process a command with monitoring and grading.
        
        Args:
            ctx: Discord context
            command_name: Name of the command
            handler_func: Command handler function
            *args, **kwargs: Arguments for the handler
            
        Returns:
            Command execution result
        """
        # Create a pipeline grader for this command
        grader = PipelineGrader(pipeline_name=f"command_{command_name}")
        
        # Add metadata
        grader.add_metadata("user_id", str(ctx.author.id))
        grader.add_metadata("command", command_name)
        
        # Start timing command execution
        grader.start_step("command_execution")
        start_time = time.time()
        
        try:
            # Execute the command
            result = await handler_func(ctx, *args, **kwargs)
            
            # Update metrics
            execution_time = time.time() - start_time
            response_metrics_tracker["total_requests"] += 1
            response_metrics_tracker["successful_requests"] += 1
            response_metrics_tracker["total_response_time"] += execution_time
            response_metrics_tracker["last_request_time"] = time.time()
            response_metrics_tracker["average_response_time"] = (
                response_metrics_tracker["total_response_time"] / 
                response_metrics_tracker["total_requests"]
            )
            
            # End step with success
            grader.end_step(
                success=True,
                performance_score=100.0 if execution_time < 1.0 else (5.0 / execution_time) * 100,
                reliability_score=100.0,
                metrics={"execution_time": execution_time}
            )
            
            return result
            
        except Exception as e:
            # Update error metrics
            execution_time = time.time() - start_time
            response_metrics_tracker["total_requests"] += 1
            response_metrics_tracker["failed_requests"] += 1
            response_metrics_tracker["total_response_time"] += execution_time
            response_metrics_tracker["last_request_time"] = time.time()
            response_metrics_tracker["average_response_time"] = (
                response_metrics_tracker["total_response_time"] / 
                response_metrics_tracker["total_requests"]
            )
            
            # End step with failure
            grader.end_step(
                success=False,
                error_message=str(e),
                performance_score=0.0,
                reliability_score=0.0,
                metrics={"execution_time": execution_time}
            )
            
            # Log the error
            self.logger.error(f"Error executing command {command_name}: {str(e)}")
            self.logger.error(traceback.format_exc())
            
            # Re-raise the exception
            raise
            
        finally:
            # Finalize the grade
            pipeline_grade = grader.finalize_grade()
            
            # Log the grade
            self.logger.info(
                f"Command {command_name} execution completed with grade {pipeline_grade.grade.value} "
                f"({pipeline_grade.overall_score:.1f}), "
                f"execution time: {pipeline_grade.execution_time:.2f}s"
            )
    
    async def analyze_command(self, ctx, *args, **kwargs):
        """Override analyze command to use monitoring"""
        return await self.process_command(ctx, "analyze", super().analyze_command, *args, **kwargs)
    
    async def ask_command(self, ctx, *args, **kwargs):
        """Override ask command to use monitoring"""
        return await self.process_command(ctx, "ask", super().ask_command, *args, **kwargs)
    
    async def watchlist_command(self, ctx, *args, **kwargs):
        """Override watchlist command to use monitoring"""
        return await self.process_command(ctx, "watchlist", super().watchlist_command, *args, **kwargs)

# Replace the original TradingBot with the monitored version
import src.bot.client
src.bot.client.TradingBot = MonitoredTradingBot
