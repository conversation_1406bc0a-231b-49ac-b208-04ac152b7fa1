"""
Main entry point for the refactored trading bot.
Enhanced with unified startup management and comprehensive error handling.
"""

import asyncio
import os
import sys
from typing import Dict
from datetime import datetime

from src.bot.core.bot import run_bot
from src.shared.error_handling.logging import get_logger, generate_correlation_id
from src.core.config_manager import get_config

logger = get_logger(__name__)

class BotStartupManager:
    """Unified bot startup manager with comprehensive error handling and service validation."""

    def __init__(self):
        self.startup_id = generate_correlation_id()
        self.config = get_config()
        self.startup_time = datetime.now()
        self.services_status: Dict[str, bool] = {}

    async def validate_environment(self) -> bool:
        """Validate required environment variables and configuration."""
        logger.info(f"� [{self.startup_id}] Validating environment...")

        required_vars = [
            'DISCORD_BOT_TOKEN',
            'OPENROUTER_API_KEY',
            'SUPABASE_URL',
            'SUPABASE_KEY'
        ]

        missing_vars = []
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)

        if missing_vars:
            logger.error(f"❌ [{self.startup_id}] Missing required environment variables: {missing_vars}")
            return False

        logger.info(f"✅ [{self.startup_id}] Environment validation passed")
        return True

    async def initialize_core_services(self) -> bool:
        """Initialize core services with proper error handling."""
        logger.info(f"🔄 [{self.startup_id}] Initializing core services...")

        try:
            # Initialize service manager
            from src.bot.core.services import ServiceManager
            service_manager = ServiceManager()
            await service_manager.initialize()

            # Check service health
            critical_services = ['database', 'ai_service', 'cache_service']
            for service_name in critical_services:
                service = service_manager.get_service(service_name)
                self.services_status[service_name] = service is not None

            # Determine if we can continue
            critical_failures = [name for name, status in self.services_status.items()
                               if not status and name in ['database', 'ai_service']]

            if critical_failures:
                logger.warning(f"⚠️ [{self.startup_id}] Critical services failed: {critical_failures}")
                logger.info(f"🔄 [{self.startup_id}] Attempting to continue with degraded functionality...")

            logger.info(f"✅ [{self.startup_id}] Core services initialized (some may be degraded)")
            return True

        except Exception as e:
            logger.error(f"❌ [{self.startup_id}] Core services initialization failed: {e}", exc_info=True)
            return False

    async def start_bot_with_recovery(self) -> int:
        """Start bot with comprehensive error recovery."""
        logger.info(f"🚀 [{self.startup_id}] Starting TradingView Automation Bot...")

        try:
            # Validate environment
            if not await self.validate_environment():
                return 1

            # Initialize services
            if not await self.initialize_core_services():
                logger.error(f"❌ [{self.startup_id}] Failed to initialize core services")
                return 1

            # Start the bot
            logger.info(f"🤖 [{self.startup_id}] Starting Discord bot...")
            await run_bot()

            return 0

        except KeyboardInterrupt:
            logger.info(f"🛑 [{self.startup_id}] Bot shutdown requested by user")
            return 0
        except Exception as e:
            logger.error(f"❌ [{self.startup_id}] Bot startup failed: {e}", exc_info=True)
            return 1
        finally:
            await self.cleanup()

    async def cleanup(self):
        """Cleanup resources on shutdown."""
        logger.info(f"🧹 [{self.startup_id}] Cleaning up resources...")

        try:
            # Import and cleanup services
            from src.bot.core.services import ServiceManager
            service_manager = ServiceManager()
            await service_manager.cleanup()

            # Cleanup AI services
            from src.shared.ai_services.timeout_manager import timeout_manager
            await timeout_manager.cancel_all_operations()

            logger.info(f"✅ [{self.startup_id}] Cleanup completed")

        except Exception as e:
            logger.error(f"❌ [{self.startup_id}] Cleanup failed: {e}", exc_info=True)

async def main():
    """Enhanced main entry point with unified startup management."""
    startup_manager = BotStartupManager()
    exit_code = await startup_manager.start_bot_with_recovery()
    sys.exit(exit_code)

if __name__ == "__main__":
    asyncio.run(main())
