"""
Enhanced Input Validator with User-Level Rate Limiting and Advanced Security
Provides comprehensive input validation, sanitization, and rate limiting for Discord bot commands.
"""

import re
import time
import logging
import hashlib
import urllib.parse
import unicodedata
import asyncio
from typing import Dict, Any, List, Tuple, Optional, Set
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import defaultdict, deque
from enum import Enum

logger = logging.getLogger(__name__)

class ValidationLevel(Enum):
    BASIC = "basic"
    STANDARD = "standard"
    STRICT = "strict"
    PARANOID = "paranoid"

class ThreatLevel(Enum):
    NONE = 0
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4

@dataclass
class ValidationResult:
    is_valid: bool
    sanitized_input: str
    threat_level: ThreatLevel
    issues: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    rate_limit_hit: bool = False
    user_risk_score: float = 0.0

@dataclass
class UserRateLimit:
    user_id: str
    command_counts: Dict[str, deque] = field(default_factory=lambda: defaultdict(deque))
    total_requests: deque = field(default_factory=deque)
    risk_score: float = 0.0
    last_violation: Optional[datetime] = None
    violation_count: int = 0

class EnhancedInputValidator:
    """Enhanced input validator with comprehensive security and rate limiting"""
    
    def __init__(self, validation_level: ValidationLevel = ValidationLevel.STANDARD):
        self.validation_level = validation_level
        self.user_limits: Dict[str, UserRateLimit] = {}
        
        # Rate limiting configuration
        self.rate_limits = {
            'ask': {'requests_per_minute': 10, 'requests_per_hour': 100},
            'analyze': {'requests_per_minute': 5, 'requests_per_hour': 50},
            'watchlist': {'requests_per_minute': 15, 'requests_per_hour': 200},
            'global': {'requests_per_minute': 20, 'requests_per_hour': 300}
        }
        
        # Enhanced security patterns
        self.security_patterns = {
            'sql_injection': [
                r'(\b(select|insert|update|delete|drop|alter|create|exec|union|where)\b.*\b(from|into|table|database|values)\b)',
                r'(\'\s*(or|and)\s*\'\s*=\s*\')',
                r'(\-\-|\#|\/\*|\*\/)',
                r'(\bxp_cmdshell\b|\bsp_executesql\b)'
            ],
            'prompt_injection': [
                r'(ignore\s+(previous|all)\s+instructions?)',
                r'(system\s+prompt|you\s+are\s+a|you\'re\s+a)',
                r'(act\s+as\s+if|pretend\s+to\s+be)',
                r'(override\s+your\s+instructions)',
                r'(forget\s+everything|disregard\s+previous)',
                r'(\[SYSTEM\]|\[ADMIN\]|\[ROOT\])'
            ],
            'command_injection': [
                r'(`|\$\(|\|\||&&|;|\n)',
                r'(curl\s+|wget\s+|nc\s+|netcat\s+)',
                r'(rm\s+\-rf|del\s+\/s)',
                r'(eval\s*\(|exec\s*\()'
            ],
            'xss_attempts': [
                r'(<script[^>]*>.*?</script>)',
                r'(javascript\s*:)',
                r'(on\w+\s*=\s*["\'][^"\']*["\'])',
                r'(<iframe[^>]*>.*?</iframe>)'
            ],
            'path_traversal': [
                r'(\.\.\/|\.\.\\)',
                r'(\/etc\/passwd|\/etc\/shadow)',
                r'(\.\.%2f|\.\.%5c)',
                r'(%2e%2e%2f|%2e%2e%5c)'
            ]
        }
        
        # SECURITY FIX: Enhanced sensitive information patterns
        self.sensitive_patterns = {
            'api_keys': r'\b[A-Za-z0-9\-_]{20,}\b',
            'passwords': r'\b(pass|pwd|password|secret|token|key|api[_-]?key|auth)(\s+(is|are)\s*)?[=:\s]+[^\s\n]{3,}\b',
            'emails': r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
            'credit_cards': r'\b(?:\d[ -]*?){13,16}\b',
            'ssn': r'\b\d{3}[-.]?\d{2}[-.]?\d{4}\b',
            'phone_numbers': r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b',
            'ip_addresses': r'\b(?:\d{1,3}\.){3}\d{1,3}\b',
            'urls_with_credentials': r'https?://[^:]+:[^@]+@[^\s]+',
            'aws_keys': r'\b(AKIA[0-9A-Z]{16}|aws_access_key_id\s*=\s*[^\s\n]+)\b',
            'bearer_tokens': r'\bbearer\s+[a-z0-9\-\._~\+/]+=*\b',
            'private_keys': r'\b(private[_-]?key|secret[_-]?key)\s*[=:]\s*[^\s\n]+\b',
        }
        
        # Symbol validation patterns
        self.symbol_patterns = {
            'stock': r'^[A-Z]{1,5}$',
            'crypto': r'^[A-Z]{2,10}$',
            'forex': r'^[A-Z]{6}$',
            'index': r'^[\^]?[A-Z0-9]{1,10}$'
        }

    def _decode_and_normalize_input(self, input_text: str) -> str:
        """SECURITY FIX: Decode and normalize input to prevent encoding bypasses"""
        if not input_text:
            return ""

        # URL decode multiple times
        decoded = input_text
        for _ in range(3):
            try:
                new_decoded = urllib.parse.unquote(decoded)
                if new_decoded == decoded:
                    break
                decoded = new_decoded
            except:
                break

        # Unicode normalization
        try:
            normalized = unicodedata.normalize('NFKC', decoded)
        except:
            normalized = decoded

        return normalized

    async def _detect_ai_security_threats(self, input_text: str) -> Tuple[ThreatLevel, List[str]]:
        """SECURITY FIX: AI-powered security threat detection for sophisticated attacks"""
        try:
            # Import here to avoid circular imports
            from src.shared.ai_services.ai_security_detector import ai_security_detector

            # Analyze with AI security detector
            analysis = await ai_security_detector.analyze_security_threat(input_text, use_ai=True)

            # Convert AI analysis to our threat system
            if analysis.severity.value >= 3:  # HIGH or CRITICAL
                threat_level = ThreatLevel.HIGH
            elif analysis.severity.value >= 2:  # MEDIUM
                threat_level = ThreatLevel.MEDIUM
            elif analysis.severity.value >= 1:  # LOW
                threat_level = ThreatLevel.LOW
            else:
                threat_level = ThreatLevel.NONE

            # Create issue description
            issues = []
            if analysis.threat_type.value != "safe":
                issues.append(f"AI detected {analysis.threat_type.value} (confidence: {analysis.confidence:.2f})")
                if analysis.indicators:
                    issues.append(f"Indicators: {', '.join(analysis.indicators[:3])}")  # Limit to first 3

            return threat_level, issues

        except Exception as e:
            logger.warning(f"AI security detection failed: {e}")
            return ThreatLevel.NONE, []

    async def validate_input(self,
                            input_text: str,
                            command: str,
                            user_id: str,
                            input_type: str = "query") -> ValidationResult:
        """
        Comprehensive input validation with rate limiting and security checks
        
        Args:
            input_text: The input to validate
            command: The command being executed
            user_id: Discord user ID
            input_type: Type of input (query, symbol, etc.)
        
        Returns:
            ValidationResult with validation status and details
        """
        
        # Check rate limits first
        rate_limit_result = self._check_rate_limits(user_id, command)
        if rate_limit_result['hit_limit']:
            return ValidationResult(
                is_valid=False,
                sanitized_input="",
                threat_level=ThreatLevel.MEDIUM,
                issues=[f"Rate limit exceeded: {rate_limit_result['message']}"],
                rate_limit_hit=True,
                user_risk_score=rate_limit_result['risk_score']
            )
        
        # Initialize result
        result = ValidationResult(
            is_valid=True,
            sanitized_input=input_text,
            threat_level=ThreatLevel.NONE,
            user_risk_score=rate_limit_result['risk_score']
        )
        
        # Basic sanitization
        sanitized = self._basic_sanitization(input_text)
        result.sanitized_input = sanitized
        
        # Security threat detection (traditional regex)
        threat_level, security_issues = self._detect_security_threats(sanitized)
        if threat_level.value > result.threat_level.value:
            result.threat_level = threat_level
        result.issues.extend(security_issues)

        # SECURITY FIX: AI-powered security threat detection for sophisticated attacks
        ai_threat_level, ai_security_issues = await self._detect_ai_security_threats(sanitized)
        if ai_threat_level.value > result.threat_level.value:
            result.threat_level = ai_threat_level
        result.issues.extend(ai_security_issues)

        # Sensitive information detection
        sensitive_issues = self._detect_sensitive_information(sanitized)
        if sensitive_issues:
            # SECURITY FIX: Correct logic - set HIGH threat if current level is lower than HIGH
            if result.threat_level.value < ThreatLevel.HIGH.value:
                result.threat_level = ThreatLevel.HIGH
            result.issues.extend(sensitive_issues)
        
        # Input type specific validation
        type_issues = self._validate_input_type(sanitized, input_type, command)
        result.issues.extend(type_issues)
        
        # Length and format validation
        format_issues = self._validate_format(sanitized, input_type)
        result.issues.extend(format_issues)
        
        # Update user risk score based on issues
        if result.issues:
            self._update_user_risk_score(user_id, len(result.issues), result.threat_level)
        
        # Determine if input is valid based on validation level
        result.is_valid = self._determine_validity(result)
        
        # Log security events
        if result.threat_level.value >= ThreatLevel.MEDIUM.value:
            self._log_security_event(user_id, command, input_text, result)
        
        return result
    
    def _check_rate_limits(self, user_id: str, command: str) -> Dict[str, Any]:
        """Check if user has exceeded rate limits"""
        current_time = datetime.now()
        
        # Get or create user rate limit tracker
        if user_id not in self.user_limits:
            self.user_limits[user_id] = UserRateLimit(user_id=user_id)
        
        user_limit = self.user_limits[user_id]
        
        # Clean old entries (older than 1 hour)
        cutoff_time = current_time - timedelta(hours=1)
        
        # Clean command-specific counts
        for cmd_queue in user_limit.command_counts.values():
            while cmd_queue and cmd_queue[0] < cutoff_time:
                cmd_queue.popleft()
        
        # Clean total requests
        while user_limit.total_requests and user_limit.total_requests[0] < cutoff_time:
            user_limit.total_requests.popleft()
        
        # Check command-specific limits
        command_limits = self.rate_limits.get(command, self.rate_limits['global'])
        command_queue = user_limit.command_counts[command]
        
        # Check per-minute limit
        minute_cutoff = current_time - timedelta(minutes=1)
        recent_requests = sum(1 for req_time in command_queue if req_time > minute_cutoff)
        
        if recent_requests >= command_limits['requests_per_minute']:
            user_limit.violation_count += 1
            user_limit.last_violation = current_time
            return {
                'hit_limit': True,
                'message': f"Too many {command} requests per minute ({recent_requests}/{command_limits['requests_per_minute']})",
                'risk_score': user_limit.risk_score
            }
        
        # Check per-hour limit
        if len(command_queue) >= command_limits['requests_per_hour']:
            user_limit.violation_count += 1
            user_limit.last_violation = current_time
            return {
                'hit_limit': True,
                'message': f"Too many {command} requests per hour ({len(command_queue)}/{command_limits['requests_per_hour']})",
                'risk_score': user_limit.risk_score
            }
        
        # Check global limits
        global_limits = self.rate_limits['global']
        global_recent = sum(1 for req_time in user_limit.total_requests if req_time > minute_cutoff)
        
        if global_recent >= global_limits['requests_per_minute']:
            user_limit.violation_count += 1
            user_limit.last_violation = current_time
            return {
                'hit_limit': True,
                'message': f"Too many total requests per minute ({global_recent}/{global_limits['requests_per_minute']})",
                'risk_score': user_limit.risk_score
            }
        
        # Record this request
        command_queue.append(current_time)
        user_limit.total_requests.append(current_time)
        
        return {
            'hit_limit': False,
            'message': 'Within limits',
            'risk_score': user_limit.risk_score
        }
    
    def _basic_sanitization(self, input_text: str) -> str:
        """Basic input sanitization"""
        if not input_text:
            return ""
        
        # Strip whitespace
        sanitized = input_text.strip()
        
        # Remove null bytes and control characters
        sanitized = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', sanitized)
        
        # HTML escape if needed
        if self.validation_level in [ValidationLevel.STRICT, ValidationLevel.PARANOID]:
            import html
            sanitized = html.escape(sanitized)
        
        return sanitized
    
    def _detect_security_threats(self, input_text: str) -> Tuple[ThreatLevel, List[str]]:
        """SECURITY FIX: Enhanced threat detection with bypass prevention"""
        issues = []
        max_threat = ThreatLevel.NONE

        # SECURITY FIX: Decode and normalize first to prevent bypasses
        normalized_input = self._decode_and_normalize_input(input_text)

        for threat_type, patterns in self.security_patterns.items():
            for pattern in patterns:
                if re.search(pattern, normalized_input, re.IGNORECASE):
                    threat_level = ThreatLevel.HIGH if threat_type in ['sql_injection', 'command_injection'] else ThreatLevel.MEDIUM
                    if threat_level.value > max_threat.value:
                        max_threat = threat_level
                    issues.append(f"Potential {threat_type.replace('_', ' ')} detected")
                    break

        return max_threat, issues

    def _detect_sensitive_information(self, input_text: str) -> List[str]:
        """SECURITY FIX: Enhanced sensitive information detection with bypass prevention"""
        issues = []

        # SECURITY FIX: Decode and normalize first to prevent bypasses
        normalized_input = self._decode_and_normalize_input(input_text)

        for info_type, pattern in self.sensitive_patterns.items():
            if re.search(pattern, normalized_input, re.IGNORECASE):
                issues.append(f"Potential {info_type.replace('_', ' ')} detected")

        return issues

    def _validate_input_type(self, input_text: str, input_type: str, command: str) -> List[str]:
        """Validate input based on its type"""
        issues = []

        if input_type == "symbol":
            # Validate stock symbol
            if not any(re.match(pattern, input_text.upper()) for pattern in self.symbol_patterns.values()):
                issues.append("Invalid symbol format")

            if len(input_text) > 10:
                issues.append("Symbol too long (max 10 characters)")

        elif input_type == "query":
            # Validate query length
            if len(input_text) > 500:
                issues.append("Query too long (max 500 characters)")

            if len(input_text) < 3:
                issues.append("Query too short (min 3 characters)")

            # Check for excessive special characters
            special_char_ratio = len(re.findall(r'[^a-zA-Z0-9\s]', input_text)) / len(input_text)
            if special_char_ratio > 0.3:
                issues.append("Too many special characters")

        elif input_type == "timeframe":
            valid_timeframes = ['1m', '5m', '15m', '30m', '1h', '4h', '1d', '1w', '1M']
            if input_text not in valid_timeframes:
                issues.append(f"Invalid timeframe. Valid options: {', '.join(valid_timeframes)}")

        return issues

    def _validate_format(self, input_text: str, input_type: str) -> List[str]:
        """Validate input format"""
        issues = []

        # Check for suspicious patterns based on validation level
        if self.validation_level in [ValidationLevel.STRICT, ValidationLevel.PARANOID]:
            # Check for repeated characters (potential spam)
            if re.search(r'(.)\1{10,}', input_text):
                issues.append("Excessive character repetition detected")

            # Check for excessive capitalization
            if input_type == "query":
                caps_ratio = len(re.findall(r'[A-Z]', input_text)) / len(input_text) if input_text else 0
                if caps_ratio > 0.7 and len(input_text) > 10:
                    issues.append("Excessive capitalization")

        return issues

    def _update_user_risk_score(self, user_id: str, issue_count: int, threat_level: ThreatLevel):
        """Update user risk score based on violations"""
        if user_id not in self.user_limits:
            self.user_limits[user_id] = UserRateLimit(user_id=user_id)

        user_limit = self.user_limits[user_id]

        # Increase risk score based on threat level and issue count
        risk_increase = threat_level.value * 0.1 + issue_count * 0.05
        user_limit.risk_score = min(1.0, user_limit.risk_score + risk_increase)

        # Decay risk score over time
        if user_limit.last_violation:
            time_since_violation = datetime.now() - user_limit.last_violation
            decay_factor = min(0.1, time_since_violation.total_seconds() / 3600)  # Decay over hours
            user_limit.risk_score = max(0.0, user_limit.risk_score - decay_factor)

    def _determine_validity(self, result: ValidationResult) -> bool:
        """Determine if input is valid based on validation level and issues"""
        if result.threat_level == ThreatLevel.CRITICAL:
            return False

        if self.validation_level == ValidationLevel.PARANOID:
            return result.threat_level == ThreatLevel.NONE and not result.issues

        elif self.validation_level == ValidationLevel.STRICT:
            return result.threat_level.value <= ThreatLevel.LOW.value

        elif self.validation_level == ValidationLevel.STANDARD:
            return result.threat_level.value <= ThreatLevel.MEDIUM.value

        else:  # BASIC
            return result.threat_level.value <= ThreatLevel.HIGH.value

    def _log_security_event(self, user_id: str, command: str, input_text: str, result: ValidationResult):
        """Log security events for monitoring"""
        logger.warning(
            f"Security event detected - User: {user_id}, Command: {command}, "
            f"Threat Level: {result.threat_level.name}, Issues: {result.issues}, "
            f"Input Hash: {hashlib.sha256(input_text.encode()).hexdigest()[:16]}"
        )

    def get_user_stats(self, user_id: str) -> Dict[str, Any]:
        """Get user statistics for monitoring"""
        if user_id not in self.user_limits:
            return {
                'risk_score': 0.0,
                'violation_count': 0,
                'last_violation': None,
                'recent_requests': 0
            }

        user_limit = self.user_limits[user_id]
        recent_cutoff = datetime.now() - timedelta(minutes=5)
        recent_requests = sum(1 for req_time in user_limit.total_requests if req_time > recent_cutoff)

        return {
            'risk_score': user_limit.risk_score,
            'violation_count': user_limit.violation_count,
            'last_violation': user_limit.last_violation,
            'recent_requests': recent_requests
        }

    def reset_user_limits(self, user_id: str):
        """Reset rate limits for a user (admin function)"""
        if user_id in self.user_limits:
            del self.user_limits[user_id]
            logger.info(f"Reset rate limits for user {user_id}")

    def get_system_stats(self) -> Dict[str, Any]:
        """Get system-wide statistics"""
        total_users = len(self.user_limits)
        high_risk_users = sum(1 for user in self.user_limits.values() if user.risk_score > 0.5)
        recent_violations = sum(1 for user in self.user_limits.values()
                              if user.last_violation and
                              (datetime.now() - user.last_violation).total_seconds() < 3600)

        return {
            'total_tracked_users': total_users,
            'high_risk_users': high_risk_users,
            'recent_violations': recent_violations,
            'validation_level': self.validation_level.value
        }

# Global validator instance
enhanced_validator = EnhancedInputValidator(ValidationLevel.STANDARD)

def validate_discord_input(input_text: str,
                          command: str,
                          user_id: str,
                          input_type: str = "query") -> ValidationResult:
    """
    Convenience function for validating Discord input

    Args:
        input_text: The input to validate
        command: The Discord command being executed
        user_id: Discord user ID
        input_type: Type of input (query, symbol, timeframe, etc.)

    Returns:
        ValidationResult with validation status and details
    """
    return enhanced_validator.validate_input(input_text, command, user_id, input_type)
