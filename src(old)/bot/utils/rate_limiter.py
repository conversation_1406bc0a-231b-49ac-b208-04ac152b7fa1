"""
Thread-safe Rate Limiter for Discord Bot
Provides rate limiting with proper thread safety using asyncio locks
"""

import asyncio
import time
from typing import Dict, List, Optional
import logging

logger = logging.getLogger(__name__)

class ThreadSafeRateLimiter:
    """Thread-safe rate limiter for API requests"""
    
    def __init__(self, max_requests: int = 50, time_window: int = 3600):
        """
        Initialize rate limiter
        
        Args:
            max_requests: Maximum number of requests allowed in time window
            time_window: Time window in seconds
        """
        self.max_requests = max_requests
        self.time_window = time_window
        self.user_requests: Dict[str, List[float]] = {}
        self.lock = asyncio.Lock()  # Lock for thread safety
    
    async def can_make_request(self, user_id: str) -> bool:
        """
        Check if user can make a request (thread-safe)
        
        Args:
            user_id: User identifier
            
        Returns:
            bool: True if request is allowed, False otherwise
        """
        async with self.lock:
            now = time.time()
            
            if user_id not in self.user_requests:
                return True
            
            # Clean old requests outside the time window
            self.user_requests[user_id] = [
                req_time for req_time in self.user_requests[user_id]
                if now - req_time < self.time_window
            ]
            
            return len(self.user_requests[user_id]) < self.max_requests
    
    async def record_request(self, user_id: str) -> None:
        """
        Record a user request (thread-safe)
        
        Args:
            user_id: User identifier
        """
        async with self.lock:
            now = time.time()
            
            if user_id not in self.user_requests:
                self.user_requests[user_id] = []
            
            self.user_requests[user_id].append(now)
            
            # Log if user is approaching rate limit
            request_count = len(self.user_requests[user_id])
            if request_count >= int(self.max_requests * 0.8):
                logger.warning(f"User {user_id} is approaching rate limit: {request_count}/{self.max_requests}")
    
    async def get_remaining_requests(self, user_id: str) -> int:
        """
        Get number of remaining requests for user (thread-safe)
        
        Args:
            user_id: User identifier
            
        Returns:
            int: Number of remaining requests
        """
        async with self.lock:
            now = time.time()
            
            if user_id not in self.user_requests:
                return self.max_requests
            
            # Clean old requests
            self.user_requests[user_id] = [
                req_time for req_time in self.user_requests[user_id]
                if now - req_time < self.time_window
            ]
            
            return max(0, self.max_requests - len(self.user_requests[user_id]))
    
    async def get_remaining_time(self, user_id: str) -> float:
        """
        Get remaining time until user can make another request (thread-safe)
        
        Args:
            user_id: User identifier
            
        Returns:
            float: Remaining time in seconds
        """
        async with self.lock:
            now = time.time()
            
            if user_id not in self.user_requests:
                return 0.0
            
            if not self.user_requests[user_id]:
                return 0.0
            
            # Clean old requests
            self.user_requests[user_id] = [
                req_time for req_time in self.user_requests[user_id]
                if now - req_time < self.time_window
            ]
            
            if len(self.user_requests[user_id]) < self.max_requests:
                return 0.0
            
            # Find oldest request and calculate time until it expires
            oldest_request = min(self.user_requests[user_id])
            return max(0.0, self.time_window - (now - oldest_request))
    
    async def reset_user(self, user_id: str) -> None:
        """
        Reset rate limit for a specific user (thread-safe)
        
        Args:
            user_id: User identifier
        """
        async with self.lock:
            if user_id in self.user_requests:
                del self.user_requests[user_id]
                logger.info(f"Rate limit reset for user {user_id}")
