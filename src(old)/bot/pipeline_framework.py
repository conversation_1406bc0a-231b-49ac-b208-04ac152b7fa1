from typing import List, Dict, Any, Callable, Coroutine
from enum import Enum, auto
import asyncio
import logging
from dataclasses import dataclass, field

logger = logging.getLogger(__name__)

class PipelineStage(Enum):
    """Enumeration of standard pipeline stages"""
    INPUT_VALIDATION = auto()
    DATA_RETRIEVAL = auto()
    DATA_PROCESSING = auto()
    ANALYSIS = auto()
    REASONING = auto()
    RESPONSE_GENERATION = auto()
    VERIFICATION = auto()
    LOGGING = auto()

@dataclass
class PipelineContext:
    """
    Shared context for pipeline execution
    Tracks state, intermediate results, and metadata
    """
    input_data: Any
    user_id: str
    command_name: str
    complexity_level: int = 1
    stages_executed: List[PipelineStage] = field(default_factory=list)
    results: Dict[PipelineStage, Any] = field(default_factory=dict)
    errors: List[Exception] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)

class PipelineStageHandler:
    """
    Abstract base class for pipeline stage handlers
    Provides a consistent interface for pipeline stages
    """
    
    @staticmethod
    async def execute(context: PipelineContext) -> PipelineContext:
        """
        Execute a pipeline stage
        
        Args:
            context (PipelineContext): Current pipeline context
        
        Returns:
            PipelineContext: Updated pipeline context
        """
        raise NotImplementedError("Subclasses must implement execute method")

class FlexiblePipeline:
    """
    Flexible, modular pipeline for processing bot commands
    Supports dynamic stage addition and complexity scaling
    """
    
    def __init__(
        self, 
        stages: List[PipelineStage] = None, 
        stage_handlers: Dict[PipelineStage, PipelineStageHandler] = None
    ):
        """
        Initialize the pipeline
        
        Args:
            stages (List[PipelineStage], optional): Predefined pipeline stages
            stage_handlers (Dict[PipelineStage, PipelineStageHandler], optional): Stage-specific handlers
        """
        self.stages = stages or list(PipelineStage)
        self.stage_handlers = stage_handlers or {}
    
    async def execute(
        self, 
        input_data: Any, 
        user_id: str, 
        command_name: str, 
        complexity_level: int = 1
    ) -> Dict[str, Any]:
        """
        Execute the pipeline with given input
        
        Args:
            input_data (Any): Input data for the pipeline
            user_id (str): User identifier
            command_name (str): Name of the command being executed
            complexity_level (int, optional): Processing complexity level
        
        Returns:
            Dict[str, Any]: Pipeline execution results
        """
        # Initialize context
        context = PipelineContext(
            input_data=input_data,
            user_id=user_id,
            command_name=command_name,
            complexity_level=complexity_level
        )
        
        try:
            # Execute stages dynamically based on complexity
            for stage in self._get_stages_for_complexity(complexity_level):
                handler = self.stage_handlers.get(stage)
                
                if handler:
                    try:
                        context = await handler.execute(context)
                        context.stages_executed.append(stage)
                    except Exception as e:
                        logger.error(f"Error in stage {stage}: {e}")
                        context.errors.append(e)
                        
                        # Optional: Implement fallback or continue based on error severity
                        if complexity_level > 3:
                            break
            
            # Final processing
            return self._process_results(context)
        
        except Exception as e:
            logger.error(f"Pipeline execution failed: {e}")
            return {
                "status": "error",
                "message": str(e),
                "stages_executed": context.stages_executed
            }
    
    def _get_stages_for_complexity(self, complexity_level: int) -> List[PipelineStage]:
        """
        Determine pipeline stages based on complexity level
        
        Args:
            complexity_level (int): Desired processing complexity
        
        Returns:
            List[PipelineStage]: Stages to execute
        """
        if complexity_level <= 1:
            return [
                PipelineStage.INPUT_VALIDATION,
                PipelineStage.DATA_RETRIEVAL,
                PipelineStage.RESPONSE_GENERATION
            ]
        elif complexity_level <= 3:
            return [
                PipelineStage.INPUT_VALIDATION,
                PipelineStage.DATA_RETRIEVAL,
                PipelineStage.DATA_PROCESSING,
                PipelineStage.ANALYSIS,
                PipelineStage.RESPONSE_GENERATION,
                PipelineStage.VERIFICATION
            ]
        else:
            return list(PipelineStage)
    
    def _process_results(self, context: PipelineContext) -> Dict[str, Any]:
        """
        Process and format pipeline results
        
        Args:
            context (PipelineContext): Final pipeline context
        
        Returns:
            Dict[str, Any]: Processed results
        """
        return {
            "status": "success" if not context.errors else "partial_success",
            "stages_executed": context.stages_executed,
            "results": context.results,
            "errors": [str(e) for e in context.errors] if context.errors else None,
            "metadata": context.metadata
        }
    
    def add_stage_handler(
        self, 
        stage: PipelineStage, 
        handler: PipelineStageHandler
    ):
        """
        Add or override a stage handler
        
        Args:
            stage (PipelineStage): Pipeline stage
            handler (PipelineStageHandler): Handler for the stage
        """
        self.stage_handlers[stage] = handler

def create_pipeline(
    stages: List[PipelineStage] = None, 
    stage_handlers: Dict[PipelineStage, PipelineStageHandler] = None
) -> FlexiblePipeline:
    """
    Factory method to create a flexible pipeline
    
    Args:
        stages (List[PipelineStage], optional): Predefined pipeline stages
        stage_handlers (Dict[PipelineStage, PipelineStageHandler], optional): Stage-specific handlers
    
    Returns:
        FlexiblePipeline: Configured pipeline instance
    """
    return FlexiblePipeline(stages, stage_handlers) 