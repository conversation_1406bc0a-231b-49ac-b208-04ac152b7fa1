"""
Request Visualization and Audit System

Automatically visualizes all Discord command requests and sends them to a developer channel for auditing.
"""

import discord
import asyncio
import time
import aiohttp
import json
from datetime import datetime
from typing import Dict, Any, Optional, List, Union, cast
from enum import Enum
import traceback
import logging
from discord.ext import commands

from discord.abc import Messageable
from discord.channel import TextChannel

from .rate_limiter import audit_rate_limiter

# Configure logging
logger = logging.getLogger(__name__)

class AuditLevel(Enum):
    """Audit logging levels"""
    DEBUG = "debug"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

class RequestVisualizer:
    """
    Visualizes Discord command requests and sends them to a developer channel or webhook
    for real-time monitoring and auditing
    """
    
    def __init__(self, bot: Optional[discord.Client] = None):
        self.bot = bot
        self.dev_channel_id: Optional[int] = None
        self.webhook_url: Optional[str] = None
        self.enabled = True
        self.log_to_console = True
        self.log_to_channel = True
        self.log_to_webhook = False
        self.include_user_data = True  # Whether to include user data in audit logs
        self.include_guild_data = True  # Whether to include guild data in audit logs
        self.max_content_length = 1000  # Maximum content length to include in visualizations
        self.visualized_commands = set()  # Track which commands are being visualized
        self._session: Optional[aiohttp.ClientSession] = None
        self._session_lock = asyncio.Lock()  # Lock for thread-safe session access
        
    # Rest of the file remains the same...
    # Only showing the important part that needs to be fixed

# Global instance
request_visualizer = RequestVisualizer()

def setup_request_visualizer(bot: commands.Bot, dev_channel_id: Optional[int] = None, webhook_url: Optional[str] = None):
    """Setup the request visualizer with the bot and developer channel or webhook"""
    global request_visualizer
    request_visualizer.bot = bot
    
    # Configure based on provided options
    log_to_channel = dev_channel_id is not None
    log_to_webhook = webhook_url is not None
    
    request_visualizer.configure(
        dev_channel_id=dev_channel_id,
        webhook_url=webhook_url,
        log_to_channel=log_to_channel,
        log_to_webhook=log_to_webhook
    )
    
    # Register default commands for visualization
    request_visualizer.register_commands(["ask", "analyze", "price", "watchlist"])
    
    if dev_channel_id:
        logger.info(f"Request visualizer configured with developer channel ID: {dev_channel_id}")
    if webhook_url:
        logger.info(f"Request visualizer configured with webhook URL")
    
    return request_visualizer
