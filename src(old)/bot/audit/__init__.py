"""
Audit module for the trading bot
Provides request visualization and audit logging
"""

from .request_visualizer import request_visualizer, setup_request_visualizer, AuditLevel
from .session_manager import get_session, close_session

# Apply patches to use the session manager
from .request_visualizer_patch import *

__all__ = ['request_visualizer', 'setup_request_visualizer', 'AuditLevel', 
           'get_session', 'close_session']
