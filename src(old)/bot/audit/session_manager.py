"""
Session Manager for HTTP Requests

Provides a centralized session manager for HTTP requests to avoid creating
multiple sessions and ensure proper cleanup.
"""

import asyncio
import aiohttp
import logging
from typing import Optional

logger = logging.getLogger(__name__)

class SessionManager:
    """
    Manages a shared aiohttp.ClientSession for HTTP requests.
    
    This ensures that we reuse the same session for all HTTP requests,
    which is more efficient and follows best practices.
    """
    
    def __init__(self):
        """Initialize the session manager."""
        self._session: Optional[aiohttp.ClientSession] = None
        self._lock = asyncio.Lock()
        self._ref_count = 0
    
    async def get_session(self) -> aiohttp.ClientSession:
        """
        Get or create an aiohttp.ClientSession.
        
        Returns:
            aiohttp.ClientSession: The shared client session
        """
        async with self._lock:
            if self._session is None or self._session.closed:
                self._session = aiohttp.ClientSession()
                logger.debug("Created new aiohttp.ClientSession")
            
            self._ref_count += 1
            return self._session
    
    async def release_session(self):
        """
        Release a reference to the session.
        
        When the reference count reaches zero, the session will be closed.
        """
        async with self._lock:
            self._ref_count -= 1
            if self._ref_count <= 0 and self._session and not self._session.closed:
                await self._session.close()
                self._session = None
                self._ref_count = 0
                logger.debug("Closed aiohttp.ClientSession")
    
    async def close(self):
        """
        Force close the session regardless of reference count.
        
        This should be called during application shutdown.
        """
        async with self._lock:
            if self._session and not self._session.closed:
                await self._session.close()
                self._session = None
                self._ref_count = 0
                logger.debug("Force closed aiohttp.ClientSession")

# Global session manager instance
session_manager = SessionManager()

async def get_session() -> aiohttp.ClientSession:
    """
    Get a shared aiohttp.ClientSession.
    
    Returns:
        aiohttp.ClientSession: The shared client session
    """
    return await session_manager.get_session()

async def close_session():
    """Close the shared aiohttp.ClientSession."""
    await session_manager.close()
