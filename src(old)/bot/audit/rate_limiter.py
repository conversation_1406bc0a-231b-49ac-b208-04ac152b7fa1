"""
Audit Rate Limiter

Provides rate limiting functionality for the audit system to handle high volumes of interactions.
This helps prevent excessive resource usage when there are many users.
"""

import time
import asyncio
import random
import json
from typing import Dict, Any, Optional, List, Set
from datetime import datetime, timedelta
import logging

import redis.asyncio as aioredis
from src.core.config_manager import get_config

logger = logging.getLogger(__name__)

class AuditRateLimiter:
    """
    Rate limiter for the audit system using Redis
    
    This class provides rate limiting functionality to prevent excessive resource usage
    when there are many users interacting with the bot. It uses Redis for storage,
    making it scalable across multiple instances.
    """
    
    def __init__(self, 
                max_events_per_minute: int = 60,
                max_events_per_user_minute: int = 10,
                max_events_per_guild_minute: int = 30,
                sampling_rate: float = 0.1,  # Sample 10% of events when rate limited
                redis_url: Optional[str] = None):
        """
        Initialize the rate limiter
        
        Args:
            max_events_per_minute: Maximum number of events to log per minute globally
            max_events_per_user_minute: Maximum number of events to log per user per minute
            max_events_per_guild_minute: Maximum number of events to log per guild per minute
            sampling_rate: Percentage of events to sample when rate limited (0.0-1.0)
            redis_url: Redis URL (defaults to config value)
        """
        self.max_events_per_minute = max_events_per_minute
        self.max_events_per_user_minute = max_events_per_user_minute
        self.max_events_per_guild_minute = max_events_per_guild_minute
        self.sampling_rate = sampling_rate
        
        # Redis configuration
        config = get_config()
        self.redis_url = redis_url or config.get('redis', 'url', 'redis://localhost:6379/0')
        self._redis_client = None
        
        # Redis key prefixes
        self.global_key = "audit_rate_limiter:global"
        self.user_key_prefix = "audit_rate_limiter:user:"
        self.guild_key_prefix = "audit_rate_limiter:guild:"
        self.sampled_users_key = "audit_rate_limiter:sampled_users"
        self.sampled_guilds_key = "audit_rate_limiter:sampled_guilds"
        
        # Lock for thread safety
        self.lock = asyncio.Lock()
        
    async def _get_redis_client(self):
        """Get or create Redis client"""
        if not self._redis_client:
            try:
                self._redis_client = await aioredis.from_url(
                    self.redis_url, 
                    encoding="utf-8", 
                    decode_responses=True
                )
            except Exception as e:
                logger.error(f"Failed to connect to Redis for rate limiter: {e}")
                # Fall back to in-memory tracking if Redis is unavailable
                self._redis_client = None
                raise
        return self._redis_client
        
    async def should_log_event(self, user_id: Optional[str] = None, guild_id: Optional[str] = None) -> bool:
        """
        Check if an event should be logged based on rate limits
        
        Args:
            user_id: The user ID associated with the event
            guild_id: The guild ID associated with the event
            
        Returns:
            True if the event should be logged, False otherwise
        """
        try:
            # Get Redis client
            redis = await self._get_redis_client()
            if not redis:
                # If Redis is unavailable, always allow logging
                return True
                
            async with self.lock:
                current_time = time.time()
                one_minute_ago = current_time - 60
                
                # Add current event to tracking with expiration
                pipe = redis.pipeline()
                
                # Add global event
                global_key = f"{self.global_key}:{int(current_time * 1000)}"
                pipe.set(global_key, "", ex=65)  # 65 seconds expiration (slightly more than 1 minute)
                
                # Add user event
                if user_id:
                    user_key = f"{self.user_key_prefix}{user_id}:{int(current_time * 1000)}"
                    pipe.set(user_key, "", ex=65)
                
                # Add guild event
                if guild_id:
                    guild_key = f"{self.guild_key_prefix}{guild_id}:{int(current_time * 1000)}"
                    pipe.set(guild_key, "", ex=65)
                
                # Execute pipeline
                await pipe.execute()
                
                # Count events in the last minute
                global_count = await self._count_events(self.global_key, one_minute_ago)
                user_count = 0
                guild_count = 0
                
                if user_id:
                    user_count = await self._count_events(f"{self.user_key_prefix}{user_id}", one_minute_ago)
                
                if guild_id:
                    guild_count = await self._count_events(f"{self.guild_key_prefix}{guild_id}", one_minute_ago)
                
                # Check rate limits
                global_rate_limited = global_count > self.max_events_per_minute
                user_rate_limited = user_id and user_count > self.max_events_per_user_minute
                guild_rate_limited = guild_id and guild_count > self.max_events_per_guild_minute
                
                # Apply rate limiting
                if global_rate_limited or user_rate_limited or guild_rate_limited:
                    # When rate limited, only log a sample of events
                    should_sample = await self._should_sample(user_id, guild_id)
                    
                    if not should_sample:
                        return False
                        
                    # Log that rate limiting is active
                    if global_rate_limited:
                        logger.warning(f"Global audit rate limit exceeded: {global_count} events/minute")
                    if user_rate_limited and user_id:
                        logger.warning(f"User audit rate limit exceeded for {user_id}: {user_count} events/minute")
                    if guild_rate_limited and guild_id:
                        logger.warning(f"Guild audit rate limit exceeded for {guild_id}: {guild_count} events/minute")
                        
                    return should_sample
                
                # Not rate limited
                return True
        except Exception as e:
            # If Redis fails, log the error and allow the event
            logger.error(f"Rate limiter error: {e}")
            return True
            
    async def _count_events(self, key_prefix: str, min_time: float) -> int:
        """Count events with the given prefix since the minimum time"""
        try:
            redis = await self._get_redis_client()
            if not redis:
                return 0
                
            # Use Redis scan to find all keys with the prefix
            cursor = 0
            count = 0
            min_time_ms = int(min_time * 1000)
            
            while True:
                cursor, keys = await redis.scan(cursor, match=f"{key_prefix}:*", count=100)
                
                # Count keys that are newer than min_time
                for key in keys:
                    try:
                        # Extract timestamp from key
                        timestamp_str = key.split(':')[-1]
                        timestamp_ms = int(timestamp_str)
                        
                        if timestamp_ms >= min_time_ms:
                            count += 1
                    except (ValueError, IndexError):
                        # Skip keys with invalid format
                        continue
                
                # Exit loop when scan is complete
                if cursor == 0:
                    break
            
            return count
        except Exception as e:
            logger.error(f"Error counting events: {e}")
            return 0
    
    # Redis-based implementation doesn't need explicit cleanup as we use TTL
    
    async def _should_sample(self, user_id: Optional[str], guild_id: Optional[str]) -> bool:
        """
        Determine if an event should be sampled when rate limited
        
        This ensures we get a representative sample of events even when rate limited.
        """
        try:
            redis = await self._get_redis_client()
            if not redis:
                return True
                
            # Always sample if no user or guild ID (system events)
            if not user_id and not guild_id:
                return True
                
            # Check if we've already sampled this user
            if user_id:
                is_sampled = await redis.sismember(self.sampled_users_key, user_id)
                if not is_sampled:
                    # Add to sampled users with 1-minute expiration
                    await redis.sadd(self.sampled_users_key, user_id)
                    await redis.expire(self.sampled_users_key, 60)
                    return True
                    
            # Check if we've already sampled this guild
            if guild_id:
                is_sampled = await redis.sismember(self.sampled_guilds_key, guild_id)
                if not is_sampled:
                    # Add to sampled guilds with 1-minute expiration
                    await redis.sadd(self.sampled_guilds_key, guild_id)
                    await redis.expire(self.sampled_guilds_key, 60)
                    return True
                    
            # Otherwise, apply sampling rate
            return random.random() < self.sampling_rate
            
        except Exception as e:
            logger.error(f"Error in sampling: {e}")
            return True  # Default to allowing events if Redis fails
            
    async def close(self):
        """Close Redis connection"""
        if self._redis_client:
            await self._redis_client.close()
            self._redis_client = None

# Global instance
audit_rate_limiter = AuditRateLimiter()
