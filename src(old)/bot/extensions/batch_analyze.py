"""
Batch Analyze Command Module

Implements the /batch_analyze command for multi-symbol processing in the analyze pipeline.
Allows users to analyze multiple stocks at once and compare their technical indicators.
"""

import discord
from discord import app_commands
from discord.ext import commands
import asyncio
import logging
from typing import Optional, List, Dict, Any
from datetime import datetime

from src.shared.error_handling.logging import get_logger
from src.bot.permissions import PermissionLevel, require_permission
from src.bot.utils.input_sanitizer import InputSanitizer
from src.bot.pipeline.commands.analyze.pipeline import execute_analyze_pipeline as analyze_pipeline
from src.bot.pipeline.commands.analyze.pipeline import PipelineContext, PipelineStatus
from src.core.formatting.response_templates import ResponseGenerator
from src.bot.utils.disclaimer_manager import add_disclaimer

logger = get_logger(__name__)

class BatchAnalyzeCommands(commands.Cog):
    """Batch analyze commands for multi-symbol processing"""
    
    def __init__(self, bot):
        self.bot = bot
        self.permission_checker = getattr(bot, 'permission_checker', None)
        self.response_generator = ResponseGenerator()
        
        # Maximum number of symbols to analyze at once
        self.max_symbols = 5
    
    @app_commands.command(name="batch_analyze", description="Analyze multiple stocks at once and compare their technical indicators")
    @app_commands.describe(
        symbols="Comma-separated list of stock symbols to analyze (max 5)",
        timeframe="Timeframe for analysis (e.g., 1d, 1w, 1m)",
        indicators="Comma-separated list of indicators to focus on (e.g., rsi,macd,sma)"
    )
    @app_commands.choices(
        timeframe=[
            app_commands.Choice(name="1 Day", value="1d"),
            app_commands.Choice(name="1 Week", value="1w"),
            app_commands.Choice(name="1 Month", value="1m"),
            app_commands.Choice(name="3 Months", value="3m"),
            app_commands.Choice(name="6 Months", value="6m"),
            app_commands.Choice(name="1 Year", value="1y")
        ]
    )
    async def batch_analyze_command(
        self, 
        interaction: discord.Interaction, 
        symbols: str,
        timeframe: Optional[str] = "1d",
        indicators: Optional[str] = None
    ):
        """
        Analyze multiple stocks at once and compare their technical indicators
        
        Parameters:
        -----------
        symbols: str
            Comma-separated list of stock symbols to analyze (max 5)
        timeframe: str, optional
            Timeframe for analysis (e.g., 1d, 1w, 1m)
        indicators: str, optional
            Comma-separated list of indicators to focus on (e.g., rsi,macd,sma)
        """
        # Check if user has paid access
        has_permission, reason = self.permission_checker.has_permission(
            interaction.user, PermissionLevel.PAID, None, str(interaction.guild_id) if interaction.guild_id else None
        )
        
        if not has_permission:
            await interaction.response.send_message(
                f"❌ This command requires paid tier access: {reason}"
            )
            return
        
        # Parse and validate symbols
        symbol_list = [s.strip() for s in symbols.split(',') if s.strip()]
        
        if not symbol_list:
            await interaction.response.send_message(
                "❌ Please provide at least one valid symbol."
            )
            return
        
        if len(symbol_list) > self.max_symbols:
            await interaction.response.send_message(
                f"❌ You can analyze a maximum of {self.max_symbols} symbols at once. "
                f"You provided {len(symbol_list)} symbols."
            )
            return
        
        # Sanitize symbols
        sanitized_symbols = []
        invalid_symbols = []
        
        for symbol in symbol_list:
            sanitized, is_valid, error_message = InputSanitizer.sanitize_symbol(symbol)
            if is_valid:
                sanitized_symbols.append(sanitized)
            else:
                invalid_symbols.append(f"{symbol} ({error_message})")
        
        if invalid_symbols:
            await interaction.response.send_message(
                f"❌ The following symbols are invalid: {', '.join(invalid_symbols)}"
            )
            return
        
        # Parse indicators if provided
        indicator_list = []
        if indicators:
            indicator_list = [i.strip().lower() for i in indicators.split(',') if i.strip()]
        
        await interaction.response.defer(thinking=True)
        
        try:
            # Create initial response
            await interaction.followup.send(
                f"🔍 Analyzing {len(sanitized_symbols)} symbols: {', '.join(sanitized_symbols)}...\n"
                f"This may take a moment."
            )
            
            # Process each symbol in parallel
            tasks = []
            for symbol in sanitized_symbols:
                # Create pipeline context
                context = PipelineContext()
                context.processing_results = {
                    "symbol": symbol,
                    "timeframe": timeframe,
                    "indicators": indicator_list
                }
                
                # Add task to analyze this symbol
                task = asyncio.create_task(analyze_pipeline(symbol, context))
                tasks.append((symbol, task))
            
            # Wait for all tasks to complete with timeout
            results = {}
            timeout_symbols = []
            
            for symbol, task in tasks:
                try:
                    # Wait for task with timeout
                    result = await asyncio.wait_for(task, timeout=30.0)
                    results[symbol] = result
                except asyncio.TimeoutError:
                    logger.warning(f"Analysis timed out for {symbol}")
                    timeout_symbols.append(symbol)
                except Exception as e:
                    logger.error(f"Error analyzing {symbol}: {e}")
                    # Create failed context
                    failed_context = PipelineContext()
                    failed_context.status = PipelineStatus.FAILED
                    failed_context.error_log.append({
                        "error_message": str(e),
                        "error_type": type(e).__name__,
                        "stage": "batch_analysis"
                    })
                    results[symbol] = failed_context
            
            # Generate comparison report
            comparison_report = await self._generate_comparison_report(results, indicator_list)
            
            # Add disclaimer
            comparison_report = add_disclaimer(comparison_report, {
                'command': 'batch_analyze',
                'symbols': sanitized_symbols
            })
            
            # Send comparison report
            await interaction.followup.send(comparison_report)
            
            # Notify about timeouts if any
            if timeout_symbols:
                await interaction.followup.send(
                    f"⚠️ Analysis timed out for the following symbols: {', '.join(timeout_symbols)}"
                )
                
        except Exception as e:
            logger.error(f"Error in batch analyze command: {e}")
            await interaction.followup.send(
                "❌ An error occurred while processing your request. Please try again later."
            )
    
    async def _generate_comparison_report(self, results: Dict[str, PipelineContext], focus_indicators: List[str]) -> str:
        """Generate a comparison report for multiple symbols"""
        # Check if we have any successful results
        if not results:
            return "❌ No analysis results available."
        
        # Extract technical analysis data
        analysis_data = {}
        failed_symbols = []
        
        for symbol, context in results.items():
            if context.status == PipelineStatus.COMPLETED and 'technical_analysis' in context.processing_results:
                analysis_data[symbol] = context.processing_results['technical_analysis']
            else:
                failed_symbols.append(symbol)
        
        if not analysis_data:
            return "❌ Technical analysis failed for all symbols."
        
        # Start building the report
        report = ["# 📊 Multi-Symbol Technical Analysis\n"]
        
        # Add summary table
        report.append("## Summary\n")
        report.append("| Symbol | Price | Change % | RSI | Trend | Signal |")
        report.append("|--------|-------|----------|-----|-------|--------|")
        
        for symbol, data in analysis_data.items():
            # Extract key metrics
            price = data.get('indicators', {}).get('current_price', 'N/A')
            if isinstance(price, (int, float)):
                price = f"${price:.2f}"
                
            change = data.get('indicators', {}).get('price_change_percent', 'N/A')
            if isinstance(change, (int, float)):
                change_str = f"{change:.2f}%"
                if change > 0:
                    change_str = f"🟢 +{change_str}"
                elif change < 0:
                    change_str = f"🔴 {change_str}"
            else:
                change_str = "N/A"
                
            rsi = data.get('indicators', {}).get('rsi', 'N/A')
            if isinstance(rsi, (int, float)):
                rsi_str = f"{rsi:.1f}"
                if rsi > 70:
                    rsi_str += " (Overbought)"
                elif rsi < 30:
                    rsi_str += " (Oversold)"
            else:
                rsi_str = "N/A"
                
            trend = data.get('trend', 'N/A')
            if isinstance(trend, str):
                if "bullish" in trend.lower():
                    trend = "🟢 " + trend
                elif "bearish" in trend.lower():
                    trend = "🔴 " + trend
                elif "neutral" in trend.lower():
                    trend = "⚪ " + trend
                    
            signal = data.get('signal', 'N/A')
            
            # Add row to table
            report.append(f"| ${symbol} | {price} | {change_str} | {rsi_str} | {trend} | {signal} |")
        
        # Add detailed comparison sections
        if focus_indicators:
            # Only include specified indicators
            report.append("\n## Indicator Comparison\n")
            
            for indicator in focus_indicators:
                if indicator in ['rsi', 'relative_strength_index']:
                    self._add_rsi_comparison(report, analysis_data)
                elif indicator in ['macd', 'moving_average_convergence_divergence']:
                    self._add_macd_comparison(report, analysis_data)
                elif indicator in ['sma', 'simple_moving_average']:
                    self._add_sma_comparison(report, analysis_data)
                elif indicator in ['ema', 'exponential_moving_average']:
                    self._add_ema_comparison(report, analysis_data)
                elif indicator in ['bb', 'bollinger_bands']:
                    self._add_bollinger_comparison(report, analysis_data)
                elif indicator in ['volume']:
                    self._add_volume_comparison(report, analysis_data)
        else:
            # Include all common indicators
            report.append("\n## Key Indicators\n")
            self._add_rsi_comparison(report, analysis_data)
            self._add_macd_comparison(report, analysis_data)
            self._add_sma_comparison(report, analysis_data)
        
        # Add support/resistance comparison
        report.append("\n## Support & Resistance Levels\n")
        report.append("| Symbol | Support Levels | Resistance Levels |")
        report.append("|--------|----------------|-------------------|")
        
        for symbol, data in analysis_data.items():
            # Extract support/resistance levels
            support_levels = data.get('support_resistance', {}).get('support', [])
            resistance_levels = data.get('support_resistance', {}).get('resistance', [])
            
            # Format levels
            if support_levels and isinstance(support_levels, list):
                support_str = ", ".join([f"${level:.2f}" for level in support_levels[:3]])
            else:
                support_str = "N/A"
                
            if resistance_levels and isinstance(resistance_levels, list):
                resistance_str = ", ".join([f"${level:.2f}" for level in resistance_levels[:3]])
            else:
                resistance_str = "N/A"
                
            # Add row to table
            report.append(f"| ${symbol} | {support_str} | {resistance_str} |")
        
        # Add price targets comparison if available
        has_price_targets = False
        for data in analysis_data.values():
            if 'price_targets' in data:
                has_price_targets = True
                break
                
        if has_price_targets:
            report.append("\n## Price Targets\n")
            report.append("| Symbol | Bullish Target | Bearish Target | Stop Loss |")
            report.append("|--------|---------------|---------------|-----------|")
            
            for symbol, data in analysis_data.items():
                # Extract price targets
                price_targets = data.get('price_targets', {})
                
                bullish = price_targets.get('bullish', 'N/A')
                if isinstance(bullish, (int, float)):
                    bullish = f"${bullish:.2f}"
                    
                bearish = price_targets.get('bearish', 'N/A')
                if isinstance(bearish, (int, float)):
                    bearish = f"${bearish:.2f}"
                    
                stop_loss = price_targets.get('stop_loss', 'N/A')
                if isinstance(stop_loss, (int, float)):
                    stop_loss = f"${stop_loss:.2f}"
                    
                # Add row to table
                report.append(f"| ${symbol} | {bullish} | {bearish} | {stop_loss} |")
        
        # Add failed symbols if any
        if failed_symbols:
            report.append(f"\n⚠️ Analysis failed for: {', '.join(failed_symbols)}")
        
        return "\n".join(report)
    
    def _add_rsi_comparison(self, report: List[str], analysis_data: Dict[str, Dict[str, Any]]):
        """Add RSI comparison to the report"""
        report.append("### RSI (Relative Strength Index)\n")
        report.append("| Symbol | RSI | Status |")
        report.append("|--------|-----|--------|")
        
        for symbol, data in analysis_data.items():
            rsi = data.get('indicators', {}).get('rsi', 'N/A')
            
            if isinstance(rsi, (int, float)):
                if rsi > 70:
                    status = "🔴 Overbought"
                elif rsi < 30:
                    status = "🟢 Oversold"
                elif rsi > 60:
                    status = "🟠 Approaching Overbought"
                elif rsi < 40:
                    status = "🟡 Approaching Oversold"
                else:
                    status = "⚪ Neutral"
                    
                rsi_str = f"{rsi:.1f}"
            else:
                rsi_str = "N/A"
                status = "N/A"
                
            report.append(f"| ${symbol} | {rsi_str} | {status} |")
    
    def _add_macd_comparison(self, report: List[str], analysis_data: Dict[str, Dict[str, Any]]):
        """Add MACD comparison to the report"""
        report.append("### MACD (Moving Average Convergence Divergence)\n")
        report.append("| Symbol | MACD | Signal | Histogram | Trend |")
        report.append("|--------|------|--------|-----------|-------|")
        
        for symbol, data in analysis_data.items():
            indicators = data.get('indicators', {})
            macd = indicators.get('macd', 'N/A')
            macd_signal = indicators.get('macd_signal', 'N/A')
            
            if isinstance(macd, (int, float)) and isinstance(macd_signal, (int, float)):
                histogram = macd - macd_signal
                
                if histogram > 0:
                    if histogram > macd_signal * 0.1:
                        trend = "🟢 Strong Bullish"
                    else:
                        trend = "🟢 Bullish"
                elif histogram < 0:
                    if histogram < -macd_signal * 0.1:
                        trend = "🔴 Strong Bearish"
                    else:
                        trend = "🔴 Bearish"
                else:
                    trend = "⚪ Neutral"
                    
                macd_str = f"{macd:.3f}"
                signal_str = f"{macd_signal:.3f}"
                histogram_str = f"{histogram:.3f}"
            else:
                macd_str = "N/A"
                signal_str = "N/A"
                histogram_str = "N/A"
                trend = "N/A"
                
            report.append(f"| ${symbol} | {macd_str} | {signal_str} | {histogram_str} | {trend} |")
    
    def _add_sma_comparison(self, report: List[str], analysis_data: Dict[str, Dict[str, Any]]):
        """Add SMA comparison to the report"""
        report.append("### SMA (Simple Moving Average)\n")
        report.append("| Symbol | Price | SMA 20 | SMA 50 | SMA 200 | Position |")
        report.append("|--------|-------|--------|--------|---------|----------|")
        
        for symbol, data in analysis_data.items():
            indicators = data.get('indicators', {})
            price = indicators.get('current_price', 'N/A')
            sma20 = indicators.get('sma_20', 'N/A')
            sma50 = indicators.get('sma_50', 'N/A')
            sma200 = indicators.get('sma_200', 'N/A')
            
            if isinstance(price, (int, float)) and isinstance(sma20, (int, float)) and \
               isinstance(sma50, (int, float)) and isinstance(sma200, (int, float)):
                
                if price > sma20 and price > sma50 and price > sma200:
                    position = "🟢 Above All SMAs"
                elif price < sma20 and price < sma50 and price < sma200:
                    position = "🔴 Below All SMAs"
                elif price > sma200:
                    position = "🟡 Above SMA 200"
                elif price < sma200:
                    position = "🟠 Below SMA 200"
                else:
                    position = "⚪ Mixed"
                    
                price_str = f"${price:.2f}"
                sma20_str = f"${sma20:.2f}"
                sma50_str = f"${sma50:.2f}"
                sma200_str = f"${sma200:.2f}"
            else:
                price_str = "N/A"
                sma20_str = "N/A"
                sma50_str = "N/A"
                sma200_str = "N/A"
                position = "N/A"
                
            report.append(f"| ${symbol} | {price_str} | {sma20_str} | {sma50_str} | {sma200_str} | {position} |")
    
    def _add_ema_comparison(self, report: List[str], analysis_data: Dict[str, Dict[str, Any]]):
        """Add EMA comparison to the report"""
        report.append("### EMA (Exponential Moving Average)\n")
        report.append("| Symbol | EMA 12 | EMA 26 | Crossover |")
        report.append("|--------|--------|--------|-----------|")
        
        for symbol, data in analysis_data.items():
            indicators = data.get('indicators', {})
            ema12 = indicators.get('ema_12', 'N/A')
            ema26 = indicators.get('ema_26', 'N/A')
            
            if isinstance(ema12, (int, float)) and isinstance(ema26, (int, float)):
                if ema12 > ema26:
                    crossover = "🟢 Bullish (12 > 26)"
                elif ema12 < ema26:
                    crossover = "🔴 Bearish (12 < 26)"
                else:
                    crossover = "⚪ Neutral"
                    
                ema12_str = f"${ema12:.2f}"
                ema26_str = f"${ema26:.2f}"
            else:
                ema12_str = "N/A"
                ema26_str = "N/A"
                crossover = "N/A"
                
            report.append(f"| ${symbol} | {ema12_str} | {ema26_str} | {crossover} |")
    
    def _add_bollinger_comparison(self, report: List[str], analysis_data: Dict[str, Dict[str, Any]]):
        """Add Bollinger Bands comparison to the report"""
        report.append("### Bollinger Bands\n")
        report.append("| Symbol | Price | Lower Band | Upper Band | Position |")
        report.append("|--------|-------|------------|------------|----------|")
        
        for symbol, data in analysis_data.items():
            indicators = data.get('indicators', {})
            price = indicators.get('current_price', 'N/A')
            lower = indicators.get('bollinger_lower', 'N/A')
            upper = indicators.get('bollinger_upper', 'N/A')
            
            if isinstance(price, (int, float)) and isinstance(lower, (int, float)) and isinstance(upper, (int, float)):
                band_width = ((upper - lower) / ((upper + lower) / 2)) * 100
                
                if price <= lower:
                    position = "🟢 At/Below Lower Band (Potential Buy)"
                elif price >= upper:
                    position = "🔴 At/Above Upper Band (Potential Sell)"
                elif price > (upper + lower) / 2:
                    position = "🟠 Upper Half"
                else:
                    position = "🟡 Lower Half"
                    
                price_str = f"${price:.2f}"
                lower_str = f"${lower:.2f}"
                upper_str = f"${upper:.2f}"
            else:
                price_str = "N/A"
                lower_str = "N/A"
                upper_str = "N/A"
                position = "N/A"
                
            report.append(f"| ${symbol} | {price_str} | {lower_str} | {upper_str} | {position} |")
    
    def _add_volume_comparison(self, report: List[str], analysis_data: Dict[str, Dict[str, Any]]):
        """Add volume comparison to the report"""
        report.append("### Volume Analysis\n")
        report.append("| Symbol | Volume | Avg Volume | Ratio | Significance |")
        report.append("|--------|--------|------------|-------|--------------|")
        
        for symbol, data in analysis_data.items():
            indicators = data.get('indicators', {})
            volume = indicators.get('volume', 'N/A')
            avg_volume = indicators.get('volume_sma', 'N/A')
            
            if isinstance(volume, (int, float)) and isinstance(avg_volume, (int, float)) and avg_volume > 0:
                ratio = volume / avg_volume
                
                if ratio > 2.0:
                    significance = "🔵 Very High (>2x avg)"
                elif ratio > 1.5:
                    significance = "🟢 High (>1.5x avg)"
                elif ratio > 1.0:
                    significance = "🟡 Above Average"
                elif ratio > 0.5:
                    significance = "🟠 Below Average"
                else:
                    significance = "🔴 Very Low (<0.5x avg)"
                    
                volume_str = f"{volume:,.0f}"
                avg_volume_str = f"{avg_volume:,.0f}"
                ratio_str = f"{ratio:.2f}x"
            else:
                volume_str = "N/A"
                avg_volume_str = "N/A"
                ratio_str = "N/A"
                significance = "N/A"
                
            report.append(f"| ${symbol} | {volume_str} | {avg_volume_str} | {ratio_str} | {significance} |")


async def setup(bot):
    """Add the batch analyze commands to the bot"""
    await bot.add_cog(BatchAnalyzeCommands(bot))
