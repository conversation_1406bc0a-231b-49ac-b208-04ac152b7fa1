"""
Status command extension - Bot health and system status.
"""

import discord
from discord.ext import commands
from discord import app_commands
from src.shared.error_handling.logging import get_logger
from src.bot.permissions import Discord<PERSON>er<PERSON><PERSON><PERSON><PERSON>, PermissionLevel

logger = get_logger(__name__)

class StatusCommand(commands.Cog):
    """Status command for bot health monitoring."""
    
    def __init__(self, bot: commands.Bot):
        self.bot = bot
        self.permission_checker = DiscordPermissionChecker()
    
    @app_commands.command(name="status", description="Check bot status and system health")
    async def status_command(self, interaction: discord.Interaction):
        """Check bot status and system health"""
        try:
            # Check if user has admin access
            if not self.permission_checker.has_permission(interaction.user, PermissionLevel.ADMIN):
                await interaction.response.send_message(
                    "❌ This command requires admin access.",
                    ephemeral=True
                )
                return
            
            # Get bot status
            latency = round(self.bot.latency * 1000)
            guild_count = len(self.bot.guilds)
            user_count = sum(guild.member_count for guild in self.bot.guilds if guild.member_count is not None)
            
            # Get services status
            services = getattr(self.bot, 'services', None)
            service_status = self._get_service_status(services)
            
            embed = discord.Embed(
                title="🤖 Bot Status",
                color=discord.Color.green(),
                timestamp=discord.utils.utcnow()
            )
            
            # Basic info
            embed.add_field(name="Latency", value=f"{latency}ms", inline=True)
            embed.add_field(name="Servers", value=guild_count, inline=True)
            embed.add_field(name="Users", value=user_count, inline=True)
            embed.add_field(name="Status", value="🟢 Online", inline=True)
            
            # Service status
            embed.add_field(
                name="�� Services",
                value=service_status,
                inline=False
            )
            
            await interaction.response.send_message(embed=embed)
            
        except Exception as e:
            logger.error(f"Error in status command: {e}", exc_info=True)
            await interaction.response.send_message(
                "❌ An error occurred while checking status.",
                ephemeral=True
            )
    
    def _get_service_status(self, services) -> str:
        """Get status of all services."""
        if not services:
            return "❌ Services not initialized"
        
        status_lines = []
        service_names = ['database', 'ai_service', 'rate_limiter', 'watchlist_manager', 'permission_checker']
        
        for service_name in service_names:
            service = services.get_service(service_name)
            status = "✅" if service else "❌"
            status_lines.append(f"{status} {service_name.replace('_', ' ').title()}")
        
        return "\n".join(status_lines)

async def setup(bot: commands.Bot):
    """Setup the status command extension."""
    await bot.add_cog(StatusCommand(bot))
    logger.info("✅ Status command extension loaded")
