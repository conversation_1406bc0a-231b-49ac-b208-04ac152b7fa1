"""
ML Administration Commands

Provides admin commands for managing ML model training and status.
"""

import discord
from discord.ext import commands
from discord import app_commands
import logging
from typing import Optional

from src.shared.error_handling.logging import get_logger
from src.analysis.ai.ml_training_service import ml_training_service
from src.analysis.ai.ml_models import trading_ml_model

logger = get_logger(__name__)


class MLAdminCog(commands.Cog):
    """ML Administration commands for managing model training"""
    
    def __init__(self, bot):
        self.bot = bot
        logger.info("✅ ML Admin extension loaded")
    
    @app_commands.command(name="ml_status", description="Check ML model training status")
    async def ml_status(self, interaction: discord.Interaction):
        """Check the status of ML models"""
        try:
            await interaction.response.defer()
            
            # Get training status
            status = ml_training_service.get_training_status()
            model_summary = trading_ml_model.get_model_summary()
            
            # Create embed
            embed = discord.Embed(
                title="🤖 ML Model Status",
                color=discord.Color.green() if status['is_trained'] else discord.Color.orange()
            )
            
            # Basic status
            embed.add_field(
                name="📊 Training Status",
                value=f"**Trained**: {'✅ Yes' if status['is_trained'] else '❌ No'}\n"
                      f"**In Progress**: {'🔄 Yes' if status['training_in_progress'] else '✅ No'}\n"
                      f"**Last Training**: {status['last_training_time'] or 'Never'}",
                inline=False
            )
            
            # Model details
            if status['is_trained']:
                models = ', '.join(status['models_available'])
                embed.add_field(
                    name="🧠 Available Models",
                    value=f"**Models**: {models}\n"
                          f"**Type**: {model_summary['model_type']}",
                    inline=False
                )
                
                # Performance metrics
                if status['performance_metrics']:
                    perf_text = ""
                    for model_name, metrics in status['performance_metrics'].items():
                        r2 = metrics.get('r2_score', 0)
                        acc = metrics.get('accuracy', 0)
                        perf_text += f"**{model_name}**: R²={r2:.3f}, Acc={acc:.3f}\n"
                    
                    embed.add_field(
                        name="📈 Performance Metrics",
                        value=perf_text,
                        inline=False
                    )
            
            # Recent training history
            if status['training_history']:
                history_text = ""
                for entry in status['training_history'][-3:]:  # Last 3 entries
                    timestamp = entry['timestamp'][:16]  # Just date and time
                    success = "✅" if entry['success'] else "❌"
                    duration = entry['duration_seconds']
                    samples = entry['training_samples']
                    history_text += f"{success} {timestamp} ({duration:.1f}s, {samples} samples)\n"
                
                embed.add_field(
                    name="📋 Recent Training History",
                    value=history_text,
                    inline=False
                )
            
            await interaction.followup.send(embed=embed)
            
        except Exception as e:
            logger.error(f"Error in ml_status command: {e}")
            await interaction.followup.send(
                "❌ Error checking ML status. Please check logs.",
                ephemeral=True
            )
    
    @app_commands.command(name="ml_train", description="Train ML models now (Admin only)")
    async def ml_train(self, interaction: discord.Interaction):
        """Manually trigger ML model training"""
        try:
            # Check if user has admin permissions
            if not any(role.name.lower() in ['admin', 'moderator', 'mod'] for role in interaction.user.roles):
                await interaction.response.send_message(
                    "❌ This command requires admin permissions.",
                    ephemeral=True
                )
                return
            
            await interaction.response.defer()
            
            # Check if training is already in progress
            status = ml_training_service.get_training_status()
            if status['training_in_progress']:
                await interaction.followup.send(
                    "⏳ ML training is already in progress. Please wait for it to complete.",
                    ephemeral=True
                )
                return
            
            # Start training
            embed = discord.Embed(
                title="🚀 Starting ML Training",
                description="Training ML models with sample data...",
                color=discord.Color.blue()
            )
            await interaction.followup.send(embed=embed)
            
            # Train the models
            training_result = await ml_training_service.train_models_now()
            
            # Create result embed
            if training_result.get('success'):
                result_embed = discord.Embed(
                    title="✅ ML Training Completed",
                    color=discord.Color.green()
                )
                
                duration = training_result.get('duration_seconds', 0)
                samples = training_result.get('training_samples', 0)
                models = ', '.join(training_result.get('models_trained', []))
                
                result_embed.add_field(
                    name="📊 Training Results",
                    value=f"**Duration**: {duration:.2f} seconds\n"
                          f"**Samples**: {samples}\n"
                          f"**Models**: {models}",
                    inline=False
                )
                
                # Performance metrics
                if training_result.get('performance'):
                    perf_text = ""
                    for model_name, metrics in training_result['performance'].items():
                        r2 = metrics.get('r2_score', 0)
                        acc = metrics.get('accuracy', 0)
                        perf_text += f"**{model_name}**: R²={r2:.3f}, Acc={acc:.3f}\n"
                    
                    result_embed.add_field(
                        name="📈 Performance",
                        value=perf_text,
                        inline=False
                    )
                
            else:
                result_embed = discord.Embed(
                    title="❌ ML Training Failed",
                    description=f"Error: {training_result.get('error', 'Unknown error')}",
                    color=discord.Color.red()
                )
            
            await interaction.edit_original_response(embed=result_embed)
            
        except Exception as e:
            logger.error(f"Error in ml_train command: {e}")
            await interaction.followup.send(
                "❌ Error during ML training. Please check logs.",
                ephemeral=True
            )
    
    @app_commands.command(name="ml_predict", description="Test ML prediction on a symbol")
    async def ml_predict(self, interaction: discord.Interaction, symbol: str):
        """Test ML prediction on a specific symbol"""
        try:
            await interaction.response.defer()
            
            # Check if models are trained
            if not trading_ml_model.is_trained:
                await interaction.followup.send(
                    "❌ ML models are not trained yet. Use `/ml_train` to train them first.",
                    ephemeral=True
                )
                return
            
            # Get sample data for prediction
            from src.api.data.providers.data_source_manager import DataSourceManager
            
            data_manager = DataSourceManager()
            market_data = await data_manager.get_market_data(symbol.upper())
            
            if not market_data:
                await interaction.followup.send(
                    f"❌ Could not get market data for {symbol.upper()}",
                    ephemeral=True
                )
                return
            
            # Make prediction
            historical_data = {
                'close': market_data.get('price', 0),
                'volume': market_data.get('volume', 0),
                'high': market_data.get('high', market_data.get('price', 0)),
                'low': market_data.get('low', market_data.get('price', 0)),
                'open': market_data.get('open', market_data.get('price', 0))
            }
            
            prediction = trading_ml_model.predict(
                historical_data=historical_data,
                technical_indicators={'rsi': 50, 'macd': 0},  # Basic fallback
                prediction_horizon="1W"
            )
            
            # Create result embed
            embed = discord.Embed(
                title=f"🤖 ML Prediction for {symbol.upper()}",
                color=discord.Color.blue()
            )
            
            current_price = market_data.get('price', 0)
            predicted_price = prediction.predicted_price
            expected_return = ((predicted_price / current_price) - 1) * 100 if current_price > 0 else 0
            
            embed.add_field(
                name="📊 Prediction",
                value=f"**Direction**: {prediction.direction}\n"
                      f"**Confidence**: {prediction.confidence:.1f}%\n"
                      f"**Probability**: {prediction.probability:.1%}",
                inline=False
            )
            
            embed.add_field(
                name="💰 Price Forecast",
                value=f"**Current**: ${current_price:.2f}\n"
                      f"**Predicted**: ${predicted_price:.2f}\n"
                      f"**Expected Return**: {expected_return:+.2f}%",
                inline=False
            )
            
            embed.add_field(
                name="🔧 Model Info",
                value=f"**Model Used**: {prediction.model_used}\n"
                      f"**Horizon**: {prediction.prediction_horizon}\n"
                      f"**Features**: {len(prediction.features_used)}",
                inline=False
            )
            
            await interaction.followup.send(embed=embed)
            
        except Exception as e:
            logger.error(f"Error in ml_predict command: {e}")
            await interaction.followup.send(
                "❌ Error making ML prediction. Please check logs.",
                ephemeral=True
            )


async def setup(bot):
    """Setup function for the cog"""
    await bot.add_cog(MLAdminCog(bot))
