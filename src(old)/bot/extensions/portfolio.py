"""
Portfolio Command Module

Implements the /portfolio command for user-specific position tracking.
Allows users to manage their stock portfolio, track performance, and receive insights.
"""

import discord
from discord import app_commands
from discord.ext import commands
import asyncio
import logging
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta
import json

from src.shared.error_handling.logging import get_logger
from src.bot.permissions import PermissionLevel, require_permission, validate_token
from src.bot.utils.input_sanitizer import InputSanitizer
from src.shared.data_providers.aggregator import DataProviderAggregator

logger = get_logger(__name__)

class Position:
    """Represents a position in a user's portfolio"""
    
    def __init__(
        self, 
        symbol: str, 
        quantity: float, 
        entry_price: float,
        entry_date: datetime = None,
        notes: str = None
    ):
        self.symbol = symbol
        self.quantity = quantity
        self.entry_price = entry_price
        self.entry_date = entry_date or datetime.now()
        self.notes = notes
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert position to dictionary for storage"""
        return {
            "symbol": self.symbol,
            "quantity": self.quantity,
            "entry_price": self.entry_price,
            "entry_date": self.entry_date.timestamp(),
            "notes": self.notes
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Position':
        """Create position from dictionary"""
        return cls(
            symbol=data["symbol"],
            quantity=data["quantity"],
            entry_price=data["entry_price"],
            entry_date=datetime.fromtimestamp(data["entry_date"]),
            notes=data.get("notes")
        )


class Portfolio:
    """Represents a user's portfolio of positions"""
    
    def __init__(self, user_id: str, name: str = "Default"):
        self.user_id = user_id
        self.name = name
        self.positions: Dict[str, Position] = {}
        self.created_at = datetime.now()
        self.updated_at = datetime.now()
        
    def add_position(self, position: Position) -> None:
        """Add a position to the portfolio"""
        self.positions[position.symbol] = position
        self.updated_at = datetime.now()
        
    def update_position(self, symbol: str, quantity: float = None, entry_price: float = None, notes: str = None) -> bool:
        """Update an existing position"""
        if symbol not in self.positions:
            return False
            
        position = self.positions[symbol]
        
        if quantity is not None:
            position.quantity = quantity
            
        if entry_price is not None:
            position.entry_price = entry_price
            
        if notes is not None:
            position.notes = notes
            
        self.updated_at = datetime.now()
        return True
        
    def remove_position(self, symbol: str) -> bool:
        """Remove a position from the portfolio"""
        if symbol in self.positions:
            del self.positions[symbol]
            self.updated_at = datetime.now()
            return True
        return False
        
    def get_position(self, symbol: str) -> Optional[Position]:
        """Get a position by symbol"""
        return self.positions.get(symbol)
        
    def get_all_positions(self) -> List[Position]:
        """Get all positions in the portfolio"""
        return list(self.positions.values())
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert portfolio to dictionary for storage"""
        return {
            "user_id": self.user_id,
            "name": self.name,
            "positions": {symbol: pos.to_dict() for symbol, pos in self.positions.items()},
            "created_at": self.created_at.timestamp(),
            "updated_at": self.updated_at.timestamp()
        }
        
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Portfolio':
        """Create portfolio from dictionary"""
        portfolio = cls(
            user_id=data["user_id"],
            name=data.get("name", "Default")
        )
        
        portfolio.created_at = datetime.fromtimestamp(data["created_at"])
        portfolio.updated_at = datetime.fromtimestamp(data["updated_at"])
        
        for symbol, pos_data in data.get("positions", {}).items():
            portfolio.positions[symbol] = Position.from_dict(pos_data)
            
        return portfolio


class PortfolioManager:
    """Manages user portfolios with database storage"""
    
    def __init__(self, db_pool=None):
        """Initialize with database pool"""
        self.db_pool = db_pool
        self.data_provider = DataProviderAggregator()
        
    async def get_user_portfolio(self, user_id: str) -> Portfolio:
        """Get a user's portfolio from the database"""
        try:
            if not self.db_pool:
                # Return empty portfolio if no database connection
                return Portfolio(user_id)
                
            async with self.db_pool.acquire() as conn:
                # Check if portfolio exists
                result = await conn.fetchrow("""
                    SELECT data FROM user_portfolios
                    WHERE user_id = $1 AND is_active = TRUE
                """, user_id)
                
                if result:
                    # Parse portfolio data
                    portfolio_data = json.loads(result["data"])
                    return Portfolio.from_dict(portfolio_data)
                else:
                    # Create new portfolio
                    portfolio = Portfolio(user_id)
                    await self.save_portfolio(portfolio)
                    return portfolio
                    
        except Exception as e:
            logger.error(f"Error getting portfolio for user {user_id}: {e}")
            # Return empty portfolio on error
            return Portfolio(user_id)
    
    async def save_portfolio(self, portfolio: Portfolio) -> bool:
        """Save a portfolio to the database"""
        try:
            if not self.db_pool:
                logger.error("No database connection available")
                return False
                
            async with self.db_pool.acquire() as conn:
                # Convert portfolio to JSON
                portfolio_data = json.dumps(portfolio.to_dict())
                
                # Upsert portfolio
                await conn.execute("""
                    INSERT INTO user_portfolios (user_id, data, is_active)
                    VALUES ($1, $2, TRUE)
                    ON CONFLICT (user_id) DO UPDATE
                    SET data = $2, updated_at = NOW()
                """, portfolio.user_id, portfolio_data)
                
                return True
                
        except Exception as e:
            logger.error(f"Error saving portfolio for user {portfolio.user_id}: {e}")
            return False
    
    async def get_market_data(self, symbols: List[str]) -> Dict[str, Dict[str, Any]]:
        """Get market data for a list of symbols"""
        result = {}
        
        for symbol in symbols:
            try:
                data = await self.data_provider.get_ticker(symbol)
                if data and not isinstance(data, str) and not data.get("error"):
                    result[symbol] = data
            except Exception as e:
                logger.error(f"Error fetching market data for {symbol}: {e}")
                
        return result
    
    async def calculate_portfolio_performance(self, portfolio: Portfolio) -> Dict[str, Any]:
        """Calculate portfolio performance metrics"""
        if not portfolio.positions:
            return {
                "total_value": 0,
                "total_cost": 0,
                "total_gain_loss": 0,
                "total_gain_loss_percent": 0,
                "positions": []
            }
            
        # Get market data for all symbols
        symbols = list(portfolio.positions.keys())
        market_data = await self.get_market_data(symbols)
        
        total_value = 0
        total_cost = 0
        positions_data = []
        
        for symbol, position in portfolio.positions.items():
            # Calculate position metrics
            current_price = market_data.get(symbol, {}).get("current_price", position.entry_price)
            position_value = position.quantity * current_price
            position_cost = position.quantity * position.entry_price
            gain_loss = position_value - position_cost
            gain_loss_percent = (gain_loss / position_cost) * 100 if position_cost > 0 else 0
            
            # Add to totals
            total_value += position_value
            total_cost += position_cost
            
            # Add position data
            positions_data.append({
                "symbol": symbol,
                "quantity": position.quantity,
                "entry_price": position.entry_price,
                "current_price": current_price,
                "value": position_value,
                "cost": position_cost,
                "gain_loss": gain_loss,
                "gain_loss_percent": gain_loss_percent,
                "entry_date": position.entry_date.strftime("%Y-%m-%d")
            })
            
        # Calculate portfolio totals
        total_gain_loss = total_value - total_cost
        total_gain_loss_percent = (total_gain_loss / total_cost) * 100 if total_cost > 0 else 0
        
        return {
            "total_value": total_value,
            "total_cost": total_cost,
            "total_gain_loss": total_gain_loss,
            "total_gain_loss_percent": total_gain_loss_percent,
            "positions": positions_data
        }


class PortfolioCommands(commands.Cog):
    """Portfolio commands for position tracking"""
    
    def __init__(self, bot):
        self.bot = bot
        self.permission_checker = getattr(bot, 'permission_checker', None)
        
        # Get database pool from bot
        db_pool = None
        if hasattr(bot, 'watchlist_manager') and bot.watchlist_manager:
            db_pool = getattr(bot.watchlist_manager, 'db_pool', None)
            
        self.portfolio_manager = PortfolioManager(db_pool)
    
    @app_commands.command(name="portfolio", description="Manage your stock portfolio and track performance")
    @app_commands.describe(
        action="Action to perform (view, add, update, remove)",
        symbol="Stock symbol for the position",
        quantity="Number of shares",
        price="Entry price per share",
        notes="Optional notes about the position"
    )
    @app_commands.choices(
        action=[
            app_commands.Choice(name="view", value="view"),
            app_commands.Choice(name="add", value="add"),
            app_commands.Choice(name="update", value="update"),
            app_commands.Choice(name="remove", value="remove")
        ]
    )
    async def portfolio_command(
        self, 
        interaction: discord.Interaction, 
        action: str,
        symbol: Optional[str] = None,
        quantity: Optional[float] = None,
        price: Optional[float] = None,
        notes: Optional[str] = None
    ):
        """
        Manage your stock portfolio and track performance
        
        Parameters:
        -----------
        action: str
            Action to perform (view, add, update, remove)
        symbol: str, optional
            Stock symbol for the position
        quantity: float, optional
            Number of shares
        price: float, optional
            Entry price per share
        notes: str, optional
            Optional notes about the position
        """
        # Check if user has paid access
        has_permission, reason = self.permission_checker.has_permission(
            interaction.user, PermissionLevel.PAID, None, str(interaction.guild_id) if interaction.guild_id else None
        )
        
        if not has_permission:
            await interaction.response.send_message(
                f"❌ This command requires paid tier access: {reason}"
            )
            return
        
        # Handle different actions
        if action == "view":
            await self._handle_view_portfolio(interaction)
        elif action == "add":
            await self._handle_add_position(interaction, symbol, quantity, price, notes)
        elif action == "update":
            await self._handle_update_position(interaction, symbol, quantity, price, notes)
        elif action == "remove":
            await self._handle_remove_position(interaction, symbol)
        else:
            await interaction.response.send_message(
                "❌ Invalid action. Please use view, add, update, or remove."
            )
    
    async def _handle_view_portfolio(self, interaction: discord.Interaction):
        """Handle viewing user's portfolio"""
        await interaction.response.defer(thinking=True)
        
        user_id = str(interaction.user.id)
        
        try:
            # Get user's portfolio
            portfolio = await self.portfolio_manager.get_user_portfolio(user_id)
            
            if not portfolio.positions:
                await interaction.followup.send(
                    "You don't have any positions in your portfolio yet. Use `/portfolio add` to add one."
                )
                return
            
            # Calculate portfolio performance
            performance = await self.portfolio_manager.calculate_portfolio_performance(portfolio)
            
            # Create embed for portfolio
            embed = discord.Embed(
                title="📊 Your Portfolio",
                description=f"Portfolio value: ${performance['total_value']:.2f}",
                color=discord.Color.blue()
            )
            
            # Add portfolio summary
            gain_loss_color = "🟢" if performance['total_gain_loss'] >= 0 else "🔴"
            embed.add_field(
                name="Summary",
                value=f"Total Value: ${performance['total_value']:.2f}\n"
                      f"Total Cost: ${performance['total_cost']:.2f}\n"
                      f"Total P/L: {gain_loss_color} ${performance['total_gain_loss']:.2f} ({performance['total_gain_loss_percent']:.2f}%)",
                inline=False
            )
            
            # Add positions
            for position in performance['positions']:
                pos_gain_loss_color = "🟢" if position['gain_loss'] >= 0 else "🔴"
                embed.add_field(
                    name=f"${position['symbol']} ({position['quantity']} shares)",
                    value=f"Current: ${position['current_price']:.2f}\n"
                          f"Entry: ${position['entry_price']:.2f}\n"
                          f"P/L: {pos_gain_loss_color} ${position['gain_loss']:.2f} ({position['gain_loss_percent']:.2f}%)",
                    inline=True
                )
            
            embed.set_footer(text="Use /portfolio add to add new positions")
            await interaction.followup.send(embed=embed)
            
        except Exception as e:
            logger.error(f"Error viewing portfolio: {e}")
            await interaction.followup.send(
                "❌ An error occurred while retrieving your portfolio. Please try again later."
            )
    
    async def _handle_add_position(
        self, 
        interaction: discord.Interaction,
        symbol: Optional[str],
        quantity: Optional[float],
        price: Optional[float],
        notes: Optional[str]
    ):
        """Handle adding a position to portfolio"""
        # Check required parameters
        if not symbol or quantity is None or price is None:
            await interaction.response.send_message(
                "❌ Missing required parameters. Please provide symbol, quantity, and price."
            )
            return
        
        # Sanitize symbol
        sanitized_symbol, is_valid, error_message = InputSanitizer.sanitize_symbol(symbol)
        if not is_valid:
            await interaction.response.send_message(f"❌ Invalid symbol: {error_message}")
            return
        
        # Validate quantity and price
        if quantity <= 0:
            await interaction.response.send_message("❌ Quantity must be greater than zero.")
            return
            
        if price <= 0:
            await interaction.response.send_message("❌ Price must be greater than zero.")
            return
        
        await interaction.response.defer(thinking=True)
        
        user_id = str(interaction.user.id)
        
        try:
            # Get user's portfolio
            portfolio = await self.portfolio_manager.get_user_portfolio(user_id)
            
            # Check if position already exists
            if sanitized_symbol in portfolio.positions:
                await interaction.followup.send(
                    f"❌ You already have a position in {sanitized_symbol}. Use `/portfolio update` to modify it."
                )
                return
            
            # Create new position
            position = Position(
                symbol=sanitized_symbol,
                quantity=quantity,
                entry_price=price,
                notes=notes
            )
            
            # Add position to portfolio
            portfolio.add_position(position)
            
            # Save portfolio
            success = await self.portfolio_manager.save_portfolio(portfolio)
            
            if success:
                # Create success embed
                embed = discord.Embed(
                    title="✅ Position Added",
                    description=f"Added {quantity} shares of ${sanitized_symbol} to your portfolio",
                    color=discord.Color.green()
                )
                
                embed.add_field(
                    name="Position Details",
                    value=f"Symbol: ${sanitized_symbol}\n"
                          f"Quantity: {quantity}\n"
                          f"Entry Price: ${price:.2f}\n"
                          f"Total Cost: ${quantity * price:.2f}",
                    inline=False
                )
                
                if notes:
                    embed.add_field(name="Notes", value=notes, inline=False)
                
                embed.set_footer(text="Use /portfolio view to see your full portfolio")
                await interaction.followup.send(embed=embed)
            else:
                await interaction.followup.send(
                    "❌ Failed to save your portfolio. Please try again later."
                )
                
        except Exception as e:
            logger.error(f"Error adding position: {e}")
            await interaction.followup.send(
                "❌ An error occurred while adding the position. Please try again later."
            )
    
    async def _handle_update_position(
        self, 
        interaction: discord.Interaction,
        symbol: Optional[str],
        quantity: Optional[float],
        price: Optional[float],
        notes: Optional[str]
    ):
        """Handle updating a position in portfolio"""
        # Check required parameters
        if not symbol:
            await interaction.response.send_message(
                "❌ Missing required parameter. Please provide a symbol."
            )
            return
        
        # Sanitize symbol
        sanitized_symbol, is_valid, error_message = InputSanitizer.sanitize_symbol(symbol)
        if not is_valid:
            await interaction.response.send_message(f"❌ Invalid symbol: {error_message}")
            return
        
        # Validate quantity and price if provided
        if quantity is not None and quantity <= 0:
            await interaction.response.send_message("❌ Quantity must be greater than zero.")
            return
            
        if price is not None and price <= 0:
            await interaction.response.send_message("❌ Price must be greater than zero.")
            return
        
        await interaction.response.defer(thinking=True)
        
        user_id = str(interaction.user.id)
        
        try:
            # Get user's portfolio
            portfolio = await self.portfolio_manager.get_user_portfolio(user_id)
            
            # Check if position exists
            if sanitized_symbol not in portfolio.positions:
                await interaction.followup.send(
                    f"❌ You don't have a position in {sanitized_symbol}. Use `/portfolio add` to create one."
                )
                return
            
            # Get current position
            current_position = portfolio.get_position(sanitized_symbol)
            
            # Update position
            success = portfolio.update_position(
                symbol=sanitized_symbol,
                quantity=quantity,
                entry_price=price,
                notes=notes
            )
            
            if success:
                # Save portfolio
                save_success = await self.portfolio_manager.save_portfolio(portfolio)
                
                if save_success:
                    # Create success embed
                    embed = discord.Embed(
                        title="✅ Position Updated",
                        description=f"Updated your position in ${sanitized_symbol}",
                        color=discord.Color.blue()
                    )
                    
                    # Show what was updated
                    updates = []
                    if quantity is not None:
                        updates.append(f"Quantity: {current_position.quantity} → {quantity}")
                    if price is not None:
                        updates.append(f"Entry Price: ${current_position.entry_price:.2f} → ${price:.2f}")
                    if notes is not None:
                        updates.append(f"Notes: {current_position.notes} → {notes}")
                        
                    embed.add_field(
                        name="Changes",
                        value="\n".join(updates),
                        inline=False
                    )
                    
                    embed.set_footer(text="Use /portfolio view to see your full portfolio")
                    await interaction.followup.send(embed=embed)
                else:
                    await interaction.followup.send(
                        "❌ Failed to save your portfolio. Please try again later."
                    )
            else:
                await interaction.followup.send(
                    "❌ Failed to update position. Please try again later."
                )
                
        except Exception as e:
            logger.error(f"Error updating position: {e}")
            await interaction.followup.send(
                "❌ An error occurred while updating the position. Please try again later."
            )
    
    async def _handle_remove_position(self, interaction: discord.Interaction, symbol: Optional[str]):
        """Handle removing a position from portfolio"""
        # Check required parameters
        if not symbol:
            await interaction.response.send_message(
                "❌ Missing required parameter. Please provide a symbol."
            )
            return
        
        # Sanitize symbol
        sanitized_symbol, is_valid, error_message = InputSanitizer.sanitize_symbol(symbol)
        if not is_valid:
            await interaction.response.send_message(f"❌ Invalid symbol: {error_message}")
            return
        
        await interaction.response.defer(thinking=True)
        
        user_id = str(interaction.user.id)
        
        try:
            # Get user's portfolio
            portfolio = await self.portfolio_manager.get_user_portfolio(user_id)
            
            # Check if position exists
            if sanitized_symbol not in portfolio.positions:
                await interaction.followup.send(
                    f"❌ You don't have a position in {sanitized_symbol}."
                )
                return
            
            # Get position details before removal
            position = portfolio.get_position(sanitized_symbol)
            
            # Remove position
            success = portfolio.remove_position(sanitized_symbol)
            
            if success:
                # Save portfolio
                save_success = await self.portfolio_manager.save_portfolio(portfolio)
                
                if save_success:
                    # Create success embed
                    embed = discord.Embed(
                        title="🗑️ Position Removed",
                        description=f"Removed ${sanitized_symbol} from your portfolio",
                        color=discord.Color.red()
                    )
                    
                    embed.add_field(
                        name="Position Details",
                        value=f"Symbol: ${sanitized_symbol}\n"
                              f"Quantity: {position.quantity}\n"
                              f"Entry Price: ${position.entry_price:.2f}",
                        inline=False
                    )
                    
                    embed.set_footer(text="Use /portfolio view to see your full portfolio")
                    await interaction.followup.send(embed=embed)
                else:
                    await interaction.followup.send(
                        "❌ Failed to save your portfolio. Please try again later."
                    )
            else:
                await interaction.followup.send(
                    "❌ Failed to remove position. Please try again later."
                )
                
        except Exception as e:
            logger.error(f"Error removing position: {e}")
            await interaction.followup.send(
                "❌ An error occurred while removing the position. Please try again later."
            )


async def setup(bot):
    """Add the portfolio commands to the bot"""
    await bot.add_cog(PortfolioCommands(bot))
