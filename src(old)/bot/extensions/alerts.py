"""
Alerts Command Module

Implements the /alerts command for real-time notifications via WebSocket integration.
Allows users to set up, manage, and receive alerts for price movements, technical indicators,
and other market events.
"""

import discord
from discord import app_commands
from discord.ext import commands
import asyncio
import logging
from typing import Optional, List, Dict, Any
from datetime import datetime

from src.shared.error_handling.logging import get_logger
from src.bot.permissions import PermissionLevel, require_permission, validate_token
from src.bot.utils.input_sanitizer import InputSanitizer
from src.bot.watchlist_alerts import AlertType

logger = get_logger(__name__)

class AlertsCommands(commands.Cog):
    """Alerts commands for real-time market notifications"""
    
    def __init__(self, bot):
        self.bot = bot
        self.permission_checker = getattr(bot, 'permission_checker', None)
        
        # Get watchlist alert manager from bot
        self.watchlist_alert_manager = getattr(bot, 'watchlist_alert_manager', None)
        
        # Alert types for choices
        self.alert_types = {
            "Price Change": AlertType.PRICE_CHANGE,
            "Volume Spike": AlertType.VOLUME_SPIKE,
            "Technical Signal": AlertType.TECHNICAL_SIGNAL,
            "Support/Resistance": AlertType.SUPPORT_RESISTANCE,
            "Volatility": AlertType.VOLATILITY,
            "Earnings": AlertType.EARNINGS,
            "News": AlertType.NEWS
        }
        
        # Direction choices
        self.directions = ["up", "down", "both"]
        
        # Timeframe choices
        self.timeframes = ["1m", "5m", "15m", "30m", "1h", "4h", "1d", "1w"]
    
    @app_commands.command(name="alerts", description="Manage price and technical alerts for your watchlist")
    @app_commands.describe(
        action="Action to perform (list, create, update, delete)",
        symbol="Stock symbol to set alert for",
        alert_type="Type of alert to create",
        threshold="Alert threshold value (e.g., 5 for 5% price change)",
        direction="Alert trigger direction (up, down, both)",
        timeframe="Timeframe for the alert (e.g., 1d for daily)"
    )
    @app_commands.choices(
        action=[
            app_commands.Choice(name="list", value="list"),
            app_commands.Choice(name="create", value="create"),
            app_commands.Choice(name="update", value="update"),
            app_commands.Choice(name="delete", value="delete"),
            app_commands.Choice(name="test", value="test")
        ],
        alert_type=[
            app_commands.Choice(name="Price Change", value="price_change"),
            app_commands.Choice(name="Volume Spike", value="volume_spike"),
            app_commands.Choice(name="Technical Signal", value="technical_signal"),
            app_commands.Choice(name="Support/Resistance", value="support_resistance"),
            app_commands.Choice(name="Volatility", value="volatility")
        ],
        direction=[
            app_commands.Choice(name="Up", value="up"),
            app_commands.Choice(name="Down", value="down"),
            app_commands.Choice(name="Both", value="both")
        ],
        timeframe=[
            app_commands.Choice(name="1 Minute", value="1m"),
            app_commands.Choice(name="5 Minutes", value="5m"),
            app_commands.Choice(name="15 Minutes", value="15m"),
            app_commands.Choice(name="30 Minutes", value="30m"),
            app_commands.Choice(name="1 Hour", value="1h"),
            app_commands.Choice(name="4 Hours", value="4h"),
            app_commands.Choice(name="1 Day", value="1d"),
            app_commands.Choice(name="1 Week", value="1w")
        ]
    )
    async def alerts_command(
        self, 
        interaction: discord.Interaction, 
        action: str,
        symbol: Optional[str] = None,
        alert_type: Optional[str] = None,
        threshold: Optional[float] = None,
        direction: Optional[str] = None,
        timeframe: Optional[str] = None
    ):
        """
        Manage price and technical alerts for your watchlist
        
        Parameters:
        -----------
        action: str
            Action to perform (list, create, update, delete, test)
        symbol: str, optional
            Stock symbol to set alert for
        alert_type: str, optional
            Type of alert to create
        threshold: float, optional
            Alert threshold value (e.g., 5 for 5% price change)
        direction: str, optional
            Alert trigger direction (up, down, both)
        timeframe: str, optional
            Timeframe for the alert (e.g., 1d for daily)
        """
        # Check if user has paid access
        has_permission, reason = self.permission_checker.has_permission(
            interaction.user, PermissionLevel.PAID, None, str(interaction.guild_id) if interaction.guild_id else None
        )
        
        if not has_permission:
            await interaction.response.send_message(
                f"❌ This command requires paid tier access: {reason}"
            )
            return
        
        # Check if watchlist alert manager is available
        if not self.watchlist_alert_manager:
            await interaction.response.send_message(
                "❌ Alert system is not available. Please try again later."
            )
            return
        
        # Handle different actions
        if action == "list":
            await self._handle_list_alerts(interaction)
        elif action == "create":
            await self._handle_create_alert(interaction, symbol, alert_type, threshold, direction, timeframe)
        elif action == "update":
            await self._handle_update_alert(interaction, symbol, alert_type, threshold, direction, timeframe)
        elif action == "delete":
            await self._handle_delete_alert(interaction, symbol, alert_type)
        elif action == "test":
            await self._handle_test_alert(interaction, symbol)
        else:
            await interaction.response.send_message(
                "❌ Invalid action. Please use list, create, update, delete, or test."
            )
    
    async def _handle_list_alerts(self, interaction: discord.Interaction):
        """Handle listing user's alerts"""
        await interaction.response.defer(thinking=True)
        
        user_id = str(interaction.user.id)
        
        try:
            # Get user's alert preferences
            alert_prefs = await self.watchlist_alert_manager.get_user_alert_preferences(user_id)
            
            if not alert_prefs:
                await interaction.followup.send(
                    "You don't have any alerts set up yet. Use `/alerts create` to create one."
                )
                return
            
            # Create embed for alerts
            embed = discord.Embed(
                title="🔔 Your Alerts",
                description="Here are your current alert settings",
                color=discord.Color.blue()
            )
            
            for symbol, prefs in alert_prefs.items():
                alert_details = []
                
                for alert_type, settings in prefs.items():
                    if alert_type.endswith("_threshold") and settings is not None:
                        base_type = alert_type.replace("_threshold", "")
                        threshold = settings
                        direction = prefs.get(f"{base_type}_direction", "both")
                        timeframe = prefs.get(f"{base_type}_timeframe", "1d")
                        enabled = prefs.get(f"{base_type}_enabled", True)
                        
                        status = "✅ Enabled" if enabled else "❌ Disabled"
                        
                        alert_details.append(
                            f"**{base_type.replace('_', ' ').title()}**: {threshold} ({direction}, {timeframe}) - {status}"
                        )
                
                if alert_details:
                    embed.add_field(
                        name=f"${symbol}",
                        value="\n".join(alert_details),
                        inline=False
                    )
            
            embed.set_footer(text="Use /alerts create to add new alerts")
            await interaction.followup.send(embed=embed)
            
        except Exception as e:
            logger.error(f"Error listing alerts: {e}")
            await interaction.followup.send(
                "❌ An error occurred while retrieving your alerts. Please try again later."
            )
    
    async def _handle_create_alert(
        self, 
        interaction: discord.Interaction,
        symbol: Optional[str],
        alert_type: Optional[str],
        threshold: Optional[float],
        direction: Optional[str],
        timeframe: Optional[str]
    ):
        """Handle creating a new alert"""
        # Check required parameters
        if not symbol or not alert_type or threshold is None:
            await interaction.response.send_message(
                "❌ Missing required parameters. Please provide symbol, alert_type, and threshold."
            )
            return
        
        # Sanitize symbol
        sanitized_symbol, is_valid, error_message = InputSanitizer.sanitize_symbol(symbol)
        if not is_valid:
            await interaction.response.send_message(f"❌ Invalid symbol: {error_message}")
            return
        
        # Set defaults for optional parameters
        direction = direction or "both"
        timeframe = timeframe or "1d"
        
        await interaction.response.defer(thinking=True)
        
        user_id = str(interaction.user.id)
        
        try:
            # Check if symbol is in user's watchlist
            watchlists = await self.bot.watchlist_manager.get_user_watchlists(user_id)
            
            symbol_in_watchlist = False
            watchlist_id = None
            
            for watchlist in watchlists:
                for symbol_item in watchlist.symbols:
                    if symbol_item.symbol.upper() == sanitized_symbol.upper():
                        symbol_in_watchlist = True
                        watchlist_id = watchlist.id
                        break
                if symbol_in_watchlist:
                    break
            
            # If symbol is not in watchlist, add it
            if not symbol_in_watchlist:
                if not watchlists:
                    # Create default watchlist if none exists
                    watchlist_id = await self.bot.watchlist_manager.create_watchlist(user_id, "Default")
                else:
                    watchlist_id = watchlists[0].id
                
                # Add symbol to watchlist
                await self.bot.watchlist_manager.add_symbol_to_watchlist(
                    watchlist_id, sanitized_symbol, 
                    notes=f"Added via /alerts command on {datetime.now().strftime('%Y-%m-%d')}"
                )
            
            # Update alert preferences
            await self.watchlist_alert_manager.update_alert_preferences(
                user_id, sanitized_symbol, alert_type,
                threshold=threshold,
                direction=direction,
                timeframe=timeframe,
                enabled=True
            )
            
            # Create success embed
            embed = discord.Embed(
                title="🔔 Alert Created",
                description=f"Alert for ${sanitized_symbol} has been created",
                color=discord.Color.green()
            )
            
            embed.add_field(
                name="Alert Details",
                value=f"**Type**: {alert_type.replace('_', ' ').title()}\n"
                      f"**Threshold**: {threshold}\n"
                      f"**Direction**: {direction}\n"
                      f"**Timeframe**: {timeframe}",
                inline=False
            )
            
            embed.set_footer(text="You will be notified when this alert is triggered")
            await interaction.followup.send(embed=embed)
            
        except Exception as e:
            logger.error(f"Error creating alert: {e}")
            await interaction.followup.send(
                "❌ An error occurred while creating your alert. Please try again later."
            )
    
    async def _handle_update_alert(
        self, 
        interaction: discord.Interaction,
        symbol: Optional[str],
        alert_type: Optional[str],
        threshold: Optional[float],
        direction: Optional[str],
        timeframe: Optional[str]
    ):
        """Handle updating an existing alert"""
        # Check required parameters
        if not symbol or not alert_type:
            await interaction.response.send_message(
                "❌ Missing required parameters. Please provide symbol and alert_type."
            )
            return
        
        # Sanitize symbol
        sanitized_symbol, is_valid, error_message = InputSanitizer.sanitize_symbol(symbol)
        if not is_valid:
            await interaction.response.send_message(f"❌ Invalid symbol: {error_message}")
            return
        
        await interaction.response.defer(thinking=True)
        
        user_id = str(interaction.user.id)
        
        try:
            # Get user's alert preferences
            alert_prefs = await self.watchlist_alert_manager.get_user_alert_preferences(user_id)
            
            # Check if alert exists
            if sanitized_symbol not in alert_prefs:
                await interaction.followup.send(
                    f"❌ You don't have any alerts set up for {sanitized_symbol}."
                )
                return
            
            # Update alert preferences
            await self.watchlist_alert_manager.update_alert_preferences(
                user_id, sanitized_symbol, alert_type,
                threshold=threshold,
                direction=direction,
                timeframe=timeframe,
                enabled=True
            )
            
            # Create success embed
            embed = discord.Embed(
                title="🔄 Alert Updated",
                description=f"Alert for ${sanitized_symbol} has been updated",
                color=discord.Color.blue()
            )
            
            embed.add_field(
                name="Alert Details",
                value=f"**Type**: {alert_type.replace('_', ' ').title()}\n"
                      f"**Threshold**: {threshold if threshold is not None else 'Unchanged'}\n"
                      f"**Direction**: {direction if direction else 'Unchanged'}\n"
                      f"**Timeframe**: {timeframe if timeframe else 'Unchanged'}",
                inline=False
            )
            
            embed.set_footer(text="You will be notified when this alert is triggered")
            await interaction.followup.send(embed=embed)
            
        except Exception as e:
            logger.error(f"Error updating alert: {e}")
            await interaction.followup.send(
                "❌ An error occurred while updating your alert. Please try again later."
            )
    
    async def _handle_delete_alert(
        self, 
        interaction: discord.Interaction,
        symbol: Optional[str],
        alert_type: Optional[str]
    ):
        """Handle deleting an alert"""
        # Check required parameters
        if not symbol or not alert_type:
            await interaction.response.send_message(
                "❌ Missing required parameters. Please provide symbol and alert_type."
            )
            return
        
        # Sanitize symbol
        sanitized_symbol, is_valid, error_message = InputSanitizer.sanitize_symbol(symbol)
        if not is_valid:
            await interaction.response.send_message(f"❌ Invalid symbol: {error_message}")
            return
        
        await interaction.response.defer(thinking=True)
        
        user_id = str(interaction.user.id)
        
        try:
            # Update alert preferences (disable the alert)
            await self.watchlist_alert_manager.update_alert_preferences(
                user_id, sanitized_symbol, alert_type, enabled=False
            )
            
            # Create success embed
            embed = discord.Embed(
                title="🗑️ Alert Deleted",
                description=f"Alert for ${sanitized_symbol} has been deleted",
                color=discord.Color.red()
            )
            
            embed.add_field(
                name="Alert Details",
                value=f"**Type**: {alert_type.replace('_', ' ').title()}",
                inline=False
            )
            
            await interaction.followup.send(embed=embed)
            
        except Exception as e:
            logger.error(f"Error deleting alert: {e}")
            await interaction.followup.send(
                "❌ An error occurred while deleting your alert. Please try again later."
            )
    
    async def _handle_test_alert(self, interaction: discord.Interaction, symbol: Optional[str]):
        """Handle testing an alert"""
        if not symbol:
            await interaction.response.send_message(
                "❌ Missing required parameter. Please provide a symbol."
            )
            return
        
        # Sanitize symbol
        sanitized_symbol, is_valid, error_message = InputSanitizer.sanitize_symbol(symbol)
        if not is_valid:
            await interaction.response.send_message(f"❌ Invalid symbol: {error_message}")
            return
        
        await interaction.response.defer(thinking=True)
        
        # Create test alert embed
        embed = discord.Embed(
            title="🔔 Test Alert",
            description=f"This is a test alert for ${sanitized_symbol}",
            color=discord.Color.gold(),
            timestamp=discord.utils.utcnow()
        )
        
        embed.add_field(
            name="Price Change Alert",
            value=f"${sanitized_symbol} has moved 5.2% up in the last 24 hours",
            inline=False
        )
        
        embed.add_field(
            name="Current Price",
            value="$150.25 (+$7.45)",
            inline=True
        )
        
        embed.add_field(
            name="Volume",
            value="1.5M (2.3x avg)",
            inline=True
        )
        
        embed.set_footer(text="This is only a test alert. No real market movement detected.")
        
        await interaction.followup.send(embed=embed)


async def setup(bot):
    """Add the alerts commands to the bot"""
    await bot.add_cog(AlertsCommands(bot))
