"""
Utility Commands Extension - Basic bot utility commands.
Contains ping, test, and status commands moved from inline registration.
"""

import discord
from discord.ext import commands
from discord import app_commands
from src.shared.error_handling.logging import get_logger
from src.bot.permissions import PermissionLevel

logger = get_logger(__name__)

class UtilityCommands(commands.Cog):
    """Utility commands for basic bot operations."""
    
    def __init__(self, bot: commands.Bot):
        self.bot = bot
        logger.info("✅ Utility commands extension loaded")
    
    @app_commands.command(name="ping", description="Check bot latency")
    async def ping_command(self, interaction: discord.Interaction):
        """Check bot latency"""
        latency = round(self.bot.latency * 1000)
        await interaction.response.send_message(f"🏓 Pong! Latency: {latency}ms")
    
    @app_commands.command(name="test", description="Test bot functionality")
    async def test_command(self, interaction: discord.Interaction):
        """Test command to verify bot is working"""
        await interaction.response.send_message("✅ Bot is working! All systems operational.")
    
    
    # Status command moved to src.bot.extensions.status to avoid conflicts

async def setup(bot: commands.Bot):
    """Setup the utility commands extension."""
    await bot.add_cog(UtilityCommands(bot))
    logger.info("✅ Utility commands extension loaded")
