"""
Discord Bot Performance Monitor Extension
Provides performance monitoring commands and real-time optimization status
"""

import asyncio
import time
from typing import Dict, Any, Optional
from datetime import datetime, timedelta

import discord
from discord.ext import commands, tasks
from discord import app_commands

from src.shared.error_handling.logging import get_logger
from src.shared.services.enhanced_performance_optimizer import (
    performance_optimizer, 
    get_performance_stats,
    OptimizationConfig
)
from src.bot.utils.embed_builder import EmbedBuilder
from src.bot.utils.permissions import require_admin

logger = get_logger(__name__)


class PerformanceMonitor(commands.Cog):
    """Performance monitoring and optimization commands"""
    
    def __init__(self, bot):
        self.bot = bot
        self.last_performance_check = datetime.now()
        self.performance_alerts_enabled = True
        self.alert_thresholds = {
            "response_time": 5.0,  # seconds
            "memory_usage": 80.0,  # percent
            "cpu_usage": 85.0,     # percent
            "cache_hit_rate": 60.0  # percent (minimum)
        }
        
    async def cog_load(self):
        """Initialize performance monitoring when cog loads"""
        logger.info("Performance Monitor extension loaded")
        self.performance_check_loop.start()
        
    async def cog_unload(self):
        """Cleanup when cog unloads"""
        self.performance_check_loop.cancel()
        logger.info("Performance Monitor extension unloaded")
    
    @app_commands.command(name="performance", description="Show current system performance metrics")
    async def performance_status(self, interaction: discord.Interaction):
        """Display current performance metrics"""
        await interaction.response.defer()
        
        try:
            stats = get_performance_stats()
            
            if stats.get("status") == "no_data":
                embed = EmbedBuilder.error(
                    title="⚠️ Performance Data Unavailable",
                    description="No performance data is currently available. The system may still be initializing."
                )
                await interaction.followup.send(embed=embed)
                return
            
            # Create performance embed
            embed = discord.Embed(
                title="🚀 System Performance Dashboard",
                description="Current performance metrics and optimization status",
                color=0x00d4ff,
                timestamp=datetime.now()
            )
            
            # System Overview
            success_rate = stats.get("success_rate", 0)
            status_emoji = "🟢" if success_rate > 95 else "🟡" if success_rate > 85 else "🔴"
            
            embed.add_field(
                name=f"{status_emoji} System Health",
                value=f"**Success Rate:** {success_rate:.1f}%\n"
                      f"**Total Operations:** {stats.get('total_operations', 0)}\n"
                      f"**Failed Operations:** {stats.get('failed_operations', 0)}",
                inline=True
            )
            
            # Performance Metrics
            avg_time = stats.get("avg_execution_time", 0)
            time_emoji = "🟢" if avg_time < 2 else "🟡" if avg_time < 5 else "🔴"
            
            embed.add_field(
                name=f"{time_emoji} Response Performance",
                value=f"**Avg Response Time:** {avg_time:.2f}s\n"
                      f"**Cache Hit Rate:** {stats.get('cache_hit_rate', 0):.1f}%\n"
                      f"**Query Optimizations:** {stats.get('optimization_stats', {}).get('query_optimizations', 0)}",
                inline=True
            )
            
            # Resource Usage
            resource_usage = stats.get("resource_usage", {})
            cpu_percent = resource_usage.get("cpu_percent", 0)
            memory_percent = resource_usage.get("memory_percent", 0)
            
            cpu_emoji = "🟢" if cpu_percent < 70 else "🟡" if cpu_percent < 85 else "🔴"
            memory_emoji = "🟢" if memory_percent < 70 else "🟡" if memory_percent < 85 else "🔴"
            
            embed.add_field(
                name=f"💻 Resource Usage",
                value=f"{cpu_emoji} **CPU:** {cpu_percent:.1f}%\n"
                      f"{memory_emoji} **Memory:** {memory_percent:.1f}%\n"
                      f"**Available RAM:** {resource_usage.get('memory_available_mb', 0):.0f}MB",
                inline=True
            )
            
            # Optimization Stats
            opt_stats = stats.get("optimization_stats", {})
            embed.add_field(
                name="⚡ Optimizations",
                value=f"**Total Optimizations:** {opt_stats.get('total_optimizations', 0)}\n"
                      f"**Cache Hits:** {opt_stats.get('cache_hits', 0)}\n"
                      f"**Connection Pool Hits:** {opt_stats.get('connection_pool_hits', 0)}",
                inline=True
            )
            
            # Performance Trends
            embed.add_field(
                name="📈 Trends",
                value=f"**Memory Usage:** {stats.get('avg_memory_usage', 0):.1f}MB avg\n"
                      f"**Cache Efficiency:** {'Excellent' if stats.get('cache_hit_rate', 0) > 80 else 'Good' if stats.get('cache_hit_rate', 0) > 60 else 'Needs Improvement'}\n"
                      f"**Last Check:** <t:{int(self.last_performance_check.timestamp())}:R>",
                inline=True
            )
            
            # Footer with recommendations
            if success_rate < 90 or avg_time > 3:
                embed.add_field(
                    name="🔧 Recommendations",
                    value="• Consider enabling aggressive caching\n"
                          "• Review slow operations in logs\n"
                          "• Check resource usage patterns",
                    inline=False
                )
            
            embed.set_footer(text="Performance data updates every 30 seconds")
            
            await interaction.followup.send(embed=embed)
            
        except Exception as e:
            logger.error(f"Error getting performance status: {e}")
            embed = EmbedBuilder.error(
                title="❌ Performance Check Failed",
                description=f"Failed to retrieve performance metrics: {str(e)}"
            )
            await interaction.followup.send(embed=embed)
    
    @app_commands.command(name="optimize", description="Trigger performance optimizations")
    @require_admin()
    async def trigger_optimization(self, interaction: discord.Interaction, 
                                 optimization_type: str = "auto"):
        """Trigger specific performance optimizations"""
        await interaction.response.defer()
        
        try:
            embed = discord.Embed(
                title="⚡ Performance Optimization",
                description=f"Triggering {optimization_type} optimization...",
                color=0x00ff88,
                timestamp=datetime.now()
            )
            
            if optimization_type == "cache":
                # Trigger cache optimization
                embed.add_field(
                    name="🗄️ Cache Optimization",
                    value="• Clearing expired cache entries\n"
                          "• Optimizing cache hit rates\n"
                          "• Adjusting TTL values",
                    inline=False
                )
                
            elif optimization_type == "database":
                # Trigger database optimization
                embed.add_field(
                    name="🗃️ Database Optimization",
                    value="• Optimizing connection pool\n"
                          "• Analyzing slow queries\n"
                          "• Updating query cache",
                    inline=False
                )
                
            elif optimization_type == "memory":
                # Trigger memory optimization
                embed.add_field(
                    name="💾 Memory Optimization",
                    value="• Garbage collection\n"
                          "• Memory leak detection\n"
                          "• Resource cleanup",
                    inline=False
                )
                
            else:  # auto
                # Trigger automatic optimization
                embed.add_field(
                    name="🤖 Automatic Optimization",
                    value="• Analyzing current performance\n"
                          "• Applying best optimizations\n"
                          "• Monitoring improvements",
                    inline=False
                )
            
            embed.add_field(
                name="⏱️ Status",
                value="Optimization in progress... Check back in a few minutes.",
                inline=False
            )
            
            await interaction.followup.send(embed=embed)
            
            # Log optimization trigger
            logger.info(f"Performance optimization triggered by {interaction.user.display_name}: {optimization_type}")
            
        except Exception as e:
            logger.error(f"Error triggering optimization: {e}")
            embed = EmbedBuilder.error(
                title="❌ Optimization Failed",
                description=f"Failed to trigger optimization: {str(e)}"
            )
            await interaction.followup.send(embed=embed)
    
    @app_commands.command(name="performance-config", description="Configure performance monitoring settings")
    @require_admin()
    async def configure_performance(self, interaction: discord.Interaction,
                                  alerts_enabled: bool = None,
                                  response_time_threshold: float = None,
                                  memory_threshold: float = None):
        """Configure performance monitoring settings"""
        await interaction.response.defer()
        
        try:
            changes = []
            
            if alerts_enabled is not None:
                self.performance_alerts_enabled = alerts_enabled
                changes.append(f"Alerts: {'Enabled' if alerts_enabled else 'Disabled'}")
            
            if response_time_threshold is not None:
                self.alert_thresholds["response_time"] = response_time_threshold
                changes.append(f"Response time threshold: {response_time_threshold}s")
            
            if memory_threshold is not None:
                self.alert_thresholds["memory_usage"] = memory_threshold
                changes.append(f"Memory threshold: {memory_threshold}%")
            
            embed = discord.Embed(
                title="⚙️ Performance Configuration Updated",
                description="Performance monitoring settings have been updated.",
                color=0x00d4ff,
                timestamp=datetime.now()
            )
            
            if changes:
                embed.add_field(
                    name="📝 Changes Made",
                    value="\n".join(f"• {change}" for change in changes),
                    inline=False
                )
            
            # Show current configuration
            embed.add_field(
                name="🔧 Current Settings",
                value=f"**Alerts Enabled:** {self.performance_alerts_enabled}\n"
                      f"**Response Time Threshold:** {self.alert_thresholds['response_time']}s\n"
                      f"**Memory Threshold:** {self.alert_thresholds['memory_usage']}%\n"
                      f"**CPU Threshold:** {self.alert_thresholds['cpu_usage']}%\n"
                      f"**Min Cache Hit Rate:** {self.alert_thresholds['cache_hit_rate']}%",
                inline=False
            )
            
            await interaction.followup.send(embed=embed)
            
        except Exception as e:
            logger.error(f"Error configuring performance settings: {e}")
            embed = EmbedBuilder.error(
                title="❌ Configuration Failed",
                description=f"Failed to update configuration: {str(e)}"
            )
            await interaction.followup.send(embed=embed)
    
    @tasks.loop(minutes=5)
    async def performance_check_loop(self):
        """Periodic performance check and alerting"""
        try:
            self.last_performance_check = datetime.now()
            
            if not self.performance_alerts_enabled:
                return
            
            stats = get_performance_stats()
            
            if stats.get("status") == "no_data":
                return
            
            # Check for performance issues
            alerts = []
            
            # Response time check
            avg_time = stats.get("avg_execution_time", 0)
            if avg_time > self.alert_thresholds["response_time"]:
                alerts.append(f"🐌 High response time: {avg_time:.2f}s")
            
            # Memory usage check
            resource_usage = stats.get("resource_usage", {})
            memory_percent = resource_usage.get("memory_percent", 0)
            if memory_percent > self.alert_thresholds["memory_usage"]:
                alerts.append(f"💾 High memory usage: {memory_percent:.1f}%")
            
            # CPU usage check
            cpu_percent = resource_usage.get("cpu_percent", 0)
            if cpu_percent > self.alert_thresholds["cpu_usage"]:
                alerts.append(f"🔥 High CPU usage: {cpu_percent:.1f}%")
            
            # Cache hit rate check
            cache_hit_rate = stats.get("cache_hit_rate", 100)
            if cache_hit_rate < self.alert_thresholds["cache_hit_rate"]:
                alerts.append(f"📉 Low cache hit rate: {cache_hit_rate:.1f}%")
            
            # Send alerts if any issues found
            if alerts:
                await self._send_performance_alert(alerts, stats)
                
        except Exception as e:
            logger.error(f"Performance check loop error: {e}")
    
    async def _send_performance_alert(self, alerts: list, stats: Dict[str, Any]):
        """Send performance alert to appropriate channels"""
        try:
            # This would send to a designated admin channel
            # For now, just log the alert
            logger.warning(f"Performance alerts: {', '.join(alerts)}")
            
        except Exception as e:
            logger.error(f"Failed to send performance alert: {e}")


async def setup(bot):
    """Setup function for the cog"""
    await bot.add_cog(PerformanceMonitor(bot))
