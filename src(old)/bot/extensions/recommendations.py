"""
Enhanced Recommendations Command Implementation

Implements the enhanced recommendations command with user risk profiles
and personalized investment strategies.
"""

import discord
from discord import app_commands
from discord.ext import commands
import asyncio
import logging
from typing import Optional, Dict, Any, List
from datetime import datetime

from src.shared.error_handling.logging import get_logger
from src.bot.permissions import PermissionLevel, require_permission
from src.bot.utils.input_sanitizer import InputSanitizer
from src.bot.pipeline.commands.analyze.parallel_pipeline import execute_parallel_analyze_pipeline
from src.bot.utils.disclaimer_manager import add_disclaimer
logger = get_logger(__name__)

# Risk Profile Classes
class UserRiskProfile:
    """User risk profile for personalized recommendations."""
    
    def __init__(self, user_id: int, risk_level: str, time_horizon: str, 
                 preferred_assets: List[str] = None, position_size: float = 0.1):
        self.user_id = user_id
        self.risk_level = risk_level
        self.time_horizon = time_horizon
        self.preferred_assets = preferred_assets or []
        self.position_size = position_size
        self.created_at = datetime.now()
        self.updated_at = datetime.now()

class UserRiskProfileManager:
    """Manages user risk profiles."""
    
    def __init__(self, db_pool):
        self.db_pool = db_pool
    
    async def get_profile(self, user_id: int) -> Optional[UserRiskProfile]:
        """Get user risk profile."""
        # Default profile for now
        return UserRiskProfile(
            user_id=user_id,
            risk_level="moderate",
            time_horizon="medium-term",
            preferred_assets=["stocks", "etfs"]
        )
    
    async def save_profile(self, profile: UserRiskProfile):
        """Save user risk profile."""
        # Placeholder implementation
        pass

# Risk Profile Definitions
RISK_PROFILES = {
    "conservative": {
        "description": "Low risk, stable returns, capital preservation focus",
        "max_position_size": 0.05,
        "preferred_assets": ["bonds", "etfs", "blue_chips"]
    },
    "moderate": {
        "description": "Balanced risk-return, diversified portfolio approach",
        "max_position_size": 0.1,
        "preferred_assets": ["stocks", "etfs", "reits"]
    },
    "aggressive": {
        "description": "High risk, high return, growth-focused strategy",
        "max_position_size": 0.2,
        "preferred_assets": ["stocks", "crypto", "options"]
    }
}

class EnhancedRecommendationsCommands(commands.Cog):
    """Enhanced recommendations commands with user risk profiles"""
    
    def __init__(self, bot):
        self.bot = bot
        self.permission_checker = getattr(bot, 'permission_checker', None)
        self.db_pool = getattr(bot, 'db_pool', None)
        
        # Initialize risk profile manager
        self.risk_profile_manager = UserRiskProfileManager(self.db_pool)
        
        # Maximum number of concurrent analyses
        self.max_concurrent_analyses = 3
        self.semaphore = asyncio.Semaphore(self.max_concurrent_analyses)
    
    @app_commands.command(name="recommendations", description="Get personalized trading recommendations based on your risk profile")
    @app_commands.describe(
        symbol="Stock symbol to analyze (e.g., AAPL, MSFT, TSLA)",
        include_backtesting="Include basic backtesting results"
    )
    async def recommendations_command(
        self, 
        interaction: discord.Interaction, 
        symbol: str,
        include_backtesting: Optional[bool] = False
    ):
        """
        Get personalized trading recommendations based on your risk profile
        
        Parameters:
        -----------
        symbol: str
            Stock symbol to analyze
        include_backtesting: bool, optional
            Include basic backtesting results
        """
        # Check if user has paid access
        has_permission, reason = self.permission_checker.has_permission(
            interaction.user, PermissionLevel.PAID, None, str(interaction.guild_id) if interaction.guild_id else None
        )
        
        if not has_permission:
            await interaction.response.send_message(
                f"❌ This command requires paid tier access: {reason}"
            )
            return
        
        # Sanitize symbol
        sanitized_symbol, is_valid, error_message = InputSanitizer.sanitize_symbol(symbol)
        if not is_valid:
            await interaction.response.send_message(f"❌ Invalid symbol: {error_message}")
            return
        
        # Defer response to allow for processing time
        await interaction.response.defer(thinking=True)
        
        try:
            # Get user risk profile
            user_id = str(interaction.user.id)
            user_profile = await self.risk_profile_manager.get_user_profile(user_id)
            
            # Send initial message
            await interaction.followup.send(
                f"🔍 Generating personalized recommendations for {sanitized_symbol} based on your {user_profile.risk_level} risk profile..."
            )
            
            # Execute analysis with semaphore to limit concurrency
            async with self.semaphore:
                # Execute parallel analyze pipeline
                context = await execute_parallel_analyze_pipeline(
                    ticker=sanitized_symbol,
                    user_id=user_id,
                    guild_id=str(interaction.guild_id) if interaction.guild_id else None,
                    correlation_id=f"recommendations_{sanitized_symbol}_{datetime.now().timestamp()}"
                )
                
                # Create personalized recommendations
                recommendations = self._generate_recommendations(
                    sanitized_symbol,
                    context,
                    user_profile,
                    include_backtesting
                )
                
                # Create embed
                embed = self._create_recommendations_embed(
                    sanitized_symbol,
                    recommendations,
                    interaction.user,
                    user_profile
                )
                
                # Add disclaimer
                embed.set_footer(text="DISCLAIMER: These are algorithmic suggestions based on technical analysis only, not personalized investment advice. Always conduct your own research before making investment decisions.")
                
                # Send the response
                await interaction.followup.send(embed=embed)
                
        except asyncio.TimeoutError:
            await interaction.followup.send(
                "⏰ The recommendations analysis took longer than expected. Please try again later."
            )
        except Exception as e:
            logger.error(f"Error in recommendations command: {e}")
            await interaction.followup.send(
                f"❌ I encountered an error while generating recommendations for {sanitized_symbol}. Please try again later."
            )
    
    @app_commands.command(name="riskprofile", description="View or update your risk profile for personalized recommendations")
    @app_commands.describe(
        risk_level="Your risk tolerance level",
        max_position_size="Maximum position size as percentage of portfolio",
        stop_loss="Default stop loss percentage",
        take_profit="Default take profit percentage"
    )
    @app_commands.choices(
        risk_level=[
            app_commands.Choice(name="Conservative", value="conservative"),
            app_commands.Choice(name="Moderate", value="moderate"),
            app_commands.Choice(name="Aggressive", value="aggressive")
        ],
        time_horizon=[
            app_commands.Choice(name="Short-term", value="short-term"),
            app_commands.Choice(name="Medium-term", value="medium-term"),
            app_commands.Choice(name="Long-term", value="long-term")
        ]
    )
    async def risk_profile_command(
        self, 
        interaction: discord.Interaction,
        risk_level: Optional[str] = None,
        max_position_size: Optional[float] = None,
        stop_loss: Optional[float] = None,
        take_profit: Optional[float] = None,
        time_horizon: Optional[str] = None
    ):
        """
        View or update your risk profile for personalized recommendations
        
        Parameters:
        -----------
        risk_level: str, optional
            Your risk tolerance level
        max_position_size: float, optional
            Maximum position size as percentage of portfolio
        stop_loss: float, optional
            Default stop loss percentage
        take_profit: float, optional
            Default take profit percentage
        time_horizon: str, optional
            Your investment time horizon
        """
        # Check if user has paid access
        has_permission, reason = self.permission_checker.has_permission(
            interaction.user, PermissionLevel.PAID, None, str(interaction.guild_id) if interaction.guild_id else None
        )
        
        if not has_permission:
            await interaction.response.send_message(
                f"❌ This command requires paid tier access: {reason}"
            )
            return
        
        # Defer response to allow for processing time
        await interaction.response.defer(thinking=True)
        
        try:
            user_id = str(interaction.user.id)
            
            # If parameters are provided, update the profile
            if any([risk_level, max_position_size, stop_loss, take_profit, time_horizon]):
                # Validate parameters
                if max_position_size is not None and (max_position_size <= 0 or max_position_size > 100):
                    await interaction.followup.send("❌ Position size must be between 0 and 100 percent.")
                    return
                
                if stop_loss is not None and (stop_loss <= 0 or stop_loss > 50):
                    await interaction.followup.send("❌ Stop loss must be between 0 and 50 percent.")
                    return
                
                if take_profit is not None and (take_profit <= 0 or take_profit > 100):
                    await interaction.followup.send("❌ Take profit must be between 0 and 100 percent.")
                    return
                
                # Update profile
                success = await self.risk_profile_manager.update_user_profile(
                    user_id=user_id,
                    risk_level=risk_level,
                    max_position_size=max_position_size,
                    stop_loss=stop_loss,
                    take_profit=take_profit,
                    time_horizon=time_horizon
                )
                
                if not success:
                    await interaction.followup.send("❌ Failed to update your risk profile. Please try again later.")
                    return
            
            # Get updated profile
            user_profile = await self.risk_profile_manager.get_user_profile(user_id)
            
            # Create embed
            embed = self._create_risk_profile_embed(user_profile, interaction.user)
            
            # Send response
            await interaction.followup.send(embed=embed)
            
        except Exception as e:
            logger.error(f"Error in risk profile command: {e}")
            await interaction.followup.send(
                "❌ I encountered an error while managing your risk profile. Please try again later."
            )
    
    def _generate_recommendations(
        self,
        symbol: str,
        context,
        user_profile: UserRiskProfile,
        include_backtesting: bool
    ) -> Dict[str, Any]:
        """
        Generate personalized recommendations based on analysis and user profile
        
        Args:
            symbol: Symbol being analyzed
            context: Analysis context
            user_profile: User risk profile
            include_backtesting: Whether to include backtesting results
            
        Returns:
            Dictionary with personalized recommendations
        """
        recommendations = {
            "symbol": symbol,
            "risk_level": user_profile.risk_level,
            "entry_points": [],
            "exit_points": [],
            "position_size": 0.0,
            "stop_loss": 0.0,
            "take_profit": 0.0,
            "time_horizon": user_profile.time_horizon,
            "signal": "neutral",
            "confidence": 0,
            "backtesting": {}
        }
        
        try:
            # Extract data from context
            if hasattr(context, "processing_results"):
                # Get market data
                market_data = context.processing_results.get("market_data", {})
                current_price = market_data.get("current_price", 0)
                
                # Get technical analysis data
                ta_data = context.processing_results.get("technical_analysis", {})
                
                if ta_data and current_price > 0:
                    # Extract trend and signal
                    trend = ta_data.get("trend", "sideways")
                    signal = ta_data.get("signal", "neutral")
                    
                    # Extract support and resistance levels
                    support_levels = ta_data.get("support_levels", [])
                    resistance_levels = ta_data.get("resistance_levels", [])
                    
                    # Find closest support and resistance
                    closest_support = None
                    for level in support_levels:
                        if level < current_price and (closest_support is None or level > closest_support):
                            closest_support = level
                    
                    closest_resistance = None
                    for level in resistance_levels:
                        if level > current_price and (closest_resistance is None or level < closest_resistance):
                            closest_resistance = level
                    
                    # Calculate risk/reward based on user profile
                    if closest_support and closest_resistance:
                        # Calculate position size based on risk/reward and user profile
                        risk = (current_price - closest_support) / current_price * 100
                        reward = (closest_resistance - current_price) / current_price * 100
                        
                        if risk > 0:
                            risk_reward_ratio = reward / risk
                            
                            # Adjust position size based on risk/reward ratio and user profile
                            base_position = user_profile.max_position_size
                            if risk_reward_ratio >= 3.0:
                                position_size = base_position
                            elif risk_reward_ratio >= 2.0:
                                position_size = base_position * 0.8
                            elif risk_reward_ratio >= 1.5:
                                position_size = base_position * 0.6
                            elif risk_reward_ratio >= 1.0:
                                position_size = base_position * 0.4
                            else:
                                position_size = base_position * 0.2
                            
                            # Set recommendations
                            recommendations["position_size"] = round(position_size, 1)
                            recommendations["stop_loss"] = round(risk, 1)
                            recommendations["take_profit"] = round(reward, 1)
                            recommendations["risk_reward_ratio"] = round(risk_reward_ratio, 2)
                    
                    # Set entry and exit points
                    if signal == "buy":
                        # Entry points
                        recommendations["entry_points"] = [
                            {"price": current_price, "type": "market", "notes": "Immediate entry based on bullish signal"},
                            {"price": current_price * 0.98, "type": "limit", "notes": "Limit order on small pullback"}
                        ]
                        
                        # Exit points
                        if closest_resistance:
                            recommendations["exit_points"] = [
                                {"price": closest_resistance, "type": "take_profit", "notes": "Take profit at resistance"}
                            ]
                        
                        if closest_support:
                            recommendations["exit_points"].append(
                                {"price": closest_support, "type": "stop_loss", "notes": "Stop loss at support"}
                            )
                    
                    elif signal == "sell":
                        # Entry points for short position
                        recommendations["entry_points"] = [
                            {"price": current_price, "type": "market", "notes": "Immediate short entry based on bearish signal"},
                            {"price": current_price * 1.02, "type": "limit", "notes": "Limit order on small bounce"}
                        ]
                        
                        # Exit points
                        if closest_support:
                            recommendations["exit_points"] = [
                                {"price": closest_support, "type": "take_profit", "notes": "Take profit at support"}
                            ]
                        
                        if closest_resistance:
                            recommendations["exit_points"].append(
                                {"price": closest_resistance, "type": "stop_loss", "notes": "Stop loss at resistance"}
                            )
                    
                    # Set signal and confidence
                    recommendations["signal"] = signal
                    
                    # Calculate confidence based on multiple factors
                    confidence = 50  # Base confidence
                    
                    # Adjust based on trend alignment
                    if (signal == "buy" and trend == "uptrend") or (signal == "sell" and trend == "downtrend"):
                        confidence += 20
                    elif (signal == "buy" and trend == "downtrend") or (signal == "sell" and trend == "uptrend"):
                        confidence -= 20
                    
                    # Adjust based on risk/reward ratio
                    if "risk_reward_ratio" in recommendations:
                        rr_ratio = recommendations["risk_reward_ratio"]
                        if rr_ratio >= 3.0:
                            confidence += 20
                        elif rr_ratio >= 2.0:
                            confidence += 10
                        elif rr_ratio < 1.0:
                            confidence -= 10
                    
                    # Ensure confidence is between 0 and 100
                    recommendations["confidence"] = max(0, min(100, confidence))
                    
                    # Add backtesting results if requested
                    if include_backtesting:
                        recommendations["backtesting"] = self._generate_backtesting_results(
                            symbol, ta_data, signal, user_profile
                        )
        
        except Exception as e:
            logger.error(f"Error generating recommendations: {e}")
        
        return recommendations
    
    def _generate_backtesting_results(
        self,
        symbol: str,
        ta_data: Dict[str, Any],
        signal: str,
        user_profile: UserRiskProfile
    ) -> Dict[str, Any]:
        """
        Generate simple backtesting results
        
        Args:
            symbol: Symbol being analyzed
            ta_data: Technical analysis data
            signal: Trading signal
            user_profile: User risk profile
            
        Returns:
            Dictionary with backtesting results
        """
        # Default results
        results = {
            "win_rate": 0.0,
            "profit_factor": 0.0,
            "average_return": 0.0,
            "max_drawdown": 0.0,
            "trades": 0,
            "time_period": "6 months"
        }
        
        try:
            # Generate simulated backtesting results based on technical indicators
            # This is a simplified simulation for demonstration purposes
            
            # Base win rate on signal strength and risk profile
            base_win_rate = 50.0
            
            # Adjust based on signal
            if signal == "buy":
                base_win_rate += 10.0
            elif signal == "sell":
                base_win_rate -= 5.0
            
            # Adjust based on risk profile
            if user_profile.risk_level == "conservative":
                base_win_rate += 5.0
                avg_return = 2.5
                max_drawdown = 5.0
                trades = 12
            elif user_profile.risk_level == "aggressive":
                base_win_rate -= 10.0
                avg_return = 8.0
                max_drawdown = 15.0
                trades = 48
            else:  # moderate
                avg_return = 4.0
                max_drawdown = 10.0
                trades = 24
            
            # Calculate profit factor based on win rate and average return
            win_rate = min(85.0, max(30.0, base_win_rate))
            profit_factor = (win_rate / 100.0 * avg_return) / ((100.0 - win_rate) / 100.0 * avg_return * 0.8)
            
            # Set results
            results["win_rate"] = round(win_rate, 1)
            results["profit_factor"] = round(profit_factor, 2)
            results["average_return"] = round(avg_return, 1)
            results["max_drawdown"] = round(max_drawdown, 1)
            results["trades"] = trades
            
        except Exception as e:
            logger.error(f"Error generating backtesting results: {e}")
        
        return results
    
    def _create_recommendations_embed(
        self,
        symbol: str,
        recommendations: Dict[str, Any],
        user: discord.User,
        user_profile: UserRiskProfile
    ) -> discord.Embed:
        """
        Create embed for personalized recommendations
        
        Args:
            symbol: Symbol being analyzed
            recommendations: Recommendations data
            user: User who requested the recommendations
            user_profile: User risk profile
            
        Returns:
            Discord embed with personalized recommendations
        """
        # Determine color based on signal
        signal = recommendations.get("signal", "neutral")
        if signal == "buy":
            color = discord.Color.green()
        elif signal == "sell":
            color = discord.Color.red()
        else:
            color = discord.Color.gold()
        
        # Create embed
        embed = discord.Embed(
            title=f"Trading Recommendations: ${symbol}",
            description=f"Personalized recommendations based on your {user_profile.risk_level} risk profile",
            color=color,
            timestamp=discord.utils.utcnow()
        )
        
        # Add signal and confidence
        confidence = recommendations.get("confidence", 0)
        confidence_emoji = "🟢" if confidence >= 70 else "🟡" if confidence >= 40 else "🔴"
        
        embed.add_field(
            name="Signal",
            value=f"{signal.upper()} {confidence_emoji}",
            inline=True
        )
        
        embed.add_field(
            name="Confidence",
            value=f"{confidence}%",
            inline=True
        )
        
        embed.add_field(
            name="Time Horizon",
            value=recommendations.get("time_horizon", "medium-term").replace("-", " ").title(),
            inline=True
        )
        
        # Add position sizing and risk management
        position_size = recommendations.get("position_size", 0.0)
        stop_loss = recommendations.get("stop_loss", 0.0)
        take_profit = recommendations.get("take_profit", 0.0)
        risk_reward_ratio = recommendations.get("risk_reward_ratio", 0.0)
        
        risk_management = ""
        if position_size > 0:
            risk_management += f"• Position Size: {position_size}% of portfolio\n"
        if stop_loss > 0:
            risk_management += f"• Stop Loss: {stop_loss}%\n"
        if take_profit > 0:
            risk_management += f"• Take Profit: {take_profit}%\n"
        if risk_reward_ratio > 0:
            risk_management += f"• Risk/Reward: {risk_reward_ratio}\n"
        
        if risk_management:
            embed.add_field(
                name="Risk Management",
                value=risk_management,
                inline=False
            )
        
        # Add entry points
        entry_points = recommendations.get("entry_points", [])
        if entry_points:
            entry_text = ""
            for i, entry in enumerate(entry_points):
                entry_text += f"• ${entry.get('price', 0):.2f} ({entry.get('type', '').title()})\n"
                if entry.get("notes"):
                    entry_text += f"  *{entry.get('notes')}*\n"
            
            embed.add_field(
                name="Entry Points",
                value=entry_text,
                inline=True
            )
        
        # Add exit points
        exit_points = recommendations.get("exit_points", [])
        if exit_points:
            exit_text = ""
            for i, exit in enumerate(exit_points):
                exit_text += f"• ${exit.get('price', 0):.2f} ({exit.get('type', '').title()})\n"
                if exit.get("notes"):
                    exit_text += f"  *{exit.get('notes')}*\n"
            
            embed.add_field(
                name="Exit Points",
                value=exit_text,
                inline=True
            )
        
        # Add backtesting results if available
        backtesting = recommendations.get("backtesting", {})
        if backtesting:
            backtesting_text = (
                f"• Win Rate: {backtesting.get('win_rate', 0)}%\n"
                f"• Profit Factor: {backtesting.get('profit_factor', 0)}\n"
                f"• Avg Return: {backtesting.get('average_return', 0)}%\n"
                f"• Max Drawdown: {backtesting.get('max_drawdown', 0)}%\n"
                f"• Trades: {backtesting.get('trades', 0)}\n"
                f"• Period: {backtesting.get('time_period', '6 months')}"
            )
            
            embed.add_field(
                name="Backtesting Results",
                value=backtesting_text,
                inline=False
            )
        
        # Add author
        embed.set_author(
            name=f"Requested by {user.display_name}",
            icon_url=user.display_avatar.url if hasattr(user, 'display_avatar') else None
        )
        
        return embed
    
    def _create_risk_profile_embed(
        self,
        profile: UserRiskProfile,
        user: discord.User
    ) -> discord.Embed:
        """
        Create embed for user risk profile
        
        Args:
            profile: User risk profile
            user: User who requested the profile
            
        Returns:
            Discord embed with risk profile information
        """
        # Determine color based on risk level
        if profile.risk_level == "conservative":
            color = discord.Color.blue()
        elif profile.risk_level == "aggressive":
            color = discord.Color.red()
        else:  # moderate
            color = discord.Color.gold()
        
        # Create embed
        embed = discord.Embed(
            title="Your Risk Profile",
            description=f"Your personalized trading risk profile",
            color=color,
            timestamp=discord.utils.utcnow()
        )
        
        # Add risk level
        embed.add_field(
            name="Risk Level",
            value=profile.risk_level.title(),
            inline=True
        )
        
        # Add time horizon
        embed.add_field(
            name="Time Horizon",
            value=profile.time_horizon.replace("-", " ").title(),
            inline=True
        )
        
        # Add position sizing and risk management
        embed.add_field(
            name="Position Size",
            value=f"{profile.max_position_size}% of portfolio",
            inline=True
        )
        
        embed.add_field(
            name="Stop Loss",
            value=f"{profile.stop_loss}%",
            inline=True
        )
        
        embed.add_field(
            name="Take Profit",
            value=f"{profile.take_profit}%",
            inline=True
        )
        
        # Add preferred assets
        if profile.preferred_assets:
            preferred_assets = ", ".join([asset.replace("_", " ").title() for asset in profile.preferred_assets])
            embed.add_field(
                name="Preferred Assets",
                value=preferred_assets,
                inline=False
            )
        
        # Add profile description
        profile_description = RISK_PROFILES.get(profile.risk_level, {}).get("description", "")
        if profile_description:
            embed.add_field(
                name="Profile Description",
                value=profile_description,
                inline=False
            )
        
        # Add instructions for updating
        embed.add_field(
            name="Update Your Profile",
            value="Use `/riskprofile` with parameters to update your risk profile settings.",
            inline=False
        )
        
        # Add author
        embed.set_author(
            name=f"{user.display_name}'s Risk Profile",
            icon_url=user.display_avatar.url if hasattr(user, 'display_avatar') else None
        )
        
        return embed


async def setup(bot):
    """Add the enhanced recommendations commands to the bot"""
    await bot.add_cog(EnhancedRecommendationsCommands(bot))
