"""
Error handler extension - Global error handling for all commands.
"""

import discord
from discord.ext import commands
from discord import app_commands
import traceback
import asyncio
from src.shared.error_handling.logging import get_logger

logger = get_logger(__name__)

async def on_app_command_error(interaction: discord.Interaction, error: app_commands.AppCommandError):
    """Global handler for application command errors."""
    command_name = interaction.command.name if interaction.command else 'unknown command'

    # Log with maximum detail
    logger.error(f"🚨 APP COMMAND ERROR in /{command_name}")
    logger.error(f"Error type: {type(error)}")
    logger.error(f"Error message: {error}")

    # Get full traceback
    tb_str = ''.join(traceback.format_exception(type(error), error, error.__traceback__))
    logger.error(f"Full traceback for /{command_name}:\n{tb_str}")

    # Unwrap the original error if it's a CommandInvokeError
    original_error = getattr(error, 'original', error)

    # Determine the user-facing message
    user_message = f"❌ An unexpected error occurred while processing `/{command_name}`. The team has been notified."
    ephemeral = True

    if isinstance(original_error, asyncio.TimeoutError):
        user_message = "⏰ The command took too long to respond. Please try again."
    elif isinstance(error, app_commands.CommandOnCooldown):
        user_message = f"⏳ This command is on cooldown. Please try again in {error.retry_after:.1f} seconds."
    elif isinstance(error, (app_commands.CheckFailure, app_commands.MissingPermissions)):
        user_message = "🚫 You do not have the required permissions for this command."
    elif isinstance(original_error, discord.errors.NotFound) and original_error.code == 10062:
        # This is the "Unknown Interaction" error. It means the bot took too long to respond (> 3s).
        logger.warning(f"Could not respond to '{command_name}' because the interaction expired. This may indicate a performance issue.")
        return  # Exit early, can't send a message.

    # Send user-friendly error message
    try:
        if interaction.response.is_done():
            await interaction.followup.send(user_message, ephemeral=ephemeral)
        else:
            await interaction.response.send_message(user_message, ephemeral=ephemeral)
    except (discord.errors.InteractionResponded, discord.errors.NotFound) as e:
        logger.warning(f"Could not send error message for '{command_name}' because the interaction was already handled or expired: {e}")
    except Exception as e:
        logger.error(f"Failed to send error message: {e}")

async def setup(bot: commands.Bot):
    """Setup the error handler extension."""
    bot.tree.error(on_app_command_error)
    logger.info("✅ Error handler extension loaded")
