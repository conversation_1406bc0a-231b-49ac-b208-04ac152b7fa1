"""Real-Time Alert Manager for Market Events

Monitors live data from buffer_manager and triggers Discord notifications
on significant events like price surges, volatility spikes, or indicator crossovers.

Integrates with Discord bot for async message sending.
Configurable thresholds from config.yaml.

Dependencies: Assume discord.py is in requirements.txt for bot integration.
"""

import asyncio
import logging
from typing import Callable, Dict, Optional
from datetime import datetime

from src.shared.data_pipeline.buffer_manager import DataBuffer
from src.shared.error_handling.logging import get_trading_logger
from src.bot.discord_bot import BotClient  # Assuming existing bot client; adjust import if needed
from src.shared.config_loader import load_config  # Assuming config loader

logger = get_trading_logger(__name__)

class AlertManager:
    """
    Manages real-time alerts based on live market data.
    
    Subscribes to buffer updates, checks against thresholds, and sends Discord alerts.
    
    Usage:
        manager = AlertManager(bot_client=bot, buffer=DataBuffer())
        manager.on_alert_trigger = lambda alert: print(alert)
        await manager.start_monitoring(symbols=["AAPL"])
    """
    
    def __init__(self, bot_client: BotClient, buffer: DataBuffer, channel_id: str):
        self.bot = bot_client
        self.buffer = buffer
        self.channel_id = channel_id
        self.monitoring_symbols: set = set()
        self.on_alert_trigger: Optional[Callable[[Dict], None]] = None
        self.is_monitoring = False
        
        # Load thresholds from config
        config = load_config()
        self.thresholds = config.get('alerts', {
            'price_change_pct': 5.0,  # Alert if >5% change in last 5min
            'volatility_high': 2.0,   # Std dev >2% of price
            'rsi_overbought': 70,     # If RSI implemented
        })
    
    async def start_monitoring(self, symbols: list):
        """Start monitoring symbols for alerts."""
        self.monitoring_symbols.update(symbols)
        self.is_monitoring = True
        logger.info(f"Started alert monitoring for {symbols}")
        
        # Background task to poll buffer every 10s (or use event-driven in full impl)
        asyncio.create_task(self._alert_loop())
    
    async def stop_monitoring(self, symbols: Optional[list] = None):
        """Stop monitoring specific or all symbols."""
        if symbols:
            self.monitoring_symbols.difference_update(symbols)
        else:
            self.monitoring_symbols.clear()
        if not self.monitoring_symbols:
            self.is_monitoring = False
        logger.info(f"Stopped monitoring for {symbols or 'all'}")
    
    async def _alert_loop(self):
        """Polling loop to check for alert conditions."""
        while self.is_monitoring:
            for symbol in list(self.monitoring_symbols):
                await self._check_alerts(symbol)
            await asyncio.sleep(10)  # Poll every 10s; optimize with events later
    
    async def _check_alerts(self, symbol: str):
        """Check buffer for alert conditions on a symbol."""
        latest = self.buffer.get_latest(symbol)
        if not latest:
            return
        
        buffer_history = self.buffer.get_buffer(symbol)
        if len(buffer_history) < 5:  # Need min history
            return
        
        # Price change alert
        prices = [item['price'] for item in buffer_history[-5:]]  # Last 5 updates
        change_pct = (prices[-1] - prices[0]) / prices[0] * 100 if prices[0] else 0
        if abs(change_pct) > self.thresholds['price_change_pct']:
            alert = {
                'symbol': symbol,
                'type': 'price_surge' if change_pct > 0 else 'price_drop',
                'change_pct': round(change_pct, 2),
                'price': prices[-1],
                'timestamp': datetime.utcnow().isoformat()
            }
            await self._send_alert(alert)
        
        # Volatility alert
        indicators = self.buffer.compute_indicators(symbol, window=10)
        if indicators and indicators['volatility'] > self.thresholds['volatility_high']:
            alert = {
                'symbol': symbol,
                'type': 'high_volatility',
                'volatility': indicators['volatility'],
                'price': latest['live_price'],
                'timestamp': datetime.utcnow().isoformat()
            }
            await self._send_alert(alert)
        
        # Trigger custom callback
        if self.on_alert_trigger:
            self.on_alert_trigger(alert)
    
    async def _send_alert(self, alert: Dict):
        """Send alert to Discord channel."""
        try:
            message = f"🚨 **{alert['type'].upper()} Alert** for {alert['symbol']}!\n"
            message += f"Current Price: ${alert['price']}\n"
            if 'change_pct' in alert:
                message += f"Change: {alert['change_pct']:.2f}%\n"
            if 'volatility' in alert:
                message += f"Volatility: {alert['volatility']:.4f}\n"
            message += f"Time: {alert['timestamp']}"
            
            await self.bot.send_message(self.channel_id, message)
            logger.info(f"Sent Discord alert: {alert}")
        except Exception as e:
            logger.error(f"Failed to send alert for {alert['symbol']}: {e}")
    
    # Event-driven alternative: Callback from WebSocket/buffer
    def register_update_callback(self, callback: Callable[[Dict], None]):
        """Register a callback for immediate alerts on updates (faster than polling)."""
        self.on_update_callback = callback
        # In integration, set buffer.add_update to call this after adding


# Example usage (for testing)
async def example_usage():
    # Mock bot and buffer
    class MockBot:
        async def send_message(self, channel, msg):
            print(f"Mock Discord: {msg}")
    
    buffer = DataBuffer(max_size=10)
    manager = AlertManager(bot_client=MockBot(), buffer=buffer, channel_id="12345")
    
    # Simulate updates
    for i in range(5):
        update = {
            "symbol": "AAPL",
            "price": 150 + i * 2,  # Simulating surge
            "size": 100,
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "type": "T"
        }
        buffer.add_update(update)
    
    await manager.start_monitoring(["AAPL"])
    await asyncio.sleep(15)  # Allow one poll
    await manager.stop_monitoring(["AAPL"])

if __name__ == "__main__":
    asyncio.run(example_usage())