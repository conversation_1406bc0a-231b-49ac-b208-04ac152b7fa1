"""
Database Manager for Discord Bot
Provides database connection and management for the bot with improved reliability
"""

import asyncio
import os
import logging
import time
from typing import Optional, Dict, Any, List
import structlog
from contextlib import asynccontextmanager

# Use the unified database manager (canonical location)
from src.database.unified_db import UnifiedDatabaseManager, database_manager as shared_db_manager, initialize_database, test_connections

logger = structlog.get_logger(__name__)

class DatabaseManager:
    """Manages database connections for the Discord bot with retry logic and improved pooling"""
    
    def __init__(self):
        self.max_retries = int(os.getenv('DB_MAX_RETRIES', '5'))
        self.retry_delay = float(os.getenv('DB_RETRY_DELAY', '1.5'))
        self.last_connection_attempt = 0
        self.connection_status = {
            'healthy': False,
            'last_error': None,
            'reconnect_attempts': 0,
            'last_successful_connection': 0
        }
    
    async def initialize(self) -> bool:
        """Initialize database connection pool with retry logic"""
        self.last_connection_attempt = time.time()
        
        # Check if we're in development mode and should skip DB connection
        if os.getenv('SKIP_DB_CONNECTION', 'false').lower() == 'true':
            logger.warning("Database connection skipped due to SKIP_DB_CONNECTION=true")
            self.connection_status['healthy'] = False
            self.connection_status['last_error'] = "Database connection skipped in development mode"
            return True  # Return True to allow the application to continue
        
        # Use the unified database manager
        try:
            # Initialize database connections
            await initialize_database()

            # Test connections to verify they work
            test_results = await test_connections()

            if test_results.get('supabase', False):
                self.connection_status['healthy'] = True
                self.connection_status['last_error'] = None
                self.connection_status['last_successful_connection'] = time.time()
                logger.info("Database connection initialized successfully via unified manager")
                return True
            else:
                self.connection_status['healthy'] = False
                self.connection_status['last_error'] = "Database connection test failed"
                logger.error("Database connection test failed via unified manager")
                return False
            
        except Exception as e:
            self.connection_status['healthy'] = False
            self.connection_status['last_error'] = str(e)
            self.connection_status['reconnect_attempts'] += 1
            logger.error(f"Database connection initialization failed: {e}")
            return False
    
    async def close(self):
        """Close database connection pool"""
        try:
            # The unified database manager handles connection cleanup automatically
            logger.info("Database connection pool closed via unified manager")
        except Exception as e:
            logger.error(f"Error closing database connection pool: {e}")

    def get_supabase_client(self):
        """Get the Supabase client instance"""
        from src.database.unified_db import get_supabase_client
        return get_supabase_client()
    
    async def test_connection(self) -> bool:
        """Test if database connection is working"""
        try:
            # Use the unified database manager's health check
            health_status = await shared_db_manager.health_check()

            if health_status:
                self.connection_status['healthy'] = True
                self.connection_status['last_successful_connection'] = time.time()
                self.connection_status['last_error'] = None
                return True
            else:
                self.connection_status['healthy'] = False
                self.connection_status['last_error'] = "Database health check failed"
                logger.error("Database connection test failed")
                return False
            
        except Exception as e:
            self.connection_status['healthy'] = False
            self.connection_status['last_error'] = str(e)
            logger.error(f"Database connection test failed: {e}")
            return False
    
    @asynccontextmanager
    async def safe_connection(self):
        """Get a database connection with automatic retry on failure"""
        client = await self.get_supabase_client()
        if not client:
            raise RuntimeError("Database connection not available. Call initialize() first.")
        
        for attempt in range(1, self.max_retries + 1):
            try:
                # For Supabase, we just yield the client
                yield client
                return
            except Exception as e:
                logger.error(f"Database operation failed (attempt {attempt}/{self.max_retries}): {e}")
                
                if attempt < self.max_retries:
                    # Exponential backoff with jitter
                    delay = self.retry_delay * (2 ** (attempt - 1)) * (0.5 + 0.5 * (time.time() % 1))
                    logger.info(f"Retrying database operation in {delay:.2f} seconds...")
                    await asyncio.sleep(delay)
                else:
                    logger.critical(f"Database operation failed after {self.max_retries} attempts")
                    raise
    
    async def execute_query(self, table: str, query: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Execute a database query with retry logic"""
        try:
            client = await self.get_supabase_client()
            if client:
                return await client.query_data(table, query)
            else:
                raise ConnectionError("Supabase client not available")
        except Exception as e:
            logger.error(f"Query execution failed: {e}")
            raise
    
    async def fetch_all(self, table: str, query: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Fetch all rows from a query with retry logic"""
        try:
            return await self.execute_query(table, query)
        except Exception as e:
            logger.error(f"Fetch operation failed: {e}")
            return []
    
    async def fetch_one(self, table: str, query: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Fetch a single row from a query with retry logic"""
        try:
            results = await self.execute_query(table, query)
            return results[0] if results else None
        except Exception as e:
            logger.error(f"Fetch operation failed: {e}")
            return None
    
    async def insert_data(self, table: str, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Insert data into a database table"""
        try:
            client = await self.get_supabase_client()
            if client:
                return await client.insert_data(table, data)
            else:
                raise ConnectionError("Supabase client not available")
        except Exception as e:
            logger.error(f"Insert operation failed: {e}")
            raise
    
    async def update_data(self, table: str, match_criteria: Dict[str, Any], update_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Update data in a database table"""
        try:
            client = await self.get_supabase_client()
            if client:
                return await client.update_data(table, match_criteria, update_data)
            else:
                raise ConnectionError("Supabase client not available")
        except Exception as e:
            logger.error(f"Update operation failed: {e}")
            raise
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get detailed health status of the database connection"""
        return {
            **self.connection_status,
            'pool_initialized': True,  # Always true with centralized manager
        }
        
# Global database manager instance
bot_db_manager = DatabaseManager()
