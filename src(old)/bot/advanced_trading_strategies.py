"""
Advanced Trading Strategies System
Provides backtesting, strategy optimization, and automated trading strategies
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import json
import math

logger = logging.getLogger(__name__)

class StrategyType(Enum):
    """Types of trading strategies"""
    MOMENTUM = "momentum"
    MEAN_REVERSION = "mean_reversion"
    BREAKOUT = "breakout"
    SCALPING = "scalping"
    SWING = "swing"
    ARBITRAGE = "arbitrage"
    PAIRS_TRADING = "pairs_trading"
    MACHINE_LEARNING = "machine_learning"

class SignalType(Enum):
    """Types of trading signals"""
    BUY = "buy"
    SELL = "sell"
    HOLD = "hold"
    CLOSE_LONG = "close_long"
    CLOSE_SHORT = "close_short"

@dataclass
class TradingSignal:
    """Trading signal structure"""
    symbol: str
    signal_type: SignalType
    price: float
    timestamp: datetime
    confidence: float
    strategy: str
    metadata: Dict[str, Any] = None

@dataclass
class BacktestResult:
    """Backtesting result structure"""
    strategy_name: str
    total_return: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    total_trades: int
    profit_factor: float
    start_date: datetime
    end_date: datetime
    trades: List[Dict[str, Any]]
    equity_curve: List[float]
    metrics: Dict[str, float]

class AdvancedTradingStrategies:
    """Advanced trading strategies system with backtesting and optimization"""
    
    def __init__(self):
        self.strategies = {}
        self.active_strategies = {}
        self.backtest_results = {}
        self.performance_metrics = {}
        
    async def initialize(self):
        """Initialize the trading strategies system"""
        logger.info("Initializing advanced trading strategies system...")
        
        # Register built-in strategies
        await self._register_builtin_strategies()
        
        logger.info("Advanced trading strategies system initialized")
    
    async def _register_builtin_strategies(self):
        """Register built-in trading strategies"""
        # Momentum Strategy
        self.strategies['momentum_rsi'] = {
            'name': 'RSI Momentum Strategy',
            'type': StrategyType.MOMENTUM,
            'description': 'Trades based on RSI momentum signals',
            'parameters': {
                'rsi_period': 14,
                'oversold_threshold': 30,
                'overbought_threshold': 70,
                'stop_loss': 0.02,
                'take_profit': 0.04
            },
            'function': self._momentum_rsi_strategy
        }
        
        # Mean Reversion Strategy
        self.strategies['mean_reversion_bollinger'] = {
            'name': 'Bollinger Bands Mean Reversion',
            'type': StrategyType.MEAN_REVERSION,
            'description': 'Trades based on Bollinger Bands mean reversion',
            'parameters': {
                'bb_period': 20,
                'bb_std': 2,
                'entry_threshold': 0.5,
                'exit_threshold': 0.2,
                'stop_loss': 0.03
            },
            'function': self._mean_reversion_bollinger_strategy
        }
        
        # Breakout Strategy
        self.strategies['breakout_support_resistance'] = {
            'name': 'Support/Resistance Breakout',
            'type': StrategyType.BREAKOUT,
            'description': 'Trades breakouts from support/resistance levels',
            'parameters': {
                'lookback_period': 20,
                'breakout_threshold': 0.01,
                'volume_confirmation': True,
                'stop_loss': 0.025,
                'take_profit': 0.05
            },
            'function': self._breakout_support_resistance_strategy
        }
        
        # Pairs Trading Strategy
        self.strategies['pairs_trading'] = {
            'name': 'Pairs Trading Strategy',
            'type': StrategyType.PAIRS_TRADING,
            'description': 'Trades based on correlation between two assets',
            'parameters': {
                'correlation_threshold': 0.7,
                'z_score_threshold': 2,
                'lookback_period': 60,
                'stop_loss': 0.03
            },
            'function': self._pairs_trading_strategy
        }
        
        # Machine Learning Strategy
        self.strategies['ml_momentum'] = {
            'name': 'ML Momentum Strategy',
            'type': StrategyType.MACHINE_LEARNING,
            'description': 'Uses machine learning for momentum prediction',
            'parameters': {
                'features': ['rsi', 'macd', 'bb_position', 'volume_ratio'],
                'model_type': 'random_forest',
                'retrain_frequency': 7,
                'confidence_threshold': 0.6
            },
            'function': self._ml_momentum_strategy
        }
    
    async def run_strategy(self, strategy_name: str, symbol: str, data: pd.DataFrame, 
                          parameters: Dict[str, Any] = None) -> List[TradingSignal]:
        """Run a trading strategy on historical data"""
        try:
            if strategy_name not in self.strategies:
                raise ValueError(f"Strategy {strategy_name} not found")
            
            strategy = self.strategies[strategy_name]
            params = parameters or strategy['parameters']
            
            # Run the strategy
            signals = await strategy['function'](symbol, data, params)
            
            logger.info(f"Generated {len(signals)} signals for {symbol} using {strategy_name}")
            return signals
            
        except Exception as e:
            logger.error(f"Error running strategy {strategy_name}: {e}")
            return []
    
    async def backtest_strategy(self, strategy_name: str, symbol: str, data: pd.DataFrame,
                               parameters: Dict[str, Any] = None, initial_capital: float = 10000) -> BacktestResult:
        """Backtest a trading strategy"""
        try:
            # Run strategy to get signals
            signals = await self.run_strategy(strategy_name, symbol, data, parameters)
            
            if not signals:
                return BacktestResult(
                    strategy_name=strategy_name,
                    total_return=0,
                    sharpe_ratio=0,
                    max_drawdown=0,
                    win_rate=0,
                    total_trades=0,
                    profit_factor=0,
                    start_date=data.index[0],
                    end_date=data.index[-1],
                    trades=[],
                    equity_curve=[initial_capital],
                    metrics={}
                )
            
            # Simulate trading
            trades, equity_curve = await self._simulate_trading(signals, data, initial_capital)
            
            # Calculate performance metrics
            metrics = await self._calculate_performance_metrics(trades, equity_curve, initial_capital)
            
            result = BacktestResult(
                strategy_name=strategy_name,
                total_return=metrics['total_return'],
                sharpe_ratio=metrics['sharpe_ratio'],
                max_drawdown=metrics['max_drawdown'],
                win_rate=metrics['win_rate'],
                total_trades=len(trades),
                profit_factor=metrics['profit_factor'],
                start_date=data.index[0],
                end_date=data.index[-1],
                trades=trades,
                equity_curve=equity_curve,
                metrics=metrics
            )
            
            # Store result
            self.backtest_results[f"{strategy_name}_{symbol}"] = result
            
            logger.info(f"Backtest completed for {strategy_name} on {symbol}: {metrics['total_return']:.2%} return")
            return result
            
        except Exception as e:
            logger.error(f"Error backtesting strategy {strategy_name}: {e}")
            return None
    
    async def optimize_strategy(self, strategy_name: str, symbol: str, data: pd.DataFrame,
                               parameter_ranges: Dict[str, List[Any]], 
                               optimization_metric: str = 'sharpe_ratio') -> Dict[str, Any]:
        """Optimize strategy parameters using grid search"""
        try:
            best_params = None
            best_score = float('-inf')
            best_result = None
            
            # Generate parameter combinations
            param_combinations = self._generate_parameter_combinations(parameter_ranges)
            
            logger.info(f"Optimizing {strategy_name} with {len(param_combinations)} parameter combinations")
            
            for i, params in enumerate(param_combinations):
                try:
                    # Run backtest with these parameters
                    result = await self.backtest_strategy(strategy_name, symbol, data, params)
                    
                    if result:
                        score = getattr(result, optimization_metric, 0)
                        
                        if score > best_score:
                            best_score = score
                            best_params = params
                            best_result = result
                    
                    # Log progress
                    if (i + 1) % 10 == 0:
                        logger.info(f"Optimization progress: {i + 1}/{len(param_combinations)}")
                        
                except Exception as e:
                    logger.warning(f"Error testing parameters {params}: {e}")
                    continue
            
            optimization_result = {
                'strategy_name': strategy_name,
                'symbol': symbol,
                'best_parameters': best_params,
                'best_score': best_score,
                'best_result': best_result,
                'total_combinations': len(param_combinations)
            }
            
            logger.info(f"Optimization completed for {strategy_name}: best score {best_score:.4f}")
            return optimization_result
            
        except Exception as e:
            logger.error(f"Error optimizing strategy {strategy_name}: {e}")
            return None
    
    async def _momentum_rsi_strategy(self, symbol: str, data: pd.DataFrame, params: Dict[str, Any]) -> List[TradingSignal]:
        """RSI Momentum Strategy"""
        signals = []
        
        try:
            # Calculate RSI
            rsi_period = params.get('rsi_period', 14)
            rsi = self._calculate_rsi(data['close'], rsi_period)
            
            oversold = params.get('oversold_threshold', 30)
            overbought = params.get('overbought_threshold', 70)
            
            position = 0  # 0: no position, 1: long, -1: short
            
            for i in range(len(data)):
                if pd.isna(rsi.iloc[i]):
                    continue
                
                current_price = data['close'].iloc[i]
                current_rsi = rsi.iloc[i]
                current_time = data.index[i]
                
                # Entry signals
                if position == 0:
                    if current_rsi < oversold:
                        # Buy signal
                        signals.append(TradingSignal(
                            symbol=symbol,
                            signal_type=SignalType.BUY,
                            price=current_price,
                            timestamp=current_time,
                            confidence=min(1.0, (oversold - current_rsi) / oversold),
                            strategy='momentum_rsi',
                            metadata={'rsi': current_rsi}
                        ))
                        position = 1
                    elif current_rsi > overbought:
                        # Sell signal
                        signals.append(TradingSignal(
                            symbol=symbol,
                            signal_type=SignalType.SELL,
                            price=current_price,
                            timestamp=current_time,
                            confidence=min(1.0, (current_rsi - overbought) / (100 - overbought)),
                            strategy='momentum_rsi',
                            metadata={'rsi': current_rsi}
                        ))
                        position = -1
                
                # Exit signals
                elif position == 1 and current_rsi > 50:
                    signals.append(TradingSignal(
                        symbol=symbol,
                        signal_type=SignalType.CLOSE_LONG,
                        price=current_price,
                        timestamp=current_time,
                        confidence=0.8,
                        strategy='momentum_rsi',
                        metadata={'rsi': current_rsi}
                    ))
                    position = 0
                elif position == -1 and current_rsi < 50:
                    signals.append(TradingSignal(
                        symbol=symbol,
                        signal_type=SignalType.CLOSE_SHORT,
                        price=current_price,
                        timestamp=current_time,
                        confidence=0.8,
                        strategy='momentum_rsi',
                        metadata={'rsi': current_rsi}
                    ))
                    position = 0
            
        except Exception as e:
            logger.error(f"Error in momentum RSI strategy: {e}")
        
        return signals
    
    async def _mean_reversion_bollinger_strategy(self, symbol: str, data: pd.DataFrame, params: Dict[str, Any]) -> List[TradingSignal]:
        """Bollinger Bands Mean Reversion Strategy"""
        signals = []
        
        try:
            # Calculate Bollinger Bands
            bb_period = params.get('bb_period', 20)
            bb_std = params.get('bb_std', 2)
            
            bb_upper, bb_middle, bb_lower = self._calculate_bollinger_bands(data['close'], bb_period, bb_std)
            
            entry_threshold = params.get('entry_threshold', 0.5)
            exit_threshold = params.get('exit_threshold', 0.2)
            
            position = 0
            
            for i in range(len(data)):
                if pd.isna(bb_upper.iloc[i]) or pd.isna(bb_lower.iloc[i]):
                    continue
                
                current_price = data['close'].iloc[i]
                current_time = data.index[i]
                
                # Calculate position within bands
                band_width = bb_upper.iloc[i] - bb_lower.iloc[i]
                position_in_bands = (current_price - bb_lower.iloc[i]) / band_width
                
                # Entry signals
                if position == 0:
                    if position_in_bands < entry_threshold:
                        # Buy signal (oversold)
                        signals.append(TradingSignal(
                            symbol=symbol,
                            signal_type=SignalType.BUY,
                            price=current_price,
                            timestamp=current_time,
                            confidence=min(1.0, (entry_threshold - position_in_bands) / entry_threshold),
                            strategy='mean_reversion_bollinger',
                            metadata={'position_in_bands': position_in_bands}
                        ))
                        position = 1
                    elif position_in_bands > (1 - entry_threshold):
                        # Sell signal (overbought)
                        signals.append(TradingSignal(
                            symbol=symbol,
                            signal_type=SignalType.SELL,
                            price=current_price,
                            timestamp=current_time,
                            confidence=min(1.0, (position_in_bands - (1 - entry_threshold)) / entry_threshold),
                            strategy='mean_reversion_bollinger',
                            metadata={'position_in_bands': position_in_bands}
                        ))
                        position = -1
                
                # Exit signals
                elif position == 1 and position_in_bands > (0.5 + exit_threshold):
                    signals.append(TradingSignal(
                        symbol=symbol,
                        signal_type=SignalType.CLOSE_LONG,
                        price=current_price,
                        timestamp=current_time,
                        confidence=0.8,
                        strategy='mean_reversion_bollinger',
                        metadata={'position_in_bands': position_in_bands}
                    ))
                    position = 0
                elif position == -1 and position_in_bands < (0.5 - exit_threshold):
                    signals.append(TradingSignal(
                        symbol=symbol,
                        signal_type=SignalType.CLOSE_SHORT,
                        price=current_price,
                        timestamp=current_time,
                        confidence=0.8,
                        strategy='mean_reversion_bollinger',
                        metadata={'position_in_bands': position_in_bands}
                    ))
                    position = 0
            
        except Exception as e:
            logger.error(f"Error in mean reversion Bollinger strategy: {e}")
        
        return signals
    
    async def _breakout_support_resistance_strategy(self, symbol: str, data: pd.DataFrame, params: Dict[str, Any]) -> List[TradingSignal]:
        """Support/Resistance Breakout Strategy"""
        signals = []
        
        try:
            lookback_period = params.get('lookback_period', 20)
            breakout_threshold = params.get('breakout_threshold', 0.01)
            volume_confirmation = params.get('volume_confirmation', True)
            
            # Calculate support and resistance levels
            support_levels = []
            resistance_levels = []
            
            for i in range(lookback_period, len(data)):
                # Look for local minima (support) and maxima (resistance)
                window_data = data.iloc[i-lookback_period:i]
                
                # Find local minima
                local_min = window_data['low'].min()
                if window_data['low'].iloc[-1] == local_min:
                    support_levels.append(local_min)
                
                # Find local maxima
                local_max = window_data['high'].max()
                if window_data['high'].iloc[-1] == local_max:
                    resistance_levels.append(local_max)
            
            position = 0
            
            for i in range(lookback_period, len(data)):
                current_price = data['close'].iloc[i]
                current_time = data.index[i]
                current_volume = data['volume'].iloc[i]
                avg_volume = data['volume'].iloc[i-lookback_period:i].mean()
                
                # Check for breakouts
                if position == 0:
                    # Check resistance breakout
                    for resistance in resistance_levels[-5:]:  # Check last 5 resistance levels
                        if current_price > resistance * (1 + breakout_threshold):
                            if not volume_confirmation or current_volume > avg_volume * 1.2:
                                signals.append(TradingSignal(
                                    symbol=symbol,
                                    signal_type=SignalType.BUY,
                                    price=current_price,
                                    timestamp=current_time,
                                    confidence=min(1.0, (current_price - resistance) / resistance),
                                    strategy='breakout_support_resistance',
                                    metadata={'resistance_level': resistance, 'volume_ratio': current_volume / avg_volume}
                                ))
                                position = 1
                                break
                    
                    # Check support breakdown
                    for support in support_levels[-5:]:  # Check last 5 support levels
                        if current_price < support * (1 - breakout_threshold):
                            if not volume_confirmation or current_volume > avg_volume * 1.2:
                                signals.append(TradingSignal(
                                    symbol=symbol,
                                    signal_type=SignalType.SELL,
                                    price=current_price,
                                    timestamp=current_time,
                                    confidence=min(1.0, (support - current_price) / support),
                                    strategy='breakout_support_resistance',
                                    metadata={'support_level': support, 'volume_ratio': current_volume / avg_volume}
                                ))
                                position = -1
                                break
                
                # Exit signals (simplified)
                elif position == 1:
                    # Close long position if price drops below recent support
                    recent_support = min(support_levels[-3:]) if support_levels else current_price * 0.95
                    if current_price < recent_support:
                        signals.append(TradingSignal(
                            symbol=symbol,
                            signal_type=SignalType.CLOSE_LONG,
                            price=current_price,
                            timestamp=current_time,
                            confidence=0.8,
                            strategy='breakout_support_resistance',
                            metadata={'support_level': recent_support}
                        ))
                        position = 0
                elif position == -1:
                    # Close short position if price rises above recent resistance
                    recent_resistance = max(resistance_levels[-3:]) if resistance_levels else current_price * 1.05
                    if current_price > recent_resistance:
                        signals.append(TradingSignal(
                            symbol=symbol,
                            signal_type=SignalType.CLOSE_SHORT,
                            price=current_price,
                            timestamp=current_time,
                            confidence=0.8,
                            strategy='breakout_support_resistance',
                            metadata={'resistance_level': recent_resistance}
                        ))
                        position = 0
            
        except Exception as e:
            logger.error(f"Error in breakout support/resistance strategy: {e}")
        
        return signals
    
    async def _pairs_trading_strategy(self, symbol: str, data: pd.DataFrame, params: Dict[str, Any]) -> List[TradingSignal]:
        """Pairs Trading Strategy (simplified - requires two symbols)"""
        signals = []
        
        try:
            # This is a simplified version - in practice, you'd need two symbols
            # For now, we'll use a single symbol and simulate pairs trading with price ratios
            
            correlation_threshold = params.get('correlation_threshold', 0.7)
            z_score_threshold = params.get('z_score_threshold', 2)
            lookback_period = params.get('lookback_period', 60)
            
            # Calculate rolling correlation with market (simplified)
            if len(data) < lookback_period:
                return signals
            
            # Use price ratios as a proxy for pairs trading
            price_ratios = data['close'].pct_change().rolling(window=lookback_period)
            z_scores = (price_ratios - price_ratios.mean()) / price_ratios.std()
            
            position = 0
            
            for i in range(lookback_period, len(data)):
                if pd.isna(z_scores.iloc[i]):
                    continue
                
                current_price = data['close'].iloc[i]
                current_time = data.index[i]
                current_z_score = z_scores.iloc[i]
                
                # Entry signals based on z-score
                if position == 0:
                    if current_z_score > z_score_threshold:
                        # Sell signal (overvalued)
                        signals.append(TradingSignal(
                            symbol=symbol,
                            signal_type=SignalType.SELL,
                            price=current_price,
                            timestamp=current_time,
                            confidence=min(1.0, abs(current_z_score) / z_score_threshold),
                            strategy='pairs_trading',
                            metadata={'z_score': current_z_score}
                        ))
                        position = -1
                    elif current_z_score < -z_score_threshold:
                        # Buy signal (undervalued)
                        signals.append(TradingSignal(
                            symbol=symbol,
                            signal_type=SignalType.BUY,
                            price=current_price,
                            timestamp=current_time,
                            confidence=min(1.0, abs(current_z_score) / z_score_threshold),
                            strategy='pairs_trading',
                            metadata={'z_score': current_z_score}
                        ))
                        position = 1
                
                # Exit signals
                elif position == 1 and current_z_score > 0:
                    signals.append(TradingSignal(
                        symbol=symbol,
                        signal_type=SignalType.CLOSE_LONG,
                        price=current_price,
                        timestamp=current_time,
                        confidence=0.8,
                        strategy='pairs_trading',
                        metadata={'z_score': current_z_score}
                    ))
                    position = 0
                elif position == -1 and current_z_score < 0:
                    signals.append(TradingSignal(
                        symbol=symbol,
                        signal_type=SignalType.CLOSE_SHORT,
                        price=current_price,
                        timestamp=current_time,
                        confidence=0.8,
                        strategy='pairs_trading',
                        metadata={'z_score': current_z_score}
                    ))
                    position = 0
            
        except Exception as e:
            logger.error(f"Error in pairs trading strategy: {e}")
        
        return signals
    
    async def _ml_momentum_strategy(self, symbol: str, data: pd.DataFrame, params: Dict[str, Any]) -> List[TradingSignal]:
        """Machine Learning Momentum Strategy (simplified)"""
        signals = []
        
        try:
            # This is a simplified version - in practice, you'd train a real ML model
            features = params.get('features', ['rsi', 'macd', 'bb_position', 'volume_ratio'])
            confidence_threshold = params.get('confidence_threshold', 0.6)
            
            # Calculate features
            feature_data = self._calculate_ml_features(data, features)
            
            if len(feature_data) < 50:  # Need enough data for ML
                return signals
            
            # Simple momentum-based ML signal (in practice, use real ML model)
            momentum_score = self._calculate_momentum_score(feature_data)
            
            position = 0
            
            for i in range(50, len(feature_data)):
                current_price = data['close'].iloc[i]
                current_time = data.index[i]
                current_score = momentum_score.iloc[i]
                
                if pd.isna(current_score):
                    continue
                
                # Entry signals based on momentum score
                if position == 0 and abs(current_score) > confidence_threshold:
                    if current_score > 0:
                        signals.append(TradingSignal(
                            symbol=symbol,
                            signal_type=SignalType.BUY,
                            price=current_price,
                            timestamp=current_time,
                            confidence=min(1.0, abs(current_score)),
                            strategy='ml_momentum',
                            metadata={'momentum_score': current_score}
                        ))
                        position = 1
                    else:
                        signals.append(TradingSignal(
                            symbol=symbol,
                            signal_type=SignalType.SELL,
                            price=current_price,
                            timestamp=current_time,
                            confidence=min(1.0, abs(current_score)),
                            strategy='ml_momentum',
                            metadata={'momentum_score': current_score}
                        ))
                        position = -1
                
                # Exit signals
                elif position == 1 and current_score < 0:
                    signals.append(TradingSignal(
                        symbol=symbol,
                        signal_type=SignalType.CLOSE_LONG,
                        price=current_price,
                        timestamp=current_time,
                        confidence=0.8,
                        strategy='ml_momentum',
                        metadata={'momentum_score': current_score}
                    ))
                    position = 0
                elif position == -1 and current_score > 0:
                    signals.append(TradingSignal(
                        symbol=symbol,
                        signal_type=SignalType.CLOSE_SHORT,
                        price=current_price,
                        timestamp=current_time,
                        confidence=0.8,
                        strategy='ml_momentum',
                        metadata={'momentum_score': current_score}
                    ))
                    position = 0
            
        except Exception as e:
            logger.error(f"Error in ML momentum strategy: {e}")
        
        return signals
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calculate RSI indicator"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def _calculate_bollinger_bands(self, prices: pd.Series, period: int = 20, std: float = 2) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """Calculate Bollinger Bands"""
        middle = prices.rolling(window=period).mean()
        std_dev = prices.rolling(window=period).std()
        upper = middle + (std_dev * std)
        lower = middle - (std_dev * std)
        return upper, middle, lower
    
    def _calculate_ml_features(self, data: pd.DataFrame, features: List[str]) -> pd.DataFrame:
        """Calculate ML features"""
        feature_data = pd.DataFrame(index=data.index)
        
        if 'rsi' in features:
            feature_data['rsi'] = self._calculate_rsi(data['close'])
        
        if 'macd' in features:
            ema_12 = data['close'].ewm(span=12).mean()
            ema_26 = data['close'].ewm(span=26).mean()
            feature_data['macd'] = ema_12 - ema_26
        
        if 'bb_position' in features:
            bb_upper, bb_middle, bb_lower = self._calculate_bollinger_bands(data['close'])
            feature_data['bb_position'] = (data['close'] - bb_lower) / (bb_upper - bb_lower)
        
        if 'volume_ratio' in features:
            feature_data['volume_ratio'] = data['volume'] / data['volume'].rolling(20).mean()
        
        return feature_data
    
    def _calculate_momentum_score(self, feature_data: pd.DataFrame) -> pd.Series:
        """Calculate momentum score (simplified ML)"""
        # Simple weighted combination of features
        weights = {'rsi': 0.3, 'macd': 0.4, 'bb_position': 0.2, 'volume_ratio': 0.1}
        
        score = pd.Series(0, index=feature_data.index)
        for feature, weight in weights.items():
            if feature in feature_data.columns:
                # Normalize feature
                normalized = (feature_data[feature] - feature_data[feature].mean()) / feature_data[feature].std()
                score += normalized * weight
        
        return score
    
    def _generate_parameter_combinations(self, parameter_ranges: Dict[str, List[Any]]) -> List[Dict[str, Any]]:
        """Generate all combinations of parameters for optimization"""
        import itertools
        
        keys = list(parameter_ranges.keys())
        values = list(parameter_ranges.values())
        
        combinations = []
        for combo in itertools.product(*values):
            combinations.append(dict(zip(keys, combo)))
        
        return combinations
    
    async def _simulate_trading(self, signals: List[TradingSignal], data: pd.DataFrame, 
                               initial_capital: float) -> Tuple[List[Dict[str, Any]], List[float]]:
        """Simulate trading based on signals"""
        trades = []
        equity_curve = [initial_capital]
        current_capital = initial_capital
        position = 0
        entry_price = 0
        entry_time = None
        
        for signal in signals:
            if signal.signal_type == SignalType.BUY and position <= 0:
                # Close short position if exists
                if position < 0:
                    pnl = (entry_price - signal.price) / entry_price
                    current_capital *= (1 + pnl)
                    trades.append({
                        'entry_time': entry_time,
                        'exit_time': signal.timestamp,
                        'entry_price': entry_price,
                        'exit_price': signal.price,
                        'position': 'short',
                        'pnl': pnl,
                        'capital': current_capital
                    })
                
                # Open long position
                position = 1
                entry_price = signal.price
                entry_time = signal.timestamp
                
            elif signal.signal_type == SignalType.SELL and position >= 0:
                # Close long position if exists
                if position > 0:
                    pnl = (signal.price - entry_price) / entry_price
                    current_capital *= (1 + pnl)
                    trades.append({
                        'entry_time': entry_time,
                        'exit_time': signal.timestamp,
                        'entry_price': entry_price,
                        'exit_price': signal.price,
                        'position': 'long',
                        'pnl': pnl,
                        'capital': current_capital
                    })
                
                # Open short position
                position = -1
                entry_price = signal.price
                entry_time = signal.timestamp
                
            elif signal.signal_type == SignalType.CLOSE_LONG and position > 0:
                pnl = (signal.price - entry_price) / entry_price
                current_capital *= (1 + pnl)
                trades.append({
                    'entry_time': entry_time,
                    'exit_time': signal.timestamp,
                    'entry_price': entry_price,
                    'exit_price': signal.price,
                    'position': 'long',
                    'pnl': pnl,
                    'capital': current_capital
                })
                position = 0
                
            elif signal.signal_type == SignalType.CLOSE_SHORT and position < 0:
                pnl = (entry_price - signal.price) / entry_price
                current_capital *= (1 + pnl)
                trades.append({
                    'entry_time': entry_time,
                    'exit_time': signal.timestamp,
                    'entry_price': entry_price,
                    'exit_price': signal.price,
                    'position': 'short',
                    'pnl': pnl,
                    'capital': current_capital
                })
                position = 0
            
            equity_curve.append(current_capital)
        
        return trades, equity_curve
    
    async def _calculate_performance_metrics(self, trades: List[Dict[str, Any]], 
                                           equity_curve: List[float], 
                                           initial_capital: float) -> Dict[str, float]:
        """Calculate performance metrics"""
        if not trades:
            return {
                'total_return': 0,
                'sharpe_ratio': 0,
                'max_drawdown': 0,
                'win_rate': 0,
                'profit_factor': 0
            }
        
        # Total return
        total_return = (equity_curve[-1] - initial_capital) / initial_capital
        
        # Win rate
        winning_trades = [t for t in trades if t['pnl'] > 0]
        win_rate = len(winning_trades) / len(trades) if trades else 0
        
        # Profit factor
        total_profit = sum(t['pnl'] for t in trades if t['pnl'] > 0)
        total_loss = abs(sum(t['pnl'] for t in trades if t['pnl'] < 0))
        profit_factor = total_profit / total_loss if total_loss > 0 else float('inf')
        
        # Sharpe ratio (simplified)
        returns = [t['pnl'] for t in trades]
        if len(returns) > 1:
            sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(252) if np.std(returns) > 0 else 0
        else:
            sharpe_ratio = 0
        
        # Maximum drawdown
        peak = initial_capital
        max_drawdown = 0
        for capital in equity_curve:
            if capital > peak:
                peak = capital
            drawdown = (peak - capital) / peak
            max_drawdown = max(max_drawdown, drawdown)
        
        return {
            'total_return': total_return,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'win_rate': win_rate,
            'profit_factor': profit_factor
        }
    
    async def get_strategy_list(self) -> List[Dict[str, Any]]:
        """Get list of available strategies"""
        return [
            {
                'name': name,
                'type': strategy['type'].value,
                'description': strategy['description'],
                'parameters': strategy['parameters']
            }
            for name, strategy in self.strategies.items()
        ]
    
    async def cleanup(self):
        """Cleanup resources"""
        try:
            self.strategies.clear()
            self.active_strategies.clear()
            self.backtest_results.clear()
            self.performance_metrics.clear()
            logger.info("Advanced trading strategies system cleaned up")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

# Global instance
advanced_strategies = AdvancedTradingStrategies()
