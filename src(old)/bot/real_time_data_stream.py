"""
Real-Time Data Streaming System
Provides live market data streaming for real-time analysis and alerts
"""

import asyncio
import websockets
import json
import logging
from typing import Dict, List, Callable, Optional, Any
from datetime import datetime, timedelta
import aiohttp
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class DataSource(Enum):
    """Available data sources for real-time streaming"""
    ALPHA_VANTAGE = "alpha_vantage"
    POLYGON = "polygon"
    FINNHUB = "finnhub"
    YAHOO_FINANCE = "yahoo_finance"
    BINANCE = "binance"

@dataclass
class MarketData:
    """Real-time market data structure"""
    symbol: str
    price: float
    change: float
    change_percent: float
    volume: int
    timestamp: datetime
    source: DataSource
    bid: Optional[float] = None
    ask: Optional[float] = None
    high: Optional[float] = None
    low: Optional[float] = None

class RealTimeDataStreamer:
    """Real-time market data streaming system"""
    
    def __init__(self):
        self.active_streams = {}
        self.subscribers = {}
        self.connection_pools = {}
        self.last_data = {}
        self.stream_health = {}
        
    async def initialize(self):
        """Initialize the streaming system"""
        logger.info("Initializing real-time data streaming system...")
        
        # Initialize connection pools for each data source
        for source in DataSource:
            self.connection_pools[source] = aiohttp.ClientSession()
            self.stream_health[source] = {
                'status': 'disconnected',
                'last_heartbeat': None,
                'error_count': 0
            }
        
        logger.info("Real-time data streaming system initialized")
    
    async def subscribe_to_symbol(self, symbol: str, callback: Callable[[MarketData], None], 
                                 sources: List[DataSource] = None) -> bool:
        """Subscribe to real-time data for a symbol"""
        if sources is None:
            sources = [DataSource.ALPHA_VANTAGE, DataSource.POLYGON]
        
        try:
            # Initialize symbol tracking
            if symbol not in self.active_streams:
                self.active_streams[symbol] = {
                    'sources': sources,
                    'callbacks': [],
                    'last_update': None,
                    'status': 'connecting'
                }
            
            # Add callback
            self.active_streams[symbol]['callbacks'].append(callback)
            
            # Start streaming for each source
            for source in sources:
                await self._start_source_stream(symbol, source)
            
            logger.info(f"Subscribed to {symbol} with sources: {[s.value for s in sources]}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to subscribe to {symbol}: {e}")
            return False
    
    async def unsubscribe_from_symbol(self, symbol: str, callback: Callable[[MarketData], None] = None) -> bool:
        """Unsubscribe from real-time data for a symbol"""
        try:
            if symbol not in self.active_streams:
                return True
            
            if callback:
                # Remove specific callback
                if callback in self.active_streams[symbol]['callbacks']:
                    self.active_streams[symbol]['callbacks'].remove(callback)
            else:
                # Remove all callbacks
                self.active_streams[symbol]['callbacks'] = []
            
            # If no more callbacks, stop streaming
            if not self.active_streams[symbol]['callbacks']:
                await self._stop_symbol_stream(symbol)
                del self.active_streams[symbol]
            
            logger.info(f"Unsubscribed from {symbol}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to unsubscribe from {symbol}: {e}")
            return False
    
    async def _start_source_stream(self, symbol: str, source: DataSource):
        """Start streaming data from a specific source"""
        try:
            if source == DataSource.ALPHA_VANTAGE:
                await self._stream_alpha_vantage(symbol)
            elif source == DataSource.POLYGON:
                await self._stream_polygon(symbol)
            elif source == DataSource.FINNHUB:
                await self._stream_finnhub(symbol)
            elif source == DataSource.YAHOO_FINANCE:
                await self._stream_yahoo_finance(symbol)
            elif source == DataSource.BINANCE:
                await self._stream_binance(symbol)
                
        except Exception as e:
            logger.error(f"Failed to start {source.value} stream for {symbol}: {e}")
            self.stream_health[source]['error_count'] += 1
    
    async def _stream_alpha_vantage(self, symbol: str):
        """Stream data from Alpha Vantage"""
        # Alpha Vantage doesn't have real-time streaming, so we'll poll
        while symbol in self.active_streams:
            try:
                data = await self._fetch_alpha_vantage_data(symbol)
                if data:
                    await self._process_market_data(data, DataSource.ALPHA_VANTAGE)
                
                # Poll every 5 seconds
                await asyncio.sleep(5)
                
            except Exception as e:
                logger.error(f"Alpha Vantage stream error for {symbol}: {e}")
                await asyncio.sleep(10)
    
    async def _stream_polygon(self, symbol: str):
        """Stream data from Polygon.io WebSocket"""
        try:
            # Polygon WebSocket URL
            ws_url = "wss://socket.polygon.io/stocks"
            
            async with websockets.connect(ws_url) as websocket:
                # Authenticate
                auth_msg = {
                    "action": "auth",
                    "params": "YOUR_POLYGON_API_KEY"  # Replace with actual API key
                }
                await websocket.send(json.dumps(auth_msg))
                
                # Subscribe to symbol
                subscribe_msg = {
                    "action": "subscribe",
                    "params": f"T.{symbol}"
                }
                await websocket.send(json.dumps(subscribe_msg))
                
                # Listen for data
                async for message in websocket:
                    if symbol not in self.active_streams:
                        break
                    
                    data = json.loads(message)
                    if data.get('ev') == 'T':  # Trade data
                        await self._process_polygon_data(data, symbol)
                        
        except Exception as e:
            logger.error(f"Polygon stream error for {symbol}: {e}")
    
    async def _stream_finnhub(self, symbol: str):
        """Stream data from Finnhub WebSocket"""
        try:
            ws_url = f"wss://ws.finnhub.io?token=YOUR_FINNHUB_API_KEY"  # Replace with actual API key
            
            async with websockets.connect(ws_url) as websocket:
                # Subscribe to symbol
                subscribe_msg = {
                    "type": "subscribe",
                    "symbol": symbol
                }
                await websocket.send(json.dumps(subscribe_msg))
                
                # Listen for data
                async for message in websocket:
                    if symbol not in self.active_streams:
                        break
                    
                    data = json.loads(message)
                    if data.get('type') == 'trade':
                        await self._process_finnhub_data(data, symbol)
                        
        except Exception as e:
            logger.error(f"Finnhub stream error for {symbol}: {e}")
    
    async def _stream_yahoo_finance(self, symbol: str):
        """Stream data from Yahoo Finance (polling)"""
        while symbol in self.active_streams:
            try:
                data = await self._fetch_yahoo_finance_data(symbol)
                if data:
                    await self._process_market_data(data, DataSource.YAHOO_FINANCE)
                
                # Poll every 3 seconds
                await asyncio.sleep(3)
                
            except Exception as e:
                logger.error(f"Yahoo Finance stream error for {symbol}: {e}")
                await asyncio.sleep(5)
    
    async def _stream_binance(self, symbol: str):
        """Stream data from Binance WebSocket"""
        try:
            # Convert symbol to Binance format (e.g., AAPL -> AAPLUSDT)
            binance_symbol = f"{symbol}USDT"
            ws_url = f"wss://stream.binance.com:9443/ws/{binance_symbol.lower()}@ticker"
            
            async with websockets.connect(ws_url) as websocket:
                async for message in websocket:
                    if symbol not in self.active_streams:
                        break
                    
                    data = json.loads(message)
                    await self._process_binance_data(data, symbol)
                    
        except Exception as e:
            logger.error(f"Binance stream error for {symbol}: {e}")
    
    async def _process_market_data(self, data: Dict[str, Any], source: DataSource):
        """Process and distribute market data to subscribers"""
        try:
            # Extract market data
            market_data = MarketData(
                symbol=data.get('symbol', ''),
                price=float(data.get('price', 0)),
                change=float(data.get('change', 0)),
                change_percent=float(data.get('change_percent', 0)),
                volume=int(data.get('volume', 0)),
                timestamp=datetime.now(),
                source=source,
                bid=data.get('bid'),
                ask=data.get('ask'),
                high=data.get('high'),
                low=data.get('low')
            )
            
            # Store last data
            self.last_data[market_data.symbol] = market_data
            
            # Notify subscribers
            if market_data.symbol in self.active_streams:
                for callback in self.active_streams[market_data.symbol]['callbacks']:
                    try:
                        await callback(market_data)
                    except Exception as e:
                        logger.error(f"Callback error for {market_data.symbol}: {e}")
            
            # Update stream health
            self.stream_health[source]['last_heartbeat'] = datetime.now()
            self.stream_health[source]['status'] = 'connected'
            self.stream_health[source]['error_count'] = 0
            
        except Exception as e:
            logger.error(f"Error processing market data: {e}")
    
    async def _fetch_alpha_vantage_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Fetch data from Alpha Vantage API"""
        try:
            # Mock data for testing purposes
            import random
            base_price = 150.0  # Base price for testing
            price_change = random.uniform(-0.03, 0.03)  # ±3% random change
            current_price = base_price * (1 + price_change)
            
            return {
                'symbol': symbol,
                'price': current_price,
                'change': current_price - base_price,
                'change_percent': price_change * 100,
                'volume': random.randint(2000000, 6000000),
                'high': current_price * (1 + random.uniform(0, 0.015)),
                'low': current_price * (1 - random.uniform(0, 0.015)),
                'timestamp': datetime.now()
            }
        except Exception as e:
            logger.error(f"Alpha Vantage API error: {e}")
            return None
    
    async def _fetch_yahoo_finance_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Fetch data from Yahoo Finance API"""
        try:
            # Mock data for testing purposes
            import random
            base_price = 150.0  # Base price for testing
            price_change = random.uniform(-0.05, 0.05)  # ±5% random change
            current_price = base_price * (1 + price_change)
            
            return {
                'symbol': symbol,
                'price': current_price,
                'change': current_price - base_price,
                'change_percent': price_change * 100,
                'volume': random.randint(1000000, 5000000),
                'high': current_price * (1 + random.uniform(0, 0.02)),
                'low': current_price * (1 - random.uniform(0, 0.02)),
                'timestamp': datetime.now()
            }
        except Exception as e:
            logger.error(f"Yahoo Finance API error: {e}")
            return None
    
    async def _process_polygon_data(self, data: Dict[str, Any], symbol: str):
        """Process Polygon.io data"""
        try:
            # Extract trade data from Polygon format
            trade_data = {
                'symbol': symbol,
                'price': data.get('p', 0),
                'volume': data.get('s', 0),
                'timestamp': datetime.fromtimestamp(data.get('t', 0) / 1000)
            }
            await self._process_market_data(trade_data, DataSource.POLYGON)
        except Exception as e:
            logger.error(f"Error processing Polygon data: {e}")
    
    async def _process_finnhub_data(self, data: Dict[str, Any], symbol: str):
        """Process Finnhub data"""
        try:
            # Extract trade data from Finnhub format
            trade_data = {
                'symbol': symbol,
                'price': data.get('p', 0),
                'volume': data.get('v', 0),
                'timestamp': datetime.fromtimestamp(data.get('t', 0) / 1000)
            }
            await self._process_market_data(trade_data, DataSource.FINNHUB)
        except Exception as e:
            logger.error(f"Error processing Finnhub data: {e}")
    
    async def _process_binance_data(self, data: Dict[str, Any], symbol: str):
        """Process Binance data"""
        try:
            # Extract ticker data from Binance format
            ticker_data = {
                'symbol': symbol,
                'price': float(data.get('c', 0)),
                'change': float(data.get('P', 0)),
                'change_percent': float(data.get('P', 0)),
                'volume': float(data.get('v', 0)),
                'high': float(data.get('h', 0)),
                'low': float(data.get('l', 0)),
                'timestamp': datetime.now()
            }
            await self._process_market_data(ticker_data, DataSource.BINANCE)
        except Exception as e:
            logger.error(f"Error processing Binance data: {e}")
    
    async def _stop_symbol_stream(self, symbol: str):
        """Stop streaming data for a symbol"""
        try:
            if symbol in self.active_streams:
                self.active_streams[symbol]['status'] = 'stopped'
            logger.info(f"Stopped streaming for {symbol}")
        except Exception as e:
            logger.error(f"Error stopping stream for {symbol}: {e}")
    
    async def get_last_data(self, symbol: str) -> Optional[MarketData]:
        """Get the last received data for a symbol"""
        return self.last_data.get(symbol)
    
    async def get_stream_health(self) -> Dict[str, Any]:
        """Get health status of all streams"""
        return {
            'active_streams': len(self.active_streams),
            'sources': self.stream_health,
            'last_data_count': len(self.last_data)
        }
    
    async def cleanup(self):
        """Cleanup resources"""
        try:
            # Stop all active streams
            for symbol in list(self.active_streams.keys()):
                await self._stop_symbol_stream(symbol)
            
            # Close connection pools
            for source, session in self.connection_pools.items():
                if session and not session.closed:
                    await session.close()
            
            logger.info("Real-time data streaming system cleaned up")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

# Global instance
real_time_streamer = RealTimeDataStreamer()
