"""
Risk Management System
Provides comprehensive risk management for trading operations
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import math

logger = logging.getLogger(__name__)

class RiskLevel(Enum):
    """Risk levels for different operations"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class PositionType(Enum):
    """Types of positions"""
    LONG = "long"
    SHORT = "short"
    CASH = "cash"

@dataclass
class Position:
    """Position structure"""
    symbol: str
    position_type: PositionType
    quantity: float
    entry_price: float
    current_price: float
    entry_time: datetime
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    max_loss: Optional[float] = None
    risk_percentage: float = 0.02  # 2% risk per position

@dataclass
class RiskMetrics:
    """Risk metrics structure"""
    portfolio_value: float
    total_exposure: float
    max_drawdown: float
    var_95: float  # Value at Risk 95%
    var_99: float  # Value at Risk 99%
    sharpe_ratio: float
    beta: float
    correlation_risk: float
    concentration_risk: float
    leverage_ratio: float

class RiskManagementSystem:
    """Comprehensive risk management system"""
    
    def __init__(self):
        self.positions = {}
        self.risk_limits = {
            'max_position_size': 0.1,  # 10% max position size
            'max_portfolio_risk': 0.05,  # 5% max portfolio risk
            'max_drawdown': 0.15,  # 15% max drawdown
            'max_correlation': 0.7,  # 70% max correlation
            'max_leverage': 2.0,  # 2x max leverage
            'stop_loss_percentage': 0.02,  # 2% stop loss
            'take_profit_percentage': 0.04,  # 4% take profit
        }
        self.portfolio_history = []
        self.risk_alerts = []
        self.market_data = {}
        
    async def initialize(self):
        """Initialize the risk management system"""
        logger.info("Initializing risk management system...")
        
        # Load risk limits from configuration
        await self._load_risk_limits()
        
        logger.info("Risk management system initialized")
    
    async def _load_risk_limits(self):
        """Load risk limits from configuration"""
        # In practice, this would load from a config file or database
        pass
    
    async def add_position(self, position: Position) -> bool:
        """Add a new position with risk validation"""
        try:
            # Validate position risk
            if not await self._validate_position_risk(position):
                return False
            
            # Calculate position metrics
            position = await self._calculate_position_metrics(position)
            
            # Add to positions
            self.positions[position.symbol] = position
            
            # Update portfolio risk
            await self._update_portfolio_risk()
            
            logger.info(f"Added position: {position.symbol} {position.position_type.value} {position.quantity}")
            return True
            
        except Exception as e:
            logger.error(f"Error adding position: {e}")
            return False
    
    async def update_position(self, symbol: str, current_price: float) -> bool:
        """Update position with current price"""
        try:
            if symbol not in self.positions:
                return False
            
            position = self.positions[symbol]
            position.current_price = current_price
            
            # Check stop loss and take profit
            await self._check_exit_conditions(position)
            
            # Update portfolio risk
            await self._update_portfolio_risk()
            
            return True
            
        except Exception as e:
            logger.error(f"Error updating position {symbol}: {e}")
            return False
    
    async def remove_position(self, symbol: str) -> bool:
        """Remove a position"""
        try:
            if symbol in self.positions:
                del self.positions[symbol]
                await self._update_portfolio_risk()
                logger.info(f"Removed position: {symbol}")
                return True
            return False
            
        except Exception as e:
            logger.error(f"Error removing position {symbol}: {e}")
            return False
    
    async def calculate_position_size(self, symbol: str, entry_price: float, 
                                    stop_loss: float, risk_percentage: float = None) -> float:
        """Calculate optimal position size based on risk"""
        try:
            if risk_percentage is None:
                risk_percentage = self.risk_limits['max_position_size']
            
            # Calculate risk per share
            risk_per_share = abs(entry_price - stop_loss)
            
            if risk_per_share == 0:
                return 0
            
            # Calculate position size
            portfolio_value = await self._get_portfolio_value()
            max_risk_amount = portfolio_value * risk_percentage
            position_size = max_risk_amount / risk_per_share
            
            # Apply maximum position size limit
            max_position_value = portfolio_value * self.risk_limits['max_position_size']
            max_position_size = max_position_value / entry_price
            
            position_size = min(position_size, max_position_size)
            
            return max(0, position_size)
            
        except Exception as e:
            logger.error(f"Error calculating position size: {e}")
            return 0
    
    async def calculate_stop_loss(self, symbol: str, entry_price: float, 
                                 position_type: PositionType) -> float:
        """Calculate stop loss price"""
        try:
            stop_loss_percentage = self.risk_limits['stop_loss_percentage']
            
            if position_type == PositionType.LONG:
                stop_loss = entry_price * (1 - stop_loss_percentage)
            else:  # SHORT
                stop_loss = entry_price * (1 + stop_loss_percentage)
            
            return stop_loss
            
        except Exception as e:
            logger.error(f"Error calculating stop loss: {e}")
            return entry_price
    
    async def calculate_take_profit(self, symbol: str, entry_price: float, 
                                   position_type: PositionType) -> float:
        """Calculate take profit price"""
        try:
            take_profit_percentage = self.risk_limits['take_profit_percentage']
            
            if position_type == PositionType.LONG:
                take_profit = entry_price * (1 + take_profit_percentage)
            else:  # SHORT
                take_profit = entry_price * (1 - take_profit_percentage)
            
            return take_profit
            
        except Exception as e:
            logger.error(f"Error calculating take profit: {e}")
            return entry_price
    
    async def get_portfolio_risk_metrics(self) -> RiskMetrics:
        """Get comprehensive portfolio risk metrics"""
        try:
            portfolio_value = await self._get_portfolio_value()
            total_exposure = await self._calculate_total_exposure()
            
            # Calculate Value at Risk
            var_95 = await self._calculate_var(0.95)
            var_99 = await self._calculate_var(0.99)
            
            # Calculate other metrics
            max_drawdown = await self._calculate_max_drawdown()
            sharpe_ratio = await self._calculate_sharpe_ratio()
            beta = await self._calculate_beta()
            correlation_risk = await self._calculate_correlation_risk()
            concentration_risk = await self._calculate_concentration_risk()
            leverage_ratio = await self._calculate_leverage_ratio()
            
            return RiskMetrics(
                portfolio_value=portfolio_value,
                total_exposure=total_exposure,
                max_drawdown=max_drawdown,
                var_95=var_95,
                var_99=var_99,
                sharpe_ratio=sharpe_ratio,
                beta=beta,
                correlation_risk=correlation_risk,
                concentration_risk=concentration_risk,
                leverage_ratio=leverage_ratio
            )
            
        except Exception as e:
            logger.error(f"Error calculating risk metrics: {e}")
            return RiskMetrics(0, 0, 0, 0, 0, 0, 0, 0, 0, 0)
    
    async def check_risk_alerts(self) -> List[Dict[str, Any]]:
        """Check for risk alerts and violations"""
        alerts = []
        
        try:
            metrics = await self.get_portfolio_risk_metrics()
            
            # Check maximum drawdown
            if metrics.max_drawdown > self.risk_limits['max_drawdown']:
                alerts.append({
                    'type': 'max_drawdown_exceeded',
                    'level': RiskLevel.CRITICAL,
                    'message': f"Maximum drawdown exceeded: {metrics.max_drawdown:.2%}",
                    'value': metrics.max_drawdown,
                    'limit': self.risk_limits['max_drawdown']
                })
            
            # Check portfolio risk
            if metrics.total_exposure > self.risk_limits['max_portfolio_risk']:
                alerts.append({
                    'type': 'portfolio_risk_exceeded',
                    'level': RiskLevel.HIGH,
                    'message': f"Portfolio risk exceeded: {metrics.total_exposure:.2%}",
                    'value': metrics.total_exposure,
                    'limit': self.risk_limits['max_portfolio_risk']
                })
            
            # Check leverage
            if metrics.leverage_ratio > self.risk_limits['max_leverage']:
                alerts.append({
                    'type': 'leverage_exceeded',
                    'level': RiskLevel.HIGH,
                    'message': f"Leverage exceeded: {metrics.leverage_ratio:.2f}x",
                    'value': metrics.leverage_ratio,
                    'limit': self.risk_limits['max_leverage']
                })
            
            # Check concentration risk
            if metrics.concentration_risk > 0.3:  # 30% max concentration
                alerts.append({
                    'type': 'concentration_risk_high',
                    'level': RiskLevel.MEDIUM,
                    'message': f"High concentration risk: {metrics.concentration_risk:.2%}",
                    'value': metrics.concentration_risk,
                    'limit': 0.3
                })
            
            # Check correlation risk
            if metrics.correlation_risk > self.risk_limits['max_correlation']:
                alerts.append({
                    'type': 'correlation_risk_high',
                    'level': RiskLevel.MEDIUM,
                    'message': f"High correlation risk: {metrics.correlation_risk:.2%}",
                    'value': metrics.correlation_risk,
                    'limit': self.risk_limits['max_correlation']
                })
            
            # Store alerts
            self.risk_alerts.extend(alerts)
            
        except Exception as e:
            logger.error(f"Error checking risk alerts: {e}")
        
        return alerts
    
    async def get_position_risk_summary(self) -> Dict[str, Any]:
        """Get risk summary for all positions"""
        try:
            summary = {
                'total_positions': len(self.positions),
                'total_exposure': 0,
                'total_risk': 0,
                'positions': []
            }
            
            for symbol, position in self.positions.items():
                position_value = position.quantity * position.current_price
                position_risk = abs(position.entry_price - position.current_price) / position.entry_price
                
                summary['total_exposure'] += position_value
                summary['total_risk'] += position_risk
                
                summary['positions'].append({
                    'symbol': symbol,
                    'type': position.position_type.value,
                    'quantity': position.quantity,
                    'entry_price': position.entry_price,
                    'current_price': position.current_price,
                    'value': position_value,
                    'risk': position_risk,
                    'pnl': (position.current_price - position.entry_price) / position.entry_price,
                    'stop_loss': position.stop_loss,
                    'take_profit': position.take_profit
                })
            
            return summary
            
        except Exception as e:
            logger.error(f"Error getting position risk summary: {e}")
            return {}
    
    async def _validate_position_risk(self, position: Position) -> bool:
        """Validate if position meets risk requirements"""
        try:
            # Check position size
            portfolio_value = await self._get_portfolio_value()
            position_value = position.quantity * position.entry_price
            position_percentage = position_value / portfolio_value
            
            if position_percentage > self.risk_limits['max_position_size']:
                logger.warning(f"Position size too large: {position_percentage:.2%}")
                return False
            
            # Check total exposure
            total_exposure = await self._calculate_total_exposure()
            if total_exposure + position_percentage > self.risk_limits['max_portfolio_risk']:
                logger.warning(f"Total exposure would exceed limit: {total_exposure + position_percentage:.2%}")
                return False
            
            # Check correlation risk
            if await self._check_correlation_risk(position):
                logger.warning(f"High correlation risk for {position.symbol}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating position risk: {e}")
            return False
    
    async def _calculate_position_metrics(self, position: Position) -> Position:
        """Calculate additional position metrics"""
        try:
            # Calculate stop loss if not provided
            if position.stop_loss is None:
                position.stop_loss = await self.calculate_stop_loss(
                    position.symbol, position.entry_price, position.position_type
                )
            
            # Calculate take profit if not provided
            if position.take_profit is None:
                position.take_profit = await self.calculate_take_profit(
                    position.symbol, position.entry_price, position.position_type
                )
            
            # Calculate maximum loss
            if position.position_type == PositionType.LONG:
                position.max_loss = (position.entry_price - position.stop_loss) * position.quantity
            else:  # SHORT
                position.max_loss = (position.stop_loss - position.entry_price) * position.quantity
            
            return position
            
        except Exception as e:
            logger.error(f"Error calculating position metrics: {e}")
            return position
    
    async def _check_exit_conditions(self, position: Position) -> bool:
        """Check if position should be exited based on stop loss or take profit"""
        try:
            should_exit = False
            exit_reason = ""
            
            if position.position_type == PositionType.LONG:
                if position.current_price <= position.stop_loss:
                    should_exit = True
                    exit_reason = "Stop loss triggered"
                elif position.current_price >= position.take_profit:
                    should_exit = True
                    exit_reason = "Take profit triggered"
            else:  # SHORT
                if position.current_price >= position.stop_loss:
                    should_exit = True
                    exit_reason = "Stop loss triggered"
                elif position.current_price <= position.take_profit:
                    should_exit = True
                    exit_reason = "Take profit triggered"
            
            if should_exit:
                logger.info(f"Exit condition met for {position.symbol}: {exit_reason}")
                # In practice, this would trigger an exit order
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking exit conditions: {e}")
            return False
    
    async def _get_portfolio_value(self) -> float:
        """Get current portfolio value"""
        try:
            # In practice, this would calculate from actual positions and cash
            # For now, return a fixed value
            return 100000.0  # $100,000 portfolio
        except Exception as e:
            logger.error(f"Error getting portfolio value: {e}")
            return 0.0
    
    async def _calculate_total_exposure(self) -> float:
        """Calculate total portfolio exposure"""
        try:
            total_exposure = 0.0
            portfolio_value = await self._get_portfolio_value()
            
            for position in self.positions.values():
                position_value = position.quantity * position.current_price
                total_exposure += position_value / portfolio_value
            
            return total_exposure
            
        except Exception as e:
            logger.error(f"Error calculating total exposure: {e}")
            return 0.0
    
    async def _calculate_var(self, confidence_level: float) -> float:
        """Calculate Value at Risk"""
        try:
            # Simplified VaR calculation
            # In practice, this would use historical returns or Monte Carlo simulation
            
            if not self.portfolio_history:
                return 0.0
            
            returns = np.array(self.portfolio_history)
            var_percentile = (1 - confidence_level) * 100
            var = np.percentile(returns, var_percentile)
            
            return abs(var)
            
        except Exception as e:
            logger.error(f"Error calculating VaR: {e}")
            return 0.0
    
    async def _calculate_max_drawdown(self) -> float:
        """Calculate maximum drawdown"""
        try:
            if not self.portfolio_history:
                return 0.0
            
            portfolio_values = np.array(self.portfolio_history)
            peak = np.maximum.accumulate(portfolio_values)
            drawdown = (peak - portfolio_values) / peak
            max_drawdown = np.max(drawdown)
            
            return max_drawdown
            
        except Exception as e:
            logger.error(f"Error calculating max drawdown: {e}")
            return 0.0
    
    async def _calculate_sharpe_ratio(self) -> float:
        """Calculate Sharpe ratio"""
        try:
            if len(self.portfolio_history) < 2:
                return 0.0
            
            returns = np.diff(self.portfolio_history)
            if len(returns) == 0 or np.std(returns) == 0:
                return 0.0
            
            sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(252)
            return sharpe_ratio
            
        except Exception as e:
            logger.error(f"Error calculating Sharpe ratio: {e}")
            return 0.0
    
    async def _calculate_beta(self) -> float:
        """Calculate portfolio beta"""
        try:
            # Simplified beta calculation
            # In practice, this would compare portfolio returns to market returns
            return 1.0  # Default beta
        except Exception as e:
            logger.error(f"Error calculating beta: {e}")
            return 1.0
    
    async def _calculate_correlation_risk(self) -> float:
        """Calculate correlation risk between positions"""
        try:
            if len(self.positions) < 2:
                return 0.0
            
            # Simplified correlation risk calculation
            # In practice, this would calculate actual correlations between positions
            return 0.0  # Default no correlation risk
            
        except Exception as e:
            logger.error(f"Error calculating correlation risk: {e}")
            return 0.0
    
    async def _calculate_concentration_risk(self) -> float:
        """Calculate concentration risk (largest position percentage)"""
        try:
            if not self.positions:
                return 0.0
            
            portfolio_value = await self._get_portfolio_value()
            max_position_value = 0.0
            
            for position in self.positions.values():
                position_value = position.quantity * position.current_price
                max_position_value = max(max_position_value, position_value)
            
            concentration_risk = max_position_value / portfolio_value
            return concentration_risk
            
        except Exception as e:
            logger.error(f"Error calculating concentration risk: {e}")
            return 0.0
    
    async def _calculate_leverage_ratio(self) -> float:
        """Calculate portfolio leverage ratio"""
        try:
            # Simplified leverage calculation
            # In practice, this would calculate actual leverage from margin positions
            return 1.0  # Default no leverage
            
        except Exception as e:
            logger.error(f"Error calculating leverage ratio: {e}")
            return 1.0
    
    async def _check_correlation_risk(self, new_position: Position) -> bool:
        """Check if new position would create high correlation risk"""
        try:
            # Simplified correlation check
            # In practice, this would check actual correlations with existing positions
            return False  # Default no correlation risk
            
        except Exception as e:
            logger.error(f"Error checking correlation risk: {e}")
            return False
    
    async def _update_portfolio_risk(self):
        """Update portfolio risk metrics"""
        try:
            # Update portfolio history
            portfolio_value = await self._get_portfolio_value()
            self.portfolio_history.append(portfolio_value)
            
            # Keep only last 1000 values
            if len(self.portfolio_history) > 1000:
                self.portfolio_history = self.portfolio_history[-1000:]
            
        except Exception as e:
            logger.error(f"Error updating portfolio risk: {e}")
    
    async def cleanup(self):
        """Cleanup resources"""
        try:
            self.positions.clear()
            self.portfolio_history.clear()
            self.risk_alerts.clear()
            self.market_data.clear()
            logger.info("Risk management system cleaned up")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

# Global instance
risk_manager = RiskManagementSystem()
