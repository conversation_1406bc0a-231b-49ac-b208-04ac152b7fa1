"""
Documentation System for ASK Pipeline

Provides comprehensive documentation capabilities:
- Create comprehensive API documentation
- Add operational runbooks and troubleshooting guides
- Implement architecture decision records (ADRs)
- Create user guides and FAQ documentation
- Add changelog and release notes automation
"""

import os
import json
import logging
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from pathlib import Path
import yaml
import re

logger = logging.getLogger(__name__)

class DocumentationType(Enum):
    """Documentation type enumeration"""
    API = "api"
    RUNBOOK = "runbook"
    ADR = "adr"
    USER_GUIDE = "user_guide"
    FAQ = "faq"
    CHANGELOG = "changelog"
    RELEASE_NOTES = "release_notes"

class ADRStatus(Enum):
    """ADR status enumeration"""
    PROPOSED = "proposed"
    ACCEPTED = "accepted"
    DEPRECATED = "deprecated"
    SUPERSEDED = "superseded"

@dataclass
class ADR:
    """Architecture Decision Record"""
    number: int
    title: str
    status: ADRStatus
    date: datetime
    deciders: List[str]
    consulted: List[str] = field(default_factory=list)
    informed: List[str] = field(default_factory=list)
    context: str = ""
    decision: str = ""
    consequences: str = ""
    alternatives: List[str] = field(default_factory=list)
    links: List[str] = field(default_factory=list)

@dataclass
class Runbook:
    """Operational runbook"""
    name: str
    description: str
    category: str
    severity: str
    steps: List[Dict[str, Any]] = field(default_factory=list)
    prerequisites: List[str] = field(default_factory=list)
    expected_duration: str = ""
    rollback_steps: List[str] = field(default_factory=list)
    contacts: List[str] = field(default_factory=list)
    last_updated: datetime = field(default_factory=datetime.utcnow)

@dataclass
class FAQ:
    """Frequently Asked Question"""
    question: str
    answer: str
    category: str
    tags: List[str] = field(default_factory=list)
    last_updated: datetime = field(default_factory=datetime.utcnow)

class DocumentationGenerator:
    """Documentation generation system"""
    
    def __init__(self, output_dir: str = "docs"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # Create subdirectories
        (self.output_dir / "api").mkdir(exist_ok=True)
        (self.output_dir / "runbooks").mkdir(exist_ok=True)
        (self.output_dir / "adrs").mkdir(exist_ok=True)
        (self.output_dir / "user_guides").mkdir(exist_ok=True)
        (self.output_dir / "faq").mkdir(exist_ok=True)
        (self.output_dir / "changelog").mkdir(exist_ok=True)
        (self.output_dir / "release_notes").mkdir(exist_ok=True)
    
    def generate_api_documentation(self, api_contracts: Dict[str, Any]) -> str:
        """Generate API documentation"""
        api_doc = f"""# ASK Pipeline API Documentation

Generated on: {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')}

## Overview

The ASK Pipeline provides a comprehensive API for processing natural language queries related to financial data and market analysis.

## Base URL

- **Development**: `http://localhost:8000`
- **Staging**: `https://staging.ask-pipeline.com`
- **Production**: `https://ask-pipeline.com`

## Authentication

All API endpoints require authentication using API keys or OAuth tokens.

### API Key Authentication

```http
Authorization: Bearer YOUR_API_KEY
```

### OAuth Authentication

```http
Authorization: Bearer YOUR_OAUTH_TOKEN
```

## Rate Limiting

API requests are rate limited per user:
- **Free Tier**: 100 requests per hour
- **Premium Tier**: 1000 requests per hour
- **Enterprise**: 10000 requests per hour

Rate limit headers are included in responses:
- `X-RateLimit-Limit`: Maximum requests per hour
- `X-RateLimit-Remaining`: Remaining requests in current window
- `X-RateLimit-Reset`: Time when the rate limit resets

## Error Handling

The API uses standard HTTP status codes and returns error details in JSON format:

```json
{{
    "error": {{
        "code": "VALIDATION_ERROR",
        "message": "Invalid request parameters",
        "details": {{
            "field": "query",
            "reason": "Query cannot be empty"
        }}
    }}
}}
```

## Endpoints

"""
        
        # Add endpoint documentation
        for endpoint_name, contract in api_contracts.items():
            api_doc += self._generate_endpoint_documentation(endpoint_name, contract)
        
        # Write to file
        api_file = self.output_dir / "api" / "index.md"
        with open(api_file, 'w') as f:
            f.write(api_doc)
        
        logger.info(f"API documentation generated: {api_file}")
        return str(api_file)
    
    def _generate_endpoint_documentation(self, endpoint_name: str, contract: Dict[str, Any]) -> str:
        """Generate documentation for a single endpoint"""
        doc = f"""
### {endpoint_name.replace('_', ' ').title()}

**Endpoint**: `POST /api/v1/{endpoint_name}`

**Description**: {contract.get('description', 'No description available')}

**Request Body**:
```json
{{
    "query": "What is the price of AAPL?",
    "user_id": "user_123",
    "guild_id": "guild_456",
    "context": {{}}
}}
```

**Response**:
```json
{{
    "success": true,
    "response": "The current price of AAPL is $150.25",
    "intent": "price_query",
    "confidence": 0.95,
    "execution_time": 1.2,
    "timestamp": "2024-01-01T12:00:00Z"
}}
```

**Error Responses**:
- `400 Bad Request`: Invalid request parameters
- `401 Unauthorized`: Invalid or missing authentication
- `429 Too Many Requests`: Rate limit exceeded
- `500 Internal Server Error`: Server error

"""
        return doc
    
    def generate_runbook(self, runbook: Runbook) -> str:
        """Generate operational runbook"""
        runbook_content = f"""# {runbook.name}

**Category**: {runbook.category}  
**Severity**: {runbook.severity}  
**Expected Duration**: {runbook.expected_duration}  
**Last Updated**: {runbook.last_updated.strftime('%Y-%m-%d %H:%M:%S')}

## Description

{runbook.description}

## Prerequisites

{self._format_list(runbook.prerequisites)}

## Steps

"""
        
        for i, step in enumerate(runbook.steps, 1):
            runbook_content += f"""
### Step {i}: {step.get('title', f'Step {i}')}

{step.get('description', '')}

**Command**:
```bash
{step.get('command', '')}
```

**Expected Output**:
```
{step.get('expected_output', '')}
```

**Verification**:
{step.get('verification', '')}

"""
        
        if runbook.rollback_steps:
            runbook_content += f"""
## Rollback Steps

{self._format_list(runbook.rollback_steps)}
"""
        
        if runbook.contacts:
            runbook_content += f"""
## Contacts

{self._format_list(runbook.contacts)}
"""
        
        # Write to file
        runbook_file = self.output_dir / "runbooks" / f"{runbook.name.lower().replace(' ', '_')}.md"
        with open(runbook_file, 'w') as f:
            f.write(runbook_content)
        
        logger.info(f"Runbook generated: {runbook_file}")
        return str(runbook_file)
    
    def generate_adr(self, adr: ADR) -> str:
        """Generate Architecture Decision Record"""
        adr_content = f"""# {adr.number}. {adr.title}

**Status**: {adr.status.value}  
**Date**: {adr.date.strftime('%Y-%m-%d')}

## Context

{adr.context}

## Decision

{adr.decision}

## Consequences

{adr.consequences}

## Alternatives Considered

{self._format_list(adr.alternatives)}

## Stakeholders

**Deciders**: {', '.join(adr.deciders)}

**Consulted**: {', '.join(adr.consulted)}

**Informed**: {', '.join(adr.informed)}

## Links

{self._format_list(adr.links)}
"""
        
        # Write to file
        adr_file = self.output_dir / "adrs" / f"{adr.number:03d}-{adr.title.lower().replace(' ', '-')}.md"
        with open(adr_file, 'w') as f:
            f.write(adr_content)
        
        logger.info(f"ADR generated: {adr_file}")
        return str(adr_file)
    
    def generate_user_guide(self) -> str:
        """Generate user guide"""
        user_guide = f"""# ASK Pipeline User Guide

Welcome to the ASK Pipeline user guide! This guide will help you get started with using the ASK Pipeline for financial data queries and market analysis.

## Table of Contents

1. [Getting Started](#getting-started)
2. [Basic Usage](#basic-usage)
3. [Advanced Features](#advanced-features)
4. [Troubleshooting](#troubleshooting)
5. [Best Practices](#best-practices)

## Getting Started

### Prerequisites

- Discord account
- Access to a server with the ASK Pipeline bot
- Basic understanding of financial terminology

### Installation

The ASK Pipeline is already installed on your Discord server. No additional installation is required.

### First Steps

1. Join a Discord server that has the ASK Pipeline bot
2. Use the `/ask` command to start querying financial data
3. Follow the prompts to get started

## Basic Usage

### Simple Queries

The most basic way to use ASK Pipeline is to ask simple questions:

```
/ask What is the price of AAPL?
/ask Show me the chart for TSLA
/ask Get news about Bitcoin
```

### Query Types

ASK Pipeline supports several types of queries:

#### Price Queries
- "What is the price of [symbol]?"
- "How much is [symbol] worth?"
- "Current price of [symbol]"

#### Analysis Queries
- "Analyze [symbol]"
- "What's the trend for [symbol]?"
- "Technical analysis of [symbol]"

#### News Queries
- "News about [symbol]"
- "Latest updates on [symbol]"
- "What's happening with [symbol]?"

#### Chart Queries
- "Show me the chart for [symbol]"
- "Graph of [symbol]"
- "Price chart for [symbol]"

## Advanced Features

### Context-Aware Queries

ASK Pipeline can understand context from previous messages:

```
User: What's the price of AAPL?
Bot: The current price of AAPL is $150.25

User: What about its trend?
Bot: AAPL is currently in an uptrend with a 5% gain over the past week
```

### Portfolio Queries

You can ask about multiple symbols at once:

```
/ask Compare AAPL, GOOGL, and MSFT
/ask Show me my watchlist performance
```

### Custom Timeframes

Specify timeframes for your queries:

```
/ask What was AAPL's price yesterday?
/ask Show me TSLA's chart for the past month
/ask Bitcoin news from last week
```

## Troubleshooting

### Common Issues

#### Bot Not Responding
- Check if the bot is online
- Verify you have the correct permissions
- Try using the `/ask` command again

#### Invalid Symbol
- Make sure the symbol is correct
- Check if the symbol is supported
- Try using the full company name

#### Rate Limiting
- You've exceeded the rate limit
- Wait a few minutes before trying again
- Consider upgrading to a higher tier

### Error Messages

#### "Symbol not found"
The symbol you provided is not recognized. Try:
- Using the correct ticker symbol
- Using the full company name
- Checking the symbol on a financial website

#### "Rate limit exceeded"
You've made too many requests. Wait before trying again.

#### "Invalid query format"
Your query doesn't match the expected format. Try rephrasing your question.

## Best Practices

### Writing Effective Queries

1. **Be Specific**: Instead of "stock info", ask "What is the price of AAPL?"
2. **Use Clear Language**: Avoid jargon and complex sentences
3. **Include Context**: Mention timeframes when relevant
4. **Ask One Thing**: Keep queries focused on a single topic

### Getting Better Results

1. **Use Ticker Symbols**: "AAPL" instead of "Apple Inc."
2. **Be Patient**: Complex queries may take a moment to process
3. **Provide Feedback**: Use reactions to help improve the bot
4. **Stay Updated**: Check for new features and updates

### Privacy and Security

1. **Don't Share Personal Information**: The bot doesn't need your personal details
2. **Be Mindful of Sensitive Data**: Don't share account numbers or passwords
3. **Report Issues**: Contact support if you notice any security concerns

## Support

If you need help or have questions:

1. Check this user guide first
2. Look at the FAQ section
3. Contact your server administrator
4. Join our support Discord server

## Changelog

See the [changelog](changelog/) for recent updates and new features.

---

*Last updated: {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')}*
"""
        
        # Write to file
        user_guide_file = self.output_dir / "user_guides" / "index.md"
        with open(user_guide_file, 'w') as f:
            f.write(user_guide)
        
        logger.info(f"User guide generated: {user_guide_file}")
        return str(user_guide_file)
    
    def generate_faq(self, faqs: List[FAQ]) -> str:
        """Generate FAQ documentation"""
        faq_content = f"""# Frequently Asked Questions

*Last updated: {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')}*

## General Questions

"""
        
        # Group FAQs by category
        categories = {}
        for faq in faqs:
            if faq.category not in categories:
                categories[faq.category] = []
            categories[faq.category].append(faq)
        
        # Generate FAQ content
        for category, category_faqs in categories.items():
            faq_content += f"### {category}\n\n"
            
            for faq in category_faqs:
                faq_content += f"""
**Q: {faq.question}**

A: {faq.answer}

"""
        
        # Write to file
        faq_file = self.output_dir / "faq" / "index.md"
        with open(faq_file, 'w') as f:
            f.write(faq_content)
        
        logger.info(f"FAQ generated: {faq_file}")
        return str(faq_file)
    
    def generate_changelog(self, changelog_data: List[Dict[str, Any]]) -> str:
        """Generate changelog"""
        changelog_content = f"""# Changelog

All notable changes to the ASK Pipeline will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- New features that have been added but not yet released

### Changed
- Changes to existing functionality

### Deprecated
- Features that are no longer recommended

### Removed
- Features that have been removed

### Fixed
- Bug fixes

### Security
- Security improvements

---

"""
        
        # Add version entries
        for entry in changelog_data:
            version = entry.get('version', 'Unknown')
            date = entry.get('date', 'Unknown')
            
            changelog_content += f"## [{version}] - {date}\n\n"
            
            for change_type in ['Added', 'Changed', 'Deprecated', 'Removed', 'Fixed', 'Security']:
                changes = entry.get(change_type.lower(), [])
                if changes:
                    changelog_content += f"### {change_type}\n"
                    for change in changes:
                        changelog_content += f"- {change}\n"
                    changelog_content += "\n"
            
            changelog_content += "---\n\n"
        
        # Write to file
        changelog_file = self.output_dir / "changelog" / "CHANGELOG.md"
        with open(changelog_file, 'w') as f:
            f.write(changelog_content)
        
        logger.info(f"Changelog generated: {changelog_file}")
        return str(changelog_file)
    
    def generate_release_notes(self, version: str, release_data: Dict[str, Any]) -> str:
        """Generate release notes"""
        release_notes = f"""# Release Notes - {version}

**Release Date**: {release_data.get('date', datetime.utcnow().strftime('%Y-%m-%d'))}  
**Release Type**: {release_data.get('type', 'Minor')}

## Overview

{release_data.get('overview', 'This release includes various improvements and bug fixes.')}

## What's New

### New Features

{self._format_list(release_data.get('new_features', []))}

### Improvements

{self._format_list(release_data.get('improvements', []))}

### Bug Fixes

{self._format_list(release_data.get('bug_fixes', []))}

### Security Updates

{self._format_list(release_data.get('security_updates', []))}

## Breaking Changes

{self._format_list(release_data.get('breaking_changes', []))}

## Migration Guide

{release_data.get('migration_guide', 'No migration required for this release.')}

## Installation

### Docker

```bash
docker pull ask-pipeline:{version}
```

### Kubernetes

```bash
kubectl apply -f k8s/production/
```

## Upgrade Instructions

{release_data.get('upgrade_instructions', 'Follow the standard upgrade process for your deployment method.')}

## Support

For questions or issues with this release:

- Check the [documentation](https://docs.ask-pipeline.com)
- Join our [Discord server](https://discord.gg/ask-pipeline)
- Open an issue on [GitHub](https://github.com/ask-pipeline/issues)

---

*Generated on: {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')}*
"""
        
        # Write to file
        release_notes_file = self.output_dir / "release_notes" / f"v{version}.md"
        with open(release_notes_file, 'w') as f:
            f.write(release_notes)
        
        logger.info(f"Release notes generated: {release_notes_file}")
        return str(release_notes_file)
    
    def _format_list(self, items: List[str]) -> str:
        """Format list items for markdown"""
        if not items:
            return "None"
        
        return "\n".join(f"- {item}" for item in items)
    
    def generate_index(self) -> str:
        """Generate main documentation index"""
        index_content = f"""# ASK Pipeline Documentation

Welcome to the ASK Pipeline documentation! This comprehensive guide covers everything you need to know about using, deploying, and maintaining the ASK Pipeline.

## Quick Start

- [User Guide](user_guides/index.md) - Get started with using the ASK Pipeline
- [API Documentation](api/index.md) - Complete API reference
- [Deployment Guide](deployment/index.md) - Deploy and configure the system

## Documentation Sections

### For Users
- [User Guide](user_guides/index.md) - How to use the ASK Pipeline
- [FAQ](faq/index.md) - Frequently asked questions
- [Troubleshooting](troubleshooting/index.md) - Common issues and solutions

### For Developers
- [API Documentation](api/index.md) - Complete API reference
- [Architecture Decision Records](adrs/) - Technical decisions and rationale
- [Development Guide](development/index.md) - Contributing and development

### For Operators
- [Runbooks](runbooks/) - Operational procedures
- [Monitoring Guide](monitoring/index.md) - Monitoring and alerting
- [Deployment Guide](deployment/index.md) - Deployment and configuration

### For Administrators
- [Security Guide](security/index.md) - Security best practices
- [Performance Tuning](performance/index.md) - Optimization and tuning
- [Backup and Recovery](backup/index.md) - Data protection procedures

## Release Information

- [Changelog](changelog/CHANGELOG.md) - Complete change history
- [Release Notes](release_notes/) - Detailed release information
- [Upgrade Guide](upgrade/index.md) - Version upgrade procedures

## Support

- [Discord Server](https://discord.gg/ask-pipeline) - Community support
- [GitHub Issues](https://github.com/ask-pipeline/issues) - Bug reports and feature requests
- [Email Support](mailto:<EMAIL>) - Direct support contact

---

*Last updated: {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')}*
"""
        
        # Write to file
        index_file = self.output_dir / "index.md"
        with open(index_file, 'w') as f:
            f.write(index_content)
        
        logger.info(f"Documentation index generated: {index_file}")
        return str(index_file)

# Global instances
_documentation_generator = DocumentationGenerator()

def get_documentation_generator() -> DocumentationGenerator:
    """Get global documentation generator"""
    return _documentation_generator

def cleanup_documentation():
    """Cleanup documentation components"""
    global _documentation_generator
    _documentation_generator = DocumentationGenerator()
