"""
Unified Cache Manager for ASK Pipeline

Consolidates all caching functionality with Redis backend support,
intelligent invalidation, warming, and performance optimization.

This replaces multiple cache implementations with a single, unified approach.
"""

import asyncio
import json
import hashlib
from typing import Dict, Any, Optional, List, Union, TYPE_CHECKING
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta

from src.shared.error_handling.logging import get_logger
from src.shared.cache.cache_service import CacheService
from .intelligent_cache import IntelligentCache, CacheStats

# Use TYPE_CHECKING to avoid circular imports
if TYPE_CHECKING:
    from ..stages.intent_detector import IntentResult
    from ..stages.simplified_tool_orchestrator import ToolResult

logger = get_logger(__name__)

@dataclass
class UnifiedCacheConfig:
    """Configuration for unified cache manager"""
    enabled: bool = True
    
    # Backend configuration
    use_redis: bool = True
    fallback_to_memory: bool = True
    
    # Memory limits
    max_memory_mb: int = 50  # Reduced since Redis handles most storage
    max_entries: int = 1000
    
    # TTL settings
    default_ttl: float = 300  # 5 minutes
    intent_ttl: float = 600   # 10 minutes (intents change less frequently)
    tool_ttl: float = 180     # 3 minutes (tool results can be more dynamic)
    response_ttl: float = 300 # 5 minutes (responses should be fresh)
    
    # Stage-specific caching
    cache_intent: bool = True
    cache_tools: bool = True
    cache_responses: bool = True
    
    # Performance settings
    async_cache_writes: bool = True
    cache_warming_enabled: bool = False
    cleanup_interval: float = 60
    
    # Redis-specific settings
    redis_key_prefix: str = "ask_pipeline"
    redis_compression: bool = True
    
    # Smart caching features
    intelligent_ttl: bool = True  # Adjust TTL based on content
    cache_analytics: bool = True  # Track cache performance

@dataclass
class CacheMetrics:
    """Cache performance metrics"""
    total_requests: int = 0
    cache_hits: int = 0
    cache_misses: int = 0
    redis_hits: int = 0
    memory_hits: int = 0
    write_operations: int = 0
    evictions: int = 0
    errors: int = 0
    
    @property
    def hit_rate(self) -> float:
        """Calculate overall cache hit rate"""
        if self.total_requests == 0:
            return 0.0
        return (self.cache_hits / self.total_requests) * 100
    
    @property
    def redis_hit_rate(self) -> float:
        """Calculate Redis-specific hit rate"""
        if self.cache_hits == 0:
            return 0.0
        return (self.redis_hits / self.cache_hits) * 100

class UnifiedCacheManager:
    """
    Unified cache management for ASK pipeline
    
    Features:
    - Redis backend with memory fallback
    - Multi-stage caching (intent, tools, responses)
    - Intelligent TTL management
    - Performance monitoring and analytics
    - Smart cache warming
    - Automatic invalidation
    - Compression and optimization
    """
    
    def __init__(self, config: UnifiedCacheConfig = None):
        self.config = config or UnifiedCacheConfig()
        
        if not self.config.enabled:
            logger.info("Unified cache disabled by configuration")
            self.enabled = False
            return
        
        self.enabled = True
        self.metrics = CacheMetrics()
        
        # Initialize Redis cache service (lazy initialization)
        self.redis_cache = None
        self._redis_initialized = False
        if self.config.use_redis:
            self.redis_cache = CacheService()
        
        # Initialize memory fallback cache
        self.memory_cache = None
        if self.config.fallback_to_memory:
            self.memory_cache = IntelligentCache(
                max_memory_mb=self.config.max_memory_mb,
                default_ttl=self.config.default_ttl,
                max_entries=self.config.max_entries
            )
        
        # Cache warming patterns
        self.warming_patterns = [
            "What is the price of AAPL?",
            "How is the market doing today?",
            "What can you help me with?",
            "Hello",
            "Show me TSLA analysis",
            "What is Bitcoin price?",
            "Market overview",
            "Help"
        ]
        
        logger.info(f"✅ Unified cache manager initialized (Redis: {self.config.use_redis}, Memory: {self.config.max_memory_mb}MB)")
        
        # Disable cache warming for now to avoid circular imports
        # if self.config.cache_warming_enabled:
        #     asyncio.create_task(self._warm_cache())
    
    async def _ensure_redis_initialized(self):
        """Ensure Redis connection is initialized (lazy initialization)"""
        if self._redis_initialized or not self.redis_cache:
            return

        try:
            await self.redis_cache.initialize()
            self._redis_initialized = True
            logger.info("✅ Redis cache backend initialized")
        except Exception as e:
            logger.warning(f"⚠️ Redis initialization failed: {e}")
            if not self.config.fallback_to_memory:
                logger.error("No fallback cache available, caching disabled")
                self.enabled = False

    def _generate_cache_key(self, stage: str, query: str, **kwargs) -> str:
        """Generate a consistent cache key for a stage and query"""
        # Create a hash of the query and additional parameters
        key_data = {
            'stage': stage,
            'query': query.lower().strip(),
            **kwargs
        }
        
        # Create deterministic hash
        key_string = json.dumps(key_data, sort_keys=True)
        key_hash = hashlib.md5(key_string.encode()).hexdigest()
        
        return f"{self.config.redis_key_prefix}:{stage}:{key_hash}"
    
    def _determine_ttl(self, stage: str, query: str, data: Any = None) -> float:
        """Determine appropriate TTL based on stage and content"""
        if not self.config.intelligent_ttl:
            return getattr(self.config, f"{stage}_ttl", self.config.default_ttl)
        
        query_lower = query.lower()
        
        # Stage-specific TTL logic
        if stage == "intent":
            # Intents are relatively stable
            return self.config.intent_ttl
        
        elif stage == "tools":
            # Price data expires quickly
            if any(word in query_lower for word in ['price', 'quote', 'current', 'now']):
                return 60  # 1 minute for real-time data
            
            # Technical analysis has medium expiration
            if any(word in query_lower for word in ['rsi', 'macd', 'analysis', 'chart']):
                return 180  # 3 minutes
            
            # Company data has longer expiration
            if any(word in query_lower for word in ['company', 'overview', 'info']):
                return 1800  # 30 minutes
            
            return self.config.tool_ttl
        
        elif stage == "response":
            # Response TTL based on content freshness
            return self.config.response_ttl
        
        return self.config.default_ttl

    async def _get_from_cache(self, key: str, stage: str) -> Optional[Any]:
        """Get value from cache (Redis first, then memory fallback)"""
        self.metrics.total_requests += 1

        try:
            # Try Redis first
            if self.redis_cache and self.config.use_redis:
                await self._ensure_redis_initialized()
                value = await self.redis_cache.get(key)
                if value is not None:
                    self.metrics.cache_hits += 1
                    self.metrics.redis_hits += 1
                    logger.debug(f"Redis cache hit for {stage}", extra={'cache_key': key})
                    return value
            
            # Fallback to memory cache
            if self.memory_cache and self.config.fallback_to_memory:
                value = await self.memory_cache.get(
                    query=key,  # Use key as query for memory cache
                    context={'stage': stage}
                )
                if value is not None:
                    self.metrics.cache_hits += 1
                    self.metrics.memory_hits += 1
                    logger.debug(f"Memory cache hit for {stage}", extra={'cache_key': key})
                    return value
            
            # Cache miss
            self.metrics.cache_misses += 1
            return None
            
        except Exception as e:
            logger.warning(f"Cache get error for {stage}: {e}", extra={'cache_key': key})
            self.metrics.errors += 1
            return None

    async def _set_in_cache(self, key: str, value: Any, ttl: float, stage: str) -> bool:
        """Set value in cache (Redis and optionally memory)"""
        try:
            self.metrics.write_operations += 1
            success = False

            # Store in Redis
            if self.redis_cache and self.config.use_redis:
                await self._ensure_redis_initialized()
                redis_success = await self.redis_cache.set(key, value, ttl=int(ttl))
                if redis_success:
                    success = True
                    logger.debug(f"Stored in Redis cache for {stage}", extra={'cache_key': key, 'ttl': ttl})
            
            # Store in memory cache as backup/faster access
            if self.memory_cache and self.config.fallback_to_memory:
                memory_success = await self.memory_cache.set(
                    query=key,  # Use key as query for memory cache
                    value=value,
                    ttl=ttl,
                    context={'stage': stage}
                )
                if memory_success:
                    success = True
                    logger.debug(f"Stored in memory cache for {stage}", extra={'cache_key': key, 'ttl': ttl})
            
            return success
            
        except Exception as e:
            logger.warning(f"Cache set error for {stage}: {e}", extra={'cache_key': key})
            self.metrics.errors += 1
            return False

    # Intent Cache Methods
    async def get_intent_cache(self, query: str, correlation_id: str) -> Optional['IntentResult']:
        """Get cached intent result"""
        if not self.enabled or not self.config.cache_intent:
            return None

        try:
            cache_key = self._generate_cache_key(
                stage="intent",
                query=query,
                correlation_id=correlation_id
            )

            cached_data = await self._get_from_cache(cache_key, "intent")

            if cached_data:
                logger.debug(f"Intent cache hit", extra={'correlation_id': correlation_id})
                return IntentResult(**cached_data)

            return None

        except Exception as e:
            logger.warning(f"Intent cache get error: {e}", extra={'correlation_id': correlation_id})
            return None

    async def set_intent_cache(self, query: str, intent_result: 'IntentResult', correlation_id: str) -> bool:
        """Cache intent result"""
        if not self.enabled or not self.config.cache_intent:
            return False

        try:
            cache_key = self._generate_cache_key(
                stage="intent",
                query=query,
                correlation_id=correlation_id
            )

            cache_data = {
                'intent': intent_result.intent,
                'confidence': intent_result.confidence,
                'reasoning': intent_result.reasoning,
                'execution_time': intent_result.execution_time,
                'entities': intent_result.entities or {}
            }

            ttl = self._determine_ttl("intent", query, cache_data)

            if self.config.async_cache_writes:
                # Async cache write
                asyncio.create_task(self._set_in_cache(cache_key, cache_data, ttl, "intent"))
                return True
            else:
                return await self._set_in_cache(cache_key, cache_data, ttl, "intent")

        except Exception as e:
            logger.warning(f"Intent cache set error: {e}", extra={'correlation_id': correlation_id})
            return False

    # Tool Cache Methods
    async def get_tool_cache(self, query: str, intent_result: 'IntentResult', correlation_id: str) -> Optional['ToolResult']:
        """Get cached tool result"""
        if not self.enabled or not self.config.cache_tools:
            return None

        try:
            cache_key = self._generate_cache_key(
                stage="tools",
                query=query,
                intent=intent_result.intent,
                entities=intent_result.entities or {},
                correlation_id=correlation_id
            )

            cached_data = await self._get_from_cache(cache_key, "tools")

            if cached_data:
                logger.debug(f"Tool cache hit", extra={'correlation_id': correlation_id})
                return ToolResult(**cached_data)

            return None

        except Exception as e:
            logger.warning(f"Tool cache get error: {e}", extra={'correlation_id': correlation_id})
            return None

    async def set_tool_cache(self, query: str, intent_result: 'IntentResult', tool_result: 'ToolResult', correlation_id: str) -> bool:
        """Cache tool result"""
        if not self.enabled or not self.config.cache_tools:
            return False

        try:
            cache_key = self._generate_cache_key(
                stage="tools",
                query=query,
                intent=intent_result.intent,
                entities=intent_result.entities or {},
                correlation_id=correlation_id
            )

            cache_data = {
                'tools_used': tool_result.tools_used,
                'results': tool_result.results,
                'execution_time': tool_result.execution_time,
                'confidence': tool_result.confidence,
                'cache_hit': False  # This is a new result
            }

            ttl = self._determine_ttl("tools", query, cache_data)

            if self.config.async_cache_writes:
                # Async cache write
                asyncio.create_task(self._set_in_cache(cache_key, cache_data, ttl, "tools"))
                return True
            else:
                return await self._set_in_cache(cache_key, cache_data, ttl, "tools")

        except Exception as e:
            logger.warning(f"Tool cache set error: {e}", extra={'correlation_id': correlation_id})
            return False

    # Response Cache Methods
    async def get_response_cache(self, query: str, intent_result: 'IntentResult', tool_result: Optional['ToolResult'], correlation_id: str) -> Optional[Any]:
        """Get cached response result"""
        if not self.enabled or not self.config.cache_responses:
            return None

        try:
            cache_key = self._generate_cache_key(
                stage="response",
                query=query,
                intent=intent_result.intent,
                tools_used=tool_result.tools_used if tool_result else [],
                correlation_id=correlation_id
            )

            cached_data = await self._get_from_cache(cache_key, "response")

            if cached_data:
                logger.debug(f"Response cache hit", extra={'correlation_id': correlation_id})
                return cached_data

            return None

        except Exception as e:
            logger.warning(f"Response cache get error: {e}", extra={'correlation_id': correlation_id})
            return None

    async def set_response_cache(self, query: str, intent_result: 'IntentResult', tool_result: Optional['ToolResult'], response_result: Any, correlation_id: str) -> bool:
        """Cache response result"""
        if not self.enabled or not self.config.cache_responses:
            return False

        try:
            cache_key = self._generate_cache_key(
                stage="response",
                query=query,
                intent=intent_result.intent,
                tools_used=tool_result.tools_used if tool_result else [],
                correlation_id=correlation_id
            )

            # Convert response to cacheable format
            cache_data = {
                'response': response_result.response if hasattr(response_result, 'response') else str(response_result),
                'confidence': getattr(response_result, 'confidence', 1.0),
                'execution_time': getattr(response_result, 'execution_time', 0.0),
                'metadata': getattr(response_result, 'metadata', {})
            }

            ttl = self._determine_ttl("response", query, cache_data)

            if self.config.async_cache_writes:
                # Async cache write
                asyncio.create_task(self._set_in_cache(cache_key, cache_data, ttl, "response"))
                return True
            else:
                return await self._set_in_cache(cache_key, cache_data, ttl, "response")

        except Exception as e:
            logger.warning(f"Response cache set error: {e}", extra={'correlation_id': correlation_id})
            return False

    # Utility Methods
    async def invalidate_cache(self, pattern: Optional[str] = None) -> int:
        """Invalidate cache entries matching pattern"""
        try:
            invalidated_count = 0

            # 1. Invalidate Redis Cache
            if self.redis_cache and self.config.use_redis:
                await self._ensure_redis_initialized()
                search_pattern = pattern if pattern is not None else f"{self.config.redis_key_prefix}:*"
                
                # Use SCAN to avoid blocking the Redis server in production.
                # This assumes self.redis_cache.redis provides access to the raw redis-py client.
                keys_to_delete = [key async for key in self.redis_cache.redis.scan_iter(match=search_pattern)]
                
                if keys_to_delete:
                    deleted_count = await self.redis_cache.redis.delete(*keys_to_delete)
                    invalidated_count += deleted_count
                    logger.info(f"Invalidated {deleted_count} keys from Redis matching '{search_pattern}'")

            # 2. Invalidate Memory Cache
            if self.memory_cache and self.config.fallback_to_memory:
                await self.memory_cache.clear()
                logger.info("Memory cache cleared")

            return invalidated_count

        except Exception as e:
            logger.warning(f"Cache invalidation error: {e}")
            return 0

    def get_metrics(self) -> CacheMetrics:
        """Get cache performance metrics"""
        return self.metrics

    async def get_cache_stats(self) -> Dict[str, Any]:
        """Get comprehensive cache statistics"""
        stats = {
            'enabled': self.enabled,
            'config': asdict(self.config),
            'metrics': asdict(self.metrics),
            'backends': {
                'redis': self.redis_cache is not None,
                'memory': self.memory_cache is not None
            }
        }

        if self.memory_cache:
            memory_stats = await self.memory_cache.get_stats()
            stats['memory_cache'] = asdict(memory_stats)

        return stats

    async def _warm_cache(self):
        """Warm cache with common queries"""
        logger.info("Starting unified cache warming...")

        try:
            # Import here to avoid circular imports
            from ..pipeline import AskPipeline
            from ..config import AskConfig

            config = AskConfig()
            pipeline = AskPipeline(config)

            for pattern in self.warming_patterns:
                try:
                    await pipeline.process(pattern, f"warm-{hash(pattern)}")
                    await asyncio.sleep(0.1)  # Small delay between requests
                except Exception as e:
                    logger.warning(f"Cache warming failed for pattern '{pattern}': {e}")

            logger.info(f"Unified cache warming completed for {len(self.warming_patterns)} patterns")

        except Exception as e:
            logger.error(f"Cache warming error: {e}")

# Backward compatibility aliases
class CacheManager(UnifiedCacheManager):
    """Backward compatibility wrapper for UnifiedCacheManager"""

    def __init__(self, config=None):
        if config and hasattr(config, '__dict__'):
            # Convert old CacheConfig to UnifiedCacheConfig
            unified_config = UnifiedCacheConfig(
                enabled=getattr(config, 'enabled', True),
                max_memory_mb=getattr(config, 'max_memory_mb', 50),
                default_ttl=getattr(config, 'default_ttl', 300),
                max_entries=getattr(config, 'max_entries', 1000),
                cache_intent=getattr(config, 'cache_intent', True),
                cache_tools=getattr(config, 'cache_tools', True),
                cache_responses=getattr(config, 'cache_responses', True),
                async_cache_writes=getattr(config, 'async_cache_writes', True),
                cache_warming_enabled=getattr(config, 'cache_warming_enabled', False)
            )
            super().__init__(unified_config)
        else:
            super().__init__(config)

# For backward compatibility, also provide the old config class
CacheConfig = UnifiedCacheConfig
