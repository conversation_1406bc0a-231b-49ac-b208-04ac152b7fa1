"""
Cache Module for ASK Pipeline

Provides unified caching capabilities with Redis backend support,
intelligent TTL management, and performance optimization.

This module consolidates all cache implementations into a single,
unified approach with backward compatibility.
"""

from .intelligent_cache import IntelligentCache, CacheEntry, CacheStats
from .unified_cache import (
    UnifiedCacheManager,
    UnifiedCacheConfig,
    CacheMetrics,
    # Backward compatibility aliases
    CacheManager,
    CacheConfig
)

__all__ = [
    # New unified cache
    'UnifiedCacheManager',
    'UnifiedCacheConfig',
    'CacheMetrics',

    # Legacy intelligent cache
    'IntelligentCache',
    'CacheEntry',
    'CacheStats',

    # Backward compatibility
    'CacheManager',
    'CacheConfig'
]
