"""
Event Bus for ASK Pipeline

Provides event-driven architecture patterns:
- Implement event bus for asynchronous communication
- Add event sourcing and CQRS patterns
- Create event handlers and subscribers
- Implement event persistence and replay
- Add event versioning and migration
"""

import asyncio
import logging
import json
from typing import Dict, Any, Optional, List, Union, Callable, Type
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import uuid
import inspect

logger = logging.getLogger(__name__)

class EventType(Enum):
    """Event type enumeration"""
    USER_QUERY = "user_query"
    AI_RESPONSE = "ai_response"
    CACHE_HIT = "cache_hit"
    CACHE_MISS = "cache_miss"
    ERROR_OCCURRED = "error_occurred"
    METRICS_COLLECTED = "metrics_collected"
    AUDIT_LOG = "audit_log"
    SYSTEM_EVENT = "system_event"

class EventStatus(Enum):
    """Event status enumeration"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    RETRYING = "retrying"

@dataclass
class Event:
    """Event data structure"""
    event_id: str
    event_type: EventType
    timestamp: datetime
    source: str
    data: Dict[str, Any]
    metadata: Dict[str, Any] = field(default_factory=dict)
    version: int = 1
    correlation_id: Optional[str] = None
    causation_id: Optional[str] = None

@dataclass
class EventHandler:
    """Event handler information"""
    handler_id: str
    event_type: EventType
    handler_function: Callable
    priority: int = 0
    enabled: bool = True
    retry_count: int = 3
    retry_delay: int = 1
    timeout: int = 30

@dataclass
class EventSubscription:
    """Event subscription information"""
    subscription_id: str
    subscriber_name: str
    event_types: List[EventType]
    handler_id: str
    created_at: datetime
    enabled: bool = True

class EventBus:
    """Event bus for asynchronous communication"""
    
    def __init__(self):
        self.handlers: Dict[EventType, List[EventHandler]] = {}
        self.subscriptions: Dict[str, EventSubscription] = {}
        self.event_store: List[Event] = []
        self.processing_queue: asyncio.Queue = asyncio.Queue()
        self.processing_tasks: List[asyncio.Task] = []
        
        # Event bus configuration
        self.max_workers = 10
        self.batch_size = 100
        self.retention_days = 30
        
        # Start event processing
        self.processing_task: Optional[asyncio.Task] = None
        asyncio.create_task(self.start_event_processing())
    
    async def start_event_processing(self):
        """Start event processing workers"""
        if self.processing_task:
            return
        
        self.processing_task = asyncio.create_task(self._event_processing_loop())
        
        # Start worker tasks
        for i in range(self.max_workers):
            task = asyncio.create_task(self._event_worker(f"worker-{i}"))
            self.processing_tasks.append(task)
        
        logger.info("Event processing started")
    
    async def stop_event_processing(self):
        """Stop event processing"""
        if self.processing_task:
            self.processing_task.cancel()
            self.processing_task = None
        
        # Cancel worker tasks
        for task in self.processing_tasks:
            task.cancel()
        self.processing_tasks.clear()
        
        logger.info("Event processing stopped")
    
    async def _event_processing_loop(self):
        """Event processing loop"""
        while True:
            try:
                # Process events in batches
                events = []
                for _ in range(self.batch_size):
                    try:
                        event = self.processing_queue.get_nowait()
                        events.append(event)
                    except asyncio.QueueEmpty:
                        break
                
                if events:
                    await self._process_event_batch(events)
                
                await asyncio.sleep(0.1)  # Small delay to prevent busy waiting
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Event processing error: {e}")
                await asyncio.sleep(1)
    
    async def _event_worker(self, worker_name: str):
        """Event worker task"""
        while True:
            try:
                event = await self.processing_queue.get()
                await self._process_single_event(event)
                self.processing_queue.task_done()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Event worker {worker_name} error: {e}")
                await asyncio.sleep(1)
    
    async def _process_event_batch(self, events: List[Event]):
        """Process a batch of events"""
        # Group events by type for efficient processing
        events_by_type = {}
        for event in events:
            if event.event_type not in events_by_type:
                events_by_type[event.event_type] = []
            events_by_type[event.event_type].append(event)
        
        # Process each event type
        for event_type, type_events in events_by_type.items():
            await self._process_events_by_type(event_type, type_events)
    
    async def _process_events_by_type(self, event_type: EventType, events: List[Event]):
        """Process events of a specific type"""
        handlers = self.handlers.get(event_type, [])
        
        for event in events:
            for handler in handlers:
                if handler.enabled:
                    try:
                        await self._execute_handler(handler, event)
                    except Exception as e:
                        logger.error(f"Handler {handler.handler_id} failed for event {event.event_id}: {e}")
    
    async def _process_single_event(self, event: Event):
        """Process a single event"""
        handlers = self.handlers.get(event.event_type, [])
        
        for handler in handlers:
            if handler.enabled:
                try:
                    await self._execute_handler(handler, event)
                except Exception as e:
                    logger.error(f"Handler {handler.handler_id} failed for event {event.event_id}: {e}")
    
    async def _execute_handler(self, handler: EventHandler, event: Event):
        """Execute a single event handler"""
        try:
            # Check if handler is async
            if inspect.iscoroutinefunction(handler.handler_function):
                await asyncio.wait_for(
                    handler.handler_function(event),
                    timeout=handler.timeout
                )
            else:
                # Run sync handler in thread pool
                await asyncio.get_event_loop().run_in_executor(
                    None, handler.handler_function, event
                )
        except asyncio.TimeoutError:
            logger.error(f"Handler {handler.handler_id} timed out for event {event.event_id}")
        except Exception as e:
            logger.error(f"Handler {handler.handler_id} failed for event {event.event_id}: {e}")
    
    def publish_event(self, event_type: EventType, data: Dict[str, Any],
                     source: str = "system", metadata: Dict[str, Any] = None,
                     correlation_id: Optional[str] = None,
                     causation_id: Optional[str] = None) -> str:
        """Publish an event to the event bus"""
        event_id = str(uuid.uuid4())
        
        event = Event(
            event_id=event_id,
            event_type=event_type,
            timestamp=datetime.utcnow(),
            source=source,
            data=data,
            metadata=metadata or {},
            correlation_id=correlation_id,
            causation_id=causation_id
        )
        
        # Store event
        self.event_store.append(event)
        
        # Add to processing queue
        asyncio.create_task(self.processing_queue.put(event))
        
        logger.debug(f"Published event: {event_id} - {event_type.value}")
        return event_id
    
    def subscribe(self, event_type: EventType, handler: Callable,
                 priority: int = 0, retry_count: int = 3,
                 retry_delay: int = 1, timeout: int = 30) -> str:
        """Subscribe to an event type"""
        handler_id = str(uuid.uuid4())
        
        event_handler = EventHandler(
            handler_id=handler_id,
            event_type=event_type,
            handler_function=handler,
            priority=priority,
            retry_count=retry_count,
            retry_delay=retry_delay,
            timeout=timeout
        )
        
        if event_type not in self.handlers:
            self.handlers[event_type] = []
        
        self.handlers[event_type].append(event_handler)
        
        # Sort handlers by priority
        self.handlers[event_type].sort(key=lambda x: x.priority, reverse=True)
        
        logger.info(f"Subscribed handler: {handler_id} - {event_type.value}")
        return handler_id
    
    def unsubscribe(self, handler_id: str) -> bool:
        """Unsubscribe a handler"""
        for event_type, handlers in self.handlers.items():
            for i, handler in enumerate(handlers):
                if handler.handler_id == handler_id:
                    del handlers[i]
                    logger.info(f"Unsubscribed handler: {handler_id}")
                    return True
        return False
    
    def create_subscription(self, subscriber_name: str, event_types: List[EventType],
                          handler: Callable, priority: int = 0) -> str:
        """Create a subscription for multiple event types"""
        subscription_id = str(uuid.uuid4())
        
        # Create handler for each event type
        handler_ids = []
        for event_type in event_types:
            handler_id = self.subscribe(event_type, handler, priority)
            handler_ids.append(handler_id)
        
        subscription = EventSubscription(
            subscription_id=subscription_id,
            subscriber_name=subscriber_name,
            event_types=event_types,
            handler_id=handler_ids[0] if handler_ids else "",
            created_at=datetime.utcnow()
        )
        
        self.subscriptions[subscription_id] = subscription
        logger.info(f"Created subscription: {subscription_id} - {subscriber_name}")
        return subscription_id
    
    def get_events(self, event_type: Optional[EventType] = None,
                  start_time: Optional[datetime] = None,
                  end_time: Optional[datetime] = None,
                  limit: int = 100) -> List[Event]:
        """Get events with optional filtering"""
        events = self.event_store
        
        if event_type:
            events = [e for e in events if e.event_type == event_type]
        
        if start_time:
            events = [e for e in events if e.timestamp >= start_time]
        
        if end_time:
            events = [e for e in events if e.timestamp <= end_time]
        
        # Sort by timestamp (newest first)
        events.sort(key=lambda x: x.timestamp, reverse=True)
        
        return events[:limit]
    
    def get_event_by_id(self, event_id: str) -> Optional[Event]:
        """Get event by ID"""
        for event in self.event_store:
            if event.event_id == event_id:
                return event
        return None
    
    def get_events_by_correlation_id(self, correlation_id: str) -> List[Event]:
        """Get events by correlation ID"""
        return [e for e in self.event_store if e.correlation_id == correlation_id]
    
    def replay_events(self, event_type: Optional[EventType] = None,
                     start_time: Optional[datetime] = None,
                     end_time: Optional[datetime] = None) -> int:
        """Replay events (reprocess them)"""
        events = self.get_events(event_type, start_time, end_time)
        
        for event in events:
            asyncio.create_task(self.processing_queue.put(event))
        
        logger.info(f"Replaying {len(events)} events")
        return len(events)
    
    def get_event_statistics(self) -> Dict[str, Any]:
        """Get event statistics"""
        total_events = len(self.event_store)
        events_by_type = {}
        events_by_source = {}
        
        for event in self.event_store:
            # Count by type
            event_type = event.event_type.value
            events_by_type[event_type] = events_by_type.get(event_type, 0) + 1
            
            # Count by source
            source = event.source
            events_by_source[source] = events_by_source.get(source, 0) + 1
        
        return {
            "total_events": total_events,
            "events_by_type": events_by_type,
            "events_by_source": events_by_source,
            "total_handlers": sum(len(handlers) for handlers in self.handlers.values()),
            "total_subscriptions": len(self.subscriptions),
            "queue_size": self.processing_queue.qsize()
        }
    
    def cleanup_old_events(self):
        """Cleanup old events based on retention policy"""
        cutoff_date = datetime.utcnow() - timedelta(days=self.retention_days)
        
        original_count = len(self.event_store)
        self.event_store = [
            event for event in self.event_store
            if event.timestamp > cutoff_date
        ]
        
        removed_count = original_count - len(self.event_store)
        if removed_count > 0:
            logger.info(f"Cleaned up {removed_count} old events")
    
    def export_events(self, output_file: str, event_type: Optional[EventType] = None,
                     start_time: Optional[datetime] = None,
                     end_time: Optional[datetime] = None) -> bool:
        """Export events to JSON file"""
        try:
            events = self.get_events(event_type, start_time, end_time)
            
            data = {
                "timestamp": datetime.utcnow().isoformat(),
                "total_events": len(events),
                "events": [
                    {
                        "event_id": event.event_id,
                        "event_type": event.event_type.value,
                        "timestamp": event.timestamp.isoformat(),
                        "source": event.source,
                        "data": event.data,
                        "metadata": event.metadata,
                        "version": event.version,
                        "correlation_id": event.correlation_id,
                        "causation_id": event.causation_id
                    }
                    for event in events
                ]
            }
            
            with open(output_file, 'w') as f:
                json.dump(data, f, indent=2, default=str)
            
            logger.info(f"Events exported: {output_file}")
            return True
            
        except Exception as e:
            logger.error(f"Error exporting events: {e}")
            return False

# Global instance
_event_bus: Optional[EventBus] = None

def get_event_bus() -> EventBus:
    """Get global event bus"""
    global _event_bus
    if _event_bus is None:
        _event_bus = EventBus()
    return _event_bus

def cleanup_event_bus():
    """Cleanup global event bus"""
    global _event_bus
    if _event_bus:
        _event_bus = None
