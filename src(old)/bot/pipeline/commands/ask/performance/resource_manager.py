"""
Resource Management System for ASK Pipeline

Provides comprehensive resource management:
- Memory usage monitoring and garbage collection optimization
- CPU usage tracking and load balancing recommendations
- Request queuing and backpressure handling
- Resource limit enforcement and graceful degradation
- Capacity planning and performance optimization
"""

import asyncio
import time
import psutil
import gc
import threading
from typing import Dict, Any, Optional, List, Callable, Awaitable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import logging
from collections import deque
import weakref

logger = logging.getLogger(__name__)

class ResourceType(Enum):
    """Types of system resources"""
    MEMORY = "memory"
    CPU = "cpu"
    NETWORK = "network"
    DISK = "disk"
    CONNECTIONS = "connections"

class ResourceStatus(Enum):
    """Resource status levels"""
    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    EXHAUSTED = "exhausted"

@dataclass
class ResourceMetrics:
    """Metrics for a specific resource type"""
    resource_type: ResourceType
    current_usage: float = 0.0
    max_usage: float = 0.0
    avg_usage: float = 0.0
    status: ResourceStatus = ResourceStatus.HEALTHY
    threshold_warning: float = 70.0
    threshold_critical: float = 90.0
    last_updated: datetime = field(default_factory=datetime.utcnow)

@dataclass
class ResourceLimits:
    """Resource limits and thresholds"""
    max_memory_mb: int = 512
    max_cpu_percent: float = 80.0
    max_connections: int = 100
    max_queue_size: int = 1000
    gc_threshold: int = 100  # Trigger GC when this many objects are created

@dataclass
class PerformanceRecommendation:
    """Performance optimization recommendation"""
    resource_type: ResourceType
    current_usage: float
    recommended_action: str
    priority: int  # 1-5, higher is more urgent
    potential_improvement: float
    reason: str

class ResourceManager:
    """Comprehensive resource management system"""
    
    def __init__(self, limits: Optional[ResourceLimits] = None):
        self.limits = limits or ResourceLimits()
        self.metrics: Dict[ResourceType, ResourceMetrics] = {}
        self.request_queue: deque = deque(maxlen=self.limits.max_queue_size)
        self.active_requests: int = 0
        self.monitoring_task: Optional[asyncio.Task] = None
        self.gc_stats = {
            'collections': 0,
            'objects_collected': 0,
            'last_collection': None
        }
        self._lock = asyncio.Lock()
        self._shutdown = False
        
        # Initialize metrics for all resource types
        for resource_type in ResourceType:
            self.metrics[resource_type] = ResourceMetrics(resource_type=resource_type)
    
    async def start_monitoring(self):
        """Start resource monitoring"""
        if self.monitoring_task is None or self.monitoring_task.done():
            self.monitoring_task = asyncio.create_task(self._monitoring_loop())
            logger.info("Resource monitoring started")
    
    async def stop_monitoring(self):
        """Stop resource monitoring"""
        self._shutdown = True
        if self.monitoring_task and not self.monitoring_task.done():
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        logger.info("Resource monitoring stopped")
    
    async def _monitoring_loop(self):
        """Background resource monitoring loop"""
        while not self._shutdown:
            try:
                await self._update_metrics()
                await self._check_thresholds()
                await self._optimize_resources()
                await asyncio.sleep(5)  # Check every 5 seconds
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Resource monitoring error: {e}")
                await asyncio.sleep(10)  # Wait before retry
    
    async def _update_metrics(self):
        """Update resource metrics"""
        process = psutil.Process()
        
        # Memory metrics
        memory_info = process.memory_info()
        memory_usage_mb = memory_info.rss / (1024 * 1024)
        memory_percent = process.memory_percent()
        
        self.metrics[ResourceType.MEMORY].current_usage = memory_usage_mb
        self.metrics[ResourceType.MEMORY].max_usage = max(
            self.metrics[ResourceType.MEMORY].max_usage,
            memory_usage_mb
        )
        self._update_avg_usage(ResourceType.MEMORY, memory_usage_mb)
        
        # CPU metrics
        cpu_percent = process.cpu_percent()
        self.metrics[ResourceType.CPU].current_usage = cpu_percent
        self.metrics[ResourceType.CPU].max_usage = max(
            self.metrics[ResourceType.CPU].max_usage,
            cpu_percent
        )
        self._update_avg_usage(ResourceType.CPU, cpu_percent)
        
        # Connection metrics
        connections = len(process.connections())
        self.metrics[ResourceType.CONNECTIONS].current_usage = connections
        self.metrics[ResourceType.CONNECTIONS].max_usage = max(
            self.metrics[ResourceType.CONNECTIONS].max_usage,
            connections
        )
        self._update_avg_usage(ResourceType.CONNECTIONS, connections)
        
        # Update timestamps
        for metrics in self.metrics.values():
            metrics.last_updated = datetime.utcnow()
    
    def _update_avg_usage(self, resource_type: ResourceType, current_usage: float):
        """Update average usage with exponential moving average"""
        metrics = self.metrics[resource_type]
        if metrics.avg_usage == 0:
            metrics.avg_usage = current_usage
        else:
            alpha = 0.1  # Smoothing factor
            metrics.avg_usage = alpha * current_usage + (1 - alpha) * metrics.avg_usage
    
    async def _check_thresholds(self):
        """Check resource thresholds and update status"""
        for resource_type, metrics in self.metrics.items():
            if resource_type == ResourceType.MEMORY:
                usage_percent = (metrics.current_usage / self.limits.max_memory_mb) * 100
            elif resource_type == ResourceType.CPU:
                usage_percent = metrics.current_usage
            elif resource_type == ResourceType.CONNECTIONS:
                usage_percent = (metrics.current_usage / self.limits.max_connections) * 100
            else:
                continue
            
            if usage_percent >= metrics.threshold_critical:
                metrics.status = ResourceStatus.CRITICAL
                await self._handle_critical_resource(resource_type, usage_percent)
            elif usage_percent >= metrics.threshold_warning:
                metrics.status = ResourceStatus.WARNING
                await self._handle_warning_resource(resource_type, usage_percent)
            else:
                metrics.status = ResourceStatus.HEALTHY
    
    async def _handle_critical_resource(self, resource_type: ResourceType, usage_percent: float):
        """Handle critical resource usage"""
        logger.warning(f"Critical {resource_type.value} usage: {usage_percent:.1f}%")
        
        if resource_type == ResourceType.MEMORY:
            await self._force_garbage_collection()
            await self._clear_caches()
        elif resource_type == ResourceType.CPU:
            await self._throttle_requests()
        elif resource_type == ResourceType.CONNECTIONS:
            await self._close_idle_connections()
    
    async def _handle_warning_resource(self, resource_type: ResourceType, usage_percent: float):
        """Handle warning resource usage"""
        logger.info(f"Warning {resource_type.value} usage: {usage_percent:.1f}%")
        
        if resource_type == ResourceType.MEMORY:
            await self._suggest_garbage_collection()
        elif resource_type == ResourceType.CPU:
            await self._monitor_cpu_intensive_operations()
    
    async def _force_garbage_collection(self):
        """Force garbage collection"""
        before = len(gc.get_objects())
        collected = gc.collect()
        after = len(gc.get_objects())
        
        self.gc_stats['collections'] += 1
        self.gc_stats['objects_collected'] += collected
        self.gc_stats['last_collection'] = datetime.utcnow()
        
        logger.info(f"Forced GC: collected {collected} objects ({before - after} remaining)")
    
    async def _suggest_garbage_collection(self):
        """Suggest garbage collection if needed"""
        object_count = len(gc.get_objects())
        if object_count > self.limits.gc_threshold:
            await self._force_garbage_collection()
    
    async def _clear_caches(self):
        """Clear caches to free memory"""
        # This would clear application caches
        logger.info("Clearing caches to free memory")
        # Implementation would depend on specific cache implementations
    
    async def _throttle_requests(self):
        """Throttle requests when CPU usage is high"""
        if self.active_requests > 10:  # Arbitrary threshold
            logger.info("Throttling requests due to high CPU usage")
            await asyncio.sleep(0.1)  # Small delay
    
    async def _close_idle_connections(self):
        """Close idle connections"""
        logger.info("Closing idle connections")
        # Implementation would depend on specific connection management
    
    async def _monitor_cpu_intensive_operations(self):
        """Monitor CPU-intensive operations"""
        # This could track and optimize CPU-intensive operations
        pass
    
    async def _optimize_resources(self):
        """Optimize resource usage"""
        # Memory optimization
        if self.metrics[ResourceType.MEMORY].status == ResourceStatus.WARNING:
            await self._optimize_memory()
        
        # CPU optimization
        if self.metrics[ResourceType.CPU].status == ResourceStatus.WARNING:
            await self._optimize_cpu()
    
    async def _optimize_memory(self):
        """Optimize memory usage"""
        # Suggest garbage collection
        await self._suggest_garbage_collection()
        
        # Clear old cache entries
        # This would be implemented based on specific cache systems
    
    async def _optimize_cpu(self):
        """Optimize CPU usage"""
        # Adjust concurrency limits
        # This would be implemented based on specific CPU-intensive operations
    
    async def queue_request(self, request: Callable) -> bool:
        """Queue a request with backpressure handling"""
        async with self._lock:
            if len(self.request_queue) >= self.limits.max_queue_size:
                logger.warning("Request queue full, dropping request")
                return False
            
            self.request_queue.append(request)
            return True
    
    async def process_requests(self, max_concurrent: int = 10):
        """Process queued requests with concurrency control"""
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def process_with_semaphore(request):
            async with semaphore:
                self.active_requests += 1
                try:
                    if asyncio.iscoroutinefunction(request):
                        return await request()
                    else:
                        return request()
                finally:
                    self.active_requests -= 1
        
        while self.request_queue:
            request = self.request_queue.popleft()
            asyncio.create_task(process_with_semaphore(request))
    
    def get_resource_status(self) -> Dict[str, Any]:
        """Get current resource status"""
        status = {}
        
        for resource_type, metrics in self.metrics.items():
            status[resource_type.value] = {
                'current_usage': metrics.current_usage,
                'max_usage': metrics.max_usage,
                'avg_usage': metrics.avg_usage,
                'status': metrics.status.value,
                'threshold_warning': metrics.threshold_warning,
                'threshold_critical': metrics.threshold_critical,
                'last_updated': metrics.last_updated.isoformat()
            }
        
        status['active_requests'] = self.active_requests
        status['queued_requests'] = len(self.request_queue)
        status['gc_stats'] = self.gc_stats
        
        return status
    
    def get_performance_recommendations(self) -> List[PerformanceRecommendation]:
        """Get performance optimization recommendations"""
        recommendations = []
        
        # Memory recommendations
        memory_metrics = self.metrics[ResourceType.MEMORY]
        if memory_metrics.status == ResourceStatus.CRITICAL:
            recommendations.append(PerformanceRecommendation(
                resource_type=ResourceType.MEMORY,
                current_usage=memory_metrics.current_usage,
                recommended_action="Increase memory limit or optimize memory usage",
                priority=5,
                potential_improvement=0.3,
                reason=f"Memory usage is critical at {memory_metrics.current_usage:.1f}MB"
            ))
        elif memory_metrics.status == ResourceStatus.WARNING:
            recommendations.append(PerformanceRecommendation(
                resource_type=ResourceType.MEMORY,
                current_usage=memory_metrics.current_usage,
                recommended_action="Monitor memory usage and consider optimization",
                priority=3,
                potential_improvement=0.1,
                reason=f"Memory usage is high at {memory_metrics.current_usage:.1f}MB"
            ))
        
        # CPU recommendations
        cpu_metrics = self.metrics[ResourceType.CPU]
        if cpu_metrics.status == ResourceStatus.CRITICAL:
            recommendations.append(PerformanceRecommendation(
                resource_type=ResourceType.CPU,
                current_usage=cpu_metrics.current_usage,
                recommended_action="Reduce CPU-intensive operations or increase CPU capacity",
                priority=5,
                potential_improvement=0.4,
                reason=f"CPU usage is critical at {cpu_metrics.current_usage:.1f}%"
            ))
        
        # Connection recommendations
        connection_metrics = self.metrics[ResourceType.CONNECTIONS]
        if connection_metrics.current_usage > self.limits.max_connections * 0.8:
            recommendations.append(PerformanceRecommendation(
                resource_type=ResourceType.CONNECTIONS,
                current_usage=connection_metrics.current_usage,
                recommended_action="Implement connection pooling or increase connection limit",
                priority=4,
                potential_improvement=0.2,
                reason=f"Connection usage is high at {connection_metrics.current_usage}"
            ))
        
        return recommendations
    
    async def cleanup(self):
        """Cleanup resource manager"""
        await self.stop_monitoring()
        
        # Clear request queue
        self.request_queue.clear()
        self.active_requests = 0
        
        logger.info("Resource manager cleaned up")

# Global resource manager instance
_resource_manager: Optional[ResourceManager] = None

async def get_resource_manager() -> ResourceManager:
    """Get global resource manager"""
    global _resource_manager
    if _resource_manager is None:
        _resource_manager = ResourceManager()
        await _resource_manager.start_monitoring()
    return _resource_manager

async def cleanup_resource_manager():
    """Cleanup global resource manager"""
    global _resource_manager
    if _resource_manager:
        await _resource_manager.cleanup()
        _resource_manager = None
