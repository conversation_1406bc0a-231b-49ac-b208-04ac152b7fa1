"""
Connection Pool Management for ASK Pipeline

Provides optimized connection pooling for:
- HTTP clients for external API calls
- Redis connections for caching and rate limiting
- Database connections for data persistence
- MCP client connections for tool execution
"""

import asyncio
import aiohttp
import redis.asyncio as redis
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import time
import logging
from contextlib import asynccontextmanager

logger = logging.getLogger(__name__)

@dataclass
class PoolConfig:
    """Configuration for connection pools"""
    max_connections: int = 100
    max_keepalive_connections: int = 20
    keepalive_timeout: int = 30
    connection_timeout: int = 10
    read_timeout: int = 30
    retry_attempts: int = 3
    retry_delay: float = 1.0
    health_check_interval: int = 60
    max_retries: int = 3

@dataclass
class ConnectionStats:
    """Statistics for connection pool usage"""
    total_connections: int = 0
    active_connections: int = 0
    idle_connections: int = 0
    failed_connections: int = 0
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    avg_response_time: float = 0.0
    last_health_check: Optional[datetime] = None

class HTTPConnectionPool:
    """Optimized HTTP connection pool for external API calls"""
    
    def __init__(self, config: PoolConfig):
        self.config = config
        self.session: Optional[aiohttp.ClientSession] = None
        self.stats = ConnectionStats()
        self._lock = asyncio.Lock()
        self._last_cleanup = time.time()
        
    async def get_session(self) -> aiohttp.ClientSession:
        """Get or create HTTP session with connection pooling"""
        if self.session is None or self.session.closed:
            await self._create_session()
        return self.session
    
    async def _create_session(self):
        """Create new HTTP session with optimized settings"""
        connector = aiohttp.TCPConnector(
            limit=self.config.max_connections,
            limit_per_host=self.config.max_keepalive_connections,
            keepalive_timeout=self.config.keepalive_timeout,
            enable_cleanup_closed=True,
            force_close=False,
            use_dns_cache=True,
            ttl_dns_cache=300,
            family=0,  # Auto-detect IPv4/IPv6
            ssl=False
        )
        
        timeout = aiohttp.ClientTimeout(
            total=self.config.read_timeout,
            connect=self.config.connection_timeout,
            sock_read=self.config.read_timeout
        )
        
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers={
                'User-Agent': 'TradingBot/1.0',
                'Accept': 'application/json',
                'Connection': 'keep-alive'
            }
        )
        
        self.stats.total_connections += 1
        logger.info("HTTP connection pool created", extra={
            'max_connections': self.config.max_connections,
            'keepalive_timeout': self.config.keepalive_timeout
        })
    
    @asynccontextmanager
    async def request(self, method: str, url: str, **kwargs):
        """Context manager for HTTP requests with automatic cleanup"""
        session = await self.get_session()
        start_time = time.time()
        
        try:
            self.stats.total_requests += 1
            async with session.request(method, url, **kwargs) as response:
                self.stats.successful_requests += 1
                response_time = time.time() - start_time
                self._update_avg_response_time(response_time)
                yield response
        except Exception as e:
            self.stats.failed_requests += 1
            logger.error(f"HTTP request failed: {e}", extra={'url': url, 'method': method})
            raise
        finally:
            await self._cleanup_if_needed()
    
    def _update_avg_response_time(self, response_time: float):
        """Update average response time"""
        if self.stats.successful_requests == 1:
            self.stats.avg_response_time = response_time
        else:
            # Exponential moving average
            alpha = 0.1
            self.stats.avg_response_time = (
                alpha * response_time + 
                (1 - alpha) * self.stats.avg_response_time
            )
    
    async def _cleanup_if_needed(self):
        """Clean up connections if needed"""
        now = time.time()
        if now - self._last_cleanup > 300:  # Every 5 minutes
            await self.cleanup()
            self._last_cleanup = now
    
    async def cleanup(self):
        """Clean up idle connections"""
        if self.session and not self.session.closed:
            await self.session.close()
            self.session = None
            logger.debug("HTTP connection pool cleaned up")
    
    async def close(self):
        """Close the connection pool"""
        if self.session and not self.session.closed:
            await self.session.close()
            self.session = None
            logger.info("HTTP connection pool closed")

class RedisConnectionPool:
    """Optimized Redis connection pool for caching and rate limiting"""
    
    def __init__(self, config: PoolConfig, redis_url: str = "redis://localhost:6379/0"):
        self.config = config
        self.redis_url = redis_url
        self.pool: Optional[redis.ConnectionPool] = None
        self.client: Optional[redis.Redis] = None
        self.stats = ConnectionStats()
        self._lock = asyncio.Lock()
        
    async def get_client(self) -> redis.Redis:
        """Get or create Redis client with connection pooling"""
        if self.client is None:
            await self._create_pool()
        return self.client
    
    async def _create_pool(self):
        """Create Redis connection pool"""
        self.pool = redis.ConnectionPool.from_url(
            self.redis_url,
            max_connections=self.config.max_connections,
            retry_on_timeout=True,
            socket_connect_timeout=self.config.connection_timeout,
            socket_timeout=self.config.read_timeout,
            health_check_interval=self.config.health_check_interval
        )
        
        self.client = redis.Redis(
            connection_pool=self.pool,
            decode_responses=True,
            retry_on_timeout=True
        )
        
        # Test connection
        try:
            await self.client.ping()
            self.stats.total_connections += 1
            logger.info("Redis connection pool created", extra={
                'max_connections': self.config.max_connections,
                'redis_url': self.redis_url
            })
        except Exception as e:
            logger.error(f"Redis connection failed: {e}")
            self.stats.failed_connections += 1
            raise
    
    async def execute_command(self, command: str, *args, **kwargs):
        """Execute Redis command with error handling"""
        client = await self.get_client()
        start_time = time.time()
        
        try:
            self.stats.total_requests += 1
            result = await client.execute_command(command, *args, **kwargs)
            self.stats.successful_requests += 1
            response_time = time.time() - start_time
            self._update_avg_response_time(response_time)
            return result
        except Exception as e:
            self.stats.failed_requests += 1
            logger.error(f"Redis command failed: {e}", extra={'command': command})
            raise
    
    def _update_avg_response_time(self, response_time: float):
        """Update average response time"""
        if self.stats.successful_requests == 1:
            self.stats.avg_response_time = response_time
        else:
            alpha = 0.1
            self.stats.avg_response_time = (
                alpha * response_time + 
                (1 - alpha) * self.stats.avg_response_time
            )
    
    async def close(self):
        """Close Redis connection pool"""
        if self.client:
            await self.client.close()
            self.client = None
        if self.pool:
            await self.pool.disconnect()
            self.pool = None
        logger.info("Redis connection pool closed")

class MCPConnectionPool:
    """Connection pool for MCP clients"""
    
    def __init__(self, config: PoolConfig):
        self.config = config
        self.clients: Dict[str, Any] = {}
        self.stats = ConnectionStats()
        self._lock = asyncio.Lock()
        
    async def get_client(self, client_type: str) -> Any:
        """Get or create MCP client"""
        async with self._lock:
            if client_type not in self.clients:
                await self._create_client(client_type)
            return self.clients[client_type]
    
    async def _create_client(self, client_type: str):
        """Create MCP client based on type"""
        # This would be implemented based on actual MCP client creation
        # For now, we'll create a placeholder
        self.clients[client_type] = f"mcp_client_{client_type}"
        self.stats.total_connections += 1
        logger.info(f"MCP client created: {client_type}")
    
    async def close(self):
        """Close all MCP clients"""
        for client_type, client in self.clients.items():
            # Close client if it has a close method
            if hasattr(client, 'close'):
                await client.close()
        self.clients.clear()
        logger.info("MCP connection pool closed")

class ConnectionPoolManager:
    """Centralized connection pool management"""
    
    def __init__(self, config: Optional[PoolConfig] = None):
        self.config = config or PoolConfig()
        self.http_pool = HTTPConnectionPool(self.config)
        self.redis_pool = RedisConnectionPool(self.config)
        self.mcp_pool = MCPConnectionPool(self.config)
        self._cleanup_task: Optional[asyncio.Task] = None
        
    async def start(self):
        """Start connection pools and cleanup tasks"""
        # Initialize pools
        await self.http_pool.get_session()
        await self.redis_pool.get_client()
        
        # Start cleanup task
        self._cleanup_task = asyncio.create_task(self._cleanup_loop())
        logger.info("Connection pool manager started")
    
    async def stop(self):
        """Stop all connection pools"""
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        await self.http_pool.close()
        await self.redis_pool.close()
        await self.mcp_pool.close()
        logger.info("Connection pool manager stopped")
    
    async def _cleanup_loop(self):
        """Background cleanup loop"""
        while True:
            try:
                await asyncio.sleep(300)  # Every 5 minutes
                await self.http_pool.cleanup()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Connection pool cleanup error: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get statistics for all connection pools"""
        return {
            'http': {
                'total_connections': self.http_pool.stats.total_connections,
                'active_connections': self.http_pool.stats.active_connections,
                'total_requests': self.http_pool.stats.total_requests,
                'successful_requests': self.http_pool.stats.successful_requests,
                'failed_requests': self.http_pool.stats.failed_requests,
                'avg_response_time': self.http_pool.stats.avg_response_time
            },
            'redis': {
                'total_connections': self.redis_pool.stats.total_connections,
                'total_requests': self.redis_pool.stats.total_requests,
                'successful_requests': self.redis_pool.stats.successful_requests,
                'failed_requests': self.redis_pool.stats.failed_requests,
                'avg_response_time': self.redis_pool.stats.avg_response_time
            },
            'mcp': {
                'total_connections': self.mcp_pool.stats.total_connections,
                'active_clients': len(self.mcp_pool.clients)
            }
        }

# Global connection pool manager instance
_pool_manager: Optional[ConnectionPoolManager] = None

async def get_connection_pool_manager() -> ConnectionPoolManager:
    """Get global connection pool manager"""
    global _pool_manager
    if _pool_manager is None:
        _pool_manager = ConnectionPoolManager()
        await _pool_manager.start()
    return _pool_manager

async def cleanup_connection_pools():
    """Cleanup all connection pools"""
    global _pool_manager
    if _pool_manager:
        await _pool_manager.stop()
        _pool_manager = None
