"""
Smart Caching System for ASK Pipeline

Provides intelligent caching with:
- Cache warming based on usage patterns
- Preloading for common queries and popular stocks
- Cache compression to reduce memory usage
- Analytics and optimization recommendations
- Performance monitoring and alerting
"""

import asyncio
import time
import json
import gzip
import pickle
from typing import Dict, Any, Optional, List, Union, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import logging
from collections import defaultdict, deque
import hashlib
import statistics

logger = logging.getLogger(__name__)

class CacheStrategy(Enum):
    """Cache warming strategies"""
    POPULAR_QUERIES = "popular_queries"
    FREQUENT_STOCKS = "frequent_stocks"
    TIME_BASED = "time_based"
    USER_PATTERNS = "user_patterns"
    PREDICTIVE = "predictive"

@dataclass
class CacheEntry:
    """Individual cache entry with metadata"""
    key: str
    value: Any
    created_at: datetime
    last_accessed: datetime
    access_count: int = 0
    ttl: float = 300.0
    compressed: bool = False
    size_bytes: int = 0
    hit_value: float = 1.0  # Value for cache hit optimization

@dataclass
class CacheAnalytics:
    """Cache performance analytics"""
    total_requests: int = 0
    cache_hits: int = 0
    cache_misses: int = 0
    hit_rate: float = 0.0
    avg_response_time: float = 0.0
    memory_usage: int = 0
    compression_ratio: float = 0.0
    eviction_count: int = 0
    warming_operations: int = 0

@dataclass
class WarmingPattern:
    """Pattern for cache warming"""
    pattern_type: CacheStrategy
    query_pattern: str
    frequency: int
    last_seen: datetime
    confidence: float
    predicted_next: Optional[datetime] = None

class SmartCacheManager:
    """Intelligent cache manager with warming and optimization"""
    
    def __init__(self, max_size: int = 1000, max_memory_mb: int = 100):
        self.max_size = max_size
        self.max_memory_bytes = max_memory_mb * 1024 * 1024
        self.cache: Dict[str, CacheEntry] = {}
        self.access_log: deque = deque(maxlen=10000)
        self.warming_patterns: List[WarmingPattern] = []
        self.analytics = CacheAnalytics()
        self._lock = asyncio.Lock()
        self._warming_task: Optional[asyncio.Task] = None
        self._compression_threshold = 1024  # Compress entries > 1KB
        
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache with analytics"""
        start_time = time.time()
        
        async with self._lock:
            self.analytics.total_requests += 1
            
            if key in self.cache:
                entry = self.cache[key]
                
                # Check TTL
                if time.time() - entry.created_at.timestamp() > entry.ttl:
                    del self.cache[key]
                    self.analytics.cache_misses += 1
                    return None
                
                # Update access info
                entry.last_accessed = datetime.utcnow()
                entry.access_count += 1
                self.analytics.cache_hits += 1
                
                # Log access
                self.access_log.append({
                    'key': key,
                    'timestamp': datetime.utcnow(),
                    'hit': True
                })
                
                # Decompress if needed
                value = entry.value
                if entry.compressed:
                    value = self._decompress(value)
                
                # Update analytics
                response_time = time.time() - start_time
                self._update_avg_response_time(response_time)
                self._update_hit_rate()
                
                return value
            else:
                self.analytics.cache_misses += 1
                
                # Log miss
                self.access_log.append({
                    'key': key,
                    'timestamp': datetime.utcnow(),
                    'hit': False
                })
                
                return None
    
    async def set(
        self, 
        key: str, 
        value: Any, 
        ttl: float = 300.0,
        compress: bool = None
    ) -> bool:
        """Set value in cache with intelligent compression"""
        async with self._lock:
            # Determine if compression is needed
            if compress is None:
                compress = self._should_compress(value)
            
            # Compress if needed
            if compress:
                value = self._compress(value)
            
            # Calculate size
            size_bytes = self._calculate_size(value)
            
            # Create cache entry
            entry = CacheEntry(
                key=key,
                value=value,
                created_at=datetime.utcnow(),
                last_accessed=datetime.utcnow(),
                ttl=ttl,
                compressed=compress,
                size_bytes=size_bytes
            )
            
            # Check memory limits
            if size_bytes > self.max_memory_bytes:
                logger.warning(f"Cache entry too large: {size_bytes} bytes")
                return False
            
            # Evict if needed
            await self._evict_if_needed(size_bytes)
            
            # Store entry
            self.cache[key] = entry
            self.analytics.memory_usage += size_bytes
            
            # Update compression ratio
            self._update_compression_ratio()
            
            return True
    
    def _should_compress(self, value: Any) -> bool:
        """Determine if value should be compressed"""
        try:
            serialized = json.dumps(value)
            return len(serialized) > self._compression_threshold
        except (TypeError, ValueError):
            # Fallback to pickle for non-JSON serializable objects
            try:
                serialized = pickle.dumps(value)
                return len(serialized) > self._compression_threshold
            except:
                return False
    
    def _compress(self, value: Any) -> bytes:
        """Compress value using gzip"""
        try:
            if isinstance(value, str):
                data = value.encode('utf-8')
            else:
                data = json.dumps(value).encode('utf-8')
            
            return gzip.compress(data)
        except Exception as e:
            logger.error(f"Compression failed: {e}")
            return value
    
    def _decompress(self, value: bytes) -> Any:
        """Decompress value using gzip"""
        try:
            decompressed = gzip.decompress(value)
            return decompressed.decode('utf-8')
        except Exception as e:
            logger.error(f"Decompression failed: {e}")
            return value
    
    def _calculate_size(self, value: Any) -> int:
        """Calculate size of value in bytes"""
        try:
            if isinstance(value, bytes):
                return len(value)
            elif isinstance(value, str):
                return len(value.encode('utf-8'))
            else:
                return len(json.dumps(value).encode('utf-8'))
        except:
            return 1024  # Default estimate
    
    async def _evict_if_needed(self, new_size: int):
        """Evict entries if memory limit would be exceeded"""
        current_memory = sum(entry.size_bytes for entry in self.cache.values())
        
        if current_memory + new_size > self.max_memory_bytes:
            # Sort by hit value (access_count / age)
            now = time.time()
            entries_with_value = []
            
            for key, entry in self.cache.items():
                age = now - entry.created_at.timestamp()
                hit_value = entry.access_count / max(age, 1)
                entries_with_value.append((key, hit_value))
            
            # Sort by hit value (ascending - evict least valuable first)
            entries_with_value.sort(key=lambda x: x[1])
            
            # Evict until we have enough space
            for key, _ in entries_with_value:
                if key in self.cache:
                    entry = self.cache[key]
                    del self.cache[key]
                    self.analytics.memory_usage -= entry.size_bytes
                    self.analytics.eviction_count += 1
                    
                    if self.analytics.memory_usage + new_size <= self.max_memory_bytes:
                        break
    
    def _update_avg_response_time(self, response_time: float):
        """Update average response time"""
        if self.analytics.total_requests == 1:
            self.analytics.avg_response_time = response_time
        else:
            alpha = 0.1
            self.analytics.avg_response_time = (
                alpha * response_time + 
                (1 - alpha) * self.analytics.avg_response_time
            )
    
    def _update_hit_rate(self):
        """Update cache hit rate"""
        if self.analytics.total_requests > 0:
            self.analytics.hit_rate = (
                self.analytics.cache_hits / self.analytics.total_requests
            )
    
    def _update_compression_ratio(self):
        """Update compression ratio"""
        compressed_entries = sum(1 for entry in self.cache.values() if entry.compressed)
        total_entries = len(self.cache)
        
        if total_entries > 0:
            self.analytics.compression_ratio = compressed_entries / total_entries
    
    async def start_warming(self):
        """Start cache warming process"""
        if self._warming_task is None or self._warming_task.done():
            self._warming_task = asyncio.create_task(self._warming_loop())
            logger.info("Cache warming started")
    
    async def stop_warming(self):
        """Stop cache warming process"""
        if self._warming_task and not self._warming_task.done():
            self._warming_task.cancel()
            try:
                await self._warming_task
            except asyncio.CancelledError:
                pass
            logger.info("Cache warming stopped")
    
    async def _warming_loop(self):
        """Background cache warming loop"""
        while True:
            try:
                await asyncio.sleep(60)  # Check every minute
                await self._analyze_patterns()
                await self._execute_warming()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Cache warming error: {e}")
                await asyncio.sleep(30)  # Wait before retry
    
    async def _analyze_patterns(self):
        """Analyze access patterns for warming opportunities"""
        if len(self.access_log) < 10:
            return
        
        # Analyze recent access patterns
        recent_logs = list(self.access_log)[-1000:]  # Last 1000 accesses
        
        # Find popular queries
        query_counts = defaultdict(int)
        for log in recent_logs:
            if log['hit']:  # Only count hits
                query_counts[log['key']] += 1
        
        # Update warming patterns
        for query, count in query_counts.items():
            if count >= 3:  # At least 3 hits
                # Check if pattern already exists
                existing = next(
                    (p for p in self.warming_patterns if p.query_pattern == query),
                    None
                )
                
                if existing:
                    existing.frequency = count
                    existing.last_seen = datetime.utcnow()
                else:
                    self.warming_patterns.append(WarmingPattern(
                        pattern_type=CacheStrategy.POPULAR_QUERIES,
                        query_pattern=query,
                        frequency=count,
                        last_seen=datetime.utcnow(),
                        confidence=min(0.9, count / 10)
                    ))
    
    async def _execute_warming(self):
        """Execute cache warming based on patterns"""
        for pattern in self.warming_patterns:
            if pattern.confidence > 0.5:  # Only warm high-confidence patterns
                await self._warm_pattern(pattern)
    
    async def _warm_pattern(self, pattern: WarmingPattern):
        """Warm cache for a specific pattern"""
        try:
            # This would be implemented based on the specific pattern type
            # For now, we'll just log the warming attempt
            logger.debug(f"Warming cache for pattern: {pattern.query_pattern}")
            self.analytics.warming_operations += 1
        except Exception as e:
            logger.error(f"Cache warming failed for pattern {pattern.query_pattern}: {e}")
    
    def get_analytics(self) -> Dict[str, Any]:
        """Get cache analytics"""
        return {
            'total_requests': self.analytics.total_requests,
            'cache_hits': self.analytics.cache_hits,
            'cache_misses': self.analytics.cache_misses,
            'hit_rate': self.analytics.hit_rate,
            'avg_response_time': self.analytics.avg_response_time,
            'memory_usage_mb': self.analytics.memory_usage / (1024 * 1024),
            'compression_ratio': self.analytics.compression_ratio,
            'eviction_count': self.analytics.eviction_count,
            'warming_operations': self.analytics.warming_operations,
            'active_patterns': len(self.warming_patterns),
            'cache_size': len(self.cache)
        }
    
    def get_optimization_recommendations(self) -> List[str]:
        """Get cache optimization recommendations"""
        recommendations = []
        
        if self.analytics.hit_rate < 0.7:
            recommendations.append(
                f"Low hit rate ({self.analytics.hit_rate:.2%}). "
                "Consider increasing cache size or TTL."
            )
        
        if self.analytics.compression_ratio < 0.3:
            recommendations.append(
                "Low compression ratio. Consider enabling compression "
                "for larger entries."
            )
        
        if self.analytics.eviction_count > self.analytics.total_requests * 0.1:
            recommendations.append(
                "High eviction rate. Consider increasing memory limit "
                "or optimizing cache keys."
            )
        
        if self.analytics.avg_response_time > 0.01:
            recommendations.append(
                f"High average response time ({self.analytics.avg_response_time:.3f}s). "
                "Consider optimizing cache operations."
            )
        
        return recommendations
    
    async def cleanup(self):
        """Cleanup cache and stop warming"""
        await self.stop_warming()
        
        async with self._lock:
            self.cache.clear()
            self.access_log.clear()
            self.warming_patterns.clear()
            self.analytics = CacheAnalytics()
        
        logger.info("Smart cache cleaned up")

# Global smart cache instance
_smart_cache: Optional[SmartCacheManager] = None

async def get_smart_cache() -> SmartCacheManager:
    """Get global smart cache manager"""
    global _smart_cache
    if _smart_cache is None:
        _smart_cache = SmartCacheManager()
        await _smart_cache.start_warming()
    return _smart_cache

async def cleanup_smart_cache():
    """Cleanup global smart cache"""
    global _smart_cache
    if _smart_cache:
        await _smart_cache.cleanup()
        _smart_cache = None
