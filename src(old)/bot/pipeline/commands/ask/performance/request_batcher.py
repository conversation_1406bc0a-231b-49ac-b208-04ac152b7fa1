"""
Request Batching System for ASK Pipeline

Provides intelligent request batching for:
- Multiple tool executions
- External API calls
- Cache operations
- Database queries
"""

import asyncio
import time
from typing import Dict, Any, Optional, List, Callable, Awaitable, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import logging
from collections import defaultdict, deque

logger = logging.getLogger(__name__)

class BatchType(Enum):
    """Types of request batches"""
    TOOL_EXECUTION = "tool_execution"
    API_CALL = "api_call"
    CACHE_OPERATION = "cache_operation"
    DATABASE_QUERY = "database_query"

@dataclass
class BatchRequest:
    """Individual request within a batch"""
    request_id: str
    batch_type: BatchType
    operation: str
    parameters: Dict[str, Any]
    priority: int = 0
    timeout: float = 30.0
    created_at: datetime = field(default_factory=datetime.utcnow)
    correlation_id: Optional[str] = None

@dataclass
class BatchResult:
    """Result of a batch operation"""
    request_id: str
    success: bool
    result: Any = None
    error: Optional[str] = None
    execution_time: float = 0.0

@dataclass
class BatchConfig:
    """Configuration for request batching"""
    max_batch_size: int = 10
    max_wait_time: float = 0.1  # 100ms
    max_priority_delay: float = 0.05  # 50ms for high priority
    timeout: float = 30.0
    retry_attempts: int = 2
    retry_delay: float = 0.5

class RequestBatcher:
    """Intelligent request batching system"""
    
    def __init__(self, config: Optional[BatchConfig] = None):
        self.config = config or BatchConfig()
        self.pending_requests: Dict[BatchType, deque] = defaultdict(deque)
        self.batch_processors: Dict[BatchType, Callable] = {}
        self.running_tasks: Dict[BatchType, asyncio.Task] = {}
        self.stats = {
            'total_batches': 0,
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'avg_batch_size': 0.0,
            'avg_processing_time': 0.0
        }
        self._lock = asyncio.Lock()
        self._shutdown = False
        
    def register_processor(self, batch_type: BatchType, processor: Callable):
        """Register a batch processor for a specific type"""
        self.batch_processors[batch_type] = processor
        logger.info(f"Registered batch processor for {batch_type.value}")
    
    async def submit_request(
        self,
        batch_type: BatchType,
        operation: str,
        parameters: Dict[str, Any],
        priority: int = 0,
        timeout: float = 30.0,
        correlation_id: Optional[str] = None
    ) -> Any:
        """Submit a request for batching"""
        request_id = f"{batch_type.value}_{operation}_{int(time.time() * 1000)}"
        
        request = BatchRequest(
            request_id=request_id,
            batch_type=batch_type,
            operation=operation,
            parameters=parameters,
            priority=priority,
            timeout=timeout,
            correlation_id=correlation_id
        )
        
        async with self._lock:
            self.pending_requests[batch_type].append(request)
            self.stats['total_requests'] += 1
            
            # Start batch processing if not already running
            if batch_type not in self.running_tasks or self.running_tasks[batch_type].done():
                self.running_tasks[batch_type] = asyncio.create_task(
                    self._process_batches(batch_type)
                )
        
        # Wait for result
        return await self._wait_for_result(request_id, batch_type, timeout)
    
    async def _wait_for_result(self, request_id: str, batch_type: BatchType, timeout: float) -> Any:
        """Wait for a specific request result"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            # Check if result is available
            result = await self._get_result(request_id, batch_type)
            if result is not None:
                return result
            
            await asyncio.sleep(0.01)  # 10ms polling
        
        raise asyncio.TimeoutError(f"Request {request_id} timed out after {timeout}s")
    
    async def _get_result(self, request_id: str, batch_type: BatchType) -> Optional[Any]:
        """Get result for a specific request (placeholder implementation)"""
        # This would be implemented with a result store
        # For now, return None to indicate no result yet
        return None
    
    async def _process_batches(self, batch_type: BatchType):
        """Process batches for a specific type"""
        while not self._shutdown:
            try:
                batch = await self._collect_batch(batch_type)
                if batch:
                    await self._execute_batch(batch_type, batch)
                else:
                    await asyncio.sleep(0.01)  # Short sleep if no requests
            except Exception as e:
                logger.error(f"Batch processing error for {batch_type.value}: {e}")
                await asyncio.sleep(0.1)
    
    async def _collect_batch(self, batch_type: BatchType) -> List[BatchRequest]:
        """Collect requests into a batch"""
        batch = []
        start_time = time.time()
        
        async with self._lock:
            while (len(batch) < self.config.max_batch_size and 
                   self.pending_requests[batch_type] and
                   time.time() - start_time < self.config.max_wait_time):
                
                request = self.pending_requests[batch_type].popleft()
                batch.append(request)
                
                # Check for high priority requests
                if (request.priority > 0 and 
                    time.time() - start_time < self.config.max_priority_delay):
                    continue
                elif request.priority > 0:
                    break
        
        return batch
    
    async def _execute_batch(self, batch_type: BatchType, batch: List[BatchRequest]):
        """Execute a batch of requests"""
        if not batch:
            return
        
        start_time = time.time()
        processor = self.batch_processors.get(batch_type)
        
        if not processor:
            logger.error(f"No processor registered for {batch_type.value}")
            return
        
        try:
            # Execute batch
            results = await processor(batch)
            
            # Process results
            for i, result in enumerate(results):
                if i < len(batch):
                    request = batch[i]
                    await self._store_result(request.request_id, result)
            
            # Update statistics
            execution_time = time.time() - start_time
            self._update_stats(len(batch), execution_time, len(results))
            
            logger.debug(f"Executed batch of {len(batch)} {batch_type.value} requests", extra={
                'batch_size': len(batch),
                'execution_time': execution_time
            })
            
        except Exception as e:
            logger.error(f"Batch execution failed for {batch_type.value}: {e}")
            # Store error results
            for request in batch:
                error_result = BatchResult(
                    request_id=request.request_id,
                    success=False,
                    error=str(e),
                    execution_time=time.time() - start_time
                )
                await self._store_result(request.request_id, error_result)
    
    async def _store_result(self, request_id: str, result: Any):
        """Store result for a request (placeholder implementation)"""
        # This would be implemented with a result store
        pass
    
    def _update_stats(self, batch_size: int, execution_time: float, success_count: int):
        """Update batch processing statistics"""
        self.stats['total_batches'] += 1
        self.stats['successful_requests'] += success_count
        self.stats['failed_requests'] += batch_size - success_count
        
        # Update average batch size
        if self.stats['total_batches'] == 1:
            self.stats['avg_batch_size'] = batch_size
        else:
            alpha = 0.1
            self.stats['avg_batch_size'] = (
                alpha * batch_size + 
                (1 - alpha) * self.stats['avg_batch_size']
            )
        
        # Update average processing time
        if self.stats['total_batches'] == 1:
            self.stats['avg_processing_time'] = execution_time
        else:
            alpha = 0.1
            self.stats['avg_processing_time'] = (
                alpha * execution_time + 
                (1 - alpha) * self.stats['avg_processing_time']
            )
    
    async def shutdown(self):
        """Shutdown the batcher"""
        self._shutdown = True
        
        # Cancel running tasks
        for task in self.running_tasks.values():
            if not task.done():
                task.cancel()
        
        # Wait for tasks to complete
        if self.running_tasks:
            await asyncio.gather(*self.running_tasks.values(), return_exceptions=True)
        
        logger.info("Request batcher shutdown complete")

class ToolExecutionBatcher:
    """Specialized batcher for tool execution requests"""
    
    def __init__(self, mcp_manager, config: Optional[BatchConfig] = None):
        self.mcp_manager = mcp_manager
        self.batcher = RequestBatcher(config)
        self.batcher.register_processor(BatchType.TOOL_EXECUTION, self._process_tool_batch)
    
    async def execute_tool(
        self,
        tool_name: str,
        parameters: Dict[str, Any],
        correlation_id: Optional[str] = None
    ) -> Any:
        """Execute a tool with batching"""
        return await self.batcher.submit_request(
            batch_type=BatchType.TOOL_EXECUTION,
            operation=tool_name,
            parameters=parameters,
            correlation_id=correlation_id
        )
    
    async def _process_tool_batch(self, batch: List[BatchRequest]) -> List[Any]:
        """Process a batch of tool execution requests"""
        results = []
        
        for request in batch:
            try:
                result = await self.mcp_manager.execute_tool(
                    tool_name=request.operation,
                    parameters=request.parameters,
                    correlation_id=request.correlation_id
                )
                results.append(result)
            except Exception as e:
                logger.error(f"Tool execution failed: {e}", extra={
                    'tool_name': request.operation,
                    'request_id': request.request_id
                })
                results.append(None)
        
        return results

class APICallBatcher:
    """Specialized batcher for external API calls"""
    
    def __init__(self, http_pool, config: Optional[BatchConfig] = None):
        self.http_pool = http_pool
        self.batcher = RequestBatcher(config)
        self.batcher.register_processor(BatchType.API_CALL, self._process_api_batch)
    
    async def make_request(
        self,
        method: str,
        url: str,
        **kwargs
    ) -> Any:
        """Make an API request with batching"""
        return await self.batcher.submit_request(
            batch_type=BatchType.API_CALL,
            operation=f"{method}_{url}",
            parameters=kwargs
        )
    
    async def _process_api_batch(self, batch: List[BatchRequest]) -> List[Any]:
        """Process a batch of API requests"""
        results = []
        
        # Group requests by URL for potential optimization
        url_groups = defaultdict(list)
        for request in batch:
            url = request.parameters.get('url', '')
            url_groups[url].append(request)
        
        # Process each URL group
        for url, requests in url_groups.items():
            if len(requests) == 1:
                # Single request - execute directly
                request = requests[0]
                try:
                    async with self.http_pool.request(
                        request.parameters.get('method', 'GET'),
                        url,
                        **{k: v for k, v in request.parameters.items() 
                           if k not in ['method', 'url']}
                    ) as response:
                        result = await response.json()
                        results.append(result)
                except Exception as e:
                    logger.error(f"API request failed: {e}", extra={'url': url})
                    results.append(None)
            else:
                # Multiple requests to same URL - could be optimized further
                for request in requests:
                    try:
                        async with self.http_pool.request(
                            request.parameters.get('method', 'GET'),
                            url,
                            **{k: v for k, v in request.parameters.items() 
                               if k not in ['method', 'url']}
                        ) as response:
                            result = await response.json()
                            results.append(result)
                    except Exception as e:
                        logger.error(f"API request failed: {e}", extra={'url': url})
                        results.append(None)
        
        return results

# Global batcher instances
_tool_batcher: Optional[ToolExecutionBatcher] = None
_api_batcher: Optional[APICallBatcher] = None

async def get_tool_batcher(mcp_manager) -> ToolExecutionBatcher:
    """Get global tool execution batcher"""
    global _tool_batcher
    if _tool_batcher is None:
        _tool_batcher = ToolExecutionBatcher(mcp_manager)
    return _tool_batcher

async def get_api_batcher(http_pool) -> APICallBatcher:
    """Get global API call batcher"""
    global _api_batcher
    if _api_batcher is None:
        _api_batcher = APICallBatcher(http_pool)
    return _api_batcher

async def cleanup_batchers():
    """Cleanup all batchers"""
    global _tool_batcher, _api_batcher
    
    if _tool_batcher:
        await _tool_batcher.batcher.shutdown()
        _tool_batcher = None
    
    if _api_batcher:
        await _api_batcher.batcher.shutdown()
        _api_batcher = None
