"""
Performance Benchmarking System for ASK Pipeline

Provides comprehensive performance testing:
- Load testing with concurrent requests
- Performance benchmarks and metrics
- Bottleneck identification
- Performance regression testing
- Capacity planning and optimization
"""

import asyncio
import time
import statistics
from typing import Dict, Any, Optional, List, Callable, Awaitable
from dataclasses import dataclass, field
from datetime import datetime, timed<PERSON><PERSON>
from enum import Enum
import logging
import json
from collections import defaultdict
import psutil
import threading

logger = logging.getLogger(__name__)

class TestType(Enum):
    """Types of performance tests"""
    LOAD_TEST = "load_test"
    STRESS_TEST = "stress_test"
    SPIKE_TEST = "spike_test"
    VOLUME_TEST = "volume_test"
    ENDURANCE_TEST = "endurance_test"

class MetricType(Enum):
    """Types of performance metrics"""
    RESPONSE_TIME = "response_time"
    THROUGHPUT = "throughput"
    ERROR_RATE = "error_rate"
    MEMORY_USAGE = "memory_usage"
    CPU_USAGE = "cpu_usage"
    CONCURRENT_USERS = "concurrent_users"

@dataclass
class BenchmarkConfig:
    """Configuration for performance benchmarks"""
    test_type: TestType
    duration_seconds: int = 60
    concurrent_users: int = 10
    requests_per_second: float = 1.0
    ramp_up_seconds: int = 10
    ramp_down_seconds: int = 10
    max_response_time: float = 5.0
    max_error_rate: float = 0.05
    warm_up_requests: int = 10

@dataclass
class PerformanceMetrics:
    """Performance metrics for a test run"""
    test_name: str
    test_type: TestType
    start_time: datetime
    end_time: datetime
    duration: float
    total_requests: int
    successful_requests: int
    failed_requests: int
    response_times: List[float]
    avg_response_time: float
    p50_response_time: float
    p95_response_time: float
    p99_response_time: float
    max_response_time: float
    min_response_time: float
    throughput: float
    error_rate: float
    memory_usage: List[float]
    cpu_usage: List[float]
    concurrent_users: int

@dataclass
class TestResult:
    """Result of a performance test"""
    config: BenchmarkConfig
    metrics: PerformanceMetrics
    passed: bool
    bottlenecks: List[str]
    recommendations: List[str]
    timestamp: datetime = field(default_factory=datetime.utcnow)

class PerformanceBenchmark:
    """Comprehensive performance benchmarking system"""
    
    def __init__(self):
        self.test_results: List[TestResult] = []
        self.monitoring_active = False
        self.monitoring_data = {
            'memory_usage': [],
            'cpu_usage': [],
            'timestamps': []
        }
        self._monitoring_task: Optional[asyncio.Task] = None
        self._lock = asyncio.Lock()
    
    async def run_benchmark(
        self,
        test_function: Callable,
        config: BenchmarkConfig,
        test_name: str = "performance_test"
    ) -> TestResult:
        """Run a performance benchmark test"""
        logger.info(f"Starting benchmark: {test_name}")
        
        # Start monitoring
        await self._start_monitoring()
        
        # Warm up
        await self._warm_up(test_function, config.warm_up_requests)
        
        # Run test
        start_time = time.time()
        test_metrics = await self._run_test(test_function, config, test_name)
        end_time = time.time()
        
        # Stop monitoring
        await self._stop_monitoring()
        
        # Calculate metrics
        metrics = self._calculate_metrics(
            test_name, config, test_metrics, start_time, end_time
        )
        
        # Analyze results
        passed, bottlenecks, recommendations = self._analyze_results(config, metrics)
        
        # Create test result
        result = TestResult(
            config=config,
            metrics=metrics,
            passed=passed,
            bottlenecks=bottlenecks,
            recommendations=recommendations
        )
        
        # Store result
        async with self._lock:
            self.test_results.append(result)
        
        logger.info(f"Benchmark completed: {test_name} - {'PASSED' if passed else 'FAILED'}")
        return result
    
    async def _warm_up(self, test_function: Callable, warm_up_requests: int):
        """Warm up the system before testing"""
        logger.info(f"Warming up with {warm_up_requests} requests")
        
        for i in range(warm_up_requests):
            try:
                if asyncio.iscoroutinefunction(test_function):
                    await test_function()
                else:
                    test_function()
            except Exception as e:
                logger.warning(f"Warm-up request {i+1} failed: {e}")
        
        # Wait for system to stabilize
        await asyncio.sleep(2)
    
    async def _run_test(
        self, 
        test_function: Callable, 
        config: BenchmarkConfig, 
        test_name: str
    ) -> Dict[str, Any]:
        """Run the actual performance test"""
        test_metrics = {
            'response_times': [],
            'successful_requests': 0,
            'failed_requests': 0,
            'total_requests': 0
        }
        
        # Calculate request intervals
        if config.requests_per_second > 0:
            request_interval = 1.0 / config.requests_per_second
        else:
            request_interval = 0
        
        # Create semaphore for concurrency control
        semaphore = asyncio.Semaphore(config.concurrent_users)
        
        async def make_request():
            async with semaphore:
                start_time = time.time()
                test_metrics['total_requests'] += 1
                
                try:
                    if asyncio.iscoroutinefunction(test_function):
                        await test_function()
                    else:
                        test_function()
                    
                    response_time = time.time() - start_time
                    test_metrics['response_times'].append(response_time)
                    test_metrics['successful_requests'] += 1
                    
                except Exception as e:
                    response_time = time.time() - start_time
                    test_metrics['response_times'].append(response_time)
                    test_metrics['failed_requests'] += 1
                    logger.debug(f"Request failed: {e}")
        
        # Run test for specified duration
        end_time = time.time() + config.duration_seconds
        tasks = []
        
        while time.time() < end_time:
            # Create request task
            task = asyncio.create_task(make_request())
            tasks.append(task)
            
            # Wait for next request
            if request_interval > 0:
                await asyncio.sleep(request_interval)
        
        # Wait for all tasks to complete
        await asyncio.gather(*tasks, return_exceptions=True)
        
        return test_metrics
    
    async def _start_monitoring(self):
        """Start system resource monitoring"""
        if not self.monitoring_active:
            self.monitoring_active = True
            self._monitoring_task = asyncio.create_task(self._monitoring_loop())
    
    async def _stop_monitoring(self):
        """Stop system resource monitoring"""
        self.monitoring_active = False
        if self._monitoring_task and not self._monitoring_task.done():
            self._monitoring_task.cancel()
            try:
                await self._monitoring_task
            except asyncio.CancelledError:
                pass
    
    async def _monitoring_loop(self):
        """Background monitoring loop"""
        process = psutil.Process()
        
        while self.monitoring_active:
            try:
                memory_usage = process.memory_info().rss / (1024 * 1024)  # MB
                cpu_usage = process.cpu_percent()
                
                self.monitoring_data['memory_usage'].append(memory_usage)
                self.monitoring_data['cpu_usage'].append(cpu_usage)
                self.monitoring_data['timestamps'].append(time.time())
                
                await asyncio.sleep(1)  # Monitor every second
            except Exception as e:
                logger.error(f"Monitoring error: {e}")
                await asyncio.sleep(1)
    
    def _calculate_metrics(
        self,
        test_name: str,
        config: BenchmarkConfig,
        test_metrics: Dict[str, Any],
        start_time: float,
        end_time: float
    ) -> PerformanceMetrics:
        """Calculate performance metrics from test data"""
        duration = end_time - start_time
        response_times = test_metrics['response_times']
        
        # Calculate response time statistics
        if response_times:
            avg_response_time = statistics.mean(response_times)
            p50_response_time = statistics.median(response_times)
            p95_response_time = self._percentile(response_times, 95)
            p99_response_time = self._percentile(response_times, 99)
            max_response_time = max(response_times)
            min_response_time = min(response_times)
        else:
            avg_response_time = p50_response_time = p95_response_time = p99_response_time = 0.0
            max_response_time = min_response_time = 0.0
        
        # Calculate throughput
        throughput = test_metrics['total_requests'] / duration if duration > 0 else 0
        
        # Calculate error rate
        error_rate = (
            test_metrics['failed_requests'] / test_metrics['total_requests']
            if test_metrics['total_requests'] > 0 else 0
        )
        
        return PerformanceMetrics(
            test_name=test_name,
            test_type=config.test_type,
            start_time=datetime.fromtimestamp(start_time),
            end_time=datetime.fromtimestamp(end_time),
            duration=duration,
            total_requests=test_metrics['total_requests'],
            successful_requests=test_metrics['successful_requests'],
            failed_requests=test_metrics['failed_requests'],
            response_times=response_times,
            avg_response_time=avg_response_time,
            p50_response_time=p50_response_time,
            p95_response_time=p95_response_time,
            p99_response_time=p99_response_time,
            max_response_time=max_response_time,
            min_response_time=min_response_time,
            throughput=throughput,
            error_rate=error_rate,
            memory_usage=self.monitoring_data['memory_usage'].copy(),
            cpu_usage=self.monitoring_data['cpu_usage'].copy(),
            concurrent_users=config.concurrent_users
        )
    
    def _percentile(self, data: List[float], percentile: int) -> float:
        """Calculate percentile of data"""
        if not data:
            return 0.0
        
        sorted_data = sorted(data)
        index = int((percentile / 100) * len(sorted_data))
        return sorted_data[min(index, len(sorted_data) - 1)]
    
    def _analyze_results(
        self, 
        config: BenchmarkConfig, 
        metrics: PerformanceMetrics
    ) -> tuple[bool, List[str], List[str]]:
        """Analyze test results and provide recommendations"""
        passed = True
        bottlenecks = []
        recommendations = []
        
        # Check response time
        if metrics.avg_response_time > config.max_response_time:
            passed = False
            bottlenecks.append(f"High average response time: {metrics.avg_response_time:.3f}s")
            recommendations.append("Optimize slow operations or increase capacity")
        
        # Check error rate
        if metrics.error_rate > config.max_error_rate:
            passed = False
            bottlenecks.append(f"High error rate: {metrics.error_rate:.2%}")
            recommendations.append("Investigate and fix error sources")
        
        # Check P95 response time
        if metrics.p95_response_time > config.max_response_time * 2:
            bottlenecks.append(f"High P95 response time: {metrics.p95_response_time:.3f}s")
            recommendations.append("Optimize slowest operations")
        
        # Check throughput
        expected_throughput = config.requests_per_second
        if expected_throughput > 0 and metrics.throughput < expected_throughput * 0.8:
            bottlenecks.append(f"Low throughput: {metrics.throughput:.2f} req/s")
            recommendations.append("Increase concurrency or optimize processing")
        
        # Check memory usage
        if metrics.memory_usage:
            max_memory = max(metrics.memory_usage)
            if max_memory > 500:  # 500MB threshold
                bottlenecks.append(f"High memory usage: {max_memory:.1f}MB")
                recommendations.append("Optimize memory usage or increase memory limit")
        
        # Check CPU usage
        if metrics.cpu_usage:
            avg_cpu = statistics.mean(metrics.cpu_usage)
            if avg_cpu > 80:  # 80% threshold
                bottlenecks.append(f"High CPU usage: {avg_cpu:.1f}%")
                recommendations.append("Optimize CPU-intensive operations")
        
        return passed, bottlenecks, recommendations
    
    def get_benchmark_summary(self) -> Dict[str, Any]:
        """Get summary of all benchmark results"""
        if not self.test_results:
            return {"message": "No benchmark results available"}
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result.passed)
        
        # Calculate average metrics
        avg_response_times = [
            result.metrics.avg_response_time 
            for result in self.test_results
        ]
        avg_throughputs = [
            result.metrics.throughput 
            for result in self.test_results
        ]
        avg_error_rates = [
            result.metrics.error_rate 
            for result in self.test_results
        ]
        
        return {
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'failed_tests': total_tests - passed_tests,
            'pass_rate': passed_tests / total_tests if total_tests > 0 else 0,
            'avg_response_time': statistics.mean(avg_response_times) if avg_response_times else 0,
            'avg_throughput': statistics.mean(avg_throughputs) if avg_throughputs else 0,
            'avg_error_rate': statistics.mean(avg_error_rates) if avg_error_rates else 0,
            'recent_tests': [
                {
                    'test_name': result.metrics.test_name,
                    'test_type': result.metrics.test_type.value,
                    'passed': result.passed,
                    'avg_response_time': result.metrics.avg_response_time,
                    'throughput': result.metrics.throughput,
                    'error_rate': result.metrics.error_rate,
                    'timestamp': result.timestamp.isoformat()
                }
                for result in self.test_results[-5:]  # Last 5 tests
            ]
        }
    
    def export_results(self, filename: str):
        """Export benchmark results to JSON file"""
        results_data = []
        
        for result in self.test_results:
            result_data = {
                'config': {
                    'test_type': result.config.test_type.value,
                    'duration_seconds': result.config.duration_seconds,
                    'concurrent_users': result.config.concurrent_users,
                    'requests_per_second': result.config.requests_per_second
                },
                'metrics': {
                    'test_name': result.metrics.test_name,
                    'duration': result.metrics.duration,
                    'total_requests': result.metrics.total_requests,
                    'successful_requests': result.metrics.successful_requests,
                    'failed_requests': result.metrics.failed_requests,
                    'avg_response_time': result.metrics.avg_response_time,
                    'p50_response_time': result.metrics.p50_response_time,
                    'p95_response_time': result.metrics.p95_response_time,
                    'p99_response_time': result.metrics.p99_response_time,
                    'max_response_time': result.metrics.max_response_time,
                    'min_response_time': result.metrics.min_response_time,
                    'throughput': result.metrics.throughput,
                    'error_rate': result.metrics.error_rate,
                    'concurrent_users': result.metrics.concurrent_users
                },
                'analysis': {
                    'passed': result.passed,
                    'bottlenecks': result.bottlenecks,
                    'recommendations': result.recommendations
                },
                'timestamp': result.timestamp.isoformat()
            }
            results_data.append(result_data)
        
        with open(filename, 'w') as f:
            json.dump(results_data, f, indent=2)
        
        logger.info(f"Benchmark results exported to {filename}")

# Global benchmark instance
_benchmark: Optional[PerformanceBenchmark] = None

def get_benchmark() -> PerformanceBenchmark:
    """Get global performance benchmark instance"""
    global _benchmark
    if _benchmark is None:
        _benchmark = PerformanceBenchmark()
    return _benchmark

async def cleanup_benchmark():
    """Cleanup global benchmark instance"""
    global _benchmark
    if _benchmark:
        await _benchmark._stop_monitoring()
        _benchmark = None
