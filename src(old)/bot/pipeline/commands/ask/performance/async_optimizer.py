"""
Async Operation Optimizer for ASK Pipeline

Provides optimization for async operations:
- Identifies and converts blocking operations to async
- Implements async context managers for resource management
- Optimizes concurrent execution patterns
- Provides performance monitoring and recommendations
"""

import asyncio
import time
import inspect
import functools
from typing import Dict, Any, Optional, List, Callable, Awaitable, Union, TypeVar
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import logging
from concurrent.futures import ThreadPoolExecutor
import threading

logger = logging.getLogger(__name__)

T = TypeVar('T')

class OperationType(Enum):
    """Types of operations that can be optimized"""
    BLOCKING_IO = "blocking_io"
    CPU_INTENSIVE = "cpu_intensive"
    NETWORK_REQUEST = "network_request"
    DATABASE_QUERY = "database_query"
    FILE_OPERATION = "file_operation"
    CACHE_OPERATION = "cache_operation"

@dataclass
class OperationMetrics:
    """Metrics for operation performance"""
    operation_name: str
    operation_type: OperationType
    total_calls: int = 0
    total_time: float = 0.0
    avg_time: float = 0.0
    max_time: float = 0.0
    min_time: float = float('inf')
    success_count: int = 0
    error_count: int = 0
    last_called: Optional[datetime] = None

@dataclass
class OptimizationRecommendation:
    """Recommendation for operation optimization"""
    operation_name: str
    current_type: OperationType
    recommended_type: OperationType
    potential_savings: float
    confidence: float
    reason: str

class AsyncOptimizer:
    """Optimizes async operations and identifies blocking code"""
    
    def __init__(self):
        self.metrics: Dict[str, OperationMetrics] = {}
        self.thread_pool = ThreadPoolExecutor(max_workers=4)
        self.recommendations: List[OptimizationRecommendation] = []
        self._lock = threading.Lock()
        
    def monitor_operation(self, operation_type: OperationType):
        """Decorator to monitor operation performance"""
        def decorator(func: Callable) -> Callable:
            @functools.wraps(func)
            async def async_wrapper(*args, **kwargs):
                return await self._monitor_async_operation(
                    func, operation_type, *args, **kwargs
                )
            
            @functools.wraps(func)
            def sync_wrapper(*args, **kwargs):
                return self._monitor_sync_operation(
                    func, operation_type, *args, **kwargs
                )
            
            if asyncio.iscoroutinefunction(func):
                return async_wrapper
            else:
                return sync_wrapper
        
        return decorator
    
    async def _monitor_async_operation(
        self, 
        func: Callable, 
        operation_type: OperationType, 
        *args, **kwargs
    ) -> Any:
        """Monitor async operation performance"""
        start_time = time.time()
        operation_name = f"{func.__module__}.{func.__name__}"
        
        try:
            result = await func(*args, **kwargs)
            await self._record_success(operation_name, operation_type, time.time() - start_time)
            return result
        except Exception as e:
            await self._record_error(operation_name, operation_type, time.time() - start_time)
            raise
    
    def _monitor_sync_operation(
        self, 
        func: Callable, 
        operation_type: OperationType, 
        *args, **kwargs
    ) -> Any:
        """Monitor sync operation performance"""
        start_time = time.time()
        operation_name = f"{func.__module__}.{func.__name__}"
        
        try:
            result = func(*args, **kwargs)
            self._record_success_sync(operation_name, operation_type, time.time() - start_time)
            return result
        except Exception as e:
            self._record_error_sync(operation_name, operation_type, time.time() - start_time)
            raise
    
    async def _record_success(self, operation_name: str, operation_type: OperationType, duration: float):
        """Record successful async operation"""
        with self._lock:
            if operation_name not in self.metrics:
                self.metrics[operation_name] = OperationMetrics(
                    operation_name=operation_name,
                    operation_type=operation_type
                )
            
            metrics = self.metrics[operation_name]
            metrics.total_calls += 1
            metrics.total_time += duration
            metrics.avg_time = metrics.total_time / metrics.total_calls
            metrics.max_time = max(metrics.max_time, duration)
            metrics.min_time = min(metrics.min_time, duration)
            metrics.success_count += 1
            metrics.last_called = datetime.utcnow()
    
    def _record_success_sync(self, operation_name: str, operation_type: OperationType, duration: float):
        """Record successful sync operation"""
        with self._lock:
            if operation_name not in self.metrics:
                self.metrics[operation_name] = OperationMetrics(
                    operation_name=operation_name,
                    operation_type=operation_type
                )
            
            metrics = self.metrics[operation_name]
            metrics.total_calls += 1
            metrics.total_time += duration
            metrics.avg_time = metrics.total_time / metrics.total_calls
            metrics.max_time = max(metrics.max_time, duration)
            metrics.min_time = min(metrics.min_time, duration)
            metrics.success_count += 1
            metrics.last_called = datetime.utcnow()
    
    async def _record_error(self, operation_name: str, operation_type: OperationType, duration: float):
        """Record failed async operation"""
        with self._lock:
            if operation_name not in self.metrics:
                self.metrics[operation_name] = OperationMetrics(
                    operation_name=operation_name,
                    operation_type=operation_type
                )
            
            metrics = self.metrics[operation_name]
            metrics.total_calls += 1
            metrics.total_time += duration
            metrics.error_count += 1
            metrics.last_called = datetime.utcnow()
    
    def _record_error_sync(self, operation_name: str, operation_type: OperationType, duration: float):
        """Record failed sync operation"""
        with self._lock:
            if operation_name not in self.metrics:
                self.metrics[operation_name] = OperationMetrics(
                    operation_name=operation_name,
                    operation_type=operation_type
                )
            
            metrics = self.metrics[operation_name]
            metrics.total_calls += 1
            metrics.total_time += duration
            metrics.error_count += 1
            metrics.last_called = datetime.utcnow()
    
    def to_async(self, func: Callable) -> Callable:
        """Convert a blocking function to async using thread pool"""
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(self.thread_pool, func, *args, **kwargs)
        
        return async_wrapper
    
    def run_concurrent(self, *coroutines: Awaitable) -> Callable:
        """Run multiple coroutines concurrently"""
        async def concurrent_wrapper():
            return await asyncio.gather(*coroutines, return_exceptions=True)
        
        return concurrent_wrapper
    
    def analyze_performance(self) -> List[OptimizationRecommendation]:
        """Analyze performance and generate optimization recommendations"""
        recommendations = []
        
        with self._lock:
            for operation_name, metrics in self.metrics.items():
                if metrics.total_calls < 5:  # Need minimum data
                    continue
                
                # Check for blocking operations that could be async
                if (metrics.operation_type in [OperationType.BLOCKING_IO, OperationType.FILE_OPERATION] 
                    and metrics.avg_time > 0.1):  # > 100ms
                    
                    potential_savings = metrics.avg_time * 0.8  # Assume 80% improvement
                    confidence = min(0.9, metrics.total_calls / 100)  # Higher confidence with more data
                    
                    recommendations.append(OptimizationRecommendation(
                        operation_name=operation_name,
                        current_type=metrics.operation_type,
                        recommended_type=OperationType.NETWORK_REQUEST,  # Make async
                        potential_savings=potential_savings,
                        confidence=confidence,
                        reason=f"Blocking operation averaging {metrics.avg_time:.3f}s could be made async"
                    ))
                
                # Check for operations that could be batched
                elif (metrics.operation_type == OperationType.NETWORK_REQUEST 
                      and metrics.avg_time < 0.05  # < 50ms
                      and metrics.total_calls > 10):
                    
                    potential_savings = metrics.avg_time * 0.5  # Assume 50% improvement with batching
                    confidence = min(0.7, metrics.total_calls / 50)
                    
                    recommendations.append(OptimizationRecommendation(
                        operation_name=operation_name,
                        current_type=metrics.operation_type,
                        recommended_type=OperationType.NETWORK_REQUEST,  # Batch requests
                        potential_savings=potential_savings,
                        confidence=confidence,
                        reason=f"Frequent short operations ({metrics.total_calls} calls) could be batched"
                    ))
                
                # Check for CPU-intensive operations that could be parallelized
                elif (metrics.operation_type == OperationType.CPU_INTENSIVE 
                      and metrics.avg_time > 0.2  # > 200ms
                      and metrics.total_calls > 5):
                    
                    potential_savings = metrics.avg_time * 0.6  # Assume 60% improvement with parallelization
                    confidence = min(0.8, metrics.total_calls / 20)
                    
                    recommendations.append(OptimizationRecommendation(
                        operation_name=operation_name,
                        current_type=metrics.operation_type,
                        recommended_type=OperationType.CPU_INTENSIVE,  # Parallelize
                        potential_savings=potential_savings,
                        confidence=confidence,
                        reason=f"CPU-intensive operation averaging {metrics.avg_time:.3f}s could be parallelized"
                    ))
        
        self.recommendations = recommendations
        return recommendations
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get summary of operation metrics"""
        with self._lock:
            total_operations = sum(m.total_calls for m in self.metrics.values())
            total_time = sum(m.total_time for m in self.metrics.values())
            avg_time = total_time / total_operations if total_operations > 0 else 0
            
            slowest_operations = sorted(
                self.metrics.values(),
                key=lambda x: x.avg_time,
                reverse=True
            )[:5]
            
            most_frequent_operations = sorted(
                self.metrics.values(),
                key=lambda x: x.total_calls,
                reverse=True
            )[:5]
            
            return {
                'total_operations': total_operations,
                'total_time': total_time,
                'avg_time': avg_time,
                'slowest_operations': [
                    {
                        'name': op.operation_name,
                        'avg_time': op.avg_time,
                        'total_calls': op.total_calls
                    }
                    for op in slowest_operations
                ],
                'most_frequent_operations': [
                    {
                        'name': op.operation_name,
                        'total_calls': op.total_calls,
                        'avg_time': op.avg_time
                    }
                    for op in most_frequent_operations
                ],
                'recommendations_count': len(self.recommendations)
            }
    
    def cleanup(self):
        """Cleanup resources"""
        self.thread_pool.shutdown(wait=True)

class AsyncContextManager:
    """Async context manager for resource management"""
    
    def __init__(self, resource_factory: Callable, cleanup_func: Callable):
        self.resource_factory = resource_factory
        self.cleanup_func = cleanup_func
        self.resource = None
    
    async def __aenter__(self):
        self.resource = await self.resource_factory()
        return self.resource
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.resource:
            await self.cleanup_func(self.resource)

class ConcurrentExecutor:
    """Execute multiple operations concurrently with optimization"""
    
    def __init__(self, max_concurrent: int = 10):
        self.max_concurrent = max_concurrent
        self.semaphore = asyncio.Semaphore(max_concurrent)
    
    async def execute_concurrent(
        self, 
        operations: List[Callable], 
        *args, **kwargs
    ) -> List[Any]:
        """Execute multiple operations concurrently"""
        async def execute_with_semaphore(operation):
            async with self.semaphore:
                if asyncio.iscoroutinefunction(operation):
                    return await operation(*args, **kwargs)
                else:
                    return operation(*args, **kwargs)
        
        tasks = [execute_with_semaphore(op) for op in operations]
        return await asyncio.gather(*tasks, return_exceptions=True)
    
    async def execute_sequential(
        self, 
        operations: List[Callable], 
        *args, **kwargs
    ) -> List[Any]:
        """Execute operations sequentially (for dependencies)"""
        results = []
        for operation in operations:
            if asyncio.iscoroutinefunction(operation):
                result = await operation(*args, **kwargs)
            else:
                result = operation(*args, **kwargs)
            results.append(result)
        return results

# Global optimizer instance
_optimizer: Optional[AsyncOptimizer] = None

def get_optimizer() -> AsyncOptimizer:
    """Get global async optimizer"""
    global _optimizer
    if _optimizer is None:
        _optimizer = AsyncOptimizer()
    return _optimizer

def cleanup_optimizer():
    """Cleanup global optimizer"""
    global _optimizer
    if _optimizer:
        _optimizer.cleanup()
        _optimizer = None
