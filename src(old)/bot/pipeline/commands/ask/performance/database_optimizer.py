"""
Database Optimization System for ASK Pipeline

Provides comprehensive database optimization:
- Connection pooling and management
- Query optimization and caching
- Connection health monitoring
- Performance analytics and recommendations
- Automatic query optimization
"""

import asyncio
import time
import logging
from typing import Dict, Any, Optional, List, Union, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import json
import hashlib
from collections import defaultdict, deque
import statistics

logger = logging.getLogger(__name__)

class QueryType(Enum):
    """Types of database queries"""
    SELECT = "select"
    INSERT = "insert"
    UPDATE = "update"
    DELETE = "delete"
    CREATE = "create"
    DROP = "drop"

class ConnectionStatus(Enum):
    """Database connection status"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    DISCONNECTED = "disconnected"

@dataclass
class QueryMetrics:
    """Metrics for database queries"""
    query_hash: str
    query_type: QueryType
    execution_count: int = 0
    total_time: float = 0.0
    avg_time: float = 0.0
    min_time: float = float('inf')
    max_time: float = 0.0
    error_count: int = 0
    last_executed: Optional[datetime] = None
    cache_hits: int = 0
    cache_misses: int = 0

@dataclass
class ConnectionMetrics:
    """Metrics for database connections"""
    total_connections: int = 0
    active_connections: int = 0
    idle_connections: int = 0
    failed_connections: int = 0
    connection_errors: int = 0
    avg_connection_time: float = 0.0
    max_connection_time: float = 0.0
    last_health_check: Optional[datetime] = None

@dataclass
class DatabaseConfig:
    """Database configuration"""
    max_connections: int = 20
    min_connections: int = 5
    connection_timeout: float = 30.0
    query_timeout: float = 60.0
    enable_query_cache: bool = True
    cache_ttl: float = 300.0
    enable_connection_pooling: bool = True
    health_check_interval: int = 30
    slow_query_threshold: float = 1.0

class DatabaseOptimizer:
    """Comprehensive database optimization system"""
    
    def __init__(self, config: Optional[DatabaseConfig] = None):
        self.config = config or DatabaseConfig()
        self.query_metrics: Dict[str, QueryMetrics] = {}
        self.connection_metrics = ConnectionMetrics()
        self.query_cache: Dict[str, Any] = {}
        self.connection_pool: List[Any] = []
        self.active_connections: int = 0
        self.health_check_task: Optional[asyncio.Task] = None
        self._lock = asyncio.Lock()
        self._shutdown = False
        
    async def start(self):
        """Start database optimizer"""
        if self.config.enable_connection_pooling:
            await self._initialize_connection_pool()
        
        if self.config.health_check_interval > 0:
            self.health_check_task = asyncio.create_task(self._health_check_loop())
        
        logger.info("Database optimizer started")
    
    async def stop(self):
        """Stop database optimizer"""
        self._shutdown = True
        
        if self.health_check_task and not self.health_check_task.done():
            self.health_check_task.cancel()
            try:
                await self.health_check_task
            except asyncio.CancelledError:
                pass
        
        await self._close_all_connections()
        logger.info("Database optimizer stopped")
    
    async def _initialize_connection_pool(self):
        """Initialize database connection pool"""
        # This would be implemented based on the specific database driver
        # For now, we'll create a placeholder
        for i in range(self.config.min_connections):
            connection = await self._create_connection()
            if connection:
                self.connection_pool.append(connection)
                self.connection_metrics.total_connections += 1
        
        logger.info(f"Initialized connection pool with {len(self.connection_pool)} connections")
    
    async def _create_connection(self) -> Optional[Any]:
        """Create a new database connection"""
        try:
            # This would be implemented based on the specific database driver
            # For now, we'll return a placeholder
            connection = f"db_connection_{len(self.connection_pool)}"
            self.connection_metrics.total_connections += 1
            return connection
        except Exception as e:
            logger.error(f"Failed to create database connection: {e}")
            self.connection_metrics.failed_connections += 1
            return None
    
    async def get_connection(self) -> Optional[Any]:
        """Get a connection from the pool"""
        async with self._lock:
            if self.connection_pool:
                connection = self.connection_pool.pop()
                self.active_connections += 1
                self.connection_metrics.active_connections = self.active_connections
                return connection
            else:
                # Create new connection if pool is empty and under limit
                if self.connection_metrics.total_connections < self.config.max_connections:
                    connection = await self._create_connection()
                    if connection:
                        self.active_connections += 1
                        self.connection_metrics.active_connections = self.active_connections
                    return connection
                else:
                    logger.warning("Connection pool exhausted")
                    return None
    
    async def return_connection(self, connection: Any):
        """Return a connection to the pool"""
        async with self._lock:
            if connection and len(self.connection_pool) < self.config.max_connections:
                self.connection_pool.append(connection)
                self.active_connections -= 1
                self.connection_metrics.active_connections = self.active_connections
            else:
                # Close connection if pool is full
                await self._close_connection(connection)
    
    async def _close_connection(self, connection: Any):
        """Close a database connection"""
        try:
            # This would be implemented based on the specific database driver
            logger.debug(f"Closing connection: {connection}")
            self.active_connections -= 1
            self.connection_metrics.active_connections = self.active_connections
        except Exception as e:
            logger.error(f"Error closing connection: {e}")
    
    async def _close_all_connections(self):
        """Close all database connections"""
        for connection in self.connection_pool:
            await self._close_connection(connection)
        self.connection_pool.clear()
        self.active_connections = 0
        self.connection_metrics.active_connections = 0
    
    async def execute_query(
        self,
        query: str,
        parameters: Optional[Dict[str, Any]] = None,
        query_type: Optional[QueryType] = None
    ) -> Any:
        """Execute a database query with optimization"""
        start_time = time.time()
        query_hash = self._hash_query(query)
        
        # Check query cache first
        if self.config.enable_query_cache:
            cached_result = await self._get_from_cache(query_hash)
            if cached_result is not None:
                await self._record_cache_hit(query_hash)
                return cached_result
        
        # Get connection
        connection = await self.get_connection()
        if not connection:
            raise RuntimeError("No database connection available")
        
        try:
            # Execute query
            result = await self._execute_with_connection(connection, query, parameters)
            
            # Cache result if it's a SELECT query
            if self.config.enable_query_cache and query_type == QueryType.SELECT:
                await self._cache_result(query_hash, result)
            
            # Record metrics
            execution_time = time.time() - start_time
            await self._record_query_metrics(query_hash, query_type, execution_time, True)
            
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            await self._record_query_metrics(query_hash, query_type, execution_time, False)
            logger.error(f"Query execution failed: {e}")
            raise
        finally:
            await self.return_connection(connection)
    
    async def _execute_with_connection(
        self, 
        connection: Any, 
        query: str, 
        parameters: Optional[Dict[str, Any]]
    ) -> Any:
        """Execute query with specific connection"""
        # This would be implemented based on the specific database driver
        # For now, we'll simulate query execution
        await asyncio.sleep(0.01)  # Simulate query execution time
        return {"result": "query_executed", "query": query}
    
    def _hash_query(self, query: str) -> str:
        """Generate hash for query caching"""
        return hashlib.md5(query.encode()).hexdigest()
    
    async def _get_from_cache(self, query_hash: str) -> Optional[Any]:
        """Get result from query cache"""
        if query_hash in self.query_cache:
            cache_entry = self.query_cache[query_hash]
            if time.time() - cache_entry['timestamp'] < self.config.cache_ttl:
                return cache_entry['result']
            else:
                # Remove expired entry
                del self.query_cache[query_hash]
        return None
    
    async def _cache_result(self, query_hash: str, result: Any):
        """Cache query result"""
        self.query_cache[query_hash] = {
            'result': result,
            'timestamp': time.time()
        }
    
    async def _record_cache_hit(self, query_hash: str):
        """Record cache hit"""
        if query_hash in self.query_metrics:
            self.query_metrics[query_hash].cache_hits += 1
    
    async def _record_query_metrics(
        self, 
        query_hash: str, 
        query_type: Optional[QueryType], 
        execution_time: float, 
        success: bool
    ):
        """Record query execution metrics"""
        if query_hash not in self.query_metrics:
            self.query_metrics[query_hash] = QueryMetrics(
                query_hash=query_hash,
                query_type=query_type or QueryType.SELECT
            )
        
        metrics = self.query_metrics[query_hash]
        metrics.execution_count += 1
        metrics.total_time += execution_time
        metrics.avg_time = metrics.total_time / metrics.execution_count
        metrics.min_time = min(metrics.min_time, execution_time)
        metrics.max_time = max(metrics.max_time, execution_time)
        metrics.last_executed = datetime.utcnow()
        
        if not success:
            metrics.error_count += 1
        
        # Check for slow queries
        if execution_time > self.config.slow_query_threshold:
            logger.warning(f"Slow query detected: {execution_time:.3f}s")
    
    async def _health_check_loop(self):
        """Background health check loop"""
        while not self._shutdown:
            try:
                await self._perform_health_check()
                await asyncio.sleep(self.config.health_check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Health check error: {e}")
                await asyncio.sleep(30)  # Wait before retry
    
    async def _perform_health_check(self):
        """Perform database health check"""
        try:
            # Test connection
            connection = await self.get_connection()
            if connection:
                # Test query
                await self._execute_with_connection(connection, "SELECT 1", None)
                await self.return_connection(connection)
                
                self.connection_metrics.last_health_check = datetime.utcnow()
                logger.debug("Database health check passed")
            else:
                logger.warning("Database health check failed: no connection available")
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            self.connection_metrics.connection_errors += 1
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get database performance metrics"""
        total_queries = sum(m.execution_count for m in self.query_metrics.values())
        total_time = sum(m.total_time for m in self.query_metrics.values())
        avg_query_time = total_time / total_queries if total_queries > 0 else 0
        
        # Find slowest queries
        slowest_queries = sorted(
            self.query_metrics.values(),
            key=lambda x: x.avg_time,
            reverse=True
        )[:5]
        
        # Find most frequent queries
        most_frequent_queries = sorted(
            self.query_metrics.values(),
            key=lambda x: x.execution_count,
            reverse=True
        )[:5]
        
        return {
            'connection_metrics': {
                'total_connections': self.connection_metrics.total_connections,
                'active_connections': self.connection_metrics.active_connections,
                'idle_connections': len(self.connection_pool),
                'failed_connections': self.connection_metrics.failed_connections,
                'connection_errors': self.connection_metrics.connection_errors,
                'last_health_check': self.connection_metrics.last_health_check.isoformat() if self.connection_metrics.last_health_check else None
            },
            'query_metrics': {
                'total_queries': total_queries,
                'avg_query_time': avg_query_time,
                'cache_hit_rate': self._calculate_cache_hit_rate(),
                'slowest_queries': [
                    {
                        'query_hash': q.query_hash,
                        'query_type': q.query_type.value,
                        'avg_time': q.avg_time,
                        'execution_count': q.execution_count
                    }
                    for q in slowest_queries
                ],
                'most_frequent_queries': [
                    {
                        'query_hash': q.query_hash,
                        'query_type': q.query_type.value,
                        'execution_count': q.execution_count,
                        'avg_time': q.avg_time
                    }
                    for q in most_frequent_queries
                ]
            },
            'cache_metrics': {
                'cache_size': len(self.query_cache),
                'cache_ttl': self.config.cache_ttl
            }
        }
    
    def _calculate_cache_hit_rate(self) -> float:
        """Calculate query cache hit rate"""
        total_hits = sum(m.cache_hits for m in self.query_metrics.values())
        total_misses = sum(m.cache_misses for m in self.query_metrics.values())
        
        if total_hits + total_misses == 0:
            return 0.0
        
        return total_hits / (total_hits + total_misses)
    
    def get_optimization_recommendations(self) -> List[str]:
        """Get database optimization recommendations"""
        recommendations = []
        
        # Check cache hit rate
        cache_hit_rate = self._calculate_cache_hit_rate()
        if cache_hit_rate < 0.5:
            recommendations.append(
                f"Low cache hit rate ({cache_hit_rate:.2%}). "
                "Consider increasing cache TTL or optimizing queries."
            )
        
        # Check for slow queries
        slow_queries = [
            q for q in self.query_metrics.values() 
            if q.avg_time > self.config.slow_query_threshold
        ]
        if slow_queries:
            recommendations.append(
                f"Found {len(slow_queries)} slow queries. "
                "Consider adding indexes or optimizing query logic."
            )
        
        # Check connection pool utilization
        if self.connection_metrics.total_connections > 0:
            utilization = self.connection_metrics.active_connections / self.connection_metrics.total_connections
            if utilization > 0.8:
                recommendations.append(
                    "High connection pool utilization. "
                    "Consider increasing max connections or optimizing connection usage."
                )
            elif utilization < 0.2:
                recommendations.append(
                    "Low connection pool utilization. "
                    "Consider decreasing max connections to save resources."
                )
        
        # Check error rate
        total_queries = sum(m.execution_count for m in self.query_metrics.values())
        total_errors = sum(m.error_count for m in self.query_metrics.values())
        if total_queries > 0:
            error_rate = total_errors / total_queries
            if error_rate > 0.05:  # 5% error rate
                recommendations.append(
                    f"High query error rate ({error_rate:.2%}). "
                    "Investigate and fix query errors."
                )
        
        return recommendations
    
    async def cleanup(self):
        """Cleanup database optimizer"""
        await self.stop()
        self.query_metrics.clear()
        self.query_cache.clear()
        logger.info("Database optimizer cleaned up")

# Global database optimizer instance
_db_optimizer: Optional[DatabaseOptimizer] = None

async def get_database_optimizer() -> DatabaseOptimizer:
    """Get global database optimizer"""
    global _db_optimizer
    if _db_optimizer is None:
        _db_optimizer = DatabaseOptimizer()
        await _db_optimizer.start()
    return _db_optimizer

async def cleanup_database_optimizer():
    """Cleanup global database optimizer"""
    global _db_optimizer
    if _db_optimizer:
        await _db_optimizer.cleanup()
        _db_optimizer = None
