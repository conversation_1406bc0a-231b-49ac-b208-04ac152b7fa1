"""
Performance Optimization System for ASK Pipeline

This module provides comprehensive performance optimization capabilities:

1. Connection Pooling - Optimized connection pools for HTTP, Redis, and MCP clients
2. Request Batching - Intelligent batching for tool executions and API calls
3. Async Optimization - Converts blocking operations to async and optimizes concurrency
4. Smart Caching - Intelligent cache warming, compression, and analytics
5. Resource Management - Memory, CPU, and connection monitoring with optimization

Features:
- Automatic connection pooling with health checks
- Request batching with priority handling
- Async operation optimization and monitoring
- Smart cache warming based on usage patterns
- Resource monitoring with automatic optimization
- Performance analytics and recommendations
"""

from .connection_pool import (
    ConnectionPoolManager,
    HTTPConnectionPool,
    RedisConnectionPool,
    MCPConnectionPool,
    PoolConfig,
    ConnectionStats,
    get_connection_pool_manager,
    cleanup_connection_pools
)

from .request_batcher import (
    RequestBatcher,
    ToolExecutionBatcher,
    APICallBatcher,
    BatchType,
    BatchConfig,
    BatchRequest,
    BatchResult,
    get_tool_batcher,
    get_api_batcher,
    cleanup_batchers
)

from .async_optimizer import (
    AsyncOptimizer,
    ConcurrentExecutor,
    AsyncContextManager,
    OperationType,
    OperationMetrics,
    OptimizationRecommendation,
    get_optimizer,
    cleanup_optimizer
)

from .smart_cache import (
    SmartCacheManager,
    CacheStrategy,
    CacheEntry,
    CacheAnalytics,
    WarmingPattern,
    get_smart_cache,
    cleanup_smart_cache
)

from .resource_manager import (
    ResourceManager,
    ResourceType,
    ResourceStatus,
    ResourceLimits,
    ResourceMetrics,
    PerformanceRecommendation,
    get_resource_manager,
    cleanup_resource_manager
)

__all__ = [
    # Connection Pooling
    'ConnectionPoolManager',
    'HTTPConnectionPool',
    'RedisConnectionPool',
    'MCPConnectionPool',
    'PoolConfig',
    'ConnectionStats',
    'get_connection_pool_manager',
    'cleanup_connection_pools',
    
    # Request Batching
    'RequestBatcher',
    'ToolExecutionBatcher',
    'APICallBatcher',
    'BatchType',
    'BatchConfig',
    'BatchRequest',
    'BatchResult',
    'get_tool_batcher',
    'get_api_batcher',
    'cleanup_batchers',
    
    # Async Optimization
    'AsyncOptimizer',
    'ConcurrentExecutor',
    'AsyncContextManager',
    'OperationType',
    'OperationMetrics',
    'OptimizationRecommendation',
    'get_optimizer',
    'cleanup_optimizer',
    
    # Smart Caching
    'SmartCacheManager',
    'CacheStrategy',
    'CacheEntry',
    'CacheAnalytics',
    'WarmingPattern',
    'get_smart_cache',
    'cleanup_smart_cache',
    
    # Resource Management
    'ResourceManager',
    'ResourceType',
    'ResourceStatus',
    'ResourceLimits',
    'ResourceMetrics',
    'PerformanceRecommendation',
    'get_resource_manager',
    'cleanup_resource_manager'
]
