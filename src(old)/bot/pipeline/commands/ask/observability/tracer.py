"""
Distributed Tracing System for ASK Pipeline

Provides comprehensive request flow tracking with:
- Trace context propagation across async operations
- Span creation for all major pipeline stages
- OpenTelemetry integration for trace export
- Performance insights and bottleneck identification
- Request flow visualization
"""

import time
import asyncio
import threading
import uuid
from typing import Dict, Any, Optional, List, Callable, Awaitable, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from contextlib import asynccontextmanager
import json

class SpanKind(Enum):
    """Types of spans"""
    SERVER = "server"
    CLIENT = "client"
    PRODUCER = "producer"
    CONSUMER = "consumer"
    INTERNAL = "internal"

class SpanStatus(Enum):
    """Span status"""
    OK = "ok"
    ERROR = "error"
    TIMEOUT = "timeout"
    CANCELLED = "cancelled"

@dataclass
class SpanContext:
    """Span context for trace propagation"""
    trace_id: str
    span_id: str
    parent_span_id: Optional[str] = None
    baggage: Dict[str, str] = field(default_factory=dict)

@dataclass
class SpanEvent:
    """Event within a span"""
    timestamp: float
    name: str
    attributes: Dict[str, Any] = field(default_factory=dict)

@dataclass
class Span:
    """Individual span in a trace"""
    trace_id: str
    span_id: str
    parent_span_id: Optional[str]
    operation_name: str
    start_time: float
    end_time: Optional[float] = None
    duration: Optional[float] = None
    status: SpanStatus = SpanStatus.OK
    kind: SpanKind = SpanKind.INTERNAL
    tags: Dict[str, Any] = field(default_factory=dict)
    events: List[SpanEvent] = field(default_factory=list)
    error: Optional[str] = None
    
    def finish(self, status: SpanStatus = SpanStatus.OK, error: Optional[str] = None):
        """Finish the span"""
        self.end_time = time.time()
        self.duration = self.end_time - self.start_time
        self.status = status
        if error:
            self.error = error
            self.status = SpanStatus.ERROR
    
    def add_event(self, name: str, attributes: Optional[Dict[str, Any]] = None):
        """Add an event to the span"""
        event = SpanEvent(
            timestamp=time.time(),
            name=name,
            attributes=attributes or {}
        )
        self.events.append(event)
    
    def set_tag(self, key: str, value: Any):
        """Set a tag on the span"""
        self.tags[key] = value
    
    def set_error(self, error: Exception):
        """Set error information on the span"""
        self.status = SpanStatus.ERROR
        self.error = str(error)
        self.tags['error'] = True
        self.tags['error.type'] = type(error).__name__
        self.tags['error.message'] = str(error)

@dataclass
class Trace:
    """Complete trace containing multiple spans"""
    trace_id: str
    spans: List[Span] = field(default_factory=list)
    start_time: float = field(default_factory=time.time)
    end_time: Optional[float] = None
    duration: Optional[float] = None
    root_span: Optional[Span] = None
    
    def add_span(self, span: Span):
        """Add a span to the trace"""
        self.spans.append(span)
        if span.parent_span_id is None:
            self.root_span = span
    
    def finish(self):
        """Finish the trace"""
        self.end_time = time.time()
        self.duration = self.end_time - self.start_time
    
    def get_critical_path(self) -> List[Span]:
        """Get the critical path (longest duration path) through the trace"""
        if not self.spans:
            return []
        
        # Build span hierarchy
        span_children = {}
        for span in self.spans:
            if span.parent_span_id:
                if span.parent_span_id not in span_children:
                    span_children[span.parent_span_id] = []
                span_children[span.parent_span_id].append(span)
        
        def get_path_duration(span: Span) -> float:
            """Get total duration of path starting from this span"""
            children = span_children.get(span.span_id, [])
            if not children:
                return span.duration or 0.0
            
            max_child_duration = max(get_path_duration(child) for child in children)
            return (span.duration or 0.0) + max_child_duration
        
        # Find critical path starting from root
        if not self.root_span:
            return []
        
        critical_path = [self.root_span]
        current_span = self.root_span
        
        while current_span.span_id in span_children:
            children = span_children[current_span.span_id]
            if not children:
                break
            
            # Find child with longest path
            longest_child = max(children, key=get_path_duration)
            critical_path.append(longest_child)
            current_span = longest_child
        
        return critical_path

class DistributedTracer:
    """
    Distributed tracing system for ASK pipeline
    
    Features:
    - Trace context propagation across async operations
    - Automatic span creation and management
    - Performance analysis and bottleneck identification
    - OpenTelemetry-compatible trace export
    - Request flow visualization
    """
    
    def __init__(self):
        # Active traces
        self.traces: Dict[str, Trace] = {}
        self.active_spans: Dict[str, Span] = {}
        
        # Thread-local storage for current span context
        self._local = threading.local()
        
        # Configuration
        self.sampling_rate = 1.0  # Sample 100% of traces
        self.max_traces = 1000  # Keep last 1000 traces
        self.trace_timeout = 300  # 5 minutes
        
        # Performance tracking
        self.trace_stats = {
            'total_traces': 0,
            'completed_traces': 0,
            'error_traces': 0,
            'avg_duration': 0.0,
            'max_duration': 0.0,
            'min_duration': float('inf')
        }
        
        # Thread safety
        self._lock = threading.RLock()
        
        # Start cleanup task
        self._cleanup_task = None
        self._start_cleanup_task()
    
    def _start_cleanup_task(self):
        """Start background task to clean up old traces"""
        def cleanup_traces():
            while True:
                try:
                    current_time = time.time()
                    with self._lock:
                        # Remove old traces
                        expired_traces = [
                            trace_id for trace_id, trace in self.traces.items()
                            if current_time - trace.start_time > self.trace_timeout
                        ]
                        
                        for trace_id in expired_traces:
                            del self.traces[trace_id]
                        
                        # Limit number of traces
                        if len(self.traces) > self.max_traces:
                            # Remove oldest traces
                            sorted_traces = sorted(
                                self.traces.items(),
                                key=lambda x: x[1].start_time
                            )
                            
                            to_remove = len(self.traces) - self.max_traces
                            for trace_id, _ in sorted_traces[:to_remove]:
                                del self.traces[trace_id]
                    
                    time.sleep(60)  # Cleanup every minute
                    
                except Exception:
                    time.sleep(60)  # Continue on error
        
        cleanup_thread = threading.Thread(target=cleanup_traces, daemon=True)
        cleanup_thread.start()
    
    def start_trace(self, operation_name: str, trace_id: Optional[str] = None) -> SpanContext:
        """Start a new trace"""
        trace_id = trace_id or self._generate_trace_id()
        span_id = self._generate_span_id()
        
        # Create root span
        span = Span(
            trace_id=trace_id,
            span_id=span_id,
            parent_span_id=None,
            operation_name=operation_name,
            start_time=time.time(),
            kind=SpanKind.SERVER
        )
        
        # Create trace
        trace = Trace(trace_id=trace_id)
        trace.add_span(span)
        
        with self._lock:
            self.traces[trace_id] = trace
            self.active_spans[span_id] = span
            self.trace_stats['total_traces'] += 1
        
        # Set current span context
        span_context = SpanContext(
            trace_id=trace_id,
            span_id=span_id
        )
        self._set_current_span_context(span_context)
        
        return span_context
    
    def start_span(
        self,
        operation_name: str,
        parent_context: Optional[SpanContext] = None,
        kind: SpanKind = SpanKind.INTERNAL,
        tags: Optional[Dict[str, Any]] = None
    ) -> SpanContext:
        """Start a new span"""
        parent_context = parent_context or self._get_current_span_context()
        
        if not parent_context:
            # No parent context, start new trace
            return self.start_trace(operation_name)
        
        span_id = self._generate_span_id()
        
        # Create span
        span = Span(
            trace_id=parent_context.trace_id,
            span_id=span_id,
            parent_span_id=parent_context.span_id,
            operation_name=operation_name,
            start_time=time.time(),
            kind=kind,
            tags=tags or {}
        )
        
        with self._lock:
            if parent_context.trace_id in self.traces:
                self.traces[parent_context.trace_id].add_span(span)
            self.active_spans[span_id] = span
        
        # Create new span context
        span_context = SpanContext(
            trace_id=parent_context.trace_id,
            span_id=span_id,
            parent_span_id=parent_context.span_id,
            baggage=parent_context.baggage.copy()
        )
        
        return span_context
    
    def finish_span(
        self,
        span_context: SpanContext,
        status: SpanStatus = SpanStatus.OK,
        error: Optional[str] = None
    ):
        """Finish a span"""
        with self._lock:
            if span_context.span_id in self.active_spans:
                span = self.active_spans[span_context.span_id]
                span.finish(status, error)
                del self.active_spans[span_context.span_id]
                
                # Check if this was the root span
                if span.parent_span_id is None and span_context.trace_id in self.traces:
                    trace = self.traces[span_context.trace_id]
                    trace.finish()
                    self._update_trace_stats(trace)
    
    def add_span_event(
        self,
        span_context: SpanContext,
        event_name: str,
        attributes: Optional[Dict[str, Any]] = None
    ):
        """Add an event to a span"""
        with self._lock:
            if span_context.span_id in self.active_spans:
                span = self.active_spans[span_context.span_id]
                span.add_event(event_name, attributes)
    
    def set_span_tag(
        self,
        span_context: SpanContext,
        key: str,
        value: Any
    ):
        """Set a tag on a span"""
        with self._lock:
            if span_context.span_id in self.active_spans:
                span = self.active_spans[span_context.span_id]
                span.set_tag(key, value)
    
    def set_span_error(
        self,
        span_context: SpanContext,
        error: Exception
    ):
        """Set error information on a span"""
        with self._lock:
            if span_context.span_id in self.active_spans:
                span = self.active_spans[span_context.span_id]
                span.set_error(error)
    
    @asynccontextmanager
    async def trace_async_operation(
        self,
        operation_name: str,
        parent_context: Optional[SpanContext] = None,
        kind: SpanKind = SpanKind.INTERNAL,
        tags: Optional[Dict[str, Any]] = None
    ):
        """Context manager for tracing async operations"""
        span_context = self.start_span(operation_name, parent_context, kind, tags)
        
        # Set as current context
        old_context = self._get_current_span_context()
        self._set_current_span_context(span_context)
        
        try:
            yield span_context
            self.finish_span(span_context, SpanStatus.OK)
        except Exception as e:
            self.set_span_error(span_context, e)
            self.finish_span(span_context, SpanStatus.ERROR, str(e))
            raise
        finally:
            # Restore previous context
            self._set_current_span_context(old_context)
    
    def _generate_trace_id(self) -> str:
        """Generate a unique trace ID"""
        return str(uuid.uuid4()).replace('-', '')[:16]
    
    def _generate_span_id(self) -> str:
        """Generate a unique span ID"""
        return str(uuid.uuid4()).replace('-', '')[:8]
    
    def _get_current_span_context(self) -> Optional[SpanContext]:
        """Get current span context from thread-local storage"""
        return getattr(self._local, 'span_context', None)
    
    def _set_current_span_context(self, context: Optional[SpanContext]):
        """Set current span context in thread-local storage"""
        self._local.span_context = context
    
    def _update_trace_stats(self, trace: Trace):
        """Update trace statistics"""
        if trace.duration is None:
            return
        
        self.trace_stats['completed_traces'] += 1
        
        # Update duration stats
        if trace.duration > self.trace_stats['max_duration']:
            self.trace_stats['max_duration'] = trace.duration
        
        if trace.duration < self.trace_stats['min_duration']:
            self.trace_stats['min_duration'] = trace.duration
        
        # Update average duration
        total_completed = self.trace_stats['completed_traces']
        current_avg = self.trace_stats['avg_duration']
        self.trace_stats['avg_duration'] = (
            (current_avg * (total_completed - 1) + trace.duration) / total_completed
        )
        
        # Check for errors
        if any(span.status == SpanStatus.ERROR for span in trace.spans):
            self.trace_stats['error_traces'] += 1
    
    def get_trace(self, trace_id: str) -> Optional[Trace]:
        """Get a trace by ID"""
        with self._lock:
            return self.traces.get(trace_id)
    
    def get_trace_summary(self, trace_id: str) -> Optional[Dict[str, Any]]:
        """Get a summary of a trace"""
        trace = self.get_trace(trace_id)
        if not trace:
            return None
        
        critical_path = trace.get_critical_path()
        
        return {
            'trace_id': trace.trace_id,
            'duration': trace.duration,
            'span_count': len(trace.spans),
            'error_count': sum(1 for span in trace.spans if span.status == SpanStatus.ERROR),
            'critical_path_duration': sum(span.duration or 0 for span in critical_path),
            'critical_path_operations': [span.operation_name for span in critical_path],
            'start_time': trace.start_time,
            'end_time': trace.end_time
        }
    
    def get_performance_insights(self) -> Dict[str, Any]:
        """Get performance insights from traces"""
        with self._lock:
            if not self.traces:
                return {'message': 'No traces available'}
            
            # Analyze operation performance
            operation_stats = {}
            for trace in self.traces.values():
                for span in trace.spans:
                    if span.duration is None:
                        continue
                    
                    op_name = span.operation_name
                    if op_name not in operation_stats:
                        operation_stats[op_name] = {
                            'count': 0,
                            'total_duration': 0.0,
                            'max_duration': 0.0,
                            'min_duration': float('inf'),
                            'error_count': 0
                        }
                    
                    stats = operation_stats[op_name]
                    stats['count'] += 1
                    stats['total_duration'] += span.duration
                    stats['max_duration'] = max(stats['max_duration'], span.duration)
                    stats['min_duration'] = min(stats['min_duration'], span.duration)
                    
                    if span.status == SpanStatus.ERROR:
                        stats['error_count'] += 1
            
            # Calculate averages and identify bottlenecks
            for op_name, stats in operation_stats.items():
                stats['avg_duration'] = stats['total_duration'] / stats['count']
                stats['error_rate'] = stats['error_count'] / stats['count']
            
            # Find slowest operations
            slowest_operations = sorted(
                operation_stats.items(),
                key=lambda x: x[1]['avg_duration'],
                reverse=True
            )[:5]
            
            return {
                'total_traces': len(self.traces),
                'trace_stats': self.trace_stats,
                'operation_stats': operation_stats,
                'slowest_operations': [
                    {
                        'operation': op_name,
                        'avg_duration': stats['avg_duration'],
                        'count': stats['count'],
                        'error_rate': stats['error_rate']
                    }
                    for op_name, stats in slowest_operations
                ],
                'recommendations': self._generate_recommendations(operation_stats)
            }
    
    def _generate_recommendations(self, operation_stats: Dict[str, Any]) -> List[str]:
        """Generate performance recommendations"""
        recommendations = []
        
        for op_name, stats in operation_stats.items():
            # High error rate
            if stats['error_rate'] > 0.1:
                recommendations.append(
                    f"High error rate ({stats['error_rate']:.1%}) in '{op_name}' - investigate error handling"
                )
            
            # Slow operations
            if stats['avg_duration'] > 5.0:
                recommendations.append(
                    f"Slow operation '{op_name}' (avg: {stats['avg_duration']:.2f}s) - consider optimization"
                )
            
            # High variance
            if stats['max_duration'] > stats['avg_duration'] * 3:
                recommendations.append(
                    f"High variance in '{op_name}' - investigate performance inconsistency"
                )
        
        return recommendations
    
    def export_traces_jaeger_format(self) -> List[Dict[str, Any]]:
        """Export traces in Jaeger-compatible format"""
        jaeger_traces = []
        
        with self._lock:
            for trace in self.traces.values():
                if not trace.spans:
                    continue
                
                jaeger_spans = []
                for span in trace.spans:
                    jaeger_span = {
                        'traceID': trace.trace_id,
                        'spanID': span.span_id,
                        'parentSpanID': span.parent_span_id,
                        'operationName': span.operation_name,
                        'startTime': int(span.start_time * 1_000_000),  # microseconds
                        'duration': int((span.duration or 0) * 1_000_000),  # microseconds
                        'tags': [
                            {'key': k, 'value': str(v), 'type': 'string'}
                            for k, v in span.tags.items()
                        ],
                        'process': {
                            'serviceName': 'ask-pipeline',
                            'tags': []
                        }
                    }
                    
                    if span.status == SpanStatus.ERROR:
                        jaeger_span['tags'].append({
                            'key': 'error',
                            'value': 'true',
                            'type': 'bool'
                        })
                    
                    jaeger_spans.append(jaeger_span)
                
                jaeger_traces.append({
                    'traceID': trace.trace_id,
                    'spans': jaeger_spans
                })
        
        return jaeger_traces

# Global tracer instance
_tracer: Optional[DistributedTracer] = None
_tracer_lock = threading.Lock()

def get_tracer() -> DistributedTracer:
    """Get or create the global tracer"""
    global _tracer
    
    with _tracer_lock:
        if _tracer is None:
            _tracer = DistributedTracer()
        return _tracer
