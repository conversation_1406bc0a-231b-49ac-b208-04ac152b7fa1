"""
Metrics Collection System for ASK Pipeline

Provides comprehensive metrics collection with:
- Request latency, throughput, and error rate metrics
- Business metrics (query types, user engagement, success rates)
- System resource metrics (CPU, memory, cache performance)
- Cost tracking metrics ($/1000 requests, API spend per user/guild)
- Prometheus compatibility
"""

import time
import threading
import psutil
import asyncio
from typing import Dict, Any, Optional, List, Callable, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from collections import defaultdict, deque
import json

class MetricType(Enum):
    """Types of metrics"""
    COUNTER = "counter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"
    SUMMARY = "summary"

class MetricCategory(Enum):
    """Categories for metric classification"""
    PERFORMANCE = "performance"
    BUSINESS = "business"
    SYSTEM = "system"
    COST = "cost"
    ERROR = "error"
    CACHE = "cache"
    TOOLS = "tools"

@dataclass
class MetricValue:
    """Individual metric value with metadata"""
    value: Union[int, float]
    timestamp: float
    labels: Dict[str, str] = field(default_factory=dict)
    
@dataclass
class HistogramBucket:
    """Histogram bucket for latency measurements"""
    upper_bound: float
    count: int = 0

@dataclass
class MetricDefinition:
    """Definition of a metric"""
    name: str
    metric_type: MetricType
    category: MetricCategory
    description: str
    unit: str = ""
    labels: List[str] = field(default_factory=list)

class MetricsCollector:
    """
    Comprehensive metrics collector with Prometheus compatibility
    
    Features:
    - Multiple metric types (counter, gauge, histogram, summary)
    - Automatic system resource monitoring
    - Cost tracking and budget alerting
    - Business metrics collection
    - Thread-safe operations
    - Export capabilities
    """
    
    def __init__(self):
        # Metric storage
        self.counters: Dict[str, float] = defaultdict(float)
        self.gauges: Dict[str, float] = {}
        self.histograms: Dict[str, List[float]] = defaultdict(list)
        self.summaries: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        
        # Metric definitions
        self.metric_definitions: Dict[str, MetricDefinition] = {}
        
        # Labels for metrics
        self.metric_labels: Dict[str, Dict[str, str]] = defaultdict(dict)
        
        # Thread safety
        self._lock = threading.RLock()
        
        # System monitoring
        self.system_monitoring_enabled = True
        self.system_metrics_interval = 30  # seconds
        self._system_monitor_task = None
        
        # Cost tracking
        self.cost_tracking = {
            'api_costs': defaultdict(float),
            'user_costs': defaultdict(float),
            'guild_costs': defaultdict(float),
            'daily_budget': 100.0,  # $100 daily budget
            'monthly_budget': 2000.0,  # $2000 monthly budget
            'cost_per_1000_requests': 0.0,
            'cache_savings': 0.0
        }
        
        # Business metrics
        self.business_metrics = {
            'query_types': defaultdict(int),
            'user_engagement': defaultdict(int),
            'success_rates': defaultdict(list),
            'response_times': defaultdict(list),
            'tool_usage': defaultdict(int),
            'error_patterns': defaultdict(int)
        }
        
        # Initialize standard metrics
        self._initialize_standard_metrics()
        
        # Start system monitoring
        if self.system_monitoring_enabled:
            self._start_system_monitoring()
    
    def _initialize_standard_metrics(self):
        """Initialize standard metrics definitions"""
        
        # Performance metrics
        self.define_metric(
            "request_duration_seconds",
            MetricType.HISTOGRAM,
            MetricCategory.PERFORMANCE,
            "Request duration in seconds",
            "seconds",
            ["stage", "success"]
        )
        
        self.define_metric(
            "requests_total",
            MetricType.COUNTER,
            MetricCategory.PERFORMANCE,
            "Total number of requests",
            "requests",
            ["method", "status"]
        )
        
        self.define_metric(
            "error_rate",
            MetricType.GAUGE,
            MetricCategory.ERROR,
            "Current error rate percentage",
            "percent"
        )
        
        # Business metrics
        self.define_metric(
            "query_types_total",
            MetricType.COUNTER,
            MetricCategory.BUSINESS,
            "Total queries by type",
            "queries",
            ["query_type", "intent"]
        )
        
        self.define_metric(
            "user_engagement_score",
            MetricType.GAUGE,
            MetricCategory.BUSINESS,
            "User engagement score",
            "score",
            ["user_id", "guild_id"]
        )
        
        # System metrics
        self.define_metric(
            "system_cpu_usage",
            MetricType.GAUGE,
            MetricCategory.SYSTEM,
            "System CPU usage percentage",
            "percent"
        )
        
        self.define_metric(
            "system_memory_usage",
            MetricType.GAUGE,
            MetricCategory.SYSTEM,
            "System memory usage percentage",
            "percent"
        )
        
        # Cache metrics
        self.define_metric(
            "cache_hit_rate",
            MetricType.GAUGE,
            MetricCategory.CACHE,
            "Cache hit rate percentage",
            "percent",
            ["cache_type"]
        )
        
        self.define_metric(
            "cache_operations_total",
            MetricType.COUNTER,
            MetricCategory.CACHE,
            "Total cache operations",
            "operations",
            ["operation", "result"]
        )
        
        # Cost metrics
        self.define_metric(
            "api_cost_dollars",
            MetricType.COUNTER,
            MetricCategory.COST,
            "API costs in dollars",
            "dollars",
            ["provider", "user_id", "guild_id"]
        )
        
        self.define_metric(
            "cost_per_1000_requests",
            MetricType.GAUGE,
            MetricCategory.COST,
            "Cost per 1000 requests in dollars",
            "dollars"
        )
        
        # Tool metrics
        self.define_metric(
            "tool_execution_duration",
            MetricType.HISTOGRAM,
            MetricCategory.TOOLS,
            "Tool execution duration in seconds",
            "seconds",
            ["tool_name", "success"]
        )
        
        self.define_metric(
            "tool_usage_total",
            MetricType.COUNTER,
            MetricCategory.TOOLS,
            "Total tool usage count",
            "executions",
            ["tool_name", "category"]
        )
    
    def define_metric(
        self,
        name: str,
        metric_type: MetricType,
        category: MetricCategory,
        description: str,
        unit: str = "",
        labels: Optional[List[str]] = None
    ):
        """Define a new metric"""
        with self._lock:
            self.metric_definitions[name] = MetricDefinition(
                name=name,
                metric_type=metric_type,
                category=category,
                description=description,
                unit=unit,
                labels=labels or []
            )
    
    def increment_counter(
        self,
        name: str,
        value: float = 1.0,
        labels: Optional[Dict[str, str]] = None
    ):
        """Increment a counter metric"""
        with self._lock:
            metric_key = self._get_metric_key(name, labels)
            self.counters[metric_key] += value
            
            if labels:
                self.metric_labels[metric_key] = labels
    
    def set_gauge(
        self,
        name: str,
        value: float,
        labels: Optional[Dict[str, str]] = None
    ):
        """Set a gauge metric value"""
        with self._lock:
            metric_key = self._get_metric_key(name, labels)
            self.gauges[metric_key] = value
            
            if labels:
                self.metric_labels[metric_key] = labels
    
    def observe_histogram(
        self,
        name: str,
        value: float,
        labels: Optional[Dict[str, str]] = None
    ):
        """Observe a value for histogram metric"""
        with self._lock:
            metric_key = self._get_metric_key(name, labels)
            self.histograms[metric_key].append(value)
            
            # Keep only last 10000 values to prevent memory issues
            if len(self.histograms[metric_key]) > 10000:
                self.histograms[metric_key] = self.histograms[metric_key][-5000:]
            
            if labels:
                self.metric_labels[metric_key] = labels
    
    def observe_summary(
        self,
        name: str,
        value: float,
        labels: Optional[Dict[str, str]] = None
    ):
        """Observe a value for summary metric"""
        with self._lock:
            metric_key = self._get_metric_key(name, labels)
            self.summaries[metric_key].append(value)
            
            if labels:
                self.metric_labels[metric_key] = labels
    
    def _get_metric_key(self, name: str, labels: Optional[Dict[str, str]]) -> str:
        """Generate metric key with labels"""
        if not labels:
            return name
        
        label_str = ",".join(f"{k}={v}" for k, v in sorted(labels.items()))
        return f"{name}{{{label_str}}}"
    
    def record_request_duration(
        self,
        duration: float,
        stage: str,
        success: bool = True
    ):
        """Record request duration"""
        self.observe_histogram(
            "request_duration_seconds",
            duration,
            {"stage": stage, "success": str(success).lower()}
        )
    
    def record_request(self, method: str = "ask", status: str = "success"):
        """Record a request"""
        self.increment_counter(
            "requests_total",
            1.0,
            {"method": method, "status": status}
        )
    
    def record_query_type(self, query_type: str, intent: str):
        """Record query type for business metrics"""
        self.increment_counter(
            "query_types_total",
            1.0,
            {"query_type": query_type, "intent": intent}
        )
        
        with self._lock:
            self.business_metrics['query_types'][query_type] += 1
    
    def record_user_engagement(self, user_id: str, guild_id: str, score: float):
        """Record user engagement score"""
        self.set_gauge(
            "user_engagement_score",
            score,
            {"user_id": user_id[:8], "guild_id": guild_id}  # Truncate for privacy
        )
        
        with self._lock:
            self.business_metrics['user_engagement'][user_id] = score
    
    def record_cache_operation(
        self,
        operation: str,
        result: str,
        cache_type: str = "unified"
    ):
        """Record cache operation"""
        self.increment_counter(
            "cache_operations_total",
            1.0,
            {"operation": operation, "result": result}
        )
        
        # Update cache hit rate
        if operation == "get":
            self._update_cache_hit_rate(cache_type, result == "hit")
    
    def _update_cache_hit_rate(self, cache_type: str, hit: bool):
        """Update cache hit rate calculation"""
        # This is a simplified calculation - in production you'd want a sliding window
        current_rate = self.gauges.get(f"cache_hit_rate{{cache_type={cache_type}}}", 0.0)
        
        # Simple exponential moving average
        alpha = 0.1
        new_rate = alpha * (100.0 if hit else 0.0) + (1 - alpha) * current_rate
        
        self.set_gauge("cache_hit_rate", new_rate, {"cache_type": cache_type})
    
    def record_api_cost(
        self,
        provider: str,
        cost: float,
        user_id: Optional[str] = None,
        guild_id: Optional[str] = None
    ):
        """Record API cost"""
        labels = {"provider": provider}
        if user_id:
            labels["user_id"] = user_id[:8]  # Truncate for privacy
        if guild_id:
            labels["guild_id"] = guild_id
        
        self.increment_counter("api_cost_dollars", cost, labels)
        
        with self._lock:
            self.cost_tracking['api_costs'][provider] += cost
            if user_id:
                self.cost_tracking['user_costs'][user_id] += cost
            if guild_id:
                self.cost_tracking['guild_costs'][guild_id] += cost
    
    def record_tool_execution(
        self,
        tool_name: str,
        duration: float,
        success: bool = True,
        category: str = "general"
    ):
        """Record tool execution metrics"""
        self.observe_histogram(
            "tool_execution_duration",
            duration,
            {"tool_name": tool_name, "success": str(success).lower()}
        )
        
        self.increment_counter(
            "tool_usage_total",
            1.0,
            {"tool_name": tool_name, "category": category}
        )
        
        with self._lock:
            self.business_metrics['tool_usage'][tool_name] += 1
    
    def _start_system_monitoring(self):
        """Start system resource monitoring"""
        def monitor_system():
            while self.system_monitoring_enabled:
                try:
                    # CPU usage
                    cpu_percent = psutil.cpu_percent(interval=1)
                    self.set_gauge("system_cpu_usage", cpu_percent)
                    
                    # Memory usage
                    memory = psutil.virtual_memory()
                    self.set_gauge("system_memory_usage", memory.percent)
                    
                    # Update cost per 1000 requests
                    self._update_cost_metrics()
                    
                    time.sleep(self.system_metrics_interval)
                    
                except Exception as e:
                    # Don't let monitoring errors break the system
                    time.sleep(self.system_metrics_interval)
        
        # Start monitoring in background thread
        monitor_thread = threading.Thread(target=monitor_system, daemon=True)
        monitor_thread.start()
    
    def _update_cost_metrics(self):
        """Update cost-related metrics"""
        with self._lock:
            total_requests = sum(self.counters.get(k, 0) for k in self.counters.keys() if "requests_total" in k)
            total_cost = sum(self.cost_tracking['api_costs'].values())
            
            if total_requests > 0:
                cost_per_1000 = (total_cost / total_requests) * 1000
                self.set_gauge("cost_per_1000_requests", cost_per_1000)
                self.cost_tracking['cost_per_1000_requests'] = cost_per_1000
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get comprehensive metrics summary"""
        with self._lock:
            return {
                'counters': dict(self.counters),
                'gauges': dict(self.gauges),
                'histogram_stats': self._get_histogram_stats(),
                'business_metrics': dict(self.business_metrics),
                'cost_tracking': dict(self.cost_tracking),
                'system_info': {
                    'cpu_count': psutil.cpu_count(),
                    'memory_total': psutil.virtual_memory().total,
                    'disk_usage': psutil.disk_usage('/').percent
                }
            }
    
    def _get_histogram_stats(self) -> Dict[str, Dict[str, float]]:
        """Calculate histogram statistics"""
        stats = {}
        for name, values in self.histograms.items():
            if values:
                sorted_values = sorted(values)
                n = len(sorted_values)
                stats[name] = {
                    'count': n,
                    'sum': sum(sorted_values),
                    'min': sorted_values[0],
                    'max': sorted_values[-1],
                    'mean': sum(sorted_values) / n,
                    'p50': sorted_values[int(n * 0.5)],
                    'p90': sorted_values[int(n * 0.9)],
                    'p95': sorted_values[int(n * 0.95)],
                    'p99': sorted_values[int(n * 0.99)]
                }
        return stats
    
    def export_prometheus_format(self) -> str:
        """Export metrics in Prometheus format"""
        lines = []
        
        # Add metric definitions as comments
        for name, definition in self.metric_definitions.items():
            lines.append(f"# HELP {name} {definition.description}")
            lines.append(f"# TYPE {name} {definition.metric_type.value}")
        
        # Export counters
        for metric_key, value in self.counters.items():
            lines.append(f"{metric_key} {value}")
        
        # Export gauges
        for metric_key, value in self.gauges.items():
            lines.append(f"{metric_key} {value}")
        
        # Export histogram summaries
        histogram_stats = self._get_histogram_stats()
        for metric_key, stats in histogram_stats.items():
            base_name = metric_key.split('{')[0]
            labels = metric_key[len(base_name):] if '{' in metric_key else ""
            
            for stat_name, stat_value in stats.items():
                if stat_name.startswith('p'):
                    # Percentile
                    quantile = float(stat_name[1:]) / 100
                    lines.append(f"{base_name}_quantile{{quantile=\"{quantile}\"{labels[1:] if labels else ''} {stat_value}")
                else:
                    lines.append(f"{base_name}_{stat_name}{labels} {stat_value}")
        
        return '\n'.join(lines)
    
    def reset_metrics(self):
        """Reset all metrics (useful for testing)"""
        with self._lock:
            self.counters.clear()
            self.gauges.clear()
            self.histograms.clear()
            self.summaries.clear()
            self.metric_labels.clear()
            self.business_metrics = {
                'query_types': defaultdict(int),
                'user_engagement': defaultdict(int),
                'success_rates': defaultdict(list),
                'response_times': defaultdict(list),
                'tool_usage': defaultdict(int),
                'error_patterns': defaultdict(int)
            }

# Global metrics collector instance
_metrics_collector: Optional[MetricsCollector] = None
_metrics_lock = threading.Lock()

def get_metrics_collector() -> MetricsCollector:
    """Get or create the global metrics collector"""
    global _metrics_collector
    
    with _metrics_lock:
        if _metrics_collector is None:
            _metrics_collector = MetricsCollector()
        return _metrics_collector
