"""
Structured Logging System for ASK Pipeline

Provides comprehensive JSON structured logging with:
- Correlation ID tracking across all pipeline stages
- Sensitive data redaction (API keys, user PII)
- Audit logging with compliance features
- Dynamic log level configuration
- GDPR/CCPA compliant data retention
"""

import json
import logging
import time
import uuid
import re
import hashlib
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, field, asdict
from datetime import datetime, timed<PERSON>ta
from enum import Enum
from pathlib import Path
import threading
from contextvars import ContextVar

# Context variable for correlation ID tracking
correlation_id_context: ContextVar[str] = ContextVar('correlation_id', default='')

class LogLevel(Enum):
    """Log levels with numeric values"""
    DEBUG = 10
    INFO = 20
    WARNING = 30
    ERROR = 40
    CRITICAL = 50
    AUDIT = 60  # Special level for audit logs

class LogCategory(Enum):
    """Categories for log classification"""
    PIPELINE = "pipeline"
    CACHE = "cache"
    TOOLS = "tools"
    ERROR = "error"
    SECURITY = "security"
    PERFORMANCE = "performance"
    AUDIT = "audit"
    USER_ACTION = "user_action"

@dataclass
class LogContext:
    """Context information for structured logging"""
    correlation_id: str = ""
    user_id: Optional[str] = None
    guild_id: Optional[str] = None
    channel_id: Optional[str] = None
    stage: Optional[str] = None
    component: Optional[str] = None
    operation: Optional[str] = None
    execution_time: Optional[float] = None
    additional_data: Dict[str, Any] = field(default_factory=dict)

@dataclass
class AuditLogEntry:
    """Audit log entry with compliance features"""
    timestamp: str
    correlation_id: str
    user_id_hash: str  # Hashed for privacy
    guild_id: Optional[str]
    action: str
    query_hash: str  # Hashed query for privacy
    query_length: int
    response_provided: bool
    tools_used: List[str]
    execution_time: float
    success: bool
    error_type: Optional[str] = None
    ip_address_hash: Optional[str] = None  # Hashed IP for privacy
    retention_until: str = ""  # GDPR retention date

class SensitiveDataRedactor:
    """Redacts sensitive data from logs"""
    
    def __init__(self):
        # Patterns for sensitive data detection
        self.patterns = {
            'api_key': re.compile(r'(?i)(api[_-]?key|token|secret)["\s]*[:=]["\s]*([a-zA-Z0-9_-]{20,})', re.IGNORECASE),
            'email': re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'),
            'discord_id': re.compile(r'\b\d{17,19}\b'),  # Discord snowflake IDs
            'ip_address': re.compile(r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b'),
            'credit_card': re.compile(r'\b(?:\d{4}[-\s]?){3}\d{4}\b'),
            'ssn': re.compile(r'\b\d{3}-\d{2}-\d{4}\b'),
            'phone': re.compile(r'\b\+?1?[-.\s]?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}\b')
        }
    
    def redact_message(self, message: str) -> str:
        """Redact sensitive data from log message"""
        redacted = message
        
        for pattern_name, pattern in self.patterns.items():
            if pattern_name == 'api_key':
                redacted = pattern.sub(r'\1: [REDACTED_API_KEY]', redacted)
            elif pattern_name == 'email':
                redacted = pattern.sub('[REDACTED_EMAIL]', redacted)
            elif pattern_name == 'discord_id':
                redacted = pattern.sub('[REDACTED_ID]', redacted)
            elif pattern_name == 'ip_address':
                redacted = pattern.sub('[REDACTED_IP]', redacted)
            else:
                redacted = pattern.sub(f'[REDACTED_{pattern_name.upper()}]', redacted)
        
        return redacted
    
    def redact_dict(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Redact sensitive data from dictionary"""
        if not isinstance(data, dict):
            return data
        
        redacted = {}
        sensitive_keys = {
            'api_key', 'token', 'secret', 'password', 'auth', 'authorization',
            'user_id', 'email', 'ip', 'credit_card', 'ssn', 'phone'
        }
        
        for key, value in data.items():
            key_lower = key.lower()
            
            if any(sensitive in key_lower for sensitive in sensitive_keys):
                if isinstance(value, str) and len(value) > 4:
                    redacted[key] = f"[REDACTED_{key.upper()}]"
                else:
                    redacted[key] = "[REDACTED]"
            elif isinstance(value, dict):
                redacted[key] = self.redact_dict(value)
            elif isinstance(value, str):
                redacted[key] = self.redact_message(value)
            else:
                redacted[key] = value
        
        return redacted

class StructuredLogger:
    """
    Structured logger with JSON output and comprehensive features
    
    Features:
    - JSON structured logging
    - Correlation ID tracking
    - Sensitive data redaction
    - Audit logging
    - Dynamic log level configuration
    - GDPR/CCPA compliance
    """
    
    def __init__(self, name: str, log_level: LogLevel = LogLevel.INFO):
        self.name = name
        self.log_level = log_level
        self.redactor = SensitiveDataRedactor()
        self._setup_logger()
        
        # Audit logging
        self.audit_enabled = True
        self.audit_retention_days = 90  # GDPR compliance
        
        # Thread safety
        self._lock = threading.Lock()
        
        # Performance tracking
        self.log_stats = {
            'total_logs': 0,
            'logs_by_level': {level.name: 0 for level in LogLevel},
            'audit_logs': 0,
            'redactions_performed': 0
        }
    
    def _setup_logger(self):
        """Setup the underlying Python logger"""
        self.logger = logging.getLogger(self.name)
        self.logger.setLevel(self.log_level.value)
        
        # Remove existing handlers to avoid duplicates
        for handler in self.logger.handlers[:]:
            self.logger.removeHandler(handler)
        
        # Create console handler with JSON formatter
        handler = logging.StreamHandler()
        handler.setLevel(self.log_level.value)
        
        # Custom JSON formatter
        formatter = logging.Formatter('%(message)s')
        handler.setFormatter(formatter)
        
        self.logger.addHandler(handler)
        self.logger.propagate = False
    
    def _create_log_entry(
        self,
        level: LogLevel,
        message: str,
        context: Optional[LogContext] = None,
        category: LogCategory = LogCategory.PIPELINE,
        extra_data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Create structured log entry"""
        
        # Get correlation ID from context or generate new one
        correlation_id = (
            context.correlation_id if context and context.correlation_id
            else correlation_id_context.get()
            or str(uuid.uuid4())[:8]
        )
        
        # Base log entry
        log_entry = {
            'timestamp': datetime.utcnow().isoformat() + 'Z',
            'level': level.name,
            'category': category.value,
            'logger': self.name,
            'correlation_id': correlation_id,
            'message': self.redactor.redact_message(message)
        }
        
        # Add context information
        if context:
            context_dict = asdict(context)
            # Remove empty values
            context_dict = {k: v for k, v in context_dict.items() if v is not None and v != ""}
            # Redact sensitive data
            context_dict = self.redactor.redact_dict(context_dict)
            log_entry['context'] = context_dict
        
        # Add extra data
        if extra_data:
            redacted_extra = self.redactor.redact_dict(extra_data)
            log_entry['extra'] = redacted_extra
        
        return log_entry
    
    def _log(
        self,
        level: LogLevel,
        message: str,
        context: Optional[LogContext] = None,
        category: LogCategory = LogCategory.PIPELINE,
        extra_data: Optional[Dict[str, Any]] = None
    ):
        """Internal logging method"""
        
        if level.value < self.log_level.value:
            return
        
        with self._lock:
            try:
                # Create structured log entry
                log_entry = self._create_log_entry(level, message, context, category, extra_data)
                
                # Convert to JSON
                json_message = json.dumps(log_entry, ensure_ascii=False, separators=(',', ':'))
                
                # Log using Python logger
                python_level = min(level.value, 50)  # Cap at CRITICAL
                self.logger.log(python_level, json_message)
                
                # Update statistics
                self.log_stats['total_logs'] += 1
                self.log_stats['logs_by_level'][level.name] += 1
                
                # Handle audit logging
                if category == LogCategory.AUDIT:
                    self._handle_audit_log(log_entry)
                
            except Exception as e:
                # Fallback logging to prevent log failures from breaking the application
                fallback_message = f"Logging error: {e}. Original message: {message[:100]}"
                self.logger.error(fallback_message)
    
    def debug(self, message: str, context: Optional[LogContext] = None, **kwargs):
        """Log debug message"""
        self._log(LogLevel.DEBUG, message, context, **kwargs)
    
    def info(self, message: str, context: Optional[LogContext] = None, **kwargs):
        """Log info message"""
        self._log(LogLevel.INFO, message, context, **kwargs)
    
    def warning(self, message: str, context: Optional[LogContext] = None, **kwargs):
        """Log warning message"""
        self._log(LogLevel.WARNING, message, context, **kwargs)
    
    def error(self, message: str, context: Optional[LogContext] = None, **kwargs):
        """Log error message"""
        self._log(LogLevel.ERROR, message, context, LogCategory.ERROR, **kwargs)
    
    def critical(self, message: str, context: Optional[LogContext] = None, **kwargs):
        """Log critical message"""
        self._log(LogLevel.CRITICAL, message, context, LogCategory.ERROR, **kwargs)
    
    def audit(
        self,
        action: str,
        user_id: Optional[str] = None,
        query: Optional[str] = None,
        success: bool = True,
        context: Optional[LogContext] = None,
        **kwargs
    ):
        """Log audit event with compliance features"""
        
        # Create audit log entry
        audit_entry = AuditLogEntry(
            timestamp=datetime.utcnow().isoformat() + 'Z',
            correlation_id=context.correlation_id if context else correlation_id_context.get() or str(uuid.uuid4())[:8],
            user_id_hash=self._hash_pii(user_id) if user_id else "",
            guild_id=context.guild_id if context else None,
            action=action,
            query_hash=self._hash_pii(query) if query else "",
            query_length=len(query) if query else 0,
            response_provided=success,
            tools_used=kwargs.get('tools_used', []),
            execution_time=kwargs.get('execution_time', 0.0),
            success=success,
            error_type=kwargs.get('error_type'),
            retention_until=(datetime.utcnow() + timedelta(days=self.audit_retention_days)).isoformat() + 'Z'
        )
        
        # Log as structured audit entry
        audit_message = f"Audit: {action} - User: {audit_entry.user_id_hash[:8]}... - Success: {success}"
        
        self._log(
            LogLevel.AUDIT,
            audit_message,
            context,
            LogCategory.AUDIT,
            extra_data={'audit_entry': asdict(audit_entry)}
        )
        
        self.log_stats['audit_logs'] += 1
    
    def _hash_pii(self, data: Optional[str]) -> str:
        """Hash PII data for privacy compliance"""
        if not data:
            return ""
        
        # Use SHA-256 with salt for privacy
        salt = "ask_pipeline_audit_salt_2024"
        return hashlib.sha256(f"{salt}{data}".encode()).hexdigest()
    
    def _handle_audit_log(self, log_entry: Dict[str, Any]):
        """Handle audit log storage and retention"""
        try:
            # Extract audit entry from log
            audit_data = log_entry.get('extra', {}).get('audit_entry', {})
            if not audit_data:
                return

            # Store audit log in secure location
            self._store_audit_log(audit_data)

            # Schedule retention cleanup if needed
            self._schedule_retention_cleanup()

        except Exception as e:
            # Log error without exposing sensitive data
            self.logger.error(f"Audit log handling failed: {str(e)[:100]}")

    def _store_audit_log(self, audit_data: Dict[str, Any]):
        """Store audit log in secure, tamper-proof storage"""
        try:
            # Create audit logs directory if it doesn't exist
            audit_dir = Path("logs/audit")
            audit_dir.mkdir(parents=True, exist_ok=True)

            # Create daily audit log file
            today = datetime.utcnow().strftime("%Y-%m-%d")
            audit_file = audit_dir / f"audit_{today}.jsonl"

            # Append audit entry to daily file
            with open(audit_file, 'a', encoding='utf-8') as f:
                json.dump(audit_data, f, ensure_ascii=False, separators=(',', ':'))
                f.write('\n')

            # Set restrictive permissions (owner read/write only)
            audit_file.chmod(0o600)

        except Exception as e:
            # Fallback to console logging if file storage fails
            self.logger.error(f"Audit storage failed: {e}")

    def _schedule_retention_cleanup(self):
        """Schedule automated cleanup of expired audit logs"""
        try:
            # Check if cleanup is needed (run once per day)
            cleanup_marker = Path("logs/audit/.last_cleanup")

            if cleanup_marker.exists():
                last_cleanup = datetime.fromtimestamp(cleanup_marker.stat().st_mtime)
                if (datetime.utcnow() - last_cleanup).days < 1:
                    return  # Cleanup already done today

            # Perform cleanup
            self._cleanup_expired_logs()

            # Update cleanup marker
            cleanup_marker.touch()

        except Exception as e:
            self.logger.error(f"Retention cleanup scheduling failed: {e}")

    def _cleanup_expired_logs(self):
        """Clean up expired audit logs based on retention policy"""
        try:
            audit_dir = Path("logs/audit")
            if not audit_dir.exists():
                return

            # Calculate cutoff date
            cutoff_date = datetime.utcnow() - timedelta(days=self.audit_retention_days)

            # Find and remove expired log files
            removed_count = 0
            for log_file in audit_dir.glob("audit_*.jsonl"):
                try:
                    # Extract date from filename
                    date_str = log_file.stem.replace("audit_", "")
                    file_date = datetime.strptime(date_str, "%Y-%m-%d")

                    if file_date < cutoff_date:
                        log_file.unlink()
                        removed_count += 1

                except (ValueError, OSError) as e:
                    # Skip files with invalid names or permission issues
                    continue

            if removed_count > 0:
                self.info(f"Cleaned up {removed_count} expired audit log files")

        except Exception as e:
            self.logger.error(f"Log cleanup failed: {e}")
    
    def set_log_level(self, level: LogLevel):
        """Dynamically adjust log level"""
        self.log_level = level
        self.logger.setLevel(level.value)
        for handler in self.logger.handlers:
            handler.setLevel(level.value)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get logging statistics"""
        return self.log_stats.copy()

# Global logger factory
_loggers: Dict[str, StructuredLogger] = {}
_logger_lock = threading.Lock()

def get_structured_logger(name: str, log_level: LogLevel = LogLevel.INFO) -> StructuredLogger:
    """Get or create a structured logger instance"""
    with _logger_lock:
        if name not in _loggers:
            _loggers[name] = StructuredLogger(name, log_level)
        return _loggers[name]

def set_correlation_id(correlation_id: str):
    """Set correlation ID in context"""
    correlation_id_context.set(correlation_id)

def get_correlation_id() -> str:
    """Get current correlation ID from context"""
    return correlation_id_context.get()

def clear_correlation_id():
    """Clear correlation ID from context"""
    correlation_id_context.set("")
