"""
Observability and Monitoring System for ASK Pipeline

This module provides comprehensive observability capabilities:

1. Structured Logging - JSON structured logging with correlation ID tracking,
   sensitive data redaction, and GDPR/CCPA compliant audit logging

2. Metrics Collection - Prometheus-compatible metrics for performance,
   business, system, and cost tracking with automatic export capabilities

3. Health Monitoring - Component health checks with automated recovery,
   readiness/liveness probes, and dependency monitoring

4. Distributed Tracing - Request flow tracking across async operations
   with OpenTelemetry integration (when implemented)

Features:
- Correlation ID tracking across all pipeline stages
- Sensitive data redaction (API keys, user PII)
- Cost tracking and budget alerting
- Business metrics (query types, user engagement)
- System resource monitoring
- Automated health recovery procedures
- Container orchestration ready (readiness/liveness probes)
"""

from .logger import (
    StructuredLogger,
    LogLevel,
    LogCategory,
    LogContext,
    AuditLogEntry,
    SensitiveDataRedactor,
    get_structured_logger,
    set_correlation_id,
    get_correlation_id,
    clear_correlation_id
)

from .metrics import (
    MetricsCollector,
    MetricType,
    MetricCategory,
    MetricValue,
    MetricDefinition,
    get_metrics_collector
)

from .health_checker import (
    HealthChecker,
    HealthStatus,
    ComponentType,
    HealthCheckResult,
    ComponentConfig,
    get_health_checker
)

from .tracer import (
    DistributedTracer,
    SpanKind,
    SpanStatus,
    SpanContext,
    Span,
    Trace,
    get_tracer
)

from .log_analyzer import (
    LogAggregator,
    LogAnalyzer,
    LogEntry,
    PerformanceMetrics,
    ErrorPattern,
    UserBehaviorMetrics,
    SecurityAlert,
    AnalysisTimeframe,
    AlertSeverity,
    analyze_logs,
    get_performance_metrics
)

__all__ = [
    # Structured Logging
    'StructuredLogger',
    'LogLevel',
    'LogCategory',
    'LogContext',
    'AuditLogEntry',
    'SensitiveDataRedactor',
    'get_structured_logger',
    'set_correlation_id',
    'get_correlation_id',
    'clear_correlation_id',
    
    # Metrics Collection
    'MetricsCollector',
    'MetricType',
    'MetricCategory',
    'MetricValue',
    'MetricDefinition',
    'get_metrics_collector',
    
    # Health Monitoring
    'HealthChecker',
    'HealthStatus',
    'ComponentType',
    'HealthCheckResult',
    'ComponentConfig',
    'get_health_checker',

    # Distributed Tracing
    'DistributedTracer',
    'SpanKind',
    'SpanStatus',
    'SpanContext',
    'Span',
    'Trace',
    'get_tracer',

    # Log Analysis
    'LogAggregator',
    'LogAnalyzer',
    'LogEntry',
    'PerformanceMetrics',
    'ErrorPattern',
    'UserBehaviorMetrics',
    'SecurityAlert',
    'AnalysisTimeframe',
    'AlertSeverity',
    'analyze_logs',
    'get_performance_metrics'
]
