"""
Health Monitoring System for ASK Pipeline

Provides comprehensive health monitoring with:
- Component health checks (Redis, AI services, external APIs)
- Readiness and liveness probes for container orchestration
- Automated health recovery procedures
- Dependency health tracking
- Health status aggregation
"""

import asyncio
import time
import threading
from typing import Dict, Any, Optional, List, Callable, Awaitable, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import aiohttp
import redis.asyncio as redis

class HealthStatus(Enum):
    """Health status levels"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"

class ComponentType(Enum):
    """Types of components to monitor"""
    DATABASE = "database"
    CACHE = "cache"
    AI_SERVICE = "ai_service"
    EXTERNAL_API = "external_api"
    INTERNAL_SERVICE = "internal_service"
    NETWORK = "network"

@dataclass
class HealthCheckResult:
    """Result of a health check"""
    component: str
    component_type: ComponentType
    status: HealthStatus
    response_time: float
    message: str
    timestamp: datetime
    details: Dict[str, Any] = field(default_factory=dict)
    error: Optional[str] = None

@dataclass
class ComponentConfig:
    """Configuration for a component health check"""
    name: str
    component_type: ComponentType
    check_function: Callable[[], Awaitable[HealthCheckResult]]
    check_interval: int = 30  # seconds
    timeout: int = 10  # seconds
    retry_count: int = 3
    critical: bool = True  # Whether this component is critical for system health
    recovery_function: Optional[Callable[[], Awaitable[bool]]] = None

class HealthChecker:
    """
    Comprehensive health monitoring system
    
    Features:
    - Configurable health checks for all components
    - Automatic retry and recovery mechanisms
    - Health status aggregation
    - Readiness and liveness probes
    - Performance monitoring
    - Alert generation
    """
    
    def __init__(self):
        # Component configurations
        self.components: Dict[str, ComponentConfig] = {}
        
        # Health check results
        self.health_results: Dict[str, HealthCheckResult] = {}
        
        # Monitoring state
        self.monitoring_active = False
        self.monitoring_tasks: Dict[str, asyncio.Task] = {}
        
        # Thread safety
        self._lock = threading.RLock()
        
        # Health statistics
        self.health_stats = {
            'total_checks': 0,
            'successful_checks': 0,
            'failed_checks': 0,
            'recovery_attempts': 0,
            'successful_recoveries': 0,
            'uptime_start': datetime.utcnow()
        }
        
        # System health thresholds
        self.health_thresholds = {
            'response_time_warning': 5.0,  # seconds
            'response_time_critical': 10.0,  # seconds
            'failure_rate_warning': 0.1,  # 10%
            'failure_rate_critical': 0.25  # 25%
        }
        
        # Initialize standard health checks
        self._initialize_standard_checks()
    
    def _initialize_standard_checks(self):
        """Initialize standard health checks"""
        
        # Redis cache health check
        self.register_component(
            ComponentConfig(
                name="redis_cache",
                component_type=ComponentType.CACHE,
                check_function=self._check_redis_health,
                check_interval=30,
                timeout=5,
                critical=False,  # Cache is not critical - we have fallbacks
                recovery_function=self._recover_redis
            )
        )
        
        # AI service health check
        self.register_component(
            ComponentConfig(
                name="ai_service",
                component_type=ComponentType.AI_SERVICE,
                check_function=self._check_ai_service_health,
                check_interval=60,
                timeout=15,
                critical=True,
                recovery_function=self._recover_ai_service
            )
        )
        
        # Alpha Vantage API health check
        self.register_component(
            ComponentConfig(
                name="alpha_vantage_api",
                component_type=ComponentType.EXTERNAL_API,
                check_function=self._check_alpha_vantage_health,
                check_interval=120,
                timeout=10,
                critical=False,  # External APIs are not critical
                recovery_function=None
            )
        )
        
        # Internal MCP server health check
        self.register_component(
            ComponentConfig(
                name="internal_mcp_server",
                component_type=ComponentType.INTERNAL_SERVICE,
                check_function=self._check_internal_mcp_health,
                check_interval=45,
                timeout=8,
                critical=True,
                recovery_function=self._recover_internal_mcp
            )
        )
    
    def register_component(self, config: ComponentConfig):
        """Register a component for health monitoring"""
        with self._lock:
            self.components[config.name] = config
    
    async def start_monitoring(self):
        """Start health monitoring for all components"""
        if self.monitoring_active:
            return
        
        self.monitoring_active = True
        
        # Start monitoring tasks for each component
        for name, config in self.components.items():
            task = asyncio.create_task(self._monitor_component(name, config))
            self.monitoring_tasks[name] = task
        
        print(f"✅ Health monitoring started for {len(self.components)} components")
    
    async def stop_monitoring(self):
        """Stop health monitoring"""
        self.monitoring_active = False
        
        # Cancel all monitoring tasks
        for task in self.monitoring_tasks.values():
            task.cancel()
        
        # Wait for tasks to complete
        if self.monitoring_tasks:
            await asyncio.gather(*self.monitoring_tasks.values(), return_exceptions=True)
        
        self.monitoring_tasks.clear()
        print("🛑 Health monitoring stopped")
    
    async def _monitor_component(self, name: str, config: ComponentConfig):
        """Monitor a single component continuously"""
        while self.monitoring_active:
            try:
                # Perform health check
                result = await self._perform_health_check(config)
                
                with self._lock:
                    self.health_results[name] = result
                    self.health_stats['total_checks'] += 1
                    
                    if result.status == HealthStatus.HEALTHY:
                        self.health_stats['successful_checks'] += 1
                    else:
                        self.health_stats['failed_checks'] += 1
                        
                        # Attempt recovery if configured and component is unhealthy
                        if (result.status == HealthStatus.UNHEALTHY and 
                            config.recovery_function and 
                            config.critical):
                            await self._attempt_recovery(name, config)
                
                # Wait for next check
                await asyncio.sleep(config.check_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                # Log error but continue monitoring
                error_result = HealthCheckResult(
                    component=name,
                    component_type=config.component_type,
                    status=HealthStatus.UNKNOWN,
                    response_time=0.0,
                    message=f"Monitoring error: {e}",
                    timestamp=datetime.utcnow(),
                    error=str(e)
                )
                
                with self._lock:
                    self.health_results[name] = error_result
                
                await asyncio.sleep(config.check_interval)
    
    async def _perform_health_check(self, config: ComponentConfig) -> HealthCheckResult:
        """Perform health check with timeout and retry"""
        last_error = None
        
        for attempt in range(config.retry_count):
            try:
                # Perform check with timeout
                start_time = time.time()
                result = await asyncio.wait_for(
                    config.check_function(),
                    timeout=config.timeout
                )
                result.response_time = time.time() - start_time
                return result
                
            except asyncio.TimeoutError:
                last_error = f"Health check timeout after {config.timeout}s"
                if attempt < config.retry_count - 1:
                    await asyncio.sleep(1)  # Brief delay before retry
            except Exception as e:
                last_error = str(e)
                if attempt < config.retry_count - 1:
                    await asyncio.sleep(1)
        
        # All retries failed
        return HealthCheckResult(
            component=config.name,
            component_type=config.component_type,
            status=HealthStatus.UNHEALTHY,
            response_time=config.timeout,
            message=f"Health check failed after {config.retry_count} attempts",
            timestamp=datetime.utcnow(),
            error=last_error
        )
    
    async def _attempt_recovery(self, name: str, config: ComponentConfig):
        """Attempt to recover a failed component"""
        if not config.recovery_function:
            return
        
        try:
            self.health_stats['recovery_attempts'] += 1
            
            print(f"🔄 Attempting recovery for component: {name}")
            success = await config.recovery_function()
            
            if success:
                self.health_stats['successful_recoveries'] += 1
                print(f"✅ Recovery successful for component: {name}")
            else:
                print(f"❌ Recovery failed for component: {name}")
                
        except Exception as e:
            print(f"❌ Recovery error for component {name}: {e}")
    
    # Standard health check implementations
    
    async def _check_redis_health(self) -> HealthCheckResult:
        """Check Redis cache health"""
        try:
            # Try to connect to Redis
            redis_client = redis.Redis.from_url("redis://localhost:6379", decode_responses=True)
            
            # Perform a simple ping
            await redis_client.ping()
            
            # Test set/get operation
            test_key = "health_check_test"
            test_value = str(time.time())
            await redis_client.set(test_key, test_value, ex=10)
            retrieved_value = await redis_client.get(test_key)
            
            await redis_client.close()
            
            if retrieved_value == test_value:
                return HealthCheckResult(
                    component="redis_cache",
                    component_type=ComponentType.CACHE,
                    status=HealthStatus.HEALTHY,
                    response_time=0.0,  # Will be set by caller
                    message="Redis is healthy and responsive",
                    timestamp=datetime.utcnow(),
                    details={"test_key": test_key, "test_passed": True}
                )
            else:
                return HealthCheckResult(
                    component="redis_cache",
                    component_type=ComponentType.CACHE,
                    status=HealthStatus.DEGRADED,
                    response_time=0.0,
                    message="Redis ping successful but set/get test failed",
                    timestamp=datetime.utcnow(),
                    details={"test_passed": False}
                )
                
        except Exception as e:
            return HealthCheckResult(
                component="redis_cache",
                component_type=ComponentType.CACHE,
                status=HealthStatus.UNHEALTHY,
                response_time=0.0,
                message="Redis connection failed",
                timestamp=datetime.utcnow(),
                error=str(e)
            )
    
    async def _check_ai_service_health(self) -> HealthCheckResult:
        """Check AI service health"""
        try:
            # This would check the actual AI service
            # For now, we'll simulate a health check
            
            # Simulate AI service check
            await asyncio.sleep(0.1)  # Simulate network delay
            
            return HealthCheckResult(
                component="ai_service",
                component_type=ComponentType.AI_SERVICE,
                status=HealthStatus.HEALTHY,
                response_time=0.0,
                message="AI service is healthy",
                timestamp=datetime.utcnow(),
                details={"service_type": "openrouter", "models_available": True}
            )
            
        except Exception as e:
            return HealthCheckResult(
                component="ai_service",
                component_type=ComponentType.AI_SERVICE,
                status=HealthStatus.UNHEALTHY,
                response_time=0.0,
                message="AI service check failed",
                timestamp=datetime.utcnow(),
                error=str(e)
            )
    
    async def _check_alpha_vantage_health(self) -> HealthCheckResult:
        """Check Alpha Vantage API health"""
        try:
            # Check if API key is configured
            import os
            api_key = os.getenv('ALPHA_VANTAGE_API_KEY')
            
            if not api_key:
                return HealthCheckResult(
                    component="alpha_vantage_api",
                    component_type=ComponentType.EXTERNAL_API,
                    status=HealthStatus.DEGRADED,
                    response_time=0.0,
                    message="Alpha Vantage API key not configured",
                    timestamp=datetime.utcnow(),
                    details={"configured": False}
                )
            
            # Test API endpoint
            async with aiohttp.ClientSession() as session:
                url = f"https://www.alphavantage.co/query?function=GLOBAL_QUOTE&symbol=AAPL&apikey={api_key}"
                async with session.get(url, timeout=aiohttp.ClientTimeout(total=8)) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        if "Global Quote" in data:
                            return HealthCheckResult(
                                component="alpha_vantage_api",
                                component_type=ComponentType.EXTERNAL_API,
                                status=HealthStatus.HEALTHY,
                                response_time=0.0,
                                message="Alpha Vantage API is healthy",
                                timestamp=datetime.utcnow(),
                                details={"configured": True, "response_valid": True}
                            )
                        else:
                            return HealthCheckResult(
                                component="alpha_vantage_api",
                                component_type=ComponentType.EXTERNAL_API,
                                status=HealthStatus.DEGRADED,
                                response_time=0.0,
                                message="Alpha Vantage API returned unexpected response",
                                timestamp=datetime.utcnow(),
                                details={"configured": True, "response_valid": False}
                            )
                    else:
                        return HealthCheckResult(
                            component="alpha_vantage_api",
                            component_type=ComponentType.EXTERNAL_API,
                            status=HealthStatus.UNHEALTHY,
                            response_time=0.0,
                            message=f"Alpha Vantage API returned status {response.status}",
                            timestamp=datetime.utcnow(),
                            error=f"HTTP {response.status}"
                        )
                        
        except Exception as e:
            return HealthCheckResult(
                component="alpha_vantage_api",
                component_type=ComponentType.EXTERNAL_API,
                status=HealthStatus.UNHEALTHY,
                response_time=0.0,
                message="Alpha Vantage API check failed",
                timestamp=datetime.utcnow(),
                error=str(e)
            )
    
    async def _check_internal_mcp_health(self) -> HealthCheckResult:
        """Check internal MCP server health"""
        try:
            # This would check the internal MCP server
            # For now, simulate the check
            
            await asyncio.sleep(0.05)  # Simulate check
            
            return HealthCheckResult(
                component="internal_mcp_server",
                component_type=ComponentType.INTERNAL_SERVICE,
                status=HealthStatus.HEALTHY,
                response_time=0.0,
                message="Internal MCP server is healthy",
                timestamp=datetime.utcnow(),
                details={"tools_available": 7, "server_responsive": True}
            )
            
        except Exception as e:
            return HealthCheckResult(
                component="internal_mcp_server",
                component_type=ComponentType.INTERNAL_SERVICE,
                status=HealthStatus.UNHEALTHY,
                response_time=0.0,
                message="Internal MCP server check failed",
                timestamp=datetime.utcnow(),
                error=str(e)
            )
    
    # Recovery functions
    
    async def _recover_redis(self) -> bool:
        """Attempt to recover Redis connection"""
        try:
            # In a real implementation, this might:
            # 1. Restart Redis service
            # 2. Clear connection pools
            # 3. Reinitialize Redis client
            
            await asyncio.sleep(1)  # Simulate recovery time
            return True  # Simulate successful recovery
            
        except Exception:
            return False
    
    async def _recover_ai_service(self) -> bool:
        """Attempt to recover AI service"""
        try:
            # In a real implementation, this might:
            # 1. Reinitialize AI client
            # 2. Switch to backup AI service
            # 3. Clear cached connections
            
            await asyncio.sleep(2)  # Simulate recovery time
            return True  # Simulate successful recovery
            
        except Exception:
            return False
    
    async def _recover_internal_mcp(self) -> bool:
        """Attempt to recover internal MCP server"""
        try:
            # In a real implementation, this might:
            # 1. Restart MCP server process
            # 2. Reinitialize MCP client
            # 3. Reload MCP tools
            
            await asyncio.sleep(3)  # Simulate recovery time
            return True  # Simulate successful recovery
            
        except Exception:
            return False
    
    # Health status queries
    
    def get_overall_health(self) -> HealthStatus:
        """Get overall system health status"""
        with self._lock:
            if not self.health_results:
                return HealthStatus.UNKNOWN
            
            critical_components = [
                name for name, config in self.components.items() 
                if config.critical
            ]
            
            # Check critical components
            critical_unhealthy = any(
                self.health_results.get(name, HealthCheckResult("", ComponentType.INTERNAL_SERVICE, HealthStatus.UNKNOWN, 0.0, "", datetime.utcnow())).status == HealthStatus.UNHEALTHY
                for name in critical_components
            )
            
            if critical_unhealthy:
                return HealthStatus.UNHEALTHY
            
            # Check for degraded components
            any_degraded = any(
                result.status == HealthStatus.DEGRADED
                for result in self.health_results.values()
            )
            
            if any_degraded:
                return HealthStatus.DEGRADED
            
            # Check if all components are healthy
            all_healthy = all(
                result.status == HealthStatus.HEALTHY
                for result in self.health_results.values()
            )
            
            return HealthStatus.HEALTHY if all_healthy else HealthStatus.UNKNOWN
    
    def is_ready(self) -> bool:
        """Check if system is ready to serve requests (readiness probe)"""
        overall_health = self.get_overall_health()
        return overall_health in [HealthStatus.HEALTHY, HealthStatus.DEGRADED]
    
    def is_alive(self) -> bool:
        """Check if system is alive (liveness probe)"""
        overall_health = self.get_overall_health()
        return overall_health != HealthStatus.UNHEALTHY
    
    def get_health_summary(self) -> Dict[str, Any]:
        """Get comprehensive health summary"""
        with self._lock:
            overall_health = self.get_overall_health()
            uptime = datetime.utcnow() - self.health_stats['uptime_start']
            
            component_statuses = {}
            for name, result in self.health_results.items():
                component_statuses[name] = {
                    'status': result.status.value,
                    'message': result.message,
                    'response_time': result.response_time,
                    'last_check': result.timestamp.isoformat(),
                    'component_type': result.component_type.value,
                    'critical': self.components[name].critical
                }
            
            return {
                'overall_status': overall_health.value,
                'ready': self.is_ready(),
                'alive': self.is_alive(),
                'uptime_seconds': uptime.total_seconds(),
                'components': component_statuses,
                'statistics': self.health_stats,
                'monitoring_active': self.monitoring_active
            }

# Global health checker instance
_health_checker: Optional[HealthChecker] = None
_health_lock = threading.Lock()

def get_health_checker() -> HealthChecker:
    """Get or create the global health checker"""
    global _health_checker
    
    with _health_lock:
        if _health_checker is None:
            _health_checker = HealthChecker()
        return _health_checker
