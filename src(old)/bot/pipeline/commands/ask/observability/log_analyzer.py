"""
Log Aggregation and Analysis Utilities for ASK Pipeline

Provides comprehensive log analysis capabilities:
- Log parsing and aggregation from multiple sources
- Performance metrics extraction
- Error pattern detection
- User behavior analysis
- Audit trail analysis
- Automated reporting and alerting
"""

import json
import re
import gzip
from typing import Dict, List, Any, Optional, Tuple, Iterator
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from pathlib import Path
from collections import defaultdict, Counter
import statistics
from enum import Enum

class AnalysisTimeframe(Enum):
    """Time frames for log analysis"""
    HOUR = "1h"
    DAY = "1d"
    WEEK = "7d"
    MONTH = "30d"

class AlertSeverity(Enum):
    """Alert severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class LogEntry:
    """Parsed log entry"""
    timestamp: datetime
    level: str
    category: str
    logger: str
    correlation_id: str
    message: str
    context: Dict[str, Any] = field(default_factory=dict)
    extra: Dict[str, Any] = field(default_factory=dict)

@dataclass
class PerformanceMetrics:
    """Performance analysis results"""
    avg_response_time: float
    p95_response_time: float
    p99_response_time: float
    total_requests: int
    error_rate: float
    throughput_per_hour: float

@dataclass
class ErrorPattern:
    """Detected error pattern"""
    error_type: str
    count: int
    first_seen: datetime
    last_seen: datetime
    correlation_ids: List[str]
    sample_messages: List[str]

@dataclass
class UserBehaviorMetrics:
    """User behavior analysis"""
    unique_users: int
    avg_queries_per_user: float
    most_active_users: List[Tuple[str, int]]
    query_patterns: Dict[str, int]
    peak_hours: List[int]

@dataclass
class SecurityAlert:
    """Security-related alert"""
    severity: AlertSeverity
    alert_type: str
    description: str
    affected_users: List[str]
    timestamp: datetime
    correlation_ids: List[str]

class LogAggregator:
    """
    Aggregates and parses logs from multiple sources
    
    Features:
    - Multi-format log parsing (JSON, plain text)
    - Real-time and batch processing
    - Compression support
    - Error handling and recovery
    """
    
    def __init__(self, log_directories: List[str] = None):
        self.log_directories = log_directories or ["logs", "logs/audit"]
        self.supported_formats = ['.log', '.jsonl', '.json', '.gz']
        
    def parse_log_file(self, file_path: Path) -> Iterator[LogEntry]:
        """Parse a single log file and yield log entries"""
        try:
            # Handle compressed files
            if file_path.suffix == '.gz':
                opener = gzip.open
                mode = 'rt'
            else:
                opener = open
                mode = 'r'
            
            with opener(file_path, mode, encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    try:
                        entry = self._parse_log_line(line.strip())
                        if entry:
                            yield entry
                    except Exception as e:
                        # Log parsing error but continue processing
                        print(f"Error parsing line {line_num} in {file_path}: {e}")
                        continue
                        
        except Exception as e:
            print(f"Error reading log file {file_path}: {e}")
    
    def _parse_log_line(self, line: str) -> Optional[LogEntry]:
        """Parse a single log line"""
        if not line.strip():
            return None
        
        try:
            # Try JSON parsing first (structured logs)
            data = json.loads(line)
            return LogEntry(
                timestamp=datetime.fromisoformat(data['timestamp'].replace('Z', '+00:00')),
                level=data.get('level', 'INFO'),
                category=data.get('category', 'unknown'),
                logger=data.get('logger', 'unknown'),
                correlation_id=data.get('correlation_id', ''),
                message=data.get('message', ''),
                context=data.get('context', {}),
                extra=data.get('extra', {})
            )
        except (json.JSONDecodeError, KeyError):
            # Fallback to plain text parsing
            return self._parse_plain_text_log(line)
    
    def _parse_plain_text_log(self, line: str) -> Optional[LogEntry]:
        """Parse plain text log line"""
        # Basic regex for common log formats
        patterns = [
            # ISO timestamp format
            r'(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d+)?(?:Z|[+-]\d{2}:\d{2})?)\s+(\w+)\s+(.+)',
            # Standard timestamp format
            r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\s+(\w+)\s+(.+)',
        ]
        
        for pattern in patterns:
            match = re.match(pattern, line)
            if match:
                try:
                    timestamp_str, level, message = match.groups()
                    # Parse timestamp
                    if 'T' in timestamp_str:
                        timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                    else:
                        timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
                    
                    return LogEntry(
                        timestamp=timestamp,
                        level=level,
                        category='unknown',
                        logger='unknown',
                        correlation_id='',
                        message=message
                    )
                except ValueError:
                    continue
        
        return None
    
    def aggregate_logs(
        self,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[LogEntry]:
        """Aggregate logs from all sources within time range"""
        
        if not start_time:
            start_time = datetime.utcnow() - timedelta(days=1)
        if not end_time:
            end_time = datetime.utcnow()
        
        all_entries = []
        
        for log_dir in self.log_directories:
            log_path = Path(log_dir)
            if not log_path.exists():
                continue
            
            # Find all log files
            for file_path in log_path.rglob('*'):
                if file_path.is_file() and any(file_path.name.endswith(fmt) for fmt in self.supported_formats):
                    for entry in self.parse_log_file(file_path):
                        # Apply time filter
                        if start_time <= entry.timestamp <= end_time:
                            # Apply additional filters
                            if self._matches_filters(entry, filters):
                                all_entries.append(entry)
        
        # Sort by timestamp
        all_entries.sort(key=lambda x: x.timestamp)
        return all_entries
    
    def _matches_filters(self, entry: LogEntry, filters: Optional[Dict[str, Any]]) -> bool:
        """Check if log entry matches the given filters"""
        if not filters:
            return True
        
        for key, value in filters.items():
            if key == 'level' and entry.level != value:
                return False
            elif key == 'category' and entry.category != value:
                return False
            elif key == 'logger' and entry.logger != value:
                return False
            elif key == 'correlation_id' and entry.correlation_id != value:
                return False
            elif key == 'message_contains' and value.lower() not in entry.message.lower():
                return False
        
        return True

class LogAnalyzer:
    """
    Analyzes aggregated logs for insights and patterns
    
    Features:
    - Performance metrics calculation
    - Error pattern detection
    - User behavior analysis
    - Security anomaly detection
    - Automated alerting
    """
    
    def __init__(self):
        self.aggregator = LogAggregator()
    
    def analyze_performance(
        self,
        entries: List[LogEntry],
        timeframe: AnalysisTimeframe = AnalysisTimeframe.DAY
    ) -> PerformanceMetrics:
        """Analyze performance metrics from log entries"""
        
        # Extract execution times from context
        execution_times = []
        total_requests = 0
        errors = 0
        
        for entry in entries:
            if entry.category in ['pipeline', 'user_action']:
                total_requests += 1
                
                # Check for errors
                if entry.level in ['ERROR', 'CRITICAL']:
                    errors += 1
                
                # Extract execution time
                exec_time = entry.context.get('execution_time')
                if exec_time and isinstance(exec_time, (int, float)):
                    execution_times.append(exec_time)
        
        # Calculate metrics
        if execution_times:
            avg_response_time = statistics.mean(execution_times)
            p95_response_time = self._percentile(execution_times, 95)
            p99_response_time = self._percentile(execution_times, 99)
        else:
            avg_response_time = p95_response_time = p99_response_time = 0.0
        
        error_rate = (errors / total_requests * 100) if total_requests > 0 else 0.0
        
        # Calculate throughput based on timeframe
        hours = self._timeframe_to_hours(timeframe)
        throughput_per_hour = total_requests / hours if hours > 0 else 0.0
        
        return PerformanceMetrics(
            avg_response_time=avg_response_time,
            p95_response_time=p95_response_time,
            p99_response_time=p99_response_time,
            total_requests=total_requests,
            error_rate=error_rate,
            throughput_per_hour=throughput_per_hour
        )
    
    def _percentile(self, data: List[float], percentile: int) -> float:
        """Calculate percentile of a dataset"""
        if not data:
            return 0.0
        sorted_data = sorted(data)
        index = int(len(sorted_data) * percentile / 100)
        return sorted_data[min(index, len(sorted_data) - 1)]
    
    def _timeframe_to_hours(self, timeframe: AnalysisTimeframe) -> float:
        """Convert timeframe enum to hours"""
        mapping = {
            AnalysisTimeframe.HOUR: 1.0,
            AnalysisTimeframe.DAY: 24.0,
            AnalysisTimeframe.WEEK: 168.0,
            AnalysisTimeframe.MONTH: 720.0
        }
        return mapping.get(timeframe, 24.0)

    def detect_error_patterns(self, entries: List[LogEntry]) -> List[ErrorPattern]:
        """Detect and analyze error patterns in logs"""
        error_groups = defaultdict(list)

        # Group errors by type and message pattern
        for entry in entries:
            if entry.level in ['ERROR', 'CRITICAL']:
                # Extract error type from message or context
                error_type = self._extract_error_type(entry)
                error_groups[error_type].append(entry)

        patterns = []
        for error_type, error_entries in error_groups.items():
            if len(error_entries) >= 2:  # Only report patterns with multiple occurrences
                correlation_ids = [e.correlation_id for e in error_entries if e.correlation_id]
                sample_messages = [e.message for e in error_entries[:5]]  # First 5 samples

                patterns.append(ErrorPattern(
                    error_type=error_type,
                    count=len(error_entries),
                    first_seen=min(e.timestamp for e in error_entries),
                    last_seen=max(e.timestamp for e in error_entries),
                    correlation_ids=correlation_ids,
                    sample_messages=sample_messages
                ))

        # Sort by count (most frequent first)
        patterns.sort(key=lambda x: x.count, reverse=True)
        return patterns

    def _extract_error_type(self, entry: LogEntry) -> str:
        """Extract error type from log entry"""
        # Check context for error type
        if 'error_type' in entry.context:
            return entry.context['error_type']

        # Extract from message using common patterns
        message = entry.message.lower()

        if 'timeout' in message:
            return 'timeout_error'
        elif 'connection' in message:
            return 'connection_error'
        elif 'permission' in message or 'unauthorized' in message:
            return 'permission_error'
        elif 'not found' in message or '404' in message:
            return 'not_found_error'
        elif 'rate limit' in message:
            return 'rate_limit_error'
        elif 'validation' in message:
            return 'validation_error'
        elif 'json' in message and 'parse' in message:
            return 'json_parse_error'
        else:
            return 'unknown_error'

    def analyze_user_behavior(self, entries: List[LogEntry]) -> UserBehaviorMetrics:
        """Analyze user behavior patterns from audit logs"""
        user_queries = defaultdict(int)
        query_patterns = Counter()
        hourly_activity = defaultdict(int)

        for entry in entries:
            if entry.category == 'audit' and 'audit_entry' in entry.extra:
                audit_data = entry.extra['audit_entry']

                # Count queries per user (using hashed user ID)
                user_id_hash = audit_data.get('user_id_hash', '')
                if user_id_hash:
                    user_queries[user_id_hash] += 1

                # Analyze query patterns
                query_length = audit_data.get('query_length', 0)
                if query_length > 0:
                    if query_length < 20:
                        query_patterns['short_query'] += 1
                    elif query_length < 100:
                        query_patterns['medium_query'] += 1
                    else:
                        query_patterns['long_query'] += 1

                # Track hourly activity
                hour = entry.timestamp.hour
                hourly_activity[hour] += 1

        # Calculate metrics
        unique_users = len(user_queries)
        avg_queries_per_user = statistics.mean(user_queries.values()) if user_queries else 0.0

        # Most active users (top 10)
        most_active_users = sorted(user_queries.items(), key=lambda x: x[1], reverse=True)[:10]

        # Peak hours (top 3 most active hours)
        peak_hours = sorted(hourly_activity.items(), key=lambda x: x[1], reverse=True)[:3]
        peak_hours = [hour for hour, _ in peak_hours]

        return UserBehaviorMetrics(
            unique_users=unique_users,
            avg_queries_per_user=avg_queries_per_user,
            most_active_users=most_active_users,
            query_patterns=dict(query_patterns),
            peak_hours=peak_hours
        )

    def detect_security_anomalies(self, entries: List[LogEntry]) -> List[SecurityAlert]:
        """Detect security anomalies and generate alerts"""
        alerts = []

        # Track suspicious patterns
        failed_attempts = defaultdict(int)
        unusual_activity = defaultdict(list)

        for entry in entries:
            # Check for authentication failures
            if 'unauthorized' in entry.message.lower() or 'permission denied' in entry.message.lower():
                user_id = entry.context.get('user_id', 'unknown')
                failed_attempts[user_id] += 1

            # Check for unusual query patterns
            if entry.category == 'audit' and 'audit_entry' in entry.extra:
                audit_data = entry.extra['audit_entry']
                query_length = audit_data.get('query_length', 0)

                # Flag unusually long queries as potential injection attempts
                if query_length > 1000:
                    user_id_hash = audit_data.get('user_id_hash', 'unknown')
                    unusual_activity[user_id_hash].append(entry)

        # Generate alerts for repeated failed attempts
        for user_id, count in failed_attempts.items():
            if count >= 5:  # Threshold for suspicious activity
                alerts.append(SecurityAlert(
                    severity=AlertSeverity.HIGH,
                    alert_type='repeated_auth_failures',
                    description=f'User {user_id} has {count} failed authentication attempts',
                    affected_users=[user_id],
                    timestamp=datetime.utcnow(),
                    correlation_ids=[]
                ))

        # Generate alerts for unusual query patterns
        for user_id_hash, suspicious_entries in unusual_activity.items():
            if len(suspicious_entries) >= 3:  # Multiple suspicious queries
                correlation_ids = [e.correlation_id for e in suspicious_entries if e.correlation_id]
                alerts.append(SecurityAlert(
                    severity=AlertSeverity.MEDIUM,
                    alert_type='unusual_query_pattern',
                    description=f'User {user_id_hash[:8]}... has {len(suspicious_entries)} unusually long queries',
                    affected_users=[user_id_hash],
                    timestamp=datetime.utcnow(),
                    correlation_ids=correlation_ids
                ))

        return alerts

    def generate_report(
        self,
        timeframe: AnalysisTimeframe = AnalysisTimeframe.DAY,
        include_security: bool = True
    ) -> Dict[str, Any]:
        """Generate comprehensive analysis report"""

        # Calculate time range
        end_time = datetime.utcnow()
        hours = self._timeframe_to_hours(timeframe)
        start_time = end_time - timedelta(hours=hours)

        # Aggregate logs
        entries = self.aggregator.aggregate_logs(start_time, end_time)

        # Perform analysis
        performance = self.analyze_performance(entries, timeframe)
        error_patterns = self.detect_error_patterns(entries)
        user_behavior = self.analyze_user_behavior(entries)

        report = {
            'timeframe': timeframe.value,
            'analysis_period': {
                'start': start_time.isoformat(),
                'end': end_time.isoformat()
            },
            'total_log_entries': len(entries),
            'performance_metrics': {
                'avg_response_time': performance.avg_response_time,
                'p95_response_time': performance.p95_response_time,
                'p99_response_time': performance.p99_response_time,
                'total_requests': performance.total_requests,
                'error_rate': performance.error_rate,
                'throughput_per_hour': performance.throughput_per_hour
            },
            'error_patterns': [
                {
                    'error_type': pattern.error_type,
                    'count': pattern.count,
                    'first_seen': pattern.first_seen.isoformat(),
                    'last_seen': pattern.last_seen.isoformat(),
                    'sample_messages': pattern.sample_messages[:3]  # Top 3 samples
                }
                for pattern in error_patterns[:10]  # Top 10 patterns
            ],
            'user_behavior': {
                'unique_users': user_behavior.unique_users,
                'avg_queries_per_user': user_behavior.avg_queries_per_user,
                'query_patterns': user_behavior.query_patterns,
                'peak_hours': user_behavior.peak_hours
            }
        }

        # Add security analysis if requested
        if include_security:
            security_alerts = self.detect_security_anomalies(entries)
            report['security_alerts'] = [
                {
                    'severity': alert.severity.value,
                    'alert_type': alert.alert_type,
                    'description': alert.description,
                    'affected_users_count': len(alert.affected_users),
                    'timestamp': alert.timestamp.isoformat()
                }
                for alert in security_alerts
            ]

        return report

# Utility functions for easy access
def analyze_logs(
    timeframe: AnalysisTimeframe = AnalysisTimeframe.DAY,
    include_security: bool = True
) -> Dict[str, Any]:
    """Quick function to analyze logs and generate report"""
    analyzer = LogAnalyzer()
    return analyzer.generate_report(timeframe, include_security)

def get_performance_metrics(timeframe: AnalysisTimeframe = AnalysisTimeframe.DAY) -> PerformanceMetrics:
    """Quick function to get performance metrics"""
    analyzer = LogAnalyzer()
    end_time = datetime.utcnow()
    hours = analyzer._timeframe_to_hours(timeframe)
    start_time = end_time - timedelta(hours=hours)

    entries = analyzer.aggregator.aggregate_logs(start_time, end_time)
    return analyzer.analyze_performance(entries, timeframe)
