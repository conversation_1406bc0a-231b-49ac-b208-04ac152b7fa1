"""
ASK Pipeline Configuration

Type-safe configuration management with validation and hot-reload capability.
"""

import os
import yaml
from typing import Dict, Any, Optional
from dataclasses import dataclass, field
from pathlib import Path

@dataclass
class IntentDetectionConfig:
    """Configuration for intent detection stage"""
    model: str = "fast_model"
    timeout: float = 10.0  # Increased for AI calls
    cache_ttl: int = 3
    confidence_threshold: float = 0.7

@dataclass
class ToolsConfig:
    """Configuration for MCP tools orchestration"""
    mcp_enabled: bool = True
    parallel_execution: bool = True
    timeout: float = 60.0  # Increased to handle satisfaction evaluation
    max_concurrent: int = 3
    rate_limit_per_minute: int = 60

@dataclass
class ResponseConfig:
    """Configuration for AI response generation"""
    model: str = "quality_model"
    max_tokens: int = 1000
    temperature: float = 0.3
    timeout: float = 5.0  # Reduced from 10.0

@dataclass
class CacheConfig:
    """Configuration for caching system"""
    enabled: bool = True
    ttl: int = 600
    max_size: int = 1000
    redis_url: Optional[str] = None

@dataclass
class MonitoringConfig:
    """Configuration for monitoring and logging"""
    log_level: str = "INFO"
    metrics_enabled: bool = True
    correlation_tracking: bool = True
    performance_tracking: bool = True

@dataclass
class AskConfig:
    """Main configuration for ASK pipeline"""
    intent_detection: IntentDetectionConfig = field(default_factory=IntentDetectionConfig)
    tools: ToolsConfig = field(default_factory=ToolsConfig)
    response: ResponseConfig = field(default_factory=ResponseConfig)
    cache: CacheConfig = field(default_factory=CacheConfig)
    monitoring: MonitoringConfig = field(default_factory=MonitoringConfig)
    
    # Performance targets
    max_response_time: float = 2.0
    target_success_rate: float = 0.99
    
    @classmethod
    def from_env(cls) -> 'AskConfig':
        """Create configuration from environment variables"""
        config = cls()
        
        # Intent detection
        config.intent_detection.timeout = float(os.getenv('ASK_INTENT_TIMEOUT', '2.0'))
        config.intent_detection.cache_ttl = int(os.getenv('ASK_INTENT_CACHE_TTL', '300'))
        
        # Tools
        config.tools.mcp_enabled = os.getenv('ASK_MCP_ENABLED', 'true').lower() == 'true'
        config.tools.timeout = float(os.getenv('ASK_TOOLS_TIMEOUT', '15.0'))
        config.tools.max_concurrent = int(os.getenv('ASK_MAX_CONCURRENT_TOOLS', '3'))
        
        # Response
        config.response.max_tokens = int(os.getenv('ASK_MAX_TOKENS', '1000'))
        config.response.temperature = float(os.getenv('ASK_TEMPERATURE', '0.3'))
        config.response.timeout = float(os.getenv('ASK_RESPONSE_TIMEOUT', '5.0'))
        
        # Cache
        config.cache.enabled = os.getenv('ASK_CACHE_ENABLED', 'true').lower() == 'true'
        config.cache.ttl = int(os.getenv('ASK_CACHE_TTL', '600'))
        config.cache.redis_url = os.getenv('REDIS_URL')
        
        # Monitoring
        config.monitoring.log_level = os.getenv('ASK_LOG_LEVEL', 'INFO')
        config.monitoring.metrics_enabled = os.getenv('ASK_METRICS_ENABLED', 'true').lower() == 'true'
        
        return config
    
    @classmethod
    def from_yaml(cls, config_path: str) -> 'AskConfig':
        """Create configuration from YAML file"""
        path = Path(config_path)
        if not path.exists():
            raise FileNotFoundError(f"Configuration file not found: {config_path}")
        
        with open(path, 'r') as f:
            data = yaml.safe_load(f)
        
        return cls.from_dict(data.get('ask_pipeline', {}))
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AskConfig':
        """Create configuration from dictionary"""
        config = cls()
        
        if 'intent_detection' in data:
            intent_data = data['intent_detection']
            config.intent_detection = IntentDetectionConfig(**intent_data)
        
        if 'tools' in data:
            tools_data = data['tools']
            config.tools = ToolsConfig(**tools_data)
        
        if 'response' in data:
            response_data = data['response']
            config.response = ResponseConfig(**response_data)
        
        if 'cache' in data:
            cache_data = data['cache']
            config.cache = CacheConfig(**cache_data)
        
        if 'monitoring' in data:
            monitoring_data = data['monitoring']
            config.monitoring = MonitoringConfig(**monitoring_data)
        
        return config
    
    def validate(self) -> bool:
        """Validate configuration values"""
        errors = []
        
        # Validate timeouts
        if self.intent_detection.timeout <= 0:
            errors.append("Intent detection timeout must be positive")
        
        if self.tools.timeout <= 0:
            errors.append("Tools timeout must be positive")
        
        if self.response.timeout <= 0:
            errors.append("Response timeout must be positive")
        
        # Validate concurrency
        if self.tools.max_concurrent <= 0:
            errors.append("Max concurrent tools must be positive")
        
        # Validate cache
        if self.cache.ttl <= 0:
            errors.append("Cache TTL must be positive")
        
        if self.cache.max_size <= 0:
            errors.append("Cache max size must be positive")
        
        # Validate performance targets
        if self.max_response_time <= 0:
            errors.append("Max response time must be positive")
        
        if not 0 < self.target_success_rate <= 1:
            errors.append("Target success rate must be between 0 and 1")
        
        if errors:
            raise ValueError(f"Configuration validation failed: {'; '.join(errors)}")
        
        return True

# Global configuration instance
_config: Optional[AskConfig] = None

def get_config() -> AskConfig:
    """Get the global configuration instance"""
    global _config
    if _config is None:
        # Try to load from environment first, then defaults
        _config = AskConfig.from_env()
        _config.validate()
    return _config

def set_config(config: AskConfig) -> None:
    """Set the global configuration instance"""
    global _config
    config.validate()
    _config = config

def reload_config(config_path: Optional[str] = None) -> AskConfig:
    """Reload configuration from file or environment"""
    global _config
    if config_path:
        _config = AskConfig.from_yaml(config_path)
    else:
        _config = AskConfig.from_env()
    _config.validate()
    return _config
