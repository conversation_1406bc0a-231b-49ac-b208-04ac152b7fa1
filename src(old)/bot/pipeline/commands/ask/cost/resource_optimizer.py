"""
Resource Optimization for ASK Pipeline

Provides comprehensive resource management and optimization:
- Monitor and optimize CPU, memory, and storage usage
- Implement resource scaling and auto-scaling
- Add resource efficiency metrics and reporting
- Create resource allocation strategies
- Implement resource usage prediction and planning
"""

import asyncio
import logging
import time
from typing import Dict, Any, Optional, List, Union, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import json
import psutil
import threading

logger = logging.getLogger(__name__)

class ResourceType(Enum):
    """Resource type enumeration"""
    CPU = "cpu"
    MEMORY = "memory"
    STORAGE = "storage"
    NETWORK = "network"
    DATABASE = "database"

class OptimizationStrategy(Enum):
    """Optimization strategy enumeration"""
    SCALE_UP = "scale_up"
    SCALE_DOWN = "scale_down"
    OPTIMIZE = "optimize"
    MIGRATE = "migrate"
    CACHE = "cache"

@dataclass
class ResourceMetrics:
    """Resource metrics data"""
    timestamp: datetime
    resource_type: ResourceType
    current_usage: float
    max_usage: float
    average_usage: float
    peak_usage: float
    utilization_percent: float
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class ResourceThreshold:
    """Resource threshold configuration"""
    resource_type: ResourceType
    warning_threshold: float
    critical_threshold: float
    scale_up_threshold: float
    scale_down_threshold: float
    enabled: bool = True

@dataclass
class OptimizationRecommendation:
    """Resource optimization recommendation"""
    resource_type: ResourceType
    strategy: OptimizationStrategy
    current_usage: float
    target_usage: float
    potential_savings: float
    implementation_effort: str  # low, medium, high
    priority: int  # 1-10
    description: str
    details: Dict[str, Any] = field(default_factory=dict)

@dataclass
class ScalingAction:
    """Resource scaling action"""
    action_id: str
    resource_type: ResourceType
    action_type: str  # scale_up, scale_down, optimize
    current_value: float
    target_value: float
    timestamp: datetime
    status: str  # pending, executing, completed, failed
    metadata: Dict[str, Any] = field(default_factory=dict)

class ResourceOptimizer:
    """Resource optimization system"""
    
    def __init__(self):
        self.resource_metrics: List[ResourceMetrics] = []
        self.thresholds: Dict[ResourceType, ResourceThreshold] = {}
        self.optimization_recommendations: List[OptimizationRecommendation] = []
        self.scaling_actions: List[ScalingAction] = []
        
        # Resource monitoring state
        self.monitoring_active = False
        self.monitoring_task: Optional[asyncio.Task] = None
        self.metrics_lock = threading.Lock()
        
        # Initialize default thresholds
        self._initialize_default_thresholds()
        
        # Start monitoring
        asyncio.create_task(self.start_monitoring())
    
    def _initialize_default_thresholds(self):
        """Initialize default resource thresholds"""
        default_thresholds = [
            ResourceThreshold(
                resource_type=ResourceType.CPU,
                warning_threshold=70.0,
                critical_threshold=90.0,
                scale_up_threshold=80.0,
                scale_down_threshold=30.0
            ),
            ResourceThreshold(
                resource_type=ResourceType.MEMORY,
                warning_threshold=80.0,
                critical_threshold=95.0,
                scale_up_threshold=85.0,
                scale_down_threshold=40.0
            ),
            ResourceThreshold(
                resource_type=ResourceType.STORAGE,
                warning_threshold=80.0,
                critical_threshold=95.0,
                scale_up_threshold=85.0,
                scale_down_threshold=50.0
            )
        ]
        
        for threshold in default_thresholds:
            self.thresholds[threshold.resource_type] = threshold
    
    async def start_monitoring(self):
        """Start resource monitoring"""
        if self.monitoring_active:
            return
        
        self.monitoring_active = True
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        logger.info("Resource monitoring started")
    
    async def stop_monitoring(self):
        """Stop resource monitoring"""
        self.monitoring_active = False
        if self.monitoring_task:
            self.monitoring_task.cancel()
            self.monitoring_task = None
        logger.info("Resource monitoring stopped")
    
    async def _monitoring_loop(self):
        """Resource monitoring loop"""
        while self.monitoring_active:
            try:
                await self._collect_resource_metrics()
                await self._analyze_resource_usage()
                await self._generate_optimization_recommendations()
                await self._execute_scaling_actions()
                await asyncio.sleep(30)  # Monitor every 30 seconds
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Resource monitoring error: {e}")
                await asyncio.sleep(60)
    
    async def _collect_resource_metrics(self):
        """Collect resource metrics"""
        with self.metrics_lock:
            # Collect CPU metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count()
            
            cpu_metrics = ResourceMetrics(
                timestamp=datetime.utcnow(),
                resource_type=ResourceType.CPU,
                current_usage=cpu_percent,
                max_usage=100.0,
                average_usage=self._calculate_average_usage(ResourceType.CPU),
                peak_usage=self._get_peak_usage(ResourceType.CPU),
                utilization_percent=cpu_percent,
                metadata={"cpu_count": cpu_count}
            )
            self.resource_metrics.append(cpu_metrics)
            
            # Collect memory metrics
            memory = psutil.virtual_memory()
            memory_metrics = ResourceMetrics(
                timestamp=datetime.utcnow(),
                resource_type=ResourceType.MEMORY,
                current_usage=memory.percent,
                max_usage=100.0,
                average_usage=self._calculate_average_usage(ResourceType.MEMORY),
                peak_usage=self._get_peak_usage(ResourceType.MEMORY),
                utilization_percent=memory.percent,
                metadata={
                    "total": memory.total,
                    "available": memory.available,
                    "used": memory.used,
                    "free": memory.free
                }
            )
            self.resource_metrics.append(memory_metrics)
            
            # Collect storage metrics
            disk = psutil.disk_usage('/')
            storage_metrics = ResourceMetrics(
                timestamp=datetime.utcnow(),
                resource_type=ResourceType.STORAGE,
                current_usage=(disk.used / disk.total) * 100,
                max_usage=100.0,
                average_usage=self._calculate_average_usage(ResourceType.STORAGE),
                peak_usage=self._get_peak_usage(ResourceType.STORAGE),
                utilization_percent=(disk.used / disk.total) * 100,
                metadata={
                    "total": disk.total,
                    "used": disk.used,
                    "free": disk.free
                }
            )
            self.resource_metrics.append(storage_metrics)
            
            # Keep only last 1000 metrics per resource type
            self._cleanup_old_metrics()
    
    def _calculate_average_usage(self, resource_type: ResourceType) -> float:
        """Calculate average usage for resource type"""
        recent_metrics = [
            m for m in self.resource_metrics 
            if m.resource_type == resource_type and 
            m.timestamp > datetime.utcnow() - timedelta(minutes=10)
        ]
        
        if not recent_metrics:
            return 0.0
        
        return sum(m.current_usage for m in recent_metrics) / len(recent_metrics)
    
    def _get_peak_usage(self, resource_type: ResourceType) -> float:
        """Get peak usage for resource type"""
        recent_metrics = [
            m for m in self.resource_metrics 
            if m.resource_type == resource_type and 
            m.timestamp > datetime.utcnow() - timedelta(hours=1)
        ]
        
        if not recent_metrics:
            return 0.0
        
        return max(m.current_usage for m in recent_metrics)
    
    def _cleanup_old_metrics(self):
        """Cleanup old metrics to prevent memory growth"""
        cutoff_time = datetime.utcnow() - timedelta(hours=24)
        self.resource_metrics = [
            m for m in self.resource_metrics 
            if m.timestamp > cutoff_time
        ]
    
    async def _analyze_resource_usage(self):
        """Analyze resource usage and detect issues"""
        for resource_type, threshold in self.thresholds.items():
            if not threshold.enabled:
                continue
            
            current_usage = self._get_current_usage(resource_type)
            
            if current_usage >= threshold.critical_threshold:
                await self._handle_critical_usage(resource_type, current_usage, threshold)
            elif current_usage >= threshold.warning_threshold:
                await self._handle_warning_usage(resource_type, current_usage, threshold)
            elif current_usage <= threshold.scale_down_threshold:
                await self._handle_low_usage(resource_type, current_usage, threshold)
    
    def _get_current_usage(self, resource_type: ResourceType) -> float:
        """Get current usage for resource type"""
        recent_metrics = [
            m for m in self.resource_metrics 
            if m.resource_type == resource_type and 
            m.timestamp > datetime.utcnow() - timedelta(minutes=5)
        ]
        
        if not recent_metrics:
            return 0.0
        
        return recent_metrics[-1].current_usage
    
    async def _handle_critical_usage(self, resource_type: ResourceType, 
                                   current_usage: float, threshold: ResourceThreshold):
        """Handle critical resource usage"""
        logger.critical(f"Critical {resource_type.value} usage: {current_usage:.1f}%")
        
        # Create scaling action
        action = ScalingAction(
            action_id=f"scale_up_{resource_type.value}_{int(time.time())}",
            resource_type=resource_type,
            action_type="scale_up",
            current_value=current_usage,
            target_value=threshold.scale_up_threshold - 10,  # Target 10% below threshold
            timestamp=datetime.utcnow(),
            status="pending",
            metadata={"reason": "critical_usage", "threshold": threshold.critical_threshold}
        )
        
        self.scaling_actions.append(action)
    
    async def _handle_warning_usage(self, resource_type: ResourceType, 
                                  current_usage: float, threshold: ResourceThreshold):
        """Handle warning resource usage"""
        logger.warning(f"Warning {resource_type.value} usage: {current_usage:.1f}%")
        
        # Create optimization recommendation
        recommendation = OptimizationRecommendation(
            resource_type=resource_type,
            strategy=OptimizationStrategy.OPTIMIZE,
            current_usage=current_usage,
            target_usage=threshold.warning_threshold - 10,
            potential_savings=current_usage - (threshold.warning_threshold - 10),
            implementation_effort="medium",
            priority=7,
            description=f"Optimize {resource_type.value} usage to prevent critical threshold",
            details={"threshold": threshold.warning_threshold}
        )
        
        self.optimization_recommendations.append(recommendation)
    
    async def _handle_low_usage(self, resource_type: ResourceType, 
                              current_usage: float, threshold: ResourceThreshold):
        """Handle low resource usage"""
        logger.info(f"Low {resource_type.value} usage: {current_usage:.1f}%")
        
        # Create scale down recommendation
        recommendation = OptimizationRecommendation(
            resource_type=resource_type,
            strategy=OptimizationStrategy.SCALE_DOWN,
            current_usage=current_usage,
            target_usage=threshold.scale_down_threshold + 10,
            potential_savings=current_usage - (threshold.scale_down_threshold + 10),
            implementation_effort="low",
            priority=5,
            description=f"Consider scaling down {resource_type.value} resources",
            details={"threshold": threshold.scale_down_threshold}
        )
        
        self.optimization_recommendations.append(recommendation)
    
    async def _generate_optimization_recommendations(self):
        """Generate optimization recommendations"""
        # Analyze resource patterns
        await self._analyze_cpu_patterns()
        await self._analyze_memory_patterns()
        await self._analyze_storage_patterns()
    
    async def _analyze_cpu_patterns(self):
        """Analyze CPU usage patterns"""
        cpu_metrics = [m for m in self.resource_metrics if m.resource_type == ResourceType.CPU]
        
        if len(cpu_metrics) < 10:
            return
        
        # Check for CPU spikes
        recent_metrics = cpu_metrics[-10:]
        avg_usage = sum(m.current_usage for m in recent_metrics) / len(recent_metrics)
        
        if avg_usage > 80:
            recommendation = OptimizationRecommendation(
                resource_type=ResourceType.CPU,
                strategy=OptimizationStrategy.SCALE_UP,
                current_usage=avg_usage,
                target_usage=70.0,
                potential_savings=0.0,  # No savings, need more resources
                implementation_effort="medium",
                priority=8,
                description="High CPU usage detected, consider scaling up",
                details={"average_usage": avg_usage, "pattern": "high_usage"}
            )
            self.optimization_recommendations.append(recommendation)
    
    async def _analyze_memory_patterns(self):
        """Analyze memory usage patterns"""
        memory_metrics = [m for m in self.resource_metrics if m.resource_type == ResourceType.MEMORY]
        
        if len(memory_metrics) < 10:
            return
        
        # Check for memory leaks
        recent_metrics = memory_metrics[-10:]
        usage_trend = self._calculate_trend([m.current_usage for m in recent_metrics])
        
        if usage_trend > 0.5:  # Increasing trend
            recommendation = OptimizationRecommendation(
                resource_type=ResourceType.MEMORY,
                strategy=OptimizationStrategy.OPTIMIZE,
                current_usage=recent_metrics[-1].current_usage,
                target_usage=recent_metrics[0].current_usage,
                potential_savings=recent_metrics[-1].current_usage - recent_metrics[0].current_usage,
                implementation_effort="high",
                priority=9,
                description="Memory usage increasing, possible memory leak",
                details={"trend": usage_trend, "pattern": "increasing"}
            )
            self.optimization_recommendations.append(recommendation)
    
    async def _analyze_storage_patterns(self):
        """Analyze storage usage patterns"""
        storage_metrics = [m for m in self.resource_metrics if m.resource_type == ResourceType.STORAGE]
        
        if len(storage_metrics) < 10:
            return
        
        # Check for storage growth
        recent_metrics = storage_metrics[-10:]
        usage_trend = self._calculate_trend([m.current_usage for m in recent_metrics])
        
        if usage_trend > 0.1:  # Growing trend
            recommendation = OptimizationRecommendation(
                resource_type=ResourceType.STORAGE,
                strategy=OptimizationStrategy.OPTIMIZE,
                current_usage=recent_metrics[-1].current_usage,
                target_usage=recent_metrics[0].current_usage,
                potential_savings=recent_metrics[-1].current_usage - recent_metrics[0].current_usage,
                implementation_effort="medium",
                priority=6,
                description="Storage usage growing, consider cleanup",
                details={"trend": usage_trend, "pattern": "growing"}
            )
            self.optimization_recommendations.append(recommendation)
    
    def _calculate_trend(self, values: List[float]) -> float:
        """Calculate trend in values (positive = increasing, negative = decreasing)"""
        if len(values) < 2:
            return 0.0
        
        # Simple linear trend calculation
        n = len(values)
        x = list(range(n))
        y = values
        
        x_mean = sum(x) / n
        y_mean = sum(y) / n
        
        numerator = sum((x[i] - x_mean) * (y[i] - y_mean) for i in range(n))
        denominator = sum((x[i] - x_mean) ** 2 for i in range(n))
        
        if denominator == 0:
            return 0.0
        
        return numerator / denominator
    
    async def _execute_scaling_actions(self):
        """Execute pending scaling actions"""
        pending_actions = [a for a in self.scaling_actions if a.status == "pending"]
        
        for action in pending_actions:
            try:
                await self._execute_scaling_action(action)
            except Exception as e:
                logger.error(f"Error executing scaling action {action.action_id}: {e}")
                action.status = "failed"
                action.metadata["error"] = str(e)
    
    async def _execute_scaling_action(self, action: ScalingAction):
        """Execute a single scaling action"""
        action.status = "executing"
        
        try:
            if action.action_type == "scale_up":
                await self._scale_up_resource(action)
            elif action.action_type == "scale_down":
                await self._scale_down_resource(action)
            elif action.action_type == "optimize":
                await self._optimize_resource(action)
            
            action.status = "completed"
            logger.info(f"Scaling action completed: {action.action_id}")
            
        except Exception as e:
            action.status = "failed"
            action.metadata["error"] = str(e)
            logger.error(f"Scaling action failed: {action.action_id} - {e}")
    
    async def _scale_up_resource(self, action: ScalingAction):
        """Scale up resource"""
        # This would integrate with actual scaling mechanisms
        logger.info(f"Scaling up {action.resource_type.value} from {action.current_value} to {action.target_value}")
        
        # Simulate scaling delay
        await asyncio.sleep(1)
    
    async def _scale_down_resource(self, action: ScalingAction):
        """Scale down resource"""
        # This would integrate with actual scaling mechanisms
        logger.info(f"Scaling down {action.resource_type.value} from {action.current_value} to {action.target_value}")
        
        # Simulate scaling delay
        await asyncio.sleep(1)
    
    async def _optimize_resource(self, action: ScalingAction):
        """Optimize resource usage"""
        # This would implement resource optimization
        logger.info(f"Optimizing {action.resource_type.value} usage")
        
        # Simulate optimization delay
        await asyncio.sleep(0.5)
    
    def get_resource_summary(self) -> Dict[str, Any]:
        """Get resource usage summary"""
        summary = {
            "timestamp": datetime.utcnow().isoformat(),
            "resources": {}
        }
        
        for resource_type in ResourceType:
            current_usage = self._get_current_usage(resource_type)
            threshold = self.thresholds.get(resource_type)
            
            summary["resources"][resource_type.value] = {
                "current_usage": current_usage,
                "thresholds": {
                    "warning": threshold.warning_threshold if threshold else None,
                    "critical": threshold.critical_threshold if threshold else None,
                    "scale_up": threshold.scale_up_threshold if threshold else None,
                    "scale_down": threshold.scale_down_threshold if threshold else None
                },
                "status": self._get_resource_status(resource_type, current_usage, threshold)
            }
        
        return summary
    
    def _get_resource_status(self, resource_type: ResourceType, 
                           current_usage: float, threshold: ResourceThreshold) -> str:
        """Get resource status"""
        if not threshold:
            return "unknown"
        
        if current_usage >= threshold.critical_threshold:
            return "critical"
        elif current_usage >= threshold.warning_threshold:
            return "warning"
        elif current_usage <= threshold.scale_down_threshold:
            return "low"
        else:
            return "normal"
    
    def get_optimization_recommendations(self) -> List[OptimizationRecommendation]:
        """Get optimization recommendations"""
        return sorted(self.optimization_recommendations, key=lambda x: x.priority, reverse=True)
    
    def get_scaling_actions(self, status: str = None) -> List[ScalingAction]:
        """Get scaling actions"""
        if status:
            return [a for a in self.scaling_actions if a.status == status]
        return self.scaling_actions
    
    def export_resource_report(self, output_file: str) -> bool:
        """Export resource report to JSON file"""
        try:
            report = {
                "timestamp": datetime.utcnow().isoformat(),
                "resource_summary": self.get_resource_summary(),
                "optimization_recommendations": [
                    {
                        "resource_type": rec.resource_type.value,
                        "strategy": rec.strategy.value,
                        "current_usage": rec.current_usage,
                        "target_usage": rec.target_usage,
                        "potential_savings": rec.potential_savings,
                        "implementation_effort": rec.implementation_effort,
                        "priority": rec.priority,
                        "description": rec.description,
                        "details": rec.details
                    }
                    for rec in self.optimization_recommendations
                ],
                "scaling_actions": [
                    {
                        "action_id": action.action_id,
                        "resource_type": action.resource_type.value,
                        "action_type": action.action_type,
                        "current_value": action.current_value,
                        "target_value": action.target_value,
                        "timestamp": action.timestamp.isoformat(),
                        "status": action.status,
                        "metadata": action.metadata
                    }
                    for action in self.scaling_actions
                ]
            }
            
            with open(output_file, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            
            logger.info(f"Resource report exported: {output_file}")
            return True
            
        except Exception as e:
            logger.error(f"Error exporting resource report: {e}")
            return False

# Global instance
_resource_optimizer: Optional[ResourceOptimizer] = None

def get_resource_optimizer() -> ResourceOptimizer:
    """Get global resource optimizer"""
    global _resource_optimizer
    if _resource_optimizer is None:
        _resource_optimizer = ResourceOptimizer()
    return _resource_optimizer

def cleanup_resource_optimizer():
    """Cleanup global resource optimizer"""
    global _resource_optimizer
    if _resource_optimizer:
        _resource_optimizer = None
