"""
ASK Pipeline - Simplified Architecture

This module provides a clean, maintainable, and performant ASK pipeline
that replaces the previous over-engineered system.

Key Features:
- Single-threaded linear flow
- Clear separation of concerns
- Minimal dependencies
- Async-first design
- < 2 second response time
- > 99% reliability
"""

from .pipeline import AskPipeline
from .executor import execute_ask_pipeline, format_response_for_discord
from .config import AskConfig

__all__ = [
    'AskPipeline',
    'execute_ask_pipeline',
    'format_response_for_discord',
    'AskConfig'
]

# Version info
__version__ = "2.0.0"
__author__ = "Trading Bot Team"
__description__ = "Simplified ASK Pipeline Architecture"
