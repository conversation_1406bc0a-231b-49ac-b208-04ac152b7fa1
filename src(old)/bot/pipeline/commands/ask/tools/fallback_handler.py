"""
Fallback Handler for ASK Pipeline

Provides graceful error handling and fallback responses when the main pipeline fails.
Implements a multi-tier fallback strategy for maximum reliability.
"""

import asyncio
from typing import Dict, Any, Optional
from dataclasses import dataclass
from datetime import datetime

from src.shared.error_handling.logging import get_logger

logger = get_logger(__name__)

@dataclass
class FallbackResponse:
    """Response from fallback handler"""
    text: str
    embed: Optional[Dict[str, Any]] = None
    fallback_level: str = "unknown"
    reasoning: str = ""

class FallbackHandler:
    """
    Multi-tier fallback system for ASK pipeline errors
    
    Fallback Chain:
    1. Primary: MCP Tools with real data
    2. Secondary: Local AI with cached data  
    3. Tertiary: Static educational responses
    4. Final: User-friendly error messages
    """
    
    def __init__(self):
        self.static_responses = {
            "general": [
                "I'm currently experiencing technical difficulties. Please try your question again in a moment.",
                "I'm having trouble processing your request right now. Could you please rephrase your question?",
                "There seems to be a temporary issue with my systems. Please try again shortly."
            ],
            "trading": [
                "I'm unable to access current market data at the moment. Please check your broker or financial news sources for the latest information.",
                "Market data services are temporarily unavailable. For current prices and analysis, please consult your trading platform.",
                "I can't retrieve live market data right now. Please refer to reliable financial sources for current market information."
            ],
            "educational": [
                "While I can't access current data, I'd recommend checking reputable financial education resources for trading concepts.",
                "For educational content about trading and markets, consider visiting financial education websites or consulting trading books.",
                "I'm unable to provide specific guidance right now, but there are many excellent trading education resources available online."
            ]
        }
    
    async def handle_error(
        self, 
        error: Exception, 
        query: str, 
        correlation_id: str
    ) -> FallbackResponse:
        """
        Handle pipeline errors with appropriate fallback response
        
        Args:
            error: The exception that occurred
            query: Original user query
            correlation_id: Request correlation ID
            
        Returns:
            FallbackResponse with appropriate message
        """
        logger.warning(f"Handling pipeline error", extra={
            'correlation_id': correlation_id,
            'error_type': type(error).__name__,
            'error_message': str(error)
        })
        
        # Determine query category for appropriate fallback
        query_lower = query.lower()
        category = self._categorize_query(query_lower)
        
        # Try different fallback levels
        try:
            # Level 3: Static educational responses
            response = await self._static_fallback(category, correlation_id)
            if response:
                return response
        except Exception as e:
            logger.error(f"Static fallback failed", extra={
                'correlation_id': correlation_id,
                'error': str(e)
            })
        
        # Level 4: Final error message
        return await self._final_fallback(error, correlation_id)
    
    def _categorize_query(self, query: str) -> str:
        """Categorize query for appropriate fallback response"""
        trading_keywords = ['price', 'stock', 'trade', 'buy', 'sell', 'market', 'chart', 'analysis']
        educational_keywords = ['learn', 'how', 'what', 'explain', 'tutorial', 'guide']
        
        if any(keyword in query for keyword in trading_keywords):
            return "trading"
        elif any(keyword in query for keyword in educational_keywords):
            return "educational"
        else:
            return "general"
    
    async def _static_fallback(self, category: str, correlation_id: str) -> FallbackResponse:
        """Provide static fallback response based on query category"""
        import random
        
        responses = self.static_responses.get(category, self.static_responses["general"])
        selected_response = random.choice(responses)
        
        # Add helpful disclaimer for trading queries
        if category == "trading":
            disclaimer = "\n\n⚠️ **Disclaimer**: This is not financial advice. Always do your own research and consult with qualified financial advisors before making investment decisions."
            selected_response += disclaimer
        
        logger.info(f"Using static fallback response", extra={
            'correlation_id': correlation_id,
            'category': category,
            'fallback_level': 'static'
        })
        
        return FallbackResponse(
            text=selected_response,
            fallback_level="static",
            reasoning=f"Static fallback for {category} query"
        )
    
    async def _final_fallback(self, error: Exception, correlation_id: str) -> FallbackResponse:
        """Final fallback when all else fails"""
        error_type = type(error).__name__
        
        # Provide user-friendly error messages based on error type
        if "timeout" in str(error).lower() or "TimeoutError" in error_type:
            message = "I'm taking longer than usual to respond. Please try your question again."
        elif "connection" in str(error).lower() or "ConnectionError" in error_type:
            message = "I'm having trouble connecting to my data sources. Please try again in a moment."
        elif "rate" in str(error).lower() or "limit" in str(error).lower():
            message = "I'm currently handling a lot of requests. Please wait a moment and try again."
        else:
            message = "I encountered an unexpected issue. Please try rephrasing your question or try again later."
        
        # Add helpful suggestions
        message += "\n\n💡 **Suggestions:**\n"
        message += "• Try rephrasing your question\n"
        message += "• Check if you're asking about a specific stock or market\n"
        message += "• Try again in a few moments\n"
        message += "• Contact support if the issue persists"
        
        logger.error(f"Using final fallback response", extra={
            'correlation_id': correlation_id,
            'error_type': error_type,
            'fallback_level': 'final'
        })
        
        return FallbackResponse(
            text=message,
            fallback_level="final",
            reasoning=f"Final fallback for {error_type}"
        )
    
    async def get_maintenance_response(self) -> FallbackResponse:
        """Get response for planned maintenance periods"""
        message = "🔧 **Maintenance Mode**\n\n"
        message += "I'm currently undergoing scheduled maintenance to improve my services. "
        message += "Please check back in a few minutes.\n\n"
        message += "Thank you for your patience!"
        
        return FallbackResponse(
            text=message,
            fallback_level="maintenance",
            reasoning="Planned maintenance period"
        )
    
    async def get_rate_limit_response(self) -> FallbackResponse:
        """Get response for rate limiting situations"""
        message = "⏱️ **Rate Limit Reached**\n\n"
        message += "You've made several requests recently. To ensure fair usage for all users, "
        message += "please wait a moment before asking another question.\n\n"
        message += "Thank you for your understanding!"
        
        return FallbackResponse(
            text=message,
            fallback_level="rate_limit",
            reasoning="Rate limit protection"
        )
