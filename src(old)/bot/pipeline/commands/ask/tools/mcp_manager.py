"""
Unified MCP Manager for ASK Pipeline

Consolidates all MCP client implementations and tool operations into a single,
focused component. Provides intelligent tool selection, performance monitoring,
and unified interface for all MCP operations.

This replaces multiple MCP client implementations with a unified approach.
"""

import asyncio
import time
from typing import Dict, Any, Optional, List, Set, Union
from dataclasses import dataclass, field
from enum import Enum

from src.shared.error_handling.logging import get_logger

logger = get_logger(__name__)

class ToolCategory(Enum):
    """Categories of available tools"""
    MARKET_DATA = "market_data"
    TECHNICAL_ANALYSIS = "technical_analysis"
    SENTIMENT_ANALYSIS = "sentiment_analysis"
    RISK_ASSESSMENT = "risk_assessment"
    GENERAL_SEARCH = "general_search"
    INTERNAL_TOOLS = "internal_tools"

@dataclass
class ToolMetrics:
    """Performance metrics for tool execution"""
    total_calls: int = 0
    successful_calls: int = 0
    failed_calls: int = 0
    average_response_time: float = 0.0
    last_used: float = 0.0
    error_rate: float = 0.0
    
    def update_success(self, response_time: float):
        """Update metrics for successful call"""
        self.total_calls += 1
        self.successful_calls += 1
        self.last_used = time.time()
        
        # Update average response time
        if self.average_response_time == 0.0:
            self.average_response_time = response_time
        else:
            self.average_response_time = (self.average_response_time + response_time) / 2
        
        self.error_rate = self.failed_calls / self.total_calls if self.total_calls > 0 else 0.0
    
    def update_failure(self):
        """Update metrics for failed call"""
        self.total_calls += 1
        self.failed_calls += 1
        self.last_used = time.time()
        self.error_rate = self.failed_calls / self.total_calls if self.total_calls > 0 else 0.0

@dataclass
class ToolInfo:
    """Information about an available tool"""
    name: str
    description: str
    category: ToolCategory
    client_type: str  # 'trading', 'alpha_vantage', 'internal', 'free'
    parameters: Dict[str, Any] = field(default_factory=dict)
    requires_auth: bool = False
    rate_limited: bool = False
    priority: int = 1  # 1=high, 2=medium, 3=low
    metrics: ToolMetrics = field(default_factory=ToolMetrics)

@dataclass
class ToolExecutionResult:
    """Result of tool execution"""
    success: bool
    data: Optional[Dict[str, Any]]
    error: Optional[str]
    execution_time: float
    tool_name: str
    client_type: str

class MCPManager:
    """
    Unified MCP Manager for all tool operations
    
    Features:
    - Consolidated MCP client management
    - Intelligent tool selection based on query analysis
    - Performance monitoring and optimization
    - Automatic fallback strategies
    - Rate limiting and error handling
    """
    
    def __init__(self):
        # MCP clients
        self.trading_mcp_client = None
        self.alpha_vantage_client = None
        self.internal_tools_client = None
        self.free_mcp_clients = {}
        
        # Tool registry
        self.available_tools: Dict[str, ToolInfo] = {}
        self.tools_by_category: Dict[ToolCategory, List[str]] = {
            category: [] for category in ToolCategory
        }
        
        # Performance tracking
        self.global_metrics = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'average_response_time': 0.0,
            'tools_initialized': 0,
            'initialization_time': 0.0
        }
        
        # Initialization state
        self.initialization_status = {
            'trading_mcp': False,
            'alpha_vantage': False,
            'internal_tools': False,
            'free_mcp': False
        }
        
        logger.info("✅ Unified MCP manager initialized")

    async def initialize_all_clients(self) -> Dict[str, bool]:
        """Initialize all MCP clients and return status"""
        start_time = time.time()
        
        initialization_tasks = [
            self._initialize_trading_mcp(),
            self._initialize_alpha_vantage(),
            self._initialize_internal_tools(),
            self._initialize_free_mcp_tools()
        ]
        
        # Run all initializations in parallel
        results = await asyncio.gather(*initialization_tasks, return_exceptions=True)
        
        # Process results
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.warning(f"MCP client {i} initialization failed: {result}")
        
        # Update global metrics
        self.global_metrics['initialization_time'] = time.time() - start_time
        self.global_metrics['tools_initialized'] = len(self.available_tools)
        
        logger.info(f"✅ MCP manager initialization complete: {len(self.available_tools)} tools available")
        return self.initialization_status.copy()

    async def _initialize_trading_mcp(self) -> bool:
        """Initialize trading MCP client"""
        try:
            from src.mcp_server.mcp_client_integration import get_mcp_client
            
            self.trading_mcp_client = await get_mcp_client()
            
            # Register trading tools
            trading_tools = [
                ("get_stock_price", "Get real-time stock price", ToolCategory.MARKET_DATA, 1),
                ("get_technical_analysis", "Perform technical analysis", ToolCategory.TECHNICAL_ANALYSIS, 1),
                ("analyze_market_sentiment", "Analyze market sentiment", ToolCategory.SENTIMENT_ANALYSIS, 2),
                ("detect_trading_intent", "Detect trading intent from query", ToolCategory.INTERNAL_TOOLS, 1),
                ("get_options_data", "Get options chain data", ToolCategory.MARKET_DATA, 2),
                ("calculate_risk_metrics", "Calculate risk metrics", ToolCategory.RISK_ASSESSMENT, 2)
            ]
            
            for name, desc, category, priority in trading_tools:
                self._register_tool(name, desc, category, "trading", priority=priority)
            
            self.initialization_status['trading_mcp'] = True
            logger.info(f"✅ Trading MCP client initialized with {len(trading_tools)} tools")
            return True
            
        except Exception as e:
            logger.warning(f"⚠️ Trading MCP client initialization failed: {e}")
            self.initialization_status['trading_mcp'] = False
            return False

    async def _initialize_alpha_vantage(self) -> bool:
        """Initialize Alpha Vantage MCP client"""
        try:
            from src.shared.data_providers.alpha_vantage_mcp import AlphaVantageMCPClient
            
            self.alpha_vantage_client = AlphaVantageMCPClient()
            
            if not self.alpha_vantage_client.is_configured:
                logger.warning("Alpha Vantage MCP client not configured - API key missing")
                return False
            
            # Register Alpha Vantage tools
            av_tools = [
                ("get_global_quote", "Get real-time stock quote", ToolCategory.MARKET_DATA, 1),
                ("get_intraday_data", "Get intraday price data", ToolCategory.MARKET_DATA, 2),
                ("get_daily_data", "Get daily price data", ToolCategory.MARKET_DATA, 2),
                ("get_company_overview", "Get company fundamental data", ToolCategory.MARKET_DATA, 2),
                ("get_rsi", "Get RSI technical indicator", ToolCategory.TECHNICAL_ANALYSIS, 2),
                ("get_macd", "Get MACD technical indicator", ToolCategory.TECHNICAL_ANALYSIS, 2),
                ("get_bbands", "Get Bollinger Bands", ToolCategory.TECHNICAL_ANALYSIS, 2),
                ("get_sma", "Get Simple Moving Average", ToolCategory.TECHNICAL_ANALYSIS, 3),
                ("get_ema", "Get Exponential Moving Average", ToolCategory.TECHNICAL_ANALYSIS, 3)
            ]
            
            for name, desc, category, priority in av_tools:
                self._register_tool(name, desc, category, "alpha_vantage", 
                                  priority=priority, rate_limited=True, requires_auth=True)
            
            self.initialization_status['alpha_vantage'] = True
            logger.info(f"✅ Alpha Vantage MCP client initialized with {len(av_tools)} tools")
            return True
            
        except Exception as e:
            logger.warning(f"⚠️ Alpha Vantage MCP client initialization failed: {e}")
            self.initialization_status['alpha_vantage'] = False
            return False

    async def _initialize_internal_tools(self) -> bool:
        """Initialize internal tools MCP client"""
        try:
            from src.mcp_server.internal_tools_client import get_internal_tools_client
            
            self.internal_tools_client = await get_internal_tools_client()
            
            # Register internal tools
            internal_tools = [
                ("analyze_trading_intent", "Analyze trading intent from text", ToolCategory.INTERNAL_TOOLS, 1),
                ("get_market_data", "Get consolidated market data", ToolCategory.MARKET_DATA, 1),
                ("calculate_technical_indicators", "Calculate technical indicators", ToolCategory.TECHNICAL_ANALYSIS, 1),
                ("synthesize_trading_response", "Synthesize AI trading response", ToolCategory.INTERNAL_TOOLS, 1),
                ("format_for_discord", "Format response for Discord", ToolCategory.INTERNAL_TOOLS, 1),
                ("cache_operation", "Perform cache operations", ToolCategory.INTERNAL_TOOLS, 3),
                ("format_financial_data", "Format financial data", ToolCategory.INTERNAL_TOOLS, 2)
            ]
            
            for name, desc, category, priority in internal_tools:
                self._register_tool(name, desc, category, "internal", priority=priority)
            
            self.initialization_status['internal_tools'] = True
            logger.info(f"✅ Internal tools MCP client initialized with {len(internal_tools)} tools")
            return True
            
        except Exception as e:
            logger.warning(f"⚠️ Internal tools MCP client initialization failed: {e}")
            self.initialization_status['internal_tools'] = False
            return False

    async def _initialize_free_mcp_tools(self) -> bool:
        """Initialize free MCP tools"""
        try:
            # Note: This would initialize free MCP servers when they're properly installed
            # For now, we'll register placeholder tools that can be activated later
            
            free_tools = [
                ("brave_search", "Search the web using Brave", ToolCategory.GENERAL_SEARCH, 2),
                ("web_fetch", "Fetch content from URLs", ToolCategory.GENERAL_SEARCH, 3),
                ("memory_operations", "Store and retrieve memories", ToolCategory.INTERNAL_TOOLS, 3),
                ("filesystem_operations", "File system operations", ToolCategory.INTERNAL_TOOLS, 3)
            ]
            
            for name, desc, category, priority in free_tools:
                self._register_tool(name, desc, category, "free", priority=priority, requires_auth=False)
            
            self.initialization_status['free_mcp'] = True
            logger.info(f"📋 Free MCP tools registered: {len(free_tools)} tools (installation required)")
            return True
            
        except Exception as e:
            logger.warning(f"⚠️ Free MCP tools initialization failed: {e}")
            self.initialization_status['free_mcp'] = False
            return False

    def _register_tool(
        self, 
        name: str, 
        description: str, 
        category: ToolCategory, 
        client_type: str,
        priority: int = 2,
        requires_auth: bool = False,
        rate_limited: bool = False,
        parameters: Optional[Dict[str, Any]] = None
    ):
        """Register a tool in the unified registry"""
        tool_info = ToolInfo(
            name=name,
            description=description,
            category=category,
            client_type=client_type,
            parameters=parameters or {},
            requires_auth=requires_auth,
            rate_limited=rate_limited,
            priority=priority
        )
        
        self.available_tools[name] = tool_info
        self.tools_by_category[category].append(name)

    def get_tools_for_query(self, query: str, max_tools: int = 5) -> List[str]:
        """
        Get optimal tools for a query using intelligent selection
        
        Args:
            query: User query to analyze
            max_tools: Maximum number of tools to return
            
        Returns:
            List of tool names optimized for the query
        """
        try:
            query_lower = query.lower()
            selected_tools = []
            
            # Analyze query for tool categories
            category_scores = self._analyze_query_categories(query_lower)
            
            # Sort categories by relevance
            sorted_categories = sorted(category_scores.items(), key=lambda x: x[1], reverse=True)
            
            # Select tools from most relevant categories
            for category, score in sorted_categories:
                if len(selected_tools) >= max_tools:
                    break
                
                if score > 0:
                    category_tools = self._get_best_tools_for_category(category, query_lower)
                    for tool in category_tools:
                        if len(selected_tools) >= max_tools:
                            break
                        if tool not in selected_tools:
                            selected_tools.append(tool)
            
            # If no tools selected, use default high-priority tools
            if not selected_tools:
                selected_tools = self._get_default_tools(max_tools)
            
            logger.info(f"Selected {len(selected_tools)} tools for query: {', '.join(selected_tools)}")
            return selected_tools
            
        except Exception as e:
            logger.warning(f"Error in tool selection: {e}")
            return self._get_default_tools(max_tools)

    def _analyze_query_categories(self, query: str) -> Dict[ToolCategory, float]:
        """Analyze query to determine relevant tool categories"""
        category_keywords = {
            ToolCategory.MARKET_DATA: ['price', 'quote', 'stock', 'ticker', 'value', 'cost', 'market'],
            ToolCategory.TECHNICAL_ANALYSIS: ['analysis', 'chart', 'technical', 'indicator', 'rsi', 'macd', 'trend'],
            ToolCategory.SENTIMENT_ANALYSIS: ['sentiment', 'news', 'opinion', 'feeling', 'mood', 'bullish', 'bearish'],
            ToolCategory.RISK_ASSESSMENT: ['risk', 'volatility', 'safe', 'dangerous', 'assessment', 'probability'],
            ToolCategory.GENERAL_SEARCH: ['search', 'find', 'look', 'information', 'about', 'what is'],
            ToolCategory.INTERNAL_TOOLS: ['format', 'cache', 'internal', 'process']
        }
        
        scores = {}
        for category, keywords in category_keywords.items():
            score = sum(1 for keyword in keywords if keyword in query)
            scores[category] = score
        
        return scores

    def _get_best_tools_for_category(self, category: ToolCategory, query: str) -> List[str]:
        """Get best tools for a specific category"""
        category_tools = self.tools_by_category.get(category, [])

        # Sort by priority and performance metrics
        def tool_score(tool_name: str) -> float:
            tool_info = self.available_tools[tool_name]

            # Base score from priority (lower priority number = higher score)
            priority_score = 4 - tool_info.priority

            # Performance score (lower error rate = higher score)
            performance_score = 1.0 - tool_info.metrics.error_rate

            # Availability score (initialized clients get bonus)
            availability_score = 1.0 if self.initialization_status.get(tool_info.client_type, False) else 0.5

            return priority_score * 0.5 + performance_score * 0.3 + availability_score * 0.2

        sorted_tools = sorted(category_tools, key=tool_score, reverse=True)
        return sorted_tools[:3]  # Return top 3 tools per category

    def _get_default_tools(self, max_tools: int) -> List[str]:
        """Get default high-priority tools"""
        all_tools = list(self.available_tools.items())

        # Sort by priority and availability
        def default_score(item):
            name, tool_info = item
            priority_score = 4 - tool_info.priority
            availability_score = 1.0 if self.initialization_status.get(tool_info.client_type, False) else 0.5
            return priority_score * 0.7 + availability_score * 0.3

        sorted_tools = sorted(all_tools, key=default_score, reverse=True)
        return [name for name, _ in sorted_tools[:max_tools]]

    async def execute_tool(
        self,
        tool_name: str,
        parameters: Dict[str, Any],
        correlation_id: str = ""
    ) -> ToolExecutionResult:
        """
        Execute a specific tool with performance monitoring

        Args:
            tool_name: Name of the tool to execute
            parameters: Parameters for the tool
            correlation_id: Request correlation ID

        Returns:
            ToolExecutionResult with execution details
        """
        start_time = time.time()

        try:
            # Check if tool exists
            if tool_name not in self.available_tools:
                return ToolExecutionResult(
                    success=False,
                    data=None,
                    error=f"Tool '{tool_name}' not found",
                    execution_time=0.0,
                    tool_name=tool_name,
                    client_type="unknown"
                )

            tool_info = self.available_tools[tool_name]

            # Check if client is initialized
            if not self.initialization_status.get(tool_info.client_type, False):
                return ToolExecutionResult(
                    success=False,
                    data=None,
                    error=f"Client '{tool_info.client_type}' not initialized",
                    execution_time=0.0,
                    tool_name=tool_name,
                    client_type=tool_info.client_type
                )

            # Execute tool based on client type
            result_data = await self._execute_tool_by_client(tool_name, parameters, tool_info)

            execution_time = time.time() - start_time

            # Update metrics
            tool_info.metrics.update_success(execution_time)
            self.global_metrics['total_requests'] += 1
            self.global_metrics['successful_requests'] += 1

            # Update average response time
            if self.global_metrics['average_response_time'] == 0.0:
                self.global_metrics['average_response_time'] = execution_time
            else:
                self.global_metrics['average_response_time'] = (
                    self.global_metrics['average_response_time'] + execution_time
                ) / 2

            logger.info(f"Tool '{tool_name}' executed successfully in {execution_time:.2f}s",
                       extra={'correlation_id': correlation_id})

            return ToolExecutionResult(
                success=True,
                data=result_data,
                error=None,
                execution_time=execution_time,
                tool_name=tool_name,
                client_type=tool_info.client_type
            )

        except Exception as e:
            execution_time = time.time() - start_time

            # Update failure metrics
            if tool_name in self.available_tools:
                self.available_tools[tool_name].metrics.update_failure()

            self.global_metrics['total_requests'] += 1
            self.global_metrics['failed_requests'] += 1

            logger.error(f"Tool '{tool_name}' execution failed: {e}",
                        extra={'correlation_id': correlation_id})

            return ToolExecutionResult(
                success=False,
                data=None,
                error=str(e),
                execution_time=execution_time,
                tool_name=tool_name,
                client_type=self.available_tools.get(tool_name, ToolInfo("", "", ToolCategory.INTERNAL_TOOLS, "")).client_type
            )

    async def _execute_tool_by_client(
        self,
        tool_name: str,
        parameters: Dict[str, Any],
        tool_info: ToolInfo
    ) -> Dict[str, Any]:
        """Execute tool using the appropriate client"""

        if tool_info.client_type == "trading":
            return await self._execute_trading_tool(tool_name, parameters)

        elif tool_info.client_type == "alpha_vantage":
            return await self._execute_alpha_vantage_tool(tool_name, parameters)

        elif tool_info.client_type == "internal":
            return await self._execute_internal_tool(tool_name, parameters)

        elif tool_info.client_type == "free":
            return await self._execute_free_mcp_tool(tool_name, parameters)

        else:
            raise ValueError(f"Unknown client type: {tool_info.client_type}")

    async def _execute_trading_tool(self, tool_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Execute trading MCP tool"""
        if not self.trading_mcp_client:
            raise RuntimeError("Trading MCP client not initialized")

        # Map tool names to client methods
        tool_mapping = {
            "get_stock_price": "get_stock_price",
            "get_technical_analysis": "get_technical_analysis",
            "analyze_market_sentiment": "analyze_market_sentiment",
            "detect_trading_intent": "detect_trading_intent",
            "get_options_data": "get_options_data",
            "calculate_risk_metrics": "calculate_risk_metrics"
        }

        method_name = tool_mapping.get(tool_name)
        if not method_name:
            raise ValueError(f"Trading tool '{tool_name}' not mapped")

        method = getattr(self.trading_mcp_client, method_name, None)
        if not method:
            raise ValueError(f"Trading client method '{method_name}' not found")

        return await method(**parameters)

    async def _execute_alpha_vantage_tool(self, tool_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Execute Alpha Vantage MCP tool"""
        if not self.alpha_vantage_client:
            raise RuntimeError("Alpha Vantage MCP client not initialized")

        # Map tool names to client methods
        tool_mapping = {
            "get_global_quote": "get_global_quote",
            "get_intraday_data": "get_intraday_data",
            "get_daily_data": "get_daily_data",
            "get_company_overview": "get_company_overview",
            "get_rsi": "get_rsi",
            "get_macd": "get_macd",
            "get_bbands": "get_bbands",
            "get_sma": "get_sma",
            "get_ema": "get_ema"
        }

        method_name = tool_mapping.get(tool_name)
        if not method_name:
            raise ValueError(f"Alpha Vantage tool '{tool_name}' not mapped")

        method = getattr(self.alpha_vantage_client, method_name, None)
        if not method:
            raise ValueError(f"Alpha Vantage client method '{method_name}' not found")

        return await method(**parameters)

    async def _execute_internal_tool(self, tool_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Execute internal MCP tool"""
        if not self.internal_tools_client:
            raise RuntimeError("Internal tools MCP client not initialized")

        # Internal tools would be executed through the internal tools client
        # For now, return a placeholder response
        return {
            "tool": tool_name,
            "parameters": parameters,
            "result": f"Internal tool '{tool_name}' executed successfully",
            "client_type": "internal"
        }

    async def _execute_free_mcp_tool(self, tool_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Execute free MCP tool"""
        # Free MCP tools would be executed when properly installed
        # For now, return a placeholder response
        return {
            "tool": tool_name,
            "parameters": parameters,
            "result": f"Free MCP tool '{tool_name}' requires installation",
            "client_type": "free",
            "status": "not_installed"
        }

    async def execute_multiple_tools(
        self,
        tool_requests: List[Dict[str, Any]],
        correlation_id: str = ""
    ) -> List[ToolExecutionResult]:
        """
        Execute multiple tools in parallel

        Args:
            tool_requests: List of {"tool_name": str, "parameters": dict}
            correlation_id: Request correlation ID

        Returns:
            List of ToolExecutionResult objects
        """
        try:
            # Create execution tasks
            tasks = []
            for request in tool_requests:
                task = self.execute_tool(
                    request["tool_name"],
                    request.get("parameters", {}),
                    correlation_id
                )
                tasks.append(task)

            # Execute all tools in parallel
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Process results
            processed_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    # Convert exception to failed result
                    processed_results.append(ToolExecutionResult(
                        success=False,
                        data=None,
                        error=str(result),
                        execution_time=0.0,
                        tool_name=tool_requests[i]["tool_name"],
                        client_type="unknown"
                    ))
                else:
                    processed_results.append(result)

            logger.info(f"Executed {len(tool_requests)} tools in parallel",
                       extra={'correlation_id': correlation_id})

            return processed_results

        except Exception as e:
            logger.error(f"Error in parallel tool execution: {e}",
                        extra={'correlation_id': correlation_id})

            # Return failed results for all tools
            return [
                ToolExecutionResult(
                    success=False,
                    data=None,
                    error=str(e),
                    execution_time=0.0,
                    tool_name=request["tool_name"],
                    client_type="unknown"
                )
                for request in tool_requests
            ]

    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get comprehensive performance metrics"""
        tool_metrics = {}
        for name, tool_info in self.available_tools.items():
            tool_metrics[name] = {
                'total_calls': tool_info.metrics.total_calls,
                'success_rate': (tool_info.metrics.successful_calls / tool_info.metrics.total_calls)
                               if tool_info.metrics.total_calls > 0 else 0.0,
                'error_rate': tool_info.metrics.error_rate,
                'average_response_time': tool_info.metrics.average_response_time,
                'last_used': tool_info.metrics.last_used,
                'client_type': tool_info.client_type,
                'category': tool_info.category.value
            }

        return {
            'global_metrics': self.global_metrics,
            'tool_metrics': tool_metrics,
            'initialization_status': self.initialization_status,
            'total_tools_available': len(self.available_tools),
            'tools_by_category': {
                category.value: len(tools)
                for category, tools in self.tools_by_category.items()
            }
        }

    def get_available_tools_info(self) -> Dict[str, Dict[str, Any]]:
        """Get information about all available tools"""
        return {
            name: {
                'description': tool_info.description,
                'category': tool_info.category.value,
                'client_type': tool_info.client_type,
                'priority': tool_info.priority,
                'requires_auth': tool_info.requires_auth,
                'rate_limited': tool_info.rate_limited,
                'parameters': tool_info.parameters,
                'metrics': {
                    'total_calls': tool_info.metrics.total_calls,
                    'success_rate': (tool_info.metrics.successful_calls / tool_info.metrics.total_calls)
                                   if tool_info.metrics.total_calls > 0 else 0.0,
                    'average_response_time': tool_info.metrics.average_response_time
                }
            }
            for name, tool_info in self.available_tools.items()
        }
