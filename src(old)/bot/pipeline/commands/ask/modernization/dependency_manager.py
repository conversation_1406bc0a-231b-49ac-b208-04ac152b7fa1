"""
Dependency Management System for ASK Pipeline

Provides comprehensive dependency management:
- Audit all dependencies for security vulnerabilities and updates
- Implement automated dependency scanning and updates
- Add dependency pinning and lock file management
- Create dependency update testing and validation
- Implement supply chain security scanning
"""

import asyncio
import json
import logging
import subprocess
import sys
from typing import Dict, Any, Optional, List, Union, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from pathlib import Path
import re
import hashlib
import tempfile
import shutil

logger = logging.getLogger(__name__)

class DependencyStatus(Enum):
    """Dependency status enumeration"""
    UP_TO_DATE = "up_to_date"
    OUTDATED = "outdated"
    VULNERABLE = "vulnerable"
    UNKNOWN = "unknown"
    ERROR = "error"

class VulnerabilitySeverity(Enum):
    """Vulnerability severity enumeration"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class DependencyInfo:
    """Dependency information"""
    name: str
    current_version: str
    latest_version: str
    status: DependencyStatus
    vulnerabilities: List[Dict[str, Any]] = field(default_factory=list)
    last_updated: Optional[datetime] = None
    license: Optional[str] = None
    description: Optional[str] = None
    homepage: Optional[str] = None
    repository: Optional[str] = None
    maintainers: List[str] = field(default_factory=list)
    dependencies: List[str] = field(default_factory=list)

@dataclass
class VulnerabilityInfo:
    """Vulnerability information"""
    cve_id: str
    severity: VulnerabilitySeverity
    description: str
    affected_versions: List[str]
    fixed_versions: List[str]
    published_date: datetime
    references: List[str] = field(default_factory=list)
    cvss_score: Optional[float] = None

@dataclass
class DependencyUpdate:
    """Dependency update information"""
    package_name: str
    current_version: str
    new_version: str
    update_type: str  # major, minor, patch
    breaking_changes: bool = False
    changelog: Optional[str] = None
    migration_notes: Optional[str] = None

class DependencyManager:
    """Dependency management system"""
    
    def __init__(self, requirements_file: str = "requirements.txt", lock_file: str = "requirements.lock"):
        self.requirements_file = Path(requirements_file)
        self.lock_file = Path(lock_file)
        self.dependencies: Dict[str, DependencyInfo] = {}
        self.vulnerabilities: List[VulnerabilityInfo] = []
        self.updates: List[DependencyUpdate] = []
        self.scan_results: Dict[str, Any] = {}
        
    async def scan_dependencies(self) -> Dict[str, Any]:
        """Scan all dependencies for vulnerabilities and updates"""
        logger.info("Starting dependency scan")
        
        try:
            # Load current dependencies
            await self._load_dependencies()
            
            # Check for vulnerabilities
            await self._scan_vulnerabilities()
            
            # Check for updates
            await self._check_updates()
            
            # Generate scan report
            scan_results = {
                "scan_timestamp": datetime.utcnow().isoformat(),
                "total_dependencies": len(self.dependencies),
                "vulnerable_dependencies": len([d for d in self.dependencies.values() if d.vulnerabilities]),
                "outdated_dependencies": len([d for d in self.dependencies.values() if d.status == DependencyStatus.OUTDATED]),
                "critical_vulnerabilities": len([v for v in self.vulnerabilities if v.severity == VulnerabilitySeverity.CRITICAL]),
                "dependencies": {name: self._serialize_dependency(dep) for name, dep in self.dependencies.items()},
                "vulnerabilities": [self._serialize_vulnerability(v) for v in self.vulnerabilities],
                "updates": [self._serialize_update(u) for u in self.updates]
            }
            
            self.scan_results = scan_results
            logger.info("Dependency scan completed")
            return scan_results
            
        except Exception as e:
            logger.error(f"Dependency scan failed: {e}")
            raise
    
    async def _load_dependencies(self):
        """Load current dependencies from requirements file"""
        if not self.requirements_file.exists():
            logger.warning(f"Requirements file {self.requirements_file} not found")
            return
        
        try:
            # Parse requirements file
            with open(self.requirements_file, 'r') as f:
                requirements = f.read().strip().split('\n')
            
            for req in requirements:
                if req.strip() and not req.startswith('#'):
                    # Parse package name and version
                    package_name, version = self._parse_requirement(req)
                    if package_name:
                        self.dependencies[package_name] = DependencyInfo(
                            name=package_name,
                            current_version=version or "unknown",
                            latest_version="unknown",
                            status=DependencyStatus.UNKNOWN
                        )
            
            logger.info(f"Loaded {len(self.dependencies)} dependencies")
            
        except Exception as e:
            logger.error(f"Failed to load dependencies: {e}")
            raise
    
    def _parse_requirement(self, req: str) -> Tuple[Optional[str], Optional[str]]:
        """Parse requirement string to extract package name and version"""
        # Remove comments and whitespace
        req = req.split('#')[0].strip()
        if not req:
            return None, None
        
        # Handle different requirement formats
        if '==' in req:
            package, version = req.split('==', 1)
        elif '>=' in req:
            package, version = req.split('>=', 1)
        elif '>' in req:
            package, version = req.split('>', 1)
        elif '<=' in req:
            package, version = req.split('<=', 1)
        elif '<' in req:
            package, version = req.split('<', 1)
        elif '~=' in req:
            package, version = req.split('~=', 1)
        else:
            package, version = req, None
        
        return package.strip(), version.strip() if version else None
    
    async def _scan_vulnerabilities(self):
        """Scan dependencies for security vulnerabilities"""
        logger.info("Scanning for vulnerabilities")
        
        try:
            # Use safety to scan for vulnerabilities
            result = await self._run_safety_scan()
            
            if result:
                vulnerabilities = self._parse_safety_output(result)
                for vuln in vulnerabilities:
                    self.vulnerabilities.append(vuln)
                    
                    # Update dependency info
                    package_name = vuln.package_name
                    if package_name in self.dependencies:
                        self.dependencies[package_name].vulnerabilities.append({
                            "cve_id": vuln.cve_id,
                            "severity": vuln.severity.value,
                            "description": vuln.description,
                            "cvss_score": vuln.cvss_score
                        })
                        self.dependencies[package_name].status = DependencyStatus.VULNERABLE
            
            logger.info(f"Found {len(self.vulnerabilities)} vulnerabilities")
            
        except Exception as e:
            logger.error(f"Vulnerability scan failed: {e}")
    
    async def _run_safety_scan(self) -> Optional[str]:
        """Run safety vulnerability scan"""
        try:
            # Check if safety is installed
            result = subprocess.run([sys.executable, "-m", "safety", "--version"], 
                                  capture_output=True, text=True)
            if result.returncode != 0:
                logger.warning("Safety not installed, skipping vulnerability scan")
                return None
            
            # Run safety scan
            result = subprocess.run([
                sys.executable, "-m", "safety", "check", 
                "--json", "--file", str(self.requirements_file)
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                return result.stdout
            else:
                logger.warning(f"Safety scan failed: {result.stderr}")
                return None
                
        except Exception as e:
            logger.error(f"Safety scan error: {e}")
            return None
    
    def _parse_safety_output(self, output: str) -> List[VulnerabilityInfo]:
        """Parse safety JSON output"""
        vulnerabilities = []
        
        try:
            data = json.loads(output)
            
            for vuln_data in data:
                vuln = VulnerabilityInfo(
                    cve_id=vuln_data.get("cve_id", "unknown"),
                    severity=VulnerabilitySeverity(vuln_data.get("severity", "unknown").lower()),
                    description=vuln_data.get("description", ""),
                    affected_versions=vuln_data.get("affected_versions", []),
                    fixed_versions=vuln_data.get("fixed_versions", []),
                    published_date=datetime.fromisoformat(vuln_data.get("published_date", "1970-01-01")),
                    references=vuln_data.get("references", []),
                    cvss_score=vuln_data.get("cvss_score")
                )
                vulnerabilities.append(vuln)
                
        except Exception as e:
            logger.error(f"Failed to parse safety output: {e}")
        
        return vulnerabilities
    
    async def _check_updates(self):
        """Check for dependency updates"""
        logger.info("Checking for updates")
        
        try:
            # Use pip list to get installed packages
            result = subprocess.run([
                sys.executable, "-m", "pip", "list", "--outdated", "--format=json"
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                outdated_packages = json.loads(result.stdout)
                
                for package_data in outdated_packages:
                    package_name = package_data["name"]
                    current_version = package_data["version"]
                    latest_version = package_data["latest_version"]
                    
                    if package_name in self.dependencies:
                        self.dependencies[package_name].latest_version = latest_version
                        self.dependencies[package_name].status = DependencyStatus.OUTDATED
                        
                        # Determine update type
                        update_type = self._determine_update_type(current_version, latest_version)
                        
                        update = DependencyUpdate(
                            package_name=package_name,
                            current_version=current_version,
                            new_version=latest_version,
                            update_type=update_type,
                            breaking_changes=update_type == "major"
                        )
                        
                        self.updates.append(update)
            
            logger.info(f"Found {len(self.updates)} available updates")
            
        except Exception as e:
            logger.error(f"Update check failed: {e}")
    
    def _determine_update_type(self, current: str, latest: str) -> str:
        """Determine update type (major, minor, patch)"""
        try:
            current_parts = [int(x) for x in current.split('.')]
            latest_parts = [int(x) for x in latest.split('.')]
            
            if len(current_parts) >= 3 and len(latest_parts) >= 3:
                if latest_parts[0] > current_parts[0]:
                    return "major"
                elif latest_parts[1] > current_parts[1]:
                    return "minor"
                elif latest_parts[2] > current_parts[2]:
                    return "patch"
            
            return "unknown"
            
        except Exception:
            return "unknown"
    
    async def update_dependencies(self, packages: Optional[List[str]] = None, 
                                dry_run: bool = True) -> Dict[str, Any]:
        """Update dependencies"""
        logger.info(f"Updating dependencies (dry_run={dry_run})")
        
        if not packages:
            packages = [update.package_name for update in self.updates]
        
        update_results = {
            "updated": [],
            "failed": [],
            "skipped": []
        }
        
        for package_name in packages:
            try:
                if dry_run:
                    logger.info(f"Would update {package_name}")
                    update_results["skipped"].append(package_name)
                else:
                    # Update package
                    result = subprocess.run([
                        sys.executable, "-m", "pip", "install", "--upgrade", package_name
                    ], capture_output=True, text=True)
                    
                    if result.returncode == 0:
                        update_results["updated"].append(package_name)
                        logger.info(f"Updated {package_name}")
                    else:
                        update_results["failed"].append({
                            "package": package_name,
                            "error": result.stderr
                        })
                        logger.error(f"Failed to update {package_name}: {result.stderr}")
                        
            except Exception as e:
                update_results["failed"].append({
                    "package": package_name,
                    "error": str(e)
                })
                logger.error(f"Error updating {package_name}: {e}")
        
        return update_results
    
    async def generate_lock_file(self) -> bool:
        """Generate lock file with pinned versions"""
        try:
            # Get all installed packages with versions
            result = subprocess.run([
                sys.executable, "-m", "pip", "freeze"
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                with open(self.lock_file, 'w') as f:
                    f.write(result.stdout)
                
                logger.info(f"Generated lock file: {self.lock_file}")
                return True
            else:
                logger.error(f"Failed to generate lock file: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"Error generating lock file: {e}")
            return False
    
    async def validate_dependencies(self) -> Dict[str, Any]:
        """Validate dependencies against lock file"""
        validation_results = {
            "valid": True,
            "mismatches": [],
            "missing": [],
            "extra": []
        }
        
        try:
            if not self.lock_file.exists():
                validation_results["valid"] = False
                validation_results["missing"].append("Lock file not found")
                return validation_results
            
            # Load lock file
            with open(self.lock_file, 'r') as f:
                lock_dependencies = {}
                for line in f:
                    if '==' in line:
                        package, version = line.strip().split('==', 1)
                        lock_dependencies[package] = version
            
            # Check current dependencies
            for package_name, dep_info in self.dependencies.items():
                if package_name in lock_dependencies:
                    if dep_info.current_version != lock_dependencies[package_name]:
                        validation_results["mismatches"].append({
                            "package": package_name,
                            "current": dep_info.current_version,
                            "locked": lock_dependencies[package_name]
                        })
                else:
                    validation_results["missing"].append(package_name)
            
            # Check for extra packages
            for package_name in lock_dependencies:
                if package_name not in self.dependencies:
                    validation_results["extra"].append(package_name)
            
            validation_results["valid"] = len(validation_results["mismatches"]) == 0
            
        except Exception as e:
            logger.error(f"Dependency validation failed: {e}")
            validation_results["valid"] = False
        
        return validation_results
    
    async def audit_supply_chain(self) -> Dict[str, Any]:
        """Audit supply chain security"""
        logger.info("Auditing supply chain security")
        
        audit_results = {
            "timestamp": datetime.utcnow().isoformat(),
            "packages_audited": 0,
            "vulnerabilities_found": 0,
            "licenses": {},
            "maintainers": {},
            "repositories": {},
            "security_issues": []
        }
        
        try:
            for package_name, dep_info in self.dependencies.items():
                audit_results["packages_audited"] += 1
                
                # Check package metadata
                package_info = await self._get_package_info(package_name)
                if package_info:
                    audit_results["licenses"][package_name] = package_info.get("license")
                    audit_results["maintainers"][package_name] = package_info.get("maintainers", [])
                    audit_results["repositories"][package_name] = package_info.get("repository")
                
                # Check for security issues
                if dep_info.vulnerabilities:
                    audit_results["vulnerabilities_found"] += len(dep_info.vulnerabilities)
                    for vuln in dep_info.vulnerabilities:
                        audit_results["security_issues"].append({
                            "package": package_name,
                            "cve": vuln["cve_id"],
                            "severity": vuln["severity"],
                            "description": vuln["description"]
                        })
            
            logger.info("Supply chain audit completed")
            
        except Exception as e:
            logger.error(f"Supply chain audit failed: {e}")
        
        return audit_results
    
    async def _get_package_info(self, package_name: str) -> Optional[Dict[str, Any]]:
        """Get package information from PyPI"""
        try:
            import aiohttp
            
            async with aiohttp.ClientSession() as session:
                async with session.get(f"https://pypi.org/pypi/{package_name}/json") as response:
                    if response.status == 200:
                        data = await response.json()
                        return {
                            "license": data.get("info", {}).get("license"),
                            "maintainers": [m.get("name") for m in data.get("info", {}).get("maintainers", [])],
                            "repository": data.get("info", {}).get("project_urls", {}).get("Source"),
                            "description": data.get("info", {}).get("summary")
                        }
        except Exception as e:
            logger.debug(f"Failed to get package info for {package_name}: {e}")
        
        return None
    
    def _serialize_dependency(self, dep: DependencyInfo) -> Dict[str, Any]:
        """Serialize dependency info for JSON output"""
        return {
            "name": dep.name,
            "current_version": dep.current_version,
            "latest_version": dep.latest_version,
            "status": dep.status.value,
            "vulnerabilities": dep.vulnerabilities,
            "last_updated": dep.last_updated.isoformat() if dep.last_updated else None,
            "license": dep.license,
            "description": dep.description,
            "homepage": dep.homepage,
            "repository": dep.repository,
            "maintainers": dep.maintainers,
            "dependencies": dep.dependencies
        }
    
    def _serialize_vulnerability(self, vuln: VulnerabilityInfo) -> Dict[str, Any]:
        """Serialize vulnerability info for JSON output"""
        return {
            "cve_id": vuln.cve_id,
            "severity": vuln.severity.value,
            "description": vuln.description,
            "affected_versions": vuln.affected_versions,
            "fixed_versions": vuln.fixed_versions,
            "published_date": vuln.published_date.isoformat(),
            "references": vuln.references,
            "cvss_score": vuln.cvss_score
        }
    
    def _serialize_update(self, update: DependencyUpdate) -> Dict[str, Any]:
        """Serialize update info for JSON output"""
        return {
            "package_name": update.package_name,
            "current_version": update.current_version,
            "new_version": update.new_version,
            "update_type": update.update_type,
            "breaking_changes": update.breaking_changes,
            "changelog": update.changelog,
            "migration_notes": update.migration_notes
        }
    
    def get_scan_summary(self) -> Dict[str, Any]:
        """Get dependency scan summary"""
        return {
            "total_dependencies": len(self.dependencies),
            "vulnerable_dependencies": len([d for d in self.dependencies.values() if d.vulnerabilities]),
            "outdated_dependencies": len([d for d in self.dependencies.values() if d.status == DependencyStatus.OUTDATED]),
            "critical_vulnerabilities": len([v for v in self.vulnerabilities if v.severity == VulnerabilitySeverity.CRITICAL]),
            "available_updates": len(self.updates),
            "last_scan": self.scan_results.get("scan_timestamp")
        }

# Global dependency manager
_dependency_manager: Optional[DependencyManager] = None

def get_dependency_manager() -> DependencyManager:
    """Get global dependency manager"""
    global _dependency_manager
    if _dependency_manager is None:
        _dependency_manager = DependencyManager()
    return _dependency_manager

def cleanup_dependency_manager():
    """Cleanup global dependency manager"""
    global _dependency_manager
    if _dependency_manager:
        _dependency_manager = None
