"""
Modern Python Features for ASK Pipeline

Implements modern Python features and patterns:
- Add async context managers for resource management
- Implement dataclasses and Pydantic models for data validation
- Add pattern matching for complex conditional logic
- Use modern typing features (Union, Optional, Generic)
- Implement async generators for streaming responses
"""

import asyncio
import logging
from typing import Dict, Any, Optional, List, Union, Generic, TypeVar, AsyncGenerator, Iterator
from dataclasses import dataclass, field, asdict
from datetime import datetime, timedelta
from enum import Enum
from contextlib import asynccontextmanager
from abc import ABC, abstractmethod
import json
import sys

logger = logging.getLogger(__name__)

# Modern typing features
T = TypeVar('T')
K = TypeVar('K')
V = TypeVar('V')

class ResourceType(Enum):
    """Resource type enumeration"""
    DATABASE = "database"
    CACHE = "cache"
    HTTP_SESSION = "http_session"
    FILE = "file"
    MEMORY = "memory"

@dataclass
class ResourceInfo:
    """Resource information"""
    resource_type: ResourceType
    name: str
    created_at: datetime
    last_accessed: datetime
    access_count: int = 0
    metadata: Dict[str, Any] = field(default_factory=dict)

class AsyncResourceManager:
    """Async resource manager with context managers"""
    
    def __init__(self):
        self.resources: Dict[str, ResourceInfo] = {}
        self._lock = asyncio.Lock()
    
    @asynccontextmanager
    async def get_database_connection(self, connection_string: str):
        """Async context manager for database connections"""
        connection_id = f"db_{id(connection_string)}"
        
        async with self._lock:
            self.resources[connection_id] = ResourceInfo(
                resource_type=ResourceType.DATABASE,
                name="database_connection",
                created_at=datetime.utcnow(),
                last_accessed=datetime.utcnow()
            )
        
        try:
            # Simulate database connection
            logger.debug(f"Opening database connection: {connection_id}")
            connection = await self._create_database_connection(connection_string)
            yield connection
        finally:
            # Cleanup
            await self._close_database_connection(connection)
            async with self._lock:
                if connection_id in self.resources:
                    del self.resources[connection_id]
            logger.debug(f"Closed database connection: {connection_id}")
    
    @asynccontextmanager
    async def get_cache_connection(self, cache_url: str):
        """Async context manager for cache connections"""
        cache_id = f"cache_{id(cache_url)}"
        
        async with self._lock:
            self.resources[cache_id] = ResourceInfo(
                resource_type=ResourceType.CACHE,
                name="cache_connection",
                created_at=datetime.utcnow(),
                last_accessed=datetime.utcnow()
            )
        
        try:
            # Simulate cache connection
            logger.debug(f"Opening cache connection: {cache_id}")
            cache = await self._create_cache_connection(cache_url)
            yield cache
        finally:
            # Cleanup
            await self._close_cache_connection(cache)
            async with self._lock:
                if cache_id in self.resources:
                    del self.resources[cache_id]
            logger.debug(f"Closed cache connection: {cache_id}")
    
    @asynccontextmanager
    async def get_http_session(self, base_url: str):
        """Async context manager for HTTP sessions"""
        session_id = f"http_{id(base_url)}"
        
        async with self._lock:
            self.resources[session_id] = ResourceInfo(
                resource_type=ResourceType.HTTP_SESSION,
                name="http_session",
                created_at=datetime.utcnow(),
                last_accessed=datetime.utcnow()
            )
        
        try:
            # Simulate HTTP session
            logger.debug(f"Opening HTTP session: {session_id}")
            session = await self._create_http_session(base_url)
            yield session
        finally:
            # Cleanup
            await self._close_http_session(session)
            async with self._lock:
                if session_id in self.resources:
                    del self.resources[session_id]
            logger.debug(f"Closed HTTP session: {session_id}")
    
    async def _create_database_connection(self, connection_string: str):
        """Create database connection"""
        # Simulate async database connection
        await asyncio.sleep(0.01)
        return {"connection_string": connection_string, "connected": True}
    
    async def _close_database_connection(self, connection):
        """Close database connection"""
        # Simulate async database disconnection
        await asyncio.sleep(0.01)
        connection["connected"] = False
    
    async def _create_cache_connection(self, cache_url: str):
        """Create cache connection"""
        # Simulate async cache connection
        await asyncio.sleep(0.01)
        return {"cache_url": cache_url, "connected": True}
    
    async def _close_cache_connection(self, cache):
        """Close cache connection"""
        # Simulate async cache disconnection
        await asyncio.sleep(0.01)
        cache["connected"] = False
    
    async def _create_http_session(self, base_url: str):
        """Create HTTP session"""
        # Simulate async HTTP session creation
        await asyncio.sleep(0.01)
        return {"base_url": base_url, "connected": True}
    
    async def _close_http_session(self, session):
        """Close HTTP session"""
        # Simulate async HTTP session closure
        await asyncio.sleep(0.01)
        session["connected"] = False
    
    async def get_resource_stats(self) -> Dict[str, Any]:
        """Get resource statistics"""
        async with self._lock:
            return {
                "total_resources": len(self.resources),
                "by_type": {
                    resource_type.value: len([r for r in self.resources.values() if r.resource_type == resource_type])
                    for resource_type in ResourceType
                },
                "resources": [asdict(resource) for resource in self.resources.values()]
            }

class ModernDataValidator:
    """Modern data validation using Pydantic and dataclasses"""
    
    def __init__(self):
        self.validation_cache: Dict[str, Any] = {}
    
    def validate_with_pattern_matching(self, data: Any) -> Dict[str, Any]:
        """Validate data using pattern matching"""
        match data:
            case str() if len(data) > 0:
                return {"type": "string", "valid": True, "length": len(data)}
            case int() if data > 0:
                return {"type": "positive_integer", "valid": True, "value": data}
            case float() if 0.0 <= data <= 1.0:
                return {"type": "probability", "valid": True, "value": data}
            case list() if len(data) > 0:
                return {"type": "non_empty_list", "valid": True, "length": len(data)}
            case dict() if len(data) > 0:
                return {"type": "non_empty_dict", "valid": True, "keys": list(data.keys())}
            case None:
                return {"type": "null", "valid": False, "error": "Value cannot be null"}
            case _:
                return {"type": "unknown", "valid": False, "error": "Unsupported data type"}
    
    def validate_complex_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate complex data structures"""
        validation_results = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "fields": {}
        }
        
        # Pattern matching for different field types
        for field_name, field_value in data.items():
            field_validation = self.validate_with_pattern_matching(field_value)
            validation_results["fields"][field_name] = field_validation
            
            if not field_validation["valid"]:
                validation_results["valid"] = False
                validation_results["errors"].append(f"{field_name}: {field_validation.get('error', 'Invalid')}")
            elif field_validation["type"] == "string" and len(field_value) > 1000:
                validation_results["warnings"].append(f"{field_name}: String is very long ({len(field_value)} chars)")
        
        return validation_results

class AsyncStreamProcessor:
    """Async stream processor using async generators"""
    
    def __init__(self):
        self.processors: Dict[str, callable] = {}
    
    async def stream_data(self, data_source: AsyncGenerator[Dict[str, Any], None], 
                         processor: Optional[callable] = None) -> AsyncGenerator[Dict[str, Any], None]:
        """Stream data through async generator"""
        async for item in data_source:
            if processor:
                processed_item = await processor(item)
                yield processed_item
            else:
                yield item
    
    async def stream_with_batching(self, data_source: AsyncGenerator[Dict[str, Any], None], 
                                 batch_size: int = 10) -> AsyncGenerator[List[Dict[str, Any]], None]:
        """Stream data in batches"""
        batch = []
        
        async for item in data_source:
            batch.append(item)
            
            if len(batch) >= batch_size:
                yield batch
                batch = []
        
        # Yield remaining items
        if batch:
            yield batch
    
    async def stream_with_filtering(self, data_source: AsyncGenerator[Dict[str, Any], None], 
                                  filter_func: callable) -> AsyncGenerator[Dict[str, Any], None]:
        """Stream data with filtering"""
        async for item in data_source:
            if await filter_func(item):
                yield item
    
    async def stream_with_transformation(self, data_source: AsyncGenerator[Dict[str, Any], None], 
                                       transform_func: callable) -> AsyncGenerator[Dict[str, Any], None]:
        """Stream data with transformation"""
        async for item in data_source:
            transformed_item = await transform_func(item)
            yield transformed_item

class ModernTypeSystem:
    """Modern type system utilities"""
    
    @staticmethod
    def create_generic_container(item_type: type) -> type:
        """Create generic container type"""
        class GenericContainer(Generic[T]):
            def __init__(self, items: List[T]):
                self.items = items
            
            def add(self, item: T) -> None:
                self.items.append(item)
            
            def get(self, index: int) -> Optional[T]:
                return self.items[index] if 0 <= index < len(self.items) else None
            
            def __len__(self) -> int:
                return len(self.items)
            
            def __iter__(self) -> Iterator[T]:
                return iter(self.items)
        
        return GenericContainer
    
    @staticmethod
    def create_typed_dict(key_type: type, value_type: type) -> type:
        """Create typed dictionary"""
        class TypedDict(Generic[K, V]):
            def __init__(self):
                self._data: Dict[K, V] = {}
            
            def set(self, key: K, value: V) -> None:
                self._data[key] = value
            
            def get(self, key: K) -> Optional[V]:
                return self._data.get(key)
            
            def keys(self) -> List[K]:
                return list(self._data.keys())
            
            def values(self) -> List[V]:
                return list(self._data.values())
            
            def items(self) -> List[tuple[K, V]]:
                return list(self._data.items())
        
        return TypedDict
    
    @staticmethod
    def create_union_type(*types) -> type:
        """Create union type"""
        return Union[types]
    
    @staticmethod
    def create_optional_type(base_type: type) -> type:
        """Create optional type"""
        return Optional[base_type]

class ModernAsyncPatterns:
    """Modern async patterns and utilities"""
    
    @staticmethod
    async def gather_with_timeout(tasks: List[asyncio.Task], timeout: float = 30.0) -> List[Any]:
        """Gather tasks with timeout"""
        try:
            return await asyncio.wait_for(asyncio.gather(*tasks), timeout=timeout)
        except asyncio.TimeoutError:
            # Cancel remaining tasks
            for task in tasks:
                if not task.done():
                    task.cancel()
            raise
    
    @staticmethod
    async def race_tasks(tasks: List[asyncio.Task]) -> Any:
        """Race tasks and return first completed result"""
        done, pending = await asyncio.wait(tasks, return_when=asyncio.FIRST_COMPLETED)
        
        # Cancel pending tasks
        for task in pending:
            task.cancel()
        
        # Return result from completed task
        return await done.pop()
    
    @staticmethod
    async def retry_async(func: callable, max_retries: int = 3, delay: float = 1.0) -> Any:
        """Retry async function with exponential backoff"""
        for attempt in range(max_retries):
            try:
                return await func()
            except Exception as e:
                if attempt == max_retries - 1:
                    raise e
                
                await asyncio.sleep(delay * (2 ** attempt))
    
    @staticmethod
    async def batch_process(items: List[Any], processor: callable, 
                          batch_size: int = 10, max_concurrent: int = 5) -> List[Any]:
        """Process items in batches with concurrency control"""
        results = []
        
        for i in range(0, len(items), batch_size):
            batch = items[i:i + batch_size]
            
            # Process batch with concurrency control
            semaphore = asyncio.Semaphore(max_concurrent)
            
            async def process_item(item):
                async with semaphore:
                    return await processor(item)
            
            batch_results = await asyncio.gather(*[process_item(item) for item in batch])
            results.extend(batch_results)
        
        return results

class ModernErrorHandling:
    """Modern error handling patterns"""
    
    @staticmethod
    def handle_errors_with_context(error_type: type, context: str = ""):
        """Decorator for error handling with context"""
        def decorator(func):
            async def async_wrapper(*args, **kwargs):
                try:
                    return await func(*args, **kwargs)
                except error_type as e:
                    logger.error(f"Error in {context}: {e}")
                    raise
                except Exception as e:
                    logger.error(f"Unexpected error in {context}: {e}")
                    raise
            
            def sync_wrapper(*args, **kwargs):
                try:
                    return func(*args, **kwargs)
                except error_type as e:
                    logger.error(f"Error in {context}: {e}")
                    raise
                except Exception as e:
                    logger.error(f"Unexpected error in {context}: {e}")
                    raise
            
            if asyncio.iscoroutinefunction(func):
                return async_wrapper
            else:
                return sync_wrapper
        
        return decorator
    
    @staticmethod
    async def safe_execute(func: callable, default_value: Any = None, 
                          error_types: tuple = (Exception,)) -> Any:
        """Safely execute function with default value on error"""
        try:
            if asyncio.iscoroutinefunction(func):
                return await func()
            else:
                return func()
        except error_types as e:
            logger.warning(f"Function execution failed, using default value: {e}")
            return default_value

class ModernDataStructures:
    """Modern data structures and utilities"""
    
    @dataclass
    class ModernConfig:
        """Modern configuration using dataclasses"""
        name: str
        value: Any
        description: str = ""
        required: bool = True
        validation_rules: List[str] = field(default_factory=list)
        metadata: Dict[str, Any] = field(default_factory=dict)
        
        def to_dict(self) -> Dict[str, Any]:
            """Convert to dictionary"""
            return asdict(self)
        
        @classmethod
        def from_dict(cls, data: Dict[str, Any]) -> 'ModernConfig':
            """Create from dictionary"""
            return cls(**data)
    
    @dataclass
    class ModernEvent:
        """Modern event using dataclasses"""
        event_type: str
        timestamp: datetime
        data: Dict[str, Any]
        source: str = "system"
        correlation_id: Optional[str] = None
        
        def to_json(self) -> str:
            """Convert to JSON"""
            return json.dumps(asdict(self), default=str)
        
        @classmethod
        def from_json(cls, json_str: str) -> 'ModernEvent':
            """Create from JSON"""
            data = json.loads(json_str)
            data['timestamp'] = datetime.fromisoformat(data['timestamp'])
            return cls(**data)

# Global instances
_resource_manager = AsyncResourceManager()
_data_validator = ModernDataValidator()
_stream_processor = AsyncStreamProcessor()
_type_system = ModernTypeSystem()
_async_patterns = ModernAsyncPatterns()
_error_handling = ModernErrorHandling()
_data_structures = ModernDataStructures()

def get_resource_manager() -> AsyncResourceManager:
    """Get global resource manager"""
    return _resource_manager

def get_data_validator() -> ModernDataValidator:
    """Get global data validator"""
    return _data_validator

def get_stream_processor() -> AsyncStreamProcessor:
    """Get global stream processor"""
    return _stream_processor

def get_type_system() -> ModernTypeSystem:
    """Get global type system"""
    return _type_system

def get_async_patterns() -> ModernAsyncPatterns:
    """Get global async patterns"""
    return _async_patterns

def get_error_handling() -> ModernErrorHandling:
    """Get global error handling"""
    return _error_handling

def get_data_structures() -> ModernDataStructures:
    """Get global data structures"""
    return _data_structures
