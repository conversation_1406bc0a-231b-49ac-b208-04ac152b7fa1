"""
Containerization for ASK Pipeline

Provides containerization capabilities:
- Create optimized Dockerfile with multi-stage builds
- Implement container security scanning and hardening
- Add container health checks and resource limits
- Create container orchestration configuration (Docker Compose/Kubernetes)
- Implement container monitoring and logging
"""

import os
import json
import logging
import subprocess
import tempfile
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from pathlib import Path
import yaml

logger = logging.getLogger(__name__)

class ContainerPlatform(Enum):
    """Container platform enumeration"""
    DOCKER = "docker"
    PODMAN = "podman"
    CONTAINERD = "containerd"

class SecurityLevel(Enum):
    """Security level enumeration"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class ContainerConfig:
    """Container configuration"""
    name: str
    image: str
    tag: str = "latest"
    platform: ContainerPlatform = ContainerPlatform.DOCKER
    base_image: str = "python:3.11-slim"
    working_dir: str = "/app"
    user: str = "appuser"
    ports: List[int] = field(default_factory=list)
    environment: Dict[str, str] = field(default_factory=dict)
    volumes: List[str] = field(default_factory=list)
    health_check: Optional[Dict[str, Any]] = None
    resource_limits: Optional[Dict[str, Any]] = None
    security_options: List[str] = field(default_factory=list)

@dataclass
class SecurityScanResult:
    """Security scan result"""
    image: str
    vulnerabilities: List[Dict[str, Any]] = field(default_factory=list)
    security_score: float = 0.0
    recommendations: List[str] = field(default_factory=list)
    scan_timestamp: datetime = field(default_factory=datetime.utcnow)

class ContainerBuilder:
    """Container builder with multi-stage builds"""
    
    def __init__(self, config: ContainerConfig):
        self.config = config
        self.dockerfile_content = ""
        self.build_context = Path(".")
    
    def generate_dockerfile(self) -> str:
        """Generate optimized Dockerfile with multi-stage builds"""
        dockerfile = f"""# Multi-stage build for ASK Pipeline
FROM {self.config.base_image} as base

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PIP_NO_CACHE_DIR=1
ENV PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies
RUN apt-get update && apt-get install -y \\
    build-essential \\
    curl \\
    git \\
    && rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN groupadd -r {self.config.user} && useradd -r -g {self.config.user} {self.config.user}

# Set working directory
WORKDIR {self.config.working_dir}

# Copy requirements first for better caching
COPY requirements.txt requirements.lock ./

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Development stage
FROM base as development
RUN pip install --no-cache-dir \\
    pytest \\
    pytest-asyncio \\
    pytest-cov \\
    black \\
    isort \\
    flake8 \\
    mypy

# Copy source code
COPY . .

# Set ownership
RUN chown -R {self.config.user}:{self.config.user} {self.config.working_dir}

# Switch to non-root user
USER {self.config.user}

# Expose ports
{self._generate_expose_ports()}

# Health check
{self._generate_health_check()}

# Production stage
FROM base as production

# Copy only necessary files
COPY --from=development {self.config.working_dir}/src ./src
COPY --from=development {self.config.working_dir}/requirements.txt ./
COPY --from=development {self.config.working_dir}/requirements.lock ./

# Install production dependencies only
RUN pip install --no-cache-dir -r requirements.txt

# Set ownership
RUN chown -R {self.config.user}:{self.config.user} {self.config.working_dir}

# Switch to non-root user
USER {self.config.user}

# Expose ports
{self._generate_expose_ports()}

# Health check
{self._generate_health_check()}

# Default command
CMD ["python", "-m", "src.bot.pipeline.commands.ask.main"]
"""
        
        self.dockerfile_content = dockerfile
        return dockerfile
    
    def _generate_expose_ports(self) -> str:
        """Generate EXPOSE directives"""
        if not self.config.ports:
            return ""
        
        expose_lines = []
        for port in self.config.ports:
            expose_lines.append(f"EXPOSE {port}")
        
        return "\n".join(expose_lines)
    
    def _generate_health_check(self) -> str:
        """Generate health check"""
        if not self.config.health_check:
            return ""
        
        health_check = self.config.health_check
        cmd = health_check.get("cmd", "python -c 'import sys; sys.exit(0)'")
        interval = health_check.get("interval", 30)
        timeout = health_check.get("timeout", 10)
        retries = health_check.get("retries", 3)
        start_period = health_check.get("start_period", 0)
        
        return f"""HEALTHCHECK --interval={interval}s --timeout={timeout}s --start-period={start_period}s --retries={retries} \\
    CMD {cmd}"""
    
    def generate_dockerignore(self) -> str:
        """Generate .dockerignore file"""
        dockerignore = """# Git
.git
.gitignore

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Documentation
docs/
*.md
README.md

# Tests
tests/
test_*
*_test.py

# Development files
.env.local
.env.development
.env.test
.env.production

# Logs
logs/
*.log

# Temporary files
tmp/
temp/
"""
        return dockerignore
    
    def build_image(self, tag: Optional[str] = None, platform: Optional[str] = None) -> bool:
        """Build container image"""
        try:
            # Write Dockerfile
            dockerfile_path = self.build_context / "Dockerfile"
            with open(dockerfile_path, 'w') as f:
                f.write(self.dockerfile_content)
            
            # Write .dockerignore
            dockerignore_path = self.build_context / ".dockerignore"
            with open(dockerignore_path, 'w') as f:
                f.write(self.generate_dockerignore())
            
            # Build command
            build_tag = tag or f"{self.config.name}:{self.config.tag}"
            cmd = [self.config.platform.value, "build", "-t", build_tag]
            
            if platform:
                cmd.extend(["--platform", platform])
            
            cmd.append(str(self.build_context))
            
            # Execute build
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info(f"Successfully built image: {build_tag}")
                return True
            else:
                logger.error(f"Build failed: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"Build error: {e}")
            return False
    
    def scan_security(self, image_tag: str) -> SecurityScanResult:
        """Scan container image for security vulnerabilities"""
        try:
            # Use trivy for security scanning
            cmd = ["trivy", "image", "--format", "json", image_tag]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                scan_data = json.loads(result.stdout)
                vulnerabilities = scan_data.get("Results", [])
                
                # Calculate security score
                security_score = self._calculate_security_score(vulnerabilities)
                
                # Generate recommendations
                recommendations = self._generate_security_recommendations(vulnerabilities)
                
                return SecurityScanResult(
                    image=image_tag,
                    vulnerabilities=vulnerabilities,
                    security_score=security_score,
                    recommendations=recommendations
                )
            else:
                logger.warning(f"Security scan failed: {result.stderr}")
                return SecurityScanResult(image=image_tag)
                
        except Exception as e:
            logger.error(f"Security scan error: {e}")
            return SecurityScanResult(image=image_tag)
    
    def _calculate_security_score(self, vulnerabilities: List[Dict[str, Any]]) -> float:
        """Calculate security score based on vulnerabilities"""
        if not vulnerabilities:
            return 100.0
        
        total_vulnerabilities = len(vulnerabilities)
        critical_count = sum(1 for v in vulnerabilities if v.get("Severity") == "CRITICAL")
        high_count = sum(1 for v in vulnerabilities if v.get("Severity") == "HIGH")
        medium_count = sum(1 for v in vulnerabilities if v.get("Severity") == "MEDIUM")
        low_count = sum(1 for v in vulnerabilities if v.get("Severity") == "LOW")
        
        # Calculate score (100 - weighted vulnerabilities)
        score = 100.0
        score -= critical_count * 20  # -20 points per critical
        score -= high_count * 10     # -10 points per high
        score -= medium_count * 5    # -5 points per medium
        score -= low_count * 1       # -1 point per low
        
        return max(0.0, score)
    
    def _generate_security_recommendations(self, vulnerabilities: List[Dict[str, Any]]) -> List[str]:
        """Generate security recommendations"""
        recommendations = []
        
        critical_vulns = [v for v in vulnerabilities if v.get("Severity") == "CRITICAL"]
        if critical_vulns:
            recommendations.append("Address critical vulnerabilities immediately")
        
        high_vulns = [v for v in vulnerabilities if v.get("Severity") == "HIGH"]
        if high_vulns:
            recommendations.append("Update packages with high severity vulnerabilities")
        
        # Check for specific vulnerability types
        if any("CVE" in str(v) for v in vulnerabilities):
            recommendations.append("Update base image to latest version")
        
        if any("python" in str(v).lower() for v in vulnerabilities):
            recommendations.append("Update Python dependencies")
        
        return recommendations

class DockerComposeGenerator:
    """Docker Compose configuration generator"""
    
    def __init__(self, services: List[ContainerConfig]):
        self.services = services
    
    def generate_compose_file(self) -> str:
        """Generate Docker Compose file"""
        compose_data = {
            "version": "3.8",
            "services": {},
            "networks": {
                "ask_network": {
                    "driver": "bridge"
                }
            },
            "volumes": {
                "ask_data": {
                    "driver": "local"
                }
            }
        }
        
        for service in self.services:
            service_config = {
                "build": {
                    "context": ".",
                    "dockerfile": "Dockerfile",
                    "target": "production"
                },
                "image": f"{service.name}:{service.tag}",
                "container_name": service.name,
                "restart": "unless-stopped",
                "environment": service.environment,
                "volumes": service.volumes,
                "networks": ["ask_network"],
                "healthcheck": service.health_check,
                "deploy": {
                    "resources": service.resource_limits or {}
                }
            }
            
            if service.ports:
                service_config["ports"] = [f"{port}:{port}" for port in service.ports]
            
            compose_data["services"][service.name] = service_config
        
        return yaml.dump(compose_data, default_flow_style=False, indent=2)
    
    def generate_development_compose(self) -> str:
        """Generate development Docker Compose file"""
        compose_data = {
            "version": "3.8",
            "services": {},
            "networks": {
                "ask_dev_network": {
                    "driver": "bridge"
                }
            },
            "volumes": {
                "ask_dev_data": {
                    "driver": "local"
                }
            }
        }
        
        for service in self.services:
            service_config = {
                "build": {
                    "context": ".",
                    "dockerfile": "Dockerfile",
                    "target": "development"
                },
                "image": f"{service.name}-dev:{service.tag}",
                "container_name": f"{service.name}-dev",
                "restart": "unless-stopped",
                "environment": {
                    **service.environment,
                    "ENVIRONMENT": "development",
                    "DEBUG": "true"
                },
                "volumes": [
                    *service.volumes,
                    ".:/app",
                    "/app/__pycache__"
                ],
                "networks": ["ask_dev_network"],
                "ports": [f"{port}:{port}" for port in service.ports] if service.ports else [],
                "command": ["python", "-m", "src.bot.pipeline.commands.ask.main"]
            }
            
            compose_data["services"][f"{service.name}-dev"] = service_config
        
        return yaml.dump(compose_data, default_flow_style=False, indent=2)

class KubernetesGenerator:
    """Kubernetes configuration generator"""
    
    def __init__(self, services: List[ContainerConfig]):
        self.services = services
    
    def generate_deployment(self, service: ContainerConfig) -> str:
        """Generate Kubernetes deployment"""
        deployment = {
            "apiVersion": "apps/v1",
            "kind": "Deployment",
            "metadata": {
                "name": service.name,
                "labels": {
                    "app": service.name,
                    "version": service.tag
                }
            },
            "spec": {
                "replicas": 3,
                "selector": {
                    "matchLabels": {
                        "app": service.name
                    }
                },
                "template": {
                    "metadata": {
                        "labels": {
                            "app": service.name,
                            "version": service.tag
                        }
                    },
                    "spec": {
                        "containers": [{
                            "name": service.name,
                            "image": f"{service.name}:{service.tag}",
                            "ports": [{"containerPort": port} for port in service.ports],
                            "env": [{"name": k, "value": v} for k, v in service.environment.items()],
                            "resources": service.resource_limits or {},
                            "livenessProbe": {
                                "httpGet": {
                                    "path": "/health",
                                    "port": service.ports[0] if service.ports else 8000
                                },
                                "initialDelaySeconds": 30,
                                "periodSeconds": 10
                            },
                            "readinessProbe": {
                                "httpGet": {
                                    "path": "/ready",
                                    "port": service.ports[0] if service.ports else 8000
                                },
                                "initialDelaySeconds": 5,
                                "periodSeconds": 5
                            }
                        }],
                        "securityContext": {
                            "runAsNonRoot": True,
                            "runAsUser": 1000,
                            "fsGroup": 1000
                        }
                    }
                }
            }
        }
        
        return yaml.dump(deployment, default_flow_style=False, indent=2)
    
    def generate_service(self, service: ContainerConfig) -> str:
        """Generate Kubernetes service"""
        service_config = {
            "apiVersion": "v1",
            "kind": "Service",
            "metadata": {
                "name": service.name,
                "labels": {
                    "app": service.name
                }
            },
            "spec": {
                "selector": {
                    "app": service.name
                },
                "ports": [{"port": port, "targetPort": port} for port in service.ports],
                "type": "ClusterIP"
            }
        }
        
        return yaml.dump(service_config, default_flow_style=False, indent=2)
    
    def generate_configmap(self, service: ContainerConfig) -> str:
        """Generate Kubernetes ConfigMap"""
        configmap = {
            "apiVersion": "v1",
            "kind": "ConfigMap",
            "metadata": {
                "name": f"{service.name}-config"
            },
            "data": service.environment
        }
        
        return yaml.dump(configmap, default_flow_style=False, indent=2)
    
    def generate_all_manifests(self) -> Dict[str, str]:
        """Generate all Kubernetes manifests"""
        manifests = {}
        
        for service in self.services:
            manifests[f"{service.name}-deployment.yaml"] = self.generate_deployment(service)
            manifests[f"{service.name}-service.yaml"] = self.generate_service(service)
            manifests[f"{service.name}-configmap.yaml"] = self.generate_configmap(service)
        
        return manifests

class ContainerMonitor:
    """Container monitoring and logging"""
    
    def __init__(self):
        self.monitoring_data: Dict[str, Any] = {}
    
    def get_container_stats(self, container_name: str) -> Dict[str, Any]:
        """Get container statistics"""
        try:
            cmd = ["docker", "stats", "--no-stream", "--format", "json", container_name]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                stats = json.loads(result.stdout)
                return {
                    "container_name": container_name,
                    "cpu_percent": stats.get("CPUPerc", "0%"),
                    "memory_usage": stats.get("MemUsage", "0B"),
                    "memory_percent": stats.get("MemPerc", "0%"),
                    "network_io": stats.get("NetIO", "0B"),
                    "block_io": stats.get("BlockIO", "0B"),
                    "pids": stats.get("PIDs", "0"),
                    "timestamp": datetime.utcnow().isoformat()
                }
            else:
                return {"error": f"Failed to get stats: {result.stderr}"}
                
        except Exception as e:
            return {"error": f"Stats error: {e}"}
    
    def get_container_logs(self, container_name: str, lines: int = 100) -> List[str]:
        """Get container logs"""
        try:
            cmd = ["docker", "logs", "--tail", str(lines), container_name]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                return result.stdout.split('\n')
            else:
                return [f"Failed to get logs: {result.stderr}"]
                
        except Exception as e:
            return [f"Logs error: {e}"]

# Global instances
_container_builder: Optional[ContainerBuilder] = None
_compose_generator: Optional[DockerComposeGenerator] = None
_k8s_generator: Optional[KubernetesGenerator] = None
_container_monitor = ContainerMonitor()

def get_container_builder(config: ContainerConfig) -> ContainerBuilder:
    """Get container builder"""
    return ContainerBuilder(config)

def get_compose_generator(services: List[ContainerConfig]) -> DockerComposeGenerator:
    """Get Docker Compose generator"""
    return DockerComposeGenerator(services)

def get_k8s_generator(services: List[ContainerConfig]) -> KubernetesGenerator:
    """Get Kubernetes generator"""
    return KubernetesGenerator(services)

def get_container_monitor() -> ContainerMonitor:
    """Get container monitor"""
    return _container_monitor
