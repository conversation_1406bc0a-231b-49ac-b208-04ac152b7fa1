"""
Comprehensive Audit Logger for ASK Pipeline

This module provides detailed audit logging for every step, decision, and plan
in the ASK pipeline. It tracks:
- Every pipeline stage execution
- Decision points and reasoning
- Performance metrics
- Error conditions
- Data flow
- User interactions
"""

import time
import json
import asyncio
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, asdict
from datetime import datetime
from pathlib import Path
import logging

from src.shared.error_handling.logging import get_logger

logger = get_logger(__name__)

@dataclass
class AuditSubStep:
    """Represents a sub-step within a main pipeline step"""
    span_id: str
    name: str
    tool: Optional[str] = None
    status: str = "success"  # 'success', 'timeout', 'error', 'fallback'
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    duration_ms: float = 0.0
    result: Optional[Dict[str, Any]] = None
    entities: Optional[List[str]] = None
    error_details: Optional[Dict[str, Any]] = None
    fallback_used: Optional[str] = None

@dataclass
class AuditStep:
    """Represents a single step in the pipeline audit"""
    step_id: str
    step_name: str
    stage: str
    timestamp: float
    duration_ms: float
    status: str  # 'started', 'completed', 'failed', 'skipped'
    input_data: Dict[str, Any]
    output_data: Dict[str, Any]
    decisions: List[Dict[str, Any]]
    performance_metrics: Dict[str, Any]
    error_details: Optional[Dict[str, Any]] = None
    reasoning: Optional[str] = None
    next_steps: List[str] = None
    sub_steps: List[AuditSubStep] = None  # New: nested sub-steps

@dataclass
class AuditDecision:
    """Represents a decision made during pipeline execution"""
    decision_id: str
    decision_point: str
    options: List[str]
    chosen_option: str
    reasoning: str
    confidence: float
    timestamp: float
    context: Dict[str, Any]

@dataclass
class AuditPlan:
    """Represents a plan or strategy for pipeline execution"""
    plan_id: str
    plan_name: str
    strategy: str
    steps: List[str]
    expected_duration: float
    fallback_strategy: Optional[str]
    success_criteria: List[str]
    timestamp: float

class AskPipelineAuditLogger:
    """
    Comprehensive audit logger for the ASK pipeline
    
    Tracks every step, decision, and plan with detailed logging
    """
    
    def __init__(self, correlation_id: str, user_id: str, query: str, audit_level: str = "normal"):
        self.correlation_id = correlation_id
        self.user_id = user_id
        self.query = query
        self.audit_level = audit_level  # 'minimal', 'normal', 'verbose'
        self.start_time = time.time()
        self.steps: List[AuditStep] = []
        self.decisions: List[AuditDecision] = []
        self.plans: List[AuditPlan] = []
        self.current_step: Optional[AuditStep] = None
        self.current_sub_steps: List[AuditSubStep] = []
        self.audit_file = self._setup_audit_file()
        
        # Log pipeline start
        self.log_pipeline_start()
    
    def _setup_audit_file(self) -> Path:
        """Setup audit file for this pipeline execution"""
        audit_dir = Path("logs/audit")
        audit_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"ask_audit_{self.correlation_id}_{timestamp}.json"
        return audit_dir / filename
    
    def log_pipeline_start(self):
        """Log the start of the pipeline"""
        logger.info("🚀 ASK PIPELINE AUDIT START", extra={
            'correlation_id': self.correlation_id,
            'user_id': self.user_id,
            'query': self.query,
            'query_length': len(self.query),
            'timestamp': self.start_time,
            'audit_file': str(self.audit_file)
        })
    
    def log_pipeline_end(self, success: bool, final_result: Dict[str, Any]):
        """Log the end of the pipeline"""
        total_duration = time.time() - self.start_time
        
        logger.info("🏁 ASK PIPELINE AUDIT END", extra={
            'correlation_id': self.correlation_id,
            'success': success,
            'total_duration': total_duration,
            'total_steps': len(self.steps),
            'total_decisions': len(self.decisions),
            'total_plans': len(self.plans),
            'final_result_keys': list(final_result.keys()) if final_result else []
        })
        
        # Save complete audit to file
        self._save_audit_to_file(success, final_result, total_duration)
    
    def start_step(self, step_id: str, step_name: str, stage: str, input_data: Dict[str, Any] = None) -> str:
        """Start tracking a pipeline step"""
        if self.current_step:
            self._complete_current_step("completed")
        
        self.current_step = AuditStep(
            step_id=step_id,
            step_name=step_name,
            stage=stage,
            timestamp=time.time(),
            duration_ms=0.0,
            status="started",
            input_data=input_data or {},
            output_data={},
            decisions=[],
            performance_metrics={},
            next_steps=[],
            sub_steps=[]
        )
        
        # Clear sub-steps for new step
        self.current_sub_steps = []
        
        logger.info(f"🔄 STEP START: {step_name}", extra={
            'correlation_id': self.correlation_id,
            'step_id': step_id,
            'stage': stage,
            'input_data': input_data,
            'timestamp': self.current_step.timestamp
        })
        
        return step_id
    
    def update_step_data(self, data: Dict[str, Any], data_type: str = "output"):
        """Update current step data"""
        if not self.current_step:
            return
        
        if data_type == "output":
            self.current_step.output_data.update(data)
        elif data_type == "input":
            self.current_step.input_data.update(data)
        
        logger.debug(f"📊 STEP DATA UPDATE: {data_type}", extra={
            'correlation_id': self.correlation_id,
            'step_id': self.current_step.step_id,
            'data_keys': list(data.keys()),
            'data_type': data_type
        })
    
    def log_decision(self, decision_point: str, options: List[str], chosen_option: str, 
                    reasoning: str, confidence: float = 1.0, context: Dict[str, Any] = None):
        """Log a decision made during pipeline execution"""
        decision = AuditDecision(
            decision_id=f"{self.correlation_id}_{len(self.decisions)}",
            decision_point=decision_point,
            options=options,
            chosen_option=chosen_option,
            reasoning=reasoning,
            confidence=confidence,
            timestamp=time.time(),
            context=context or {}
        )
        
        self.decisions.append(decision)
        
        if self.current_step:
            self.current_step.decisions.append(asdict(decision))
        
        logger.info(f"🤔 DECISION: {decision_point}", extra={
            'correlation_id': self.correlation_id,
            'decision_id': decision.decision_id,
            'options': options,
            'chosen': chosen_option,
            'reasoning': reasoning,
            'confidence': confidence,
            'context': context
        })
    
    def log_plan(self, plan_name: str, strategy: str, steps: List[str], 
                expected_duration: float, fallback_strategy: str = None, 
                success_criteria: List[str] = None):
        """Log a plan or strategy"""
        plan = AuditPlan(
            plan_id=f"{self.correlation_id}_plan_{len(self.plans)}",
            plan_name=plan_name,
            strategy=strategy,
            steps=steps,
            expected_duration=expected_duration,
            fallback_strategy=fallback_strategy,
            success_criteria=success_criteria or [],
            timestamp=time.time()
        )
        
        self.plans.append(plan)
        
        logger.info(f"📋 PLAN: {plan_name}", extra={
            'correlation_id': self.correlation_id,
            'plan_id': plan.plan_id,
            'strategy': strategy,
            'steps': steps,
            'expected_duration': expected_duration,
            'fallback': fallback_strategy,
            'success_criteria': success_criteria
        })
    
    def start_sub_step(self, name: str, tool: str = None, span_id: str = None) -> str:
        """Start tracking a sub-step within the current step"""
        if not self.current_step:
            logger.warning("No current step for sub-step", extra={'correlation_id': self.correlation_id})
            return ""
        
        if span_id is None:
            span_id = f"{self.current_step.step_id}.{len(self.current_sub_steps) + 1}"
        
        sub_step = AuditSubStep(
            span_id=span_id,
            name=name,
            tool=tool,
            status="started",
            start_time=time.time()
        )
        
        self.current_sub_steps.append(sub_step)
        
        if self.audit_level in ["normal", "verbose"]:
            logger.info(f"🔄 SUB-STEP START: {name}", extra={
                'correlation_id': self.correlation_id,
                'span_id': span_id,
                'tool': tool,
                'step': self.current_step.step_name
            })
        
        return span_id
    
    def complete_sub_step(self, span_id: str, status: str = "success", 
                         result: Dict[str, Any] = None, entities: List[str] = None,
                         error_details: Dict[str, Any] = None, fallback_used: str = None):
        """Complete a sub-step"""
        if not self.current_step:
            return
        
        # Find the sub-step
        sub_step = None
        for i, ss in enumerate(self.current_sub_steps):
            if ss.span_id == span_id:
                sub_step = ss
                break
        
        if not sub_step:
            logger.warning(f"Sub-step {span_id} not found", extra={'correlation_id': self.correlation_id})
            return
        
        # Update sub-step
        sub_step.status = status
        sub_step.end_time = time.time()
        sub_step.duration_ms = (sub_step.end_time - sub_step.start_time) * 1000
        sub_step.result = result
        sub_step.entities = entities
        sub_step.error_details = error_details
        sub_step.fallback_used = fallback_used
        
        if self.audit_level in ["normal", "verbose"]:
            logger.info(f"✅ SUB-STEP COMPLETE: {sub_step.name}", extra={
                'correlation_id': self.correlation_id,
                'span_id': span_id,
                'status': status,
                'duration_ms': sub_step.duration_ms,
                'tool': sub_step.tool,
                'result': result,
                'entities': entities
            })
    
    def should_log_verbose(self) -> bool:
        """Check if we should log verbose details"""
        return self.audit_level == "verbose"
    
    def log_performance_metric(self, metric_name: str, value: float, unit: str = "ms"):
        """Log a performance metric"""
        if not self.current_step:
            return
        
        self.current_step.performance_metrics[metric_name] = {
            'value': value,
            'unit': unit,
            'timestamp': time.time()
        }
        
        logger.debug(f"📈 PERFORMANCE: {metric_name}={value}{unit}", extra={
            'correlation_id': self.correlation_id,
            'step_id': self.current_step.step_id,
            'metric': metric_name,
            'value': value,
            'unit': unit
        })
    
    def log_error(self, error: Exception, context: Dict[str, Any] = None):
        """Log an error during pipeline execution"""
        error_details = {
            'error_type': type(error).__name__,
            'error_message': str(error),
            'context': context or {},
            'timestamp': time.time()
        }
        
        if self.current_step:
            self.current_step.error_details = error_details
            self.current_step.status = "failed"
        
        logger.error(f"❌ ERROR: {type(error).__name__}", extra={
            'correlation_id': self.correlation_id,
            'step_id': self.current_step.step_id if self.current_step else None,
            'error_details': error_details
        })
    
    def complete_step(self, status: str = "completed", output_data: Dict[str, Any] = None, 
                     reasoning: str = None, next_steps: List[str] = None):
        """Complete the current step"""
        if not self.current_step:
            return
        
        self.current_step.status = status
        self.current_step.duration_ms = (time.time() - self.current_step.timestamp) * 1000
        self.current_step.reasoning = reasoning
        self.current_step.next_steps = next_steps or []
        
        if output_data:
            self.current_step.output_data.update(output_data)
        
        # Include sub-steps in the step
        self.current_step.sub_steps = self.current_sub_steps.copy()
        
        self.steps.append(self.current_step)
        
        logger.info(f"✅ STEP COMPLETE: {self.current_step.step_name}", extra={
            'correlation_id': self.correlation_id,
            'step_id': self.current_step.step_id,
            'status': status,
            'duration_ms': self.current_step.duration_ms,
            'reasoning': reasoning,
            'next_steps': next_steps,
            'output_keys': list(self.current_step.output_data.keys())
        })
        
        self.current_step = None
    
    def _serialize_step(self, step: AuditStep) -> Dict[str, Any]:
        """Serialize a step with proper sub-step handling"""
        step_dict = asdict(step)
        if step.sub_steps:
            step_dict['sub_steps'] = [asdict(sub_step) for sub_step in step.sub_steps]
        return step_dict
    
    def _complete_current_step(self, status: str = "completed"):
        """Complete the current step if one exists"""
        if self.current_step:
            self.complete_step(status)
    
    def _save_audit_to_file(self, success: bool, final_result: Dict[str, Any], total_duration: float):
        """Save complete audit to JSON file"""
        audit_data = {
            'correlation_id': self.correlation_id,
            'user_id': self.user_id,
            'query': self.query,
            'start_time': self.start_time,
            'end_time': time.time(),
            'total_duration': total_duration,
            'success': success,
            'steps': [self._serialize_step(step) for step in self.steps],
            'decisions': [asdict(decision) for decision in self.decisions],
            'plans': [asdict(plan) for plan in self.plans],
            'final_result': final_result,
            'summary': {
                'total_steps': len(self.steps),
                'total_decisions': len(self.decisions),
                'total_plans': len(self.plans),
                'success_rate': len([s for s in self.steps if s.status == "completed"]) / len(self.steps) if self.steps else 0,
                'average_step_duration': sum(s.duration_ms for s in self.steps) / len(self.steps) if self.steps else 0
            }
        }
        
        try:
            with open(self.audit_file, 'w') as f:
                json.dump(audit_data, f, indent=2, default=str)
            
            logger.info(f"💾 AUDIT SAVED: {self.audit_file}", extra={
                'correlation_id': self.correlation_id,
                'audit_file': str(self.audit_file),
                'file_size': self.audit_file.stat().st_size
            })
        except Exception as e:
            logger.error(f"❌ FAILED TO SAVE AUDIT: {e}", extra={
                'correlation_id': self.correlation_id,
                'error': str(e)
            })
    
    def get_audit_summary(self) -> Dict[str, Any]:
        """Get a summary of the audit"""
        return {
            'correlation_id': self.correlation_id,
            'total_steps': len(self.steps),
            'total_decisions': len(self.decisions),
            'total_plans': len(self.plans),
            'current_step': self.current_step.step_name if self.current_step else None,
            'duration': time.time() - self.start_time,
            'audit_file': str(self.audit_file)
        }

# Global audit logger instance
_audit_logger: Optional[AskPipelineAuditLogger] = None

def get_audit_logger() -> Optional[AskPipelineAuditLogger]:
    """Get the current audit logger instance"""
    return _audit_logger

def set_audit_logger(logger: AskPipelineAuditLogger):
    """Set the current audit logger instance"""
    global _audit_logger
    _audit_logger = logger

def create_audit_logger(correlation_id: str, user_id: str, query: str, audit_level: str = "normal") -> AskPipelineAuditLogger:
    """Create a new audit logger instance"""
    global _audit_logger
    _audit_logger = AskPipelineAuditLogger(correlation_id, user_id, query, audit_level)
    return _audit_logger
