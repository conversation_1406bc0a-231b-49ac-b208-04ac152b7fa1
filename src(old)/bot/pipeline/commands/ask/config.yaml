# ASK Pipeline Configuration
# This file contains the default configuration for the new simplified ASK pipeline

ask_pipeline:
  # Intent Detection Configuration
  intent_detection:
    model: "fast_model"           # AI model for intent classification
    timeout: 1.0                  # Maximum time for intent detection (seconds)
    cache_ttl: 300               # Cache time-to-live (seconds)
    confidence_threshold: 0.7     # Minimum confidence for intent classification

  # MCP Tools Configuration
  tools:
    mcp_enabled: true            # Enable MCP tool integration
    parallel_execution: true     # Execute tools in parallel
    timeout: 5.0                 # Maximum time for tool execution (seconds)
    max_concurrent: 3            # Maximum concurrent tool executions
    rate_limit_per_minute: 60    # Rate limit for tool calls

  # AI Response Generation Configuration
  response:
    model: "quality_model"       # AI model for response generation
    max_tokens: 1000             # Maximum tokens in response
    temperature: 0.3             # AI temperature (creativity vs consistency)
    timeout: 10.0                # Maximum time for response generation (seconds)

  # Caching Configuration
  cache:
    enabled: true                # Enable caching system
    ttl: 600                     # Default cache time-to-live (seconds)
    max_size: 1000               # Maximum cache entries
    redis_url: null              # Redis URL (optional, uses memory cache if null)

  # Monitoring and Logging Configuration
  monitoring:
    log_level: "INFO"            # Logging level (DEBUG, INFO, WARNING, ERROR)
    metrics_enabled: true        # Enable performance metrics
    correlation_tracking: true   # Enable correlation ID tracking
    performance_tracking: true   # Enable detailed performance tracking

  # Performance Targets
  max_response_time: 2.0         # Target maximum response time (seconds)
  target_success_rate: 0.99      # Target success rate (0.0 - 1.0)

# Environment-specific overrides
development:
  ask_pipeline:
    monitoring:
      log_level: "DEBUG"
    tools:
      timeout: 10.0              # Longer timeout for development
    response:
      timeout: 15.0

production:
  ask_pipeline:
    monitoring:
      log_level: "WARNING"
    cache:
      redis_url: "${REDIS_URL}"  # Use Redis in production
    tools:
      rate_limit_per_minute: 120 # Higher rate limit in production
