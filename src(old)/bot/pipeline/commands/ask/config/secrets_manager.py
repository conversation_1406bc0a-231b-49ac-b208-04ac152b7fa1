"""
Secrets Management System for ASK Pipeline

Provides secure secrets management:
- Secure API key storage and rotation
- Encrypted configuration file support
- Runtime secrets injection and validation
- Secrets audit logging and access tracking
- Secrets management documentation and procedures
"""

import os
import json
import base64
import hashlib
import logging
from typing import Dict, Any, Optional, List, Union, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timed<PERSON>ta
from enum import Enum
from pathlib import Path
import threading
import time
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import secrets

logger = logging.getLogger(__name__)

class SecretType(Enum):
    """Types of secrets"""
    API_KEY = "api_key"
    PASSWORD = "password"
    TOKEN = "token"
    CERTIFICATE = "certificate"
    PRIVATE_KEY = "private_key"
    DATABASE_URL = "database_url"
    ENCRYPTION_KEY = "encryption_key"

class SecretStatus(Enum):
    """Secret status"""
    ACTIVE = "active"
    EXPIRED = "expired"
    ROTATED = "rotated"
    REVOKED = "revoked"

@dataclass
class SecretMetadata:
    """Secret metadata"""
    name: str
    secret_type: SecretType
    status: SecretStatus
    created_at: datetime
    expires_at: Optional[datetime] = None
    last_accessed: Optional[datetime] = None
    access_count: int = 0
    rotation_count: int = 0
    tags: List[str] = field(default_factory=list)
    description: str = ""

@dataclass
class SecretAccess:
    """Secret access record"""
    secret_name: str
    accessed_at: datetime
    accessed_by: str
    operation: str
    success: bool
    error_message: Optional[str] = None

class SecretsManager:
    """Secure secrets management system"""
    
    def __init__(self, master_key: Optional[str] = None, secrets_file: str = "secrets.json"):
        self.master_key = master_key or self._generate_master_key()
        self.secrets_file = Path(secrets_file)
        self.fernet = self._create_fernet()
        self.secrets: Dict[str, bytes] = {}
        self.metadata: Dict[str, SecretMetadata] = {}
        self.access_log: List[SecretAccess] = []
        self._lock = threading.RLock()
        
        # Load existing secrets
        self._load_secrets()
    
    def _generate_master_key(self) -> str:
        """Generate master key for encryption"""
        return Fernet.generate_key().decode()
    
    def _create_fernet(self) -> Fernet:
        """Create Fernet encryption instance"""
        # Derive key from master key
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=b'ask_pipeline_salt',  # In production, use random salt
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(self.master_key.encode()))
        return Fernet(key)
    
    def _load_secrets(self):
        """Load secrets from file"""
        try:
            if not self.secrets_file.exists():
                logger.info("No secrets file found, starting with empty secrets")
                return
            
            with open(self.secrets_file, 'r') as f:
                data = json.load(f)
            
            # Decrypt secrets
            for name, encrypted_secret in data.get('secrets', {}).items():
                try:
                    decrypted = self.fernet.decrypt(encrypted_secret.encode())
                    self.secrets[name] = decrypted
                except Exception as e:
                    logger.error(f"Failed to decrypt secret {name}: {e}")
            
            # Load metadata
            for name, meta_data in data.get('metadata', {}).items():
                try:
                    self.metadata[name] = SecretMetadata(
                        name=meta_data['name'],
                        secret_type=SecretType(meta_data['secret_type']),
                        status=SecretStatus(meta_data['status']),
                        created_at=datetime.fromisoformat(meta_data['created_at']),
                        expires_at=datetime.fromisoformat(meta_data['expires_at']) if meta_data.get('expires_at') else None,
                        last_accessed=datetime.fromisoformat(meta_data['last_accessed']) if meta_data.get('last_accessed') else None,
                        access_count=meta_data.get('access_count', 0),
                        rotation_count=meta_data.get('rotation_count', 0),
                        tags=meta_data.get('tags', []),
                        description=meta_data.get('description', '')
                    )
                except Exception as e:
                    logger.error(f"Failed to load metadata for secret {name}: {e}")
            
            # Load access log
            for access_data in data.get('access_log', []):
                try:
                    self.access_log.append(SecretAccess(
                        secret_name=access_data['secret_name'],
                        accessed_at=datetime.fromisoformat(access_data['accessed_at']),
                        accessed_by=access_data['accessed_by'],
                        operation=access_data['operation'],
                        success=access_data['success'],
                        error_message=access_data.get('error_message')
                    ))
                except Exception as e:
                    logger.error(f"Failed to load access log entry: {e}")
            
            logger.info(f"Loaded {len(self.secrets)} secrets from {self.secrets_file}")
            
        except Exception as e:
            logger.error(f"Failed to load secrets: {e}")
    
    def _save_secrets(self):
        """Save secrets to file"""
        try:
            with self._lock:
                # Encrypt secrets
                encrypted_secrets = {}
                for name, secret in self.secrets.items():
                    encrypted = self.fernet.encrypt(secret)
                    encrypted_secrets[name] = encrypted.decode()
                
                # Prepare metadata
                metadata_dict = {}
                for name, meta in self.metadata.items():
                    metadata_dict[name] = {
                        'name': meta.name,
                        'secret_type': meta.secret_type.value,
                        'status': meta.status.value,
                        'created_at': meta.created_at.isoformat(),
                        'expires_at': meta.expires_at.isoformat() if meta.expires_at else None,
                        'last_accessed': meta.last_accessed.isoformat() if meta.last_accessed else None,
                        'access_count': meta.access_count,
                        'rotation_count': meta.rotation_count,
                        'tags': meta.tags,
                        'description': meta.description
                    }
                
                # Prepare access log
                access_log_dict = []
                for access in self.access_log[-1000:]:  # Keep last 1000 entries
                    access_log_dict.append({
                        'secret_name': access.secret_name,
                        'accessed_at': access.accessed_at.isoformat(),
                        'accessed_by': access.accessed_by,
                        'operation': access.operation,
                        'success': access.success,
                        'error_message': access.error_message
                    })
                
                # Save to file
                data = {
                    'secrets': encrypted_secrets,
                    'metadata': metadata_dict,
                    'access_log': access_log_dict,
                    'last_updated': datetime.utcnow().isoformat()
                }
                
                with open(self.secrets_file, 'w') as f:
                    json.dump(data, f, indent=2)
                
                logger.debug("Secrets saved to file")
                
        except Exception as e:
            logger.error(f"Failed to save secrets: {e}")
    
    def store_secret(self, name: str, secret: str, secret_type: SecretType, 
                    expires_at: Optional[datetime] = None, tags: List[str] = None,
                    description: str = "") -> bool:
        """Store a secret"""
        try:
            with self._lock:
                # Validate secret
                if not self._validate_secret(secret, secret_type):
                    return False
                
                # Store secret
                self.secrets[name] = secret.encode()
                
                # Create metadata
                self.metadata[name] = SecretMetadata(
                    name=name,
                    secret_type=secret_type,
                    status=SecretStatus.ACTIVE,
                    created_at=datetime.utcnow(),
                    expires_at=expires_at,
                    tags=tags or [],
                    description=description
                )
                
                # Log access
                self._log_access(name, "store", True)
                
                # Save to file
                self._save_secrets()
                
                logger.info(f"Secret stored: {name}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to store secret {name}: {e}")
            self._log_access(name, "store", False, str(e))
            return False
    
    def get_secret(self, name: str, accessed_by: str = "system") -> Optional[str]:
        """Get a secret"""
        try:
            with self._lock:
                if name not in self.secrets:
                    self._log_access(name, "get", False, "Secret not found")
                    return None
                
                # Check if secret is expired
                metadata = self.metadata.get(name)
                if metadata and metadata.expires_at and metadata.expires_at < datetime.utcnow():
                    metadata.status = SecretStatus.EXPIRED
                    self._log_access(name, "get", False, "Secret expired")
                    return None
                
                # Check if secret is revoked
                if metadata and metadata.status == SecretStatus.REVOKED:
                    self._log_access(name, "get", False, "Secret revoked")
                    return None
                
                # Get secret
                secret = self.secrets[name].decode()
                
                # Update metadata
                if metadata:
                    metadata.last_accessed = datetime.utcnow()
                    metadata.access_count += 1
                
                # Log access
                self._log_access(name, "get", True)
                
                logger.debug(f"Secret accessed: {name}")
                return secret
                
        except Exception as e:
            logger.error(f"Failed to get secret {name}: {e}")
            self._log_access(name, "get", False, str(e))
            return None
    
    def rotate_secret(self, name: str, new_secret: str, rotated_by: str = "system") -> bool:
        """Rotate a secret"""
        try:
            with self._lock:
                if name not in self.secrets:
                    self._log_access(name, "rotate", False, "Secret not found")
                    return False
                
                # Validate new secret
                metadata = self.metadata.get(name)
                if not metadata:
                    self._log_access(name, "rotate", False, "Metadata not found")
                    return False
                
                if not self._validate_secret(new_secret, metadata.secret_type):
                    self._log_access(name, "rotate", False, "Invalid new secret")
                    return False
                
                # Store old secret for rollback
                old_secret = self.secrets[name]
                
                # Update secret
                self.secrets[name] = new_secret.encode()
                
                # Update metadata
                metadata.status = SecretStatus.ROTATED
                metadata.rotation_count += 1
                
                # Log access
                self._log_access(name, "rotate", True)
                
                # Save to file
                self._save_secrets()
                
                logger.info(f"Secret rotated: {name}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to rotate secret {name}: {e}")
            self._log_access(name, "rotate", False, str(e))
            return False
    
    def revoke_secret(self, name: str, revoked_by: str = "system") -> bool:
        """Revoke a secret"""
        try:
            with self._lock:
                if name not in self.secrets:
                    self._log_access(name, "revoke", False, "Secret not found")
                    return False
                
                # Update metadata
                metadata = self.metadata.get(name)
                if metadata:
                    metadata.status = SecretStatus.REVOKED
                
                # Log access
                self._log_access(name, "revoke", True)
                
                # Save to file
                self._save_secrets()
                
                logger.info(f"Secret revoked: {name}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to revoke secret {name}: {e}")
            self._log_access(name, "revoke", False, str(e))
            return False
    
    def delete_secret(self, name: str, deleted_by: str = "system") -> bool:
        """Delete a secret"""
        try:
            with self._lock:
                if name not in self.secrets:
                    self._log_access(name, "delete", False, "Secret not found")
                    return False
                
                # Remove secret and metadata
                del self.secrets[name]
                if name in self.metadata:
                    del self.metadata[name]
                
                # Log access
                self._log_access(name, "delete", True)
                
                # Save to file
                self._save_secrets()
                
                logger.info(f"Secret deleted: {name}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to delete secret {name}: {e}")
            self._log_access(name, "delete", False, str(e))
            return False
    
    def list_secrets(self, secret_type: Optional[SecretType] = None, 
                    status: Optional[SecretStatus] = None) -> List[SecretMetadata]:
        """List secrets with optional filtering"""
        with self._lock:
            secrets = []
            for metadata in self.metadata.values():
                if secret_type and metadata.secret_type != secret_type:
                    continue
                if status and metadata.status != status:
                    continue
                secrets.append(metadata)
            return secrets
    
    def get_secret_metadata(self, name: str) -> Optional[SecretMetadata]:
        """Get secret metadata"""
        with self._lock:
            return self.metadata.get(name)
    
    def _validate_secret(self, secret: str, secret_type: SecretType) -> bool:
        """Validate secret based on type"""
        if not secret or len(secret) < 1:
            return False
        
        if secret_type == SecretType.API_KEY:
            # API keys should be alphanumeric with some special chars
            return len(secret) >= 8 and any(c.isalnum() for c in secret)
        elif secret_type == SecretType.PASSWORD:
            # Passwords should be at least 8 characters
            return len(secret) >= 8
        elif secret_type == SecretType.TOKEN:
            # Tokens should be alphanumeric
            return len(secret) >= 16 and secret.replace('-', '').replace('_', '').isalnum()
        elif secret_type == SecretType.DATABASE_URL:
            # Database URLs should start with protocol
            return secret.startswith(('postgresql://', 'mysql://', 'sqlite://', 'mongodb://'))
        
        return True
    
    def _log_access(self, secret_name: str, operation: str, success: bool, 
                   error_message: Optional[str] = None):
        """Log secret access"""
        access = SecretAccess(
            secret_name=secret_name,
            accessed_at=datetime.utcnow(),
            accessed_by="system",  # In production, get from context
            operation=operation,
            success=success,
            error_message=error_message
        )
        
        self.access_log.append(access)
        
        # Keep only last 10000 entries
        if len(self.access_log) > 10000:
            self.access_log = self.access_log[-10000:]
    
    def get_access_log(self, secret_name: Optional[str] = None, 
                      limit: int = 100) -> List[SecretAccess]:
        """Get access log"""
        with self._lock:
            log = self.access_log
            if secret_name:
                log = [access for access in log if access.secret_name == secret_name]
            return log[-limit:]
    
    def get_secrets_summary(self) -> Dict[str, Any]:
        """Get secrets summary"""
        with self._lock:
            total_secrets = len(self.secrets)
            active_secrets = sum(1 for meta in self.metadata.values() if meta.status == SecretStatus.ACTIVE)
            expired_secrets = sum(1 for meta in self.metadata.values() if meta.status == SecretStatus.EXPIRED)
            revoked_secrets = sum(1 for meta in self.metadata.values() if meta.status == SecretStatus.REVOKED)
            
            secret_types = {}
            for meta in self.metadata.values():
                secret_type = meta.secret_type.value
                secret_types[secret_type] = secret_types.get(secret_type, 0) + 1
            
            return {
                "total_secrets": total_secrets,
                "active_secrets": active_secrets,
                "expired_secrets": expired_secrets,
                "revoked_secrets": revoked_secrets,
                "secret_types": secret_types,
                "total_access_log_entries": len(self.access_log)
            }
    
    def cleanup_expired_secrets(self) -> int:
        """Cleanup expired secrets"""
        cleaned_count = 0
        
        with self._lock:
            expired_secrets = []
            for name, metadata in self.metadata.items():
                if (metadata.status == SecretStatus.EXPIRED and 
                    metadata.expires_at and 
                    metadata.expires_at < datetime.utcnow() - timedelta(days=30)):
                    expired_secrets.append(name)
            
            for name in expired_secrets:
                if self.delete_secret(name, "cleanup"):
                    cleaned_count += 1
        
        logger.info(f"Cleaned up {cleaned_count} expired secrets")
        return cleaned_count

# Global secrets manager
_secrets_manager: Optional[SecretsManager] = None

def get_secrets_manager(master_key: Optional[str] = None) -> SecretsManager:
    """Get global secrets manager"""
    global _secrets_manager
    if _secrets_manager is None:
        _secrets_manager = SecretsManager(master_key)
    return _secrets_manager

def cleanup_secrets_manager():
    """Cleanup global secrets manager"""
    global _secrets_manager
    if _secrets_manager:
        _secrets_manager = None
