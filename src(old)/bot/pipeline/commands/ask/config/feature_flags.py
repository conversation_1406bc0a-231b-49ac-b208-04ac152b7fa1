"""
Feature Flag System for ASK Pipeline

Provides feature flag management:
- Feature flag system for gradual rollouts
- A/B testing capabilities
- Feature toggle management
- User and guild targeting
- Feature flag analytics and monitoring
"""

import json
import logging
import hashlib
import time
from typing import Dict, Any, Optional, List, Union, Callable
from dataclasses import dataclass, field, asdict
from datetime import datetime, timed<PERSON>ta
from enum import Enum
import threading
import asyncio

logger = logging.getLogger(__name__)

class FeatureFlagStatus(Enum):
    """Feature flag status"""
    ENABLED = "enabled"
    DISABLED = "disabled"
    ROLLING_OUT = "rolling_out"
    ROLLED_BACK = "rolled_back"

class TargetingType(Enum):
    """Targeting type"""
    ALL = "all"
    PERCENTAGE = "percentage"
    USER_LIST = "user_list"
    GUILD_LIST = "guild_list"
    CONDITIONAL = "conditional"

@dataclass
class FeatureFlag:
    """Feature flag configuration"""
    name: str
    description: str
    status: FeatureFlagStatus
    rollout_percentage: float = 0.0
    targeting_type: TargetingType = TargetingType.ALL
    target_users: List[str] = field(default_factory=list)
    target_guilds: List[str] = field(default_factory=list)
    conditions: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)
    created_by: str = "system"
    expires_at: Optional[datetime] = None
    tags: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class FeatureFlagEvaluation:
    """Feature flag evaluation result"""
    flag_name: str
    user_id: str
    guild_id: Optional[str]
    enabled: bool
    reason: str
    evaluated_at: datetime
    rollout_percentage: float
    targeting_type: TargetingType

@dataclass
class FeatureFlagAnalytics:
    """Feature flag analytics"""
    flag_name: str
    total_evaluations: int
    enabled_evaluations: int
    disabled_evaluations: int
    unique_users: int
    unique_guilds: int
    evaluation_rate: float
    last_evaluated: Optional[datetime]

class FeatureFlagManager:
    """Feature flag management system"""
    
    def __init__(self, config_file: str = "feature_flags.json"):
        self.config_file = config_file
        self.flags: Dict[str, FeatureFlag] = {}
        self.evaluations: List[FeatureFlagEvaluation] = []
        self.analytics: Dict[str, FeatureFlagAnalytics] = {}
        self._lock = threading.RLock()
        self._watchers: List[Callable] = []
        
        # Load existing flags
        self._load_flags()
    
    def _load_flags(self):
        """Load feature flags from file"""
        try:
            import json
            from pathlib import Path
            
            config_path = Path(self.config_file)
            if not config_path.exists():
                logger.info("No feature flags file found, starting with empty flags")
                return
            
            with open(config_path, 'r') as f:
                data = json.load(f)
            
            for flag_data in data.get('flags', []):
                try:
                    flag = FeatureFlag(
                        name=flag_data['name'],
                        description=flag_data['description'],
                        status=FeatureFlagStatus(flag_data['status']),
                        rollout_percentage=flag_data.get('rollout_percentage', 0.0),
                        targeting_type=TargetingType(flag_data.get('targeting_type', 'all')),
                        target_users=flag_data.get('target_users', []),
                        target_guilds=flag_data.get('target_guilds', []),
                        conditions=flag_data.get('conditions', {}),
                        created_at=datetime.fromisoformat(flag_data['created_at']),
                        updated_at=datetime.fromisoformat(flag_data['updated_at']),
                        created_by=flag_data.get('created_by', 'system'),
                        expires_at=datetime.fromisoformat(flag_data['expires_at']) if flag_data.get('expires_at') else None,
                        tags=flag_data.get('tags', []),
                        metadata=flag_data.get('metadata', {})
                    )
                    self.flags[flag.name] = flag
                except Exception as e:
                    logger.error(f"Failed to load feature flag {flag_data.get('name', 'unknown')}: {e}")
            
            logger.info(f"Loaded {len(self.flags)} feature flags from {self.config_file}")
            
        except Exception as e:
            logger.error(f"Failed to load feature flags: {e}")
    
    def _save_flags(self):
        """Save feature flags to file"""
        try:
            with self._lock:
                flags_data = []
                for flag in self.flags.values():
                    flag_data = asdict(flag)
                    # Convert datetime objects to ISO format
                    flag_data['created_at'] = flag.created_at.isoformat()
                    flag_data['updated_at'] = flag.updated_at.isoformat()
                    if flag.expires_at:
                        flag_data['expires_at'] = flag.expires_at.isoformat()
                    flags_data.append(flag_data)
                
                data = {
                    'flags': flags_data,
                    'last_updated': datetime.utcnow().isoformat()
                }
                
                with open(self.config_file, 'w') as f:
                    json.dump(data, f, indent=2)
                
                logger.debug("Feature flags saved to file")
                
        except Exception as e:
            logger.error(f"Failed to save feature flags: {e}")
    
    def create_flag(self, name: str, description: str, created_by: str = "system",
                   tags: List[str] = None, metadata: Dict[str, Any] = None) -> bool:
        """Create a new feature flag"""
        try:
            with self._lock:
                if name in self.flags:
                    logger.warning(f"Feature flag {name} already exists")
                    return False
                
                flag = FeatureFlag(
                    name=name,
                    description=description,
                    status=FeatureFlagStatus.DISABLED,
                    created_by=created_by,
                    tags=tags or [],
                    metadata=metadata or {}
                )
                
                self.flags[name] = flag
                self._save_flags()
                self._notify_watchers(name, flag)
                
                logger.info(f"Feature flag created: {name}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to create feature flag {name}: {e}")
            return False
    
    def update_flag(self, name: str, **kwargs) -> bool:
        """Update a feature flag"""
        try:
            with self._lock:
                if name not in self.flags:
                    logger.warning(f"Feature flag {name} not found")
                    return False
                
                flag = self.flags[name]
                
                # Update allowed fields
                allowed_fields = {
                    'description', 'status', 'rollout_percentage', 'targeting_type',
                    'target_users', 'target_guilds', 'conditions', 'expires_at',
                    'tags', 'metadata'
                }
                
                for key, value in kwargs.items():
                    if key in allowed_fields:
                        if key == 'status' and isinstance(value, str):
                            value = FeatureFlagStatus(value)
                        elif key == 'targeting_type' and isinstance(value, str):
                            value = TargetingType(value)
                        setattr(flag, key, value)
                
                flag.updated_at = datetime.utcnow()
                
                self._save_flags()
                self._notify_watchers(name, flag)
                
                logger.info(f"Feature flag updated: {name}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to update feature flag {name}: {e}")
            return False
    
    def delete_flag(self, name: str) -> bool:
        """Delete a feature flag"""
        try:
            with self._lock:
                if name not in self.flags:
                    logger.warning(f"Feature flag {name} not found")
                    return False
                
                del self.flags[name]
                self._save_flags()
                self._notify_watchers(name, None)
                
                logger.info(f"Feature flag deleted: {name}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to delete feature flag {name}: {e}")
            return False
    
    def get_flag(self, name: str) -> Optional[FeatureFlag]:
        """Get a feature flag"""
        with self._lock:
            return self.flags.get(name)
    
    def list_flags(self, status: Optional[FeatureFlagStatus] = None,
                  tags: Optional[List[str]] = None) -> List[FeatureFlag]:
        """List feature flags with optional filtering"""
        with self._lock:
            flags = list(self.flags.values())
            
            if status:
                flags = [flag for flag in flags if flag.status == status]
            
            if tags:
                flags = [flag for flag in flags if any(tag in flag.tags for tag in tags)]
            
            return flags
    
    def evaluate_flag(self, name: str, user_id: str, guild_id: Optional[str] = None) -> FeatureFlagEvaluation:
        """Evaluate a feature flag for a user"""
        try:
            with self._lock:
                flag = self.flags.get(name)
                if not flag:
                    return FeatureFlagEvaluation(
                        flag_name=name,
                        user_id=user_id,
                        guild_id=guild_id,
                        enabled=False,
                        reason="Flag not found",
                        evaluated_at=datetime.utcnow(),
                        rollout_percentage=0.0,
                        targeting_type=TargetingType.ALL
                    )
                
                # Check if flag is expired
                if flag.expires_at and flag.expires_at < datetime.utcnow():
                    return FeatureFlagEvaluation(
                        flag_name=name,
                        user_id=user_id,
                        guild_id=guild_id,
                        enabled=False,
                        reason="Flag expired",
                        evaluated_at=datetime.utcnow(),
                        rollout_percentage=flag.rollout_percentage,
                        targeting_type=flag.targeting_type
                    )
                
                # Check status
                if flag.status == FeatureFlagStatus.DISABLED:
                    enabled = False
                    reason = "Flag disabled"
                elif flag.status == FeatureFlagStatus.ENABLED:
                    enabled = True
                    reason = "Flag enabled"
                elif flag.status == FeatureFlagStatus.ROLLED_BACK:
                    enabled = False
                    reason = "Flag rolled back"
                else:  # ROLLING_OUT
                    enabled = self._evaluate_rollout(flag, user_id, guild_id)
                    reason = "Rollout evaluation"
                
                evaluation = FeatureFlagEvaluation(
                    flag_name=name,
                    user_id=user_id,
                    guild_id=guild_id,
                    enabled=enabled,
                    reason=reason,
                    evaluated_at=datetime.utcnow(),
                    rollout_percentage=flag.rollout_percentage,
                    targeting_type=flag.targeting_type
                )
                
                # Store evaluation
                self.evaluations.append(evaluation)
                
                # Update analytics
                self._update_analytics(evaluation)
                
                logger.debug(f"Feature flag {name} evaluated for user {user_id}: {enabled}")
                return evaluation
                
        except Exception as e:
            logger.error(f"Failed to evaluate feature flag {name}: {e}")
            return FeatureFlagEvaluation(
                flag_name=name,
                user_id=user_id,
                guild_id=guild_id,
                enabled=False,
                reason=f"Evaluation error: {e}",
                evaluated_at=datetime.utcnow(),
                rollout_percentage=0.0,
                targeting_type=TargetingType.ALL
            )
    
    def _evaluate_rollout(self, flag: FeatureFlag, user_id: str, guild_id: Optional[str]) -> bool:
        """Evaluate rollout for a feature flag"""
        if flag.targeting_type == TargetingType.ALL:
            return True
        elif flag.targeting_type == TargetingType.PERCENTAGE:
            return self._evaluate_percentage_rollout(flag, user_id)
        elif flag.targeting_type == TargetingType.USER_LIST:
            return user_id in flag.target_users
        elif flag.targeting_type == TargetingType.GUILD_LIST:
            return guild_id in flag.target_guilds if guild_id else False
        elif flag.targeting_type == TargetingType.CONDITIONAL:
            return self._evaluate_conditional_rollout(flag, user_id, guild_id)
        
        return False
    
    def _evaluate_percentage_rollout(self, flag: FeatureFlag, user_id: str) -> bool:
        """Evaluate percentage-based rollout"""
        # Use consistent hashing for stable rollout
        hash_input = f"{flag.name}:{user_id}"
        hash_value = int(hashlib.md5(hash_input.encode()).hexdigest(), 16)
        percentage = (hash_value % 100) + 1
        
        return percentage <= flag.rollout_percentage
    
    def _evaluate_conditional_rollout(self, flag: FeatureFlag, user_id: str, guild_id: Optional[str]) -> bool:
        """Evaluate conditional rollout"""
        conditions = flag.conditions
        
        # Check user conditions
        if 'user_conditions' in conditions:
            user_conditions = conditions['user_conditions']
            # This would integrate with user data in a real implementation
            pass
        
        # Check guild conditions
        if 'guild_conditions' in conditions and guild_id:
            guild_conditions = conditions['guild_conditions']
            # This would integrate with guild data in a real implementation
            pass
        
        # Check time conditions
        if 'time_conditions' in conditions:
            time_conditions = conditions['time_conditions']
            current_hour = datetime.utcnow().hour
            if 'allowed_hours' in time_conditions:
                if current_hour not in time_conditions['allowed_hours']:
                    return False
        
        return True
    
    def _update_analytics(self, evaluation: FeatureFlagEvaluation):
        """Update feature flag analytics"""
        flag_name = evaluation.flag_name
        
        if flag_name not in self.analytics:
            self.analytics[flag_name] = FeatureFlagAnalytics(
                flag_name=flag_name,
                total_evaluations=0,
                enabled_evaluations=0,
                disabled_evaluations=0,
                unique_users=set(),
                unique_guilds=set(),
                evaluation_rate=0.0,
                last_evaluated=None
            )
        
        analytics = self.analytics[flag_name]
        analytics.total_evaluations += 1
        
        if evaluation.enabled:
            analytics.enabled_evaluations += 1
        else:
            analytics.disabled_evaluations += 1
        
        analytics.unique_users.add(evaluation.user_id)
        if evaluation.guild_id:
            analytics.unique_guilds.add(evaluation.guild_id)
        
        analytics.last_evaluated = evaluation.evaluated_at
        
        # Calculate evaluation rate (evaluations per minute)
        recent_evaluations = [
            e for e in self.evaluations 
            if e.flag_name == flag_name and 
            e.evaluated_at > datetime.utcnow() - timedelta(minutes=1)
        ]
        analytics.evaluation_rate = len(recent_evaluations)
    
    def get_analytics(self, flag_name: str) -> Optional[FeatureFlagAnalytics]:
        """Get feature flag analytics"""
        with self._lock:
            analytics = self.analytics.get(flag_name)
            if analytics:
                # Convert sets to counts for JSON serialization
                return FeatureFlagAnalytics(
                    flag_name=analytics.flag_name,
                    total_evaluations=analytics.total_evaluations,
                    enabled_evaluations=analytics.enabled_evaluations,
                    disabled_evaluations=analytics.disabled_evaluations,
                    unique_users=len(analytics.unique_users),
                    unique_guilds=len(analytics.unique_guilds),
                    evaluation_rate=analytics.evaluation_rate,
                    last_evaluated=analytics.last_evaluated
                )
            return None
    
    def get_all_analytics(self) -> Dict[str, FeatureFlagAnalytics]:
        """Get all feature flag analytics"""
        with self._lock:
            result = {}
            for flag_name, analytics in self.analytics.items():
                result[flag_name] = FeatureFlagAnalytics(
                    flag_name=analytics.flag_name,
                    total_evaluations=analytics.total_evaluations,
                    enabled_evaluations=analytics.enabled_evaluations,
                    disabled_evaluations=analytics.disabled_evaluations,
                    unique_users=len(analytics.unique_users),
                    unique_guilds=len(analytics.unique_guilds),
                    evaluation_rate=analytics.evaluation_rate,
                    last_evaluated=analytics.last_evaluated
                )
            return result
    
    def add_watcher(self, watcher: Callable[[str, Optional[FeatureFlag]], None]):
        """Add feature flag watcher"""
        self._watchers.append(watcher)
    
    def remove_watcher(self, watcher: Callable[[str, Optional[FeatureFlag]], None]):
        """Remove feature flag watcher"""
        if watcher in self._watchers:
            self._watchers.remove(watcher)
    
    def _notify_watchers(self, flag_name: str, flag: Optional[FeatureFlag]):
        """Notify feature flag watchers"""
        for watcher in self._watchers:
            try:
                watcher(flag_name, flag)
            except Exception as e:
                logger.error(f"Feature flag watcher error: {e}")
    
    def cleanup_old_evaluations(self, days: int = 30):
        """Cleanup old evaluations"""
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        with self._lock:
            original_count = len(self.evaluations)
            self.evaluations = [
                e for e in self.evaluations 
                if e.evaluated_at > cutoff_date
            ]
            cleaned_count = original_count - len(self.evaluations)
            
            logger.info(f"Cleaned up {cleaned_count} old evaluations")
            return cleaned_count

# Global feature flag manager
_feature_flag_manager: Optional[FeatureFlagManager] = None

def get_feature_flag_manager() -> FeatureFlagManager:
    """Get global feature flag manager"""
    global _feature_flag_manager
    if _feature_flag_manager is None:
        _feature_flag_manager = FeatureFlagManager()
    return _feature_flag_manager

def cleanup_feature_flag_manager():
    """Cleanup global feature flag manager"""
    global _feature_flag_manager
    if _feature_flag_manager:
        _feature_flag_manager = None
