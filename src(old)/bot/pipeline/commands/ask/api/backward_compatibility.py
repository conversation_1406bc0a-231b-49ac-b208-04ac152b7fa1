"""
Backward Compatibility for ASK Pipeline

Provides backward compatibility capabilities:
- Maintain existing executor interface for legacy code
- Implement gradual migration strategy with feature flags
- Add compatibility testing for existing integrations
- Create migration documentation and tooling
- Implement rollback procedures for failed migrations
"""

import asyncio
import logging
import warnings
from typing import Dict, Any, Optional, List, Union, Callable, Type
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import json
import inspect
from functools import wraps

from .contracts import BaseRequest, BaseResponse, ErrorResponse, ErrorCode
from .versioning import APIVersion, get_version_manager

logger = logging.getLogger(__name__)

class CompatibilityLevel(Enum):
    """Compatibility level enumeration"""
    FULL = "full"
    PARTIAL = "partial"
    NONE = "none"

class MigrationStatus(Enum):
    """Migration status enumeration"""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    ROLLED_BACK = "rolled_back"

@dataclass
class CompatibilityInfo:
    """Compatibility information"""
    interface_name: str
    version: str
    compatibility_level: CompatibilityLevel
    deprecated: bool = False
    deprecation_date: Optional[datetime] = None
    sunset_date: Optional[datetime] = None
    migration_guide: Optional[str] = None
    breaking_changes: List[str] = field(default_factory=list)
    workarounds: List[str] = field(default_factory=list)

@dataclass
class MigrationPlan:
    """Migration plan"""
    plan_id: str
    from_version: str
    to_version: str
    status: MigrationStatus
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    steps: List[Dict[str, Any]] = field(default_factory=list)
    rollback_steps: List[Dict[str, Any]] = field(default_factory=list)
    feature_flags: List[str] = field(default_factory=list)

class BackwardCompatibilityManager:
    """Backward compatibility management system"""
    
    def __init__(self):
        self.compatibility_info: Dict[str, CompatibilityInfo] = {}
        self.migration_plans: Dict[str, MigrationPlan] = {}
        self.legacy_interfaces: Dict[str, Callable] = {}
        self.migration_scripts: Dict[str, Callable] = {}
        self.rollback_scripts: Dict[str, Callable] = {}
        self.compatibility_tests: Dict[str, Callable] = {}
        
        # Initialize default compatibility info
        self._initialize_compatibility_info()
    
    def _initialize_compatibility_info(self):
        """Initialize default compatibility information"""
        # Legacy executor interface
        self.compatibility_info["executor"] = CompatibilityInfo(
            interface_name="execute_ask_pipeline",
            version="1.0.0",
            compatibility_level=CompatibilityLevel.FULL,
            deprecated=False,
            migration_guide="https://docs.ask-pipeline.com/migration/executor"
        )
        
        # Legacy configuration interface
        self.compatibility_info["config"] = CompatibilityInfo(
            interface_name="get_config",
            version="1.0.0",
            compatibility_level=CompatibilityLevel.PARTIAL,
            deprecated=True,
            deprecation_date=datetime(2024, 6, 1),
            sunset_date=datetime(2024, 12, 1),
            breaking_changes=[
                "Configuration structure changed",
                "New required parameters added"
            ],
            workarounds=[
                "Use new ConfigurationManager",
                "Update configuration calls"
            ],
            migration_guide="https://docs.ask-pipeline.com/migration/config"
        )
        
        # Legacy security interface
        self.compatibility_info["security"] = CompatibilityInfo(
            interface_name="validate_input",
            version="1.0.0",
            compatibility_level=CompatibilityLevel.FULL,
            deprecated=False,
            migration_guide="https://docs.ask-pipeline.com/migration/security"
        )
    
    def register_legacy_interface(self, name: str, interface: Callable, compatibility_info: CompatibilityInfo):
        """Register legacy interface"""
        self.legacy_interfaces[name] = interface
        self.compatibility_info[name] = compatibility_info
        logger.info(f"Registered legacy interface: {name}")
    
    def register_migration_script(self, name: str, script: Callable):
        """Register migration script"""
        self.migration_scripts[name] = script
        logger.info(f"Registered migration script: {name}")
    
    def register_rollback_script(self, name: str, script: Callable):
        """Register rollback script"""
        self.rollback_scripts[name] = script
        logger.info(f"Registered rollback script: {name}")
    
    def register_compatibility_test(self, name: str, test: Callable):
        """Register compatibility test"""
        self.compatibility_tests[name] = test
        logger.info(f"Registered compatibility test: {name}")
    
    def create_migration_plan(self, from_version: str, to_version: str, steps: List[Dict[str, Any]]) -> str:
        """Create migration plan"""
        plan_id = f"migration_{from_version}_to_{to_version}_{int(datetime.utcnow().timestamp())}"
        
        plan = MigrationPlan(
            plan_id=plan_id,
            from_version=from_version,
            to_version=to_version,
            status=MigrationStatus.NOT_STARTED,
            created_at=datetime.utcnow(),
            steps=steps
        )
        
        self.migration_plans[plan_id] = plan
        logger.info(f"Created migration plan: {plan_id}")
        return plan_id
    
    async def execute_migration_plan(self, plan_id: str) -> bool:
        """Execute migration plan"""
        if plan_id not in self.migration_plans:
            raise ValueError(f"Migration plan {plan_id} not found")
        
        plan = self.migration_plans[plan_id]
        plan.status = MigrationStatus.IN_PROGRESS
        plan.started_at = datetime.utcnow()
        
        try:
            for step in plan.steps:
                step_name = step.get("name")
                step_type = step.get("type")
                
                logger.info(f"Executing migration step: {step_name}")
                
                if step_type == "script":
                    script_name = step.get("script")
                    if script_name in self.migration_scripts:
                        await self.migration_scripts[script_name](step.get("params", {}))
                    else:
                        raise ValueError(f"Migration script {script_name} not found")
                
                elif step_type == "feature_flag":
                    flag_name = step.get("flag")
                    # This would integrate with feature flag system
                    logger.info(f"Setting feature flag: {flag_name}")
                
                elif step_type == "validation":
                    test_name = step.get("test")
                    if test_name in self.compatibility_tests:
                        result = await self.compatibility_tests[test_name]()
                        if not result:
                            raise ValueError(f"Migration validation failed: {test_name}")
                    else:
                        raise ValueError(f"Compatibility test {test_name} not found")
                
                else:
                    logger.warning(f"Unknown migration step type: {step_type}")
            
            plan.status = MigrationStatus.COMPLETED
            plan.completed_at = datetime.utcnow()
            logger.info(f"Migration plan {plan_id} completed successfully")
            return True
            
        except Exception as e:
            plan.status = MigrationStatus.FAILED
            logger.error(f"Migration plan {plan_id} failed: {e}")
            return False
    
    async def rollback_migration_plan(self, plan_id: str) -> bool:
        """Rollback migration plan"""
        if plan_id not in self.migration_plans:
            raise ValueError(f"Migration plan {plan_id} not found")
        
        plan = self.migration_plans[plan_id]
        
        try:
            for step in reversed(plan.rollback_steps):
                step_name = step.get("name")
                step_type = step.get("type")
                
                logger.info(f"Executing rollback step: {step_name}")
                
                if step_type == "script":
                    script_name = step.get("script")
                    if script_name in self.rollback_scripts:
                        await self.rollback_scripts[script_name](step.get("params", {}))
                    else:
                        raise ValueError(f"Rollback script {script_name} not found")
                
                elif step_type == "feature_flag":
                    flag_name = step.get("flag")
                    # This would integrate with feature flag system
                    logger.info(f"Clearing feature flag: {flag_name}")
            
            plan.status = MigrationStatus.ROLLED_BACK
            logger.info(f"Migration plan {plan_id} rolled back successfully")
            return True
            
        except Exception as e:
            logger.error(f"Rollback of migration plan {plan_id} failed: {e}")
            return False
    
    def get_compatibility_info(self, interface_name: str) -> Optional[CompatibilityInfo]:
        """Get compatibility information for interface"""
        return self.compatibility_info.get(interface_name)
    
    def check_compatibility(self, interface_name: str, version: str) -> CompatibilityLevel:
        """Check compatibility level for interface and version"""
        info = self.compatibility_info.get(interface_name)
        if not info:
            return CompatibilityLevel.NONE
        
        # Simple version comparison (in production, use proper semver)
        if version == info.version:
            return info.compatibility_level
        
        return CompatibilityLevel.PARTIAL
    
    def get_deprecated_interfaces(self) -> List[str]:
        """Get list of deprecated interfaces"""
        return [
            name for name, info in self.compatibility_info.items()
            if info.deprecated
        ]
    
    def get_sunset_interfaces(self) -> List[str]:
        """Get list of interfaces that will be sunset soon"""
        cutoff_date = datetime.utcnow() + timedelta(days=30)
        return [
            name for name, info in self.compatibility_info.items()
            if info.sunset_date and info.sunset_date <= cutoff_date
        ]
    
    def get_migration_recommendations(self) -> List[Dict[str, Any]]:
        """Get migration recommendations"""
        recommendations = []
        
        for name, info in self.compatibility_info.items():
            if info.deprecated:
                recommendations.append({
                    "interface": name,
                    "reason": "deprecated",
                    "deprecation_date": info.deprecation_date,
                    "sunset_date": info.sunset_date,
                    "migration_guide": info.migration_guide
                })
            
            if info.sunset_date and info.sunset_date <= datetime.utcnow() + timedelta(days=30):
                recommendations.append({
                    "interface": name,
                    "reason": "sunset_soon",
                    "sunset_date": info.sunset_date,
                    "migration_guide": info.migration_guide
                })
        
        return recommendations

def deprecated_interface(replacement: str, version: str, removal_date: Optional[str] = None):
    """Decorator for deprecated interfaces"""
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            warnings.warn(
                f"{func.__name__} is deprecated. Use {replacement} instead. "
                f"Will be removed in version {version}" + 
                (f" on {removal_date}" if removal_date else ""),
                DeprecationWarning,
                stacklevel=2
            )
            return await func(*args, **kwargs)
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            warnings.warn(
                f"{func.__name__} is deprecated. Use {replacement} instead. "
                f"Will be removed in version {version}" + 
                (f" on {removal_date}" if removal_date else ""),
                DeprecationWarning,
                stacklevel=2
            )
            return func(*args, **kwargs)
        
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator

def compatibility_wrapper(interface_name: str, version: str):
    """Decorator for compatibility wrapper"""
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            # Check compatibility
            compatibility_manager = get_compatibility_manager()
            compatibility_level = compatibility_manager.check_compatibility(interface_name, version)
            
            if compatibility_level == CompatibilityLevel.NONE:
                raise ValueError(f"Interface {interface_name} is not compatible with version {version}")
            
            # Execute with compatibility checks
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                # Log compatibility issues
                logger.warning(f"Compatibility issue with {interface_name}: {e}")
                raise e
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            # Check compatibility
            compatibility_manager = get_compatibility_manager()
            compatibility_level = compatibility_manager.check_compatibility(interface_name, version)
            
            if compatibility_level == CompatibilityLevel.NONE:
                raise ValueError(f"Interface {interface_name} is not compatible with version {version}")
            
            # Execute with compatibility checks
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # Log compatibility issues
                logger.warning(f"Compatibility issue with {interface_name}: {e}")
                raise e
        
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator

class LegacyExecutorInterface:
    """Legacy executor interface for backward compatibility"""
    
    def __init__(self, new_executor):
        self.new_executor = new_executor
        self.compatibility_manager = get_compatibility_manager()
    
    @deprecated_interface("new_executor.execute", "2.0.0", "2024-12-01")
    async def execute_ask_pipeline(
        self,
        query: str,
        user_id: str,
        guild_id: Optional[str] = None,
        channel_id: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Legacy execute_ask_pipeline interface"""
        try:
            # Convert legacy parameters to new format
            request_data = {
                "query": query,
                "user_id": user_id,
                "guild_id": guild_id,
                "channel_id": channel_id,
                "context": kwargs.get("context", {}),
                "version": "v1"
            }
            
            # Execute using new executor
            result = await self.new_executor.execute(request_data)
            
            # Convert response to legacy format
            return {
                "success": result.get("success", False),
                "response": result.get("response", ""),
                "error": result.get("error"),
                "execution_time": result.get("execution_time", 0.0),
                "intent": result.get("intent"),
                "confidence": result.get("confidence", 0.0)
            }
            
        except Exception as e:
            logger.error(f"Legacy executor error: {e}")
            return {
                "success": False,
                "response": "",
                "error": str(e),
                "execution_time": 0.0,
                "intent": None,
                "confidence": 0.0
            }

class CompatibilityTestRunner:
    """Compatibility test runner"""
    
    def __init__(self):
        self.tests: Dict[str, Callable] = {}
        self.results: Dict[str, Dict[str, Any]] = {}
    
    def register_test(self, name: str, test_func: Callable):
        """Register compatibility test"""
        self.tests[name] = test_func
        logger.info(f"Registered compatibility test: {name}")
    
    async def run_test(self, name: str) -> Dict[str, Any]:
        """Run compatibility test"""
        if name not in self.tests:
            raise ValueError(f"Test {name} not found")
        
        test_func = self.tests[name]
        start_time = datetime.utcnow()
        
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            end_time = datetime.utcnow()
            duration = (end_time - start_time).total_seconds()
            
            test_result = {
                "name": name,
                "status": "passed" if result else "failed",
                "duration": duration,
                "timestamp": start_time,
                "result": result
            }
            
            self.results[name] = test_result
            logger.info(f"Compatibility test {name}: {test_result['status']}")
            return test_result
            
        except Exception as e:
            end_time = datetime.utcnow()
            duration = (end_time - start_time).total_seconds()
            
            test_result = {
                "name": name,
                "status": "error",
                "duration": duration,
                "timestamp": start_time,
                "error": str(e)
            }
            
            self.results[name] = test_result
            logger.error(f"Compatibility test {name} error: {e}")
            return test_result
    
    async def run_all_tests(self) -> Dict[str, Dict[str, Any]]:
        """Run all compatibility tests"""
        results = {}
        
        for name in self.tests:
            results[name] = await self.run_test(name)
        
        return results
    
    def get_test_results(self, name: Optional[str] = None) -> Union[Dict[str, Any], Dict[str, Dict[str, Any]]]:
        """Get test results"""
        if name:
            return self.results.get(name, {})
        return self.results

# Global instances
_compatibility_manager = BackwardCompatibilityManager()
_test_runner = CompatibilityTestRunner()

def get_compatibility_manager() -> BackwardCompatibilityManager:
    """Get global compatibility manager"""
    return _compatibility_manager

def get_test_runner() -> CompatibilityTestRunner:
    """Get global test runner"""
    return _test_runner

def cleanup_compatibility():
    """Cleanup compatibility components"""
    global _compatibility_manager, _test_runner
    _compatibility_manager = BackwardCompatibilityManager()
    _test_runner = CompatibilityTestRunner()
