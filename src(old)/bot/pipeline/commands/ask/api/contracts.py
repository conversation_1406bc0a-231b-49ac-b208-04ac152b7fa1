"""
API Contracts for ASK Pipeline

Provides standardized API contracts:
- Consistent API contracts for all pipeline components
- Request/response validation with Pydantic models
- API versioning strategy for backward compatibility
- API documentation with OpenAPI specifications
- API testing and contract validation
"""

from typing import Dict, Any, Optional, List, Union, Literal
from datetime import datetime
from enum import Enum
from pydantic import BaseModel, Field, validator, root_validator
import uuid

class APIVersion(str, Enum):
    """API version enumeration"""
    V1 = "v1"
    V2 = "v2"
    V3 = "v3"

class RequestStatus(str, Enum):
    """Request status enumeration"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class ErrorCode(str, Enum):
    """Error code enumeration"""
    VALIDATION_ERROR = "validation_error"
    AUTHENTICATION_ERROR = "authentication_error"
    AUTHORIZATION_ERROR = "authorization_error"
    RATE_LIMIT_ERROR = "rate_limit_error"
    INTERNAL_ERROR = "internal_error"
    EXTERNAL_SERVICE_ERROR = "external_service_error"
    TIMEOUT_ERROR = "timeout_error"
    NOT_FOUND_ERROR = "not_found_error"

class IntentType(str, Enum):
    """Intent type enumeration"""
    PRICE_QUERY = "price_query"
    ANALYSIS_QUERY = "analysis_query"
    NEWS_QUERY = "news_query"
    CHART_QUERY = "chart_query"
    SENTIMENT_QUERY = "sentiment_query"
    GENERAL_QUERY = "general_query"

class BaseRequest(BaseModel):
    """Base request model"""
    request_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    version: APIVersion = Field(default=APIVersion.V1)
    correlation_id: Optional[str] = None
    
    class Config:
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class BaseResponse(BaseModel):
    """Base response model"""
    request_id: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    version: APIVersion = Field(default=APIVersion.V1)
    status: RequestStatus
    success: bool
    correlation_id: Optional[str] = None
    
    class Config:
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class ErrorResponse(BaseResponse):
    """Error response model"""
    error_code: ErrorCode
    error_message: str
    error_details: Optional[Dict[str, Any]] = None
    retry_after: Optional[int] = None
    
    @root_validator
    def validate_error_response(cls, values):
        if values.get('success', False):
            raise ValueError("Error response cannot have success=True")
        return values

class AskRequest(BaseRequest):
    """ASK command request model"""
    query: str = Field(..., min_length=1, max_length=2000, description="User query")
    user_id: str = Field(..., min_length=1, description="Discord user ID")
    guild_id: Optional[str] = Field(None, description="Discord guild ID")
    channel_id: Optional[str] = Field(None, description="Discord channel ID")
    context: Optional[Dict[str, Any]] = Field(None, description="Additional context")
    
    @validator('query')
    def validate_query(cls, v):
        if not v or not v.strip():
            raise ValueError("Query cannot be empty")
        return v.strip()
    
    @validator('user_id')
    def validate_user_id(cls, v):
        if not v or not v.strip():
            raise ValueError("User ID cannot be empty")
        return v.strip()

class AskResponse(BaseResponse):
    """ASK command response model"""
    response: str = Field(..., description="Generated response")
    intent: IntentType = Field(..., description="Detected intent")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Intent confidence score")
    execution_time: float = Field(..., ge=0.0, description="Execution time in seconds")
    tokens_used: Optional[int] = Field(None, ge=0, description="Number of tokens used")
    cost: Optional[float] = Field(None, ge=0.0, description="Estimated cost")
    sources: Optional[List[str]] = Field(None, description="Source URLs or references")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")
    
    @root_validator
    def validate_success_response(cls, values):
        if values.get('success', False) and not values.get('response'):
            raise ValueError("Successful response must have response text")
        return values

class IntentAnalysisRequest(BaseRequest):
    """Intent analysis request model"""
    query: str = Field(..., min_length=1, max_length=2000)
    user_id: str = Field(..., min_length=1)
    context: Optional[Dict[str, Any]] = None

class IntentAnalysisResponse(BaseResponse):
    """Intent analysis response model"""
    intent: IntentType = Field(..., description="Detected intent")
    confidence: float = Field(..., ge=0.0, le=1.0)
    entities: List[Dict[str, Any]] = Field(default_factory=list)
    sentiment: Optional[str] = Field(None, description="Sentiment analysis result")
    language: Optional[str] = Field(None, description="Detected language")
    processing_time: float = Field(..., ge=0.0)

class SecurityValidationRequest(BaseRequest):
    """Security validation request model"""
    query: str = Field(..., min_length=1, max_length=2000)
    user_id: str = Field(..., min_length=1)
    guild_id: Optional[str] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None

class SecurityValidationResponse(BaseResponse):
    """Security validation response model"""
    is_safe: bool = Field(..., description="Whether the query is safe")
    risk_level: str = Field(..., description="Risk level: low, medium, high")
    sanitized_query: Optional[str] = Field(None, description="Sanitized query if unsafe")
    risk_indicators: List[str] = Field(default_factory=list)
    validation_time: float = Field(..., ge=0.0)

class RateLimitRequest(BaseRequest):
    """Rate limit check request model"""
    user_id: str = Field(..., min_length=1)
    guild_id: Optional[str] = None
    operation: str = Field(..., description="Operation being performed")
    weight: int = Field(1, ge=1, description="Request weight")

class RateLimitResponse(BaseResponse):
    """Rate limit check response model"""
    allowed: bool = Field(..., description="Whether request is allowed")
    remaining_requests: int = Field(..., ge=0, description="Remaining requests")
    reset_time: Optional[datetime] = Field(None, description="Rate limit reset time")
    retry_after: Optional[int] = Field(None, description="Seconds to wait before retry")

class AuthenticationRequest(BaseRequest):
    """Authentication request model"""
    user_id: str = Field(..., min_length=1)
    guild_id: Optional[str] = None
    session_token: Optional[str] = None

class AuthenticationResponse(BaseResponse):
    """Authentication response model"""
    authenticated: bool = Field(..., description="Whether user is authenticated")
    user_permissions: List[str] = Field(default_factory=list)
    session_expires_at: Optional[datetime] = None
    requires_reauth: bool = Field(False, description="Whether re-authentication is required")

class HealthCheckRequest(BaseRequest):
    """Health check request model"""
    component: Optional[str] = Field(None, description="Specific component to check")
    include_dependencies: bool = Field(True, description="Include dependency checks")

class HealthCheckResponse(BaseResponse):
    """Health check response model"""
    overall_status: str = Field(..., description="Overall health status")
    components: Dict[str, Dict[str, Any]] = Field(default_factory=dict)
    uptime: float = Field(..., ge=0.0, description="System uptime in seconds")
    version: str = Field(..., description="System version")
    timestamp: datetime = Field(default_factory=datetime.utcnow)

class MetricsRequest(BaseRequest):
    """Metrics request model"""
    metric_type: str = Field(..., description="Type of metrics requested")
    time_range: Optional[str] = Field(None, description="Time range for metrics")
    aggregation: Optional[str] = Field(None, description="Aggregation method")

class MetricsResponse(BaseResponse):
    """Metrics response model"""
    metrics: Dict[str, Any] = Field(..., description="Requested metrics")
    time_range: Optional[str] = None
    aggregation: Optional[str] = None
    generated_at: datetime = Field(default_factory=datetime.utcnow)

class ConfigurationRequest(BaseRequest):
    """Configuration request model"""
    config_key: Optional[str] = Field(None, description="Specific configuration key")
    environment: Optional[str] = Field(None, description="Environment to get config for")
    include_secrets: bool = Field(False, description="Include secret values")

class ConfigurationResponse(BaseResponse):
    """Configuration response model"""
    configuration: Dict[str, Any] = Field(..., description="Configuration values")
    environment: str = Field(..., description="Configuration environment")
    last_updated: datetime = Field(..., description="Last configuration update")

class FeatureFlagRequest(BaseRequest):
    """Feature flag request model"""
    flag_name: str = Field(..., min_length=1, description="Feature flag name")
    user_id: str = Field(..., min_length=1)
    guild_id: Optional[str] = None
    context: Optional[Dict[str, Any]] = None

class FeatureFlagResponse(BaseResponse):
    """Feature flag response model"""
    flag_name: str = Field(..., description="Feature flag name")
    enabled: bool = Field(..., description="Whether flag is enabled")
    rollout_percentage: float = Field(..., ge=0.0, le=100.0)
    targeting_type: str = Field(..., description="Targeting type")
    reason: str = Field(..., description="Reason for the result")

class BatchRequest(BaseRequest):
    """Batch request model"""
    requests: List[BaseRequest] = Field(..., min_items=1, max_items=100)
    batch_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    priority: int = Field(1, ge=1, le=10, description="Batch priority")
    
    @validator('requests')
    def validate_requests(cls, v):
        if not v:
            raise ValueError("Batch must contain at least one request")
        return v

class BatchResponse(BaseResponse):
    """Batch response model"""
    batch_id: str = Field(..., description="Batch ID")
    responses: List[BaseResponse] = Field(..., description="Individual responses")
    total_requests: int = Field(..., ge=0)
    successful_requests: int = Field(..., ge=0)
    failed_requests: int = Field(..., ge=0)
    processing_time: float = Field(..., ge=0.0)

class WebhookRequest(BaseRequest):
    """Webhook request model"""
    event_type: str = Field(..., description="Type of event")
    payload: Dict[str, Any] = Field(..., description="Event payload")
    source: str = Field(..., description="Event source")
    signature: Optional[str] = Field(None, description="Request signature for validation")

class WebhookResponse(BaseResponse):
    """Webhook response model"""
    event_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    processed: bool = Field(..., description="Whether event was processed")
    processing_time: float = Field(..., ge=0.0)
    next_retry: Optional[datetime] = Field(None, description="Next retry time if failed")

# API Contract Registry
API_CONTRACTS = {
    "ask": {
        "request": AskRequest,
        "response": AskResponse,
        "error": ErrorResponse
    },
    "intent_analysis": {
        "request": IntentAnalysisRequest,
        "response": IntentAnalysisResponse,
        "error": ErrorResponse
    },
    "security_validation": {
        "request": SecurityValidationRequest,
        "response": SecurityValidationResponse,
        "error": ErrorResponse
    },
    "rate_limit": {
        "request": RateLimitRequest,
        "response": RateLimitResponse,
        "error": ErrorResponse
    },
    "authentication": {
        "request": AuthenticationRequest,
        "response": AuthenticationResponse,
        "error": ErrorResponse
    },
    "health_check": {
        "request": HealthCheckRequest,
        "response": HealthCheckResponse,
        "error": ErrorResponse
    },
    "metrics": {
        "request": MetricsRequest,
        "response": MetricsResponse,
        "error": ErrorResponse
    },
    "configuration": {
        "request": ConfigurationRequest,
        "response": ConfigurationResponse,
        "error": ErrorResponse
    },
    "feature_flag": {
        "request": FeatureFlagRequest,
        "response": FeatureFlagResponse,
        "error": ErrorResponse
    },
    "batch": {
        "request": BatchRequest,
        "response": BatchResponse,
        "error": ErrorResponse
    },
    "webhook": {
        "request": WebhookRequest,
        "response": WebhookResponse,
        "error": ErrorResponse
    }
}

def get_contract(api_name: str, contract_type: str) -> Optional[BaseModel]:
    """Get API contract by name and type"""
    if api_name not in API_CONTRACTS:
        return None
    
    contracts = API_CONTRACTS[api_name]
    return contracts.get(contract_type)

def validate_request(api_name: str, data: Dict[str, Any]) -> tuple[bool, Optional[BaseModel], Optional[str]]:
    """Validate request data against API contract"""
    try:
        request_contract = get_contract(api_name, "request")
        if not request_contract:
            return False, None, f"Unknown API: {api_name}"
        
        validated_request = request_contract(**data)
        return True, validated_request, None
        
    except Exception as e:
        return False, None, f"Validation error: {str(e)}"

def validate_response(api_name: str, data: Dict[str, Any]) -> tuple[bool, Optional[BaseModel], Optional[str]]:
    """Validate response data against API contract"""
    try:
        response_contract = get_contract(api_name, "response")
        if not response_contract:
            return False, None, f"Unknown API: {api_name}"
        
        validated_response = response_contract(**data)
        return True, validated_response, None
        
    except Exception as e:
        return False, None, f"Validation error: {str(e)}"

def create_error_response(
    request_id: str,
    error_code: ErrorCode,
    error_message: str,
    error_details: Optional[Dict[str, Any]] = None,
    retry_after: Optional[int] = None,
    correlation_id: Optional[str] = None
) -> ErrorResponse:
    """Create standardized error response"""
    return ErrorResponse(
        request_id=request_id,
        status=RequestStatus.FAILED,
        success=False,
        error_code=error_code,
        error_message=error_message,
        error_details=error_details,
        retry_after=retry_after,
        correlation_id=correlation_id
    )
