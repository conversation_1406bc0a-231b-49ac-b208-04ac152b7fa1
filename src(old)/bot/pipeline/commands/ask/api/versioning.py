"""
API Versioning System for ASK Pipeline

Provides API versioning capabilities:
- API versioning strategy for backward compatibility
- Version-specific request/response handling
- Migration utilities for API changes
- Version deprecation and sunset management
- Backward compatibility testing
"""

from typing import Dict, Any, Optional, List, Union, Callable, Type
from datetime import datetime, timed<PERSON>ta
from enum import Enum
from dataclasses import dataclass, field
import logging
import asyncio
from functools import wraps

from .contracts import APIVersion, BaseRequest, BaseResponse, ErrorResponse, ErrorCode

logger = logging.getLogger(__name__)

class VersionStatus(Enum):
    """Version status enumeration"""
    ACTIVE = "active"
    DEPRECATED = "deprecated"
    SUNSET = "sunset"
    UNSUPPORTED = "unsupported"

@dataclass
class VersionInfo:
    """Version information"""
    version: APIVersion
    status: VersionStatus
    release_date: datetime
    sunset_date: Optional[datetime] = None
    deprecation_date: Optional[datetime] = None
    migration_guide: Optional[str] = None
    breaking_changes: List[str] = field(default_factory=list)
    new_features: List[str] = field(default_factory=list)
    bug_fixes: List[str] = field(default_factory=list)

@dataclass
class VersionCompatibility:
    """Version compatibility information"""
    from_version: APIVersion
    to_version: APIVersion
    compatible: bool
    migration_required: bool
    migration_script: Optional[str] = None
    compatibility_notes: Optional[str] = None

class APIVersionManager:
    """API version management system"""
    
    def __init__(self):
        self.versions: Dict[APIVersion, VersionInfo] = {}
        self.compatibility_matrix: Dict[tuple[APIVersion, APIVersion], VersionCompatibility] = {}
        self.version_handlers: Dict[APIVersion, Dict[str, Callable]] = {}
        self.migration_scripts: Dict[tuple[APIVersion, APIVersion], Callable] = {}
        self._default_version = APIVersion.V1
        
        # Initialize default versions
        self._initialize_default_versions()
    
    def _initialize_default_versions(self):
        """Initialize default version information"""
        # Version 1 - Initial release
        self.versions[APIVersion.V1] = VersionInfo(
            version=APIVersion.V1,
            status=VersionStatus.ACTIVE,
            release_date=datetime(2024, 1, 1),
            new_features=["Initial ASK pipeline implementation"],
            bug_fixes=[]
        )
        
        # Version 2 - Enhanced features
        self.versions[APIVersion.V2] = VersionInfo(
            version=APIVersion.V2,
            status=VersionStatus.ACTIVE,
            release_date=datetime(2024, 6, 1),
            new_features=[
                "Enhanced intent analysis",
                "Improved security validation",
                "Better error handling",
                "Performance optimizations"
            ],
            breaking_changes=[
                "Response format changes",
                "New required fields in requests"
            ],
            migration_guide="https://docs.ask-pipeline.com/migration/v1-to-v2"
        )
        
        # Version 3 - Future release
        self.versions[APIVersion.V3] = VersionInfo(
            version=APIVersion.V3,
            status=VersionStatus.ACTIVE,
            release_date=datetime(2024, 12, 1),
            new_features=[
                "Advanced analytics",
                "Real-time streaming",
                "Enhanced caching",
                "Multi-language support"
            ],
            breaking_changes=[
                "Complete API redesign",
                "New authentication system",
                "Updated response schemas"
            ],
            migration_guide="https://docs.ask-pipeline.com/migration/v2-to-v3"
        )
        
        # Initialize compatibility matrix
        self._initialize_compatibility_matrix()
    
    def _initialize_compatibility_matrix(self):
        """Initialize version compatibility matrix"""
        # V1 to V2 compatibility
        self.compatibility_matrix[(APIVersion.V1, APIVersion.V2)] = VersionCompatibility(
            from_version=APIVersion.V1,
            to_version=APIVersion.V2,
            compatible=True,
            migration_required=True,
            migration_script="migrate_v1_to_v2",
            compatibility_notes="V2 adds new fields but maintains backward compatibility"
        )
        
        # V2 to V3 compatibility
        self.compatibility_matrix[(APIVersion.V2, APIVersion.V3)] = VersionCompatibility(
            from_version=APIVersion.V2,
            to_version=APIVersion.V3,
            compatible=False,
            migration_required=True,
            migration_script="migrate_v2_to_v3",
            compatibility_notes="V3 introduces breaking changes requiring migration"
        )
        
        # V1 to V3 compatibility
        self.compatibility_matrix[(APIVersion.V1, APIVersion.V3)] = VersionCompatibility(
            from_version=APIVersion.V1,
            to_version=APIVersion.V3,
            compatible=False,
            migration_required=True,
            migration_script="migrate_v1_to_v3",
            compatibility_notes="V3 requires migration through V2 first"
        )
    
    def register_version_handler(self, version: APIVersion, api_name: str, handler: Callable):
        """Register version-specific API handler"""
        if version not in self.version_handlers:
            self.version_handlers[version] = {}
        
        self.version_handlers[version][api_name] = handler
        logger.debug(f"Registered handler for {api_name} in version {version.value}")
    
    def get_version_handler(self, version: APIVersion, api_name: str) -> Optional[Callable]:
        """Get version-specific API handler"""
        return self.version_handlers.get(version, {}).get(api_name)
    
    def register_migration_script(self, from_version: APIVersion, to_version: APIVersion, script: Callable):
        """Register migration script between versions"""
        self.migration_scripts[(from_version, to_version)] = script
        logger.debug(f"Registered migration script from {from_version.value} to {to_version.value}")
    
    def get_migration_script(self, from_version: APIVersion, to_version: APIVersion) -> Optional[Callable]:
        """Get migration script between versions"""
        return self.migration_scripts.get((from_version, to_version))
    
    def is_version_supported(self, version: APIVersion) -> bool:
        """Check if version is supported"""
        version_info = self.versions.get(version)
        if not version_info:
            return False
        
        return version_info.status in [VersionStatus.ACTIVE, VersionStatus.DEPRECATED]
    
    def is_version_deprecated(self, version: APIVersion) -> bool:
        """Check if version is deprecated"""
        version_info = self.versions.get(version)
        if not version_info:
            return True
        
        return version_info.status == VersionStatus.DEPRECATED
    
    def is_version_sunset(self, version: APIVersion) -> bool:
        """Check if version is sunset"""
        version_info = self.versions.get(version)
        if not version_info:
            return True
        
        return version_info.status == VersionStatus.SUNSET
    
    def get_supported_versions(self) -> List[APIVersion]:
        """Get list of supported versions"""
        return [
            version for version, info in self.versions.items()
            if info.status in [VersionStatus.ACTIVE, VersionStatus.DEPRECATED]
        ]
    
    def get_latest_version(self) -> APIVersion:
        """Get latest supported version"""
        supported_versions = self.get_supported_versions()
        if not supported_versions:
            return self._default_version
        
        # Return the highest version number
        return max(supported_versions, key=lambda v: int(v.value[1:]))
    
    def get_version_info(self, version: APIVersion) -> Optional[VersionInfo]:
        """Get version information"""
        return self.versions.get(version)
    
    def deprecate_version(self, version: APIVersion, deprecation_date: Optional[datetime] = None):
        """Deprecate a version"""
        if version not in self.versions:
            raise ValueError(f"Version {version.value} not found")
        
        version_info = self.versions[version]
        version_info.status = VersionStatus.DEPRECATED
        version_info.deprecation_date = deprecation_date or datetime.utcnow()
        
        logger.info(f"Version {version.value} deprecated on {version_info.deprecation_date}")
    
    def sunset_version(self, version: APIVersion, sunset_date: Optional[datetime] = None):
        """Sunset a version"""
        if version not in self.versions:
            raise ValueError(f"Version {version.value} not found")
        
        version_info = self.versions[version]
        version_info.status = VersionStatus.SUNSET
        version_info.sunset_date = sunset_date or datetime.utcnow()
        
        logger.info(f"Version {version.value} sunset on {version_info.sunset_date}")
    
    def check_compatibility(self, from_version: APIVersion, to_version: APIVersion) -> VersionCompatibility:
        """Check compatibility between versions"""
        compatibility = self.compatibility_matrix.get((from_version, to_version))
        if compatibility:
            return compatibility
        
        # Default compatibility check
        return VersionCompatibility(
            from_version=from_version,
            to_version=to_version,
            compatible=from_version == to_version,
            migration_required=from_version != to_version
        )
    
    async def migrate_request(self, request: BaseRequest, target_version: APIVersion) -> BaseRequest:
        """Migrate request to target version"""
        if request.version == target_version:
            return request
        
        # Check if migration is needed
        compatibility = self.check_compatibility(request.version, target_version)
        if not compatibility.migration_required:
            return request
        
        # Get migration script
        migration_script = self.get_migration_script(request.version, target_version)
        if not migration_script:
            raise ValueError(f"No migration script available from {request.version.value} to {target_version.value}")
        
        # Execute migration
        try:
            migrated_request = await migration_script(request)
            migrated_request.version = target_version
            return migrated_request
        except Exception as e:
            logger.error(f"Migration failed from {request.version.value} to {target_version.value}: {e}")
            raise
    
    async def migrate_response(self, response: BaseResponse, target_version: APIVersion) -> BaseResponse:
        """Migrate response to target version"""
        if response.version == target_version:
            return response
        
        # Check if migration is needed
        compatibility = self.check_compatibility(response.version, target_version)
        if not compatibility.migration_required:
            return response
        
        # Get migration script
        migration_script = self.get_migration_script(response.version, target_version)
        if not migration_script:
            raise ValueError(f"No migration script available from {response.version.value} to {target_version.value}")
        
        # Execute migration
        try:
            migrated_response = await migration_script(response)
            migrated_response.version = target_version
            return migrated_response
        except Exception as e:
            logger.error(f"Migration failed from {response.version.value} to {target_version.value}: {e}")
            raise
    
    def get_version_warnings(self, version: APIVersion) -> List[str]:
        """Get version-specific warnings"""
        warnings = []
        version_info = self.versions.get(version)
        
        if not version_info:
            warnings.append(f"Version {version.value} is not supported")
            return warnings
        
        if version_info.status == VersionStatus.DEPRECATED:
            warnings.append(f"Version {version.value} is deprecated")
            if version_info.deprecation_date:
                warnings.append(f"Deprecated on {version_info.deprecation_date}")
            if version_info.sunset_date:
                warnings.append(f"Will be sunset on {version_info.sunset_date}")
        
        if version_info.status == VersionStatus.SUNSET:
            warnings.append(f"Version {version.value} is sunset and no longer supported")
        
        return warnings
    
    def get_migration_recommendations(self, current_version: APIVersion) -> List[str]:
        """Get migration recommendations for current version"""
        recommendations = []
        latest_version = self.get_latest_version()
        
        if current_version == latest_version:
            return recommendations
        
        # Check if current version is deprecated
        if self.is_version_deprecated(current_version):
            recommendations.append(f"Upgrade from {current_version.value} to {latest_version.value}")
        
        # Check if current version will be sunset
        version_info = self.get_version_info(current_version)
        if version_info and version_info.sunset_date:
            if version_info.sunset_date <= datetime.utcnow() + timedelta(days=30):
                recommendations.append(f"Version {current_version.value} will be sunset soon")
        
        return recommendations

def version_handler(version: APIVersion, api_name: str):
    """Decorator for version-specific API handlers"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Get version manager
            version_manager = get_version_manager()
            
            # Check if version is supported
            if not version_manager.is_version_supported(version):
                raise ValueError(f"Version {version.value} is not supported")
            
            # Check for deprecation warnings
            warnings = version_manager.get_version_warnings(version)
            if warnings:
                logger.warning(f"Version {version.value} warnings: {', '.join(warnings)}")
            
            # Execute handler
            return await func(*args, **kwargs)
        
        # Register handler
        version_manager.register_version_handler(version, api_name, func)
        return wrapper
    
    return decorator

def migrate_request(from_version: APIVersion, to_version: APIVersion):
    """Decorator for request migration scripts"""
    def decorator(func):
        @wraps(func)
        async def wrapper(request: BaseRequest) -> BaseRequest:
            # Execute migration
            migrated_request = await func(request)
            
            # Update version
            migrated_request.version = to_version
            
            return migrated_request
        
        # Register migration script
        version_manager = get_version_manager()
        version_manager.register_migration_script(from_version, to_version, wrapper)
        return wrapper
    
    return decorator

def migrate_response(from_version: APIVersion, to_version: APIVersion):
    """Decorator for response migration scripts"""
    def decorator(func):
        @wraps(func)
        async def wrapper(response: BaseResponse) -> BaseResponse:
            # Execute migration
            migrated_response = await func(response)
            
            # Update version
            migrated_response.version = to_version
            
            return migrated_response
        
        # Register migration script
        version_manager = get_version_manager()
        version_manager.register_migration_script(from_version, to_version, wrapper)
        return wrapper
    
    return decorator

# Global version manager
_version_manager: Optional[APIVersionManager] = None

def get_version_manager() -> APIVersionManager:
    """Get global version manager"""
    global _version_manager
    if _version_manager is None:
        _version_manager = APIVersionManager()
    return _version_manager

def cleanup_version_manager():
    """Cleanup global version manager"""
    global _version_manager
    if _version_manager:
        _version_manager = None
