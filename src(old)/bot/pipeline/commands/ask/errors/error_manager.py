"""
Consolidated Error Manager for ASK Pipeline

Centralizes all error handling functionality into a single, focused component.
Combines error classification, user messaging, and coordination logic.

This replaces multiple error handling modules with a unified approach.
"""

import time
import asyncio
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass
from enum import Enum

from src.shared.error_handling.logging import get_logger

logger = get_logger(__name__)

class ErrorType(Enum):
    """Types of errors that can occur in the pipeline"""
    AI_SERVICE_UNAVAILABLE = "ai_service_unavailable"
    AI_RATE_LIMITED = "ai_rate_limited"
    AI_TIMEOUT = "ai_timeout"
    TOOL_EXECUTION_FAILED = "tool_execution_failed"
    NETWORK_ERROR = "network_error"
    CACHE_ERROR = "cache_error"
    VALIDATION_ERROR = "validation_error"
    UNKNOWN_ERROR = "unknown_error"

class ErrorSeverity(Enum):
    """Severity levels for errors"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class ErrorContext:
    """Context information for error handling"""
    stage: str
    user_id: Optional[str] = None
    query: str = ""
    retry_count: int = 0
    execution_time: float = 0.0
    additional_data: Optional[Dict[str, Any]] = None

@dataclass
class ErrorResult:
    """Result of error handling"""
    success: bool
    response: str
    error_type: str
    severity: ErrorSeverity
    execution_time: float
    correlation_id: str = ""
    embed: Optional[Dict[str, Any]] = None
    retry_recommended: bool = False
    fallback_used: Optional[str] = None

class ErrorManager:
    """
    Consolidated error manager for ASK pipeline
    
    Features:
    - Intelligent error classification
    - User-friendly message generation
    - Circuit breaker pattern
    - Error statistics and monitoring
    - Fallback strategy coordination
    """
    
    def __init__(self):
        self.error_stats = {
            'total_errors': 0,
            'errors_by_type': {},
            'errors_by_severity': {},
            'recovery_success_rate': 0.0,
            'circuit_breaker_trips': 0
        }
        
        # Circuit breaker states
        self.circuit_breakers = {}
        self.circuit_breaker_config = {
            'failure_threshold': 5,
            'recovery_timeout': 300,  # 5 minutes
            'half_open_max_calls': 3
        }
        
        # Error classification patterns
        self.error_patterns = {
            'rate_limit': ['rate limit', 'quota exceeded', 'too many requests'],
            'timeout': ['timeout', 'timed out', 'connection timeout'],
            'unavailable': ['service unavailable', 'connection refused', 'not available'],
            'network': ['network error', 'connection error', 'dns resolution'],
            'validation': ['validation error', 'invalid input', 'malformed request']
        }
        
        # User-friendly error messages
        self.error_messages = {
            ErrorType.AI_SERVICE_UNAVAILABLE: {
                ErrorSeverity.LOW: "I'm having a small hiccup with my AI services. Let me try a different approach.",
                ErrorSeverity.MEDIUM: "My AI services are temporarily unavailable, but I can still help with general information.",
                ErrorSeverity.HIGH: "I'm experiencing significant AI connectivity issues. Please try again in a few minutes.",
                ErrorSeverity.CRITICAL: "My AI services are currently down. Please try again later."
            },
            ErrorType.AI_RATE_LIMITED: {
                ErrorSeverity.LOW: "I'm being rate limited. Let me try again in a moment.",
                ErrorSeverity.MEDIUM: "I've hit my rate limit. Please wait a moment before trying again.",
                ErrorSeverity.HIGH: "I'm currently rate limited. Please try again in a few minutes.",
                ErrorSeverity.CRITICAL: "I'm severely rate limited. Please try again later."
            },
            ErrorType.AI_TIMEOUT: {
                ErrorSeverity.LOW: "That took longer than expected. Let me try a faster approach.",
                ErrorSeverity.MEDIUM: "I'm experiencing some delays. Let me try a different method.",
                ErrorSeverity.HIGH: "I'm having timeout issues. Please try again with a simpler request.",
                ErrorSeverity.CRITICAL: "I'm experiencing severe timeouts. Please try again later."
            },
            ErrorType.TOOL_EXECUTION_FAILED: {
                ErrorSeverity.LOW: "One of my tools had an issue, but I can still help you.",
                ErrorSeverity.MEDIUM: "I'm having trouble with some of my tools. Let me try a different approach.",
                ErrorSeverity.HIGH: "Several of my tools are having issues. I'll do my best with what's available.",
                ErrorSeverity.CRITICAL: "My tools are currently unavailable. I can only provide general assistance."
            },
            ErrorType.NETWORK_ERROR: {
                ErrorSeverity.LOW: "I had a small network hiccup. Let me try again.",
                ErrorSeverity.MEDIUM: "I'm experiencing network connectivity issues. Retrying...",
                ErrorSeverity.HIGH: "I'm having significant network problems. Please try again shortly.",
                ErrorSeverity.CRITICAL: "I'm experiencing severe network issues. Please try again later."
            },
            ErrorType.CACHE_ERROR: {
                ErrorSeverity.LOW: "I had a small cache issue, but I can still help you.",
                ErrorSeverity.MEDIUM: "My cache is having issues, but I can work without it.",
                ErrorSeverity.HIGH: "I'm having cache problems, which may slow down my responses.",
                ErrorSeverity.CRITICAL: "My cache system is down, but I can still assist you."
            },
            ErrorType.VALIDATION_ERROR: {
                ErrorSeverity.LOW: "I didn't quite understand that. Could you rephrase your question?",
                ErrorSeverity.MEDIUM: "I'm having trouble understanding your request. Could you be more specific?",
                ErrorSeverity.HIGH: "I can't process that request. Please try rephrasing it differently.",
                ErrorSeverity.CRITICAL: "I can't understand that request format. Please try a different approach."
            },
            ErrorType.UNKNOWN_ERROR: {
                ErrorSeverity.LOW: "I encountered a small issue, but let me try to help anyway.",
                ErrorSeverity.MEDIUM: "I ran into an unexpected problem. Let me try a different approach.",
                ErrorSeverity.HIGH: "I encountered an unexpected error. Please try again.",
                ErrorSeverity.CRITICAL: "I'm experiencing unexpected technical difficulties. Please try again later."
            }
        }
        
        logger.info("✅ Consolidated error manager initialized")

    async def handle_error(
        self,
        exception: Exception,
        context: ErrorContext,
        correlation_id: str = ""
    ) -> ErrorResult:
        """
        Handle an error with classification, fallback, and user messaging
        
        Args:
            exception: The exception that occurred
            context: Context information about the error
            correlation_id: Unique identifier for tracking
            
        Returns:
            ErrorResult with user-friendly response and handling information
        """
        start_time = time.time()
        
        try:
            # Update error statistics
            self.error_stats['total_errors'] += 1
            
            # Classify the error
            error_type = self._classify_error(exception, context)
            severity = self._determine_severity(error_type, exception, context)
            
            # Update error statistics by type and severity
            self.error_stats['errors_by_type'][error_type.value] = \
                self.error_stats['errors_by_type'].get(error_type.value, 0) + 1
            self.error_stats['errors_by_severity'][severity.value] = \
                self.error_stats['errors_by_severity'].get(severity.value, 0) + 1
            
            # Check circuit breaker
            circuit_breaker_key = f"{context.stage}_{error_type.value}"
            if self._should_circuit_break(circuit_breaker_key, severity):
                return await self._handle_circuit_break(circuit_breaker_key, context, correlation_id)
            
            # Generate user-friendly message
            user_message = self._generate_user_message(error_type, severity, context)
            
            # Determine if retry is recommended
            retry_recommended = self._should_retry(error_type, severity, context)
            
            # Create error result
            execution_time = time.time() - start_time
            result = ErrorResult(
                success=False,
                response=user_message,
                error_type=error_type.value,
                severity=severity,
                execution_time=execution_time,
                correlation_id=correlation_id,
                retry_recommended=retry_recommended
            )
            
            # Log the error
            self._log_error(exception, error_type, severity, context, correlation_id)
            
            return result
            
        except Exception as e:
            logger.error(f"Error in error handler: {e}", extra={'correlation_id': correlation_id})
            # Fallback error response
            return ErrorResult(
                success=False,
                response="I encountered an unexpected technical issue. Please try again.",
                error_type="error_handler_failure",
                severity=ErrorSeverity.HIGH,
                execution_time=time.time() - start_time,
                correlation_id=correlation_id,
                retry_recommended=True
            )

    def _classify_error(self, exception: Exception, context: ErrorContext) -> ErrorType:
        """Classify an error based on exception type and message"""
        exception_str = str(exception).lower()
        exception_type = type(exception).__name__.lower()
        
        # Check for specific patterns in error message
        for pattern_type, patterns in self.error_patterns.items():
            if any(pattern in exception_str for pattern in patterns):
                if pattern_type == 'rate_limit':
                    return ErrorType.AI_RATE_LIMITED
                elif pattern_type == 'timeout':
                    return ErrorType.AI_TIMEOUT
                elif pattern_type == 'unavailable':
                    return ErrorType.AI_SERVICE_UNAVAILABLE
                elif pattern_type == 'network':
                    return ErrorType.NETWORK_ERROR
                elif pattern_type == 'validation':
                    return ErrorType.VALIDATION_ERROR
        
        # Check exception types
        if 'timeout' in exception_type:
            return ErrorType.AI_TIMEOUT
        elif 'connection' in exception_type or 'network' in exception_type:
            return ErrorType.NETWORK_ERROR
        elif 'validation' in exception_type or 'value' in exception_type:
            return ErrorType.VALIDATION_ERROR
        elif 'cache' in exception_type:
            return ErrorType.CACHE_ERROR
        elif context.stage == 'tools':
            return ErrorType.TOOL_EXECUTION_FAILED
        elif context.stage in ['intent', 'response']:
            return ErrorType.AI_SERVICE_UNAVAILABLE
        
        return ErrorType.UNKNOWN_ERROR

    def _determine_severity(
        self, 
        error_type: ErrorType, 
        exception: Exception, 
        context: ErrorContext
    ) -> ErrorSeverity:
        """Determine error severity based on type, context, and retry count"""
        # High retry count increases severity
        if context.retry_count >= 3:
            return ErrorSeverity.CRITICAL
        elif context.retry_count >= 2:
            return ErrorSeverity.HIGH
        
        # Base severity by error type
        base_severity = {
            ErrorType.AI_SERVICE_UNAVAILABLE: ErrorSeverity.HIGH,
            ErrorType.AI_RATE_LIMITED: ErrorSeverity.MEDIUM,
            ErrorType.AI_TIMEOUT: ErrorSeverity.MEDIUM,
            ErrorType.TOOL_EXECUTION_FAILED: ErrorSeverity.LOW,
            ErrorType.NETWORK_ERROR: ErrorSeverity.MEDIUM,
            ErrorType.CACHE_ERROR: ErrorSeverity.LOW,
            ErrorType.VALIDATION_ERROR: ErrorSeverity.LOW,
            ErrorType.UNKNOWN_ERROR: ErrorSeverity.MEDIUM
        }.get(error_type, ErrorSeverity.MEDIUM)
        
        # Adjust based on execution time (longer = more severe)
        if context.execution_time > 30:
            if base_severity == ErrorSeverity.LOW:
                return ErrorSeverity.MEDIUM
            elif base_severity == ErrorSeverity.MEDIUM:
                return ErrorSeverity.HIGH
        
        return base_severity

    def _generate_user_message(
        self,
        error_type: ErrorType,
        severity: ErrorSeverity,
        context: ErrorContext
    ) -> str:
        """Generate user-friendly error message"""
        try:
            # Get base message for error type and severity
            base_message = self.error_messages.get(error_type, {}).get(
                severity,
                "I encountered an unexpected issue. Let me try to help you anyway."
            )

            # Add context-specific information
            if context.retry_count > 0:
                if context.retry_count == 1:
                    base_message += " Let me try that again..."
                elif context.retry_count == 2:
                    base_message += " Still having issues. Trying a different approach..."
                else:
                    base_message += " I'm having persistent difficulties. Let me try one more time..."

            return base_message

        except Exception as e:
            logger.warning(f"Error generating user message: {e}")
            return "I encountered an issue while processing your request. Please try again."

    def _should_retry(
        self,
        error_type: ErrorType,
        severity: ErrorSeverity,
        context: ErrorContext
    ) -> bool:
        """Determine if retry is recommended"""
        # Don't retry if we've already tried too many times
        if context.retry_count >= 3:
            return False

        # Don't retry critical errors
        if severity == ErrorSeverity.CRITICAL:
            return False

        # Don't retry validation errors
        if error_type == ErrorType.VALIDATION_ERROR:
            return False

        # Retry eligible error types
        retry_eligible = {
            ErrorType.AI_TIMEOUT,
            ErrorType.NETWORK_ERROR,
            ErrorType.AI_RATE_LIMITED,
            ErrorType.TOOL_EXECUTION_FAILED,
            ErrorType.AI_SERVICE_UNAVAILABLE
        }

        return error_type in retry_eligible

    def _should_circuit_break(self, circuit_breaker_key: str, severity: ErrorSeverity) -> bool:
        """Check if circuit breaker should trip"""
        if circuit_breaker_key not in self.circuit_breakers:
            self.circuit_breakers[circuit_breaker_key] = {
                'failure_count': 0,
                'last_failure_time': 0,
                'state': 'closed',  # closed, open, half_open
                'half_open_attempts': 0
            }

        breaker = self.circuit_breakers[circuit_breaker_key]
        current_time = time.time()

        # Update failure count for high/critical errors
        if severity in [ErrorSeverity.HIGH, ErrorSeverity.CRITICAL]:
            breaker['failure_count'] += 1
            breaker['last_failure_time'] = current_time

        # Check if we should trip the breaker
        if (breaker['state'] == 'closed' and
            breaker['failure_count'] >= self.circuit_breaker_config['failure_threshold']):
            breaker['state'] = 'open'
            self.error_stats['circuit_breaker_trips'] += 1
            logger.warning(f"Circuit breaker tripped for {circuit_breaker_key}")
            return True

        # Check if we should move from open to half-open
        if (breaker['state'] == 'open' and
            current_time - breaker['last_failure_time'] > self.circuit_breaker_config['recovery_timeout']):
            breaker['state'] = 'half_open'
            breaker['half_open_attempts'] = 0
            logger.info(f"Circuit breaker moving to half-open for {circuit_breaker_key}")

        # Check if we should close the breaker from half-open
        if breaker['state'] == 'half_open':
            breaker['half_open_attempts'] += 1
            if breaker['half_open_attempts'] >= self.circuit_breaker_config['half_open_max_calls']:
                if severity in [ErrorSeverity.HIGH, ErrorSeverity.CRITICAL]:
                    # Still failing, go back to open
                    breaker['state'] = 'open'
                    breaker['last_failure_time'] = current_time
                    return True
                else:
                    # Success, close the breaker
                    breaker['state'] = 'closed'
                    breaker['failure_count'] = 0
                    logger.info(f"Circuit breaker closed for {circuit_breaker_key}")

        return breaker['state'] == 'open'

    async def _handle_circuit_break(
        self,
        circuit_breaker_key: str,
        context: ErrorContext,
        correlation_id: str
    ) -> ErrorResult:
        """Handle circuit breaker trip"""
        logger.warning(f"Circuit breaker active for {circuit_breaker_key}",
                      extra={'correlation_id': correlation_id})

        return ErrorResult(
            success=False,
            response="I'm temporarily limiting requests to this service due to repeated failures. Please try again in a few minutes.",
            error_type="circuit_breaker_open",
            severity=ErrorSeverity.HIGH,
            execution_time=0.0,
            correlation_id=correlation_id,
            retry_recommended=False,
            fallback_used="circuit_breaker"
        )

    def _log_error(
        self,
        exception: Exception,
        error_type: ErrorType,
        severity: ErrorSeverity,
        context: ErrorContext,
        correlation_id: str
    ):
        """Log error with appropriate level based on severity"""
        log_data = {
            'correlation_id': correlation_id,
            'error_type': error_type.value,
            'severity': severity.value,
            'stage': context.stage,
            'retry_count': context.retry_count,
            'execution_time': context.execution_time
        }

        if severity == ErrorSeverity.CRITICAL:
            logger.critical(f"Critical error in {context.stage}: {exception}", extra=log_data)
        elif severity == ErrorSeverity.HIGH:
            logger.error(f"High severity error in {context.stage}: {exception}", extra=log_data)
        elif severity == ErrorSeverity.MEDIUM:
            logger.warning(f"Medium severity error in {context.stage}: {exception}", extra=log_data)
        else:
            logger.info(f"Low severity error in {context.stage}: {exception}", extra=log_data)

    def get_error_stats(self) -> Dict[str, Any]:
        """Get current error statistics"""
        return self.error_stats.copy()

    def reset_circuit_breaker(self, circuit_breaker_key: str) -> bool:
        """Manually reset a circuit breaker"""
        if circuit_breaker_key in self.circuit_breakers:
            self.circuit_breakers[circuit_breaker_key] = {
                'failure_count': 0,
                'last_failure_time': 0,
                'state': 'closed',
                'half_open_attempts': 0
            }
            logger.info(f"Circuit breaker manually reset for {circuit_breaker_key}")
            return True
        return False

    def create_error_embed(self, error_result: ErrorResult) -> Optional[Dict[str, Any]]:
        """Create Discord embed for error (if needed)"""
        try:
            if error_result.severity in [ErrorSeverity.HIGH, ErrorSeverity.CRITICAL]:
                return {
                    "title": "⚠️ Error Occurred",
                    "description": error_result.response,
                    "color": 0xff0000,  # Red color
                    "fields": [
                        {
                            "name": "Error Type",
                            "value": error_result.error_type,
                            "inline": True
                        },
                        {
                            "name": "Severity",
                            "value": error_result.severity.value,
                            "inline": True
                        }
                    ],
                    "footer": {
                        "text": f"Correlation ID: {error_result.correlation_id}"
                    }
                }
            return None
        except Exception as e:
            logger.warning(f"Error creating error embed: {e}")
            return None
