Audit logger clarification
==========================

This directory contains audit and compliance related logging utilities for the
`ask` pipeline. There are two separate audit logger implementations in the
project:

- `src/bot/pipeline/commands/ask/audit/audit_logger.py` — pipeline-focused
  audit logging used across pipeline stages and internal tooling.
- `src/bot/pipeline/commands/ask/compliance/audit_logger.py` — compliance-
  focused audit logging intended for regulatory/data-retention workflows and
  compliance reporting.

Current approach
----------------
To avoid deleting or changing existing files, this directory keeps the
compliance-specific `audit_logger.py` in-place. A small compatibility shim
(`compliance_logger.py`) is provided so code can import a descriptive name
(`compliance_logger`) while both implementations remain available.

Next steps (manual decision required)
-----------------------------------
- If you want a single canonical audit logger, consider merging functionality
  and removing the duplicate file only after review. Do not delete files
  without explicit consent.
- If you prefer to keep separation, update import sites to use
  `compliance.compliance_logger` for compliance workflows and
  `audit.audit_logger` for pipeline workflows.

This README is informational and does not modify any behavior.


