"""
Data Management and Compliance for ASK Pipeline

Provides comprehensive data governance and compliance:
- Implement data retention and privacy controls for GDPR/CCPA compliance
- Add data classification and sensitivity labeling
- Create data anonymization and pseudonymization
- Implement data subject rights management
- Add compliance reporting and audit trails
"""

import asyncio
import logging
import hashlib
import json
from typing import Dict, Any, Optional, List, Union, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import uuid
import re

logger = logging.getLogger(__name__)

class DataClassification(Enum):
    """Data classification levels"""
    PUBLIC = "public"
    INTERNAL = "internal"
    CONFIDENTIAL = "confidential"
    RESTRICTED = "restricted"
    TOP_SECRET = "top_secret"

class DataSubjectRights(Enum):
    """Data subject rights under GDPR/CCPA"""
    ACCESS = "access"
    RECTIFICATION = "rectification"
    ERASURE = "erasure"
    PORTABILITY = "portability"
    RESTRICTION = "restriction"
    OBJECTION = "objection"

class RetentionPolicy(Enum):
    """Data retention policy types"""
    LEGAL_REQUIREMENT = "legal_requirement"
    BUSINESS_NEED = "business_need"
    CONSENT_BASED = "consent_based"
    AUTOMATIC_DELETION = "automatic_deletion"

@dataclass
class DataRecord:
    """Data record with compliance metadata"""
    record_id: str
    data_type: str
    classification: DataClassification
    retention_policy: RetentionPolicy
    retention_period: int  # days
    created_at: datetime
    expires_at: Optional[datetime] = None
    data_subject_id: Optional[str] = None
    processing_purpose: str = ""
    legal_basis: str = ""
    consent_id: Optional[str] = None
    anonymized: bool = False
    pseudonymized: bool = False
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class DataSubject:
    """Data subject information"""
    subject_id: str
    email: Optional[str] = None
    name: Optional[str] = None
    consent_given: bool = False
    consent_date: Optional[datetime] = None
    consent_withdrawn: bool = False
    consent_withdrawal_date: Optional[datetime] = None
    data_retention_consent: bool = False
    marketing_consent: bool = False
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class DataRequest:
    """Data subject request"""
    request_id: str
    subject_id: str
    request_type: DataSubjectRights
    description: str
    status: str  # pending, processing, completed, rejected
    created_at: datetime
    completed_at: Optional[datetime] = None
    response_data: Optional[Dict[str, Any]] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class ComplianceReport:
    """Compliance report"""
    report_id: str
    report_type: str
    generated_at: datetime
    period_start: datetime
    period_end: datetime
    total_records: int
    records_by_classification: Dict[str, int] = field(default_factory=dict)
    retention_compliance: Dict[str, Any] = field(default_factory=dict)
    data_subject_requests: int = 0
    privacy_incidents: int = 0
    metadata: Dict[str, Any] = field(default_factory=dict)

class DataManager:
    """Data management and compliance system"""
    
    def __init__(self):
        self.data_records: Dict[str, DataRecord] = {}
        self.data_subjects: Dict[str, DataSubject] = {}
        self.data_requests: Dict[str, DataRequest] = {}
        self.compliance_reports: List[ComplianceReport] = []
        
        # Data retention policies
        self.retention_policies = {
            DataClassification.PUBLIC: 365,  # 1 year
            DataClassification.INTERNAL: 2555,  # 7 years
            DataClassification.CONFIDENTIAL: 1095,  # 3 years
            DataClassification.RESTRICTED: 1825,  # 5 years
            DataClassification.TOP_SECRET: 3650  # 10 years
        }
        
        # Start retention monitoring
        self.retention_task: Optional[asyncio.Task] = None
        asyncio.create_task(self.start_retention_monitoring())
    
    async def start_retention_monitoring(self):
        """Start data retention monitoring"""
        if self.retention_task:
            return
        
        self.retention_task = asyncio.create_task(self._retention_monitoring_loop())
        logger.info("Data retention monitoring started")
    
    async def stop_retention_monitoring(self):
        """Stop data retention monitoring"""
        if self.retention_task:
            self.retention_task.cancel()
            self.retention_task = None
        logger.info("Data retention monitoring stopped")
    
    async def _retention_monitoring_loop(self):
        """Data retention monitoring loop"""
        while True:
            try:
                await self._check_expired_records()
                await self._cleanup_expired_data()
                await asyncio.sleep(3600)  # Check every hour
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Retention monitoring error: {e}")
                await asyncio.sleep(300)
    
    async def _check_expired_records(self):
        """Check for expired records"""
        now = datetime.utcnow()
        expired_records = []
        
        for record_id, record in self.data_records.items():
            if record.expires_at and record.expires_at <= now:
                expired_records.append(record_id)
        
        if expired_records:
            logger.info(f"Found {len(expired_records)} expired records")
            # In a real implementation, you would handle expired records here
    
    async def _cleanup_expired_data(self):
        """Cleanup expired data"""
        now = datetime.utcnow()
        records_to_remove = []
        
        for record_id, record in self.data_records.items():
            if record.expires_at and record.expires_at <= now:
                records_to_remove.append(record_id)
        
        for record_id in records_to_remove:
            del self.data_records[record_id]
            logger.info(f"Removed expired record: {record_id}")
    
    def create_data_record(self, data_type: str, classification: DataClassification,
                          data_subject_id: Optional[str] = None,
                          processing_purpose: str = "",
                          legal_basis: str = "",
                          consent_id: Optional[str] = None,
                          custom_retention: Optional[int] = None) -> str:
        """Create a new data record"""
        record_id = str(uuid.uuid4())
        
        # Determine retention period
        retention_period = custom_retention or self.retention_policies.get(classification, 365)
        expires_at = datetime.utcnow() + timedelta(days=retention_period)
        
        record = DataRecord(
            record_id=record_id,
            data_type=data_type,
            classification=classification,
            retention_policy=RetentionPolicy.BUSINESS_NEED,
            retention_period=retention_period,
            created_at=datetime.utcnow(),
            expires_at=expires_at,
            data_subject_id=data_subject_id,
            processing_purpose=processing_purpose,
            legal_basis=legal_basis,
            consent_id=consent_id
        )
        
        self.data_records[record_id] = record
        logger.info(f"Created data record: {record_id}")
        return record_id
    
    def register_data_subject(self, subject_id: str, email: Optional[str] = None,
                            name: Optional[str] = None, consent_given: bool = False) -> str:
        """Register a new data subject"""
        subject = DataSubject(
            subject_id=subject_id,
            email=email,
            name=name,
            consent_given=consent_given,
            consent_date=datetime.utcnow() if consent_given else None
        )
        
        self.data_subjects[subject_id] = subject
        logger.info(f"Registered data subject: {subject_id}")
        return subject_id
    
    def update_consent(self, subject_id: str, consent_given: bool,
                      data_retention_consent: bool = False,
                      marketing_consent: bool = False) -> bool:
        """Update data subject consent"""
        if subject_id not in self.data_subjects:
            return False
        
        subject = self.data_subjects[subject_id]
        
        if consent_given and not subject.consent_given:
            subject.consent_given = True
            subject.consent_date = datetime.utcnow()
            subject.consent_withdrawn = False
            subject.consent_withdrawal_date = None
        elif not consent_given and subject.consent_given:
            subject.consent_withdrawn = True
            subject.consent_withdrawal_date = datetime.utcnow()
            subject.consent_given = False
        
        subject.data_retention_consent = data_retention_consent
        subject.marketing_consent = marketing_consent
        
        logger.info(f"Updated consent for subject: {subject_id}")
        return True
    
    def create_data_request(self, subject_id: str, request_type: DataSubjectRights,
                          description: str) -> str:
        """Create a data subject request"""
        request_id = str(uuid.uuid4())
        
        request = DataRequest(
            request_id=request_id,
            subject_id=subject_id,
            request_type=request_type,
            description=description,
            status="pending",
            created_at=datetime.utcnow()
        )
        
        self.data_requests[request_id] = request
        logger.info(f"Created data request: {request_id}")
        return request_id
    
    async def process_data_request(self, request_id: str) -> bool:
        """Process a data subject request"""
        if request_id not in self.data_requests:
            return False
        
        request = self.data_requests[request_id]
        request.status = "processing"
        
        try:
            if request.request_type == DataSubjectRights.ACCESS:
                await self._process_access_request(request)
            elif request.request_type == DataSubjectRights.ERASURE:
                await self._process_erasure_request(request)
            elif request.request_type == DataSubjectRights.PORTABILITY:
                await self._process_portability_request(request)
            elif request.request_type == DataSubjectRights.RECTIFICATION:
                await self._process_rectification_request(request)
            elif request.request_type == DataSubjectRights.RESTRICTION:
                await self._process_restriction_request(request)
            elif request.request_type == DataSubjectRights.OBJECTION:
                await self._process_objection_request(request)
            
            request.status = "completed"
            request.completed_at = datetime.utcnow()
            
            logger.info(f"Processed data request: {request_id}")
            return True
            
        except Exception as e:
            request.status = "rejected"
            request.metadata["error"] = str(e)
            logger.error(f"Error processing data request {request_id}: {e}")
            return False
    
    async def _process_access_request(self, request: DataRequest):
        """Process data access request"""
        # Find all records for this subject
        subject_records = [
            record for record in self.data_records.values()
            if record.data_subject_id == request.subject_id
        ]
        
        request.response_data = {
            "subject_id": request.subject_id,
            "records": [
                {
                    "record_id": record.record_id,
                    "data_type": record.data_type,
                    "classification": record.classification.value,
                    "created_at": record.created_at.isoformat(),
                    "processing_purpose": record.processing_purpose,
                    "legal_basis": record.legal_basis
                }
                for record in subject_records
            ],
            "total_records": len(subject_records)
        }
    
    async def _process_erasure_request(self, request: DataRequest):
        """Process data erasure request"""
        # Find and mark records for deletion
        subject_records = [
            record_id for record_id, record in self.data_records.items()
            if record.data_subject_id == request.subject_id
        ]
        
        # In a real implementation, you would delete the actual data
        # Here we just mark records as deleted
        for record_id in subject_records:
            if record_id in self.data_records:
                del self.data_records[record_id]
        
        request.response_data = {
            "subject_id": request.subject_id,
            "records_deleted": len(subject_records),
            "deletion_date": datetime.utcnow().isoformat()
        }
    
    async def _process_portability_request(self, request: DataRequest):
        """Process data portability request"""
        # Find all records for this subject
        subject_records = [
            record for record in self.data_records.values()
            if record.data_subject_id == request.subject_id
        ]
        
        # Create portable data format
        portable_data = {
            "subject_id": request.subject_id,
            "export_date": datetime.utcnow().isoformat(),
            "data": [
                {
                    "record_id": record.record_id,
                    "data_type": record.data_type,
                    "created_at": record.created_at.isoformat(),
                    "metadata": record.metadata
                }
                for record in subject_records
            ]
        }
        
        request.response_data = portable_data
    
    async def _process_rectification_request(self, request: DataRequest):
        """Process data rectification request"""
        # This would handle data correction requests
        request.response_data = {
            "subject_id": request.subject_id,
            "status": "rectification_processed",
            "processed_at": datetime.utcnow().isoformat()
        }
    
    async def _process_restriction_request(self, request: DataRequest):
        """Process data restriction request"""
        # This would handle data processing restriction requests
        request.response_data = {
            "subject_id": request.subject_id,
            "status": "restriction_processed",
            "processed_at": datetime.utcnow().isoformat()
        }
    
    async def _process_objection_request(self, request: DataRequest):
        """Process data objection request"""
        # This would handle data processing objection requests
        request.response_data = {
            "subject_id": request.subject_id,
            "status": "objection_processed",
            "processed_at": datetime.utcnow().isoformat()
        }
    
    def anonymize_data(self, data: str, method: str = "hash") -> str:
        """Anonymize data using specified method"""
        if method == "hash":
            return hashlib.sha256(data.encode()).hexdigest()[:16]
        elif method == "mask":
            if "@" in data:  # Email
                local, domain = data.split("@", 1)
                return f"{local[0]}***@{domain}"
            elif len(data) > 4:  # Name or other text
                return f"{data[0]}***{data[-1]}"
            else:
                return "***"
        elif method == "replace":
            return "ANONYMIZED"
        else:
            return data
    
    def pseudonymize_data(self, data: str, subject_id: str) -> str:
        """Pseudonymize data using subject ID as seed"""
        # Create a deterministic pseudonym based on subject ID
        seed = f"{subject_id}_{data}"
        return hashlib.sha256(seed.encode()).hexdigest()[:16]
    
    def classify_data(self, data: str, context: str = "") -> DataClassification:
        """Classify data based on content and context"""
        # Simple classification logic - in practice, this would be more sophisticated
        
        # Check for sensitive patterns
        sensitive_patterns = [
            r'\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b',  # Credit card
            r'\b\d{3}-\d{2}-\d{4}\b',  # SSN
            r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',  # Email
            r'\b\d{3}-\d{3}-\d{4}\b',  # Phone
        ]
        
        for pattern in sensitive_patterns:
            if re.search(pattern, data):
                return DataClassification.CONFIDENTIAL
        
        # Check for business-sensitive terms
        business_terms = ['revenue', 'profit', 'strategy', 'confidential', 'internal']
        if any(term in data.lower() for term in business_terms):
            return DataClassification.INTERNAL
        
        # Check context
        if 'public' in context.lower() or 'announcement' in context.lower():
            return DataClassification.PUBLIC
        
        # Default classification
        return DataClassification.INTERNAL
    
    def generate_compliance_report(self, report_type: str = "monthly") -> ComplianceReport:
        """Generate compliance report"""
        now = datetime.utcnow()
        
        if report_type == "monthly":
            period_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            period_end = now
        elif report_type == "quarterly":
            quarter = (now.month - 1) // 3 + 1
            period_start = now.replace(month=(quarter - 1) * 3 + 1, day=1, hour=0, minute=0, second=0, microsecond=0)
            period_end = now
        else:
            period_start = now - timedelta(days=30)
            period_end = now
        
        # Count records by classification
        records_by_classification = {}
        for classification in DataClassification:
            count = len([
                record for record in self.data_records.values()
                if record.classification == classification and
                period_start <= record.created_at <= period_end
            ])
            records_by_classification[classification.value] = count
        
        # Calculate retention compliance
        total_records = sum(records_by_classification.values())
        expired_records = len([
            record for record in self.data_records.values()
            if record.expires_at and record.expires_at <= now
        ])
        
        retention_compliance = {
            "total_records": total_records,
            "expired_records": expired_records,
            "compliance_rate": (total_records - expired_records) / total_records if total_records > 0 else 1.0
        }
        
        # Count data subject requests
        data_subject_requests = len([
            request for request in self.data_requests.values()
            if period_start <= request.created_at <= period_end
        ])
        
        report = ComplianceReport(
            report_id=str(uuid.uuid4()),
            report_type=report_type,
            generated_at=now,
            period_start=period_start,
            period_end=period_end,
            total_records=total_records,
            records_by_classification=records_by_classification,
            retention_compliance=retention_compliance,
            data_subject_requests=data_subject_requests,
            privacy_incidents=0  # This would be tracked separately
        )
        
        self.compliance_reports.append(report)
        logger.info(f"Generated compliance report: {report.report_id}")
        return report
    
    def get_data_subject_records(self, subject_id: str) -> List[DataRecord]:
        """Get all records for a data subject"""
        return [
            record for record in self.data_records.values()
            if record.data_subject_id == subject_id
        ]
    
    def get_retention_summary(self) -> Dict[str, Any]:
        """Get data retention summary"""
        now = datetime.utcnow()
        
        summary = {
            "total_records": len(self.data_records),
            "records_by_classification": {},
            "expiring_soon": 0,
            "expired": 0,
            "retention_policies": {}
        }
        
        # Count by classification
        for classification in DataClassification:
            count = len([
                record for record in self.data_records.values()
                if record.classification == classification
            ])
            summary["records_by_classification"][classification.value] = count
        
        # Count expiring and expired records
        for record in self.data_records.values():
            if record.expires_at:
                if record.expires_at <= now:
                    summary["expired"] += 1
                elif record.expires_at <= now + timedelta(days=30):
                    summary["expiring_soon"] += 1
        
        # Add retention policies
        for classification, days in self.retention_policies.items():
            summary["retention_policies"][classification.value] = days
        
        return summary
    
    def export_compliance_data(self, output_file: str) -> bool:
        """Export compliance data to JSON file"""
        try:
            data = {
                "timestamp": datetime.utcnow().isoformat(),
                "data_records": [
                    {
                        "record_id": record.record_id,
                        "data_type": record.data_type,
                        "classification": record.classification.value,
                        "retention_policy": record.retention_policy.value,
                        "retention_period": record.retention_period,
                        "created_at": record.created_at.isoformat(),
                        "expires_at": record.expires_at.isoformat() if record.expires_at else None,
                        "data_subject_id": record.data_subject_id,
                        "processing_purpose": record.processing_purpose,
                        "legal_basis": record.legal_basis,
                        "consent_id": record.consent_id,
                        "anonymized": record.anonymized,
                        "pseudonymized": record.pseudonymized,
                        "metadata": record.metadata
                    }
                    for record in self.data_records.values()
                ],
                "data_subjects": [
                    {
                        "subject_id": subject.subject_id,
                        "email": subject.email,
                        "name": subject.name,
                        "consent_given": subject.consent_given,
                        "consent_date": subject.consent_date.isoformat() if subject.consent_date else None,
                        "consent_withdrawn": subject.consent_withdrawn,
                        "consent_withdrawal_date": subject.consent_withdrawal_date.isoformat() if subject.consent_withdrawal_date else None,
                        "data_retention_consent": subject.data_retention_consent,
                        "marketing_consent": subject.marketing_consent,
                        "metadata": subject.metadata
                    }
                    for subject in self.data_subjects.values()
                ],
                "data_requests": [
                    {
                        "request_id": request.request_id,
                        "subject_id": request.subject_id,
                        "request_type": request.request_type.value,
                        "description": request.description,
                        "status": request.status,
                        "created_at": request.created_at.isoformat(),
                        "completed_at": request.completed_at.isoformat() if request.completed_at else None,
                        "response_data": request.response_data,
                        "metadata": request.metadata
                    }
                    for request in self.data_requests.values()
                ],
                "retention_summary": self.get_retention_summary()
            }
            
            with open(output_file, 'w') as f:
                json.dump(data, f, indent=2, default=str)
            
            logger.info(f"Compliance data exported: {output_file}")
            return True
            
        except Exception as e:
            logger.error(f"Error exporting compliance data: {e}")
            return False

# Global instance
_data_manager: Optional[DataManager] = None

def get_data_manager() -> DataManager:
    """Get global data manager"""
    global _data_manager
    if _data_manager is None:
        _data_manager = DataManager()
    return _data_manager

def cleanup_data_manager():
    """Cleanup global data manager"""
    global _data_manager
    if _data_manager:
        _data_manager = None
