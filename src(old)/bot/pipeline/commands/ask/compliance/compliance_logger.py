"""Compatibility shim to expose a descriptive compliance logger API.

This module re-exports the compliance-focused audit logger implementation
without modifying or deleting the original file. Import sites can switch to
`from src.bot.pipeline.commands.ask.compliance import compliance_logger` and
use `compliance_logger` for clarity.
"""

from .audit_logger import *  # re-export all public names from the compliance audit logger

__all__ = [name for name in globals().keys() if not name.startswith("_")]


