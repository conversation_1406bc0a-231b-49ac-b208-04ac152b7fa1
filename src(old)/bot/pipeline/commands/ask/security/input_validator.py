"""
Input Validator for ASK Command Security

Provides comprehensive input sanitization, validation, and prompt injection detection
to protect against malicious queries and ensure system security.
"""

import re
import unicodedata
from dataclasses import dataclass
from typing import List, Optional, Dict, Any
from enum import Enum

from src.shared.error_handling.logging import get_logger

logger = get_logger(__name__)


class RiskLevel(Enum):
    """Security risk levels for validation results"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class ValidationResult:
    """Result of input validation"""
    is_valid: bool
    sanitized_query: str
    detected_issues: List[str]
    risk_score: float
    risk_level: RiskLevel
    blocked_reason: Optional[str] = None


class InputValidator:
    """
    Comprehensive input validator with security controls
    
    Features:
    - Query length limits (max 2000 characters)
    - Character filtering and normalization
    - Prompt injection detection
    - UTF-8 encoding validation
    - Malicious pattern detection
    """
    
    # Configuration constants
    MAX_QUERY_LENGTH = 2000
    MIN_QUERY_LENGTH = 3
    MAX_RISK_SCORE = 100.0
    BLOCK_THRESHOLD = 75.0
    
    # Prompt injection patterns (basic detection)
    PROMPT_INJECTION_PATTERNS = [
        # Direct instruction attempts
        r'(?i)\b(ignore|forget|disregard)\s+(previous|above|all|your)\s+(instructions?|prompts?|rules?)',
        r'(?i)\b(act\s+as|pretend\s+to\s+be|roleplay\s+as)\s+(?!.*trading|.*financial)',
        r'(?i)\b(system\s+prompt|initial\s+prompt|base\s+prompt)',
        
        # Jailbreak attempts
        r'(?i)\b(jailbreak|bypass|override)\b',
        r'(?i)\b(developer\s+mode|admin\s+mode|debug\s+mode)',
        r'(?i)\b(unrestricted|unlimited|no\s+limits?)',
        r'(?i)\b(DAN|Do\s+Anything\s+Now)',  # Added DAN detection
        
        # Injection markers
        r'(?i)```\s*(system|user|assistant)',
        r'(?i)<\s*(system|user|assistant)\s*>',
        r'(?i)\[INST\]|\[/INST\]',
        
        # Harmful content attempts
        r'(?i)\b(generate|create|write)\s+.*(malware|virus|exploit)',
        r'(?i)\b(hack|crack|break\s+into)',
        
        # Financial manipulation attempts
        r'(?i)\b(pump\s+and\s+dump|market\s+manipulation)',
        r'(?i)\b(insider\s+trading|illegal\s+trading)',
    ]
    
    # Dangerous characters and sequences
    DANGEROUS_CHARS = [
        '\x00', '\x01', '\x02', '\x03', '\x04', '\x05', '\x06', '\x07',
        '\x08', '\x0b', '\x0c', '\x0e', '\x0f', '\x10', '\x11', '\x12',
        '\x13', '\x14', '\x15', '\x16', '\x17', '\x18', '\x19', '\x1a',
        '\x1b', '\x1c', '\x1d', '\x1e', '\x1f', '\x7f'
    ]
    
    # Suspicious patterns that increase risk score
    SUSPICIOUS_PATTERNS = [
        (r'(?i)\b(sql|union|select|drop|delete|insert|update)\b', 25),  # High risk for SQL injection
        (r'(?i)<script|javascript:|data:|<img.*onerror|<iframe|<object|<embed', 25),  # High risk for XSS
        (r'(?i)\b(exec|eval|system|shell)\b', 25),  # High risk for code execution
        (r'(?i)\b(password|token|key|secret)\b', 15),  # Medium-high risk for credential theft
        (r'(?i)\b(admin|root|sudo)\b', 12),  # Medium risk for privilege escalation
        (r'[^\x00-\x7F]{20,}', 8),  # Excessive non-ASCII
        (r'(.)\1{50,}', 10),  # Excessive repetition
        (r'[!@#$%^&*()]{10,}', 8),  # Excessive special chars
    ]
    
    def __init__(self):
        """Initialize the input validator"""
        self.compiled_patterns = [
            re.compile(pattern) for pattern in self.PROMPT_INJECTION_PATTERNS
        ]
        self.suspicious_compiled = [
            (re.compile(pattern), score) for pattern, score in self.SUSPICIOUS_PATTERNS
        ]
    
    async def validate_query(self, query: str, user_id: str) -> ValidationResult:
        """
        Validate and sanitize user query
        
        Args:
            query: Raw user query
            user_id: User identifier for logging
            
        Returns:
            ValidationResult with validation status and sanitized query
        """
        try:
            # Initialize result tracking
            issues = []
            risk_score = 0.0
            
            # Basic validation
            if not query or not isinstance(query, str):
                return ValidationResult(
                    is_valid=False,
                    sanitized_query="",
                    detected_issues=["Empty or invalid query"],
                    risk_score=100.0,
                    risk_level=RiskLevel.CRITICAL,
                    blocked_reason="Invalid query format"
                )
            
            # Length validation
            if len(query) > self.MAX_QUERY_LENGTH:
                issues.append(f"Query too long ({len(query)} > {self.MAX_QUERY_LENGTH})")
                risk_score += 20
            
            if len(query.strip()) < self.MIN_QUERY_LENGTH:
                issues.append(f"Query too short ({len(query.strip())} < {self.MIN_QUERY_LENGTH})")
                risk_score += 15
            
            # UTF-8 validation and normalization
            try:
                normalized_query = unicodedata.normalize('NFKC', query)
            except Exception as e:
                issues.append(f"Unicode normalization failed: {str(e)}")
                risk_score += 25
                normalized_query = query
            
            # Character filtering
            sanitized_query, char_issues, char_risk = self._filter_dangerous_characters(normalized_query)
            issues.extend(char_issues)
            risk_score += char_risk
            
            # Prompt injection detection
            injection_detected, injection_issues, injection_risk = self._detect_prompt_injection(sanitized_query)
            issues.extend(injection_issues)
            risk_score += injection_risk
            
            # Suspicious pattern detection
            suspicious_issues, suspicious_risk = self._detect_suspicious_patterns(sanitized_query)
            issues.extend(suspicious_issues)
            risk_score += suspicious_risk
            
            # Determine risk level
            risk_level = self._calculate_risk_level(risk_score)
            
            # Check if query should be blocked
            is_valid = risk_score < self.BLOCK_THRESHOLD
            blocked_reason = None
            if not is_valid:
                blocked_reason = f"Security risk too high (score: {risk_score:.1f})"
            
            # Log security events
            if risk_score > 30 or injection_detected:
                logger.warning(
                    f"Security validation alert for user {user_id}: "
                    f"risk_score={risk_score:.1f}, issues={issues}"
                )
            
            return ValidationResult(
                is_valid=is_valid,
                sanitized_query=sanitized_query.strip(),
                detected_issues=issues,
                risk_score=risk_score,
                risk_level=risk_level,
                blocked_reason=blocked_reason
            )
            
        except Exception as e:
            logger.error(f"Input validation error for user {user_id}: {str(e)}")
            return ValidationResult(
                is_valid=False,
                sanitized_query="",
                detected_issues=[f"Validation error: {str(e)}"],
                risk_score=100.0,
                risk_level=RiskLevel.CRITICAL,
                blocked_reason="Internal validation error"
            )
    
    def _filter_dangerous_characters(self, query: str) -> tuple[str, List[str], float]:
        """Filter out dangerous characters and sequences"""
        issues = []
        risk_score = 0.0
        
        # Remove null bytes and control characters
        filtered_query = query
        for char in self.DANGEROUS_CHARS:
            if char in filtered_query:
                filtered_query = filtered_query.replace(char, '')
                issues.append(f"Removed dangerous character: {repr(char)}")
                risk_score += 5
        
        # Check for excessive whitespace
        if len(query) - len(query.strip()) > 100:
            issues.append("Excessive whitespace detected")
            risk_score += 3
        
        return filtered_query, issues, risk_score
    
    def _detect_prompt_injection(self, query: str) -> tuple[bool, List[str], float]:
        """Detect potential prompt injection attempts"""
        issues = []
        risk_score = 0.0
        injection_detected = False
        
        for pattern in self.compiled_patterns:
            matches = pattern.findall(query)
            if matches:
                injection_detected = True
                issues.append(f"Potential prompt injection detected: {pattern.pattern[:50]}...")
                risk_score += 25  # High penalty for injection attempts
        
        return injection_detected, issues, risk_score
    
    def _detect_suspicious_patterns(self, query: str) -> tuple[List[str], float]:
        """Detect suspicious patterns that increase risk score"""
        issues = []
        risk_score = 0.0
        
        for pattern, score in self.suspicious_compiled:
            matches = pattern.findall(query)
            if matches:
                issues.append(f"Suspicious pattern detected: {pattern.pattern[:30]}...")
                risk_score += score
        
        return issues, risk_score
    
    def _calculate_risk_level(self, risk_score: float) -> RiskLevel:
        """Calculate risk level based on score"""
        if risk_score >= 75:
            return RiskLevel.CRITICAL
        elif risk_score >= 50:
            return RiskLevel.HIGH
        elif risk_score >= 25:
            return RiskLevel.MEDIUM
        else:
            return RiskLevel.LOW
    
    async def sanitize_input(self, query: str) -> str:
        """
        Quick sanitization without full validation
        
        Args:
            query: Raw query string
            
        Returns:
            Sanitized query string
        """
        if not query:
            return ""
        
        # Basic sanitization
        sanitized = unicodedata.normalize('NFKC', query)
        
        # Remove dangerous characters
        for char in self.DANGEROUS_CHARS:
            sanitized = sanitized.replace(char, '')
        
        # Trim and return
        return sanitized.strip()
    
    async def detect_prompt_injection(self, query: str) -> bool:
        """
        Quick prompt injection detection
        
        Args:
            query: Query to check
            
        Returns:
            True if potential injection detected
        """
        for pattern in self.compiled_patterns:
            if pattern.search(query):
                return True
        return False