"""
Rate Limiter for ASK Command Security

Provides Redis-backed rate limiting to prevent spam and abuse while allowing
legitimate users to interact with the trading bot effectively.
"""

import time
from dataclasses import dataclass
from typing import Optional, Dict, Any, Union
from enum import Enum

from src.shared.error_handling.logging import get_logger

logger = get_logger(__name__)


class RateLimitType(Enum):
    """Types of rate limits"""
    USER_PER_MINUTE = "user_per_minute"
    USER_PER_HOUR = "user_per_hour"
    GUILD_PER_MINUTE = "guild_per_minute"
    GUILD_PER_HOUR = "guild_per_hour"
    GLOBAL_PER_SECOND = "global_per_second"


@dataclass
class RateLimitResult:
    """Result of rate limit check"""
    allowed: bool
    remaining: int
    reset_time: float
    limit: int
    retry_after: Optional[float] = None
    limit_type: Optional[RateLimitType] = None


@dataclass
class RateLimitConfig:
    """Configuration for rate limiting"""
    # Per-user limits
    user_requests_per_minute: int = 10
    user_requests_per_hour: int = 100
    
    # Per-guild limits (to prevent server abuse)
    guild_requests_per_minute: int = 50
    guild_requests_per_hour: int = 500
    
    # Global limits (system protection)
    global_requests_per_second: int = 20
    
    # Premium user multipliers
    premium_multiplier: float = 2.0
    admin_multiplier: float = 5.0
    
    # Redis settings
    redis_key_prefix: str = "ask_rate_limit"
    redis_ttl_buffer: int = 60  # Extra TTL to prevent race conditions


class RateLimiter:
    """
    Redis-backed rate limiter with multiple limit types
    
    Features:
    - Per-user rate limits (10/min, 100/hour)
    - Per-guild rate limits to prevent server abuse
    - Global rate limits for system protection
    - Premium user bypass capabilities
    - Sliding window rate limiting
    - Automatic cleanup of expired keys
    """
    
    def __init__(self, config: Optional[RateLimitConfig] = None):
        """Initialize rate limiter with configuration"""
        self.config = config or RateLimitConfig()
        self._redis_client = None
        self._fallback_cache: Dict[str, Dict[str, Any]] = {}
        self._last_cleanup = time.time()
        
    async def _get_redis_client(self):
        """Get Redis client with lazy initialization"""
        if self._redis_client is None:
            try:
                # Try to import and initialize Redis
                import redis.asyncio as redis
                client = redis.Redis(
                    host='localhost',
                    port=6379,
                    db=0,
                    decode_responses=True,
                    socket_connect_timeout=1,
                    socket_timeout=1
                )
                # Test connection
                await client.ping()
                self._redis_client = client
                logger.info("Redis connection established for rate limiting")
            except Exception as e:
                logger.warning(f"Redis not available for rate limiting, using fallback: {e}")
                self._redis_client = False  # Mark as unavailable
        
        return self._redis_client if self._redis_client is not False else None
    
    async def check_rate_limit(
        self, 
        user_id: str, 
        command: str = "ask",
        guild_id: Optional[str] = None,
        user_tier: str = "standard"
    ) -> RateLimitResult:
        """
        Check if request is within rate limits
        
        Args:
            user_id: Discord user ID
            command: Command being executed
            guild_id: Discord guild ID (optional)
            user_tier: User tier (standard, premium, admin)
            
        Returns:
            RateLimitResult with limit status and metadata
        """
        try:
            current_time = time.time()
            
            # Get user multiplier based on tier
            multiplier = self._get_user_multiplier(user_tier)
            
            # Check multiple rate limit types in order of priority
            # Check user limits first (most restrictive for individual users)
            user_minute_limit = int(self.config.user_requests_per_minute * multiplier)
            user_minute_result = await self._check_single_limit(
                f"user:{user_id}:minute", 
                user_minute_limit, 
                60, 
                current_time
            )
            if not user_minute_result.allowed:
                user_minute_result.limit_type = RateLimitType.USER_PER_MINUTE
                logger.warning(
                    f"Rate limit exceeded for user_per_minute: "
                    f"user={user_id}, guild={guild_id}, key=user:{user_id}:minute"
                )
                return user_minute_result
            
            user_hour_limit = int(self.config.user_requests_per_hour * multiplier)
            user_hour_result = await self._check_single_limit(
                f"user:{user_id}:hour", 
                user_hour_limit, 
                3600, 
                current_time
            )
            if not user_hour_result.allowed:
                user_hour_result.limit_type = RateLimitType.USER_PER_HOUR
                logger.warning(
                    f"Rate limit exceeded for user_per_hour: "
                    f"user={user_id}, guild={guild_id}, key=user:{user_id}:hour"
                )
                return user_hour_result
            
            # Check guild limits if guild_id provided
            if guild_id:
                guild_minute_result = await self._check_single_limit(
                    f"guild:{guild_id}:minute", 
                    self.config.guild_requests_per_minute, 
                    60, 
                    current_time
                )
                if not guild_minute_result.allowed:
                    guild_minute_result.limit_type = RateLimitType.GUILD_PER_MINUTE
                    logger.warning(
                        f"Rate limit exceeded for guild_per_minute: "
                        f"user={user_id}, guild={guild_id}, key=guild:{guild_id}:minute"
                    )
                    return guild_minute_result
                
                guild_hour_result = await self._check_single_limit(
                    f"guild:{guild_id}:hour", 
                    self.config.guild_requests_per_hour, 
                    3600, 
                    current_time
                )
                if not guild_hour_result.allowed:
                    guild_hour_result.limit_type = RateLimitType.GUILD_PER_HOUR
                    logger.warning(
                        f"Rate limit exceeded for guild_per_hour: "
                        f"user={user_id}, guild={guild_id}, key=guild:{guild_id}:hour"
                    )
                    return guild_hour_result
            
            # Check global limits last (system protection)
            global_result = await self._check_single_limit(
                "global:second", 
                self.config.global_requests_per_second, 
                1, 
                current_time
            )
            if not global_result.allowed:
                global_result.limit_type = RateLimitType.GLOBAL_PER_SECOND
                logger.warning(
                    f"Rate limit exceeded for global_per_second: "
                    f"user={user_id}, guild={guild_id}, key=global:second"
                )
                return global_result
            
            # All checks passed, record the request
            await self._record_request(user_id, guild_id, current_time)
            
            # Return success result with remaining quota from most restrictive limit
            return RateLimitResult(
                allowed=True,
                remaining=user_minute_result.remaining,
                reset_time=user_minute_result.reset_time,
                limit=user_minute_result.limit,
                limit_type=RateLimitType.USER_PER_MINUTE
            )
            
        except Exception as e:
            logger.error(f"Rate limit check failed for user {user_id}: {e}")
            # Fail open - allow request but log error
            return RateLimitResult(
                allowed=True,
                remaining=999,
                reset_time=time.time() + 60,
                limit=999
            )
    
    async def _check_single_limit(
        self, 
        key: str, 
        limit: int, 
        window: int, 
        current_time: float
    ) -> RateLimitResult:
        """Check a single rate limit using sliding window"""
        redis_client = await self._get_redis_client()
        
        if redis_client:
            return await self._check_redis_limit(redis_client, key, limit, window, current_time)
        else:
            return await self._check_fallback_limit(key, limit, window, current_time)
    
    async def _check_redis_limit(
        self, 
        redis_client, 
        key: str, 
        limit: int, 
        window: int, 
        current_time: float
    ) -> RateLimitResult:
        """Check rate limit using Redis sliding window"""
        try:
            full_key = f"{self.config.redis_key_prefix}:{key}"
            window_start = current_time - window
            
            # Use Redis pipeline for atomic operations
            pipe = redis_client.pipeline()
            
            # Remove expired entries
            pipe.zremrangebyscore(full_key, 0, window_start)
            
            # Count current requests in window
            pipe.zcard(full_key)
            
            # Add current request timestamp
            pipe.zadd(full_key, {str(current_time): current_time})
            
            # Set expiration
            pipe.expire(full_key, window + self.config.redis_ttl_buffer)
            
            # Execute pipeline
            results = await pipe.execute()
            current_count = results[1]  # Count after cleanup
            
            # Check if limit exceeded
            if current_count >= limit:
                # Remove the request we just added since it's rejected
                await redis_client.zrem(full_key, str(current_time))
                
                # Calculate retry after time
                oldest_request = await redis_client.zrange(full_key, 0, 0, withscores=True)
                if oldest_request:
                    retry_after = oldest_request[0][1] + window - current_time
                else:
                    retry_after = window
                
                return RateLimitResult(
                    allowed=False,
                    remaining=0,
                    reset_time=current_time + retry_after,
                    limit=limit,
                    retry_after=max(0, retry_after)
                )
            
            return RateLimitResult(
                allowed=True,
                remaining=limit - current_count - 1,  # -1 for current request
                reset_time=current_time + window,
                limit=limit
            )
            
        except Exception as e:
            logger.error(f"Redis rate limit check failed for key {key}: {e}")
            # Fall back to in-memory check
            return await self._check_fallback_limit(key, limit, window, current_time)
    
    async def _check_fallback_limit(
        self, 
        key: str, 
        limit: int, 
        window: int, 
        current_time: float
    ) -> RateLimitResult:
        """Fallback in-memory rate limiting when Redis is unavailable"""
        # Clean up old entries periodically
        if current_time - self._last_cleanup > 60:
            await self._cleanup_fallback_cache(current_time)
            self._last_cleanup = current_time
        
        if key not in self._fallback_cache:
            self._fallback_cache[key] = {"requests": [], "limit": limit, "window": window}
        
        cache_entry = self._fallback_cache[key]
        window_start = current_time - window
        
        # Remove expired requests
        cache_entry["requests"] = [
            req_time for req_time in cache_entry["requests"] 
            if req_time > window_start
        ]
        
        # Check limit
        if len(cache_entry["requests"]) >= limit:
            oldest_request = min(cache_entry["requests"])
            retry_after = oldest_request + window - current_time
            
            return RateLimitResult(
                allowed=False,
                remaining=0,
                reset_time=current_time + retry_after,
                limit=limit,
                retry_after=max(0, retry_after)
            )
        
        # Don't add current request yet - this is just a check
        # The request will be added later if all checks pass
        
        return RateLimitResult(
            allowed=True,
            remaining=limit - len(cache_entry["requests"]) - 1,  # -1 for current request
            reset_time=current_time + window,
            limit=limit
        )
    
    async def _record_request(self, user_id: str, guild_id: Optional[str], current_time: float):
        """Record successful request for analytics and rate limiting"""
        try:
            # Record in fallback cache if Redis is not available
            redis_client = await self._get_redis_client()
            if not redis_client:
                # Add to fallback cache for all relevant keys
                keys_to_update = [
                    f"user:{user_id}:minute",
                    f"user:{user_id}:hour",
                ]
                
                if guild_id:
                    keys_to_update.extend([
                        f"guild:{guild_id}:minute",
                        f"guild:{guild_id}:hour",
                    ])
                
                keys_to_update.append("global:second")
                
                for key in keys_to_update:
                    if key in self._fallback_cache:
                        self._fallback_cache[key]["requests"].append(current_time)
            
            logger.debug(f"Request recorded: user={user_id}, guild={guild_id}, time={current_time}")
        except Exception as e:
            logger.error(f"Failed to record request: {e}")
    
    async def _cleanup_fallback_cache(self, current_time: float):
        """Clean up expired entries from fallback cache"""
        try:
            keys_to_remove = []
            for key, cache_entry in self._fallback_cache.items():
                window_start = current_time - cache_entry["window"]
                cache_entry["requests"] = [
                    req_time for req_time in cache_entry["requests"] 
                    if req_time > window_start
                ]
                
                # Remove empty entries
                if not cache_entry["requests"]:
                    keys_to_remove.append(key)
            
            for key in keys_to_remove:
                del self._fallback_cache[key]
                
            logger.debug(f"Cleaned up {len(keys_to_remove)} expired cache entries")
        except Exception as e:
            logger.error(f"Fallback cache cleanup failed: {e}")
    
    def _get_user_multiplier(self, user_tier: str) -> float:
        """Get rate limit multiplier based on user tier"""
        multipliers = {
            "admin": self.config.admin_multiplier,
            "premium": self.config.premium_multiplier,
            "standard": 1.0
        }
        return multipliers.get(user_tier.lower(), 1.0)
    
    async def get_remaining_quota(self, user_id: str, guild_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Get remaining quota for all rate limit types
        
        Args:
            user_id: Discord user ID
            guild_id: Discord guild ID (optional)
            
        Returns:
            Dictionary with remaining quotas for each limit type
        """
        try:
            current_time = time.time()
            quotas = {}
            
            # Check user limits
            user_minute = await self._check_single_limit(
                f"user:{user_id}:minute", 
                self.config.user_requests_per_minute, 
                60, 
                current_time
            )
            quotas["user_per_minute"] = user_minute.remaining
            
            user_hour = await self._check_single_limit(
                f"user:{user_id}:hour", 
                self.config.user_requests_per_hour, 
                3600, 
                current_time
            )
            quotas["user_per_hour"] = user_hour.remaining
            
            # Check guild limits if provided
            if guild_id:
                guild_minute = await self._check_single_limit(
                    f"guild:{guild_id}:minute", 
                    self.config.guild_requests_per_minute, 
                    60, 
                    current_time
                )
                quotas["guild_per_minute"] = guild_minute.remaining
                
                guild_hour = await self._check_single_limit(
                    f"guild:{guild_id}:hour", 
                    self.config.guild_requests_per_hour, 
                    3600, 
                    current_time
                )
                quotas["guild_per_hour"] = guild_hour.remaining
            
            return quotas
            
        except Exception as e:
            logger.error(f"Failed to get remaining quota for user {user_id}: {e}")
            return {"error": "Unable to retrieve quota information"}
    
    async def reset_user_limits(self, user_id: str, admin_user_id: str) -> bool:
        """
        Reset rate limits for a specific user (admin function)
        
        Args:
            user_id: User ID to reset limits for
            admin_user_id: Admin user ID performing the reset
            
        Returns:
            True if successful, False otherwise
        """
        try:
            redis_client = await self._get_redis_client()
            
            keys_to_delete = [
                f"{self.config.redis_key_prefix}:user:{user_id}:minute",
                f"{self.config.redis_key_prefix}:user:{user_id}:hour"
            ]
            
            if redis_client:
                await redis_client.delete(*keys_to_delete)
            
            # Also clean fallback cache
            for key in list(self._fallback_cache.keys()):
                if f"user:{user_id}" in key:
                    del self._fallback_cache[key]
            
            logger.info(f"Rate limits reset for user {user_id} by admin {admin_user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to reset limits for user {user_id}: {e}")
            return False