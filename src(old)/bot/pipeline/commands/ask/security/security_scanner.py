"""Security Scanner for the ASK command responses."""

from dataclasses import dataclass
from typing import List, Dict, Any, Optional, Tuple
import re
import logging
from discord.ext import commands

@dataclass
class SecurityResult:
    """Result of a security scan."""
    is_secure: bool
    issues: List[str]
    redacted_content: Optional[str] = None
    risk_level: str = "low"  # low, medium, high

@dataclass
class PIIMatch:
    """A detected PII match."""
    match: str
    pii_type: str
    confidence: float  # 0.0 to 1.0

class SecurityScanner:
    """
    Scans AI responses for security issues including PII, data leakage, and format validation.
    
    Provides redaction capabilities and logging for security events.
    """
    
    def __init__(self):
        # Logger for security events
        self.logger = logging.getLogger(__name__)
        # Enhanced PII patterns with confidence weights
        self.pii_patterns = {
            'email': {
                'pattern': re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'),
                'confidence': 0.95
            },
            'phone': {
                'pattern': re.compile(r'\b(?:\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})\b'),
                'confidence': 0.90
            },
            'ssn': {
                'pattern': re.compile(r'\b\d{3}-\d{2}-\d{4}\b'),
                'confidence': 0.98
            },
            'credit_card': {
                'pattern': re.compile(r'\b(?:\d{4}[-\s]?){3}\d{4}\b'),
                'confidence': 0.85
            },
            'ip_address': {
                'pattern': re.compile(r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b'),
                'confidence': 0.80
            },
            # Basic address pattern (street, city, etc.)
            'address': {
                'pattern': re.compile(r'\d{1,5}\s\w+\s(?:St|Rd|Ave|Blvd|Dr|Ln|Ct|Way|Pl)\b', re.IGNORECASE),
                'confidence': 0.70
            },
        }
        self.redaction_replacement = "[{type}_REDACTED]"
        
        # Data leakage patterns
        self.leakage_patterns = [
            re.compile(r'password\s*[:=]\s*\S+', re.IGNORECASE),
            re.compile(r'api[_-]?key\s*[:=]\s*\S+', re.IGNORECASE),
            re.compile(r'secret\s*[:=]\s*\S+', re.IGNORECASE),
            re.compile(r'token\s*[:=]\s*\S+', re.IGNORECASE),
            re.compile(r'internal\/api', re.IGNORECASE),
        ]
        
        # Format validation patterns
        self.xss_patterns = [
            re.compile(r'<script[^>]*>.*?</script>', re.IGNORECASE | re.DOTALL),
            re.compile(r'javascript:', re.IGNORECASE),
            re.compile(r'on\w+\s*=.*?', re.IGNORECASE),
        ]
        
        # Markdown validation
        self.markdown_pairs = [
            (re.compile(r'\*\*(.*?)\*\*'), r'\*\*'),  # Bold
            (re.compile(r'`(.*?)`'), r'`'),           # Inline code
            (re.compile(r'__(.*?)__'), r'__'),        # Underline
        ]
    
    async def scan_response(self, response: str, context: Dict[str, Any]) -> SecurityResult:
        """
        Perform comprehensive security scan on the response.
        
        Args:
            response: The AI-generated response to scan.
            context: Additional context (e.g., user_id, query).
        
        Returns:
            SecurityResult with scan details.
        """
        issues = []
        
        # Enhanced PII detection and redaction
        pii_matches = self.detect_pii(response)
        redacted = self.redact_pii(response, pii_matches)
        if pii_matches:
            issues.append(f"{len(pii_matches)} PII instances detected and redacted")
        
        # Data leakage check
        if self.check_data_leakage(redacted, context):
            issues.append("Potential data leakage detected")
        
        # Output format validation
        if not self.validate_output_format(redacted):
            issues.append("Invalid output format")
        
        is_secure = len(issues) == 0
        risk_level = self._calculate_risk_level(issues)
        
        # Log security event
        await self.log_security_event(
            response=redacted,
            issues=issues,
            user_id=context.get('user_id'),
            risk_level=risk_level,
            pii_matches=pii_matches
        )
        
        return SecurityResult(
            is_secure=is_secure,
            issues=issues,
            redacted_content=redacted,
            risk_level=risk_level
        )
    
    def detect_pii(self, text: str) -> List[PIIMatch]:
        """
        Detect PII in the text using enhanced regex patterns with confidence scoring.
        
        Args:
            text: The text to scan for PII.
            
        Returns:
            List of detected PII matches.
        """
        matches = []
        for pii_type, pattern_info in self.pii_patterns.items():
            pattern = pattern_info['pattern']
            confidence = pattern_info['confidence']
            for match in pattern.finditer(text):
                matches.append(PIIMatch(
                    match=match.group(),
                    pii_type=pii_type,
                    confidence=confidence
                ))
        return matches
    
    def redact_pii(self, text: str, pii_matches: List[PIIMatch]) -> str:
        """
        Redact PII from text, preserving positions.
        
        Args:
            text: The text to redact.
            pii_matches: List of PII matches to redact.
            
        Returns:
            Redacted text.
        """
        if not pii_matches:
            return text
            
        # Sort matches by position (reverse order to preserve indices)
        sorted_matches = sorted(pii_matches, key=lambda x: text.find(x.match), reverse=True)
        
        redacted = text
        for match in sorted_matches:
            # Find all occurrences of this match
            pattern = re.escape(match.match)
            replacement = self.redaction_replacement.format(type=match.pii_type.upper())
            redacted = re.sub(pattern, replacement, redacted)
            
        return redacted
    
    def check_data_leakage(self, text: str, context: Dict[str, Any]) -> bool:
        """
        Check for potential data leakage in the text.
        
        Args:
            text: The text to check.
            context: Context with sensitive keywords.
            
        Returns:
            True if potential leakage detected, False otherwise.
        """
        # Check for context-sensitive keywords
        sensitive_keywords = context.get("sensitive_keywords", [])
        for keyword in sensitive_keywords:
            if keyword.lower() in text.lower():
                return True
        
        # Check for common leakage patterns
        for pattern in self.leakage_patterns:
            if pattern.search(text):
                return True
                
        return False
    
    def validate_output_format(self, text: str) -> bool:
        """
        Validate output format to prevent XSS and malformed responses.
        
        Args:
            text: The text to validate.
            
        Returns:
            True if valid, False otherwise.
        """
        # Length validation
        if len(text) > 4000:  # Reasonable limit for Discord messages
            return False
            
        # XSS pattern detection
        for pattern in self.xss_patterns:
            if pattern.search(text):
                return False
        
        # Markdown validation - check for unbalanced pairs
        if not self._validate_markdown_balance(text):
            return False
            
        return True
    
    def _validate_markdown_balance(self, text: str) -> bool:
        """
        Validate markdown syntax balance.
        
        Args:
            text: The text to validate.
            
        Returns:
            True if balanced, False otherwise.
        """
        # Simple check for unbalanced markdown
        for pattern, marker in self.markdown_pairs:
            # Find all opening markers
            openings = list(pattern.finditer(text))
            # Count raw markers
            raw_count = text.count(marker)
            # Should be even number of markers
            if raw_count % 2 != 0:
                return False
                
        return True
    
    def _calculate_risk_level(self, issues: List[str]) -> str:
        """
        Calculate risk level based on detected issues.
        
        Args:
            issues: List of detected security issues.
            
        Returns:
            Risk level ("low", "medium", "high").
        """
        if not issues:
            return "low"
            
        # High risk for data leakage or format issues
        high_risk_indicators = ["leakage", "format"]
        for issue in issues:
            for indicator in high_risk_indicators:
                if indicator in issue.lower():
                    return "high"
                    
        # Medium risk for PII issues
        medium_risk_indicators = ["pii"]
        for issue in issues:
            for indicator in medium_risk_indicators:
                if indicator in issue.lower():
                    return "medium"
                    
        return "low"
    
    async def log_security_event(
        self, 
        response: str, 
        issues: List[str], 
        user_id: Optional[str], 
        risk_level: str,
        pii_matches: Optional[List[PIIMatch]] = None
    ) -> None:
        """
        Log security event asynchronously.
        
        Args:
            response: The scanned response.
            issues: Detected security issues.
            user_id: The user ID.
            risk_level: The calculated risk level.
            pii_matches: Optional PII matches for detailed logging.
        """
        if not issues:
            return
            
        # Log based on risk level
        log_msg = f"Security event for user {user_id}: {', '.join(issues)}"
        if risk_level == "high":
            self.logger.critical(f"HIGH RISK ALERT: {log_msg}")
        elif risk_level == "medium":
            self.logger.warning(f"MEDIUM RISK ALERT: {log_msg}")
        else:
            self.logger.info(f"LOW RISK ALERT: {log_msg}")
            
        # Log detailed PII information if provided
        if pii_matches:
            pii_details = [f"{m.pii_type}({m.confidence}): {m.match}" for m in pii_matches]
            self.logger.debug(f"PII details: {', '.join(pii_details)}")