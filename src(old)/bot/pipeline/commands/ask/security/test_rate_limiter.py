"""
Unit tests for RateLimiter

Tests all rate limiting scenarios including Redis and fallback modes,
concurrent requests, and different user tiers.
"""

import pytest
import asyncio
import time
from unittest.mock import AsyncMock, MagicMock, patch

from .rate_limiter import RateLimiter, RateLimitConfig, RateLimitResult, RateLimitType


class TestRateLimiter:
    """Test suite for RateLimiter"""
    
    @pytest.fixture
    def config(self):
        """Create test configuration with lower limits"""
        return RateLimitConfig(
            user_requests_per_minute=5,
            user_requests_per_hour=20,
            guild_requests_per_minute=10,
            guild_requests_per_hour=50,
            global_requests_per_second=100,  # High enough to not interfere with tests
            premium_multiplier=2.0,
            admin_multiplier=3.0
        )
    
    @pytest.fixture
    def rate_limiter(self, config):
        """Create rate limiter instance for testing"""
        return RateLimiter(config)
    
    @pytest.mark.asyncio
    async def test_basic_rate_limiting(self, rate_limiter):
        """Test basic rate limiting functionality"""
        user_id = "test_user_123"
        
        # First request should be allowed
        result = await rate_limiter.check_rate_limit(user_id)
        assert result.allowed is True
        assert result.remaining == 4  # 5 - 1
        assert result.limit == 5
        
        # Make more requests up to limit
        for i in range(4):
            result = await rate_limiter.check_rate_limit(user_id)
            assert result.allowed is True
            assert result.remaining == 4 - i - 1
        
        # Next request should be blocked
        result = await rate_limiter.check_rate_limit(user_id)
        assert result.allowed is False
        assert result.remaining == 0
        assert result.retry_after is not None
        assert result.retry_after > 0
    
    @pytest.mark.asyncio
    async def test_different_user_tiers(self, rate_limiter):
        """Test rate limiting with different user tiers"""
        # Standard user - 5 requests per minute
        for i in range(5):
            result = await rate_limiter.check_rate_limit("standard_user", user_tier="standard")
            assert result.allowed is True
        
        result = await rate_limiter.check_rate_limit("standard_user", user_tier="standard")
        assert result.allowed is False
        
        # Premium user - 10 requests per minute (2x multiplier)
        for i in range(10):
            result = await rate_limiter.check_rate_limit("premium_user", user_tier="premium")
            assert result.allowed is True
        
        result = await rate_limiter.check_rate_limit("premium_user", user_tier="premium")
        assert result.allowed is False
        
        # Admin user - 15 requests per minute (3x multiplier)
        for i in range(15):
            result = await rate_limiter.check_rate_limit("admin_user", user_tier="admin")
            assert result.allowed is True
        
        result = await rate_limiter.check_rate_limit("admin_user", user_tier="admin")
        assert result.allowed is False
    
    @pytest.mark.asyncio
    async def test_guild_rate_limiting(self, rate_limiter):
        """Test guild-level rate limiting"""
        guild_id = "test_guild_456"
        
        # Use different users in same guild
        users = [f"user_{i}" for i in range(12)]
        
        # First 10 requests should be allowed (guild limit)
        for i, user_id in enumerate(users[:10]):
            result = await rate_limiter.check_rate_limit(user_id, guild_id=guild_id)
            assert result.allowed is True, f"Request {i+1} should be allowed"
        
        # 11th request should be blocked due to guild limit
        result = await rate_limiter.check_rate_limit(users[10], guild_id=guild_id)
        assert result.allowed is False
        assert result.limit_type == RateLimitType.GUILD_PER_MINUTE
    
    @pytest.mark.asyncio
    async def test_global_rate_limiting(self, rate_limiter):
        """Test global rate limiting"""
        # Make requests from different users rapidly
        users = [f"global_user_{i}" for i in range(5)]
        
        # First 3 requests should be allowed (global limit per second)
        for i, user_id in enumerate(users[:3]):
            result = await rate_limiter.check_rate_limit(user_id)
            assert result.allowed is True, f"Global request {i+1} should be allowed"
        
        # 4th request should be blocked due to global limit
        result = await rate_limiter.check_rate_limit(users[3])
        assert result.allowed is False
        assert result.limit_type == RateLimitType.GLOBAL_PER_SECOND
    
    @pytest.mark.asyncio
    async def test_sliding_window(self, rate_limiter):
        """Test sliding window rate limiting"""
        user_id = "sliding_user"
        
        # Fill up the rate limit
        for i in range(5):
            result = await rate_limiter.check_rate_limit(user_id)
            assert result.allowed is True
        
        # Should be blocked now
        result = await rate_limiter.check_rate_limit(user_id)
        assert result.allowed is False
        
        # Wait a bit and try again (simulate time passing)
        # In real implementation, we'd need to mock time or wait
        # For testing, we'll verify the retry_after is reasonable
        assert result.retry_after <= 60  # Should be within the window
    
    @pytest.mark.asyncio
    async def test_fallback_mode_when_redis_unavailable(self, rate_limiter):
        """Test fallback to in-memory caching when Redis is unavailable"""
        # Mock Redis to be unavailable
        with patch.object(rate_limiter, '_get_redis_client', return_value=None):
            user_id = "fallback_user"
            
            # Should still work with fallback cache
            for i in range(5):
                result = await rate_limiter.check_rate_limit(user_id)
                assert result.allowed is True
                assert result.remaining == 4 - i
            
            # Should be blocked after limit
            result = await rate_limiter.check_rate_limit(user_id)
            assert result.allowed is False
            assert result.remaining == 0
    
    @pytest.mark.asyncio
    async def test_concurrent_requests(self, rate_limiter):
        """Test concurrent requests from same user"""
        user_id = "concurrent_user"
        
        # Make concurrent requests
        tasks = []
        for i in range(10):
            task = asyncio.create_task(rate_limiter.check_rate_limit(user_id))
            tasks.append(task)
        
        results = await asyncio.gather(*tasks)
        
        # Count allowed vs blocked requests
        allowed_count = sum(1 for result in results if result.allowed)
        blocked_count = sum(1 for result in results if not result.allowed)
        
        # Should allow exactly 5 requests (the limit)
        assert allowed_count == 5
        assert blocked_count == 5
    
    @pytest.mark.asyncio
    async def test_get_remaining_quota(self, rate_limiter):
        """Test getting remaining quota information"""
        user_id = "quota_user"
        guild_id = "quota_guild"
        
        # Make a few requests
        for i in range(3):
            await rate_limiter.check_rate_limit(user_id, guild_id=guild_id)
        
        # Check remaining quota
        quotas = await rate_limiter.get_remaining_quota(user_id, guild_id)
        
        assert "user_per_minute" in quotas
        assert "user_per_hour" in quotas
        assert "guild_per_minute" in quotas
        assert "guild_per_hour" in quotas
        
        # Should have 2 remaining for user per minute (5 - 3)
        assert quotas["user_per_minute"] == 2
    
    @pytest.mark.asyncio
    async def test_reset_user_limits(self, rate_limiter):
        """Test resetting user rate limits"""
        user_id = "reset_user"
        admin_id = "admin_123"
        
        # Fill up rate limit
        for i in range(5):
            await rate_limiter.check_rate_limit(user_id)
        
        # Should be blocked
        result = await rate_limiter.check_rate_limit(user_id)
        assert result.allowed is False
        
        # Reset limits
        success = await rate_limiter.reset_user_limits(user_id, admin_id)
        assert success is True
        
        # Should be allowed again
        result = await rate_limiter.check_rate_limit(user_id)
        assert result.allowed is True
    
    @pytest.mark.asyncio
    async def test_error_handling(self, rate_limiter):
        """Test error handling in rate limiter"""
        # Mock an exception in rate limit check
        with patch.object(rate_limiter, '_check_single_limit', side_effect=Exception("Test error")):
            result = await rate_limiter.check_rate_limit("error_user")
            
            # Should fail open (allow request) but log error
            assert result.allowed is True
            assert result.remaining == 999  # Fallback value
    
    @pytest.mark.asyncio
    async def test_cache_cleanup(self, rate_limiter):
        """Test fallback cache cleanup"""
        # Force fallback mode
        with patch.object(rate_limiter, '_get_redis_client', return_value=None):
            # Add some entries to cache
            for i in range(3):
                await rate_limiter.check_rate_limit(f"cleanup_user_{i}")
            
            # Verify cache has entries
            assert len(rate_limiter._fallback_cache) > 0
            
            # Force cleanup
            current_time = time.time()
            await rate_limiter._cleanup_fallback_cache(current_time)
            
            # Cache should still have recent entries
            assert len(rate_limiter._fallback_cache) >= 0
    
    @pytest.mark.asyncio
    async def test_user_multiplier_calculation(self, rate_limiter):
        """Test user tier multiplier calculation"""
        assert rate_limiter._get_user_multiplier("standard") == 1.0
        assert rate_limiter._get_user_multiplier("premium") == 2.0
        assert rate_limiter._get_user_multiplier("admin") == 3.0
        assert rate_limiter._get_user_multiplier("unknown") == 1.0
        assert rate_limiter._get_user_multiplier("ADMIN") == 3.0  # Case insensitive


class TestRateLimitConfig:
    """Test suite for RateLimitConfig"""
    
    def test_default_config(self):
        """Test default configuration values"""
        config = RateLimitConfig()
        
        assert config.user_requests_per_minute == 10
        assert config.user_requests_per_hour == 100
        assert config.guild_requests_per_minute == 50
        assert config.guild_requests_per_hour == 500
        assert config.global_requests_per_second == 20
        assert config.premium_multiplier == 2.0
        assert config.admin_multiplier == 5.0
    
    def test_custom_config(self):
        """Test custom configuration values"""
        config = RateLimitConfig(
            user_requests_per_minute=5,
            premium_multiplier=1.5
        )
        
        assert config.user_requests_per_minute == 5
        assert config.premium_multiplier == 1.5
        # Other values should remain default
        assert config.user_requests_per_hour == 100


class TestRateLimitIntegration:
    """Integration tests for RateLimiter with realistic scenarios"""
    
    @pytest.fixture
    def production_config(self):
        """Production-like configuration"""
        return RateLimitConfig(
            user_requests_per_minute=10,
            user_requests_per_hour=100,
            guild_requests_per_minute=50,
            guild_requests_per_hour=500,
            global_requests_per_second=20
        )
    
    @pytest.fixture
    def production_limiter(self, production_config):
        """Production-like rate limiter"""
        return RateLimiter(production_config)
    
    @pytest.mark.asyncio
    async def test_realistic_trading_bot_usage(self, production_limiter):
        """Test realistic trading bot usage patterns"""
        # Simulate multiple users asking questions
        users = [f"trader_{i}" for i in range(20)]
        guild_id = "trading_server"
        
        # Each user makes a few requests
        results = []
        for user_id in users:
            for request_num in range(3):
                result = await production_limiter.check_rate_limit(
                    user_id, 
                    guild_id=guild_id,
                    user_tier="standard"
                )
                results.append((user_id, request_num, result.allowed))
        
        # All individual user requests should be allowed (3 < 10 per minute)
        user_requests = [(user, req, allowed) for user, req, allowed in results]
        for user, req, allowed in user_requests:
            assert allowed is True, f"User {user} request {req} should be allowed"
    
    @pytest.mark.asyncio
    async def test_spam_protection(self, production_limiter):
        """Test protection against spam attacks"""
        spammer_id = "spammer_123"
        
        # Spammer tries to make many requests quickly
        allowed_count = 0
        blocked_count = 0
        
        for i in range(50):
            result = await production_limiter.check_rate_limit(spammer_id)
            if result.allowed:
                allowed_count += 1
            else:
                blocked_count += 1
                # Once blocked, stop trying (realistic behavior)
                break
        
        # Should allow exactly 10 requests per minute, then block
        assert allowed_count == 10
        assert blocked_count >= 1  # At least one blocked request
    
    @pytest.mark.asyncio
    async def test_premium_user_benefits(self, production_limiter):
        """Test that premium users get higher limits"""
        standard_user = "standard_trader"
        premium_user = "premium_trader"
        
        # Standard user - should be blocked after 10 requests
        for i in range(10):
            result = await production_limiter.check_rate_limit(standard_user, user_tier="standard")
            assert result.allowed is True
        
        result = await production_limiter.check_rate_limit(standard_user, user_tier="standard")
        assert result.allowed is False
        
        # Premium user - should be allowed 20 requests (2x multiplier)
        for i in range(20):
            result = await production_limiter.check_rate_limit(premium_user, user_tier="premium")
            assert result.allowed is True
        
        result = await production_limiter.check_rate_limit(premium_user, user_tier="premium")
        assert result.allowed is False
    
    @pytest.mark.asyncio
    async def test_server_protection(self, production_limiter):
        """Test protection against server-wide abuse"""
        guild_id = "busy_server"
        
        # Many users in same server make requests
        allowed_requests = 0
        for user_num in range(100):
            user_id = f"server_user_{user_num}"
            result = await production_limiter.check_rate_limit(user_id, guild_id=guild_id)
            if result.allowed:
                allowed_requests += 1
            else:
                # Should be blocked due to guild limit
                assert result.limit_type == RateLimitType.GUILD_PER_MINUTE
                break
        
        # Should allow exactly 50 requests per minute for the guild
        assert allowed_requests == 50


if __name__ == "__main__":
    pytest.main([__file__])