"""
Simplified Security Module for ASK Pipeline

Combines essential security functionality into a single, focused module.
Replaces the 4 separate security modules with streamlined security for a Discord bot.
"""

import time
import re
from typing import Dict, Any, Optional, List
from dataclasses import dataclass

from src.shared.error_handling.logging import get_logger

logger = get_logger(__name__)

@dataclass
class SecurityResult:
    """Result of security validation"""
    is_valid: bool
    risk_level: str = "low"  # low, medium, high
    violations: List[str] = None
    sanitized_input: str = ""
    user_id: str = ""
    rate_limit_ok: bool = True

class SimplifiedSecurity:
    """
    Simplified security module for Discord bot
    
    Combines:
    - Input validation
    - Rate limiting
    - Basic authentication
    - Input sanitization
    
    Removes enterprise-level features not needed for Discord bot
    """

    def __init__(self):
        """Initialize simplified security"""
        self.rate_limits = {}  # Simple in-memory rate limiting
        self.max_requests_per_minute = 10
        self.max_requests_per_hour = 100
        self.max_query_length = 2000

    async def validate_request(
        self, 
        query: str, 
        user_id: str = "", 
        correlation_id: str = ""
    ) -> SecurityResult:
        """
        Validate a request with simplified security checks
        
        Args:
            query: User query
            user_id: Discord user ID
            correlation_id: Request correlation ID
            
        Returns:
            SecurityResult with validation status
        """
        try:
            violations = []
            
            # 1. Input validation
            if not self._validate_input(query):
                violations.append("Invalid input format")
            
            # 2. Length check
            if len(query) > self.max_query_length:
                violations.append(f"Query too long (max {self.max_query_length} chars)")
            
            # 3. Rate limiting
            if not self._check_rate_limit(user_id):
                violations.append("Rate limit exceeded")
            
            # 4. Basic sanitization
            sanitized_input = self._sanitize_input(query)
            
            # Determine risk level
            risk_level = self._assess_risk_level(violations, query)
            
            is_valid = len(violations) == 0
            
            result = SecurityResult(
                is_valid=is_valid,
                risk_level=risk_level,
                violations=violations,
                sanitized_input=sanitized_input,
                user_id=user_id,
                rate_limit_ok=len(violations) == 0
            )
            
            logger.info(f"Security validation completed", extra={
                'correlation_id': correlation_id,
                'user_id': user_id,
                'is_valid': is_valid,
                'risk_level': risk_level,
                'violations_count': len(violations)
            })
            
            return result
            
        except Exception as e:
            logger.error(f"Security validation failed: {e}", extra={'correlation_id': correlation_id})
            return SecurityResult(
                is_valid=False,
                risk_level="high",
                violations=[f"Security validation error: {str(e)}"],
                user_id=user_id
            )

    def _validate_input(self, query: str) -> bool:
        """Basic input validation"""
        try:
            if not query or not isinstance(query, str):
                return False
            
            # Check for basic malicious patterns
            malicious_patterns = [
                r'<script.*?>.*?</script>',  # Script tags
                r'javascript:',  # JavaScript protocol
                r'data:text/html',  # Data URLs
                r'vbscript:',  # VBScript
                r'on\w+\s*=',  # Event handlers
            ]
            
            for pattern in malicious_patterns:
                if re.search(pattern, query, re.IGNORECASE):
                    return False
            
            return True
            
        except Exception as e:
            logger.warning(f"Input validation error: {e}")
            return False

    def _check_rate_limit(self, user_id: str) -> bool:
        """Simple rate limiting check"""
        try:
            if not user_id:
                return True  # No rate limiting for anonymous users
            
            current_time = time.time()
            user_limits = self.rate_limits.get(user_id, {
                'requests': [],
                'hourly_requests': []
            })
            
            # Clean old requests (older than 1 hour)
            user_limits['requests'] = [
                req_time for req_time in user_limits['requests'] 
                if current_time - req_time < 3600
            ]
            user_limits['hourly_requests'] = [
                req_time for req_time in user_limits['hourly_requests'] 
                if current_time - req_time < 3600
            ]
            
            # Check minute limit
            minute_requests = [
                req_time for req_time in user_limits['requests'] 
                if current_time - req_time < 60
            ]
            
            if len(minute_requests) >= self.max_requests_per_minute:
                return False
            
            # Check hour limit
            if len(user_limits['hourly_requests']) >= self.max_requests_per_hour:
                return False
            
            # Add current request
            user_limits['requests'].append(current_time)
            user_limits['hourly_requests'].append(current_time)
            self.rate_limits[user_id] = user_limits
            
            return True
            
        except Exception as e:
            logger.warning(f"Rate limit check error: {e}")
            return True  # Allow on error

    def _sanitize_input(self, query: str) -> str:
        """Basic input sanitization"""
        try:
            # Remove or escape potentially dangerous characters
            sanitized = query
            
            # Remove null bytes
            sanitized = sanitized.replace('\x00', '')
            
            # Limit length
            sanitized = sanitized[:self.max_query_length]
            
            # Basic HTML escaping (simple version)
            sanitized = sanitized.replace('<', '&lt;').replace('>', '&gt;')
            
            return sanitized.strip()
            
        except Exception as e:
            logger.warning(f"Input sanitization error: {e}")
            return query[:self.max_query_length] if query else ""

    def _assess_risk_level(self, violations: List[str], query: str) -> str:
        """Assess risk level based on violations and query content"""
        try:
            if not violations:
                return "low"
            
            # High risk indicators
            high_risk_keywords = ['admin', 'delete', 'drop', 'exec', 'system']
            if any(keyword in query.lower() for keyword in high_risk_keywords):
                return "high"
            
            # Medium risk for multiple violations
            if len(violations) > 2:
                return "medium"
            
            # Low risk for single violation
            return "low"
            
        except Exception as e:
            logger.warning(f"Risk assessment error: {e}")
            return "medium"

    def get_user_stats(self, user_id: str) -> Dict[str, Any]:
        """Get user statistics for monitoring"""
        try:
            if not user_id or user_id not in self.rate_limits:
                return {"requests_last_hour": 0, "requests_last_minute": 0}
            
            current_time = time.time()
            user_limits = self.rate_limits[user_id]
            
            minute_requests = [
                req_time for req_time in user_limits['requests'] 
                if current_time - req_time < 60
            ]
            
            hourly_requests = [
                req_time for req_time in user_limits['hourly_requests'] 
                if current_time - req_time < 3600
            ]
            
            return {
                "requests_last_minute": len(minute_requests),
                "requests_last_hour": len(hourly_requests),
                "rate_limit_remaining_minute": max(0, self.max_requests_per_minute - len(minute_requests)),
                "rate_limit_remaining_hour": max(0, self.max_requests_per_hour - len(hourly_requests))
            }
            
        except Exception as e:
            logger.warning(f"Error getting user stats: {e}")
            return {"requests_last_hour": 0, "requests_last_minute": 0}

    def reset_user_rate_limit(self, user_id: str) -> bool:
        """Reset rate limit for a specific user (admin function)"""
        try:
            if user_id in self.rate_limits:
                del self.rate_limits[user_id]
                logger.info(f"Rate limit reset for user {user_id}")
                return True
            return False
            
        except Exception as e:
            logger.error(f"Error resetting rate limit for user {user_id}: {e}")
            return False

    def get_security_summary(self) -> Dict[str, Any]:
        """Get overall security summary"""
        try:
            total_users = len(self.rate_limits)
            active_users = sum(
                1 for user_limits in self.rate_limits.values()
                if any(current_time - req_time < 3600 for req_time in user_limits['requests'])
            )
            
            return {
                "total_users": total_users,
                "active_users": active_users,
                "rate_limit_config": {
                    "max_per_minute": self.max_requests_per_minute,
                    "max_per_hour": self.max_requests_per_hour,
                    "max_query_length": self.max_query_length
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting security summary: {e}")
            return {"total_users": 0, "active_users": 0}
