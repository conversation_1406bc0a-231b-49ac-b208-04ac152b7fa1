"""
Unit tests for AuthManager

Tests all authentication and authorization scenarios including RBAC,
session management, blacklists, and channel restrictions.
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
import discord
from discord.ext import commands

from .auth_manager import Auth<PERSON><PERSON><PERSON>, AuthResult


class TestAuthManager:
    """Test suite for AuthManager"""
    
    @pytest.fixture
    def bot(self):
        """Create mock bot instance for testing"""
        return MagicMock()
    
    @pytest.fixture
    def auth_manager(self, bot):
        """Create auth manager instance for testing"""
        return AuthManager(bot)
    
    def test_init(self, auth_manager):
        """Test initialization of AuthManager"""
        assert auth_manager.global_blacklist == set()
        assert auth_manager.guild_blacklists == {}
        assert auth_manager.allowed_channels == {}
        assert "admin" in auth_manager.role_permissions
        assert "premium" in auth_manager.role_permissions
        assert "@everyone" in auth_manager.role_permissions
        assert auth_manager.sessions == {}
    
    @pytest.mark.asyncio
    async def test_validate_permissions_authorized_basic(self, auth_manager):
        """Test basic authorization for valid user"""
        result = await auth_manager.validate_permissions("123456789", "ask")
        
        assert isinstance(result, AuthResult)
        assert result.is_authorized is True
        assert result.reason == "Authorized (DM or no guild check)"
        assert result.user_tier == "basic"
        assert result.permissions == {"ask": True}
    
    @pytest.mark.asyncio
    async def test_validate_permissions_global_blacklist(self, auth_manager):
        """Test authorization failure for globally blacklisted user"""
        # Add user to global blacklist
        auth_manager.add_to_global_blacklist("123456789")
        
        result = await auth_manager.validate_permissions("123456789", "ask")
        
        assert result.is_authorized is False
        assert result.reason == "User is globally blacklisted."
    
    @pytest.mark.asyncio
    async def test_validate_permissions_guild_blacklist(self, auth_manager, bot):
        """Test authorization failure for guild blacklisted user"""
        guild_id = "987654321"
        user_id = "123456789"
        
        # Add user to guild blacklist
        auth_manager.add_to_guild_blacklist(user_id, guild_id)
        
        # Mock guild and member
        mock_guild = MagicMock()
        mock_member = MagicMock()
        mock_guild.get_member.return_value = mock_member
        bot.get_guild.return_value = mock_guild
        
        result = await auth_manager.validate_permissions(user_id, "ask", guild_id=guild_id)
        
        assert result.is_authorized is False
        assert result.reason == "User is blacklisted in this guild."
    
    @pytest.mark.asyncio
    async def test_validate_permissions_channel_restriction(self, auth_manager, bot):
        """Test authorization failure for channel restriction"""
        guild_id = "987654321"
        channel_id = "111222333"
        user_id = "123456789"
        
        # Add allowed channel (but not the one we're testing)
        auth_manager.add_allowed_channel("444555666", guild_id)
        
        # Mock guild and member with basic role
        mock_role = MagicMock()
        mock_role.name = "@everyone"
        mock_member = MagicMock()
        mock_member.roles = [mock_role]
        
        mock_guild = MagicMock()
        mock_guild.get_member.return_value = mock_member
        bot.get_guild.return_value = mock_guild
        
        result = await auth_manager.validate_permissions(
            user_id, "ask", guild_id=guild_id, channel_id=channel_id
        )
        
        assert result.is_authorized is False
        assert result.reason == "Command not allowed in this channel."
    
    @pytest.mark.asyncio
    async def test_validate_permissions_rbac_admin(self, auth_manager, bot):
        """Test RBAC authorization for admin user"""
        guild_id = "987654321"
        user_id = "123456789"
        
        # Mock guild and member with admin role
        mock_role = MagicMock()
        mock_role.name = "admin"
        mock_member = MagicMock()
        mock_member.roles = [mock_role]
        mock_member.id = int(user_id)
        
        mock_guild = MagicMock()
        mock_guild.get_member.return_value = mock_member
        mock_guild.id = int(guild_id)
        bot.get_guild.return_value = mock_guild
        
        result = await auth_manager.validate_permissions(user_id, "admin_tools", guild_id=guild_id)
        
        assert result.is_authorized is True
        assert result.reason == "Authorized via RBAC, channel, and session check"
        assert result.user_tier == "admin"
        assert result.permissions["admin_tools"] is True
    
    @pytest.mark.asyncio
    async def test_validate_permissions_rbac_premium(self, auth_manager, bot):
        """Test RBAC authorization for premium user"""
        guild_id = "987654321"
        user_id = "123456789"
        
        # Mock guild and member with premium role
        mock_role = MagicMock()
        mock_role.name = "premium"
        mock_member = MagicMock()
        mock_member.roles = [mock_role]
        mock_member.id = int(user_id)
        
        mock_guild = MagicMock()
        mock_guild.get_member.return_value = mock_member
        mock_guild.id = int(guild_id)
        bot.get_guild.return_value = mock_guild
        
        result = await auth_manager.validate_permissions(user_id, "advanced_ask", guild_id=guild_id)
        
        assert result.is_authorized is True
        assert result.user_tier == "premium"
        assert result.permissions["advanced_ask"] is True
    
    @pytest.mark.asyncio
    async def test_validate_permissions_rbac_insufficient_permissions(self, auth_manager, bot):
        """Test RBAC authorization failure for insufficient permissions"""
        guild_id = "987654321"
        user_id = "123456789"
        
        # Mock guild and member with basic role
        mock_role = MagicMock()
        mock_role.name = "@everyone"
        mock_member = MagicMock()
        mock_member.roles = [mock_role]
        mock_member.id = int(user_id)
        
        mock_guild = MagicMock()
        mock_guild.get_member.return_value = mock_member
        mock_guild.id = int(guild_id)
        bot.get_guild.return_value = mock_guild
        
        result = await auth_manager.validate_permissions(user_id, "admin_tools", guild_id=guild_id)
        
        assert result.is_authorized is False
        assert "Insufficient role permissions" in result.reason
    
    @pytest.mark.asyncio
    async def test_validate_permissions_with_valid_session(self, auth_manager):
        """Test authorization with valid session token"""
        user_id = "123456789"
        guild_id = "987654321"
        
        # Generate a session token
        token = auth_manager.generate_session_token(user_id, guild_id)
        
        # Mock guild and member
        mock_role = MagicMock()
        mock_role.name = "@everyone"
        mock_member = MagicMock()
        mock_member.roles = [mock_role]
        mock_member.id = int(user_id)
        
        mock_guild = MagicMock()
        mock_guild.get_member.return_value = mock_member
        mock_guild.id = int(guild_id)
        auth_manager.bot.get_guild.return_value = mock_guild
        
        result = await auth_manager.validate_permissions(
            user_id, "ask", guild_id=guild_id, session_token=token
        )
        
        assert result.is_authorized is True
        assert result.reason == "Authorized via RBAC, channel, and session check"
    
    @pytest.mark.asyncio
    async def test_validate_permissions_with_invalid_session(self, auth_manager):
        """Test authorization failure with invalid session token"""
        user_id = "123456789"
        
        result = await auth_manager.validate_permissions(
            user_id, "ask", session_token="invalid_token"
        )
        
        assert result.is_authorized is False
        assert result.reason == "Invalid or expired session token."
    
    def test_get_user_permissions_from_roles_admin(self, auth_manager):
        """Test getting permissions for admin role"""
        mock_role = MagicMock()
        mock_role.name = "admin"
        roles = [mock_role]
        
        permissions = auth_manager._get_user_permissions_from_roles(roles)
        
        assert permissions["ask"] is True
        assert permissions["advanced_ask"] is True
        assert permissions["admin_tools"] is True
    
    def test_get_user_permissions_from_roles_premium(self, auth_manager):
        """Test getting permissions for premium role"""
        mock_role = MagicMock()
        mock_role.name = "premium"
        roles = [mock_role]
        
        permissions = auth_manager._get_user_permissions_from_roles(roles)
        
        assert permissions["ask"] is True
        assert permissions["advanced_ask"] is True
        assert "admin_tools" not in permissions
    
    def test_get_user_permissions_from_roles_basic(self, auth_manager):
        """Test getting permissions for basic (@everyone) role"""
        mock_role = MagicMock()
        mock_role.name = "@everyone"
        roles = [mock_role]
        
        permissions = auth_manager._get_user_permissions_from_roles(roles)
        
        assert permissions["ask"] is True
        assert "advanced_ask" not in permissions
        assert "admin_tools" not in permissions
    
    def test_get_user_tier_from_roles(self, auth_manager):
        """Test getting user tier from roles"""
        # Admin role
        mock_role = MagicMock()
        mock_role.name = "Admin"  # Test case insensitivity
        roles = [mock_role]
        tier = auth_manager._get_user_tier_from_roles(roles)
        assert tier == "admin"
        
        # Premium role
        mock_role.name = "premium"
        tier = auth_manager._get_user_tier_from_roles(roles)
        assert tier == "premium"
        
        # Basic role
        mock_role.name = "member"
        tier = auth_manager._get_user_tier_from_roles(roles)
        assert tier == "basic"
    
    @pytest.mark.asyncio
    async def test_get_user_tier(self, auth_manager, bot):
        """Test getting user tier"""
        guild_id = "987654321"
        user_id = "123456789"
        
        # Mock guild and member with premium role
        mock_role = MagicMock()
        mock_role.name = "premium"
        mock_member = MagicMock()
        mock_member.roles = [mock_role]
        mock_member.id = int(user_id)
        
        mock_guild = MagicMock()
        mock_guild.get_member.return_value = mock_member
        mock_guild.id = int(guild_id)
        bot.get_guild.return_value = mock_guild
        
        tier = await auth_manager.get_user_tier(user_id, guild_id)
        assert tier == "premium"
        
        # Test fallback to basic when no guild
        tier = await auth_manager.get_user_tier(user_id)
        assert tier == "basic"
    
    @pytest.mark.asyncio
    async def test_check_feature_access(self, auth_manager):
        """Test checking feature access"""
        # Test basic access
        has_access = await auth_manager.check_feature_access("123456789", "ask")
        assert has_access is True
        
        # Test access denied - should be True because of fallback permissions
        has_access = await auth_manager.check_feature_access("123456789", "admin_tools")
        # For basic users, this will be True because of the fallback permissions in validate_permissions
        assert has_access is True
    
    def test_generate_session_token(self, auth_manager):
        """Test session token generation"""
        user_id = "123456789"
        guild_id = "987654321"
        
        token = auth_manager.generate_session_token(user_id, guild_id)
        
        assert isinstance(token, str)
        assert len(token) > 0
        assert user_id in auth_manager.sessions
        assert auth_manager.sessions[user_id][0] == token
        assert auth_manager.sessions[user_id][2] == guild_id
    
    @pytest.mark.asyncio
    async def test_validate_session_token_valid(self, auth_manager):
        """Test valid session token validation"""
        user_id = "123456789"
        guild_id = "987654321"
        
        token = auth_manager.generate_session_token(user_id, guild_id)
        is_valid = await auth_manager.validate_session_token(user_id, token, guild_id)
        
        assert is_valid is True
    
    @pytest.mark.asyncio
    async def test_validate_session_token_invalid(self, auth_manager):
        """Test invalid session token validation"""
        user_id = "123456789"
        is_valid = await auth_manager.validate_session_token(user_id, "invalid_token")
        
        assert is_valid is False
    
    @pytest.mark.asyncio
    async def test_validate_session_token_expired(self, auth_manager):
        """Test expired session token validation"""
        user_id = "123456789"
        guild_id = "987654321"
        
        # Generate token and manually expire it
        token = auth_manager.generate_session_token(user_id, guild_id)
        auth_manager.sessions[user_id] = (token, 0, guild_id)  # Set expiration to 0
        
        is_valid = await auth_manager.validate_session_token(user_id, token, guild_id)
        
        assert is_valid is False
        assert user_id not in auth_manager.sessions  # Should be cleaned up
    
    @pytest.mark.asyncio
    async def test_invalidate_session(self, auth_manager):
        """Test session invalidation"""
        user_id = "123456789"
        guild_id = "987654321"
        
        token = auth_manager.generate_session_token(user_id, guild_id)
        assert user_id in auth_manager.sessions
        
        await auth_manager.invalidate_session(user_id)
        assert user_id not in auth_manager.sessions
    
    def test_add_to_global_blacklist(self, auth_manager):
        """Test adding user to global blacklist"""
        user_id = "123456789"
        auth_manager.add_to_global_blacklist(user_id)
        assert user_id in auth_manager.global_blacklist
    
    def test_remove_from_global_blacklist(self, auth_manager):
        """Test removing user from global blacklist"""
        user_id = "123456789"
        auth_manager.add_to_global_blacklist(user_id)
        assert user_id in auth_manager.global_blacklist
        
        auth_manager.remove_from_global_blacklist(user_id)
        assert user_id not in auth_manager.global_blacklist
    
    def test_add_to_guild_blacklist(self, auth_manager):
        """Test adding user to guild blacklist"""
        user_id = "123456789"
        guild_id = "987654321"
        
        auth_manager.add_to_guild_blacklist(user_id, guild_id)
        assert guild_id in auth_manager.guild_blacklists
        assert user_id in auth_manager.guild_blacklists[guild_id]
    
    def test_remove_from_guild_blacklist(self, auth_manager):
        """Test removing user from guild blacklist"""
        user_id = "123456789"
        guild_id = "987654321"
        
        auth_manager.add_to_guild_blacklist(user_id, guild_id)
        assert user_id in auth_manager.guild_blacklists[guild_id]
        
        auth_manager.remove_from_guild_blacklist(user_id, guild_id)
        assert user_id not in auth_manager.guild_blacklists[guild_id]
    
    def test_add_allowed_channel(self, auth_manager):
        """Test adding allowed channel"""
        channel_id = "111222333"
        guild_id = "987654321"
        
        auth_manager.add_allowed_channel(channel_id, guild_id)
        assert guild_id in auth_manager.allowed_channels
        assert channel_id in auth_manager.allowed_channels[guild_id]
    
    def test_remove_allowed_channel(self, auth_manager):
        """Test removing allowed channel"""
        channel_id = "111222333"
        guild_id = "987654321"
        
        auth_manager.add_allowed_channel(channel_id, guild_id)
        assert channel_id in auth_manager.allowed_channels[guild_id]
        
        auth_manager.remove_allowed_channel(channel_id, guild_id)
        assert channel_id not in auth_manager.allowed_channels[guild_id]
    
    def test_load_role_permissions(self, auth_manager):
        """Test loading role permissions from config"""
        config = {
            "custom_role": {"custom_feature": True},
            "admin": {"new_admin_feature": True}
        }
        
        auth_manager.load_role_permissions(config)
        assert auth_manager.role_permissions == config


if __name__ == "__main__":
    pytest.main([__file__])