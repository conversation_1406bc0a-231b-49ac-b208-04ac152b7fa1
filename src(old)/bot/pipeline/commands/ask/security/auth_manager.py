"""Authentication and Authorization Manager for the ASK command."""

from dataclasses import dataclass
from typing import Optional, Dict, Any, List, Set
import discord
from discord.ext import commands
import time
import secrets
import base64

@dataclass
class AuthResult:
    """Result of an authentication/authorization check."""
    is_authorized: bool
    reason: str
    user_tier: Optional[str] = None
    permissions: Optional[Dict[str, bool]] = None

class AuthManager:
    """
    Manages authentication and authorization for the ASK command.
    
    Handles user permission validation, role-based access control,
    channel restrictions, and session management.
    """
    
    def __init__(self, bot: commands.Bot):
        self.bot = bot
        # Blacklist: global and guild-specific
        self.global_blacklist: set[str] = set()
        self.guild_blacklists: Dict[str, Set[str]] = {}  # guild_id -> set of user_ids
        # Allowed channels per guild
        self.allowed_channels: Dict[str, Set[str]] = {}  # guild_id -> set of channel_ids
        # RBAC: Map role names to permissions
        self.role_permissions: Dict[str, Dict[str, bool]] = {
            "admin": {"ask": True, "advanced_ask": True, "admin_tools": True},
            "premium": {"ask": True, "advanced_ask": True},
            "@everyone": {"ask": True},
            # Can be loaded from config
        }
        # Session management: user_id -> (token, expiration, guild_id)
        self.sessions: Dict[str, tuple[str, float, Optional[str]]] = {}
        self.SESSION_EXPIRATION = 3600  # 1 hour in seconds
    
    async def validate_permissions(
        self,
        user_id: str,
        command: str,
        guild_id: Optional[str] = None,
        channel_id: Optional[str] = None,
        session_token: Optional[str] = None
    ) -> AuthResult:
        """
        Validate if a user has permission to execute the command.
        
        Args:
            user_id: The Discord user ID.
            command: The command being executed (e.g., 'ask').
            guild_id: The guild ID (for server-specific checks).
            channel_id: The channel ID (for channel restrictions).
            session_token: Optional session token for validation.
        
        Returns:
            AuthResult indicating authorization status.
        """
        # Session validation if token provided
        if session_token:
            if not await self.validate_session_token(user_id, session_token, guild_id):
                return AuthResult(is_authorized=False, reason="Invalid or expired session token.")
        
        # Global blacklist check
        if user_id in self.global_blacklist:
            return AuthResult(is_authorized=False, reason="User is globally blacklisted.")
        
        # Guild-specific blacklist check
        if guild_id and user_id in self.guild_blacklists.get(guild_id, set()):
            return AuthResult(is_authorized=False, reason="User is blacklisted in this guild.")
        
        # Channel restrictions check
        if guild_id and channel_id:
            allowed = self.allowed_channels.get(guild_id, set())
            if allowed and channel_id not in allowed:
                return AuthResult(
                    is_authorized=False,
                    reason="Command not allowed in this channel."
                )
        
        # RBAC: Fetch member and check roles
        if guild_id:
            guild = self.bot.get_guild(int(guild_id))
            if guild:
                member = guild.get_member(int(user_id))
                if member:
                    user_permissions = self._get_user_permissions_from_roles(member.roles)
                    if not user_permissions.get(command, False):
                        return AuthResult(
                            is_authorized=False,
                            reason=f"Insufficient role permissions for '{command}'."
                        )
                    user_tier = self._get_user_tier_from_roles(member.roles)
                    return AuthResult(
                        is_authorized=True,
                        reason="Authorized via RBAC, channel, and session check",
                        user_tier=user_tier,
                        permissions=user_permissions
                    )
        
        # Fallback for DMs or no guild: basic access
        return AuthResult(
            is_authorized=True,
            reason="Authorized (DM or no guild check)",
            user_tier="basic",
            permissions={"ask": True}
        )
    
    def _get_user_permissions_from_roles(self, roles: List[discord.Role]) -> Dict[str, bool]:
        """Get permissions based on user's roles (highest priority wins)."""
        role_names = [role.name for role in roles]
        
        # Check from highest to lowest priority (admin > premium > everyone)
        if "admin" in role_names:
            return self.role_permissions.get("admin", {})
        elif "premium" in role_names:
            return self.role_permissions.get("premium", {})
        else:
            return self.role_permissions.get("@everyone", {})
    
    def _get_user_tier_from_roles(self, roles: List[discord.Role]) -> str:
        """Determine user tier from roles."""
        role_names = [role.name.lower() for role in roles]
        if "admin" in role_names:
            return "admin"
        elif "premium" in role_names:
            return "premium"
        return "basic"
    
    async def get_user_tier(self, user_id: str, guild_id: Optional[str] = None) -> str:
        """
        Get the user's tier (e.g., basic, premium, admin).
        
        Args:
            user_id: The Discord user ID.
            guild_id: The guild ID.
        
        Returns:
            The user tier.
        """
        if guild_id:
            guild = self.bot.get_guild(int(guild_id))
            if guild:
                member = guild.get_member(int(user_id))
                if member:
                    return self._get_user_tier_from_roles(member.roles)
        return "basic"
    
    async def check_feature_access(self, user_id: str, feature: str, guild_id: Optional[str] = None, session_token: Optional[str] = None) -> bool:
        """
        Check if user has access to a specific feature.
        
        Args:
            user_id: The Discord user ID.
            feature: The feature name.
            guild_id: The guild ID.
            session_token: Optional session token.
        
        Returns:
            True if access granted, False otherwise.
        """
        # Use validate_permissions logic (channel_id not needed for feature check)
        result = await self.validate_permissions(user_id, feature, guild_id, session_token=session_token)
        return result.is_authorized
    
    # Session management methods
    def generate_session_token(self, user_id: str, guild_id: Optional[str] = None) -> str:
        """
        Generate a secure session token for the user.
        
        Args:
            user_id: The Discord user ID.
            guild_id: The guild ID (optional).
        
        Returns:
            The generated session token.
        """
        token_bytes = secrets.token_bytes(32)
        token = base64.urlsafe_b64encode(token_bytes).decode('utf-8').rstrip('=')
        expiration = time.time() + self.SESSION_EXPIRATION
        self.sessions[user_id] = (token, expiration, guild_id)
        return token
    
    async def validate_session_token(self, user_id: str, token: str, guild_id: Optional[str] = None) -> bool:
        """
        Validate a session token for the user.
        
        Args:
            user_id: The Discord user ID.
            token: The session token.
            guild_id: The guild ID (optional, for guild-specific sessions).
        
        Returns:
            True if valid, False otherwise.
        """
        if user_id not in self.sessions:
            return False
        
        stored_token, expiration, stored_guild_id = self.sessions[user_id]
        if time.time() > expiration:
            # Expired, remove
            del self.sessions[user_id]
            return False
        
        if token != stored_token:
            return False
        
        if guild_id and stored_guild_id and guild_id != stored_guild_id:
            return False
        
        # Renew expiration on validation
        new_expiration = time.time() + self.SESSION_EXPIRATION
        self.sessions[user_id] = (token, new_expiration, stored_guild_id)
        return True
    
    async def invalidate_session(self, user_id: str) -> None:
        """
        Invalidate the user's session.
        
        Args:
            user_id: The Discord user ID.
        """
        self.sessions.pop(user_id, None)
    
    # Blacklist methods
    def add_to_global_blacklist(self, user_id: str) -> None:
        """Add a user to the global blacklist."""
        self.global_blacklist.add(user_id)
    
    def remove_from_global_blacklist(self, user_id: str) -> None:
        """Remove a user from the global blacklist."""
        self.global_blacklist.discard(user_id)
    
    def add_to_guild_blacklist(self, user_id: str, guild_id: str) -> None:
        """Add a user to a guild-specific blacklist."""
        if guild_id not in self.guild_blacklists:
            self.guild_blacklists[guild_id] = set()
        self.guild_blacklists[guild_id].add(user_id)
    
    def remove_from_guild_blacklist(self, user_id: str, guild_id: str) -> None:
        """Remove a user from a guild-specific blacklist."""
        if guild_id in self.guild_blacklists:
            self.guild_blacklists[guild_id].discard(user_id)
    
    # Channel restriction methods
    def add_allowed_channel(self, channel_id: str, guild_id: str) -> None:
        """Add a channel to allowed list for a guild."""
        if guild_id not in self.allowed_channels:
            self.allowed_channels[guild_id] = set()
        self.allowed_channels[guild_id].add(channel_id)
    
    def remove_allowed_channel(self, channel_id: str, guild_id: str) -> None:
        """Remove a channel from allowed list for a guild."""
        if guild_id in self.allowed_channels:
            self.allowed_channels[guild_id].discard(channel_id)
    
    def load_role_permissions(self, config: Dict[str, Any]) -> None:
        """
        Load role permissions from configuration.
        
        Args:
            config: Dict with role names as keys and permission dicts as values.
        """
        self.role_permissions = config