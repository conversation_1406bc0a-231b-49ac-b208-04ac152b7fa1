"""
AI Synthesis Component for Tool Orchestrator

Handles AI reasoning, fact verification, and response synthesis
"""

import time
from typing import List, Dict, Any, Optional

from src.shared.error_handling.logging import get_logger
from .intent_detector import IntentResult

logger = get_logger(__name__)

class AISynthesizer:
    """
    Handles AI synthesis and reasoning for financial analysis
    
    Responsibilities:
    - AI reasoning and synthesis
    - Fact verification
    - Insight extraction
    - Response generation
    """

    def __init__(self):
        """Initialize AI synthesizer"""
        pass

    async def perform_ai_synthesis(self, query: str, analysis_data: Dict[str, Any]) -> Dict[str, Any]:
        """Perform AI synthesis of all analysis data"""
        try:
            synthesis_result = {
                "query": query,
                "synthesis": "",
                "key_insights": [],
                "confidence": 0.0,
                "reasoning": "",
                "execution_time": 0.0
            }
            
            start_time = time.time()
            
            # Extract key information from analysis data
            market_data = analysis_data.get('market_data', {})
            technical_analysis = analysis_data.get('technical_analysis', {})
            ml_predictions = analysis_data.get('ml_predictions', {})
            mcp_insights = analysis_data.get('mcp_insights', {})
            
            # Generate synthesis
            synthesis = await self._generate_synthesis(
                query, market_data, technical_analysis, ml_predictions, mcp_insights
            )
            
            synthesis_result["synthesis"] = synthesis
            synthesis_result["key_insights"] = self._extract_insights(synthesis)
            synthesis_result["confidence"] = self._calculate_synthesis_confidence(analysis_data)
            synthesis_result["reasoning"] = self._generate_reasoning(analysis_data)
            synthesis_result["execution_time"] = time.time() - start_time
            
            return synthesis_result
            
        except Exception as e:
            logger.error(f"Error in AI synthesis: {e}")
            return {"query": query, "synthesis": "", "error": str(e)}

    async def _generate_synthesis(
        self, 
        query: str, 
        market_data: Dict[str, Any], 
        technical_analysis: Dict[str, Any], 
        ml_predictions: Dict[str, Any], 
        mcp_insights: Dict[str, Any]
    ) -> str:
        """Generate AI synthesis of all analysis components"""
        try:
            # Build synthesis from available data
            synthesis_parts = []
            
            # Market data summary
            if market_data.get('data'):
                symbols = list(market_data['data'].keys())
                synthesis_parts.append(f"Analysis of {', '.join(symbols)}:")
                
                for symbol, data in market_data['data'].items():
                    price = data.get('price', 0)
                    change = data.get('change_percent', 0)
                    synthesis_parts.append(f"- {symbol}: ${price:.2f} ({change:+.2f}%)")
            
            # Technical analysis summary
            if technical_analysis.get('indicators'):
                trend = technical_analysis.get('trend_analysis', {})
                momentum = technical_analysis.get('momentum_analysis', {})
                
                if trend.get('trend_direction') != 'unknown':
                    synthesis_parts.append(f"Technical Analysis: {trend['trend_direction']} trend with {trend.get('trend_strength', 0):.1%} strength")
                
                if momentum.get('overbought_oversold') != 'neutral':
                    synthesis_parts.append(f"Momentum: {momentum['overbought_oversold']} conditions")
            
            # ML predictions summary
            if ml_predictions.get('price_prediction'):
                pred = ml_predictions['price_prediction']
                direction = pred.get('direction', 'neutral')
                confidence = pred.get('confidence', 0)
                synthesis_parts.append(f"ML Prediction: {direction} direction with {confidence:.1%} confidence")
            
            # MCP insights summary
            if mcp_insights.get('insights'):
                synthesis_parts.append("Additional Insights: Market data and analysis from external sources")
            
            # Combine all parts
            if synthesis_parts:
                synthesis = " ".join(synthesis_parts)
            else:
                synthesis = "Analysis completed with available data."
            
            return synthesis
            
        except Exception as e:
            logger.error(f"Error generating synthesis: {e}")
            return "Error generating analysis synthesis."

    def _extract_insights(self, synthesis: str) -> List[str]:
        """Extract key insights from synthesis text"""
        try:
            insights = []
            
            # Simple insight extraction based on keywords
            if "bullish" in synthesis.lower():
                insights.append("Bullish market sentiment detected")
            if "bearish" in synthesis.lower():
                insights.append("Bearish market sentiment detected")
            if "overbought" in synthesis.lower():
                insights.append("Overbought conditions may indicate pullback")
            if "oversold" in synthesis.lower():
                insights.append("Oversold conditions may indicate bounce")
            if "high volatility" in synthesis.lower():
                insights.append("High volatility suggests increased risk")
            if "strong trend" in synthesis.lower():
                insights.append("Strong trend continuation likely")
            
            return insights
            
        except Exception as e:
            logger.error(f"Error extracting insights: {e}")
            return []

    def _calculate_synthesis_confidence(self, analysis_data: Dict[str, Any]) -> float:
        """Calculate confidence score for synthesis"""
        try:
            confidence_factors = []
            
            # Market data confidence
            market_data = analysis_data.get('market_data', {})
            if market_data.get('data'):
                confidence_factors.append(0.3)
            
            # Technical analysis confidence
            technical_analysis = analysis_data.get('technical_analysis', {})
            if technical_analysis.get('indicators'):
                confidence_factors.append(0.3)
            
            # ML predictions confidence
            ml_predictions = analysis_data.get('ml_predictions', {})
            if ml_predictions.get('confidence', 0) > 0:
                confidence_factors.append(ml_predictions['confidence'] * 0.2)
            
            # MCP insights confidence
            mcp_insights = analysis_data.get('mcp_insights', {})
            if mcp_insights.get('insights'):
                confidence_factors.append(0.2)
            
            # Calculate weighted average
            if confidence_factors:
                return sum(confidence_factors) / len(confidence_factors)
            else:
                return 0.0
                
        except Exception as e:
            logger.error(f"Error calculating synthesis confidence: {e}")
            return 0.0

    def _generate_reasoning(self, analysis_data: Dict[str, Any]) -> str:
        """Generate reasoning for the analysis"""
        try:
            reasoning_parts = []
            
            # Data quality reasoning
            market_data = analysis_data.get('market_data', {})
            if market_data.get('data'):
                symbol_count = len(market_data['data'])
                reasoning_parts.append(f"Analysis based on {symbol_count} symbol(s)")
            
            # Technical analysis reasoning
            technical_analysis = analysis_data.get('technical_analysis', {})
            if technical_analysis.get('indicators'):
                reasoning_parts.append("Technical indicators analyzed")
            
            # ML predictions reasoning
            ml_predictions = analysis_data.get('ml_predictions', {})
            if ml_predictions.get('price_prediction'):
                confidence = ml_predictions.get('confidence', 0)
                reasoning_parts.append(f"ML model prediction (confidence: {confidence:.1%})")
            
            # MCP insights reasoning
            mcp_insights = analysis_data.get('mcp_insights', {})
            if mcp_insights.get('insights'):
                reasoning_parts.append("External data sources consulted")
            
            if reasoning_parts:
                return "Analysis reasoning: " + "; ".join(reasoning_parts)
            else:
                return "Analysis based on available data sources"
                
        except Exception as e:
            logger.error(f"Error generating reasoning: {e}")
            return "Analysis reasoning unavailable"

    async def verify_facts(self, analysis_data: Dict[str, Any]) -> Dict[str, Any]:
        """Verify facts in the analysis data"""
        try:
            verification_result = {
                "facts_verified": True,
                "verification_score": 0.0,
                "verified_facts": [],
                "unverified_facts": [],
                "verification_method": "cross_reference"
            }
            
            # Mock fact verification
            # In real implementation, this would cross-reference with reliable sources
            
            market_data = analysis_data.get('market_data', {})
            if market_data.get('data'):
                for symbol, data in market_data['data'].items():
                    price = data.get('price', 0)
                    if price > 0:
                        verification_result["verified_facts"].append(f"{symbol} price: ${price:.2f}")
                        verification_result["verification_score"] += 0.1
            
            technical_analysis = analysis_data.get('technical_analysis', {})
            if technical_analysis.get('indicators'):
                verification_result["verified_facts"].append("Technical indicators calculated")
                verification_result["verification_score"] += 0.2
            
            ml_predictions = analysis_data.get('ml_predictions', {})
            if ml_predictions.get('price_prediction'):
                verification_result["verified_facts"].append("ML prediction generated")
                verification_result["verification_score"] += 0.1
            
            # Normalize verification score
            verification_result["verification_score"] = min(1.0, verification_result["verification_score"])
            verification_result["facts_verified"] = verification_result["verification_score"] > 0.5
            
            return verification_result
            
        except Exception as e:
            logger.error(f"Error verifying facts: {e}")
            return {"facts_verified": False, "verification_score": 0.0, "error": str(e)}
