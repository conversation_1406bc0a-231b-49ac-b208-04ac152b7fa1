"""
Discord Formatter for ASK Pipeline

Formats AI responses for Discord with embeds, character limits, and styling.
"""

import time
from typing import Dict, Any, Optional
from dataclasses import dataclass

from src.shared.error_handling.logging import get_logger
from .response_generator import ResponseResult

logger = get_logger(__name__)

@dataclass
class FormattedResponse:
    """Formatted response for Discord"""
    text: Optional[str] = None
    embed: Optional[Dict[str, Any]] = None
    execution_time: float = 0.0

class DiscordFormatter:
    """
    Discord Response Formatter
    
    Formats responses for Discord with:
    - Embed creation with proper styling
    - Character limit handling (2000 chars for text, 6000 for embeds)
    - Markdown formatting
    - Error message formatting
    - Success indicators
    """
    
    def __init__(self):
        self.max_text_length = 2000
        self.max_embed_description = 4096
        self.max_embed_field_value = 1024
        
        # Discord color scheme
        self.colors = {
            'success': 0x00ff00,      # Green
            'info': 0x0099ff,         # Blue  
            'warning': 0xffaa00,      # Orange
            'error': 0xff0000,        # Red
            'trading': 0x1f8b4c,      # Dark green
            'casual': 0x7289da        # Discord blurple
        }
    
    async def format(self, response_result: ResponseResult, correlation_id: str) -> FormattedResponse:
        """
        Format response result for Discord
        
        Args:
            response_result: Result from response generation
            correlation_id: Request correlation ID
            
        Returns:
            FormattedResponse with Discord-formatted content
        """
        start_time = time.time()
        
        try:
            logger.info(f"🔍 Formatter input: {response_result.response[:200]}...", extra={'correlation_id': correlation_id})
            logger.debug(f"Formatting response for Discord", extra={
                'correlation_id': correlation_id,
                'response_length': len(response_result.response),
                'has_disclaimer': response_result.disclaimer_added
            })
            
            # Determine if we should use embed or plain text
            use_embed = self._should_use_embed(response_result.response)
            logger.info(f"🔍 Use embed: {use_embed}", extra={'correlation_id': correlation_id})
            
            if use_embed:
                logger.info(f"🔍 Creating embed...", extra={'correlation_id': correlation_id})
                embed = await self._create_embed(response_result, correlation_id)
                formatted_response = FormattedResponse(
                    embed=embed,
                    execution_time=time.time() - start_time
                )
            else:
                logger.info(f"🔍 Formatting text...", extra={'correlation_id': correlation_id})
                text = await self._format_text(response_result.response, correlation_id)
                logger.info(f"🔍 Formatted text: {text[:200] if text else 'None'}...", extra={'correlation_id': correlation_id})
                formatted_response = FormattedResponse(
                    text=text,
                    execution_time=time.time() - start_time
                )
            
            logger.info(f"🔍 Formatter output: {formatted_response.text[:200] if formatted_response.text else 'None'}...", extra={'correlation_id': correlation_id})
            logger.debug(f"Response formatting completed", extra={
                'correlation_id': correlation_id,
                'format_type': 'embed' if use_embed else 'text',
                'execution_time': formatted_response.execution_time
            })
            
            return formatted_response
            
        except Exception as e:
            logger.error(f"Formatter error: {e}", extra={'correlation_id': correlation_id})
            import traceback
            logger.error(f"Formatter traceback: {traceback.format_exc()}", extra={'correlation_id': correlation_id})
            
            # Return a simple fallback response
            return FormattedResponse(
                text=response_result.response,
                execution_time=time.time() - start_time
            )
    
    def _should_use_embed(self, response: str) -> bool:
        """Determine if response should use embed format"""
        # Use embed for longer responses or those with structure
        if len(response) > 500:
            return True
        
        # Use embed if response contains multiple sections
        if response.count('\n\n') >= 2:
            return True
        
        # Use embed if response contains markdown headers or lists
        if any(marker in response for marker in ['**', '•', '#', '##']):
            return True
        
        return False
    
    async def _create_embed(self, response_result: ResponseResult, correlation_id: str) -> Dict[str, Any]:
        """Create Discord embed from response"""
        response = response_result.response
        
        logger.info(f"🔍 Creating embed for response: {response[:200]}...", extra={'correlation_id': correlation_id})
        
        # Determine embed color based on content
        color = self._get_embed_color(response)
        
        # Split response into title and description
        title, description = self._split_response_for_embed(response)
        
        logger.info(f"🔍 Embed title: {title}, description: {description[:100]}...", extra={'correlation_id': correlation_id})
        
        # Create base embed with enhanced metadata
        embed = {
            'title': title,
            'description': description,
            'color': color,
            'timestamp': time.strftime('%Y-%m-%dT%H:%M:%S.000Z'),
            'footer': {
                'text': self._create_footer_text(response_result)
            }
        }

        # Add author field for branding
        embed['author'] = {
            'name': 'Trading Assistant',
            'icon_url': 'https://cdn.discordapp.com/emojis/📊.png'  # Placeholder
        }
        
        # Add fields if response has structured content
        fields = self._extract_fields_from_response(response)
        if fields:
            embed['fields'] = fields
        
        # Add disclaimer field if present
        if response_result.disclaimer_added and '⚠️' in response:
            disclaimer_text = self._extract_disclaimer(response)
            if disclaimer_text:
                embed['fields'] = embed.get('fields', [])
                embed['fields'].append({
                    'name': '⚠️ Important Disclaimer',
                    'value': disclaimer_text,
                    'inline': False
                })
                
                # Remove disclaimer from description to avoid duplication
                embed['description'] = response.split('⚠️')[0].strip()
        
        # Ensure embed doesn't exceed Discord limits
        embed = self._truncate_embed(embed)
        
        logger.info(f"🔍 Created embed: {embed.get('title', 'No title')} - {len(str(embed))} chars", extra={'correlation_id': correlation_id})
        
        return embed
    
    async def _format_text(self, response: str, correlation_id: str) -> str:
        """Format response as plain text"""
        # Ensure text doesn't exceed Discord's character limit
        if len(response) > self.max_text_length:
            # Truncate and add indicator
            truncated = response[:self.max_text_length - 50]
            # Try to cut at a sentence boundary
            last_period = truncated.rfind('.')
            if last_period > self.max_text_length - 200:
                truncated = truncated[:last_period + 1]
            
            response = truncated + "\n\n*[Response truncated due to length]*"
        
        return response
    
    def _get_embed_color(self, response: str) -> int:
        """Determine embed color based on response content"""
        response_lower = response.lower()
        
        if any(word in response_lower for word in ['error', 'failed', 'problem']):
            return self.colors['error']
        elif any(word in response_lower for word in ['warning', 'caution', 'risk']):
            return self.colors['warning']
        elif any(word in response_lower for word in ['trading', 'stock', 'market', 'price']):
            return self.colors['trading']
        elif any(word in response_lower for word in ['hello', 'help', 'welcome']):
            return self.colors['casual']
        else:
            return self.colors['info']
    
    def _split_response_for_embed(self, response: str) -> tuple:
        """Split response into title and description for embed"""
        lines = response.split('\n')

        # Look for a natural title (first line if short, or extract from content)
        if lines and len(lines[0]) < 100 and not lines[0].startswith('•'):
            title = lines[0].strip()
            description = '\n'.join(lines[1:]).strip()
        else:
            # Generate a smart title based on content analysis
            title = self._generate_smart_title(response)
            description = response

        # Ensure title isn't too long (Discord limit is 256 chars)
        if len(title) > 256:
            title = title[:253] + "..."

        return title, description

    def _generate_smart_title(self, response: str) -> str:
        """Generate intelligent title based on response content"""
        response_lower = response.lower()

        # Stock/Symbol specific
        import re
        symbols = re.findall(r'\b[A-Z]{1,5}\b', response)
        if symbols:
            symbol = symbols[0]
            if 'price' in response_lower:
                return f"📈 {symbol} Price Analysis"
            elif 'technical' in response_lower or 'chart' in response_lower:
                return f"📊 {symbol} Technical Analysis"
            elif 'risk' in response_lower:
                return f"⚠️ {symbol} Risk Assessment"
            else:
                return f"💹 {symbol} Market Data"

        # Content-based titles
        if any(word in response_lower for word in ['option', 'call', 'put']):
            return "🎯 Options Trading Analysis"
        elif 'portfolio' in response_lower:
            return "📋 Portfolio Analysis"
        elif any(word in response_lower for word in ['risk', 'volatility']):
            return "⚠️ Risk Analysis"
        elif 'market' in response_lower and 'outlook' in response_lower:
            return "🔮 Market Outlook"
        elif any(word in response_lower for word in ['learn', 'education', 'explain']):
            return "🎓 Educational Content"
        elif any(word in response_lower for word in ['hello', 'hi', 'help']):
            return "👋 Trading Assistant"
        elif 'data' in response_lower and 'unavailable' in response_lower:
            return "⚠️ Data Unavailable"
        else:
            return "💡 Market Analysis"
    
    def _extract_fields_from_response(self, response: str) -> list:
        """Extract structured fields from response"""
        fields = []
        
        # Look for sections marked with **headers**
        import re
        sections = re.findall(r'\*\*(.*?)\*\*:(.*?)(?=\*\*|$)', response, re.DOTALL)
        
        for name, value in sections:
            if len(value.strip()) > 0:
                fields.append({
                    'name': name.strip(),
                    'value': value.strip()[:self.max_embed_field_value],
                    'inline': len(value.strip()) < 100
                })
        
        return fields[:25]  # Discord limit is 25 fields
    
    def _extract_disclaimer(self, response: str) -> str:
        """Extract disclaimer text from response"""
        if '⚠️' in response:
            disclaimer_part = response.split('⚠️')[1] if '⚠️' in response else ''
            # Clean up the disclaimer text
            disclaimer = disclaimer_part.replace('**Disclaimer:**', '').strip()
            return disclaimer[:self.max_embed_field_value]
        return ''
    
    def _truncate_embed(self, embed: Dict[str, Any]) -> Dict[str, Any]:
        """Ensure embed doesn't exceed Discord limits"""
        # Truncate description if too long
        if 'description' in embed and len(embed['description']) > self.max_embed_description:
            embed['description'] = embed['description'][:self.max_embed_description - 50] + "...\n\n*[Content truncated]*"
        
        # Truncate field values if too long
        if 'fields' in embed:
            for field in embed['fields']:
                if len(field['value']) > self.max_embed_field_value:
                    field['value'] = field['value'][:self.max_embed_field_value - 20] + "...*[truncated]*"
        
        return embed

    def _create_footer_text(self, response_result: ResponseResult) -> str:
        """Create intelligent footer text with performance metrics"""
        footer_parts = []

        # Response time
        footer_parts.append(f"⚡ {response_result.execution_time:.2f}s")

        # Confidence indicator
        if hasattr(response_result, 'confidence') and response_result.confidence:
            confidence_emoji = "🟢" if response_result.confidence > 0.8 else "🟡" if response_result.confidence > 0.6 else "🔴"
            footer_parts.append(f"{confidence_emoji} {response_result.confidence:.0%}")

        # AI model indicator
        if hasattr(response_result, 'fallback_used') and response_result.fallback_used:
            footer_parts.append("🔄 Fallback")
        else:
            footer_parts.append("🤖 AI")

        # Cache indicator
        if hasattr(response_result, 'cache_used') and response_result.cache_used:
            footer_parts.append("💾 Cached")

        return " • ".join(footer_parts)
