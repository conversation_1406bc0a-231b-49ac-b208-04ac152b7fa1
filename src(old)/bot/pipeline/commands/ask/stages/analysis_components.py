"""
Analysis Components for Tool Orchestrator

Contains the core analysis logic split from the monolithic tool_orchestrator.py
"""

import time
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum

from src.shared.error_handling.logging import get_logger
from .intent_detector import IntentResult
from ..tools.mcp_client import <PERSON><PERSON><PERSON>, MCPToolResult
from src.analysis.ai.ml_models import trading_ml_model, MLPrediction

logger = get_logger(__name__)

class AnalysisDepth(Enum):
    BASIC = "basic"
    INTERMEDIATE = "intermediate"
    COMPREHENSIVE = "comprehensive"

@dataclass
class ToolResult:
    """Result of tool orchestration"""
    tools_used: List[str]
    data: Dict[str, Any]
    execution_time: float
    success: bool = True
    cache_hit: bool = False
    errors: List[str] = None
    analysis_depth: str = "basic"
    confidence_score: float = 0.0
    fact_verified: bool = False

class AnalysisComponents:
    """
    Core analysis components for financial data processing
    
    Handles:
    - Technical analysis
    - ML predictions
    - Trend analysis
    - Risk assessment
    - Momentum analysis
    """

    def __init__(self):
        """Initialize analysis components"""
        self.ml_model = trading_ml_model

    def _analyze_trend(self, indicators, current_price) -> Dict[str, Any]:
        """Analyze market trend from technical indicators"""
        try:
            trend_analysis = {
                "trend_direction": "neutral",
                "trend_strength": 0.0,
                "trend_duration": "short",
                "key_levels": [],
                "breakout_potential": 0.0
            }
            
            if not indicators or not current_price:
                return trend_analysis
                
            # Simple trend analysis logic
            sma_20 = indicators.get('sma_20', current_price)
            sma_50 = indicators.get('sma_50', current_price)
            
            if current_price > sma_20 > sma_50:
                trend_analysis["trend_direction"] = "bullish"
                trend_analysis["trend_strength"] = min(1.0, (current_price - sma_50) / sma_50)
            elif current_price < sma_20 < sma_50:
                trend_analysis["trend_direction"] = "bearish"
                trend_analysis["trend_strength"] = min(1.0, (sma_50 - current_price) / sma_50)
            
            # Calculate key support/resistance levels
            trend_analysis["key_levels"] = [
                sma_20 * 0.98,  # Support
                sma_20 * 1.02,  # Resistance
                sma_50 * 0.95,  # Strong support
                sma_50 * 1.05   # Strong resistance
            ]
            
            return trend_analysis
            
        except Exception as e:
            logger.error(f"Error in trend analysis: {e}")
            return {"trend_direction": "unknown", "trend_strength": 0.0}

    def _analyze_momentum(self, indicators) -> Dict[str, Any]:
        """Analyze market momentum from technical indicators"""
        try:
            momentum_analysis = {
                "momentum_direction": "neutral",
                "momentum_strength": 0.0,
                "overbought_oversold": "neutral",
                "divergence_signals": []
            }
            
            if not indicators:
                return momentum_analysis
                
            # RSI analysis
            rsi = indicators.get('rsi', 50)
            if rsi > 70:
                momentum_analysis["overbought_oversold"] = "overbought"
                momentum_analysis["momentum_direction"] = "bearish"
            elif rsi < 30:
                momentum_analysis["overbought_oversold"] = "oversold"
                momentum_analysis["momentum_direction"] = "bullish"
            else:
                momentum_analysis["momentum_direction"] = "neutral"
                
            momentum_analysis["momentum_strength"] = abs(rsi - 50) / 50
            
            return momentum_analysis
            
        except Exception as e:
            logger.error(f"Error in momentum analysis: {e}")
            return {"momentum_direction": "unknown", "momentum_strength": 0.0}

    def _assess_risk(self, indicators, market_data) -> Dict[str, Any]:
        """Assess market risk from indicators and data"""
        try:
            risk_assessment = {
                "risk_level": "medium",
                "volatility": 0.0,
                "risk_factors": [],
                "risk_score": 0.5
            }
            
            if not indicators or not market_data:
                return risk_assessment
                
            # Calculate volatility
            price = market_data.get('price', 0)
            volatility = indicators.get('volatility', 0)
            risk_assessment["volatility"] = volatility
            
            # Risk factors
            risk_factors = []
            if volatility > 0.3:
                risk_factors.append("high_volatility")
            if indicators.get('rsi', 50) > 80:
                risk_factors.append("overbought_conditions")
            if indicators.get('rsi', 50) < 20:
                risk_factors.append("oversold_conditions")
                
            risk_assessment["risk_factors"] = risk_factors
            
            # Overall risk score
            risk_score = 0.5
            if volatility > 0.3:
                risk_score += 0.3
            if len(risk_factors) > 2:
                risk_score += 0.2
                
            risk_assessment["risk_score"] = min(1.0, risk_score)
            risk_assessment["risk_level"] = "high" if risk_score > 0.7 else "low" if risk_score < 0.3 else "medium"
            
            return risk_assessment
            
        except Exception as e:
            logger.error(f"Error in risk assessment: {e}")
            return {"risk_level": "unknown", "risk_score": 0.5}

    async def perform_technical_analysis(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Perform comprehensive technical analysis"""
        try:
            technical_analysis = {
                "indicators": {},
                "trend_analysis": {},
                "momentum_analysis": {},
                "risk_assessment": {},
                "signals": []
            }
            
            if not market_data:
                return technical_analysis
                
            # Calculate basic indicators
            price = market_data.get('price', 0)
            volume = market_data.get('volume', 0)
            
            # Simple technical indicators (in real implementation, use proper TA library)
            indicators = {
                'sma_20': price * 0.98,  # Mock SMA
                'sma_50': price * 0.95,  # Mock SMA
                'rsi': 50,  # Mock RSI
                'volatility': 0.2,  # Mock volatility
                'volume_ratio': volume / 1000000 if volume > 0 else 1.0
            }
            
            technical_analysis["indicators"] = indicators
            
            # Perform analysis
            technical_analysis["trend_analysis"] = self._analyze_trend(indicators, price)
            technical_analysis["momentum_analysis"] = self._analyze_momentum(indicators)
            technical_analysis["risk_assessment"] = self._assess_risk(indicators, market_data)
            
            # Generate signals
            signals = []
            if technical_analysis["trend_analysis"]["trend_direction"] == "bullish":
                signals.append("bullish_trend")
            if technical_analysis["momentum_analysis"]["overbought_oversold"] == "oversold":
                signals.append("oversold_bounce")
                
            technical_analysis["signals"] = signals
            
            return technical_analysis
            
        except Exception as e:
            logger.error(f"Error in technical analysis: {e}")
            return {"indicators": {}, "error": str(e)}

    async def perform_ml_predictions(self, market_data: Dict[str, Any], technical_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Perform ML-based predictions"""
        try:
            ml_predictions = {
                "price_prediction": {},
                "confidence": 0.0,
                "model_used": "trading_ml_model",
                "features_used": []
            }
            
            if not market_data or not technical_analysis:
                return ml_predictions
                
            # Prepare features for ML model
            features = {
                'price': market_data.get('price', 0),
                'volume': market_data.get('volume', 0),
                'rsi': technical_analysis.get('indicators', {}).get('rsi', 50),
                'volatility': technical_analysis.get('indicators', {}).get('volatility', 0.2)
            }
            
            ml_predictions["features_used"] = list(features.keys())
            
            # Get ML prediction (mock for now)
            try:
                prediction = await self.ml_model.predict(features)
                ml_predictions["price_prediction"] = {
                    "next_price": prediction.price,
                    "direction": prediction.direction,
                    "confidence": prediction.confidence
                }
                ml_predictions["confidence"] = prediction.confidence
            except Exception as e:
                logger.warning(f"ML prediction failed: {e}")
                ml_predictions["price_prediction"] = {
                    "next_price": market_data.get('price', 0),
                    "direction": "neutral",
                    "confidence": 0.0
                }
            
            return ml_predictions
            
        except Exception as e:
            logger.error(f"Error in ML predictions: {e}")
            return {"price_prediction": {}, "confidence": 0.0, "error": str(e)}

    def calculate_confidence_score(self, analysis_data: Dict[str, Any]) -> float:
        """Calculate overall confidence score for analysis"""
        try:
            confidence_factors = []
            
            # Technical analysis confidence
            tech_analysis = analysis_data.get('technical_analysis', {})
            if tech_analysis.get('indicators'):
                confidence_factors.append(0.3)
            
            # ML prediction confidence
            ml_predictions = analysis_data.get('ml_predictions', {})
            if ml_predictions.get('confidence', 0) > 0.5:
                confidence_factors.append(ml_predictions['confidence'] * 0.4)
            
            # Data quality confidence
            market_data = analysis_data.get('market_data', {})
            if market_data.get('price', 0) > 0:
                confidence_factors.append(0.3)
            
            # Calculate weighted average
            if confidence_factors:
                return sum(confidence_factors) / len(confidence_factors)
            else:
                return 0.0
                
        except Exception as e:
            logger.error(f"Error calculating confidence score: {e}")
            return 0.0
