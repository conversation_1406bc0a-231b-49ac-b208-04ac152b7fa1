"""
Data Collection Component for Tool Orchestrator

Handles market data collection, MCP insights, and symbol discovery
"""

import asyncio
import time
from typing import List, Dict, Any, Optional

from src.shared.error_handling.logging import get_logger
from .intent_detector import IntentResult
from ..tools.mcp_client import MC<PERSON>lient, MCPToolResult

logger = get_logger(__name__)

class DataCollector:
    """
    Handles data collection for financial analysis
    
    Responsibilities:
    - Symbol extraction and discovery
    - Market data collection
    - MCP insights gathering
    - Data validation and cleaning
    """

    def __init__(self):
        """Initialize data collector"""
        self.mcp_client = MCPClient()

    async def extract_symbols_from_query(self, query: str) -> List[str]:
        """Extract stock symbols from user query"""
        try:
            symbols = []
            query_upper = query.upper()
            
            # Common stock symbol patterns
            import re
            
            # Look for ticker symbols (3-5 uppercase letters)
            ticker_pattern = r'\b[A-Z]{3,5}\b'
            potential_tickers = re.findall(ticker_pattern, query_upper)
            
            # Filter out common words that aren't tickers
            common_words = {'THE', 'AND', 'FOR', 'ARE', 'BUT', 'NOT', 'YOU', 'ALL', 'CAN', 'HAD', 'HER', 'WAS', 'ONE', 'OUR', 'OUT', 'DAY', 'GET', 'HAS', 'HIM', 'HIS', 'HOW', 'ITS', 'MAY', 'NEW', 'NOW', 'OLD', 'SEE', 'TWO', 'WAY', 'WHO', 'BOY', 'DID', 'ITS', 'LET', 'PUT', 'SAY', 'SHE', 'TOO', 'USE'}
            
            for ticker in potential_tickers:
                if ticker not in common_words and len(ticker) >= 3:
                    symbols.append(ticker)
            
            # Look for explicit mentions like "AAPL stock" or "Apple (AAPL)"
            explicit_patterns = [
                r'(\w+)\s*\(([A-Z]{3,5})\)',  # Apple (AAPL)
                r'([A-Z]{3,5})\s+stock',     # AAPL stock
                r'stock\s+([A-Z]{3,5})',     # stock AAPL
            ]
            
            for pattern in explicit_patterns:
                matches = re.findall(pattern, query_upper)
                for match in matches:
                    if isinstance(match, tuple):
                        symbol = match[1] if len(match) > 1 else match[0]
                    else:
                        symbol = match
                    if symbol not in symbols and symbol not in common_words:
                        symbols.append(symbol)
            
            # Remove duplicates and return
            return list(set(symbols))
            
        except Exception as e:
            logger.error(f"Error extracting symbols from query: {e}")
            return []

    async def discover_and_screen_tickers(self, query: str, analysis_plan: Dict[str, Any]) -> Dict[str, Any]:
        """Discover and screen potential ticker symbols"""
        try:
            discovery_result = {
                "symbols": [],
                "discovery_method": "query_extraction",
                "screening_passed": [],
                "screening_failed": [],
                "confidence": 0.0
            }
            
            # Extract symbols from query
            symbols = await self.extract_symbols_from_query(query)
            discovery_result["symbols"] = symbols
            
            if not symbols:
                # Try to generate potential tickers based on query
                potential_tickers = await self._generate_potential_tickers(analysis_plan)
                discovery_result["symbols"] = potential_tickers
                discovery_result["discovery_method"] = "ai_generation"
            
            # Screen the discovered symbols
            if discovery_result["symbols"]:
                screened_symbols = await self._quick_screen_tickers(discovery_result["symbols"], analysis_plan)
                discovery_result["screening_passed"] = screened_symbols
                discovery_result["screening_failed"] = [s for s in discovery_result["symbols"] if s not in screened_symbols]
                discovery_result["confidence"] = len(screened_symbols) / len(discovery_result["symbols"]) if discovery_result["symbols"] else 0.0
            
            return discovery_result
            
        except Exception as e:
            logger.error(f"Error in ticker discovery: {e}")
            return {"symbols": [], "error": str(e)}

    async def _generate_potential_tickers(self, analysis_plan: Dict[str, Any]) -> List[str]:
        """Generate potential ticker symbols based on analysis plan"""
        try:
            # This would typically use AI to generate tickers based on query context
            # For now, return some common tickers as examples
            common_tickers = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'NVDA', 'META', 'NFLX', 'AMD', 'INTC']
            
            # Filter based on analysis plan context
            query_type = analysis_plan.get('query_type', 'general')
            if 'tech' in query_type.lower():
                return ['AAPL', 'MSFT', 'GOOGL', 'NVDA', 'AMD']
            elif 'energy' in query_type.lower():
                return ['XOM', 'CVX', 'COP', 'EOG', 'SLB']
            else:
                return common_tickers[:5]
                
        except Exception as e:
            logger.error(f"Error generating potential tickers: {e}")
            return []

    async def _quick_screen_tickers(self, potential_tickers: List[str], analysis_plan: Dict[str, Any]) -> List[str]:
        """Quickly screen ticker symbols for validity"""
        try:
            screened_tickers = []
            
            for ticker in potential_tickers:
                try:
                    # Basic validation - check if ticker looks valid
                    if len(ticker) >= 3 and len(ticker) <= 5 and ticker.isalpha():
                        # Mock validation - in real implementation, check against actual stock database
                        screened_tickers.append(ticker)
                except Exception as e:
                    logger.debug(f"Ticker {ticker} failed screening: {e}")
                    continue
            
            return screened_tickers
            
        except Exception as e:
            logger.error(f"Error in ticker screening: {e}")
            return []

    async def collect_market_data(self, symbols: List[str], intent: str) -> Dict[str, Any]:
        """Collect market data for given symbols"""
        try:
            market_data = {
                "symbols": symbols,
                "data": {},
                "collection_time": time.time(),
                "data_sources": [],
                "errors": []
            }
            
            if not symbols:
                return market_data
            
            # Mock market data collection
            # In real implementation, this would call actual market data APIs
            for symbol in symbols:
                try:
                    # Simulate market data
                    mock_data = {
                        'price': 150.0 + (hash(symbol) % 100),  # Mock price
                        'volume': 1000000 + (hash(symbol) % 500000),
                        'change': (hash(symbol) % 20) - 10,  # Mock change
                        'change_percent': ((hash(symbol) % 20) - 10) / 100,
                        'high': 160.0 + (hash(symbol) % 50),
                        'low': 140.0 + (hash(symbol) % 30),
                        'open': 155.0 + (hash(symbol) % 40),
                        'timestamp': time.time()
                    }
                    
                    market_data["data"][symbol] = mock_data
                    market_data["data_sources"].append(f"mock_api_{symbol}")
                    
                except Exception as e:
                    logger.warning(f"Failed to collect data for {symbol}: {e}")
                    market_data["errors"].append(f"Failed to collect data for {symbol}: {str(e)}")
            
            return market_data
            
        except Exception as e:
            logger.error(f"Error collecting market data: {e}")
            return {"symbols": symbols, "data": {}, "error": str(e)}

    async def get_mcp_insights(self, query: str, symbols: List[str], intent_result: IntentResult) -> Dict[str, Any]:
        """Get insights from MCP tools"""
        try:
            mcp_insights = {
                "query": query,
                "symbols": symbols,
                "insights": {},
                "tools_used": [],
                "execution_time": 0.0,
                "errors": []
            }
            
            start_time = time.time()
            
            if not symbols:
                return mcp_insights
            
            # Use MCP client to get insights
            try:
                mcp_result = await self.mcp_client.execute_query(query, symbols)
                mcp_insights["insights"] = mcp_result.data
                mcp_insights["tools_used"] = mcp_result.tools_used
                mcp_insights["execution_time"] = time.time() - start_time
                
            except Exception as e:
                logger.warning(f"MCP insights failed: {e}")
                mcp_insights["errors"].append(f"MCP insights failed: {str(e)}")
                mcp_insights["execution_time"] = time.time() - start_time
            
            return mcp_insights
            
        except Exception as e:
            logger.error(f"Error getting MCP insights: {e}")
            return {"query": query, "symbols": symbols, "insights": {}, "error": str(e)}

    def validate_data_quality(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate the quality of collected data"""
        try:
            validation_result = {
                "is_valid": True,
                "quality_score": 0.0,
                "issues": [],
                "recommendations": []
            }
            
            if not data:
                validation_result["is_valid"] = False
                validation_result["issues"].append("No data provided")
                return validation_result
            
            # Check data completeness
            required_fields = ['price', 'volume', 'timestamp']
            quality_factors = []
            
            for symbol, symbol_data in data.get('data', {}).items():
                symbol_quality = 0.0
                for field in required_fields:
                    if field in symbol_data and symbol_data[field] is not None:
                        symbol_quality += 1.0
                
                symbol_quality = symbol_quality / len(required_fields)
                quality_factors.append(symbol_quality)
                
                if symbol_quality < 0.5:
                    validation_result["issues"].append(f"Incomplete data for {symbol}")
            
            # Calculate overall quality score
            if quality_factors:
                validation_result["quality_score"] = sum(quality_factors) / len(quality_factors)
                validation_result["is_valid"] = validation_result["quality_score"] > 0.5
            
            # Add recommendations
            if validation_result["quality_score"] < 0.8:
                validation_result["recommendations"].append("Consider using additional data sources")
            
            return validation_result
            
        except Exception as e:
            logger.error(f"Error validating data quality: {e}")
            return {"is_valid": False, "quality_score": 0.0, "error": str(e)}
