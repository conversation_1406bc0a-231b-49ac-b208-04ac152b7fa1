"""
Response Generator for ASK Pipeline

AI-powered response synthesis using centralized prompt system and tool results.
Intelligently combines query context, intent, and tool data into high-quality responses.
"""

import time
import async<PERSON>
import j<PERSON>
from typing import Dict, Any, Optional
from dataclasses import dataclass

from src.shared.error_handling.logging import get_logger
from src.shared.ai_chat.ai_client import AIClientWrapper
from src.core.prompts import PromptManager
from .intent_detector import IntentResult
from .simplified_tool_orchestrator import ToolResult
# Proactive response removed - over-engineered for Discord bot

logger = get_logger(__name__)

@dataclass
class ResponseResult:
    """Result of response generation"""
    response: str
    confidence: float
    execution_time: float
    model_used: str = "ai_synthesis"
    tokens_used: int = 0
    disclaimer_added: bool = False
    cache_used: bool = False
    fallback_used: bool = False

class ResponseGenerator:
    """
    AI-Powered Response Generation Stage

    Synthesizes intelligent responses using:
    - User query context
    - Intent classification results
    - Tool execution results
    - Centralized prompt system
    - Context-aware AI prompting

    Features:
    - Single AI call for response synthesis
    - Tool result integration
    - Context-aware prompting
    - Fallback handling
    - Trading disclaimer management
    """

    def __init__(self):
        # Create context for AI client
        class ResponseContext:
            def __init__(self):
                self.pipeline_id = "ask_response_generator"

        # Initialize AI client for response synthesis
        self.ai_client = AIClientWrapper(context=ResponseContext())

        # Initialize centralized prompt manager
        self.prompt_manager = PromptManager()

        # Initialize proactive response generator for ASK2 transformation
        # Proactive generator removed - over-engineered for Discord bot

        # Fallback responses for when AI is unavailable
        self.fallback_responses = {
            "casual": [
                "Hello! I'm here to help you with trading and market questions. What would you like to know?",
                "Hi there! I can assist you with market analysis, stock information, and trading insights. How can I help?",
                "Welcome! I'm your trading assistant. Feel free to ask me about stocks, markets, or trading strategies."
            ],
            "data_needed": [
                "I've analyzed the available data for your query. Here's what I found:",
                "Based on the current market information, here's my analysis:",
                "After reviewing the relevant data, I can provide the following insights:"
            ]
        }
    
    async def generate(
        self,
        query: str,
        intent_result: IntentResult,
        tool_result: Optional[ToolResult],
        correlation_id: str
    ) -> ResponseResult:
        """
        Generate AI-powered response based on query, intent, and tool results

        Args:
            query: User's question or request
            intent_result: Result from intent detection
            tool_result: Result from tool orchestration (if any)
            correlation_id: Request correlation ID

        Returns:
            ResponseResult with generated response
        """
        start_time = time.time()

        logger.debug(f"Generating AI response", extra={
            'correlation_id': correlation_id,
            'intent': intent_result.intent,
            'has_tool_data': tool_result is not None,
            'tools_used': tool_result.tools_used if tool_result else []
        })

        # Generate AI response directly (proactive response removed - over-engineered)
        try:
            response, confidence, tokens_used = await self._generate_ai_response(
                query, intent_result, tool_result, correlation_id
            )
            fallback_used = False

        except Exception as e:
            logger.warning(f"AI response generation failed, using fallback", extra={
                'correlation_id': correlation_id,
                'error': str(e)
            })

            # Use fallback response generation
            response = await self._generate_fallback_response(
                query, intent_result, tool_result, correlation_id
            )
            confidence = 0.6  # Lower confidence for fallback
            tokens_used = len(response.split())
            fallback_used = True

        # Add disclaimer for trading-related content
        disclaimer_added = self._should_add_disclaimer(query, response)
        if disclaimer_added:
            response = self._add_trading_disclaimer(response)

        execution_time = time.time() - start_time

        result = ResponseResult(
            response=response,
            confidence=confidence,
            execution_time=execution_time,
            model_used=self.ai_client.model_name if hasattr(self.ai_client, 'model_name') else "ai_synthesis",
            tokens_used=tokens_used,
            disclaimer_added=disclaimer_added,
            fallback_used=fallback_used
        )

        logger.info(f"Response generation completed", extra={
            'correlation_id': correlation_id,
            'response_length': len(response),
            'execution_time': execution_time,
            'disclaimer_added': disclaimer_added,
            'fallback_used': fallback_used,
            'confidence': confidence
        })

        return result
    
    async def _generate_ai_response(
        self,
        query: str,
        intent_result: IntentResult,
        tool_result: Optional[ToolResult],
        correlation_id: str
    ) -> tuple[str, float, int]:
        """
        Generate AI-powered response using centralized prompt system

        Returns:
            tuple: (response_text, confidence_score, tokens_used)
        """
        # Determine appropriate persona based on query content
        persona = self._select_persona(query, intent_result)

        # Build context for AI response
        context = self._build_response_context(query, intent_result, tool_result)

        # Create response generation prompt
        system_prompt = self.prompt_manager.get_system_prompt(
            persona=persona,
            context=context
        )

        # Build user prompt with query and context
        user_prompt = self._build_user_prompt(query, intent_result, tool_result)

        logger.debug(f"Calling AI for response synthesis", extra={
            'correlation_id': correlation_id,
            'persona': persona,
            'context_keys': list(context.keys()),
            'has_tool_data': tool_result is not None
        })

        # Call AI for response generation
        ai_response = await self.ai_client.generate_response(
            system_prompt=system_prompt,
            user_prompt=user_prompt,
            correlation_id=correlation_id
        )

        if not ai_response or not ai_response.content:
            raise Exception("AI returned empty response")

        # Extract confidence from AI response if available
        confidence = getattr(ai_response, 'confidence', 0.85)
        tokens_used = getattr(ai_response, 'tokens_used', len(ai_response.content.split()))

        return ai_response.content, confidence, tokens_used

    def _select_persona(self, query: str, intent_result: IntentResult) -> str:
        """Select appropriate AI persona based on query content"""
        query_lower = query.lower()

        # Check for specific trading/analysis keywords
        if any(word in query_lower for word in ['risk', 'portfolio', 'diversification', 'volatility']):
            return "risk_analyst"
        elif any(word in query_lower for word in ['chart', 'technical', 'indicator', 'pattern', 'trend']):
            return "trading_expert"
        elif any(word in query_lower for word in ['option', 'call', 'put', 'strike', 'expiration']):
            return "options_specialist"
        elif any(word in query_lower for word in ['market', 'economy', 'sector', 'industry']):
            return "market_analyst"
        elif intent_result.intent == "casual" or any(word in query_lower for word in ['learn', 'explain', 'how', 'what']):
            return "educational_assistant"
        else:
            # Default to trading expert for data-driven queries
            return "trading_expert"

    def _build_response_context(
        self,
        query: str,
        intent_result: IntentResult,
        tool_result: Optional[ToolResult]
    ) -> Dict[str, Any]:
        """Build context dictionary for AI response generation"""
        context = {
            "query_intent": intent_result.intent,
            "query_confidence": intent_result.confidence,
            "query_entities": intent_result.entities or {},
            "has_market_data": False,
            "tools_executed": [],
            "data_summary": ""
        }

        # Add tool result context if available
        if tool_result:
            context.update({
                "has_market_data": bool(tool_result.tools_used),
                "tools_executed": tool_result.tools_used,
                "execution_time": tool_result.execution_time,
                "cache_hit": tool_result.cache_hit
            })

            # Summarize tool data for AI context
            if tool_result.results:
                context["data_summary"] = self._summarize_tool_data(tool_result.results)

        return context

    def _build_user_prompt(
        self,
        query: str,
        intent_result: IntentResult,
        tool_result: Optional[ToolResult]
    ) -> str:
        """Build user prompt for AI response generation"""
        prompt_parts = [
            f"User Query: {query}",
            f"Query Intent: {intent_result.intent}",
            f"Intent Confidence: {intent_result.confidence:.2f}"
        ]

        # Add entity information if available
        if intent_result.entities:
            entities_str = ", ".join([f"{k}: {v}" for k, v in intent_result.entities.items()])
            prompt_parts.append(f"Extracted Entities: {entities_str}")

        # Add tool execution results if available
        if tool_result and tool_result.tools_used:
            prompt_parts.append(f"Tools Executed: {', '.join(tool_result.tools_used)}")

            if tool_result.results:
                prompt_parts.append("Tool Results:")
                for tool_name, result in tool_result.results.items():
                    if result.get('success'):
                        data_preview = str(result.get('data', ''))[:200]  # First 200 chars
                        prompt_parts.append(f"- {tool_name}: {data_preview}...")
                    else:
                        prompt_parts.append(f"- {tool_name}: Failed - {result.get('error', 'Unknown error')}")
        else:
            prompt_parts.append("No market data tools were executed.")

        prompt_parts.append("\nPlease provide a helpful, accurate response based on the above information.")

        return "\n".join(prompt_parts)

    def _summarize_tool_data(self, tool_results: Dict[str, Any]) -> str:
        """Create a summary of tool execution results for context"""
        summaries = []

        for tool_name, result in tool_results.items():
            if result.get('success'):
                data = result.get('data', {})
                if isinstance(data, dict):
                    # Extract key metrics from common tool responses
                    if 'price' in data:
                        summaries.append(f"{tool_name}: Price data available")
                    elif 'values' in data:
                        summaries.append(f"{tool_name}: Technical indicator data available")
                    elif 'overview' in data:
                        summaries.append(f"{tool_name}: Company overview data available")
                    else:
                        summaries.append(f"{tool_name}: Data retrieved successfully")
                else:
                    summaries.append(f"{tool_name}: Data retrieved")
            else:
                summaries.append(f"{tool_name}: Failed to retrieve data")

        return "; ".join(summaries) if summaries else "No tool data available"
    
    async def _generate_fallback_response(
        self,
        query: str,
        intent_result: IntentResult,
        tool_result: Optional[ToolResult],
        correlation_id: str
    ) -> str:
        """Generate fallback response when AI is unavailable"""
        import random

        # Select appropriate fallback based on intent
        if intent_result.intent == "casual":
            return await self._generate_casual_fallback(query)
        else:
            return await self._generate_data_fallback(query, tool_result)

    async def _generate_casual_fallback(self, query: str) -> str:
        """Generate fallback response for casual queries"""
        import random

        query_lower = query.lower()

        if any(word in query_lower for word in ['hello', 'hi', 'hey']):
            return random.choice(self.fallback_responses["casual"])
        elif 'help' in query_lower:
            return ("I can help you with:\n"
                   "• Stock prices and market data\n"
                   "• Technical and fundamental analysis\n"
                   "• Trading strategies and insights\n"
                   "• Market news and trends\n"
                   "• Educational content about trading\n\n"
                   "What would you like to explore?")
        elif any(word in query_lower for word in ['what', 'how', 'explain']):
            return ("I'd be happy to explain trading concepts! I can help with topics like:\n"
                   "• Market basics and terminology\n"
                   "• Technical analysis indicators\n"
                   "• Risk management strategies\n"
                   "• Different trading styles\n"
                   "• Investment fundamentals\n\n"
                   "What specific topic interests you?")
        else:
            return random.choice(self.fallback_responses["casual"])

    async def _generate_data_fallback(
        self,
        query: str,
        tool_result: Optional[ToolResult]
    ) -> str:
        """Generate fallback response for data-driven queries"""
        import random

        base_response = random.choice(self.fallback_responses["data_needed"])

        if tool_result and tool_result.tools_used:
            # Include information about tools used
            tools_info = f"\n\n**Data Sources Used:** {', '.join(tool_result.tools_used)}"

            # Add analysis based on tools
            if any(tool in tool_result.tools_used for tool in ['get_global_quote', 'market_data']):
                base_response += "\n\n📊 **Market Data Analysis:**\n• Current market conditions analyzed\n• Price trends evaluated\n• Volume patterns reviewed"

            if any(tool in tool_result.tools_used for tool in ['get_macd', 'get_rsi', 'technical_analysis']):
                base_response += "\n\n📈 **Technical Analysis:**\n• Chart patterns identified\n• Key support/resistance levels noted\n• Momentum indicators assessed"

            if 'news_search' in tool_result.tools_used:
                base_response += "\n\n📰 **Recent News Impact:**\n• Latest market news reviewed\n• Sentiment analysis conducted\n• Key events identified"

            base_response += tools_info
        else:
            # No tool data available
            base_response += "\n\nI'm currently unable to access live market data, but I can provide general guidance based on trading principles and market knowledge."

        return base_response
    
    def _should_add_disclaimer(self, query: str, response: str) -> bool:
        """Determine if trading disclaimer should be added"""
        trading_keywords = [
            'buy', 'sell', 'invest', 'trade', 'stock', 'price', 'recommendation',
            'advice', 'strategy', 'profit', 'loss', 'risk', 'return'
        ]
        
        combined_text = (query + " " + response).lower()
        return any(keyword in combined_text for keyword in trading_keywords)
    
    def _add_trading_disclaimer(self, response: str) -> str:
        """Add trading disclaimer to response"""
        disclaimer = "\n\n⚠️ **Disclaimer:** This information is for educational purposes only and should not be considered as financial advice. Always conduct your own research and consult with qualified financial advisors before making investment decisions. Trading involves risk and you may lose money."
        
        return response + disclaimer
