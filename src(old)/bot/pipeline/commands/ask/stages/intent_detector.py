"""
Intent Detector for ASK Pipeline

AI-powered intent classification to determine if a query needs data or is casual.
Uses the existing AI infrastructure with optimized prompts for fast classification.
"""

import time
import json
import asyncio
from typing import Optional
from dataclasses import dataclass

from src.shared.error_handling.logging import get_logger
from src.shared.ai_chat.ai_client import AIClientWrapper
from src.shared.ai_services.simple_model_config import get_model_id_for_job

logger = get_logger(__name__)

@dataclass
class IntentResult:
    """Result of intent detection"""
    intent: str  # "casual" or "data_needed"
    confidence: float  # 0.0 to 1.0
    reasoning: str
    execution_time: float = 0.0
    entities: dict = None  # Extracted entities (symbols, timeframes, etc.)

class IntentDetector:
    """
    AI-Powered Intent Detection Stage

    Uses AI to determine whether a query is:
    - "casual": General conversation, greetings, help requests, educational questions
    - "data_needed": Requires market data, analysis, or real-time information

    Features:
    - Fast AI classification (< 1 second)
    - High accuracy intent detection
    - Entity extraction (symbols, timeframes)
    - Confidence scoring
    - Caching for performance
    """

    def __init__(self):
        # Initialize AI client with fast model for intent classification
        try:
            # Create a simple context object for the AI client
            class IntentContext:
                def __init__(self):
                    self.pipeline_id = "ask_intent_detector"

            self.ai_client = AIClientWrapper(context=IntentContext())
            self.ai_available = True
            logger.info("✅ AI-powered intent detector initialized")
        except Exception as e:
            logger.warning(f"⚠️ AI client failed to initialize, using fallback: {e}")
            self.ai_client = None
            self.ai_available = False

        # Cache for recent intent classifications
        self.intent_cache = {}
        self.cache_ttl = 300  # 5 minutes

        # Fallback keywords for when AI is unavailable
        self.data_keywords = [
            'price', 'stock', 'ticker', 'chart', 'analysis', 'buy', 'sell',
            'market', 'trading', 'volume', 'earnings', 'news', 'forecast',
            'trend', 'support', 'resistance', 'technical', 'fundamental',
            'options', 'futures', 'crypto', 'bitcoin', 'ethereum'
        ]

        self.casual_keywords = [
            'hello', 'hi', 'thanks', 'thank you', 'help', 'how are you',
            'what can you do', 'explain', 'learn', 'tutorial', 'guide'
        ]
    
    async def detect(self, query: str, correlation_id: str) -> IntentResult:
        """
        Detect intent of user query using AI

        Args:
            query: User's question or request
            correlation_id: Request correlation ID

        Returns:
            IntentResult with AI-powered classification
        """
        start_time = time.time()

        logger.debug(f"Detecting intent for query", extra={
            'correlation_id': correlation_id,
            'query_length': len(query),
            'ai_available': self.ai_available
        })

        # Check cache first
        cache_key = self._get_cache_key(query)
        cached_result = self._get_cached_result(cache_key)
        if cached_result:
            logger.debug(f"Using cached intent result", extra={'correlation_id': correlation_id})
            return cached_result

        # Try AI classification first
        if self.ai_available:
            try:
                result = await self._detect_with_ai(query, correlation_id)
                if result:
                    # Cache the result
                    self._cache_result(cache_key, result)
                    return result
            except Exception as e:
                logger.warning(f"AI intent detection failed, using fallback: {e}", extra={
                    'correlation_id': correlation_id
                })

        # Fallback to keyword-based classification
        result = await self._detect_with_keywords(query, correlation_id)
        execution_time = time.time() - start_time
        result.execution_time = execution_time

        logger.info(f"Intent detection completed", extra={
            'correlation_id': correlation_id,
            'intent': result.intent,
            'confidence': result.confidence,
            'execution_time': execution_time,
            'method': 'ai' if self.ai_available else 'keywords'
        })

        return result

    async def _detect_with_ai(self, query: str, correlation_id: str) -> Optional[IntentResult]:
        """Detect intent using AI classification"""
        prompt = self._create_intent_prompt(query)

        try:
            # Call AI with timeout
            response = await asyncio.wait_for(
                self.ai_client.generate_response(prompt),
                timeout=15.0  # Reasonable timeout for classification
            )

            if not response:
                return None

            # Parse AI response
            return self._parse_ai_response(response, query)

        except asyncio.TimeoutError:
            logger.warning(f"AI intent detection timed out", extra={'correlation_id': correlation_id})
            return None
        except Exception as e:
            logger.error(f"AI intent detection error: {e}", extra={'correlation_id': correlation_id})
            return None
    
    def _create_intent_prompt(self, query: str) -> str:
        """Create optimized prompt for intent classification"""
        return f"""You are a financial trading assistant intent classifier. Analyze this user query and classify it as either "casual" or "data_needed".

CLASSIFICATION RULES:
- "casual": Greetings, help requests, general questions, educational content that doesn't need live data
- "data_needed": Stock prices, market analysis, trading decisions, news, charts, technical analysis

Query: "{query}"

Respond with ONLY a JSON object in this exact format:
{{"intent": "casual" or "data_needed", "confidence": 0.0-1.0, "reasoning": "brief explanation", "entities": {{"symbols": [], "timeframes": [], "indicators": []}}}}

Examples:
- "Hello, how are you?" → {{"intent": "casual", "confidence": 0.95, "reasoning": "greeting", "entities": {{"symbols": [], "timeframes": [], "indicators": []}}}}
- "What's AAPL price?" → {{"intent": "data_needed", "confidence": 0.98, "reasoning": "stock price request", "entities": {{"symbols": ["AAPL"], "timeframes": [], "indicators": []}}}}
- "How do I learn trading?" → {{"intent": "casual", "confidence": 0.90, "reasoning": "educational question", "entities": {{"symbols": [], "timeframes": [], "indicators": []}}}}

JSON only:"""

    def _parse_ai_response(self, response: str, query: str) -> Optional[IntentResult]:
        """Parse AI response into IntentResult"""
        try:
            # Extract JSON from response
            response_clean = response.strip()
            if response_clean.startswith('```json'):
                response_clean = response_clean[7:]
            if response_clean.endswith('```'):
                response_clean = response_clean[:-3]

            # Parse JSON
            data = json.loads(response_clean.strip())

            # Validate required fields
            if 'intent' not in data or data['intent'] not in ['casual', 'data_needed']:
                logger.warning(f"Invalid intent in AI response: {data.get('intent')}")
                return None

            return IntentResult(
                intent=data['intent'],
                confidence=float(data.get('confidence', 0.8)),
                reasoning=data.get('reasoning', 'AI classification'),
                entities=data.get('entities', {}),
                execution_time=0.0  # Will be set by caller
            )

        except (json.JSONDecodeError, KeyError, ValueError) as e:
            logger.warning(f"Failed to parse AI intent response: {e}")
            logger.debug(f"Raw AI response: {response}")
            return None

    async def _detect_with_keywords(self, query: str, correlation_id: str) -> IntentResult:
        """Fallback keyword-based classification"""
        query_lower = query.lower()

        # Count data-related keywords
        data_score = sum(1 for keyword in self.data_keywords if keyword in query_lower)

        # Count casual keywords
        casual_score = sum(1 for keyword in self.casual_keywords if keyword in query_lower)

        # Determine intent based on scores
        if data_score > casual_score:
            intent = "data_needed"
            confidence = min(0.8, 0.5 + (data_score * 0.1))
            reasoning = f"Found {data_score} data-related keywords"
        elif casual_score > 0:
            intent = "casual"
            confidence = min(0.8, 0.5 + (casual_score * 0.1))
            reasoning = f"Found {casual_score} casual keywords"
        else:
            # Default to data_needed for safety
            intent = "data_needed"
            confidence = 0.6
            reasoning = "No clear indicators, defaulting to data_needed for safety"

        return IntentResult(
            intent=intent,
            confidence=confidence,
            reasoning=reasoning,
            entities={}
        )

    def _get_cache_key(self, query: str) -> str:
        """Generate cache key for query"""
        import hashlib
        return hashlib.md5(query.lower().encode()).hexdigest()

    def _get_cached_result(self, cache_key: str) -> Optional[IntentResult]:
        """Get cached result if still valid"""
        if cache_key in self.intent_cache:
            cached_data, timestamp = self.intent_cache[cache_key]
            if time.time() - timestamp < self.cache_ttl:
                return cached_data
            else:
                # Remove expired cache entry
                del self.intent_cache[cache_key]
        return None

    def _cache_result(self, cache_key: str, result: IntentResult) -> None:
        """Cache intent result"""
        self.intent_cache[cache_key] = (result, time.time())

        # Simple cache cleanup - remove oldest entries if cache gets too large
        if len(self.intent_cache) > 100:
            oldest_key = min(self.intent_cache.keys(),
                           key=lambda k: self.intent_cache[k][1])
            del self.intent_cache[oldest_key]

    def get_supported_intents(self) -> list:
        """Get list of supported intent types"""
        return ["casual", "data_needed"]

    def get_cache_stats(self) -> dict:
        """Get cache statistics for monitoring"""
        return {
            'cache_size': len(self.intent_cache),
            'cache_ttl': self.cache_ttl,
            'ai_available': self.ai_available
        }
