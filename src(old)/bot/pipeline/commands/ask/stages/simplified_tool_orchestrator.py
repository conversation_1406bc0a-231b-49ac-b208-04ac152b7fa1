"""
Simplified Tool Orchestrator

A streamlined version of the tool orchestrator that uses modular components.
This replaces the monolithic 1,107-line tool_orchestrator.py
"""

import time
import asyncio
from typing import List, Dict, Any, Optional

from src.shared.error_handling.logging import get_logger
from .intent_detector import IntentResult
from .analysis_components import AnalysisCom<PERSON>, ToolResult, AnalysisDepth
from .data_collector import DataCollector
from .ai_synthesizer import AISynthesizer

logger = get_logger(__name__)

class SimplifiedToolOrchestrator:
    """
    Simplified Tool Orchestrator for ASK Pipeline
    
    Uses modular components to provide clean, maintainable tool orchestration.
    Replaces the monolithic 1,107-line tool_orchestrator.py
    """

    def __init__(self):
        """Initialize the simplified orchestrator with components"""
        self.analysis_components = AnalysisComponents()
        self.data_collector = DataCollector()
        self.ai_synthesizer = AISynthesizer()

        # Initialize unified MCP manager
        from ..tools import MCPManager
        self.mcp_manager = MCPManager()
        self._mcp_initialized = False

    async def execute(
        self, 
        query: str, 
        intent_result: IntentResult, 
        correlation_id: str = ""
    ) -> ToolResult:
        """
        Execute tool orchestration with simplified flow
        
        Args:
            query: User query
            intent_result: Intent detection result
            correlation_id: Request correlation ID
            
        Returns:
            ToolResult with analysis data
        """
        start_time = time.time()

        try:
            # Initialize MCP manager if not already done
            if not self._mcp_initialized:
                await self._initialize_mcp_manager(correlation_id)

            logger.info(f"Starting simplified tool orchestration", extra={
                'correlation_id': correlation_id,
                'query': query[:100],
                'intent': intent_result.intent
            })
            
            # Determine analysis depth
            analysis_depth = self._determine_analysis_depth(query, intent_result)
            
            # Create analysis plan
            analysis_plan = await self._create_analysis_plan(query, analysis_depth)
            
            # Execute analysis pipeline
            analysis_data = await self._execute_analysis_pipeline(
                query, intent_result, analysis_plan, correlation_id
            )
            
            # Calculate execution time
            execution_time = time.time() - start_time
            
            # Build result
            result = ToolResult(
                tools_used=analysis_data.get('tools_used', []),
                data=analysis_data,
                execution_time=execution_time,
                success=True,
                analysis_depth=analysis_depth.value,
                confidence_score=analysis_data.get('confidence_score', 0.0),
                fact_verified=analysis_data.get('fact_verified', False)
            )
            
            logger.info(f"Simplified tool orchestration completed", extra={
                'correlation_id': correlation_id,
                'execution_time': execution_time,
                'tools_used': result.tools_used,
                'confidence': result.confidence_score
            })
            
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Simplified tool orchestration failed", extra={
                'correlation_id': correlation_id,
                'execution_time': execution_time,
                'error': str(e)
            }, exc_info=True)
            
            return ToolResult(
                tools_used=[],
                data={},
                execution_time=execution_time,
                success=False,
                errors=[str(e)]
            )

    def _determine_analysis_depth(self, query: str, intent_result: IntentResult) -> AnalysisDepth:
        """Determine the depth of analysis needed"""
        try:
            # Simple heuristic for analysis depth
            query_lower = query.lower()
            
            # Comprehensive analysis for complex queries
            if any(keyword in query_lower for keyword in [
                'comprehensive', 'detailed', 'full analysis', 'deep dive',
                'technical analysis', 'fundamental analysis', 'complete'
            ]):
                return AnalysisDepth.COMPREHENSIVE
            
            # Intermediate analysis for specific requests
            elif any(keyword in query_lower for keyword in [
                'analyze', 'evaluate', 'assess', 'compare', 'trend',
                'prediction', 'forecast', 'outlook'
            ]):
                return AnalysisDepth.INTERMEDIATE
            
            # Basic analysis for simple queries
            else:
                return AnalysisDepth.BASIC
                
        except Exception as e:
            logger.warning(f"Error determining analysis depth: {e}")
            return AnalysisDepth.BASIC

    async def _create_analysis_plan(self, query: str, analysis_depth: AnalysisDepth) -> Dict[str, Any]:
        """Create analysis plan based on query and depth"""
        try:
            plan = {
                'query': query,
                'analysis_depth': analysis_depth.value,
                'query_type': self._classify_query_type(query),
                'required_data': self._determine_required_data(analysis_depth),
                'analysis_steps': self._plan_analysis_steps(analysis_depth)
            }
            
            return plan
            
        except Exception as e:
            logger.error(f"Error creating analysis plan: {e}")
            return {'query': query, 'analysis_depth': 'basic'}

    def _classify_query_type(self, query: str) -> str:
        """Classify the type of query"""
        query_lower = query.lower()
        
        if any(keyword in query_lower for keyword in ['price', 'cost', 'value', 'worth']):
            return 'price_inquiry'
        elif any(keyword in query_lower for keyword in ['trend', 'direction', 'movement']):
            return 'trend_analysis'
        elif any(keyword in query_lower for keyword in ['predict', 'forecast', 'outlook']):
            return 'prediction'
        elif any(keyword in query_lower for keyword in ['compare', 'vs', 'versus']):
            return 'comparison'
        else:
            return 'general'

    def _determine_required_data(self, analysis_depth: AnalysisDepth) -> List[str]:
        """Determine what data is required for analysis"""
        base_data = ['market_data']
        
        if analysis_depth == AnalysisDepth.BASIC:
            return base_data
        elif analysis_depth == AnalysisDepth.INTERMEDIATE:
            return base_data + ['technical_analysis']
        else:  # COMPREHENSIVE
            return base_data + ['technical_analysis', 'ml_predictions', 'mcp_insights']

    def _plan_analysis_steps(self, analysis_depth: AnalysisDepth) -> List[str]:
        """Plan the analysis steps"""
        steps = ['symbol_discovery', 'data_collection']
        
        if analysis_depth != AnalysisDepth.BASIC:
            steps.extend(['technical_analysis', 'ai_synthesis'])
        
        if analysis_depth == AnalysisDepth.COMPREHENSIVE:
            steps.extend(['ml_predictions', 'mcp_insights', 'fact_verification'])
        
        return steps

    async def _execute_analysis_pipeline(
        self, 
        query: str, 
        intent_result: IntentResult, 
        analysis_plan: Dict[str, Any], 
        correlation_id: str
    ) -> Dict[str, Any]:
        """Execute the analysis pipeline using modular components"""
        try:
            analysis_data = {
                'query': query,
                'analysis_plan': analysis_plan,
                'tools_used': [],
                'confidence_score': 0.0,
                'fact_verified': False
            }
            
            # Step 1: Symbol discovery
            discovery_result = await self.data_collector.discover_and_screen_tickers(
                query, analysis_plan
            )
            analysis_data['symbol_discovery'] = discovery_result
            analysis_data['tools_used'].append('symbol_discovery')
            
            symbols = discovery_result.get('symbols', [])
            if not symbols:
                logger.warning(f"No symbols found for query: {query}", extra={'correlation_id': correlation_id})
                return analysis_data
            
            # Step 2: Market data collection
            market_data = await self.data_collector.collect_market_data(
                symbols, intent_result.intent
            )
            analysis_data['market_data'] = market_data
            analysis_data['tools_used'].append('market_data_collection')
            
            # Step 3: Technical analysis (if needed)
            if 'technical_analysis' in analysis_plan.get('analysis_steps', []):
                technical_analysis = await self.analysis_components.perform_technical_analysis(
                    market_data
                )
                analysis_data['technical_analysis'] = technical_analysis
                analysis_data['tools_used'].append('technical_analysis')
            
            # Step 4: ML predictions (if needed)
            if 'ml_predictions' in analysis_plan.get('analysis_steps', []):
                ml_predictions = await self.analysis_components.perform_ml_predictions(
                    market_data, analysis_data.get('technical_analysis', {})
                )
                analysis_data['ml_predictions'] = ml_predictions
                analysis_data['tools_used'].append('ml_predictions')
            
            # Step 5: MCP insights (if needed)
            if 'mcp_insights' in analysis_plan.get('analysis_steps', []):
                mcp_insights = await self.data_collector.get_mcp_insights(
                    query, symbols, intent_result
                )
                analysis_data['mcp_insights'] = mcp_insights
                analysis_data['tools_used'].append('mcp_insights')
            
            # Step 6: AI synthesis
            if 'ai_synthesis' in analysis_plan.get('analysis_steps', []):
                synthesis_result = await self.ai_synthesizer.perform_ai_synthesis(
                    query, analysis_data
                )
                analysis_data['ai_synthesis'] = synthesis_result
                analysis_data['tools_used'].append('ai_synthesis')
            
            # Step 7: Fact verification (if needed)
            if 'fact_verification' in analysis_plan.get('analysis_steps', []):
                verification_result = await self.ai_synthesizer.verify_facts(analysis_data)
                analysis_data['fact_verification'] = verification_result
                analysis_data['fact_verified'] = verification_result.get('facts_verified', False)
                analysis_data['tools_used'].append('fact_verification')
            
            # Calculate overall confidence
            analysis_data['confidence_score'] = self.analysis_components.calculate_confidence_score(analysis_data)
            
            return analysis_data
            
        except Exception as e:
            logger.error(f"Error in analysis pipeline: {e}", extra={'correlation_id': correlation_id})
            analysis_data['error'] = str(e)
            return analysis_data

    async def execute_simple_analysis(
        self, 
        query: str, 
        intent_result: IntentResult, 
        correlation_id: str = ""
    ) -> ToolResult:
        """Execute simple analysis for basic queries"""
        try:
            start_time = time.time()
            
            # Extract symbols
            symbols = await self.data_collector.extract_symbols_from_query(query)
            
            if not symbols:
                return ToolResult(
                    tools_used=['symbol_extraction'],
                    data={'query': query, 'symbols': []},
                    execution_time=time.time() - start_time,
                    success=True,
                    analysis_depth='basic'
                )
            
            # Collect basic market data
            market_data = await self.data_collector.collect_market_data(symbols, intent_result.intent)
            
            execution_time = time.time() - start_time
            
            return ToolResult(
                tools_used=['symbol_extraction', 'market_data'],
                data={
                    'query': query,
                    'symbols': symbols,
                    'market_data': market_data
                },
                execution_time=execution_time,
                success=True,
                analysis_depth='basic',
                confidence_score=0.8
            )
            
        except Exception as e:
            logger.error(f"Error in simple analysis: {e}", extra={'correlation_id': correlation_id})
            return ToolResult(
                tools_used=[],
                data={'error': str(e)},
                execution_time=time.time() - start_time,
                success=False,
                errors=[str(e)]
            )

    async def _initialize_mcp_manager(self, correlation_id: str = ""):
        """Initialize the unified MCP manager"""
        try:
            logger.info("Initializing unified MCP manager...", extra={'correlation_id': correlation_id})

            # Initialize all MCP clients
            initialization_status = await self.mcp_manager.initialize_all_clients()

            # Log initialization results
            successful_clients = [client for client, status in initialization_status.items() if status]
            failed_clients = [client for client, status in initialization_status.items() if not status]

            if successful_clients:
                logger.info(f"✅ MCP clients initialized: {', '.join(successful_clients)}",
                           extra={'correlation_id': correlation_id})

            if failed_clients:
                logger.warning(f"⚠️ MCP clients failed: {', '.join(failed_clients)}",
                              extra={'correlation_id': correlation_id})

            # Get performance metrics
            metrics = self.mcp_manager.get_performance_metrics()
            logger.info(f"📊 MCP Manager: {metrics['total_tools_available']} tools available",
                       extra={'correlation_id': correlation_id})

            self._mcp_initialized = True

        except Exception as e:
            logger.error(f"Failed to initialize MCP manager: {e}", extra={'correlation_id': correlation_id})
            self._mcp_initialized = False

    def get_mcp_performance_metrics(self) -> Dict[str, Any]:
        """Get MCP manager performance metrics"""
        if self._mcp_initialized:
            return self.mcp_manager.get_performance_metrics()
        else:
            return {'error': 'MCP manager not initialized'}

    def get_available_tools_info(self) -> Dict[str, Any]:
        """Get information about available tools"""
        if self._mcp_initialized:
            return self.mcp_manager.get_available_tools_info()
        else:
            return {'error': 'MCP manager not initialized'}
