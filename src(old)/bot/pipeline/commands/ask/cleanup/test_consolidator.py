"""
Test Consolidator for ASK Pipeline

Provides tools for analyzing and consolidating test files:
- Analyze 15+ overlapping test files and identify duplicates
- Identify duplicate test scenarios and outdated test data
- Standardize test naming conventions and organization
- Create test suite documentation and execution guides
- Implement test maintenance and update procedures

NOTE: This tool only ANALYZES and REPORTS - it does NOT delete or modify anything
"""

import os
import re
import logging
from typing import Dict, Any, Optional, List, Set, Tuple
from dataclasses import dataclass, field
from datetime import datetime
from pathlib import Path
import json
import ast

logger = logging.getLogger(__name__)

@dataclass
class TestFile:
    """Test file information"""
    file_path: str
    test_functions: List[str] = field(default_factory=list)
    test_classes: List[str] = field(default_factory=list)
    imports: List[str] = field(default_factory=list)
    fixtures: List[str] = field(default_factory=list)
    test_data: List[str] = field(default_factory=list)
    dependencies: List[str] = field(default_factory=list)
    file_size: int = 0
    line_count: int = 0
    last_modified: datetime = field(default_factory=datetime.utcnow)

@dataclass
class TestScenario:
    """Test scenario information"""
    name: str
    file_path: str
    function_name: str
    description: str
    test_type: str  # unit, integration, e2e, performance
    dependencies: List[str] = field(default_factory=list)
    data_requirements: List[str] = field(default_factory=list)
    expected_duration: Optional[float] = None
    tags: List[str] = field(default_factory=list)

@dataclass
class DuplicateTest:
    """Duplicate test information"""
    test_name: str
    files: List[str]
    similarity_score: float
    differences: List[str] = field(default_factory=list)
    suggested_action: str = ""

@dataclass
class TestConsolidationReport:
    """Test consolidation analysis report"""
    total_test_files: int
    total_test_functions: int
    duplicate_tests: List[DuplicateTest] = field(default_factory=list)
    overlapping_files: List[Tuple[str, str, float]] = field(default_factory=list)
    outdated_tests: List[str] = field(default_factory=list)
    naming_inconsistencies: List[Dict[str, Any]] = field(default_factory=list)
    consolidation_recommendations: List[Dict[str, Any]] = field(default_factory=list)
    analysis_timestamp: datetime = field(default_factory=datetime.utcnow)

class TestConsolidator:
    """Test consolidation analysis system"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.test_files: Dict[str, TestFile] = {}
        self.test_scenarios: List[TestScenario] = []
        self.duplicate_tests: List[DuplicateTest] = []
        self.analysis_results: Optional[TestConsolidationReport] = None
        
    def analyze_test_files(self, test_patterns: List[str] = None) -> TestConsolidationReport:
        """Analyze all test files in the project"""
        logger.info("Starting test file analysis")
        
        if test_patterns is None:
            test_patterns = [
                "**/test_*.py",
                "**/*_test.py",
                "**/tests/**/*.py"
            ]
        
        # Find all test files
        test_file_paths = []
        for pattern in test_patterns:
            for file_path in self.project_root.glob(pattern):
                if file_path.is_file() and file_path.suffix == '.py':
                    test_file_paths.append(file_path)
        
        logger.info(f"Found {len(test_file_paths)} test files to analyze")
        
        # Analyze each test file
        for file_path in test_file_paths:
            try:
                self._analyze_test_file(file_path)
            except Exception as e:
                logger.error(f"Error analyzing test file {file_path}: {e}")
        
        # Identify duplicates and overlaps
        self._identify_duplicate_tests()
        self._identify_overlapping_files()
        self._identify_outdated_tests()
        self._identify_naming_inconsistencies()
        
        # Generate consolidation recommendations
        self._generate_consolidation_recommendations()
        
        # Create report
        self.analysis_results = TestConsolidationReport(
            total_test_files=len(self.test_files),
            total_test_functions=sum(len(tf.test_functions) for tf in self.test_files.values()),
            duplicate_tests=self.duplicate_tests,
            overlapping_files=self._get_overlapping_files(),
            outdated_tests=self._get_outdated_tests(),
            naming_inconsistencies=self._get_naming_inconsistencies(),
            consolidation_recommendations=self._get_consolidation_recommendations()
        )
        
        logger.info("Test file analysis completed")
        return self.analysis_results
    
    def _analyze_test_file(self, file_path: Path):
        """Analyze a single test file"""
        logger.debug(f"Analyzing test file: {file_path}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Parse AST
            tree = ast.parse(content)
            
            # Create test file info
            test_file = TestFile(
                file_path=str(file_path),
                file_size=file_path.stat().st_size,
                line_count=len(content.split('\n')),
                last_modified=datetime.fromtimestamp(file_path.stat().st_mtime)
            )
            
            # Extract test functions
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    if node.name.startswith('test_'):
                        test_file.test_functions.append(node.name)
                        
                        # Create test scenario
                        scenario = TestScenario(
                            name=node.name,
                            file_path=str(file_path),
                            function_name=node.name,
                            description=self._extract_docstring(node),
                            test_type=self._determine_test_type(node, content),
                            dependencies=self._extract_dependencies(node),
                            data_requirements=self._extract_data_requirements(node),
                            tags=self._extract_tags(node, content)
                        )
                        self.test_scenarios.append(scenario)
                
                elif isinstance(node, ast.ClassDef):
                    if node.name.startswith('Test'):
                        test_file.test_classes.append(node.name)
                        
                        # Extract test methods from class
                        for method in node.body:
                            if isinstance(method, ast.FunctionDef) and method.name.startswith('test_'):
                                test_file.test_functions.append(f"{node.name}.{method.name}")
                                
                                scenario = TestScenario(
                                    name=f"{node.name}.{method.name}",
                                    file_path=str(file_path),
                                    function_name=method.name,
                                    description=self._extract_docstring(method),
                                    test_type=self._determine_test_type(method, content),
                                    dependencies=self._extract_dependencies(method),
                                    data_requirements=self._extract_data_requirements(method),
                                    tags=self._extract_tags(method, content)
                                )
                                self.test_scenarios.append(scenario)
                
                elif isinstance(node, (ast.Import, ast.ImportFrom)):
                    if isinstance(node, ast.Import):
                        for alias in node.names:
                            test_file.imports.append(alias.name)
                    else:
                        if node.module:
                            test_file.imports.append(node.module)
                        for alias in node.names:
                            test_file.imports.append(alias.name)
            
            # Extract fixtures
            test_file.fixtures = self._extract_fixtures(content)
            
            # Extract test data
            test_file.test_data = self._extract_test_data(content)
            
            # Store test file info
            self.test_files[str(file_path)] = test_file
            
        except Exception as e:
            logger.error(f"Error analyzing test file {file_path}: {e}")
    
    def _extract_docstring(self, node: ast.FunctionDef) -> str:
        """Extract docstring from function"""
        if (node.body and isinstance(node.body[0], ast.Expr) and 
            isinstance(node.body[0].value, ast.Constant) and 
            isinstance(node.body[0].value.value, str)):
            return node.body[0].value.value.strip()
        return ""
    
    def _determine_test_type(self, node: ast.FunctionDef, content: str) -> str:
        """Determine test type based on function content and context"""
        function_content = ast.get_source_segment(content, node) or ""
        
        if 'integration' in function_content.lower() or 'integration' in node.name.lower():
            return 'integration'
        elif 'e2e' in function_content.lower() or 'end_to_end' in function_content.lower():
            return 'e2e'
        elif 'performance' in function_content.lower() or 'benchmark' in function_content.lower():
            return 'performance'
        else:
            return 'unit'
    
    def _extract_dependencies(self, node: ast.FunctionDef) -> List[str]:
        """Extract test dependencies from function"""
        dependencies = []
        
        for decorator in node.decorator_list:
            if isinstance(decorator, ast.Name):
                if decorator.id in ['pytest.mark.skipif', 'pytest.mark.parametrize']:
                    dependencies.append(decorator.id)
            elif isinstance(decorator, ast.Call):
                if isinstance(decorator.func, ast.Attribute):
                    if decorator.func.attr in ['skipif', 'parametrize']:
                        dependencies.append(decorator.func.attr)
        
        return dependencies
    
    def _extract_data_requirements(self, node: ast.FunctionDef) -> List[str]:
        """Extract test data requirements from function"""
        # This is a simplified implementation
        # In practice, you'd analyze the function body for data usage
        return []
    
    def _extract_tags(self, node: ast.FunctionDef, content: str) -> List[str]:
        """Extract test tags from function"""
        tags = []
        
        for decorator in node.decorator_list:
            if isinstance(decorator, ast.Call):
                if isinstance(decorator.func, ast.Attribute):
                    if decorator.func.attr == 'mark':
                        for arg in decorator.args:
                            if isinstance(arg, ast.Name):
                                tags.append(arg.id)
        
        return tags
    
    def _extract_fixtures(self, content: str) -> List[str]:
        """Extract pytest fixtures from content"""
        fixtures = []
        
        # Look for @pytest.fixture decorators
        fixture_pattern = r'@pytest\.fixture\s*\n\s*def\s+(\w+)'
        matches = re.findall(fixture_pattern, content)
        fixtures.extend(matches)
        
        # Look for conftest.py fixtures
        conftest_pattern = r'def\s+(\w+)\s*\([^)]*\)\s*:'
        matches = re.findall(conftest_pattern, content)
        fixtures.extend(matches)
        
        return list(set(fixtures))
    
    def _extract_test_data(self, content: str) -> List[str]:
        """Extract test data from content"""
        test_data = []
        
        # Look for test data patterns
        data_patterns = [
            r'test_data\s*=\s*\[(.*?)\]',
            r'TEST_DATA\s*=\s*\[(.*?)\]',
            r'@pytest\.mark\.parametrize\s*\([^)]*\)',
        ]
        
        for pattern in data_patterns:
            matches = re.findall(pattern, content, re.DOTALL)
            test_data.extend(matches)
        
        return test_data
    
    def _identify_duplicate_tests(self):
        """Identify duplicate test functions"""
        logger.info("Identifying duplicate tests")
        
        # Group tests by name
        test_groups = {}
        for scenario in self.test_scenarios:
            if scenario.name not in test_groups:
                test_groups[scenario.name] = []
            test_groups[scenario.name].append(scenario)
        
        # Find duplicates
        for test_name, scenarios in test_groups.items():
            if len(scenarios) > 1:
                files = [s.file_path for s in scenarios]
                similarity_score = self._calculate_similarity(scenarios)
                
                duplicate = DuplicateTest(
                    test_name=test_name,
                    files=files,
                    similarity_score=similarity_score,
                    suggested_action=f"Consider consolidating {len(scenarios)} duplicate tests"
                )
                
                self.duplicate_tests.append(duplicate)
    
    def _calculate_similarity(self, scenarios: List[TestScenario]) -> float:
        """Calculate similarity between test scenarios"""
        if len(scenarios) < 2:
            return 0.0
        
        # Simple similarity calculation based on description and tags
        descriptions = [s.description for s in scenarios]
        tags_sets = [set(s.tags) for s in scenarios]
        
        # Calculate description similarity
        desc_similarity = 0.0
        if all(descriptions):
            desc_similarity = len(set(descriptions)) / len(descriptions)
        
        # Calculate tags similarity
        tags_similarity = 0.0
        if all(tags_sets):
            common_tags = set.intersection(*tags_sets)
            all_tags = set.union(*tags_sets)
            tags_similarity = len(common_tags) / len(all_tags) if all_tags else 0.0
        
        return (desc_similarity + tags_similarity) / 2
    
    def _identify_overlapping_files(self):
        """Identify files with overlapping test coverage"""
        logger.info("Identifying overlapping test files")
        
        # This would analyze test coverage overlap between files
        # For now, we'll use a simple heuristic based on test names
        pass
    
    def _identify_outdated_tests(self):
        """Identify outdated tests"""
        logger.info("Identifying outdated tests")
        
        # Look for tests that haven't been updated recently
        # or that test deprecated functionality
        pass
    
    def _identify_naming_inconsistencies(self):
        """Identify naming inconsistencies"""
        logger.info("Identifying naming inconsistencies")
        
        # Analyze test naming patterns
        naming_patterns = {}
        
        for scenario in self.test_scenarios:
            # Extract naming pattern
            if scenario.name.startswith('test_'):
                pattern = 'test_*'
            elif scenario.name.startswith('Test'):
                pattern = 'Test*'
            else:
                pattern = 'other'
            
            if pattern not in naming_patterns:
                naming_patterns[pattern] = []
            naming_patterns[pattern].append(scenario.name)
        
        # Report inconsistencies
        if len(naming_patterns) > 1:
            logger.warning(f"Found {len(naming_patterns)} different naming patterns")
    
    def _generate_consolidation_recommendations(self):
        """Generate consolidation recommendations"""
        logger.info("Generating consolidation recommendations")
        
        recommendations = []
        
        # Recommend consolidating duplicate tests
        if self.duplicate_tests:
            recommendations.append({
                "type": "consolidate_duplicates",
                "description": f"Found {len(self.duplicate_tests)} duplicate test functions",
                "action": "Review and consolidate duplicate tests",
                "priority": "high"
            })
        
        # Recommend standardizing naming
        recommendations.append({
            "type": "standardize_naming",
            "description": "Standardize test naming conventions",
            "action": "Use consistent naming patterns across all test files",
            "priority": "medium"
        })
        
        # Recommend organizing by test type
        recommendations.append({
            "type": "organize_by_type",
            "description": "Organize tests by type (unit, integration, e2e)",
            "action": "Create separate directories for different test types",
            "priority": "medium"
        })
        
        return recommendations
    
    def _get_overlapping_files(self) -> List[Tuple[str, str, float]]:
        """Get overlapping files with similarity scores"""
        # This would return files with overlapping test coverage
        return []
    
    def _get_outdated_tests(self) -> List[str]:
        """Get list of outdated tests"""
        # This would return tests that are outdated
        return []
    
    def _get_naming_inconsistencies(self) -> List[Dict[str, Any]]:
        """Get naming inconsistencies"""
        # This would return naming inconsistency details
        return []
    
    def _get_consolidation_recommendations(self) -> List[Dict[str, Any]]:
        """Get consolidation recommendations"""
        return self._generate_consolidation_recommendations()
    
    def generate_test_documentation(self, output_file: str) -> bool:
        """Generate test suite documentation"""
        try:
            if not self.analysis_results:
                logger.error("No analysis results available. Run analyze_test_files() first.")
                return False
            
            doc_content = f"""# Test Suite Documentation

Generated on: {self.analysis_results.analysis_timestamp.strftime('%Y-%m-%d %H:%M:%S')}

## Overview

This document provides an overview of the test suite for the ASK Pipeline project.

## Test Statistics

- **Total Test Files**: {self.analysis_results.total_test_files}
- **Total Test Functions**: {self.analysis_results.total_test_functions}
- **Duplicate Tests**: {len(self.analysis_results.duplicate_tests)}
- **Overlapping Files**: {len(self.analysis_results.overlapping_files)}

## Test Files

"""
            
            # Add test file information
            for file_path, test_file in self.test_files.items():
                doc_content += f"""
### {file_path}

- **Test Functions**: {len(test_file.test_functions)}
- **Test Classes**: {len(test_file.test_classes)}
- **File Size**: {test_file.file_size} bytes
- **Line Count**: {test_file.line_count}
- **Last Modified**: {test_file.last_modified.strftime('%Y-%m-%d %H:%M:%S')}

#### Test Functions
{chr(10).join(f"- {func}" for func in test_file.test_functions)}

#### Imports
{chr(10).join(f"- {imp}" for imp in test_file.imports)}

"""
            
            # Add duplicate tests section
            if self.analysis_results.duplicate_tests:
                doc_content += """
## Duplicate Tests

The following tests appear in multiple files and should be consolidated:

"""
                for duplicate in self.analysis_results.duplicate_tests:
                    doc_content += f"""
### {duplicate.test_name}

- **Files**: {', '.join(duplicate.files)}
- **Similarity Score**: {duplicate.similarity_score:.2f}
- **Suggested Action**: {duplicate.suggested_action}

"""
            
            # Add recommendations section
            if self.analysis_results.consolidation_recommendations:
                doc_content += """
## Consolidation Recommendations

"""
                for rec in self.analysis_results.consolidation_recommendations:
                    doc_content += f"""
### {rec['type'].replace('_', ' ').title()}

- **Description**: {rec['description']}
- **Action**: {rec['action']}
- **Priority**: {rec['priority']}

"""
            
            # Write to file
            with open(output_file, 'w') as f:
                f.write(doc_content)
            
            logger.info(f"Test documentation generated: {output_file}")
            return True
            
        except Exception as e:
            logger.error(f"Error generating test documentation: {e}")
            return False
    
    def export_analysis_report(self, output_file: str) -> bool:
        """Export analysis report to JSON file"""
        try:
            if not self.analysis_results:
                logger.error("No analysis results available. Run analyze_test_files() first.")
                return False
            
            report_data = {
                "analysis_timestamp": self.analysis_results.analysis_timestamp.isoformat(),
                "total_test_files": self.analysis_results.total_test_files,
                "total_test_functions": self.analysis_results.total_test_functions,
                "duplicate_tests": [
                    {
                        "test_name": dt.test_name,
                        "files": dt.files,
                        "similarity_score": dt.similarity_score,
                        "suggested_action": dt.suggested_action
                    }
                    for dt in self.analysis_results.duplicate_tests
                ],
                "test_files": {
                    file_path: {
                        "test_functions": tf.test_functions,
                        "test_classes": tf.test_classes,
                        "imports": tf.imports,
                        "fixtures": tf.fixtures,
                        "test_data": tf.test_data,
                        "file_size": tf.file_size,
                        "line_count": tf.line_count,
                        "last_modified": tf.last_modified.isoformat()
                    }
                    for file_path, tf in self.test_files.items()
                },
                "consolidation_recommendations": self.analysis_results.consolidation_recommendations
            }
            
            with open(output_file, 'w') as f:
                json.dump(report_data, f, indent=2, default=str)
            
            logger.info(f"Test consolidation report exported: {output_file}")
            return True
            
        except Exception as e:
            logger.error(f"Error exporting analysis report: {e}")
            return False

# Global instance
_test_consolidator: Optional[TestConsolidator] = None

def get_test_consolidator() -> TestConsolidator:
    """Get global test consolidator"""
    global _test_consolidator
    if _test_consolidator is None:
        _test_consolidator = TestConsolidator()
    return _test_consolidator

def cleanup_test_consolidator():
    """Cleanup global test consolidator"""
    global _test_consolidator
    if _test_consolidator:
        _test_consolidator = None
