"""
Dead Code Analyzer for ASK Pipeline

Provides analysis tools for identifying dead code:
- Identify and analyze unused imports and functions across all ask-related files
- Analyze obsolete test files and duplicate implementations
- Identify commented-out code and temporary debugging statements
- Analyze unused configuration options and environment variables
- Create code cleanup validation and prevention tools

NOTE: This tool only ANALYZES and REPORTS - it does NOT delete anything
"""

import ast
import os
import re
import logging
from typing import Dict, Any, Optional, List, Set, Tuple
from dataclasses import dataclass, field
from datetime import datetime
from pathlib import Path
import json

logger = logging.getLogger(__name__)

@dataclass
class DeadCodeReport:
    """Dead code analysis report"""
    file_path: str
    unused_imports: List[str] = field(default_factory=list)
    unused_functions: List[str] = field(default_factory=list)
    unused_classes: List[str] = field(default_factory=list)
    unused_variables: List[str] = field(default_factory=list)
    commented_code: List[Dict[str, Any]] = field(default_factory=list)
    debug_statements: List[Dict[str, Any]] = field(default_factory=list)
    todo_comments: List[Dict[str, Any]] = field(default_factory=list)
    duplicate_code: List[Dict[str, Any]] = field(default_factory=list)
    analysis_timestamp: datetime = field(default_factory=datetime.utcnow)

@dataclass
class CodeMetrics:
    """Code metrics for a file"""
    file_path: str
    total_lines: int
    code_lines: int
    comment_lines: int
    blank_lines: int
    function_count: int
    class_count: int
    import_count: int
    complexity_score: float
    maintainability_index: float

class DeadCodeAnalyzer:
    """Dead code analysis system"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.analysis_results: Dict[str, DeadCodeReport] = {}
        self.code_metrics: Dict[str, CodeMetrics] = {}
        self.import_graph: Dict[str, Set[str]] = {}
        self.usage_graph: Dict[str, Set[str]] = {}
        
    def analyze_project(self, include_patterns: List[str] = None, 
                       exclude_patterns: List[str] = None) -> Dict[str, Any]:
        """Analyze entire project for dead code"""
        logger.info("Starting dead code analysis")
        
        # Default patterns
        if include_patterns is None:
            include_patterns = ["**/*.py"]
        
        if exclude_patterns is None:
            exclude_patterns = [
                "**/__pycache__/**",
                "**/venv/**",
                "**/env/**",
                "**/.git/**",
                "**/node_modules/**",
                "**/build/**",
                "**/dist/**"
            ]
        
        # Find all Python files
        python_files = []
        for pattern in include_patterns:
            for file_path in self.project_root.glob(pattern):
                if file_path.is_file() and file_path.suffix == '.py':
                    # Check if file should be excluded
                    should_exclude = False
                    for exclude_pattern in exclude_patterns:
                        if file_path.match(exclude_pattern):
                            should_exclude = True
                            break
                    
                    if not should_exclude:
                        python_files.append(file_path)
        
        logger.info(f"Found {len(python_files)} Python files to analyze")
        
        # Analyze each file
        for file_path in python_files:
            try:
                self.analyze_file(file_path)
            except Exception as e:
                logger.error(f"Error analyzing {file_path}: {e}")
        
        # Build usage graph
        self._build_usage_graph()
        
        # Generate summary report
        summary = self._generate_summary_report()
        
        logger.info("Dead code analysis completed")
        return summary
    
    def analyze_file(self, file_path: Path) -> DeadCodeReport:
        """Analyze a single file for dead code"""
        logger.debug(f"Analyzing file: {file_path}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Parse AST
            tree = ast.parse(content)
            
            # Create report
            report = DeadCodeReport(file_path=str(file_path))
            
            # Analyze different types of dead code
            self._analyze_unused_imports(tree, report, content)
            self._analyze_unused_functions(tree, report, content)
            self._analyze_unused_classes(tree, report, content)
            self._analyze_unused_variables(tree, report, content)
            self._analyze_commented_code(content, report)
            self._analyze_debug_statements(content, report)
            self._analyze_todo_comments(content, report)
            
            # Calculate code metrics
            metrics = self._calculate_code_metrics(file_path, content, tree)
            self.code_metrics[str(file_path)] = metrics
            
            # Store report
            self.analysis_results[str(file_path)] = report
            
            return report
            
        except Exception as e:
            logger.error(f"Error analyzing file {file_path}: {e}")
            return DeadCodeReport(file_path=str(file_path))
    
    def _analyze_unused_imports(self, tree: ast.AST, report: DeadCodeReport, content: str):
        """Analyze unused imports"""
        # Get all import statements
        imports = []
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    imports.append(alias.name.split('.')[0])
            elif isinstance(node, ast.ImportFrom):
                if node.module:
                    imports.append(node.module.split('.')[0])
                for alias in node.names:
                    imports.append(alias.name)
        
        # Check which imports are actually used
        for import_name in imports:
            # Simple check - look for import name in content
            # This is a basic implementation - could be improved with AST analysis
            if not self._is_import_used(import_name, content):
                report.unused_imports.append(import_name)
    
    def _analyze_unused_functions(self, tree: ast.AST, report: DeadCodeReport, content: str):
        """Analyze unused functions"""
        # Get all function definitions
        functions = []
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                functions.append(node.name)
        
        # Check which functions are actually used
        for func_name in functions:
            if not self._is_function_used(func_name, content):
                report.unused_functions.append(func_name)
    
    def _analyze_unused_classes(self, tree: ast.AST, report: DeadCodeReport, content: str):
        """Analyze unused classes"""
        # Get all class definitions
        classes = []
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef):
                classes.append(node.name)
        
        # Check which classes are actually used
        for class_name in classes:
            if not self._is_class_used(class_name, content):
                report.unused_classes.append(class_name)
    
    def _analyze_unused_variables(self, tree: ast.AST, report: DeadCodeReport, content: str):
        """Analyze unused variables"""
        # This is a simplified implementation
        # In practice, you'd need more sophisticated analysis
        pass
    
    def _analyze_commented_code(self, content: str, report: DeadCodeReport):
        """Analyze commented-out code"""
        lines = content.split('\n')
        
        for i, line in enumerate(lines, 1):
            stripped = line.strip()
            
            # Look for commented code patterns
            if stripped.startswith('#') and len(stripped) > 1:
                # Check if it looks like code (not just a comment)
                code_part = stripped[1:].strip()
                
                # Simple heuristics to identify commented code
                if (code_part.startswith(('def ', 'class ', 'import ', 'from ', 'if ', 'for ', 'while ', 'try:', 'except:', 'with ')) or
                    (code_part.endswith(':') and not code_part.startswith('#')) or
                    ('=' in code_part and not code_part.startswith('#'))):
                    
                    report.commented_code.append({
                        'line_number': i,
                        'content': stripped,
                        'suggested_action': 'Review if this code should be removed or uncommented'
                    })
    
    def _analyze_debug_statements(self, content: str, report: DeadCodeReport):
        """Analyze debug statements"""
        lines = content.split('\n')
        
        debug_patterns = [
            r'print\s*\(',  # print statements
            r'console\.log\s*\(',  # console.log (if any JS mixed in)
            r'debugger',  # debugger statements
            r'pdb\.set_trace\(\)',  # pdb breakpoints
            r'breakpoint\(\)',  # Python 3.7+ breakpoints
            r'import pdb',  # pdb imports
            r'logging\.debug\s*\(',  # debug logging
        ]
        
        for i, line in enumerate(lines, 1):
            for pattern in debug_patterns:
                if re.search(pattern, line, re.IGNORECASE):
                    report.debug_statements.append({
                        'line_number': i,
                        'content': line.strip(),
                        'pattern': pattern,
                        'suggested_action': 'Remove or replace with proper logging'
                    })
    
    def _analyze_todo_comments(self, content: str, report: DeadCodeReport):
        """Analyze TODO comments"""
        lines = content.split('\n')
        
        todo_patterns = [
            r'TODO:',
            r'FIXME:',
            r'XXX:',
            r'HACK:',
            r'NOTE:',
            r'BUG:',
        ]
        
        for i, line in enumerate(lines, 1):
            for pattern in todo_patterns:
                if re.search(pattern, line, re.IGNORECASE):
                    report.todo_comments.append({
                        'line_number': i,
                        'content': line.strip(),
                        'pattern': pattern,
                        'suggested_action': 'Address the TODO item or remove if no longer relevant'
                    })
    
    def _is_import_used(self, import_name: str, content: str) -> bool:
        """Check if an import is used in the content"""
        # Simple check - look for import name in content
        # This could be improved with more sophisticated AST analysis
        return import_name in content
    
    def _is_function_used(self, func_name: str, content: str) -> bool:
        """Check if a function is used in the content"""
        # Simple check - look for function name in content
        # This could be improved with more sophisticated AST analysis
        return func_name in content
    
    def _is_class_used(self, class_name: str, content: str) -> bool:
        """Check if a class is used in the content"""
        # Simple check - look for class name in content
        # This could be improved with more sophisticated AST analysis
        return class_name in content
    
    def _calculate_code_metrics(self, file_path: Path, content: str, tree: ast.AST) -> CodeMetrics:
        """Calculate code metrics for a file"""
        lines = content.split('\n')
        total_lines = len(lines)
        
        code_lines = 0
        comment_lines = 0
        blank_lines = 0
        
        for line in lines:
            stripped = line.strip()
            if not stripped:
                blank_lines += 1
            elif stripped.startswith('#'):
                comment_lines += 1
            else:
                code_lines += 1
        
        # Count functions and classes
        function_count = len([node for node in ast.walk(tree) if isinstance(node, ast.FunctionDef)])
        class_count = len([node for node in ast.walk(tree) if isinstance(node, ast.ClassDef)])
        import_count = len([node for node in ast.walk(tree) if isinstance(node, (ast.Import, ast.ImportFrom))])
        
        # Calculate complexity (simplified)
        complexity_score = self._calculate_complexity(tree)
        
        # Calculate maintainability index (simplified)
        maintainability_index = max(0, 171 - 5.2 * complexity_score - 0.23 * total_lines)
        
        return CodeMetrics(
            file_path=str(file_path),
            total_lines=total_lines,
            code_lines=code_lines,
            comment_lines=comment_lines,
            blank_lines=blank_lines,
            function_count=function_count,
            class_count=class_count,
            import_count=import_count,
            complexity_score=complexity_score,
            maintainability_index=maintainability_index
        )
    
    def _calculate_complexity(self, tree: ast.AST) -> float:
        """Calculate cyclomatic complexity"""
        complexity = 1  # Base complexity
        
        for node in ast.walk(tree):
            if isinstance(node, (ast.If, ast.While, ast.For, ast.AsyncFor, ast.ExceptHandler, ast.With, ast.AsyncWith)):
                complexity += 1
            elif isinstance(node, ast.BoolOp):
                complexity += len(node.values) - 1
        
        return complexity
    
    def _build_usage_graph(self):
        """Build usage graph for imports and functions"""
        # This would build a graph of how imports and functions are used
        # across the project to identify truly unused code
        pass
    
    def _generate_summary_report(self) -> Dict[str, Any]:
        """Generate summary report of dead code analysis"""
        total_files = len(self.analysis_results)
        files_with_issues = len([r for r in self.analysis_results.values() 
                                if any([r.unused_imports, r.unused_functions, r.unused_classes, 
                                       r.commented_code, r.debug_statements, r.todo_comments])])
        
        total_unused_imports = sum(len(r.unused_imports) for r in self.analysis_results.values())
        total_unused_functions = sum(len(r.unused_functions) for r in self.analysis_results.values())
        total_unused_classes = sum(len(r.unused_classes) for r in self.analysis_results.values())
        total_commented_code = sum(len(r.commented_code) for r in self.analysis_results.values())
        total_debug_statements = sum(len(r.debug_statements) for r in self.analysis_results.values())
        total_todo_comments = sum(len(r.todo_comments) for r in self.analysis_results.values())
        
        return {
            "analysis_timestamp": datetime.utcnow().isoformat(),
            "total_files_analyzed": total_files,
            "files_with_issues": files_with_issues,
            "summary": {
                "unused_imports": total_unused_imports,
                "unused_functions": total_unused_functions,
                "unused_classes": total_unused_classes,
                "commented_code": total_commented_code,
                "debug_statements": total_debug_statements,
                "todo_comments": total_todo_comments
            },
            "files": {
                file_path: {
                    "unused_imports": report.unused_imports,
                    "unused_functions": report.unused_functions,
                    "unused_classes": report.unused_classes,
                    "commented_code": report.commented_code,
                    "debug_statements": report.debug_statements,
                    "todo_comments": report.todo_comments
                }
                for file_path, report in self.analysis_results.items()
            },
            "metrics": {
                file_path: {
                    "total_lines": metrics.total_lines,
                    "code_lines": metrics.code_lines,
                    "comment_lines": metrics.comment_lines,
                    "blank_lines": metrics.blank_lines,
                    "function_count": metrics.function_count,
                    "class_count": metrics.class_count,
                    "import_count": metrics.import_count,
                    "complexity_score": metrics.complexity_score,
                    "maintainability_index": metrics.maintainability_index
                }
                for file_path, metrics in self.code_metrics.items()
            }
        }
    
    def export_report(self, output_file: str) -> bool:
        """Export analysis report to JSON file"""
        try:
            summary = self._generate_summary_report()
            
            with open(output_file, 'w') as f:
                json.dump(summary, f, indent=2, default=str)
            
            logger.info(f"Dead code analysis report exported to: {output_file}")
            return True
            
        except Exception as e:
            logger.error(f"Error exporting report: {e}")
            return False
    
    def get_file_report(self, file_path: str) -> Optional[DeadCodeReport]:
        """Get analysis report for a specific file"""
        return self.analysis_results.get(file_path)
    
    def get_metrics_for_file(self, file_path: str) -> Optional[CodeMetrics]:
        """Get code metrics for a specific file"""
        return self.code_metrics.get(file_path)

# Global instance
_dead_code_analyzer: Optional[DeadCodeAnalyzer] = None

def get_dead_code_analyzer() -> DeadCodeAnalyzer:
    """Get global dead code analyzer"""
    global _dead_code_analyzer
    if _dead_code_analyzer is None:
        _dead_code_analyzer = DeadCodeAnalyzer()
    return _dead_code_analyzer

def cleanup_dead_code_analyzer():
    """Cleanup global dead code analyzer"""
    global _dead_code_analyzer
    if _dead_code_analyzer:
        _dead_code_analyzer = None
