"""
Legacy Code Cleanup for ASK Pipeline

This module provides comprehensive legacy code cleanup capabilities:

1. Dead Code Analyzer - Identifies unused imports, functions, and code
2. Test Consolidator - Analyzes and consolidates test files
3. Legacy Archiver - Archives deprecated components with migration guides

Features:
- Dead code analysis and reporting
- Test file consolidation and organization
- Legacy component archiving and migration
- Code cleanup validation and prevention tools
- Migration guides and documentation
- Usage tracking and monitoring

NOTE: All tools are ANALYSIS-ONLY and do NOT delete or modify code
"""

from .dead_code_analyzer import (
    DeadCodeAnalyzer,
    DeadCodeReport,
    CodeMetrics,
    get_dead_code_analyzer,
    cleanup_dead_code_analyzer
)

from .test_consolidator import (
    TestConsolidator,
    TestFile,
    TestScenario,
    DuplicateTest,
    TestConsolidationReport,
    get_test_consolidator,
    cleanup_test_consolidator
)

from .legacy_archiver import (
    LegacyArchiver,
    LegacyComponent,
    ArchivePlan,
    get_legacy_archiver,
    cleanup_legacy_archiver
)

__all__ = [
    # Dead Code Analyzer
    'DeadCodeAnalyzer',
    'DeadCodeReport',
    'CodeMetrics',
    'get_dead_code_analyzer',
    'cleanup_dead_code_analyzer',
    
    # Test Consolidator
    'TestConsolidator',
    'TestFile',
    'TestScenario',
    'DuplicateTest',
    'TestConsolidationReport',
    'get_test_consolidator',
    'cleanup_test_consolidator',
    
    # Legacy Archiver
    'LegacyArchiver',
    'LegacyComponent',
    'ArchivePlan',
    'get_legacy_archiver',
    'cleanup_legacy_archiver'
]
