"""
Code Quality and Standards System for ASK Pipeline

This module provides comprehensive code quality capabilities:

1. Type Safety - Runtime type checking and validation
2. Documentation - Docstring generation and API documentation
3. Code Standards - Formatting, linting, and security scanning

Features:
- Runtime type checking with decorators
- Automatic docstring generation
- Code formatting with black and isort
- Linting with flake8 and pylint
- Security scanning with bandit
- Pre-commit hooks integration
- Sphinx documentation generation
- MyPy configuration and CI integration
"""

from .type_safety import (
    TypeValidator,
    TypeValidationError,
    TypeValidationResult,
    TypeSafeDataClass,
    type_check,
    type_check_async,
    validate_json_schema,
    TypeSafetyChecker,
    check_module_types,
    generate_type_report
)

from .documentation import (
    DocstringGenerator,
    DocumentationGenerator,
    CodeFlowAnalyzer,
    DocstringInfo,
    ModuleDocumentation,
    generate_module_docs,
    generate_sphinx_docs,
    generate_architecture_diagram
)

from .code_standards import (
    CodeFormatter,
    CodeLinter,
    SecurityScanner,
    CodeQualityManager,
    CodeQualityIssue,
    CodeQualityReport,
    check_file_quality,
    check_directory_quality,
    generate_quality_report
)

__all__ = [
    # Type Safety
    'TypeValidator',
    'TypeValidationError',
    'TypeValidationResult',
    'TypeSafeDataClass',
    'type_check',
    'type_check_async',
    'validate_json_schema',
    'TypeSafetyChecker',
    'check_module_types',
    'generate_type_report',
    
    # Documentation
    'DocstringGenerator',
    'DocumentationGenerator',
    'CodeFlowAnalyzer',
    'DocstringInfo',
    'ModuleDocumentation',
    'generate_module_docs',
    'generate_sphinx_docs',
    'generate_architecture_diagram',
    
    # Code Standards
    'CodeFormatter',
    'CodeLinter',
    'SecurityScanner',
    'CodeQualityManager',
    'CodeQualityIssue',
    'CodeQualityReport',
    'check_file_quality',
    'check_directory_quality',
    'generate_quality_report'
]
