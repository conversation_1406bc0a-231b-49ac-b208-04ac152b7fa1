"""
Type Safety System for ASK Pipeline

Provides comprehensive type safety features:
- Runtime type checking for critical paths
- Type validation for data models and interfaces
- Type safety tests and validation utilities
- MyPy configuration and integration
"""

import inspect
import functools
from typing import Any, Type, TypeVar, Callable, Dict, List, Optional, Union, get_type_hints, get_origin, get_args
from dataclasses import dataclass, field
from datetime import datetime
import logging
import json

logger = logging.getLogger(__name__)

T = TypeVar('T')

class TypeValidationError(Exception):
    """Exception raised when type validation fails"""
    pass

@dataclass
class TypeValidationResult:
    """Result of type validation"""
    is_valid: bool
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)

class TypeValidator:
    """Comprehensive type validation system"""
    
    def __init__(self, strict_mode: bool = True):
        self.strict_mode = strict_mode
        self.validation_cache: Dict[str, bool] = {}
        self.type_registry: Dict[str, Type] = {}
        
    def register_type(self, name: str, type_class: Type):
        """Register a custom type for validation"""
        self.type_registry[name] = type_class
        
    def validate_value(self, value: Any, expected_type: Type, field_name: str = "") -> TypeValidationResult:
        """Validate a value against an expected type"""
        try:
            if self._is_valid_type(value, expected_type):
                return TypeValidationResult(is_valid=True)
            else:
                error_msg = f"Type mismatch for {field_name}: expected {expected_type.__name__}, got {type(value).__name__}"
                return TypeValidationResult(is_valid=False, errors=[error_msg])
        except Exception as e:
            return TypeValidationResult(is_valid=False, errors=[f"Validation error: {str(e)}"])
    
    def _is_valid_type(self, value: Any, expected_type: Type) -> bool:
        """Check if value matches expected type"""
        # Handle None values
        if value is None:
            return expected_type is type(None) or self._is_optional_type(expected_type)
        
        # Handle direct type matches
        if isinstance(value, expected_type):
            return True
        
        # Handle Union types
        if self._is_union_type(expected_type):
            union_args = get_args(expected_type)
            return any(self._is_valid_type(value, arg) for arg in union_args)
        
        # Handle Optional types
        if self._is_optional_type(expected_type):
            optional_args = get_args(expected_type)
            if len(optional_args) == 2 and type(None) in optional_args:
                non_none_type = next(arg for arg in optional_args if arg is not type(None))
                return self._is_valid_type(value, non_none_type)
        
        # Handle List types
        if self._is_list_type(expected_type):
            if not isinstance(value, list):
                return False
            list_args = get_args(expected_type)
            if list_args:
                element_type = list_args[0]
                return all(self._is_valid_type(item, element_type) for item in value)
            return True
        
        # Handle Dict types
        if self._is_dict_type(expected_type):
            if not isinstance(value, dict):
                return False
            dict_args = get_args(expected_type)
            if len(dict_args) == 2:
                key_type, value_type = dict_args
                return all(
                    self._is_valid_type(k, key_type) and self._is_valid_type(v, value_type)
                    for k, v in value.items()
                )
            return True
        
        # Handle custom types
        if expected_type.__name__ in self.type_registry:
            custom_type = self.type_registry[expected_type.__name__]
            return isinstance(value, custom_type)
        
        return False
    
    def _is_union_type(self, type_hint: Type) -> bool:
        """Check if type is a Union type"""
        return get_origin(type_hint) is Union
    
    def _is_optional_type(self, type_hint: Type) -> bool:
        """Check if type is Optional (Union[T, None])"""
        if self._is_union_type(type_hint):
            args = get_args(type_hint)
            return len(args) == 2 and type(None) in args
        return False
    
    def _is_list_type(self, type_hint: Type) -> bool:
        """Check if type is a List type"""
        origin = get_origin(type_hint)
        return origin is list or origin is List
    
    def _is_dict_type(self, type_hint: Type) -> bool:
        """Check if type is a Dict type"""
        origin = get_origin(type_hint)
        return origin is dict or origin is Dict

def type_check(func: Callable) -> Callable:
    """Decorator for runtime type checking of function arguments and return values"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        # Get type hints
        type_hints = get_type_hints(func)
        
        # Validate arguments
        validator = TypeValidator()
        bound_args = inspect.signature(func).bind(*args, **kwargs)
        bound_args.apply_defaults()
        
        for param_name, value in bound_args.arguments.items():
            if param_name in type_hints:
                result = validator.validate_value(value, type_hints[param_name], param_name)
                if not result.is_valid:
                    raise TypeValidationError(f"Type validation failed for parameter '{param_name}': {', '.join(result.errors)}")
        
        # Execute function
        result = func(*args, **kwargs)
        
        # Validate return value
        if 'return' in type_hints:
            return_result = validator.validate_value(result, type_hints['return'], 'return')
            if not return_result.is_valid:
                raise TypeValidationError(f"Type validation failed for return value: {', '.join(return_result.errors)}")
        
        return result
    
    return wrapper

def type_check_async(func: Callable) -> Callable:
    """Decorator for runtime type checking of async function arguments and return values"""
    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        # Get type hints
        type_hints = get_type_hints(func)
        
        # Validate arguments
        validator = TypeValidator()
        bound_args = inspect.signature(func).bind(*args, **kwargs)
        bound_args.apply_defaults()
        
        for param_name, value in bound_args.arguments.items():
            if param_name in type_hints:
                result = validator.validate_value(value, type_hints[param_name], param_name)
                if not result.is_valid:
                    raise TypeValidationError(f"Type validation failed for parameter '{param_name}': {', '.join(result.errors)}")
        
        # Execute function
        result = await func(*args, **kwargs)
        
        # Validate return value
        if 'return' in type_hints:
            return_result = validator.validate_value(result, type_hints['return'], 'return')
            if not return_result.is_valid:
                raise TypeValidationError(f"Type validation failed for return value: {', '.join(return_result.errors)}")
        
        return result
    
    return wrapper

class TypeSafeDataClass:
    """Base class for type-safe data classes with validation"""
    
    def __post_init__(self):
        """Validate all fields after initialization"""
        validator = TypeValidator()
        type_hints = get_type_hints(self.__class__)
        
        for field_name, field_value in self.__dict__.items():
            if field_name in type_hints:
                result = validator.validate_value(field_value, type_hints[field_name], field_name)
                if not result.is_valid:
                    raise TypeValidationError(f"Type validation failed for field '{field_name}': {', '.join(result.errors)}")

def validate_json_schema(data: Dict[str, Any], schema: Dict[str, Any]) -> TypeValidationResult:
    """Validate JSON data against a schema"""
    errors = []
    
    for field_name, field_schema in schema.items():
        if field_name not in data:
            if field_schema.get('required', False):
                errors.append(f"Required field '{field_name}' is missing")
            continue
        
        value = data[field_name]
        expected_type = field_schema.get('type')
        
        if expected_type == 'string' and not isinstance(value, str):
            errors.append(f"Field '{field_name}' must be a string")
        elif expected_type == 'integer' and not isinstance(value, int):
            errors.append(f"Field '{field_name}' must be an integer")
        elif expected_type == 'number' and not isinstance(value, (int, float)):
            errors.append(f"Field '{field_name}' must be a number")
        elif expected_type == 'boolean' and not isinstance(value, bool):
            errors.append(f"Field '{field_name}' must be a boolean")
        elif expected_type == 'array' and not isinstance(value, list):
            errors.append(f"Field '{field_name}' must be an array")
        elif expected_type == 'object' and not isinstance(value, dict):
            errors.append(f"Field '{field_name}' must be an object")
        
        # Check enum values
        if 'enum' in field_schema and value not in field_schema['enum']:
            errors.append(f"Field '{field_name}' must be one of {field_schema['enum']}")
        
        # Check minimum/maximum values
        if 'minimum' in field_schema and value < field_schema['minimum']:
            errors.append(f"Field '{field_name}' must be >= {field_schema['minimum']}")
        if 'maximum' in field_schema and value > field_schema['maximum']:
            errors.append(f"Field '{field_name}' must be <= {field_schema['maximum']}")
    
    return TypeValidationResult(is_valid=len(errors) == 0, errors=errors)

class TypeSafetyChecker:
    """Comprehensive type safety checker for modules"""
    
    def __init__(self):
        self.validator = TypeValidator()
        self.type_issues: List[Dict[str, Any]] = []
    
    def check_module(self, module) -> List[Dict[str, Any]]:
        """Check a module for type safety issues"""
        issues = []
        
        # Check all functions
        for name, obj in inspect.getmembers(module):
            if inspect.isfunction(obj) or inspect.ismethod(obj):
                func_issues = self._check_function(obj, name)
                issues.extend(func_issues)
        
        # Check all classes
        for name, obj in inspect.getmembers(module):
            if inspect.isclass(obj):
                class_issues = self._check_class(obj, name)
                issues.extend(class_issues)
        
        return issues
    
    def _check_function(self, func: Callable, name: str) -> List[Dict[str, Any]]:
        """Check a function for type safety issues"""
        issues = []
        
        # Check if function has type hints
        type_hints = get_type_hints(func)
        if not type_hints:
            issues.append({
                'type': 'missing_type_hints',
                'severity': 'warning',
                'function': name,
                'message': f"Function '{name}' has no type hints"
            })
        
        # Check for Any types
        for param_name, param_type in type_hints.items():
            if param_type is Any:
                issues.append({
                    'type': 'any_type',
                    'severity': 'warning',
                    'function': name,
                    'parameter': param_name,
                    'message': f"Parameter '{param_name}' in function '{name}' uses Any type"
                })
        
        return issues
    
    def _check_class(self, cls: Type, name: str) -> List[Dict[str, Any]]:
        """Check a class for type safety issues"""
        issues = []
        
        # Check class methods
        for method_name, method in inspect.getmembers(cls, predicate=inspect.isfunction):
            if not method_name.startswith('_'):
                method_issues = self._check_function(method, f"{name}.{method_name}")
                issues.extend(method_issues)
        
        # Check class attributes
        annotations = getattr(cls, '__annotations__', {})
        for attr_name, attr_type in annotations.items():
            if attr_type is Any:
                issues.append({
                    'type': 'any_type',
                    'severity': 'warning',
                    'class': name,
                    'attribute': attr_name,
                    'message': f"Attribute '{attr_name}' in class '{name}' uses Any type"
                })
        
        return issues
    
    def generate_report(self, issues: List[Dict[str, Any]]) -> str:
        """Generate a type safety report"""
        if not issues:
            return "No type safety issues found!"
        
        report = ["Type Safety Report", "=" * 50, ""]
        
        # Group issues by type
        issues_by_type = {}
        for issue in issues:
            issue_type = issue['type']
            if issue_type not in issues_by_type:
                issues_by_type[issue_type] = []
            issues_by_type[issue_type].append(issue)
        
        # Report each type of issue
        for issue_type, type_issues in issues_by_type.items():
            report.append(f"{issue_type.replace('_', ' ').title()}: {len(type_issues)} issues")
            for issue in type_issues:
                report.append(f"  - {issue['message']}")
            report.append("")
        
        return "\n".join(report)

# Global type safety checker
_type_checker = TypeSafetyChecker()

def check_module_types(module) -> List[Dict[str, Any]]:
    """Check a module for type safety issues"""
    return _type_checker.check_module(module)

def generate_type_report(module) -> str:
    """Generate a type safety report for a module"""
    issues = check_module_types(module)
    return _type_checker.generate_report(issues)
