"""
Documentation System for ASK Pipeline

Provides comprehensive documentation features:
- Docstring generation and validation
- API documentation with Sphinx
- Architecture diagrams and code flow documentation
- Developer onboarding guides
"""

import inspect
import ast
import os
from typing import Any, Dict, List, Optional, Type, Callable
from dataclasses import dataclass, field
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

@dataclass
class DocstringInfo:
    """Information about a docstring"""
    summary: str
    description: str = ""
    parameters: Dict[str, str] = field(default_factory=dict)
    returns: str = ""
    raises: Dict[str, str] = field(default_factory=dict)
    examples: List[str] = field(default_factory=list)
    notes: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)

@dataclass
class ModuleDocumentation:
    """Documentation for a module"""
    name: str
    description: str
    classes: List[Dict[str, Any]] = field(default_factory=list)
    functions: List[Dict[str, Any]] = field(default_factory=list)
    constants: List[Dict[str, Any]] = field(default_factory=list)
    imports: List[str] = field(default_factory=list)

class DocstringGenerator:
    """Generate comprehensive docstrings for code"""
    
    def __init__(self):
        self.templates = {
            'function': self._generate_function_docstring,
            'class': self._generate_class_docstring,
            'method': self._generate_method_docstring,
            'property': self._generate_property_docstring
        }
    
    def generate_docstring(self, obj: Any, obj_type: str) -> str:
        """Generate a docstring for an object"""
        if obj_type in self.templates:
            return self.templates[obj_type](obj)
        return ""
    
    def _generate_function_docstring(self, func: Callable) -> str:
        """Generate docstring for a function"""
        name = func.__name__
        signature = inspect.signature(func)
        type_hints = get_type_hints(func)
        
        docstring = f'"""\n{name}\n'
        docstring += "=" * len(name) + "\n\n"
        
        # Add description
        docstring += "Description\n"
        docstring += "-----------\n"
        docstring += "TODO: Add function description\n\n"
        
        # Add parameters
        if signature.parameters:
            docstring += "Parameters\n"
            docstring += "----------\n"
            for param_name, param in signature.parameters.items():
                param_type = type_hints.get(param_name, "Any")
                docstring += f"{param_name} : {param_type}\n"
                docstring += f"    TODO: Add parameter description\n\n"
        
        # Add return type
        if 'return' in type_hints:
            return_type = type_hints['return']
            docstring += "Returns\n"
            docstring += "-------\n"
            docstring += f"{return_type}\n"
            docstring += "    TODO: Add return description\n\n"
        
        # Add raises
        docstring += "Raises\n"
        docstring += "------\n"
        docstring += "TODO: Add exception descriptions\n\n"
        
        # Add examples
        docstring += "Examples\n"
        docstring += "--------\n"
        docstring += "TODO: Add usage examples\n\n"
        
        docstring += '"""'
        return docstring
    
    def _generate_class_docstring(self, cls: Type) -> str:
        """Generate docstring for a class"""
        name = cls.__name__
        
        docstring = f'"""\n{name}\n'
        docstring += "=" * len(name) + "\n\n"
        
        # Add description
        docstring += "Description\n"
        docstring += "-----------\n"
        docstring += "TODO: Add class description\n\n"
        
        # Add attributes
        annotations = getattr(cls, '__annotations__', {})
        if annotations:
            docstring += "Attributes\n"
            docstring += "----------\n"
            for attr_name, attr_type in annotations.items():
                docstring += f"{attr_name} : {attr_type}\n"
                docstring += f"    TODO: Add attribute description\n\n"
        
        # Add methods
        methods = [name for name, obj in inspect.getmembers(cls, predicate=inspect.isfunction)
                  if not name.startswith('_')]
        if methods:
            docstring += "Methods\n"
            docstring += "-------\n"
            for method_name in methods:
                docstring += f"{method_name}()\n"
                docstring += f"    TODO: Add method description\n\n"
        
        # Add examples
        docstring += "Examples\n"
        docstring += "--------\n"
        docstring += "TODO: Add usage examples\n\n"
        
        docstring += '"""'
        return docstring
    
    def _generate_method_docstring(self, method: Callable) -> str:
        """Generate docstring for a method"""
        return self._generate_function_docstring(method)
    
    def _generate_property_docstring(self, prop: property) -> str:
        """Generate docstring for a property"""
        name = prop.fget.__name__ if prop.fget else "property"
        
        docstring = f'"""\n{name}\n'
        docstring += "=" * len(name) + "\n\n"
        docstring += "TODO: Add property description\n\n"
        docstring += '"""'
        return docstring

class DocumentationGenerator:
    """Generate comprehensive documentation for modules"""
    
    def __init__(self):
        self.docstring_generator = DocstringGenerator()
    
    def generate_module_documentation(self, module) -> ModuleDocumentation:
        """Generate documentation for a module"""
        module_name = module.__name__
        
        # Get module description
        description = module.__doc__ or "TODO: Add module description"
        
        # Get classes
        classes = []
        for name, obj in inspect.getmembers(module, predicate=inspect.isclass):
            if not name.startswith('_'):
                class_doc = self._document_class(obj, name)
                classes.append(class_doc)
        
        # Get functions
        functions = []
        for name, obj in inspect.getmembers(module, predicate=inspect.isfunction):
            if not name.startswith('_'):
                func_doc = self._document_function(obj, name)
                functions.append(func_doc)
        
        # Get constants
        constants = []
        for name, obj in inspect.getmembers(module, predicate=lambda x: not callable(x) and not inspect.ismodule(x)):
            if not name.startswith('_') and isinstance(obj, (str, int, float, bool)):
                constants.append({
                    'name': name,
                    'value': obj,
                    'type': type(obj).__name__
                })
        
        return ModuleDocumentation(
            name=module_name,
            description=description,
            classes=classes,
            functions=functions,
            constants=constants
        )
    
    def _document_class(self, cls: Type, name: str) -> Dict[str, Any]:
        """Document a class"""
        docstring = inspect.getdoc(cls) or "TODO: Add class description"
        
        # Get methods
        methods = []
        for method_name, method in inspect.getmembers(cls, predicate=inspect.isfunction):
            if not method_name.startswith('_'):
                method_doc = self._document_function(method, method_name)
                methods.append(method_doc)
        
        # Get properties
        properties = []
        for prop_name, prop in inspect.getmembers(cls, predicate=lambda x: isinstance(x, property)):
            if not prop_name.startswith('_'):
                properties.append({
                    'name': prop_name,
                    'description': inspect.getdoc(prop) or "TODO: Add property description"
                })
        
        return {
            'name': name,
            'description': docstring,
            'methods': methods,
            'properties': properties,
            'base_classes': [base.__name__ for base in cls.__bases__ if base is not object]
        }
    
    def _document_function(self, func: Callable, name: str) -> Dict[str, Any]:
        """Document a function"""
        docstring = inspect.getdoc(func) or "TODO: Add function description"
        signature = inspect.signature(func)
        type_hints = get_type_hints(func)
        
        # Parse parameters
        parameters = []
        for param_name, param in signature.parameters.items():
            param_type = type_hints.get(param_name, "Any")
            param_doc = {
                'name': param_name,
                'type': param_type,
                'default': param.default if param.default is not inspect.Parameter.empty else None,
                'description': f"TODO: Add description for {param_name}"
            }
            parameters.append(param_doc)
        
        # Parse return type
        return_type = type_hints.get('return', "Any")
        
        return {
            'name': name,
            'description': docstring,
            'parameters': parameters,
            'return_type': return_type,
            'is_async': inspect.iscoroutinefunction(func)
        }
    
    def generate_sphinx_documentation(self, module_docs: ModuleDocumentation) -> str:
        """Generate Sphinx documentation for a module"""
        doc = []
        
        # Module header
        doc.append(f".. module:: {module_docs.name}")
        doc.append("")
        doc.append(module_docs.description)
        doc.append("")
        
        # Classes
        if module_docs.classes:
            doc.append("Classes")
            doc.append("-------")
            doc.append("")
            
            for class_doc in module_docs.classes:
                doc.append(f".. class:: {class_doc['name']}")
                doc.append("")
                doc.append(f"   {class_doc['description']}")
                doc.append("")
                
                # Methods
                if class_doc['methods']:
                    doc.append("   Methods:")
                    doc.append("")
                    for method in class_doc['methods']:
                        doc.append(f"   .. method:: {method['name']}({', '.join(p['name'] for p in method['parameters'])})")
                        doc.append("")
                        doc.append(f"      {method['description']}")
                        doc.append("")
        
        # Functions
        if module_docs.functions:
            doc.append("Functions")
            doc.append("---------")
            doc.append("")
            
            for func_doc in module_docs.functions:
                doc.append(f".. function:: {func_doc['name']}({', '.join(p['name'] for p in func_doc['parameters'])})")
                doc.append("")
                doc.append(f"   {func_doc['description']}")
                doc.append("")
        
        return "\n".join(doc)
    
    def generate_architecture_diagram(self, module_docs: ModuleDocumentation) -> str:
        """Generate a simple architecture diagram"""
        diagram = []
        diagram.append("```mermaid")
        diagram.append("graph TD")
        diagram.append("")
        
        # Add classes
        for class_doc in module_docs.classes:
            class_name = class_doc['name']
            diagram.append(f"    {class_name}[{class_name}]")
            
            # Add methods
            for method in class_doc['methods']:
                method_name = method['name']
                diagram.append(f"    {class_name} --> {method_name}[{method_name}]")
        
        # Add functions
        for func_doc in module_docs.functions:
            func_name = func_doc['name']
            diagram.append(f"    {func_name}[{func_name}]")
        
        diagram.append("```")
        return "\n".join(doc)

class CodeFlowAnalyzer:
    """Analyze code flow and dependencies"""
    
    def __init__(self):
        self.dependencies: Dict[str, List[str]] = {}
        self.call_graph: Dict[str, List[str]] = {}
    
    def analyze_module(self, module) -> Dict[str, Any]:
        """Analyze a module for code flow and dependencies"""
        module_name = module.__name__
        
        # Get all functions and classes
        functions = [name for name, obj in inspect.getmembers(module, predicate=inspect.isfunction)
                    if not name.startswith('_')]
        classes = [name for name, obj in inspect.getmembers(module, predicate=inspect.isclass)
                  if not name.startswith('_')]
        
        # Analyze dependencies
        dependencies = self._analyze_dependencies(module)
        
        # Analyze call graph
        call_graph = self._analyze_call_graph(module)
        
        return {
            'module_name': module_name,
            'functions': functions,
            'classes': classes,
            'dependencies': dependencies,
            'call_graph': call_graph,
            'complexity_score': self._calculate_complexity(module)
        }
    
    def _analyze_dependencies(self, module) -> List[str]:
        """Analyze module dependencies"""
        dependencies = []
        
        # Get imports
        source = inspect.getsource(module)
        tree = ast.parse(source)
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    dependencies.append(alias.name)
            elif isinstance(node, ast.ImportFrom):
                if node.module:
                    dependencies.append(node.module)
        
        return dependencies
    
    def _analyze_call_graph(self, module) -> Dict[str, List[str]]:
        """Analyze function call graph"""
        call_graph = {}
        
        # This is a simplified analysis
        # In a real implementation, you would parse the AST to find function calls
        for name, obj in inspect.getmembers(module, predicate=inspect.isfunction):
            if not name.startswith('_'):
                call_graph[name] = []  # Placeholder
        
        return call_graph
    
    def _calculate_complexity(self, module) -> int:
        """Calculate module complexity score"""
        # Simplified complexity calculation
        functions = [obj for name, obj in inspect.getmembers(module, predicate=inspect.isfunction)
                    if not name.startswith('_')]
        classes = [obj for name, obj in inspect.getmembers(module, predicate=inspect.isclass)
                  if not name.startswith('_')]
        
        return len(functions) + len(classes) * 2

# Global documentation generator
_doc_generator = DocumentationGenerator()

def generate_module_docs(module) -> ModuleDocumentation:
    """Generate documentation for a module"""
    return _doc_generator.generate_module_documentation(module)

def generate_sphinx_docs(module) -> str:
    """Generate Sphinx documentation for a module"""
    module_docs = generate_module_docs(module)
    return _doc_generator.generate_sphinx_documentation(module_docs)

def generate_architecture_diagram(module) -> str:
    """Generate architecture diagram for a module"""
    module_docs = generate_module_docs(module)
    return _doc_generator.generate_architecture_diagram(module_docs)
