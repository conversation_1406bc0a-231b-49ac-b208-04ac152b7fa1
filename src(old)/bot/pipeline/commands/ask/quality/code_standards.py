"""
Code Standards System for ASK Pipeline

Provides comprehensive code quality standards:
- Code formatting with black and isort
- Linting with flake8 and pylint
- Security scanning with bandit and safety
- Code review checklist and quality gates
- Automated code quality reporting
"""

import subprocess
import os
import json
import ast
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

@dataclass
class CodeQualityIssue:
    """Represents a code quality issue"""
    file_path: str
    line_number: int
    column: int
    severity: str
    rule_id: str
    message: str
    tool: str

@dataclass
class CodeQualityReport:
    """Comprehensive code quality report"""
    total_issues: int
    issues_by_severity: Dict[str, int]
    issues_by_tool: Dict[str, int]
    issues_by_file: Dict[str, int]
    issues: List[CodeQualityIssue]
    score: float
    timestamp: datetime = field(default_factory=datetime.utcnow)

class CodeFormatter:
    """Code formatting with black and isort"""
    
    def __init__(self, line_length: int = 88, target_version: str = "py38"):
        self.line_length = line_length
        self.target_version = target_version
        self.black_config = {
            'line_length': line_length,
            'target_version': target_version,
            'skip_string_normalization': False,
            'skip_magic_trailing_comma': False
        }
        self.isort_config = {
            'line_length': line_length,
            'multi_line_output': 3,
            'include_trailing_comma': True,
            'force_grid_wrap': 0,
            'use_parentheses': True,
            'ensure_newline_before_comments': True
        }
    
    def format_file(self, file_path: str) -> Tuple[bool, str]:
        """Format a single file with black and isort"""
        try:
            # Format with black
            black_result = self._run_black(file_path)
            if not black_result[0]:
                return False, f"Black formatting failed: {black_result[1]}"
            
            # Sort imports with isort
            isort_result = self._run_isort(file_path)
            if not isort_result[0]:
                return False, f"Import sorting failed: {isort_result[1]}"
            
            return True, "File formatted successfully"
            
        except Exception as e:
            return False, f"Formatting error: {str(e)}"
    
    def format_directory(self, directory: str) -> Dict[str, Any]:
        """Format all Python files in a directory"""
        results = {
            'total_files': 0,
            'successful_files': 0,
            'failed_files': 0,
            'errors': []
        }
        
        for root, dirs, files in os.walk(directory):
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    results['total_files'] += 1
                    
                    success, message = self.format_file(file_path)
                    if success:
                        results['successful_files'] += 1
                    else:
                        results['failed_files'] += 1
                        results['errors'].append({
                            'file': file_path,
                            'error': message
                        })
        
        return results
    
    def _run_black(self, file_path: str) -> Tuple[bool, str]:
        """Run black formatter on a file"""
        try:
            cmd = [
                'black',
                '--line-length', str(self.line_length),
                '--target-version', self.target_version,
                file_path
            ]
            result = subprocess.run(cmd, capture_output=True, text=True)
            return result.returncode == 0, result.stderr
        except FileNotFoundError:
            return False, "Black not found. Install with: pip install black"
    
    def _run_isort(self, file_path: str) -> Tuple[bool, str]:
        """Run isort on a file"""
        try:
            cmd = [
                'isort',
                '--line-length', str(self.line_length),
                '--multi-line', '3',
                '--trailing-comma',
                '--force-grid-wrap', '0',
                '--use-parentheses',
                '--ensure-newline-before-comments',
                file_path
            ]
            result = subprocess.run(cmd, capture_output=True, text=True)
            return result.returncode == 0, result.stderr
        except FileNotFoundError:
            return False, "isort not found. Install with: pip install isort"

class CodeLinter:
    """Code linting with flake8 and pylint"""
    
    def __init__(self):
        self.flake8_config = {
            'max_line_length': 88,
            'ignore': ['E203', 'W503', 'E501'],
            'exclude': ['__pycache__', '.git', '.venv', 'venv']
        }
        self.pylint_config = {
            'disable': ['C0114', 'C0116'],  # Disable missing docstring warnings
            'max_line_length': 88,
            'good_names': ['i', 'j', 'k', 'ex', 'Run', '_', 'id']
        }
    
    def lint_file(self, file_path: str) -> List[CodeQualityIssue]:
        """Lint a single file with flake8 and pylint"""
        issues = []
        
        # Run flake8
        flake8_issues = self._run_flake8(file_path)
        issues.extend(flake8_issues)
        
        # Run pylint
        pylint_issues = self._run_pylint(file_path)
        issues.extend(pylint_issues)
        
        return issues
    
    def lint_directory(self, directory: str) -> CodeQualityReport:
        """Lint all Python files in a directory"""
        all_issues = []
        
        for root, dirs, files in os.walk(directory):
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    file_issues = self.lint_file(file_path)
                    all_issues.extend(file_issues)
        
        return self._generate_report(all_issues)
    
    def _run_flake8(self, file_path: str) -> List[CodeQualityIssue]:
        """Run flake8 on a file"""
        issues = []
        try:
            cmd = [
                'flake8',
                '--max-line-length', str(self.flake8_config['max_line_length']),
                '--ignore', ','.join(self.flake8_config['ignore']),
                '--format', 'json',
                file_path
            ]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode != 0:
                try:
                    flake8_output = json.loads(result.stdout)
                    for issue in flake8_output:
                        issues.append(CodeQualityIssue(
                            file_path=issue['filename'],
                            line_number=issue['line_number'],
                            column=issue['column_number'],
                            severity=self._map_flake8_severity(issue['code']),
                            rule_id=issue['code'],
                            message=issue['text'],
                            tool='flake8'
                        ))
                except json.JSONDecodeError:
                    # Fallback to parsing text output
                    for line in result.stdout.split('\n'):
                        if line.strip():
                            parts = line.split(':')
                            if len(parts) >= 4:
                                issues.append(CodeQualityIssue(
                                    file_path=parts[0],
                                    line_number=int(parts[1]),
                                    column=int(parts[2]),
                                    severity='warning',
                                    rule_id=parts[3].split()[0],
                                    message=' '.join(parts[3].split()[1:]),
                                    tool='flake8'
                                ))
        except FileNotFoundError:
            logger.warning("flake8 not found. Install with: pip install flake8")
        
        return issues
    
    def _run_pylint(self, file_path: str) -> List[CodeQualityIssue]:
        """Run pylint on a file"""
        issues = []
        try:
            cmd = [
                'pylint',
                '--disable', ','.join(self.pylint_config['disable']),
                '--max-line-length', str(self.pylint_config['max_line_length']),
                '--output-format', 'json',
                file_path
            ]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode != 0:
                try:
                    pylint_output = json.loads(result.stdout)
                    for issue in pylint_output:
                        issues.append(CodeQualityIssue(
                            file_path=issue['path'],
                            line_number=issue['line'],
                            column=issue['column'],
                            severity=issue['type'].lower(),
                            rule_id=issue['message-id'],
                            message=issue['message'],
                            tool='pylint'
                        ))
                except json.JSONDecodeError:
                    logger.warning(f"Failed to parse pylint output for {file_path}")
        except FileNotFoundError:
            logger.warning("pylint not found. Install with: pip install pylint")
        
        return issues
    
    def _map_flake8_severity(self, code: str) -> str:
        """Map flake8 error codes to severity levels"""
        if code.startswith('E'):
            return 'error'
        elif code.startswith('W'):
            return 'warning'
        elif code.startswith('F'):
            return 'error'
        else:
            return 'info'
    
    def _generate_report(self, issues: List[CodeQualityIssue]) -> CodeQualityReport:
        """Generate a comprehensive code quality report"""
        total_issues = len(issues)
        
        # Group by severity
        issues_by_severity = {}
        for issue in issues:
            severity = issue.severity
            issues_by_severity[severity] = issues_by_severity.get(severity, 0) + 1
        
        # Group by tool
        issues_by_tool = {}
        for issue in issues:
            tool = issue.tool
            issues_by_tool[tool] = issues_by_tool.get(tool, 0) + 1
        
        # Group by file
        issues_by_file = {}
        for issue in issues:
            file_path = issue.file_path
            issues_by_file[file_path] = issues_by_file.get(file_path, 0) + 1
        
        # Calculate quality score (0-100)
        error_count = issues_by_severity.get('error', 0)
        warning_count = issues_by_severity.get('warning', 0)
        total_penalty = error_count * 10 + warning_count * 2
        score = max(0, 100 - total_penalty)
        
        return CodeQualityReport(
            total_issues=total_issues,
            issues_by_severity=issues_by_severity,
            issues_by_tool=issues_by_tool,
            issues_by_file=issues_by_file,
            issues=issues,
            score=score
        )

class SecurityScanner:
    """Security scanning with bandit and safety"""
    
    def __init__(self):
        self.bandit_config = {
            'severity': 'medium',
            'confidence': 'medium',
            'exclude_dirs': ['tests', '__pycache__', '.git']
        }
    
    def scan_file(self, file_path: str) -> List[CodeQualityIssue]:
        """Scan a single file for security issues"""
        issues = []
        
        # Run bandit
        bandit_issues = self._run_bandit(file_path)
        issues.extend(bandit_issues)
        
        return issues
    
    def scan_directory(self, directory: str) -> List[CodeQualityIssue]:
        """Scan all Python files in a directory for security issues"""
        all_issues = []
        
        for root, dirs, files in os.walk(directory):
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    file_issues = self.scan_file(file_path)
                    all_issues.extend(file_issues)
        
        return all_issues
    
    def _run_bandit(self, file_path: str) -> List[CodeQualityIssue]:
        """Run bandit security scanner on a file"""
        issues = []
        try:
            cmd = [
                'bandit',
                '-f', 'json',
                '-r', file_path
            ]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode != 0:
                try:
                    bandit_output = json.loads(result.stdout)
                    for issue in bandit_output.get('results', []):
                        issues.append(CodeQualityIssue(
                            file_path=issue['filename'],
                            line_number=issue['line_number'],
                            column=0,
                            severity=issue['issue_severity'].lower(),
                            rule_id=issue['test_id'],
                            message=issue['issue_text'],
                            tool='bandit'
                        ))
                except json.JSONDecodeError:
                    logger.warning(f"Failed to parse bandit output for {file_path}")
        except FileNotFoundError:
            logger.warning("bandit not found. Install with: pip install bandit")
        
        return issues

class CodeQualityManager:
    """Comprehensive code quality management"""
    
    def __init__(self):
        self.formatter = CodeFormatter()
        self.linter = CodeLinter()
        self.security_scanner = SecurityScanner()
    
    def check_code_quality(self, file_path: str) -> Dict[str, Any]:
        """Check code quality for a single file"""
        results = {
            'file_path': file_path,
            'formatting': {'success': False, 'message': ''},
            'linting': {'issues': [], 'score': 0},
            'security': {'issues': [], 'vulnerabilities': 0}
        }
        
        # Check formatting
        if file_path.endswith('.py'):
            success, message = self.formatter.format_file(file_path)
            results['formatting'] = {'success': success, 'message': message}
        
        # Check linting
        lint_issues = self.linter.lint_file(file_path)
        results['linting'] = {
            'issues': [self._issue_to_dict(issue) for issue in lint_issues],
            'score': self._calculate_lint_score(lint_issues)
        }
        
        # Check security
        security_issues = self.security_scanner.scan_file(file_path)
        results['security'] = {
            'issues': [self._issue_to_dict(issue) for issue in security_issues],
            'vulnerabilities': len([i for i in security_issues if i.severity == 'high'])
        }
        
        return results
    
    def check_directory_quality(self, directory: str) -> Dict[str, Any]:
        """Check code quality for a directory"""
        results = {
            'directory': directory,
            'total_files': 0,
            'formatted_files': 0,
            'linting_report': None,
            'security_issues': [],
            'overall_score': 0
        }
        
        # Format files
        format_results = self.formatter.format_directory(directory)
        results['total_files'] = format_results['total_files']
        results['formatted_files'] = format_results['successful_files']
        
        # Lint files
        lint_report = self.linter.lint_directory(directory)
        results['linting_report'] = {
            'total_issues': lint_report.total_issues,
            'score': lint_report.score,
            'issues_by_severity': lint_report.issues_by_severity
        }
        
        # Security scan
        security_issues = self.security_scanner.scan_directory(directory)
        results['security_issues'] = [self._issue_to_dict(issue) for issue in security_issues]
        
        # Calculate overall score
        lint_score = lint_report.score
        security_penalty = len([i for i in security_issues if i.severity == 'high']) * 10
        results['overall_score'] = max(0, lint_score - security_penalty)
        
        return results
    
    def _issue_to_dict(self, issue: CodeQualityIssue) -> Dict[str, Any]:
        """Convert CodeQualityIssue to dictionary"""
        return {
            'file_path': issue.file_path,
            'line_number': issue.line_number,
            'column': issue.column,
            'severity': issue.severity,
            'rule_id': issue.rule_id,
            'message': issue.message,
            'tool': issue.tool
        }
    
    def _calculate_lint_score(self, issues: List[CodeQualityIssue]) -> int:
        """Calculate linting score based on issues"""
        error_count = len([i for i in issues if i.severity == 'error'])
        warning_count = len([i for i in issues if i.severity == 'warning'])
        
        penalty = error_count * 10 + warning_count * 2
        return max(0, 100 - penalty)
    
    def generate_quality_report(self, directory: str) -> str:
        """Generate a comprehensive quality report"""
        results = self.check_directory_quality(directory)
        
        report = []
        report.append("Code Quality Report")
        report.append("=" * 50)
        report.append(f"Directory: {directory}")
        report.append(f"Timestamp: {datetime.now().isoformat()}")
        report.append("")
        
        # Formatting results
        report.append("Formatting")
        report.append("-" * 20)
        report.append(f"Total files: {results['total_files']}")
        report.append(f"Formatted files: {results['formatted_files']}")
        report.append("")
        
        # Linting results
        lint_report = results['linting_report']
        report.append("Linting")
        report.append("-" * 20)
        report.append(f"Total issues: {lint_report['total_issues']}")
        report.append(f"Score: {lint_report['score']}/100")
        report.append("Issues by severity:")
        for severity, count in lint_report['issues_by_severity'].items():
            report.append(f"  {severity}: {count}")
        report.append("")
        
        # Security results
        report.append("Security")
        report.append("-" * 20)
        report.append(f"Total issues: {len(results['security_issues'])}")
        high_severity = len([i for i in results['security_issues'] if i['severity'] == 'high'])
        report.append(f"High severity: {high_severity}")
        report.append("")
        
        # Overall score
        report.append("Overall Quality Score")
        report.append("-" * 20)
        report.append(f"Score: {results['overall_score']}/100")
        
        if results['overall_score'] >= 90:
            report.append("Status: Excellent")
        elif results['overall_score'] >= 80:
            report.append("Status: Good")
        elif results['overall_score'] >= 70:
            report.append("Status: Fair")
        else:
            report.append("Status: Needs Improvement")
        
        return "\n".join(report)

# Global code quality manager
_quality_manager = CodeQualityManager()

def check_file_quality(file_path: str) -> Dict[str, Any]:
    """Check code quality for a single file"""
    return _quality_manager.check_code_quality(file_path)

def check_directory_quality(directory: str) -> Dict[str, Any]:
    """Check code quality for a directory"""
    return _quality_manager.check_directory_quality(directory)

def generate_quality_report(directory: str) -> str:
    """Generate a comprehensive quality report"""
    return _quality_manager.generate_quality_report(directory)
