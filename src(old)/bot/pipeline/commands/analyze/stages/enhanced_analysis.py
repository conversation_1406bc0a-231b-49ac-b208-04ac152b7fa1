"""
Enhanced Analysis Stage for Analyze Pipeline
"""

from typing import Dict, Any
from src.bot.pipeline.core.pipeline_engine import BasePipelineStage, StageResult
from src.bot.pipeline.core.context_manager import PipelineContext

class EnhancedAnalysisStage(BasePipelineStage):
    """Stage for generating enhanced analysis including sentiment and probabilities"""
    
    def __init__(self):
        super().__init__("enhanced_analysis")
        self.required_inputs = ["market_data", "technical_analysis"]
        self.outputs = ["enhanced_analysis"]
    
    async def process(self, context: PipelineContext) -> StageResult:
        """Generate enhanced analysis"""
        start_time = time.time()
        ticker = context.ticker
        
        try:
            # Get data from context
            market_data = context.processing_results.get("market_data", {})
            technical_analysis = context.processing_results.get("technical_analysis", {})
            
            current_price = market_data.get('current_price', 0)
            if not current_price:
                raise ValueError("No current price available for enhanced analysis")
            
            trend = technical_analysis.get('trend', 'sideways')
            rsi = technical_analysis.get('rsi', 50.0)
            volatility = technical_analysis.get('volatility', 'medium')
            
            # Determine market sentiment based on technical indicators
            if trend == 'uptrend' and rsi < 70:
                sentiment = 'bullish'
                probability = 0.65 + (70 - rsi) / 100 * 0.2  # Adjust based on RSI
            elif trend == 'downtrend' and rsi > 30:
                sentiment = 'bearish'
                probability = 0.65 + (rsi - 30) / 100 * 0.2
            else:
                sentiment = 'neutral'
                probability = 0.5
            
            # Adjust probability based on volatility
            if volatility == 'high':
                probability *= 0.8  # Reduce confidence in high volatility
            elif volatility == 'low':
                probability *= 1.1  # Increase confidence in low volatility
            
            # Calculate confidence score
            confidence = min(0.9, probability * 0.8 + (1 - abs(rsi - 50) / 50) * 0.2)
            
            # Generate key levels summary
            support = technical_analysis.get('support_levels', [current_price * 0.95])
            resistance = technical_analysis.get('resistance_levels', [current_price * 1.05])
            
            key_levels = {
                "nearest_support": min(support),
                "nearest_resistance": min(resistance),
                "distance_to_support": current_price - min(support),
                "distance_to_resistance": min(resistance) - current_price
            }
            
            enhanced_analysis = {
                "sentiment": sentiment,
                "probability": round(probability, 2),
                "confidence": round(confidence, 2),
                "key_levels": key_levels,
                "volatility_assessment": volatility,
                "recommendation_strength": "strong" if confidence > 0.7 else "moderate" if confidence > 0.5 else "weak",
                "trading_bias": f"{sentiment} with {volatility} volatility"
            }
            
            return StageResult(
                success=True,
                output_data={"enhanced_analysis": enhanced_analysis},
                execution_time=time.time() - start_time
            )
            
        except Exception as e:
            # Log error but provide fallback
            logger.error(f"Enhanced analysis failed for {ticker}: {e}")
            
            fallback_enhanced = {
                "sentiment": "neutral",
                "probability": 0.5,
                "confidence": 0.3,
                "key_levels": {
                    "nearest_support": 0,
                    "nearest_resistance": 0,
                    "distance_to_support": 0,
                    "distance_to_resistance": 0
                },
                "volatility_assessment": "unknown",
                "recommendation_strength": "weak",
                "trading_bias": "neutral - insufficient data",
                "is_fallback": True,
                "fallback_reason": str(e)
            }
            
            return StageResult(
                success=True,  # Return success with fallback
                output_data={"enhanced_analysis": fallback_enhanced},
                execution_time=time.time() - start_time,
                error_message=f"Used fallback enhanced analysis: {str(e)}"
            )