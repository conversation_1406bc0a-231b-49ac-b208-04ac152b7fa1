"""Stage: report_generator
 
Format a comprehensive analysis report from context.processing_results and attach it to
context.processing_results['response'].
"""

from typing import Dict, Any
from src.shared.error_handling.logging import get_trading_logger
from ..pipeline import PipelineContext
from datetime import datetime


def _safe_float(value: Any, default: float = 0.0) -> float:
    """Safely convert value to float, handling None and non-numeric values."""
    if value is None:
        return default
    try:
        return float(value)
    except (ValueError, TypeError):
        return default


def _format_report(ticker: str, market_data: Dict[str, Any], technical: Dict[str, Any], targets: Dict[str, Any], enhanced: Dict[str, Any] = {}) -> str:
    # Safely extract current_price, ensuring it's a float
    current_price_raw = market_data.get('current_price') or market_data.get('price')
    current_price = _safe_float(current_price_raw, 0.0)
    
    rsi = technical.get('rsi', 'N/A')
    macd = technical.get('macd', 'N/A')
    trend = technical.get('trend', 'N/A')

    # Safely extract targets
    tp1 = _safe_float(targets.get('tp1'))
    tp2 = _safe_float(targets.get('tp2'))
    tp3 = _safe_float(targets.get('tp3'))
    sl = _safe_float(targets.get('sl'))

    report = f"""
📊 ANALYSIS REPORT FOR {ticker}

💰 CURRENT PRICE: ${current_price:.2f}

📈 TREND: {trend}
• RSI: {rsi}
• MACD: {macd}

🎯 PRICE TARGETS:
• TP1: ${tp1:.2f}
• TP2: ${tp2:.2f}
• TP3: ${tp3:.2f}
• SL: ${sl:.2f}
"""

    # Add enhanced analysis if available
    if enhanced:
        # Safely extract probability values
        prob_assess = enhanced.get('probability_assessment', {})
        bullish_prob = _safe_float(prob_assess.get('bullish_probability'), 0.0)
        bearish_prob = _safe_float(prob_assess.get('bearish_probability'), 0.0)
        confidence_level = _safe_float(prob_assess.get('confidence_level'), 0.0)
        
        # Safely extract timeframe confirmation values
        timeframe_confirm = enhanced.get('timeframe_confirmation', {})
        agreement_score = _safe_float(timeframe_confirm.get('agreement_score'), 0.0)
        overall_confidence = _safe_float(timeframe_confirm.get('overall_confidence'), 0.0)
        
        # Safely extract price targets from enhanced
        enhanced_targets = enhanced.get('price_targets', {})
        cons_target = _safe_float(enhanced_targets.get('conservative_target'), 0.0)
        mod_target = _safe_float(enhanced_targets.get('moderate_target'), 0.0)
        agg_target = _safe_float(enhanced_targets.get('aggressive_target'), 0.0)
        stop_loss_enhanced = _safe_float(enhanced_targets.get('stop_loss'), 0.0)
        
        report += f"""
 
🚀 ENHANCED ANALYSIS:
 
🎯 ADVANCED TARGETS:
• Conservative: ${cons_target:.2f}
• Moderate: ${mod_target:.2f}
• Aggressive: ${agg_target:.2f}
• Stop Loss: ${stop_loss_enhanced:.2f}

🎲 PROBABILITY ASSESSMENT:
• Bullish: {bullish_prob * 100:.1f}%
• Bearish: {bearish_prob * 100:.1f}%
• Confidence: {confidence_level * 100:.1f}%

⏰ TIMEFRAME ANALYSIS:
• Agreement Score: {agreement_score * 100:.1f}%
• Overall Confidence: {overall_confidence * 100:.1f}%

💡 RECOMMENDATION: {enhanced.get('overall_recommendation', 'N/A')}
🔒 RISK LEVEL: {enhanced.get('risk_level', 'N/A')}
"""

    report += f"""

🕒 Generated: {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')} UTC

⚠️ Disclaimer: For informational purposes only.
""".strip()

    return report


async def run(context: PipelineContext) -> PipelineContext:
    logger = get_trading_logger("report_generator_stage")
    ticker = context.ticker
    logger.info(f"[report_generator] Building report for {ticker}")

    try:
        market_data = context.processing_results.get('market_data', {})
        technical = context.processing_results.get('technical_analysis', {})
        targets = context.processing_results.get('price_targets', {})
        enhanced = context.processing_results.get('enhanced_analysis', {})

        # Log extracted data for debugging
        logger.debug(f"[report_generator] Market data keys for {ticker}: {list(market_data.keys())}")
        logger.debug(f"[report_generator] Targets keys for {ticker}: {list(targets.keys())}")
        logger.debug(f"[report_generator] Enhanced keys for {ticker}: {list(enhanced.keys())}")

        report = _format_report(ticker, market_data, technical, targets, enhanced)
        context.processing_results['response'] = report
        logger.info(f"[report_generator] Report built successfully for {ticker} (length: {len(report)} chars)")

    except Exception as exc:
        logger.error(f"[report_generator] Error building report for {ticker}: {exc}", exc_info=True)
        context.error_log.append({
            'stage': 'report_generator',
            'error_message': str(exc),
            'error_type': type(exc).__name__,
        })
        
        # Generate a minimal fallback report
        current_price_fallback = _safe_float(market_data.get('current_price'), 0.0)
        trend_fallback = technical.get('trend', 'N/A')
        
        fallback_report = f"""
❌ **REPORT GENERATION FAILED FOR {ticker}**

Error: {str(exc)}

**Basic Info (if available):**
• Current Price: ${current_price_fallback:.2f}
• Trend: {trend_fallback}

⚠️ Please try again later. This is a temporary issue.
*This is for informational purposes only.*
"""
        context.processing_results['response'] = fallback_report

    return context