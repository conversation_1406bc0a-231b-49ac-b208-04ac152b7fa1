"""Stage: technical_analysis

Compute technical indicators and attach the results to PipelineContext.processing_results['technical_analysis'].
"""

from typing import Dict, Any
from src.shared.error_handling.logging import get_trading_logger
from ..pipeline import PipelineContext


async def run(context: PipelineContext) -> PipelineContext:
    """Generate technical analysis and attach to context.processing_results['technical_analysis']."""
    logger = get_trading_logger("technical_analysis_stage")
    ticker = context.ticker
    logger.info(f"[technical_analysis] Starting technical analysis for {ticker}")

    try:
        market_data = context.processing_results.get('market_data', {})
        if not market_data:
            raise ValueError("No market_data available for technical analysis")

        from src.shared.technical_analysis.calculator import TechnicalAnalysisCalculator
        calculator = TechnicalAnalysisCalculator()

        # Attempt to get historical closes in a few possible shapes
        historical = market_data.get('historical') or market_data.get('history') or {}
        closes = []
        if isinstance(historical, dict):
            closes = historical.get('closes', [])
        elif isinstance(historical, list):
            # assume list of dicts with 'close'
            closes = [dp.get('close') for dp in historical if dp.get('close') is not None]

        # Enhanced data sufficiency check with fallback strategies
        if len(closes) < 14:
            logger.warning(f"[technical_analysis] Insufficient closes ({len(closes)}) for indicators; need at least 14 data points")
            
            # Try to get more data or use alternative approaches
            closes = await _enhance_data_for_analysis(closes, market_data, ticker, logger)
            
            # If still insufficient, provide minimal analysis
            if len(closes) < 14:
                context.processing_results['technical_analysis'] = _generate_minimal_analysis(
                    closes, ticker, len(closes), logger
                )
                return context

        # Calculate indicators with error handling
        try:
            rsi = calculator.calculate_rsi(closes, period=14)
        except Exception as e:
            logger.warning(f"[technical_analysis] RSI calculation failed: {e}")
            rsi = None

        try:
            macd_data = calculator.calculate_macd(closes)
            macd_signal_label = 'bullish' if macd_data and macd_data.get('macd', 0) > macd_data.get('signal', 0) else 'bearish'
        except Exception as e:
            logger.warning(f"[technical_analysis] MACD calculation failed: {e}")
            macd_signal_label = 'neutral'

        try:
            support_levels = calculator.calculate_support_levels(closes)
        except Exception as e:
            logger.warning(f"[technical_analysis] Support levels calculation failed: {e}")
            support_levels = []

        try:
            resistance_levels = calculator.calculate_resistance_levels(closes)
        except Exception as e:
            logger.warning(f"[technical_analysis] Resistance levels calculation failed: {e}")
            resistance_levels = []

        try:
            trend = 'uptrend' if closes[-1] > closes[0] else 'downtrend' if closes[-1] < closes[0] else 'sideways'
        except Exception as e:
            logger.warning(f"[technical_analysis] Trend calculation failed: {e}")
            trend = 'unknown'

        try:
            volatility = calculator.calculate_volatility(closes)
            volatility_level = 'high' if volatility and volatility > 0.05 else 'medium' if volatility and volatility > 0.02 else 'low'
        except Exception as e:
            logger.warning(f"[technical_analysis] Volatility calculation failed: {e}")
            volatility_level = 'unknown'

        result: Dict[str, Any] = {
            'rsi': rsi,
            'macd': macd_signal_label,
            'support_levels': support_levels,
            'resistance_levels': resistance_levels,
            'trend': trend,
            'volatility': volatility_level,
        }

        context.processing_results['technical_analysis'] = result
        logger.info(f"[technical_analysis] Completed for {ticker}")

    except Exception as exc:
        logger.error(f"[technical_analysis] Error for {ticker}: {exc}")
        context.error_log.append({
            'stage': 'technical_analysis',
            'error_message': str(exc),
            'error_type': type(exc).__name__,
        })

    return context


async def _enhance_data_for_analysis(closes: list, market_data: Dict[str, Any], ticker: str, logger) -> list:
    """
    Try to enhance data for technical analysis when insufficient data is available.
    
    Args:
        closes: Current close prices list
        market_data: Market data dictionary
        ticker: Stock symbol
        logger: Logger instance
        
    Returns:
        Enhanced closes list
    """
    try:
        # Try to get extended historical data
        from src.shared.data_providers.aggregator import DataProviderAggregator
        aggregator = DataProviderAggregator()
        
        # Try with longer period
        extended_hist = await aggregator.get_history(ticker, period='3mo', interval='1d')
        if extended_hist and 'error' not in extended_hist:
            historical = extended_hist.get('historical', {})
            if isinstance(historical, dict):
                extended_closes = historical.get('closes', [])
                if len(extended_closes) > len(closes):
                    logger.info(f"[technical_analysis] Enhanced data for {ticker} with {len(extended_closes)} data points")
                    return extended_closes
    except Exception as e:
        logger.debug(f"[technical_analysis] Failed to enhance data for {ticker}: {e}")
    
    return closes


def _generate_minimal_analysis(closes: list, ticker: str, data_points: int, logger) -> Dict[str, Any]:
    """
    Generate minimal analysis when insufficient data is available.
    
    Args:
        closes: Available close prices
        ticker: Stock symbol
        data_points: Number of available data points
        logger: Logger instance
        
    Returns:
        Minimal analysis dictionary
    """
    logger.info(f"[technical_analysis] Generating minimal analysis for {ticker} with {data_points} data points")
    
    # Basic price analysis with available data
    basic_analysis = {
        'error': 'insufficient_data',
        'message': f'Need at least 14 data points for full technical analysis, only {data_points} available',
        'data_points_available': data_points,
        'minimum_required': 14,
        'current_price': closes[-1] if closes else None,
        'price_change': None,
        'basic_trend': None
    }
    
    # Add basic trend analysis if we have some data
    if len(closes) >= 2:
        price_change = ((closes[-1] - closes[0]) / closes[0] * 100) if closes[0] != 0 else 0
        basic_analysis['price_change'] = round(price_change, 2)
        basic_analysis['basic_trend'] = 'up' if price_change > 0 else 'down' if price_change < 0 else 'flat'
    
    return basic_analysis 