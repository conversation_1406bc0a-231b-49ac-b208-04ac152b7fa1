"""Stage: fetch_data

Fetch market data for the requested ticker and attach it to the PipelineContext.

This version mirrors the comprehensive fetch flow used by the /ask pipeline: it prefers
an enhanced MarketDataService if available, and falls back to the project's
DataProviderAggregator. It collects current price, technical indicators and
historical data and stores them under context.processing_results['market_data'].
"""

from typing import Any, Dict
from src.shared.error_handling.logging import get_trading_logger
from ..pipeline import PipelineContext
import asyncio
import logging

from src.shared.data_pipeline.websocket_handler import PolygonWebSocketClient
from src.shared.config_loader import load_config  # Assuming config loader; adjust if needed


async def run(context: PipelineContext) -> PipelineContext:
    """Fetch market data and attach to context.processing_results['market_data'].

    The function attempts to use the centralized MarketDataService (used by /ask).
    If that's not importable, it falls back to DataProviderAggregator. It collects
    comprehensive stock data, technical indicators and historical data where available.
    """
    logger = get_trading_logger("fetch_data_stage")
    ticker = context.ticker
    logger.info(f"[fetch_data] Fetching comprehensive market data for {ticker}")

    try:
        # Prefer centralized MarketDataService if present (same service /ask uses)
        try:
            from src.api.data.market_data_service import MarketDataService as EnhancedMarketDataService
            market_service = EnhancedMarketDataService()
            logger.debug("[fetch_data] Using EnhancedMarketDataService")
        except Exception:
            market_service = None
            logger.debug("[fetch_data] EnhancedMarketDataService not available; will fallback to aggregator")

        market_data: Dict[str, Any] = {}

        if market_service and hasattr(market_service, 'get_comprehensive_stock_data'):
            # Use the single-call comprehensive fetch when available
            try:
                comprehensive = await asyncio.wait_for(
                    market_service.get_comprehensive_stock_data(ticker),
                    timeout=12.0
                )
                logger.info(f"[fetch_data] Retrieved comprehensive data for {ticker} from MarketDataService")
                # Merge returned comprehensive structure into market_data
                if isinstance(comprehensive, dict):
                    market_data.update(comprehensive)
                    market_data.setdefault('provider', 'market_data_service')
                    market_data.setdefault('status', 'success')
            except Exception as exc:
                logger.warning(f"[fetch_data] MarketDataService comprehensive fetch failed for {ticker}: {exc}")

        # If we don't yet have key pieces, fallback to aggregator
        if not market_data or market_data.get('status') != 'success':
            try:
                from src.shared.data_providers.aggregator import DataProviderAggregator
                aggregator = DataProviderAggregator()
                logger.info(f"[fetch_data] Falling back to DataProviderAggregator for {ticker}")

                agg_data = await asyncio.wait_for(aggregator.get_ticker(ticker), timeout=12.0)
                if isinstance(agg_data, dict):
                    market_data.update(agg_data)
                    market_data.setdefault('provider', market_data.get('provider', 'aggregator'))
                    market_data.setdefault('status', market_data.get('status', 'success' if agg_data else 'error'))
                else:
                    logger.warning(f"[fetch_data] Aggregator returned non-dict for {ticker}")
            except Exception as exc:
                logger.error(f"[fetch_data] Aggregator fetch failed for {ticker}: {exc}")
                context.error_log.append({
                    'stage': 'fetch_data',
                    'error_message': str(exc),
                    'error_type': type(exc).__name__,
                })

        # Ensure we have current price fields normalized
        # Normalize common variations used across codebase
        if 'current_price' in market_data and 'price' not in market_data:
            market_data['price'] = market_data['current_price']
        if 'change_percent' in market_data and 'change' not in market_data:
            market_data['change'] = market_data['change_percent']

        # Fetch technical indicators if not included
        try:
            if 'technical_indicators' not in market_data or not market_data.get('technical_indicators'):
                if market_service and hasattr(market_service, 'get_technical_indicators'):
                    ti = await asyncio.wait_for(market_service.get_technical_indicators(ticker), timeout=10.0)
                    market_data['technical_indicators'] = ti
                    logger.info(f"[fetch_data] Fetched technical indicators for {ticker} from MarketDataService")
                else:
                    # Aggregator may expose fetch_technical_indicators or fetch_technical_indicators
                    try:
                        if 'aggregator' in locals():
                            ti = await asyncio.wait_for(aggregator.get_technical_indicators(ticker), timeout=10.0)
                            market_data['technical_indicators'] = ti
                            logger.info(f"[fetch_data] Fetched technical indicators for {ticker} from Aggregator")
                    except Exception:
                        logger.debug(f"[fetch_data] Technical indicators not available from aggregator for {ticker}")
        except Exception as exc:
            logger.warning(f"[fetch_data] Technical indicators fetch failed: {exc}")

        # Fetch historical data if not present
        try:
            if 'historical' not in market_data or not market_data.get('historical'):
                if market_service and hasattr(market_service, 'get_historical_data'):
                    hist = await asyncio.wait_for(market_service.get_historical_data(ticker, days=30), timeout=12.0)
                    market_data['historical'] = hist
                    logger.info(f"[fetch_data] Retrieved historical data for {ticker} from MarketDataService")
                else:
                    if 'aggregator' in locals():
                        hist = await asyncio.wait_for(aggregator.get_history(ticker, period='1mo', interval='1d'), timeout=12.0)
                        market_data['historical'] = hist
                        logger.info(f"[fetch_data] Retrieved historical data for {ticker} from Aggregator")
        except Exception as exc:
            logger.warning(f"[fetch_data] Historical data fetch failed: {exc}")

        # Normalize market cap: accept various key names and compute if possible
        try:
            # Accept variations from different providers
            if 'market_cap' not in market_data or not market_data.get('market_cap'):
                for key in ('market_cap', 'marketCap', 'marketCapUSD', 'marketcap'):
                    if key in market_data and market_data.get(key):
                        market_data['market_cap'] = market_data.get(key)
                        break

            # If still missing or zero, try to compute from shares outstanding and current price
            if (not market_data.get('market_cap') or float(market_data.get('market_cap', 0)) == 0) and market_data.get('shares_outstanding'):
                try:
                    shares = float(market_data.get('shares_outstanding'))
                    price = float(market_data.get('current_price') or market_data.get('price') or 0)
                    if shares > 0 and price > 0:
                        market_data['market_cap'] = round(shares * price, 2)
                        logger.info(f"[fetch_data] Computed market_cap from shares_outstanding for {ticker}")
                except Exception:
                    # ignore compute failures
                    pass
        except Exception as exc:
            logger.debug(f"[fetch_data] Market cap normalization error: {exc}")

        from datetime import datetime
        market_data.setdefault('timestamp', datetime.utcnow().isoformat())

        # Final sanity defaults
        market_data.setdefault('symbol', ticker)
        market_data.setdefault('status', market_data.get('status', 'success'))

        # Enhanced data validation and fallback handling
        market_data = await _validate_and_enhance_market_data(market_data, ticker, logger)
        
        context.processing_results['market_data'] = market_data
        logger.info(f"[fetch_data] Completed comprehensive data collection for {ticker}")

        # Incorporate real-time updates if enabled
        config = load_config()  # Load from config.yaml
        if config.get('real_time', {}).get('enabled', False):
            try:
                api_key = config['real_time']['polygon_api_key']
                client = PolygonWebSocketClient(api_key=api_key)

                def merge_live_update(live_data: dict):
                    """Callback to merge live data into market_data."""
                    if 'price' in live_data:
                        market_data['live_price'] = live_data['price']
                        market_data['live_timestamp'] = live_data['timestamp']
                        logger.debug(f"[fetch_data] Merged live update for {ticker}: {live_data['price']}")
                    context.processing_results['market_data'] = market_data  # Update context

                client.on_update = merge_live_update

                # Start background task for WebSocket
                async def start_live_feed():
                    await client.connect()
                    await client.subscribe([ticker])
                    # Keep running until pipeline ends; in full impl, use context lifecycle

                # Create task; detach for now (in production, manage with context)
                live_task = asyncio.create_task(start_live_feed())
                context.processing_results['live_task'] = live_task  # Store for cleanup
                logger.info(f"[fetch_data] Started real-time feed for {ticker}")

            except Exception as live_exc:
                logger.warning(f"[fetch_data] Failed to start real-time feed for {ticker}: {live_exc}")

    except Exception as exc:
        logger.error(f"[fetch_data] Unexpected error while fetching data for {ticker}: {exc}")
        context.error_log.append({
            'stage': 'fetch_data',
            'error_message': str(exc),
            'error_type': type(exc).__name__,
        })

    return context


async def _validate_and_enhance_market_data(market_data: Dict[str, Any], ticker: str, logger) -> Dict[str, Any]:
    """
    Validate and enhance market data with fallback mechanisms for insufficient data scenarios.
    
    Args:
        market_data: Raw market data dictionary
        ticker: Stock symbol
        logger: Logger instance
        
    Returns:
        Enhanced and validated market data dictionary
    """
    # Check if we have sufficient data for technical analysis
    has_sufficient_data = _check_data_sufficiency(market_data)
    
    if not has_sufficient_data:
        logger.warning(f"[fetch_data] Insufficient data for {ticker}, attempting to enhance with fallback strategies")
        
        # Try to get more historical data if we have some but not enough
        if 'historical' in market_data and market_data['historical']:
            market_data = await _enhance_with_extended_history(market_data, ticker, logger)
        
        # If still insufficient, try alternative data sources
        if not _check_data_sufficiency(market_data):
            market_data = await _apply_alternative_data_strategies(market_data, ticker, logger)
    
    # Add data quality metrics
    market_data['data_quality'] = _calculate_data_quality_metrics(market_data)
    
    return market_data


def _check_data_sufficiency(market_data: Dict[str, Any]) -> bool:
    """
    Check if market data is sufficient for technical analysis.
    
    Args:
        market_data: Market data dictionary
        
    Returns:
        True if sufficient, False otherwise
    """
    # Check for required fields
    required_fields = ['price', 'symbol']
    for field in required_fields:
        if not market_data.get(field):
            return False
    
    # Check for historical data sufficiency
    historical = market_data.get('historical')
    if historical:
        # For technical indicators, we typically need at least 14-50 data points
        if isinstance(historical, dict) and 'closes' in historical:
            closes = historical['closes']
            if len(closes) >= 14:  # Minimum for RSI
                return True
        elif hasattr(historical, '__len__') and len(historical) >= 14:
            return True
    
    # Check for direct price series
    close_prices = market_data.get('close')
    if close_prices and len(close_prices) >= 14:
        return True
    
    return False


async def _enhance_with_extended_history(market_data: Dict[str, Any], ticker: str, logger) -> Dict[str, Any]:
    """
    Try to get extended historical data to improve data sufficiency.
    
    Args:
        market_data: Current market data
        ticker: Stock symbol
        logger: Logger instance
        
    Returns:
        Enhanced market data with extended history
    """
    try:
        # Try to get more historical data
        from src.shared.data_providers.aggregator import DataProviderAggregator
        aggregator = DataProviderAggregator()
        
        # Try with longer period
        extended_hist = await aggregator.get_history(ticker, period='3mo', interval='1d')
        if extended_hist and 'error' not in extended_hist:
            market_data['historical'] = extended_hist
            logger.info(f"[fetch_data] Enhanced historical data for {ticker} with 3-month period")
    except Exception as e:
        logger.debug(f"[fetch_data] Failed to enhance history for {ticker}: {e}")
    
    return market_data


async def _apply_alternative_data_strategies(market_data: Dict[str, Any], ticker: str, logger) -> Dict[str, Any]:
    """
    Apply alternative data strategies when primary data is insufficient.
    
    Args:
        market_data: Current market data
        ticker: Stock symbol
        logger: Logger instance
        
    Returns:
        Market data with alternative strategies applied
    """
    try:
        # Try to get basic price data even if technical analysis fails
        from src.shared.data_providers.aggregator import DataProviderAggregator
        aggregator = DataProviderAggregator()
        
        # Try different providers
        providers = ['yfinance', 'alpha_vantage', 'finnhub']
        for provider in providers:
            try:
                basic_data = await aggregator.get_ticker(ticker, preferred_provider=provider)
                if basic_data and 'error' not in basic_data:
                    # Merge with existing data
                    market_data.update(basic_data)
                    logger.info(f"[fetch_data] Got basic data for {ticker} from {provider}")
                    break
            except Exception:
                continue
                
        # If we still don't have price data, create minimal valid structure
        if not market_data.get('price'):
            market_data.update({
                'price': 0.0,
                'symbol': ticker,
                'status': 'insufficient_data',
                'warning': 'Insufficient market data available for analysis'
            })
            logger.warning(f"[fetch_data] Created minimal data structure for {ticker} due to insufficient data")
            
    except Exception as e:
        logger.error(f"[fetch_data] Error applying alternative strategies for {ticker}: {e}")
    
    return market_data


def _calculate_data_quality_metrics(market_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Calculate data quality metrics for the market data.
    
    Args:
        market_data: Market data dictionary
        
    Returns:
        Data quality metrics dictionary
    """
    quality_metrics = {
        'completeness': 0,
        'accuracy': 0,
        'timeliness': 0,
        'consistency': 0
    }
    
    # Calculate completeness
    required_fields = ['price', 'symbol', 'timestamp']
    available_fields = [field for field in required_fields if market_data.get(field) is not None]
    quality_metrics['completeness'] = (len(available_fields) / len(required_fields)) * 100
    
    # Calculate data points count for technical analysis
    data_points = 0
    if 'historical' in market_data:
        historical = market_data['historical']
        if isinstance(historical, dict) and 'closes' in historical:
            data_points = len(historical['closes'])
        elif hasattr(historical, '__len__'):
            data_points = len(historical)
    elif 'close' in market_data:
        data_points = len(market_data['close'])
    
    quality_metrics['data_points'] = data_points
    quality_metrics['technical_analysis_possible'] = data_points >= 14
    
    # Add overall quality score
    quality_metrics['overall_score'] = int(
        (quality_metrics['completeness'] + 
         (100 if data_points >= 14 else 0) + 
         quality_metrics['completeness']) / 3
    )
    
    return quality_metrics 