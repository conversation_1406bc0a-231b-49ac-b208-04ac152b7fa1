"""
Pipeline Engine

Core pipeline execution engine that orchestrates stages for Discord bot commands.
Each command gets its own pipeline instance with configurable stages.
"""

import asyncio
import time
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Callable, Union
from dataclasses import dataclass
import logging
import re

from .circuit_breaker import (
    CircuitBreaker, CircuitBreakerConfig, CircuitBreakerOpenError,
    get_circuit_breaker, with_circuit_breaker
)

from .context_manager import PipelineContext, PipelineStatus, AuditEntry

logger = logging.getLogger(__name__)


@dataclass
class StageResult:
    """Result from a pipeline stage execution"""
    success: bool
    output_data: Dict[str, Any]
    execution_time: float
    error_message: Optional[str] = None
    confidence_score: float = 0.0
    metadata: Dict[str, Any] = None
    circuit_breaker_metrics: Optional[Dict[str, Any]] = None


class BasePipelineStage(ABC):
    """
    Abstract base class for all pipeline stages
    
    Each stage must implement the process method and can optionally
    implement pre_process and post_process hooks.
    """
    
    def __init__(self, name: str, config: Dict[str, Any] = None):
        self.name = name
        self.config = config or {}
        self.required_inputs: List[str] = []
        self.optional_inputs: List[str] = []
        self.outputs: List[str] = []
        
        # Circuit breaker configuration
        self.use_circuit_breaker = config.get('use_circuit_breaker', True)
        self.circuit_breaker_name = f"stage_{name}"
        self.circuit_breaker_config = CircuitBreakerConfig(
            failure_threshold=config.get('circuit_breaker_failure_threshold', 3),
            recovery_timeout=config.get('circuit_breaker_recovery_timeout', 60.0),
            timeout=config.get('circuit_breaker_timeout', 30.0)
        )
        
    async def execute(self, context: PipelineContext) -> StageResult:
        """Execute the stage with full lifecycle management"""
        start_time = time.time()
        circuit_breaker_metrics = None
        
        try:
            # Pre-process validation
            if not await self.pre_process(context):
                return StageResult(
                    success=False,
                    output_data={},
                    execution_time=time.time() - start_time,
                    error_message="Pre-process validation failed"
                )
            
            # Main processing with circuit breaker if enabled
            if self.use_circuit_breaker:
                try:
                    # Get or create circuit breaker
                    circuit_breaker = get_circuit_breaker(
                        self.circuit_breaker_name,
                        self.circuit_breaker_config
                    )
                    
                    # Execute with circuit breaker protection
                    result = await circuit_breaker.execute(self.process, context)
                    
                    # Capture circuit breaker metrics
                    circuit_breaker_metrics = circuit_breaker.get_metrics()
                    
                except CircuitBreakerOpenError as e:
                    logger.warning(f"Circuit breaker open for stage {self.name}: {e}")
                    return StageResult(
                        success=False,
                        output_data={},
                        execution_time=time.time() - start_time,
                        error_message=f"Circuit breaker open: {str(e)}",
                        circuit_breaker_metrics=get_circuit_breaker(self.circuit_breaker_name).get_metrics()
                    )
            else:
                # Execute without circuit breaker
                result = await self.process(context)
            
            # Post-process cleanup
            await self.post_process(context, result)
            
            execution_time = time.time() - start_time
            
            # Update context
            context.update_stage(self.name, execution_time)
            context.add_audit_entry(
                stage=self.name,
                action="execute",
                input_data=self._get_input_data(context),
                output_data=result.output_data,
                execution_time=execution_time,
                success=result.success,
                error_details=result.error_message,
                confidence=result.confidence_score,
                circuit_breaker_metrics=circuit_breaker_metrics
            )
            
            # Add circuit breaker metrics to result if available
            if circuit_breaker_metrics and not hasattr(result, 'circuit_breaker_metrics'):
                result.circuit_breaker_metrics = circuit_breaker_metrics
                
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            # Secure error handling - don't expose internal details
            error_msg = f"Stage {self.name} execution failed"
            logger.error(f"{error_msg}: {str(e)}", exc_info=True)
            
            # Update context with error (sanitized)
            context.add_error(self.name, e, error_msg)
            
            return StageResult(
                success=False,
                output_data={},
                execution_time=execution_time,
                error_message=error_msg
            )
    
    async def pre_process(self, context: PipelineContext) -> bool:
        """Pre-process validation and setup"""
        # Check if required inputs are available
        for input_key in self.required_inputs:
            # Check all possible locations in context
            if (input_key not in context.collected_data and 
                input_key not in context.validated_data and
                input_key not in context.processing_results and
                input_key not in context.ai_responses and
                not hasattr(context, input_key)):
                logger.warning(f"Stage {self.name}: Required input '{input_key}' not available")
                logger.warning(f"Stage {self.name}: context.collected_data keys: {list(context.collected_data.keys())}")
                logger.warning(f"Stage {self.name}: context.validated_data keys: {list(context.validated_data.keys())}")
                logger.warning(f"Stage {self.name}: context.processing_results keys: {list(context.processing_results.keys())}")
                logger.warning(f"Stage {self.name}: context.ai_responses keys: {list(context.ai_responses.keys())}")
                return False
        return True
    
    @abstractmethod
    async def process(self, context: PipelineContext) -> StageResult:
        """Main stage processing logic - must be implemented by subclasses"""
        pass
    
    async def post_process(self, context: PipelineContext, result: StageResult):
        """Post-process cleanup and context updates"""
        if result.success:
            # Sanitize and validate output data before storing
            sanitized_data = self._validate_output_data(result.output_data)
            
            # Update context with stage outputs
            for output_key in self.outputs:
                if output_key in sanitized_data:
                    logger.info(f"Stage {self.name} post_process: Processing output_key: {output_key}")
                    
                    # FIX: Always store outputs in processing_results for consistency
                    # This ensures all stages can access data in the same way
                    if output_key.startswith('quality_'):
                        logger.info(f"Stage {self.name} post_process: Storing in context.quality_scores[{output_key}]")
                        context.quality_scores[output_key] = sanitized_data[output_key]
                    elif output_key.startswith('ai_'):
                        logger.info(f"Stage {self.name} post_process: Storing in context.ai_responses[{output_key}]")
                        context.ai_responses[output_key] = sanitized_data[output_key]
                    else:
                        logger.info(f"Stage {self.name} post_process: Storing in context.processing_results[{output_key}]")
                        context.processing_results[output_key] = sanitized_data[output_key]
                    
                    # Also set as attribute for backward compatibility, but primary storage is in processing_results
                    if hasattr(context, output_key):
                        logger.info(f"Stage {self.name} post_process: Also setting attribute context.{output_key}")
                        setattr(context, output_key, sanitized_data[output_key])
            
            # Debug logging
            logger.info(f"Stage {self.name} post_process: Stored outputs {self.outputs} in context")
            logger.info(f"Stage {self.name} post_process: context.processing_results keys: {list(context.processing_results.keys())}")
            logger.info(f"Stage {self.name} post_process: context.processing_results content: {context.processing_results}")
            
            # Immediate verification
            for output_key in self.outputs:
                if output_key in sanitized_data:
                    logger.info(f"Stage {self.name} post_process: VERIFICATION - stored {output_key} = {sanitized_data.get(output_key, 'NOT_FOUND')}")
                    if output_key.startswith('quality_'):
                        logger.info(f"Stage {self.name} post_process: VERIFICATION - context.quality_scores[{output_key}] = {context.quality_scores.get(output_key, 'NOT_FOUND')}")
                    elif output_key.startswith('ai_'):
                        logger.info(f"Stage {self.name} post_process: VERIFICATION - context.ai_responses[{output_key}] = {context.ai_responses.get(output_key, 'NOT_FOUND')}")
                    else:
                        logger.info(f"Stage {self.name} post_process: VERIFICATION - context.processing_results[{output_key}] = {context.processing_results.get(output_key, 'NOT_FOUND')}")
    
    def _get_input_data(self, context: PipelineContext) -> Dict[str, Any]:
        """Get input data for this stage from context"""
        input_data = {}
        
        # Check collected data
        for key in self.required_inputs + self.optional_inputs:
            if key in context.collected_data:
                input_data[key] = context.collected_data[key]
            elif key in context.validated_data:
                input_data[key] = context.validated_data[key]
            elif key in context.processing_results:
                input_data[key] = context.processing_results[key]
        
        return input_data

    def _sanitize_output_key(self, key: str) -> str:
        """Sanitize output key to prevent injection attacks"""
        # Only allow alphanumeric and underscores, max 50 characters
        if not re.match(r'^[a-zA-Z0-9_]{1,50}$', key):
            raise ValueError(f"Invalid output key: {key} - only alphanumeric and underscores allowed, max 50 chars")
        return key
    
    def _validate_output_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and sanitize output data"""
        sanitized_data = {}
        for key, value in data.items():
            try:
                sanitized_key = self._sanitize_output_key(key)
                # Basic type validation
                if isinstance(value, (str, int, float, bool, list, dict)) or value is None:
                    sanitized_data[sanitized_key] = value
                else:
                    # Convert to string for safety
                    sanitized_data[sanitized_key] = str(value)
                    logger.warning(f"Output value for key '{sanitized_key}' converted to string for safety")
            except ValueError as e:
                logger.error(f"Output key validation failed: {e}")
                continue
        return sanitized_data


class PipelineEngine:
    """
    Main pipeline execution engine
    
    Orchestrates the execution of multiple stages in sequence or parallel,
    managing the flow of data through the pipeline.
    """
    
    def __init__(self, name: str, stages: List[BasePipelineStage] = None, config: Dict[str, Any] = None):
        self.name = name
        self.stages = stages or []
        self.config = config or {}
        self.max_execution_time: float = 30.0
        self.parallel_execution: bool = False
        
        # Circuit breaker for the entire pipeline
        self.use_circuit_breaker = True
        self.circuit_breaker_name = f"pipeline_{name}"
        self.circuit_breaker_config = CircuitBreakerConfig(
            failure_threshold=5,
            recovery_timeout=120.0,
            timeout=self.max_execution_time
        )
        
    def add_stage(self, stage: BasePipelineStage):
        """Add a stage to the pipeline"""
        self.stages.append(stage)
    
    def add_stages(self, stages: List[BasePipelineStage]):
        """Add multiple stages to the pipeline"""
        self.stages.extend(stages)
    
    async def execute(self, context: PipelineContext) -> PipelineContext:
        """Execute the complete pipeline"""
        if not self.stages:
            logger.warning(f"Pipeline {self.name}: No stages configured")
            return context
        
        context.command_name = self.name
        context.status = PipelineStatus.RUNNING
        context.max_execution_time = self.max_execution_time
        
        logger.info(f"Starting pipeline {self.name} with {len(self.stages)} stages")
        
        # Execute with circuit breaker if enabled
        if self.use_circuit_breaker:
            try:
                # Get or create circuit breaker for the pipeline
                circuit_breaker = get_circuit_breaker(
                    self.circuit_breaker_name,
                    self.circuit_breaker_config
                )
                
                # Execute with circuit breaker protection
                if self.parallel_execution:
                    await circuit_breaker.execute(self._execute_parallel, context)
                else:
                    await circuit_breaker.execute(self._execute_sequential, context)
                    
                # Add circuit breaker metrics to context
                context.circuit_breaker_metrics = circuit_breaker.get_metrics()
                
            except CircuitBreakerOpenError as e:
                logger.error(f"Pipeline {self.name} circuit breaker open: {e}")
                context.add_error("pipeline", e, f"Circuit breaker open: {str(e)}")
                context.status = PipelineStatus.FAILED
                context.circuit_breaker_metrics = get_circuit_breaker(self.circuit_breaker_name).get_metrics()
            except Exception as e:
                logger.error(f"Pipeline {self.name} failed: {str(e)}", exc_info=True)
                context.add_error("pipeline", e, "Pipeline execution failed")
                context.status = PipelineStatus.FAILED
        else:
            # Execute without circuit breaker
            try:
                if self.parallel_execution:
                    await self._execute_parallel(context)
                else:
                    await self._execute_sequential(context)
                    
            except Exception as e:
                logger.error(f"Pipeline {self.name} failed: {str(e)}", exc_info=True)
                context.add_error("pipeline", e, "Pipeline execution failed")
                context.status = PipelineStatus.FAILED
        
        # Finalize pipeline
        context.finalize()
        
        logger.info(f"Pipeline {self.name} completed with status: {context.status.value}")
        return context
    
    async def _execute_sequential(self, context: PipelineContext):
        """Execute stages sequentially"""
        for stage in self.stages:
            if not context.can_continue():
                logger.warning(f"Pipeline {self.name}: Stopping execution due to errors or limits")
                break
            
            logger.debug(f"Executing stage: {stage.name}")
            result = await stage.execute(context)
            
            # Merge stage outputs into context for next stages to use
            if result.success and result.output_data:
                context.processing_results.update(result.output_data)
                logger.debug(f"Stage {stage.name} outputs merged: {list(result.output_data.keys())}")
            
            if not result.success:
                logger.error(f"Stage {stage.name} failed: {result.error_message}")
                # Continue to next stage unless it's critical
                if result.error_message and "critical" in result.error_message.lower():
                    context.should_continue = False
    
    async def _execute_parallel(self, context: PipelineContext):
        """Execute stages in parallel where possible"""
        # Group stages by dependencies (simplified for now)
        # In a real implementation, you'd build a dependency graph
        
        # For now, execute in parallel if no dependencies
        tasks = []
        for stage in self.stages:
            if context.can_continue():
                task = asyncio.create_task(stage.execute(context))
                tasks.append(task)
        
        if tasks:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Process results
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"Stage {self.stages[i].name} failed: {str(result)}")
                    context.add_error(self.stages[i].name, result, "Parallel execution failed")
                elif not result.success:
                    logger.error(f"Stage {self.stages[i].name} failed: {result.error_message}")
    
    def get_pipeline_summary(self, context: PipelineContext) -> Dict[str, Any]:
        """Get a summary of pipeline execution"""
        summary = {
            "pipeline_name": self.name,
            "status": context.status.value,
            "total_stages": len(self.stages),
            "executed_stages": len(context.stage_history),
            "total_time": context.total_execution_time,
            "overall_quality": context.get_overall_quality().quality_level.value,
            "error_count": len(context.error_log),
            "success_rate": len([s for s in context.stage_history if s not in context.error_log]) / len(context.stage_history) if context.stage_history else 0
        }
        
        # Add circuit breaker metrics if available
        if hasattr(context, 'circuit_breaker_metrics') and context.circuit_breaker_metrics:
            summary["circuit_breaker"] = context.circuit_breaker_metrics
            
        return summary


class PipelineBuilder:
    """Builder pattern for creating pipelines"""
    
    def __init__(self, name: str):
        self.pipeline = PipelineEngine(name)
    
    def add_stage(self, stage: BasePipelineStage) -> 'PipelineBuilder':
        """Add a stage to the pipeline"""
        self.pipeline.add_stage(stage)
        return self
    
    def add_stages(self, stages: List[BasePipelineStage]) -> 'PipelineBuilder':
        """Add multiple stages to the pipeline"""
        self.pipeline.add_stages(stages)
        return self
    
    def set_config(self, config: Dict[str, Any]) -> 'PipelineBuilder':
        """Set pipeline configuration"""
        self.pipeline.config = config
        return self
    
    def set_max_execution_time(self, max_time: float) -> 'PipelineBuilder':
        """Set maximum execution time"""
        self.pipeline.max_execution_time = max_time
        self.pipeline.circuit_breaker_config.timeout = max_time
        return self
    
    def enable_parallel_execution(self) -> 'PipelineBuilder':
        """Enable parallel execution where possible"""
        self.pipeline.parallel_execution = True
        return self
    
    def configure_circuit_breaker(self, 
                                 enabled: bool = True,
                                 failure_threshold: int = 5,
                                 recovery_timeout: float = 120.0) -> 'PipelineBuilder':
        """Configure circuit breaker for the pipeline"""
        self.pipeline.use_circuit_breaker = enabled
        self.pipeline.circuit_breaker_config.failure_threshold = failure_threshold
        self.pipeline.circuit_breaker_config.recovery_timeout = recovery_timeout
        return self
    
    def build(self) -> PipelineEngine:
        """Build and return the configured pipeline"""
        return self.pipeline 