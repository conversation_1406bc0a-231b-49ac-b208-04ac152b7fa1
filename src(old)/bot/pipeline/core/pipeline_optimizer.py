"""
Pipeline Execution Optimizer

Provides intelligent optimization for pipeline execution including:
- Dependency analysis and parallel execution planning
- Performance monitoring and bottleneck detection
- Resource usage optimization
- Execution time prediction and optimization
"""

import asyncio
import time
import logging
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict, deque

logger = logging.getLogger(__name__)

class OptimizationStrategy(Enum):
    """Pipeline optimization strategies"""
    SEQUENTIAL = "sequential"  # Original sequential execution
    PARALLEL_BATCHES = "parallel_batches"  # Parallel execution within dependency batches
    AGGRESSIVE_PARALLEL = "aggressive_parallel"  # Maximum parallelization
    ADAPTIVE = "adaptive"  # Adaptive based on performance history

@dataclass
class ExecutionMetrics:
    """Metrics for pipeline execution"""
    total_time: float = 0.0
    stage_times: Dict[str, float] = field(default_factory=dict)
    parallel_efficiency: float = 0.0
    bottleneck_stages: List[str] = field(default_factory=list)
    dependency_violations: int = 0
    cache_hit_rate: float = 0.0
    resource_usage: Dict[str, float] = field(default_factory=dict)

@dataclass
class OptimizationPlan:
    """Execution plan with optimization details"""
    strategy: OptimizationStrategy
    execution_batches: List[List[str]]
    estimated_time: float
    parallelization_factor: float
    critical_path: List[str]
    optimization_notes: List[str] = field(default_factory=list)

class PipelineOptimizer:
    """Intelligent pipeline execution optimizer"""
    
    def __init__(self):
        self.execution_history: Dict[str, List[ExecutionMetrics]] = defaultdict(list)
        self.dependency_cache: Dict[str, Set[str]] = {}
        self.performance_baselines: Dict[str, float] = {}
        self.optimization_enabled = True
        
    def analyze_dependencies(self, stages: Dict[str, Any]) -> Dict[str, Set[str]]:
        """Analyze stage dependencies and build dependency graph"""
        dependency_graph = {}
        
        for stage_id, stage in stages.items():
            # Get dependencies from stage configuration
            dependencies = set()
            if hasattr(stage, 'dependencies'):
                dependencies = set(stage.dependencies)
            elif isinstance(stage, dict) and 'dependencies' in stage:
                dependencies = set(stage['dependencies'])
            
            dependency_graph[stage_id] = dependencies
            
        # Cache for future use
        pipeline_key = "_".join(sorted(stages.keys()))
        self.dependency_cache[pipeline_key] = dependency_graph
        
        return dependency_graph
    
    def generate_optimization_plan(
        self, 
        stages: Dict[str, Any], 
        strategy: OptimizationStrategy = OptimizationStrategy.ADAPTIVE
    ) -> OptimizationPlan:
        """Generate an optimized execution plan"""
        
        # Analyze dependencies
        dependency_graph = self.analyze_dependencies(stages)
        
        # Generate execution batches based on strategy
        if strategy == OptimizationStrategy.SEQUENTIAL:
            batches = self._generate_sequential_batches(stages, dependency_graph)
        elif strategy == OptimizationStrategy.PARALLEL_BATCHES:
            batches = self._generate_parallel_batches(stages, dependency_graph)
        elif strategy == OptimizationStrategy.AGGRESSIVE_PARALLEL:
            batches = self._generate_aggressive_parallel_batches(stages, dependency_graph)
        else:  # ADAPTIVE
            batches = self._generate_adaptive_batches(stages, dependency_graph)
        
        # Calculate critical path
        critical_path = self._calculate_critical_path(stages, dependency_graph)
        
        # Estimate execution time
        estimated_time = self._estimate_execution_time(stages, batches)
        
        # Calculate parallelization factor
        total_stages = len(stages)
        max_batch_size = max(len(batch) for batch in batches) if batches else 1
        parallelization_factor = max_batch_size / total_stages if total_stages > 0 else 0
        
        # Generate optimization notes
        notes = self._generate_optimization_notes(stages, batches, critical_path)
        
        return OptimizationPlan(
            strategy=strategy,
            execution_batches=batches,
            estimated_time=estimated_time,
            parallelization_factor=parallelization_factor,
            critical_path=critical_path,
            optimization_notes=notes
        )
    
    def _generate_parallel_batches(self, stages: Dict[str, Any], dependency_graph: Dict[str, Set[str]]) -> List[List[str]]:
        """Generate parallel execution batches using topological sort"""
        batches = []
        remaining_stages = set(stages.keys())
        
        while remaining_stages:
            # Find stages with no unmet dependencies
            ready_stages = []
            for stage_id in remaining_stages:
                unmet_deps = dependency_graph[stage_id] & remaining_stages
                if not unmet_deps:
                    ready_stages.append(stage_id)
            
            if not ready_stages:
                # Circular dependency - break by taking stage with fewest dependencies
                min_deps = min(len(dependency_graph[stage_id] & remaining_stages) for stage_id in remaining_stages)
                ready_stages = [
                    stage_id for stage_id in remaining_stages 
                    if len(dependency_graph[stage_id] & remaining_stages) == min_deps
                ][:1]  # Take only one to break the cycle
                logger.warning(f"Circular dependency detected, forcing execution of {ready_stages[0]}")
            
            # Add this batch and remove from remaining
            batches.append(ready_stages)
            remaining_stages -= set(ready_stages)
        
        return batches
    
    def _generate_sequential_batches(self, stages: Dict[str, Any], dependency_graph: Dict[str, Set[str]]) -> List[List[str]]:
        """Generate sequential execution (one stage per batch)"""
        # Use topological sort but create single-stage batches
        parallel_batches = self._generate_parallel_batches(stages, dependency_graph)
        sequential_batches = []
        for batch in parallel_batches:
            for stage in batch:
                sequential_batches.append([stage])
        return sequential_batches
    
    def _generate_aggressive_parallel_batches(self, stages: Dict[str, Any], dependency_graph: Dict[str, Set[str]]) -> List[List[str]]:
        """Generate aggressive parallel execution (minimize batches)"""
        # Same as parallel batches but with more aggressive grouping
        return self._generate_parallel_batches(stages, dependency_graph)
    
    def _generate_adaptive_batches(self, stages: Dict[str, Any], dependency_graph: Dict[str, Set[str]]) -> List[List[str]]:
        """Generate adaptive execution plan based on performance history"""
        # Start with parallel batches
        batches = self._generate_parallel_batches(stages, dependency_graph)
        
        # Adjust based on performance history
        pipeline_key = "_".join(sorted(stages.keys()))
        if pipeline_key in self.execution_history:
            history = self.execution_history[pipeline_key]
            if history:
                # If we have performance issues, be more conservative
                avg_efficiency = sum(m.parallel_efficiency for m in history[-5:]) / min(5, len(history))
                if avg_efficiency < 0.7:  # Low efficiency
                    # Split large batches
                    new_batches = []
                    for batch in batches:
                        if len(batch) > 2:
                            # Split into smaller batches
                            for i in range(0, len(batch), 2):
                                new_batches.append(batch[i:i+2])
                        else:
                            new_batches.append(batch)
                    batches = new_batches
        
        return batches
    
    def _calculate_critical_path(self, stages: Dict[str, Any], dependency_graph: Dict[str, Set[str]]) -> List[str]:
        """Calculate the critical path through the pipeline"""
        # Simple implementation - find longest dependency chain
        def get_depth(stage_id: str, visited: Set[str] = None) -> int:
            if visited is None:
                visited = set()
            if stage_id in visited:
                return 0  # Circular dependency
            visited.add(stage_id)
            
            deps = dependency_graph.get(stage_id, set())
            if not deps:
                return 1
            
            max_depth = max(get_depth(dep, visited.copy()) for dep in deps)
            return max_depth + 1
        
        # Find stage with maximum depth
        depths = {stage_id: get_depth(stage_id) for stage_id in stages.keys()}
        critical_stage = max(depths, key=depths.get)
        
        # Build critical path
        path = []
        current = critical_stage
        visited = set()
        
        while current and current not in visited:
            path.append(current)
            visited.add(current)
            deps = dependency_graph.get(current, set())
            if deps:
                # Choose dependency with maximum depth
                current = max(deps, key=lambda x: depths.get(x, 0))
            else:
                break
        
        return list(reversed(path))
    
    def _estimate_execution_time(self, stages: Dict[str, Any], batches: List[List[str]]) -> float:
        """Estimate total execution time for the plan"""
        total_time = 0.0
        
        for batch in batches:
            # For parallel batch, time is the maximum time of stages in the batch
            batch_time = 0.0
            for stage_id in batch:
                # Use historical data if available, otherwise use default estimate
                stage_time = self.performance_baselines.get(stage_id, 5.0)  # Default 5 seconds
                batch_time = max(batch_time, stage_time)
            
            total_time += batch_time
        
        return total_time
    
    def _generate_optimization_notes(self, stages: Dict[str, Any], batches: List[List[str]], critical_path: List[str]) -> List[str]:
        """Generate human-readable optimization notes"""
        notes = []
        
        # Parallelization analysis
        total_stages = len(stages)
        parallel_stages = sum(len(batch) for batch in batches if len(batch) > 1)
        if parallel_stages > 0:
            notes.append(f"Parallelizing {parallel_stages}/{total_stages} stages across {len(batches)} batches")
        
        # Critical path analysis
        if critical_path:
            notes.append(f"Critical path: {' → '.join(critical_path)} ({len(critical_path)} stages)")
        
        # Bottleneck identification
        large_batches = [batch for batch in batches if len(batch) > 3]
        if large_batches:
            notes.append(f"Large parallel batches detected: {len(large_batches)} batches with 3+ stages")
        
        # Dependency analysis
        total_deps = sum(len(dep_graph.get(stage_id, set())) for stage_id in stages.keys() for dep_graph in [self.analyze_dependencies(stages)])
        if total_deps == 0:
            notes.append("No dependencies detected - maximum parallelization possible")
        elif total_deps > len(stages):
            notes.append("Complex dependency structure detected - limited parallelization")
        
        return notes
    
    def record_execution_metrics(self, pipeline_key: str, metrics: ExecutionMetrics):
        """Record execution metrics for future optimization"""
        self.execution_history[pipeline_key].append(metrics)
        
        # Keep only recent history (last 10 executions)
        if len(self.execution_history[pipeline_key]) > 10:
            self.execution_history[pipeline_key] = self.execution_history[pipeline_key][-10:]
        
        # Update performance baselines
        for stage_id, stage_time in metrics.stage_times.items():
            if stage_id not in self.performance_baselines:
                self.performance_baselines[stage_id] = stage_time
            else:
                # Exponential moving average
                self.performance_baselines[stage_id] = 0.7 * self.performance_baselines[stage_id] + 0.3 * stage_time
    
    def get_optimization_summary(self, pipeline_key: str) -> Dict[str, Any]:
        """Get optimization summary for a pipeline"""
        history = self.execution_history.get(pipeline_key, [])
        if not history:
            return {"status": "no_history", "executions": 0}
        
        recent_metrics = history[-5:]  # Last 5 executions
        
        avg_time = sum(m.total_time for m in recent_metrics) / len(recent_metrics)
        avg_efficiency = sum(m.parallel_efficiency for m in recent_metrics) / len(recent_metrics)
        
        return {
            "status": "optimized",
            "executions": len(history),
            "avg_execution_time": avg_time,
            "avg_parallel_efficiency": avg_efficiency,
            "performance_trend": "improving" if len(history) > 1 and history[-1].total_time < history[-2].total_time else "stable",
            "bottleneck_stages": list(set(stage for m in recent_metrics for stage in m.bottleneck_stages))
        }

# Global optimizer instance
pipeline_optimizer = PipelineOptimizer()

def get_pipeline_optimizer() -> PipelineOptimizer:
    """Get the global pipeline optimizer instance"""
    return pipeline_optimizer
