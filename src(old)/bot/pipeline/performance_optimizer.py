"""
AI Pipeline Performance Optimizer
Implements caching, connection pooling, and performance enhancements
"""

import asyncio
import time
import json
import hashlib
from typing import Dict, Any, Optional, List, Tuple, TYPE_CHECKING
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import logging

if TYPE_CHECKING:
    from .performance_optimizer import PipelineOptimizer

logger = logging.getLogger(__name__)

@dataclass
class CacheEntry:
    """Cache entry with metadata"""
    data: Any
    created_at: datetime
    access_count: int = 0
    last_accessed: Optional[datetime] = None
    ttl_seconds: int = 300  # 5 minutes default
    
    def __post_init__(self):
        if self.last_accessed is None:
            self.last_accessed = self.created_at
    
    def is_expired(self) -> bool:
        """Check if cache entry is expired"""
        return datetime.now() > self.created_at + timedelta(seconds=self.ttl_seconds)
    
    def access(self):
        """Record cache access"""
        self.access_count += 1
        self.last_accessed = datetime.now()

class IntelligentCache:
    """High-performance cache with intelligent eviction"""
    
    def __init__(self, max_size: int = 1000, default_ttl: int = 300):
        self.max_size = max_size
        self.default_ttl = default_ttl
        self._cache: Dict[str, CacheEntry] = {}
        self._access_order: List[str] = []
        self._lock = asyncio.Lock()
    
    def _generate_key(self, query: str, context: Optional[Dict[str, Any]] = None) -> str:
        """Generate cache key from query and context"""
        key_data = {
            'query': query.lower().strip(),
            'context': context or {}
        }
        key_string = json.dumps(key_data, sort_keys=True)
        return hashlib.sha256(key_string.encode()).hexdigest()[:16]
    
    async def get(self, query: str, context: Optional[Dict[str, Any]] = None) -> Optional[Any]:
        """Get cached result"""
        async with self._lock:
            key = self._generate_key(query, context or {})
            
            if key not in self._cache:
                return None
            
            entry = self._cache[key]
            
            # Check if expired
            if entry.is_expired():
                del self._cache[key]
                if key in self._access_order:
                    self._access_order.remove(key)
                return None
            
            # Record access
            entry.access()
            
            # Update access order (LRU)
            if key in self._access_order:
                self._access_order.remove(key)
            self._access_order.append(key)
            
            logger.debug(f"Cache hit for key {key}")
            return entry.data
    
    async def set(self, query: str, data: Any, context: Optional[Dict[str, Any]] = None, ttl: Optional[int] = None) -> None:
        """Set cached result"""
        async with self._lock:
            key = self._generate_key(query, context or {})
            ttl_value = ttl if ttl is not None else self.default_ttl
            
            # Evict if at capacity
            if len(self._cache) >= self.max_size:
                await self._evict_lru()
            
            entry = CacheEntry(
                data=data,
                created_at=datetime.now(),
                ttl_seconds=ttl_value
            )
            
            self._cache[key] = entry
            self._access_order.append(key)
            
            logger.debug(f"Cache set for key {key}")
    
    async def _evict_lru(self) -> None:
        """Evict least recently used entries"""
        if not self._access_order:
            return
        
        # Remove oldest 10% of entries
        evict_count = max(1, len(self._access_order) // 10)
        
        for _ in range(evict_count):
            if self._access_order:
                oldest_key = self._access_order.pop(0)
                if oldest_key in self._cache:
                    del self._cache[oldest_key]
    
    async def clear_expired(self) -> int:
        """Clear expired entries and return count"""
        async with self._lock:
            expired_keys = [
                key for key, entry in self._cache.items()
                if entry.is_expired()
            ]
            
            for key in expired_keys:
                del self._cache[key]
                if key in self._access_order:
                    self._access_order.remove(key)
            
            return len(expired_keys)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        total_access = sum(entry.access_count for entry in self._cache.values())
        return {
            'size': len(self._cache),
            'max_size': self.max_size,
            'total_accesses': total_access,
            'hit_rate': 0.0 if total_access == 0 else len(self._cache) / total_access
        }

class ConnectionPool:
    """Async connection pool for external services"""
    
    def __init__(self, max_connections: int = 10):
        self.max_connections = max_connections
        self._available_connections = asyncio.Queue(maxsize=max_connections)
        self._active_connections = 0
        self._lock = asyncio.Lock()
    
    async def acquire(self) -> Any:
        """Acquire a connection from the pool"""
        async with self._lock:
            if self._active_connections < self.max_connections:
                # Create new connection
                self._active_connections += 1
                return f"connection_{self._active_connections}"
            else:
                # Wait for available connection
                return await self._available_connections.get()
    
    async def release(self, connection: Any) -> None:
        """Release connection back to pool"""
        await self._available_connections.put(connection)

class PipelineOptimizer:
    """Main pipeline performance optimizer"""
    
    def __init__(self):
        """Initialize the performance optimizer with cache and background tasks"""
        # Initialize cache
        self.cache = IntelligentCache(max_size=1000, default_ttl=300)  # 5min default TTL
        
        # Performance metrics
        self.metrics = {
            'cache_hits': 0,
            'cache_misses': 0,
            'total_requests': 0,
            'avg_response_time': 0.0,
            'optimization_savings': 0.0
        }
        
        # Background cleanup task - defer until event loop is running
        self._cleanup_task_handle = None
        
        logger.info("Pipeline performance optimizer initialized")
        
    def start_background_tasks(self):
        """Start background tasks once event loop is available"""
        if self._cleanup_task_handle is None:
            self._cleanup_task_handle = asyncio.create_task(self._cleanup_task())
            logger.info("Performance optimizer background tasks started")
    
    async def optimize_query(self, query: str, context: Dict[str, Any] = None) -> Tuple[Any, bool]:
        """
        Optimize query execution with caching and performance enhancements
        
        Returns:
            Tuple of (result, was_cached)
        """
        start_time = time.time()
        self.metrics['total_requests'] += 1
        
        # Try cache first
        cached_result = await self.cache.get(query, context)
        if cached_result is not None:
            self.metrics['cache_hits'] += 1
            execution_time = time.time() - start_time
            self.metrics['optimization_savings'] += execution_time
            logger.info(f"Cache hit for query optimization ({execution_time:.3f}s saved)")
            return cached_result, True
        
        self.metrics['cache_misses'] += 1
        return None, False
    
    async def cache_result(self, query: str, result: Any, context: Dict[str, Any] = None, ttl: int = None) -> None:
        """Cache a result for future use"""
        await self.cache.set(query, result, context, ttl)
    
    async def _cleanup_task(self) -> None:
        """Background task to clean up expired cache entries"""
        while True:
            try:
                await asyncio.sleep(60)  # Run every minute
                expired_count = await self.cache.clear_expired()
                if expired_count > 0:
                    logger.debug(f"Cleaned up {expired_count} expired cache entries")
            except Exception as e:
                logger.error(f"Error in cache cleanup task: {e}")
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get comprehensive performance metrics"""
        total_requests = self.metrics['total_requests']
        cache_hit_rate = 0.0
        if total_requests > 0:
            cache_hit_rate = self.metrics['cache_hits'] / total_requests
        
        return {
            'cache_stats': self.cache.get_stats(),
            'hit_rate': cache_hit_rate,
            'total_requests': total_requests,
            'cache_hits': self.metrics['cache_hits'],
            'cache_misses': self.metrics['cache_misses'],
            'optimization_savings_seconds': self.metrics['optimization_savings'],
            'avg_response_time': self.metrics['avg_response_time']
        }

# Global optimizer instance (initialized later)
pipeline_optimizer: Optional['PipelineOptimizer'] = None


def initialize_pipeline_optimizer():
    """Initialize the global pipeline optimizer with proper async context"""
    global pipeline_optimizer
    if pipeline_optimizer is None:
        pipeline_optimizer = PipelineOptimizer()
    return pipeline_optimizer

async def optimize_pipeline_execution(query: str, execute_func, context: Optional[Dict[str, Any]] = None) -> Any:
    """
    Wrapper function to optimize any pipeline execution
    
    Args:
        query: The query string
        execute_func: Async function to execute if not cached
        context: Additional context for caching
    
    Returns:
        Optimized result
    """
    # Ensure pipeline optimizer is initialized
    global pipeline_optimizer
    if pipeline_optimizer is None:
        pipeline_optimizer = initialize_pipeline_optimizer()
        pipeline_optimizer.start_background_tasks()
    
    start_time = time.time()
    
    # Try to get cached result
    result, was_cached = await pipeline_optimizer.optimize_query(query, context or {})
    
    if not was_cached:
        # Execute the actual function
        result = await execute_func()
        
        # Cache the result
        await pipeline_optimizer.cache_result(query, result, context or {})
    
    execution_time = time.time() - start_time
    
    # Update metrics
    pipeline_optimizer.metrics['avg_response_time'] = (
        (pipeline_optimizer.metrics['avg_response_time'] * (pipeline_optimizer.metrics['total_requests'] - 1) + execution_time) /
        pipeline_optimizer.metrics['total_requests']
    )
    
    return result
