"""
Test Script for the New Pipeline System

This script demonstrates how the multi-stage pipeline works
and can be used for testing and development.
"""

import asyncio
import sys
import os

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from src.bot.pipeline.commands.ask.pipeline import execute_ask_pipeline
from src.bot.pipeline.core.context_manager import PipelineContext


async def test_ask_pipeline():
    """Test the complete ask pipeline with different query types"""
    
    test_queries = [
        "What is the current price of $AAPL?",
        "How is the stock market doing today?",
        "Give me a technical analysis of $TSLA",
        "What are some good stocks to invest in?",
        "Hello, can you help me with trading advice?"
    ]
    
    print("🚀 Testing Multi-Stage Pipeline System")
    print("=" * 50)
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n📝 Test {i}: {query}")
        print("-" * 40)
        
        try:
            # Execute the pipeline
            context = await execute_ask_pipeline(query)
            
            # Display results
            print(f"✅ Pipeline Status: {context.status.value}")
            print(f"⏱️  Total Time: {context.total_execution_time:.2f}s")
            print(f"🔍 Stages Executed: {len(context.stage_history)}")
            print(f"📊 Overall Quality: {context.get_overall_quality().quality_level.value}")
            print(f"❌ Errors: {len(context.error_log)}")
            
            # Show stage breakdown
            print("\n📋 Stage Breakdown:")
            for stage in context.stage_history:
                timing = context.stage_timings.get(stage, 0)
                print(f"  • {stage}: {timing:.3f}s")
            
            # Show final response
            if context.processing_results.get("formatted_response"):
                print(f"\n🤖 AI Response Preview:")
                response = context.processing_results["formatted_response"]
                preview = response[:200] + "..." if len(response) > 200 else response
                print(f"  {preview}")
            
        except Exception as e:
            print(f"❌ Pipeline failed: {str(e)}")
        
        print("\n" + "=" * 50)


async def test_pipeline_context():
    """Test the pipeline context functionality"""
    print("\n🧪 Testing Pipeline Context")
    print("=" * 30)
    
    # Create a context
    context = PipelineContext(
        command_name="test",
        original_query="Test query",
        user_id="12345"
    )
    
    # Test context methods
    print(f"Pipeline ID: {context.pipeline_id}")
    print(f"Status: {context.status.value}")
    print(f"Start Time: {context.start_time}")
    
    # Add some test data
    context.collected_data["test_symbol"] = {"price": 100.0}
    context.processing_results["test_analysis"] = {"score": 85}
    
    # Test quality scoring
    from src.bot.pipeline.core.context_manager import QualityScore
    quality = QualityScore(overall_score=85.0, freshness_score=90.0)
    context.set_quality_score("test_data", quality)
    
    print(f"Overall Quality: {context.get_overall_quality().quality_level.value}")
    
    # Test audit trail
    context.add_audit_entry(
        stage="test_stage",
        action="test_action",
        input_data={"test": "input"},
        output_data={"test": "output"},
        execution_time=0.1,
        success=True
    )
    
    print(f"Audit Entries: {len(context.audit_trail)}")
    
    # Finalize
    context.finalize()
    print(f"Final Status: {context.status.value}")
    print(f"Total Time: {context.total_execution_time:.3f}s")


async def main():
    """Main test function"""
    print("🎯 Multi-Stage Pipeline System Test")
    print("This demonstrates the new n8n-style pipeline architecture")
    
    # Test context functionality
    await test_pipeline_context()
    
    # Test full pipeline
    await test_ask_pipeline()
    
    print("\n✅ All tests completed!")


if __name__ == "__main__":
    asyncio.run(main()) 