"""
Metrics Collector for Pipeline Operations

Provides Prometheus-friendly metrics collection for pipeline sections.
Abstracts metrics collection to avoid vendor lock-in.
"""

import time
import threading
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field
from collections import defaultdict, deque
from enum import Enum

class MetricType(Enum):
    """Types of metrics supported"""
    COUNTER = "counter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"
    SUMMARY = "summary"

@dataclass
class MetricValue:
    """Individual metric value with labels"""
    value: float
    timestamp: float
    labels: Dict[str, str] = field(default_factory=dict)

@dataclass
class Metric:
    """Metric definition with metadata"""
    name: str
    type: MetricType
    help: str
    labels: List[str] = field(default_factory=list)
    values: deque = field(default_factory=lambda: deque(maxlen=1000))  # Keep last 1000 values

class MetricsCollector:
    """
    Metrics collector for pipeline operations.
    
    Provides Prometheus-friendly metrics:
    - ask_section_success_total
    - ask_section_failure_total  
    - ask_section_retry_total
    - ask_pipeline_latency_seconds
    """
    
    def __init__(self):
        self._metrics: Dict[str, Metric] = {}
        self._lock = threading.Lock()
        self._start_time = time.time()
        
        # Initialize default metrics
        self._init_default_metrics()
    
    def _init_default_metrics(self):
        """Initialize default pipeline metrics"""
        self.register_metric(
            "ask_section_success_total",
            MetricType.COUNTER,
            "Total successful pipeline section executions",
            ["section", "pipeline"]
        )
        
        self.register_metric(
            "ask_section_failure_total", 
            MetricType.COUNTER,
            "Total failed pipeline section executions",
            ["section", "pipeline", "error_type"]
        )
        
        self.register_metric(
            "ask_section_retry_total",
            MetricType.COUNTER,
            "Total retry attempts for pipeline sections",
            ["section", "pipeline"]
        )
        
        self.register_metric(
            "ask_pipeline_latency_seconds",
            MetricType.HISTOGRAM,
            "Pipeline section execution latency in seconds",
            ["section", "pipeline"]
        )
        
        self.register_metric(
            "ask_pipeline_active_sections",
            MetricType.GAUGE,
            "Number of currently active pipeline sections",
            ["pipeline"]
        )
        
        self.register_metric(
            "ask_pipeline_circuit_breaker_state",
            MetricType.GAUGE,
            "Circuit breaker state (0=closed, 1=half_open, 2=open)",
            ["section", "pipeline"]
        )
    
    def register_metric(self, name: str, metric_type: MetricType, help_text: str, labels: List[str] = None):
        """Register a new metric"""
        with self._lock:
            if name in self._metrics:
                return  # Already registered
            
            self._metrics[name] = Metric(
                name=name,
                type=metric_type,
                help=help_text,
                labels=labels or []
            )
    
    def inc_counter(self, name: str, labels: Dict[str, str] = None, value: float = 1.0):
        """Increment a counter metric"""
        if name not in self._metrics:
            self.register_metric(name, MetricType.COUNTER, f"Counter metric: {name}", list(labels.keys()) if labels else [])
        
        metric = self._metrics[name]
        if metric.type != MetricType.COUNTER:
            raise ValueError(f"Metric {name} is not a counter")
        
        with self._lock:
            metric.values.append(MetricValue(
                value=value,
                timestamp=time.time(),
                labels=labels or {}
            ))
    
    def set_gauge(self, name: str, value: float, labels: Dict[str, str] = None):
        """Set a gauge metric value"""
        if name not in self._metrics:
            self.register_metric(name, MetricType.GAUGE, f"Gauge metric: {name}", list(labels.keys()) if labels else [])
        
        metric = self._metrics[name]
        if metric.type != MetricType.GAUGE:
            raise ValueError(f"Metric {name} is not a gauge")
        
        with self._lock:
            metric.values.append(MetricValue(
                value=value,
                timestamp=time.time(),
                labels=labels or {}
            ))
    
    def observe_histogram(self, name: str, value: float, labels: Dict[str, str] = None):
        """Observe a histogram metric value"""
        if name not in self._metrics:
            self.register_metric(name, MetricType.HISTOGRAM, f"Histogram metric: {name}", list(labels.keys()) if labels else [])
        
        metric = self._metrics[name]
        if metric.type != MetricType.HISTOGRAM:
            raise ValueError(f"Metric {name} is not a histogram")
        
        with self._lock:
            metric.values.append(MetricValue(
                value=value,
                timestamp=time.time(),
                labels=labels or {}
            ))
    
    def record_section_success(self, section: str, pipeline: str = "ask"):
        """Record successful section execution"""
        self.inc_counter("ask_section_success_total", {"section": section, "pipeline": pipeline})
    
    def record_section_failure(self, section: str, error_type: str, pipeline: str = "ask"):
        """Record failed section execution"""
        self.inc_counter("ask_section_failure_total", {"section": section, "pipeline": pipeline, "error_type": error_type})
    
    def record_section_retry(self, section: str, pipeline: str = "ask"):
        """Record section retry attempt"""
        self.inc_counter("ask_section_retry_total", {"section": section, "pipeline": pipeline})
    
    def record_section_latency(self, section: str, latency_seconds: float, pipeline: str = "ask"):
        """Record section execution latency"""
        self.observe_histogram("ask_pipeline_latency_seconds", latency_seconds, {"section": section, "pipeline": pipeline})
    
    def set_circuit_breaker_state(self, section: str, state: str, pipeline: str = "ask"):
        """Set circuit breaker state (0=closed, 1=half_open, 2=open)"""
        state_value = {"closed": 0, "half_open": 1, "open": 2}.get(state, 0)
        self.set_gauge("ask_pipeline_circuit_breaker_state", state_value, {"section": section, "pipeline": pipeline})
    
    def set_active_sections(self, count: int, pipeline: str = "ask"):
        """Set number of active pipeline sections"""
        self.set_gauge("ask_pipeline_active_sections", count, {"pipeline": pipeline})
    
    def get_metric_value(self, name: str, labels: Dict[str, str] = None) -> float:
        """Get current value of a metric"""
        if name not in self._metrics:
            return 0.0
        
        metric = self._metrics[name]
        if not metric.values:
            return 0.0
        
        # Filter by labels if provided
        if labels:
            filtered_values = [
                v for v in metric.values 
                if all(v.labels.get(k) == v for k, v in labels.items())
            ]
            if not filtered_values:
                return 0.0
            return filtered_values[-1].value
        
        return metric.values[-1].value
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get summary of all metrics"""
        with self._lock:
            summary = {
                "uptime_seconds": time.time() - self._start_time,
                "total_metrics": len(self._metrics),
                "metrics": {}
            }
            
            for name, metric in self._metrics.items():
                summary["metrics"][name] = {
                    "type": metric.type.value,
                    "help": metric.help,
                    "labels": metric.labels,
                    "current_value": self.get_metric_value(name),
                    "total_values": len(metric.values)
                }
            
            return summary
    
    def get_prometheus_format(self) -> str:
        """Export metrics in Prometheus format"""
        lines = []
        
        with self._lock:
            for name, metric in self._metrics.items():
                # Add help text
                lines.append(f"# HELP {name} {metric.help}")
                lines.append(f"# TYPE {name} {metric.type.value}")
                
                # Add metric values
                if metric.values:
                    latest_value = metric.values[-1]
                    if metric.labels and latest_value.labels:
                        label_str = ",".join([f'{k}="{v}"' for k, v in latest_value.labels.items()])
                        lines.append(f"{name}{{{label_str}}} {latest_value.value}")
                    else:
                        lines.append(f"{name} {latest_value.value}")
        
        return "\n".join(lines)
    
    def reset_metrics(self):
        """Reset all metrics to initial state"""
        with self._lock:
            for metric in self._metrics.values():
                metric.values.clear()
            self._start_time = time.time()
    
    def __str__(self) -> str:
        return f"MetricsCollector(metrics={len(self._metrics)}, uptime={time.time() - self._start_time:.1f}s)"
    
    def __repr__(self) -> str:
        return self.__str__()

# Global metrics collector instance
metrics_collector = MetricsCollector() 