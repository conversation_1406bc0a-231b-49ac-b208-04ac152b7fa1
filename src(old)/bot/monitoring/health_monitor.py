"""
Comprehensive Health Monitoring System
Real-time monitoring and alerting for the trading bot
"""

import asyncio
import time
import psutil
import logging
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
import json

logger = logging.getLogger(__name__)

class HealthStatus(Enum):
    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    UNKNOWN = "unknown"

@dataclass
class HealthMetric:
    """Individual health metric"""
    name: str
    value: Any
    status: HealthStatus
    threshold_warning: Optional[float] = None
    threshold_critical: Optional[float] = None
    last_updated: datetime = None
    message: str = ""
    
    def __post_init__(self):
        if self.last_updated is None:
            self.last_updated = datetime.now()

@dataclass
class SystemHealth:
    """Overall system health status"""
    status: HealthStatus
    uptime_seconds: float
    cpu_usage: float
    memory_usage: float
    disk_usage: float
    active_connections: int
    error_rate: float
    response_time_avg: float
    cache_hit_rate: float
    last_check: datetime
    warnings: List[str]
    critical_issues: List[str]

class HealthChecker:
    """Core health checking functionality"""
    
    def __init__(self):
        self.start_time = time.time()
        self.metrics: Dict[str, HealthMetric] = {}
        self.history: List[SystemHealth] = []
        self.max_history = 1000
        
        # Thresholds
        self.thresholds = {
            'cpu_usage': {'warning': 70.0, 'critical': 90.0},
            'memory_usage': {'warning': 80.0, 'critical': 95.0},
            'disk_usage': {'warning': 85.0, 'critical': 95.0},
            'error_rate': {'warning': 5.0, 'critical': 15.0},
            'response_time': {'warning': 5.0, 'critical': 10.0}
        }
    
    async def check_system_resources(self) -> Dict[str, HealthMetric]:
        """Check system resource usage"""
        metrics = {}
        
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_status = self._determine_status('cpu_usage', cpu_percent)
            metrics['cpu_usage'] = HealthMetric(
                name="CPU Usage",
                value=cpu_percent,
                status=cpu_status,
                threshold_warning=self.thresholds['cpu_usage']['warning'],
                threshold_critical=self.thresholds['cpu_usage']['critical'],
                message=f"CPU usage at {cpu_percent:.1f}%"
            )
            
            # Memory usage
            memory = psutil.virtual_memory()
            memory_status = self._determine_status('memory_usage', memory.percent)
            metrics['memory_usage'] = HealthMetric(
                name="Memory Usage",
                value=memory.percent,
                status=memory_status,
                threshold_warning=self.thresholds['memory_usage']['warning'],
                threshold_critical=self.thresholds['memory_usage']['critical'],
                message=f"Memory usage at {memory.percent:.1f}% ({memory.used // (1024**3):.1f}GB used)"
            )
            
            # Disk usage
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            disk_status = self._determine_status('disk_usage', disk_percent)
            metrics['disk_usage'] = HealthMetric(
                name="Disk Usage",
                value=disk_percent,
                status=disk_status,
                threshold_warning=self.thresholds['disk_usage']['warning'],
                threshold_critical=self.thresholds['disk_usage']['critical'],
                message=f"Disk usage at {disk_percent:.1f}% ({disk.used // (1024**3):.1f}GB used)"
            )
            
        except Exception as e:
            logger.error(f"Error checking system resources: {e}")
            metrics['system_error'] = HealthMetric(
                name="System Check Error",
                value=str(e),
                status=HealthStatus.CRITICAL,
                message=f"Failed to check system resources: {e}"
            )
        
        return metrics
    
    async def check_database_health(self, db_pool) -> Dict[str, HealthMetric]:
        """Check database connection health"""
        metrics = {}
        
        try:
            if db_pool is None:
                metrics['database'] = HealthMetric(
                    name="Database Connection",
                    value="Not initialized",
                    status=HealthStatus.CRITICAL,
                    message="Database pool is not initialized"
                )
                return metrics
            
            start_time = time.time()
            async with db_pool.acquire() as conn:
                await conn.fetchval("SELECT 1")
            response_time = time.time() - start_time
            
            # Check response time
            response_status = self._determine_status('response_time', response_time)
            metrics['database_response'] = HealthMetric(
                name="Database Response Time",
                value=response_time,
                status=response_status,
                threshold_warning=self.thresholds['response_time']['warning'],
                threshold_critical=self.thresholds['response_time']['critical'],
                message=f"Database responding in {response_time:.3f}s"
            )
            
            # Check pool status
            pool_size = db_pool.get_size()
            pool_available = db_pool.get_idle_size()
            pool_usage = ((pool_size - pool_available) / pool_size) * 100 if pool_size > 0 else 0
            
            pool_status = HealthStatus.HEALTHY
            if pool_usage > 80:
                pool_status = HealthStatus.WARNING
            if pool_usage > 95:
                pool_status = HealthStatus.CRITICAL
            
            metrics['database_pool'] = HealthMetric(
                name="Database Pool",
                value=pool_usage,
                status=pool_status,
                message=f"Pool usage: {pool_usage:.1f}% ({pool_size - pool_available}/{pool_size} connections)"
            )
            
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            metrics['database'] = HealthMetric(
                name="Database Health",
                value="Error",
                status=HealthStatus.CRITICAL,
                message=f"Database health check failed: {e}"
            )
        
        return metrics
    
    async def check_pipeline_health(self) -> Dict[str, HealthMetric]:
        """Check AI pipeline health"""
        metrics = {}
        
        try:
            # Import here to avoid circular imports
            from ..pipeline.commands.ask.pipeline import get_pipeline_health, get_pipeline_metrics
            
            health_data = get_pipeline_health()
            pipeline_metrics = get_pipeline_metrics()
            
            # Check success rate
            success_rate = health_data.get('success_rate', 0)
            if success_rate >= 95:
                success_status = HealthStatus.HEALTHY
            elif success_rate >= 85:
                success_status = HealthStatus.WARNING
            else:
                success_status = HealthStatus.CRITICAL
            
            metrics['pipeline_success_rate'] = HealthMetric(
                name="Pipeline Success Rate",
                value=success_rate,
                status=success_status,
                message=f"Pipeline success rate: {success_rate:.1f}%"
            )
            
            # Check average response time
            avg_response = health_data.get('average_response_time', 0)
            response_status = self._determine_status('response_time', avg_response)
            metrics['pipeline_response_time'] = HealthMetric(
                name="Pipeline Response Time",
                value=avg_response,
                status=response_status,
                threshold_warning=self.thresholds['response_time']['warning'],
                threshold_critical=self.thresholds['response_time']['critical'],
                message=f"Average response time: {avg_response:.2f}s"
            )
            
        except Exception as e:
            logger.error(f"Pipeline health check failed: {e}")
            metrics['pipeline'] = HealthMetric(
                name="Pipeline Health",
                value="Error",
                status=HealthStatus.CRITICAL,
                message=f"Pipeline health check failed: {e}"
            )
        
        return metrics
    
    def _determine_status(self, metric_name: str, value: float) -> HealthStatus:
        """Determine health status based on thresholds"""
        thresholds = self.thresholds.get(metric_name, {})
        
        critical_threshold = thresholds.get('critical')
        warning_threshold = thresholds.get('warning')
        
        if critical_threshold is not None and value >= critical_threshold:
            return HealthStatus.CRITICAL
        elif warning_threshold is not None and value >= warning_threshold:
            return HealthStatus.WARNING
        else:
            return HealthStatus.HEALTHY
    
    async def get_comprehensive_health(self, db_pool=None) -> SystemHealth:
        """Get comprehensive system health status"""
        all_metrics = {}
        
        # Collect all metrics
        system_metrics = await self.check_system_resources()
        all_metrics.update(system_metrics)
        
        if db_pool:
            db_metrics = await self.check_database_health(db_pool)
            all_metrics.update(db_metrics)
        
        pipeline_metrics = await self.check_pipeline_health()
        all_metrics.update(pipeline_metrics)
        
        # Update stored metrics
        self.metrics.update(all_metrics)
        
        # Determine overall status
        overall_status = HealthStatus.HEALTHY
        warnings = []
        critical_issues = []
        
        for metric in all_metrics.values():
            if metric.status == HealthStatus.CRITICAL:
                overall_status = HealthStatus.CRITICAL
                critical_issues.append(metric.message)
            elif metric.status == HealthStatus.WARNING and overall_status != HealthStatus.CRITICAL:
                overall_status = HealthStatus.WARNING
                warnings.append(metric.message)
        
        # Create system health summary
        uptime = time.time() - self.start_time
        
        health = SystemHealth(
            status=overall_status,
            uptime_seconds=uptime,
            cpu_usage=all_metrics.get('cpu_usage', HealthMetric('cpu', 0, HealthStatus.UNKNOWN)).value,
            memory_usage=all_metrics.get('memory_usage', HealthMetric('memory', 0, HealthStatus.UNKNOWN)).value,
            disk_usage=all_metrics.get('disk_usage', HealthMetric('disk', 0, HealthStatus.UNKNOWN)).value,
            active_connections=0,  # TODO: Implement connection tracking
            error_rate=0.0,  # TODO: Implement error rate tracking
            response_time_avg=all_metrics.get('pipeline_response_time', HealthMetric('response', 0, HealthStatus.UNKNOWN)).value,
            cache_hit_rate=0.0,  # TODO: Get from cache metrics
            last_check=datetime.now(),
            warnings=warnings,
            critical_issues=critical_issues
        )
        
        # Store in history
        self.history.append(health)
        if len(self.history) > self.max_history:
            self.history.pop(0)
        
        return health

class HealthMonitor:
    """Main health monitoring service"""
    
    def __init__(self, check_interval: int = 60):
        self.check_interval = check_interval
        self.checker = HealthChecker()
        self.db_pool = None
        self.running = False
        self.monitor_task: Optional[asyncio.Task] = None
        self.alert_callbacks: List[Callable] = []
    
    def set_database_pool(self, db_pool):
        """Set database pool for health checks"""
        self.db_pool = db_pool
    
    def add_alert_callback(self, callback: Callable):
        """Add callback for health alerts"""
        self.alert_callbacks.append(callback)
    
    async def start(self):
        """Start health monitoring"""
        if self.running:
            return
        
        self.running = True
        self.monitor_task = asyncio.create_task(self._monitor_loop())
        logger.info("Health monitoring started")
    
    async def stop(self):
        """Stop health monitoring"""
        self.running = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        logger.info("Health monitoring stopped")
    
    async def _monitor_loop(self):
        """Main monitoring loop"""
        while self.running:
            try:
                health = await self.checker.get_comprehensive_health(self.db_pool)
                
                # Send alerts if needed
                if health.status in [HealthStatus.WARNING, HealthStatus.CRITICAL]:
                    await self._send_alerts(health)
                
                logger.debug(f"Health check completed: {health.status.value}")
                
            except Exception as e:
                logger.error(f"Error in health monitoring loop: {e}")
            
            await asyncio.sleep(self.check_interval)
    
    async def _send_alerts(self, health: SystemHealth):
        """Send health alerts"""
        for callback in self.alert_callbacks:
            try:
                await callback(health)
            except Exception as e:
                logger.error(f"Error sending health alert: {e}")
    
    async def get_health_report(self) -> Dict[str, Any]:
        """Get current health report"""
        health = await self.checker.get_comprehensive_health(self.db_pool)
        return {
            'overall_status': health.status.value,
            'uptime_hours': health.uptime_seconds / 3600,
            'system_metrics': {
                'cpu_usage': health.cpu_usage,
                'memory_usage': health.memory_usage,
                'disk_usage': health.disk_usage
            },
            'performance_metrics': {
                'response_time_avg': health.response_time_avg,
                'cache_hit_rate': health.cache_hit_rate,
                'error_rate': health.error_rate
            },
            'issues': {
                'warnings': health.warnings,
                'critical': health.critical_issues
            },
            'last_check': health.last_check.isoformat(),
            'detailed_metrics': {
                name: asdict(metric) for name, metric in self.checker.metrics.items()
            }
        }

# Global health monitor instance
health_monitor = HealthMonitor()

async def get_system_health() -> Dict[str, Any]:
    """Get current system health status"""
    return await health_monitor.get_health_report()

async def start_health_monitoring(db_pool=None):
    """Start the health monitoring system"""
    if db_pool:
        health_monitor.set_database_pool(db_pool)
    await health_monitor.start()

async def stop_health_monitoring():
    """Stop the health monitoring system"""
    await health_monitor.stop()
