from dataclasses import dataclass
from typing import Optional, List, Dict, Any
from datetime import datetime

@dataclass
class StockQuote:
    """Real-time stock quote data"""
    symbol: str
    price: float
    volume: Optional[int]
    timestamp: datetime
    change: Optional[float]
    change_percent: Optional[float]
    open: Optional[float]
    high: Optional[float]
    low: Optional[float]
    close: Optional[float]
    market_cap: Optional[float]
    pe_ratio: Optional[float]
    eps: Optional[float]

@dataclass
class HistoricalData:
    """Historical price data"""
    symbol: str
    dates: List[datetime]
    prices: List[float]
    volumes: List[Optional[int]]
    highs: List[Optional[float]]
    lows: List[Optional[float]]
    opens: List[Optional[float]]
    closes: List[float]

@dataclass
class TechnicalIndicators:
    """Technical analysis indicators"""
    rsi: Optional[float]
    macd: Optional[Dict[str, float]]
    sma_50: Optional[float]
    sma_200: Optional[float]
    ema_12: Optional[float]
    ema_26: Optional[float]
    bollinger_bands: Optional[Dict[str, float]]
    support_level: Optional[float]
    resistance_level: Optional[float]
    volume_sma: Optional[float]

@dataclass
class FundamentalMetrics:
    """Fundamental analysis metrics"""
    pe_ratio: Optional[float]
    eps: Optional[float]
    revenue_growth: Optional[float]
    profit_margin: Optional[float]
    debt_to_equity: Optional[float]
    return_on_equity: Optional[float]
    price_to_book: Optional[float]
    peg_ratio: Optional[float]
    dividend_yield: Optional[float]
    free_cash_flow: Optional[float]

@dataclass
class RiskAssessment:
    """Risk analysis results"""
    volatility: Optional[float]
    beta: Optional[float]
    risk_warnings: List[str]
    stop_loss_recommendation: Optional[float]
    risk_level: str  # "LOW", "MEDIUM", "HIGH"

@dataclass
class MarketContext:
    """Market context information"""
    sector: Optional[str]
    industry: Optional[str]
    market_trend: Optional[str]
    sector_performance: Optional[float]
    market_sentiment: Optional[str]

@dataclass
class AnalysisResult:
    """Complete analysis result"""
    symbol: str
    timestamp: datetime
    
    # Data components
    quote: Optional[StockQuote]
    historical: Optional[HistoricalData]
    technical: Optional[TechnicalIndicators]
    fundamental: Optional[FundamentalMetrics]
    risk: Optional[RiskAssessment]
    market_context: Optional[MarketContext]
    
    # AI recommendation
    recommendation: str  # "BUY", "HOLD", "SELL"
    confidence: int  # 0-100
    time_horizon: str  # "SHORT", "MEDIUM", "LONG"
    
    # Analysis summary
    key_insights: List[str]
    summary: str
    
    # Metadata
    data_sources: List[str]
    analysis_quality: str  # "HIGH", "MEDIUM", "LOW"
    confidence_factors: Dict[str, int]
