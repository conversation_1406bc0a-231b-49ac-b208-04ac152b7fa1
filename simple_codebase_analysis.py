#!/usr/bin/env python3
"""
Simple Codebase Analysis Script
Quick analysis of the src/ folder structure to understand the layout
"""

import os
import json
from pathlib import Path
from collections import defaultdict, Counter
from typing import Dict, List, Set, Tuple, Any

def analyze_codebase(src_path: str):
    """Analyze the codebase structure"""
    src_path = Path(src_path)
    
    # Basic stats
    stats = {
        "total_files": 0,
        "total_lines": 0,
        "total_size": 0,
        "modules": {},
        "file_sizes": [],
        "deep_nesting": [],
        "duplicate_names": defaultdict(list),
        "large_files": [],
        "import_patterns": Counter(),
        "syntax_errors": []
    }
    
    print("🔍 Analyzing codebase structure...")
    
    # Walk through all files
    for py_file in src_path.rglob("*.py"):
        if "__pycache__" in str(py_file):
            continue
            
        rel_path = str(py_file.relative_to(src_path))
        
        # Basic file info
        try:
            with open(py_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            lines = len(content.splitlines())
            size = py_file.stat().st_size
            
            stats["total_files"] += 1
            stats["total_lines"] += lines
            stats["total_size"] += size
            
            # Track file sizes
            stats["file_sizes"].append((rel_path, size, lines))
            
            # Check for large files
            if lines > 500:
                stats["large_files"].append((rel_path, lines))
            
            # Check nesting depth
            depth = len(rel_path.split('/'))
            if depth > 4:
                stats["deep_nesting"].append((rel_path, depth))
            
            # Track duplicate filenames
            filename = py_file.name
            stats["duplicate_names"][filename].append(rel_path)
            
            # Analyze imports (simple pattern matching)
            for line in content.splitlines():
                line = line.strip()
                if line.startswith('import ') or line.startswith('from '):
                    if 'src.' in line:
                        stats["import_patterns"]['src_absolute'] += 1
                    elif line.startswith('from .'):
                        stats["import_patterns"]['relative'] += 1
                    elif '.' in line:
                        stats["import_patterns"]['absolute'] += 1
                    else:
                        stats["import_patterns"]['simple'] += 1
            
            # Organize by module
            module_path = str(py_file.parent.relative_to(src_path))
            if module_path == ".":
                module_path = "root"
            
            if module_path not in stats["modules"]:
                stats["modules"][module_path] = {
                    "files": 0,
                    "lines": 0,
                    "size": 0,
                    "files_list": []
                }
            
            stats["modules"][module_path]["files"] += 1
            stats["modules"][module_path]["lines"] += lines
            stats["modules"][module_path]["size"] += size
            stats["modules"][module_path]["files_list"].append(rel_path)
            
        except Exception as e:
            stats["syntax_errors"].append((rel_path, str(e)))
    
    return stats

def print_analysis(stats):
    """Print analysis results"""
    print("\n" + "="*80)
    print("📊 CODEBASE ANALYSIS RESULTS")
    print("="*80)
    
    print(f"📁 Total Files: {stats['total_files']}")
    print(f"📄 Total Lines: {stats['total_lines']:,}")
    print(f"💾 Total Size: {stats['total_size'] / (1024*1024):.1f} MB")
    print(f"📦 Modules: {len(stats['modules'])}")
    
    print(f"\n🔍 DUPLICATE FILENAMES:")
    for filename, paths in stats['duplicate_names'].items():
        if len(paths) > 1:
            print(f"  - {filename}: {len(paths)} copies")
            for path in paths[:3]:  # Show first 3
                print(f"    * {path}")
    
    print(f"\n🏗️  DEEP NESTING (>{4} levels): {len(stats['deep_nesting'])}")
    for path, depth in stats['deep_nesting'][:10]:
        print(f"  - {path} ({depth} levels)")
    
    print(f"\n📄 LARGE FILES (>{500} lines): {len(stats['large_files'])}")
    for path, lines in sorted(stats['large_files'], key=lambda x: x[1], reverse=True)[:10]:
        print(f"  - {path}: {lines} lines")
    
    print(f"\n📊 IMPORT PATTERNS:")
    for pattern, count in stats['import_patterns'].most_common():
        print(f"  - {pattern}: {count}")
    
    print(f"\n⚠️  SYNTAX ERRORS: {len(stats['syntax_errors'])}")
    for path, error in stats['syntax_errors'][:5]:
        print(f"  - {path}: {error}")
    
    print(f"\n📦 MODULE BREAKDOWN (Top 20 by file count):")
    sorted_modules = sorted(stats['modules'].items(), key=lambda x: x[1]['files'], reverse=True)
    for module, info in sorted_modules[:20]:
        print(f"  - {module}: {info['files']} files, {info['lines']} lines")

def generate_module_map(stats):
    """Generate a visual module map"""
    print("\n" + "="*80)
    print("🗺️  MODULE STRUCTURE MAP")
    print("="*80)
    
    # Group by top-level modules
    top_level = defaultdict(list)
    for module_path, info in stats['modules'].items():
        if '/' in module_path:
            top = module_path.split('/')[0]
        else:
            top = "root"
        top_level[top].append((module_path, info))
    
    for top_module, submodules in sorted(top_level.items()):
        print(f"\n📁 {top_module}/")
        total_files = sum(info['files'] for _, info in submodules)
        total_lines = sum(info['lines'] for _, info in submodules)
        print(f"   Total: {total_files} files, {total_lines} lines")
        
        # Show submodules
        for module_path, info in sorted(submodules, key=lambda x: x[1]['files'], reverse=True)[:10]:
            indent = "  " + "  " * (len(module_path.split('/')) - 1)
            print(f"{indent}📄 {module_path}: {info['files']} files, {info['lines']} lines")

def identify_problems(stats):
    """Identify specific problems in the codebase"""
    print("\n" + "="*80)
    print("🚨 IDENTIFIED PROBLEMS")
    print("="*80)
    
    problems = []
    
    # Problem 1: Too many files
    if stats['total_files'] > 200:
        problems.append(f"❌ TOO MANY FILES: {stats['total_files']} files (should be <200)")
    
    # Problem 2: Deep nesting
    if stats['deep_nesting']:
        problems.append(f"❌ DEEP NESTING: {len(stats['deep_nesting'])} files with >4 levels")
    
    # Problem 3: Large files
    large_files = [f for f in stats['large_files'] if f[1] > 1000]
    if large_files:
        problems.append(f"❌ LARGE FILES: {len(large_files)} files with >1000 lines")
    
    # Problem 4: Duplicate filenames
    duplicates = [f for f, paths in stats['duplicate_names'].items() if len(paths) > 1]
    if duplicates:
        problems.append(f"❌ DUPLICATE FILENAMES: {len(duplicates)} duplicate names")
    
    # Problem 5: Too many modules
    if len(stats['modules']) > 50:
        problems.append(f"❌ TOO MANY MODULES: {len(stats['modules'])} modules (should be <50)")
    
    # Problem 6: Syntax errors
    if stats['syntax_errors']:
        problems.append(f"❌ SYNTAX ERRORS: {len(stats['syntax_errors'])} files with syntax errors")
    
    # Problem 7: Import patterns
    src_imports = stats['import_patterns'].get('src_absolute', 0)
    if src_imports > 100:
        problems.append(f"❌ COMPLEX IMPORTS: {src_imports} src.* imports (indicates tight coupling)")
    
    for problem in problems:
        print(problem)
    
    if not problems:
        print("✅ No major problems identified!")
    
    return problems

def main():
    """Main function"""
    import sys
    
    if len(sys.argv) != 2:
        print("Usage: python simple_codebase_analysis.py <src_path>")
        print("Example: python simple_codebase_analysis.py src(old)")
        sys.exit(1)
    
    src_path = sys.argv[1]
    if not os.path.exists(src_path):
        print(f"Error: Path {src_path} does not exist")
        sys.exit(1)
    
    print("🚀 Starting Simple Codebase Analysis")
    print(f"📂 Analyzing: {src_path}")
    
    stats = analyze_codebase(src_path)
    print_analysis(stats)
    generate_module_map(stats)
    problems = identify_problems(stats)
    
    # Save results
    with open("simple_analysis_results.json", "w") as f:
        # Convert sets to lists for JSON serialization
        json_stats = {}
        for key, value in stats.items():
            if isinstance(value, defaultdict):
                json_stats[key] = dict(value)
            elif isinstance(value, Counter):
                json_stats[key] = dict(value)
            else:
                json_stats[key] = value
        json.dump(json_stats, f, indent=2)
    
    print(f"\n✅ Analysis complete!")
    print(f"📄 Results saved to: simple_analysis_results.json")
    print(f"🚨 Problems found: {len(problems)}")

if __name__ == "__main__":
    main()
