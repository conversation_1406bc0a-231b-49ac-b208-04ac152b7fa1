# Performance Monitoring Configuration
# Generated by apply_performance_optimizations.py

[monitoring]
enabled = true
interval_seconds = 30
dashboard_enabled = true
alerts_enabled = true

[thresholds]
response_time_warning = 2.0
response_time_critical = 5.0
memory_usage_warning = 70.0
memory_usage_critical = 85.0
cpu_usage_warning = 70.0
cpu_usage_critical = 85.0
cache_hit_rate_warning = 60.0
cache_hit_rate_critical = 40.0

[optimization]
auto_optimization = true
aggressive_caching = true
query_optimization = true
connection_pooling = true
resource_monitoring = true

[cache]
default_ttl = 1800
max_size = 10000
redis_enabled = true
memory_enabled = true
compression_enabled = true

[database]
pool_size = 20
max_overflow = 30
query_cache_enabled = true
slow_query_threshold = 2.0
connection_timeout = 10
statement_timeout = 30000

[alerts]
slack_webhook_url = 
email_notifications = false
discord_notifications = true
log_alerts = true
