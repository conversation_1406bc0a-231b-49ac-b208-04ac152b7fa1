# Simplified AI Model Configuration
# Clean, single source of truth for all AI model settings
# No over-engineering, just what we actually need

# Core Models - Only 4 models for 6 jobs
models:
  # Quick & Cheap - For simple parsing tasks
  quick:
    model_id: "gpt-4o-mini"
    description: "Fast, cheap model for symbol extraction and intent classification"
    max_tokens: 1000
    temperature: 0.1
    timeout_seconds: 10
    jobs:
      - symbol_extraction
      - intent_classification
    
  # Analysis - For market and technical analysis  
  analysis:
    model_id: "anthropic/claude-3.5-sonnet"
    description: "Balanced model for market and technical analysis"
    max_tokens: 4000
    temperature: 0.3
    timeout_seconds: 30
    jobs:
      - market_analysis
      - technical_analysis
      
  # Heavy - For complex reasoning and risk assessment
  heavy:
    model_id: "deepcogito/cogito-v2-preview-deepseek-671b"
    description: "Advanced model for risk assessment and complex reasoning"
    max_tokens: 8000
    temperature: 0.2
    timeout_seconds: 45
    jobs:
      - risk_assessment
      - user_explanations
      
  # Fallback - When others fail
  fallback:
    model_id: "gpt-4o-mini"
    description: "Reliable fallback when primary models fail"
    max_tokens: 2000
    temperature: 0.5
    timeout_seconds: 15
    jobs:
      - all

# Job to Model Mapping - Simple lookup
job_routing:
  symbol_extraction: "quick"
  intent_classification: "quick"
  market_analysis: "analysis"
  technical_analysis: "analysis"
  risk_assessment: "heavy"
  user_explanations: "heavy"
  fallback: "fallback"

# Provider Configuration - Just OpenRouter
provider:
  base_url: "https://openrouter.ai/api/v1"
  timeout_seconds: 30
  max_retries: 3
  
# Environment Overrides - Simple dev/prod differences
environments:
  development:
    # Use cheaper models in development
    quick:
      model_id: "x-ai/grok-4-fast:free"
    analysis:
      model_id: "x-ai/grok-4-fast:free"  # Cheaper for dev
    heavy:
      model_id: "x-ai/grok-4-fast:free"  # Cheaper for dev

  production:
    # Use optimal models in production
    quick:
      model_id: "x-ai/grok-4-fast:free"
    analysis:
      model_id: "x-ai/grok-4-fast:free"
    heavy:
      model_id: "x-ai/grok-4-fast:free"
