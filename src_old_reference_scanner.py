#!/usr/bin/env python3
"""
src(old) to src Migration Scanner
Scans for remaining references to src(old) modules that need to be updated to src.
Also identifies what's missing from src(old) that needs to be migrated.
"""

import os
import re
import json
import ast
from pathlib import Path
from typing import Dict, List, Set, Tuple, Any
from dataclasses import dataclass, asdict
from collections import defaultdict

@dataclass
class Reference:
    """A reference to src(old) found in the codebase that needs updating"""
    file_path: str
    line_number: int
    line_content: str
    reference_type: str  # 'import', 'from_import', 'string_literal', 'comment'
    old_module: str
    suggested_new_module: str = ""
    confidence: str = "unknown"  # 'high', 'medium', 'low', 'unknown'

class SrcOldReferenceScanner:
    """Scans for references to src(old) modules that need migration to src"""

    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.references: List[Reference] = []
        self.module_mapping = self._create_migration_mapping()

        # Patterns for different types of references
        self.import_patterns = [
            r'^\s*import\s+src\(old\)\.([^\s,]+)',
            r'^\s*from\s+src\(old\)\.([^\s]+)\s+import',
            r'^\s*from\s+src\(old\)\s+import',
        ]
        
        self.string_patterns = [
            r'["\']src\(old\)\.([^"\']+)["\']',
            r'["\']src\(old\)["\']',
        ]
        
        self.comment_patterns = [
            r'#.*src\(old\)\.([^\s]+)',
            r'#.*src\(old\)',
        ]
    
    def _create_migration_mapping(self) -> Dict[str, str]:
        """Create migration mapping from src(old) to src based on actual structure"""
        return {
            "src(old).mcp_server": "src.mcp",
            "src(old).shared": "src.shared",
            "src(old).core": "src.core",
            "src(old).data": "src.database",  # Main data models moved here
            "src(old).bot": "src.bot",
            "src(old).security": "src.security",
            "src(old).services": "src.services",
            "src(old).templates": "src.core.prompts",  # likely moved here
            "src(old).utils": "src.utils",
            
            # Specific submodules that may have moved
            "src(old).data.models": "src.database.models",
            "src(old).data.cache": "src.shared.cache",  # likely moved
            "src(old).bot.enhancements": "src.bot.commands",  # likely consolidated
            "src(old).bot.events": "src.bot.commands",  # likely consolidated
            "src(old).bot.security": "src.security",  # likely moved up
            "src(old).core.secure_cache": "src.shared.cache",  # likely moved
            "src(old).services.analytics_service": "src.services.ai",  # likely moved
            "src(old).templates.analysis_response": "src.core.prompts",  # likely moved
        }
    
    def scan_file(self, file_path: Path) -> List[Reference]:
        """Scan a single file for src(old) references"""
        references = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line_num, line in enumerate(lines, 1):
                line_stripped = line.strip()
                
                # Skip empty lines and pure comments
                if not line_stripped or line_stripped.startswith('#'):
                    continue
                
                # Check for import statements
                for pattern in self.import_patterns:
                    match = re.search(pattern, line)
                    if match:
                        old_module = f"src(old).{match.group(1)}" if match.groups() else "src(old)"
                        ref = Reference(
                            file_path=str(file_path),
                            line_number=line_num,
                            line_content=line.strip(),
                            reference_type='import',
                            old_module=old_module,
                            suggested_new_module=self._suggest_mapping(old_module),
                            confidence=self._assess_confidence(old_module)
                        )
                        references.append(ref)
                
                # Check for string literals
                for pattern in self.string_patterns:
                    matches = re.finditer(pattern, line)
                    for match in matches:
                        old_module = f"src(old).{match.group(1)}" if match.groups() else "src(old)"
                        ref = Reference(
                            file_path=str(file_path),
                            line_number=line_num,
                            line_content=line.strip(),
                            reference_type='string_literal',
                            old_module=old_module,
                            suggested_new_module=self._suggest_mapping(old_module),
                            confidence=self._assess_confidence(old_module)
                        )
                        references.append(ref)
                
                # Check for comments
                for pattern in self.comment_patterns:
                    match = re.search(pattern, line)
                    if match:
                        old_module = f"src(old).{match.group(1)}" if match.groups() else "src(old)"
                        ref = Reference(
                            file_path=str(file_path),
                            line_number=line_num,
                            line_content=line.strip(),
                            reference_type='comment',
                            old_module=old_module,
                            suggested_new_module=self._suggest_mapping(old_module),
                            confidence='low'  # Comments are lower priority
                        )
                        references.append(ref)
        
        except Exception as e:
            print(f"Error scanning {file_path}: {e}")
        
        return references
    
    def _suggest_mapping(self, old_module: str) -> str:
        """Suggest new module path for old module"""
        # Direct mapping
        if old_module in self.module_mapping:
            return self.module_mapping[old_module]
        
        # Pattern-based mapping for submodules
        for old_pattern, new_pattern in self.module_mapping.items():
            if old_module.startswith(old_pattern + "."):
                suffix = old_module[len(old_pattern):]
                return new_pattern + suffix
        
        # Default fallback - just replace src(old) with src
        return old_module.replace("src(old)", "src")
    
    def _assess_confidence(self, old_module: str) -> str:
        """Assess confidence level of the mapping"""
        if old_module in self.module_mapping:
            return "high"
        
        # Check if it's a known pattern
        for pattern in self.module_mapping.keys():
            if old_module.startswith(pattern + "."):
                return "medium"
        
        return "low"
    
    def scan_codebase(self) -> List[Reference]:
        """Scan entire codebase for src(old) references"""
        print("🔍 Scanning codebase for src(old) references...")
        
        # Find all Python files
        python_files = list(self.project_root.rglob("*.py"))
        
        # Also scan other relevant files
        other_files = []
        for pattern in ["*.md", "*.txt", "*.yml", "*.yaml", "*.json", "*.sh"]:
            other_files.extend(self.project_root.rglob(pattern))
        
        all_files = python_files + other_files
        
        print(f"📁 Found {len(all_files)} files to scan")
        
        all_references = []
        for file_path in all_files:
            # Skip certain directories
            if any(skip in str(file_path) for skip in ['.git', '__pycache__', '.venv', 'venv', 'node_modules']):
                continue
            
            references = self.scan_file(file_path)
            all_references.extend(references)
        
        self.references = all_references
        print(f"✅ Found {len(all_references)} src(old) references")
        
        return all_references
    
    def generate_mapping_json(self, output_file: str = "src_old_mapping.json") -> str:
        """Generate machine-readable mapping file"""
        mapping_data = {
            "scan_timestamp": str(Path().cwd()),
            "total_references": len(self.references),
            "module_mapping": self.module_mapping,
            "references": [asdict(ref) for ref in self.references],
            "summary": self._generate_summary()
        }
        
        with open(output_file, 'w') as f:
            json.dump(mapping_data, f, indent=2)
        
        print(f"📄 Generated mapping file: {output_file}")
        return output_file
    
    def generate_markdown_report(self, output_file: str = "src_old_references.md") -> str:
        """Generate human-readable markdown report"""
        report_lines = [
            "# src(old) References Report",
            "",
            f"**Total References Found:** {len(self.references)}",
            "",
            "## Summary by Type",
            ""
        ]
        
        # Summary by type
        by_type = defaultdict(int)
        for ref in self.references:
            by_type[ref.reference_type] += 1
        
        for ref_type, count in sorted(by_type.items()):
            report_lines.append(f"- **{ref_type}**: {count}")
        
        report_lines.extend([
            "",
            "## Summary by Confidence",
            ""
        ])
        
        # Summary by confidence
        by_confidence = defaultdict(int)
        for ref in self.references:
            by_confidence[ref.confidence] += 1
        
        for confidence, count in sorted(by_confidence.items()):
            report_lines.append(f"- **{confidence}**: {count}")
        
        report_lines.extend([
            "",
            "## Detailed References",
            ""
        ])
        
        # Group by file
        by_file = defaultdict(list)
        for ref in self.references:
            by_file[ref.file_path].append(ref)
        
        for file_path in sorted(by_file.keys()):
            report_lines.append(f"### {file_path}")
            report_lines.append("")
            
            for ref in by_file[file_path]:
                report_lines.append(f"**Line {ref.line_number}** ({ref.reference_type}, {ref.confidence} confidence)")
                report_lines.append(f"```")
                report_lines.append(ref.line_content)
                report_lines.append(f"```")
                report_lines.append(f"- **Old:** `{ref.old_module}`")
                report_lines.append(f"- **Suggested:** `{ref.suggested_new_module}`")
                report_lines.append("")
        
        with open(output_file, 'w') as f:
            f.write('\n'.join(report_lines))
        
        print(f"📋 Generated report: {output_file}")
        return output_file
    
    def _generate_summary(self) -> Dict[str, Any]:
        """Generate summary statistics"""
        by_type = defaultdict(int)
        by_confidence = defaultdict(int)
        by_module = defaultdict(int)
        
        for ref in self.references:
            by_type[ref.reference_type] += 1
            by_confidence[ref.confidence] += 1
            by_module[ref.old_module] += 1
        
        return {
            "by_type": dict(by_type),
            "by_confidence": dict(by_confidence),
            "by_module": dict(by_module),
            "unique_files": len(set(ref.file_path for ref in self.references)),
            "unique_modules": len(set(ref.old_module for ref in self.references))
        }

def main():
    """Main function to run the scanner"""
    scanner = SrcOldReferenceScanner()
    
    # Scan the codebase
    references = scanner.scan_codebase()
    
    if not references:
        print("🎉 No src(old) references found!")
        return
    
    # Generate reports
    scanner.generate_mapping_json()
    scanner.generate_markdown_report()
    
    print("\n📊 Summary:")
    summary = scanner._generate_summary()
    print(f"- Total references: {len(references)}")
    print(f"- Unique files: {summary['unique_files']}")
    print(f"- Unique modules: {summary['unique_modules']}")
    print(f"- High confidence: {summary['by_confidence'].get('high', 0)}")
    print(f"- Medium confidence: {summary['by_confidence'].get('medium', 0)}")
    print(f"- Low confidence: {summary['by_confidence'].get('low', 0)}")

if __name__ == "__main__":
    main()
