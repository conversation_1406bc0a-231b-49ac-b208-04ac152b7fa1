#!/usr/bin/env python3
"""Test Discord bot startup"""

from dotenv import load_dotenv
import os

# Load environment variables
load_dotenv()

print("🚀 Testing Discord Bot Startup")
print("=" * 50)

# Check environment variables
print("🔧 Checking Environment Variables...")
required_vars = ["DISCORD_BOT_TOKEN", "OPENROUTER_API_KEY", "SUPABASE_URL", "SUPABASE_KEY"]
for var in required_vars:
    status = "✅ SET" if os.getenv(var) else "❌ NOT SET"
    print(f"  {var}: {status}")

print("\n🧪 Testing Bot Import...")
try:
    import src.bot.main
    print("✅ Bot main imported successfully")
    print("✅ Bot is ready to run with Discord")
    print("\n🎯 To run the bot, use: python -m src.bot.main")
    print("🎯 The /ask, /analyze, and /health commands should work")
except Exception as e:
    print(f"❌ Error importing bot: {e}")
    import traceback
    traceback.print_exc()

print("\n" + "=" * 50)
print("✅ Discord bot test completed")
