"""
Test suite for Ask Command Extension

Tests the ask command functionality including:
- Command initialization
- Query processing
- Error handling
- Response formatting
- Audit logging
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from datetime import datetime

from src.bot.extensions.ask import AskCommand
from src.shared.error_handling.logging import get_logger

logger = get_logger(__name__)


class TestAskCommand:
    """Test the AskCommand class"""

    @pytest.fixture
    def mock_bot(self):
        """Create a mock bot for testing"""
        bot = Mock()
        bot.user = Mock()
        bot.user.display_name = "TestBot"
        return bot

    @pytest.fixture
    def ask_command(self, mock_bot):
        """Create an AskCommand instance for testing"""
        return AskCommand(mock_bot)

    @pytest.fixture
    def mock_interaction(self):
        """Create a mock Discord interaction"""
        interaction = Mock()
        interaction.user = Mock()
        interaction.user.id = 12345
        interaction.user.display_name = "TestUser"
        interaction.response = Mock()
        interaction.followup = Mock()
        interaction.guild_id = 67890
        interaction.channel = Mock()
        interaction.channel.id = 11111
        return interaction

    def test_ask_command_initialization(self, ask_command, mock_bot):
        """Test AskCommand initialization"""
        assert ask_command.bot == mock_bot
        assert hasattr(ask_command, 'ask_command')
        assert callable(ask_command.ask_command)

    @pytest.mark.asyncio
    async def test_ask_command_success(self, ask_command, mock_interaction):
        """Test successful ask command execution"""
        query = "What is the current price of AAPL?"
        
        # Mock the pipeline execution
        mock_response = {
            "response": "AAPL is currently trading at $150.00",
            "confidence": 0.95,
            "sources": ["market_data"],
            "timestamp": datetime.now().isoformat()
        }
        
        with patch('src.bot.extensions.ask.execute_ask_pipeline', new_callable=AsyncMock) as mock_pipeline:
            with patch('src.bot.extensions.ask.format_response_for_discord') as mock_format:
                with patch('src.bot.extensions.ask.create_audit_logger') as mock_audit:
                    # Setup mocks
                    mock_pipeline.return_value = mock_response
                    mock_format.return_value = "Formatted response"
                    mock_audit_logger = Mock()
                    mock_audit.return_value = mock_audit_logger
                    
                    # Execute command
                    await ask_command.ask_command(mock_interaction, query)
                    
                    # Verify pipeline was called
                    mock_pipeline.assert_called_once()
                    call_args = mock_pipeline.call_args
                    assert call_args[1]['query'] == query
                    assert call_args[1]['user_id'] == str(mock_interaction.user.id)
                    
                    # Verify response was sent
                    mock_interaction.response.send_message.assert_called_once()
                    
                    # Verify audit logging
                    mock_audit_logger.start_step.assert_called()
                    mock_audit_logger.complete_step.assert_called()

    @pytest.mark.asyncio
    async def test_ask_command_with_empty_query(self, ask_command, mock_interaction):
        """Test ask command with empty query"""
        query = ""
        
        with patch('src.bot.extensions.ask.create_audit_logger') as mock_audit:
            mock_audit_logger = Mock()
            mock_audit.return_value = mock_audit_logger
            
            await ask_command.ask_command(mock_interaction, query)
            
            # Should still process but with empty query
            mock_interaction.response.send_message.assert_called_once()

    @pytest.mark.asyncio
    async def test_ask_command_with_long_query(self, ask_command, mock_interaction):
        """Test ask command with very long query"""
        query = "A" * 2000  # Very long query
        
        with patch('src.bot.extensions.ask.execute_ask_pipeline', new_callable=AsyncMock) as mock_pipeline:
            with patch('src.bot.extensions.ask.format_response_for_discord') as mock_format:
                with patch('src.bot.extensions.ask.create_audit_logger') as mock_audit:
                    # Setup mocks
                    mock_pipeline.return_value = {"response": "Processed long query"}
                    mock_format.return_value = "Formatted response"
                    mock_audit_logger = Mock()
                    mock_audit.return_value = mock_audit_logger
                    
                    # Execute command
                    await ask_command.ask_command(mock_interaction, query)
                    
                    # Should handle long queries gracefully
                    mock_pipeline.assert_called_once()
                    mock_interaction.response.send_message.assert_called_once()

    @pytest.mark.asyncio
    async def test_ask_command_pipeline_error(self, ask_command, mock_interaction):
        """Test ask command when pipeline execution fails"""
        query = "What is the price of INVALID_SYMBOL?"
        
        with patch('src.bot.extensions.ask.execute_ask_pipeline', new_callable=AsyncMock) as mock_pipeline:
            with patch('src.bot.extensions.ask.create_audit_logger') as mock_audit:
                # Setup mocks
                mock_pipeline.side_effect = Exception("Pipeline error")
                mock_audit_logger = Mock()
                mock_audit.return_value = mock_audit_logger
                
                # Execute command
                await ask_command.ask_command(mock_interaction, query)
                
                # Should handle error gracefully
                mock_interaction.response.send_message.assert_called_once()
                # Should log the error
                mock_audit_logger.log_error.assert_called()

    @pytest.mark.asyncio
    async def test_ask_command_formatting_error(self, ask_command, mock_interaction):
        """Test ask command when response formatting fails"""
        query = "What is the price of AAPL?"
        
        with patch('src.bot.extensions.ask.execute_ask_pipeline', new_callable=AsyncMock) as mock_pipeline:
            with patch('src.bot.extensions.ask.format_response_for_discord') as mock_format:
                with patch('src.bot.extensions.ask.create_audit_logger') as mock_audit:
                    # Setup mocks
                    mock_pipeline.return_value = {"response": "Test response"}
                    mock_format.side_effect = Exception("Formatting error")
                    mock_audit_logger = Mock()
                    mock_audit.return_value = mock_audit_logger
                    
                    # Execute command
                    await ask_command.ask_command(mock_interaction, query)
                    
                    # Should handle formatting error gracefully
                    mock_interaction.response.send_message.assert_called_once()
                    mock_audit_logger.log_error.assert_called()

    @pytest.mark.asyncio
    async def test_ask_command_audit_logging(self, ask_command, mock_interaction):
        """Test that audit logging works correctly"""
        query = "What is the current market sentiment?"
        
        with patch('src.bot.extensions.ask.execute_ask_pipeline', new_callable=AsyncMock) as mock_pipeline:
            with patch('src.bot.extensions.ask.format_response_for_discord') as mock_format:
                with patch('src.bot.extensions.ask.create_audit_logger') as mock_audit:
                    # Setup mocks
                    mock_pipeline.return_value = {"response": "Market is bullish"}
                    mock_format.return_value = "Formatted response"
                    mock_audit_logger = Mock()
                    mock_audit.return_value = mock_audit_logger
                    
                    # Execute command
                    await ask_command.ask_command(mock_interaction, query)
                    
                    # Verify audit logger was created with correct parameters
                    mock_audit.assert_called_once()
                    call_args = mock_audit.call_args
                    assert 'correlation_id' in call_args[1]
                    assert call_args[1]['user_id'] == str(mock_interaction.user.id)
                    assert call_args[1]['query'] == query
                    
                    # Verify audit steps were logged
                    assert mock_audit_logger.start_step.called
                    assert mock_audit_logger.complete_step.called

    @pytest.mark.asyncio
    async def test_ask_command_response_types(self, ask_command, mock_interaction):
        """Test different types of responses from the pipeline"""
        test_cases = [
            {
                "query": "What is the price of AAPL?",
                "response": {"response": "AAPL is at $150.00", "confidence": 0.95}
            },
            {
                "query": "Analyze the market trends",
                "response": {"response": "Market analysis...", "charts": ["chart1.png"]}
            },
            {
                "query": "What are the risks?",
                "response": {"response": "Risk analysis...", "warnings": ["High volatility"]}
            }
        ]
        
        for test_case in test_cases:
            with patch('src.bot.extensions.ask.execute_ask_pipeline', new_callable=AsyncMock) as mock_pipeline:
                with patch('src.bot.extensions.ask.format_response_for_discord') as mock_format:
                    with patch('src.bot.extensions.ask.create_audit_logger') as mock_audit:
                        # Setup mocks
                        mock_pipeline.return_value = test_case["response"]
                        mock_format.return_value = "Formatted response"
                        mock_audit_logger = Mock()
                        mock_audit.return_value = mock_audit_logger
                        
                        # Execute command
                        await ask_command.ask_command(mock_interaction, test_case["query"])
                        
                        # Verify pipeline was called with correct query
                        mock_pipeline.assert_called_once()
                        call_args = mock_pipeline.call_args
                        assert call_args[1]['query'] == test_case["query"]
                        
                        # Verify response was sent
                        mock_interaction.response.send_message.assert_called_once()

    @pytest.mark.asyncio
    async def test_ask_command_timeout_handling(self, ask_command, mock_interaction):
        """Test ask command timeout handling"""
        query = "Complex analysis that might timeout"
        
        with patch('src.bot.extensions.ask.execute_ask_pipeline', new_callable=AsyncMock) as mock_pipeline:
            with patch('src.bot.extensions.ask.create_audit_logger') as mock_audit:
                # Setup mocks
                mock_pipeline.side_effect = asyncio.TimeoutError("Pipeline timeout")
                mock_audit_logger = Mock()
                mock_audit.return_value = mock_audit_logger
                
                # Execute command
                await ask_command.ask_command(mock_interaction, query)
                
                # Should handle timeout gracefully
                mock_interaction.response.send_message.assert_called_once()
                mock_audit_logger.log_error.assert_called()

    def test_ask_command_attributes(self, ask_command):
        """Test that AskCommand has required attributes and methods"""
        assert hasattr(ask_command, 'bot')
        assert hasattr(ask_command, 'ask_command')
        assert callable(ask_command.ask_command)
        
        # Check that it's a Discord app command
        assert hasattr(ask_command.ask_command, '__app_commands_meta__')

    @pytest.mark.asyncio
    async def test_ask_command_with_special_characters(self, ask_command, mock_interaction):
        """Test ask command with special characters in query"""
        special_queries = [
            "What's the price of AAPL?",
            "Analyze $SPY vs $QQQ",
            "What about crypto? BTC/ETH",
            "Query with émojis 🚀📈💰"
        ]
        
        for query in special_queries:
            with patch('src.bot.extensions.ask.execute_ask_pipeline', new_callable=AsyncMock) as mock_pipeline:
                with patch('src.bot.extensions.ask.format_response_for_discord') as mock_format:
                    with patch('src.bot.extensions.ask.create_audit_logger') as mock_audit:
                        # Setup mocks
                        mock_pipeline.return_value = {"response": f"Processed: {query}"}
                        mock_format.return_value = "Formatted response"
                        mock_audit_logger = Mock()
                        mock_audit.return_value = mock_audit_logger
                        
                        # Execute command
                        await ask_command.ask_command(mock_interaction, query)
                        
                        # Should handle special characters gracefully
                        mock_pipeline.assert_called_once()
                        mock_interaction.response.send_message.assert_called_once()


class TestAskCommandIntegration:
    """Integration tests for AskCommand"""

    @pytest.fixture
    def mock_bot(self):
        """Create a mock bot for integration testing"""
        bot = Mock()
        bot.user = Mock()
        bot.user.display_name = "TestBot"
        return bot

    @pytest.fixture
    def ask_command(self, mock_bot):
        """Create an AskCommand instance for integration testing"""
        return AskCommand(mock_bot)

    @pytest.mark.asyncio
    async def test_ask_command_full_workflow(self, ask_command):
        """Test the complete ask command workflow"""
        # Create a more realistic mock interaction
        interaction = Mock()
        interaction.user = Mock()
        interaction.user.id = 12345
        interaction.user.display_name = "TestUser"
        interaction.response = Mock()
        interaction.followup = Mock()
        interaction.guild_id = 67890
        interaction.channel = Mock()
        interaction.channel.id = 11111
        
        query = "What is the current market analysis for technology stocks?"
        
        # Mock the entire pipeline
        with patch('src.bot.extensions.ask.execute_ask_pipeline', new_callable=AsyncMock) as mock_pipeline:
            with patch('src.bot.extensions.ask.format_response_for_discord') as mock_format:
                with patch('src.bot.extensions.ask.create_audit_logger') as mock_audit:
                    # Setup comprehensive mocks
                    mock_response = {
                        "response": "Technology stocks are showing strong performance...",
                        "confidence": 0.92,
                        "sources": ["market_data", "technical_analysis"],
                        "timestamp": datetime.now().isoformat(),
                        "recommendations": ["AAPL", "MSFT", "GOOGL"]
                    }
                    
                    mock_pipeline.return_value = mock_response
                    mock_format.return_value = "**Market Analysis:** Technology stocks are showing strong performance..."
                    mock_audit_logger = Mock()
                    mock_audit.return_value = mock_audit_logger
                    
                    # Execute the complete workflow
                    await ask_command.ask_command(interaction, query)
                    
                    # Verify all components were called correctly
                    mock_audit.assert_called_once()
                    mock_pipeline.assert_called_once()
                    mock_format.assert_called_once_with(mock_response)
                    
                    # Verify interaction methods were called
                    interaction.response.send_message.assert_called_once()
                    
                    # Verify audit logging
                    assert mock_audit_logger.start_step.called
                    assert mock_audit_logger.complete_step.called
