"""
Comprehensive Testing Framework for ASK Pipeline

This test suite provides:
- Unit tests for individual components
- Integration tests for pipeline flow
- Performance tests under load
- Security validation tests
- Compliance and audit tests
- End-to-end testing with mock services
"""

import asyncio
import pytest
import time
import json
from unittest.mock import Mock, patch, AsyncMock
from typing import Dict, Any, Optional

# Import system under test
from src.bot.pipeline.commands.ask.core.controller import <PERSON><PERSON><PERSON>elineController
from src.bot.pipeline.commands.ask.stages.intent_detector import IntentDetector
from src.bot.pipeline.commands.ask.stages.mcp_orchestrator import get_mcp_orchestrator
from src.bot.pipeline.commands.ask.stages.response_generator import ResponseGenerator
from src.bot.pipeline.commands.ask.config.ask_config import get_ask_config
from src.shared.monitoring.observability import observability_manager
from src.bot.pipeline.commands.ask.security.security_manager import validate_request_input
from src.shared.cache.cache_service import cache_service

# Test configuration
TEST_CONFIG = {
    'test_user_id': 'test_user_123',
    'test_query': 'What is the current price of AAPL?',
    'expected_intent': 'price_check',
    'expected_confidence': 0.8,
    'max_response_time': 5.0,  # seconds
    'test_timeout': 10.0
}

@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

class TestPipelineComponents:
    """Test individual pipeline components"""

    @pytest.fixture
    def intent_detector(self):
        """Fixture for intent detector"""
        return IntentDetector()

    @pytest.fixture
    def mcp_orchestrator(self):
        """Fixture for MCP orchestrator"""
        return get_mcp_orchestrator()

    @pytest.fixture
    def response_generator(self):
        """Fixture for response generator"""
        return ResponseGenerator()

    @pytest.fixture
    def pipeline_controller(self):
        """Fixture for pipeline controller"""
        return AskPipelineController()

    def test_intent_detection(self, intent_detector):
        """Test intent detection accuracy"""
        query = "What's the current price of AAPL?"
        result = intent_detector.detect(query, "test_correlation_id")
        
        assert result.intent == "price_check"
        assert result.confidence >= 0.8
        assert isinstance(result.entities, dict)
        assert "symbol" in result.entities
        assert result.entities["symbol"] == "AAPL"

    @pytest.mark.asyncio
    async def test_mcp_tool_execution(self, mcp_orchestrator):
        """Test MCP tool execution"""
        session_id = await mcp_orchestrator.create_session("test_user", "test_correlation")
        
        tools_to_execute = [
            {
                'name': 'get_stock_price',
                'args': {'symbol': 'AAPL'}
            }
        ]
        
        context = {
            'user_id': 'test_user',
            'session_id': session_id
        }
        
        result = await mcp_orchestrator.execute_tools(session_id, tools_to_execute, context)
        
        assert result['success'] is True
        assert 'tools_used' in result
        assert len(result['tools_used']) == 1
        assert 'results' in result
        assert 'get_stock_price' in result['results']
        assert result['results']['get_stock_price']['success'] is True
        
        # Cleanup
        await mcp_orchestrator.close_session(session_id)

    @pytest.mark.asyncio
    async def test_response_generation(self, response_generator):
        """Test response generation"""
        mock_intent = Mock()
        mock_intent.intent = "price_check"
        mock_intent.confidence = 0.9
        mock_intent.entities = {'symbol': 'AAPL'}
        
        mock_tool_result = Mock()
        mock_tool_result.tools_used = ['get_stock_price']
        mock_tool_result.results = {
            'get_stock_price': {
                'success': True,
                'data': {'symbol': 'AAPL', 'price': 150.25}
            }
        }
        
        result = await response_generator.generate(
            query="What's the price of AAPL?",
            intent_result=mock_intent,
            tool_result=mock_tool_result,
            correlation_id="test_correlation"
        )
        
        assert result.response is not None
        assert len(result.response) > 50
        assert result.confidence > 0.7
        assert result.execution_time > 0
        assert "AAPL" in result.response
        assert "$150" in result.response  # Approximate price
        assert "Disclaimer" in result.response  # Trading disclaimer

    def test_config_loading(self):
        """Test configuration loading"""
        config = get_ask_config()
        
        assert config is not None
        assert config.intent_detection.timeout > 0
        assert config.tools.max_concurrent > 0
        assert config.response.model is not None
        assert config.cache.enabled is True
        assert config.monitoring.metrics_enabled is True
        assert config.security.rate_limiting_enabled is True

class TestPipelineIntegration:
    """Test full pipeline integration"""

    @pytest.fixture
    def pipeline_controller(self):
        """Fixture for pipeline controller with test config"""
        config = get_ask_config()
        config.max_response_time = 10.0  # Increase for tests
        return AskPipelineController()

    @pytest.mark.asyncio
    async def test_full_pipeline_execution(self, pipeline_controller):
        """Test complete pipeline execution"""
        start_time = time.time()
        
        result = await pipeline_controller.process(
            query="What's the current price of AAPL?",
            user_id=TEST_CONFIG['test_user_id']
        )
        
        execution_time = time.time() - start_time
        
        # Basic functionality tests
        assert result.success is True
        assert result.response is not None
        assert len(result.response) > 100
        assert "AAPL" in result.response
        assert result.execution_time > 0
        assert result.execution_time < TEST_CONFIG['test_timeout']
        assert result.correlation_id is not None
        
        # Check for required components
        assert "price" in result.response.lower()
        assert "disclaimer" in result.response.lower()
        
        # Performance validation
        assert execution_time < TEST_CONFIG['max_response_time'], f"Execution time {execution_time:.2f}s exceeded limit"
        
        logger.info(f"Full pipeline test passed in {execution_time:.2f}s")

    @pytest.mark.asyncio
    async def test_pipeline_error_handling(self, pipeline_controller):
        """Test pipeline error handling and recovery"""
        # Test with invalid query to trigger error handling
        result = await pipeline_controller.process(
            query="INVALID QUERY TO TRIGGER ERROR",
            user_id=TEST_CONFIG['test_user_id']
        )
        
        # Should handle error gracefully
        assert result.success is False
        assert result.error is not None
        assert "error" in result.error.lower()
        assert result.response is not None  # Should have fallback response
        assert len(result.response) > 20
        
        logger.info("Error handling test passed")

    @pytest.mark.asyncio
    async def test_pipeline_with_caching(self, pipeline_controller):
        """Test pipeline caching behavior"""
        # First execution - should not hit cache
        result1 = await pipeline_controller.process(
            query="What's the price of AAPL?",
            user_id=TEST_CONFIG['test_user_id']
        )
        
        # Second execution - should benefit from caching
        result2 = await pipeline_controller.process(
            query="What's the price of AAPL?",
            user_id=TEST_CONFIG['test_user_id']
        )
        
        # Second execution should be faster due to caching
        assert result2.execution_time < result1.execution_time * 1.5, "Caching should improve performance"
        
        logger.info("Caching test passed")

class TestSecurityAndCompliance:
    """Test security and compliance features"""

    @pytest.mark.asyncio
    async def test_input_validation(self):
        """Test input validation security"""
        test_cases = [
            {
                'input': {'query': "What's the price of AAPL?"},
                'expected': True,
                'issues': []
            },
            {
                'input': {'query': "'; DROP TABLE users; --"},
                'expected': False,
                'issues': ['query: Suspicious patterns detected: sql_injection']
            },
            {
                'input': {'query': "<script>alert('xss')</script>"},
                'expected': False,
                'issues': ['query: Suspicious patterns detected: xss']
            },
            {
                'input': {'query': "Normal query with no issues"},
                'expected': True,
                'issues': []
            }
        ]

        for i, test_case in enumerate(test_cases):
            is_valid, issues = validate_request_input(test_case['input'])
            
            assert is_valid == test_case['expected']
            assert len(issues) > 0 if not is_valid else len(issues) == 0
            
            if not is_valid:
                assert any(threat in str(issues[0]) for threat in ['suspicious', 'pattern']) 

        logger.info("Input validation tests passed")

    @pytest.mark.asyncio
    async def test_rate_limiting(self):
        """Test rate limiting functionality"""
        from src.bot.pipeline.commands.ask.security.security_manager import check_rate_limit
        
        # Test normal rate limit
        for i in range(3):
            allowed = await check_rate_limit("test_user", "user")
            assert allowed is True
        
        # Test rate limit exceeded (this is async, so we need to wait between calls)
        await asyncio.sleep(1)
        allowed = await check_rate_limit("test_user", "user")
        assert allowed is True  # Should still pass for now
        
        logger.info("Rate limiting test passed (basic)")

class TestPerformanceAndLoad:
    """Test performance under load"""

    @pytest.mark.asyncio
    async def test_concurrent_requests(self):
        """Test pipeline performance under concurrent load"""
        from concurrent.futures import ThreadPoolExecutor
        import concurrent.futures
        
        async def single_request():
            controller = AskPipelineController()
            start = time.time()
            result = await controller.process(
                query="What's the price of AAPL?",
                user_id=f"load_test_user_{time.time()}"
            )
            duration = time.time() - start
            return duration, result.success
        
        # Run 10 concurrent requests
        concurrency = 10
        tasks = [single_request() for _ in range(concurrency)]
        results = await asyncio.gather(*tasks)
        
        durations, successes = zip(*results)
        
        # All should succeed
        assert all(successes), "All concurrent requests should succeed"
        
        # Average response time should be reasonable
        avg_duration = sum(durations) / len(durations)
        assert avg_duration < 5.0, f"Average response time {avg_duration:.2f}s too high under load"
        
        logger.info(f"Concurrent load test passed: avg {avg_duration:.2f}s")

    def test_cache_performance(self):
        """Test cache hit/miss performance"""
        # Warm up cache
        cache_service.set("test_key", "test_value", ttl=60)
        
        # Test cache hit
        start = time.time()
        value = cache_service.get("test_key")
        hit_time = time.time() - start
        
        # Test cache miss
        start = time.time()
        value = cache_service.get("miss_key")
        miss_time = time.time() - start
        
        # Hit should be faster than miss
        assert hit_time < miss_time * 2, "Cache hit should be significantly faster than miss"
        
        logger.info("Cache performance test passed")

class TestComplianceAndAudit:
    """Test compliance and audit features"""

    def test_audit_logging(self):
        """Test audit logging functionality"""
        from src.bot.pipeline.commands.ask.audit import get_audit_logger
        
        audit_logger = get_audit_logger()
        
        # Test audit event logging
        event_id = audit_logger.log_data_access(
            user_id="test_user",
            resource_id="test_resource",
            data_type="market_data"
        )
        
        assert event_id is not None
        assert len(event_id) > 0
        
        # Test audit summary
        summary = audit_logger.get_audit_summary(hours=1)
        assert 'total_events' in summary
        assert isinstance(summary['total_events'], int)
        
        logger.info("Audit logging test passed")

    def test_data_retention(self):
        """Test data retention policies"""
        from src.bot.pipeline.commands.ask.compliance.data_manager import get_data_manager
        
        data_manager = get_data_manager()
        
        # Create test record
        record_id = data_manager.create_data_record(
            data_type="test_data",
            classification="internal",
            data_subject_id="test_subject"
        )
        
        assert record_id is not None
        
        # Check retention summary
        summary = data_manager.get_retention_summary()
        assert 'total_records' in summary
        assert summary['total_records'] > 0
        
        logger.info("Data retention test passed")

class TestEndToEnd:
    """End-to-end testing with real data flows"""

    @pytest.mark.asyncio
    async def test_end_to_end_conversation(self):
        """Test multi-turn conversation flow"""
        controller = AskPipelineController()
        
        # First message - casual greeting
        result1 = await controller.process(
            query="Hi, can you help me with stock analysis?",
            user_id=TEST_CONFIG['test_user_id']
        )
        
        assert result1.success
        assert "help" in result1.response.lower()
        assert "analysis" in result1.response.lower()
        
        # Second message - specific analysis request
        result2 = await controller.process(
            query="Show me technical analysis for TSLA",
            user_id=TEST_CONFIG['test_user_id']
        )
        
        assert result2.success
        assert "TSLA" in result2.response
        assert any(indicator in result2.response.lower() for indicator in ['rsi', 'macd', 'moving average'])
        
        # Third message - follow-up question
        result3 = await controller.process(
            query="What about the risk level?",
            user_id=TEST_CONFIG['test_user_id']
        )
        
        assert result3.success
        assert "risk" in result3.response.lower()
        assert "volatility" in result3.response.lower()
        
        logger.info("End-to-end conversation test passed")

    @pytest.mark.asyncio
    async def test_different_intent_types(self):
        """Test various intent types"""
        controller = AskPipelineController()
        test_cases = [
            ("What's AAPL price?", "price_check"),
            ("Show TSLA technical analysis", "technical_analysis"),
            ("Give me market news", "news_search"),
            ("Explain RSI indicator", "educational_query"),
            ("Hello, how are you?", "casual")
        ]
        
        for query, expected_intent in test_cases:
            result = await controller.process(query, TEST_CONFIG['test_user_id'])
            
            assert result.success
            assert expected_intent in result.intent or "intent" in result.intent.lower()
            assert len(result.response) > 50
            
        logger.info("Multi-intent test passed")

# Run tests
if __name__ == "__main__":
    pytest.main([__file__, "-v"])