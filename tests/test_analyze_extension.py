"""
Test suite for Analyze Command Extension

Tests the analyze command functionality including:
- Command initialization
- Symbol analysis
- Caching mechanism
- Permission handling
- Error handling
- Response formatting
"""

import pytest
import asyncio
import sys
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from datetime import datetime, timedelta
import hashlib

# Mock the missing import before importing the module
with patch.dict('sys.modules', {'src.bot.pipeline.commands.analyze.parallel_pipeline': Mock()}):
    from src.bot.extensions.analyze import AsyncAnalyzeCommands
from src.bot.permissions import PermissionLevel
from src.shared.error_handling.logging import get_logger

logger = get_logger(__name__)


class TestAsyncAnalyzeCommands:
    """Test the AsyncAnalyzeCommands class"""

    @pytest.fixture
    def mock_bot(self):
        """Create a mock bot for testing"""
        bot = Mock()
        bot.user = Mock()
        bot.user.display_name = "TestBot"
        bot.permission_checker = Mock()
        return bot

    @pytest.fixture
    def analyze_commands(self, mock_bot):
        """Create an AsyncAnalyzeCommands instance for testing"""
        return AsyncAnalyzeCommands(mock_bot)

    @pytest.fixture
    def mock_interaction(self):
        """Create a mock Discord interaction"""
        interaction = Mock()
        interaction.user = Mock()
        interaction.user.id = 12345
        interaction.user.display_name = "TestUser"
        interaction.response = Mock()
        interaction.followup = Mock()
        interaction.guild_id = 67890
        interaction.channel = Mock()
        interaction.channel.id = 11111
        return interaction

    def test_analyze_commands_initialization(self, analyze_commands, mock_bot):
        """Test AsyncAnalyzeCommands initialization"""
        assert analyze_commands.bot == mock_bot
        assert analyze_commands.max_concurrent_analyses == 3
        assert hasattr(analyze_commands, 'semaphore')
        assert hasattr(analyze_commands, 'cache')
        assert hasattr(analyze_commands, 'cache_timestamps')

    def test_get_cache_key(self, analyze_commands):
        """Test cache key generation"""
        symbol = "AAPL"
        timeframe = "1D"
        
        cache_key = analyze_commands._get_cache_key(symbol, timeframe)
        
        # Should generate consistent hash
        expected_key = hashlib.md5(f"{symbol}:{timeframe}".encode()).hexdigest()
        assert cache_key == expected_key
        
        # Should be consistent for same inputs
        cache_key2 = analyze_commands._get_cache_key(symbol, timeframe)
        assert cache_key == cache_key2

    def test_is_cache_valid_fresh_data(self, analyze_commands):
        """Test cache validation for fresh data"""
        cache_key = "test_key"
        now = datetime.now()
        
        # Add fresh data to cache
        analyze_commands.cache_timestamps[cache_key] = now
        
        # Should be valid for fresh data
        assert analyze_commands._is_cache_valid(cache_key, "technical_analysis", True) is True

    def test_is_cache_valid_stale_data(self, analyze_commands):
        """Test cache validation for stale data"""
        cache_key = "test_key"
        stale_time = datetime.now() - timedelta(hours=2)  # 2 hours old
        
        # Add stale data to cache
        analyze_commands.cache_timestamps[cache_key] = stale_time
        
        # Should be invalid for stale data
        assert analyze_commands._is_cache_valid(cache_key, "technical_analysis", True) is False

    def test_is_cache_valid_no_data(self, analyze_commands):
        """Test cache validation when no data needed"""
        cache_key = "test_key"
        now = datetime.now()
        
        # Add data to cache
        analyze_commands.cache_timestamps[cache_key] = now
        
        # Should be valid when no data needed
        assert analyze_commands._is_cache_valid(cache_key, "technical_analysis", False) is True

    @pytest.mark.asyncio
    async def test_analyze_command_success(self, analyze_commands, mock_interaction):
        """Test successful analyze command execution"""
        symbol = "AAPL"
        timeframe = "1D"
        
        # Mock the pipeline execution
        mock_analysis = {
            "symbol": symbol,
            "timeframe": timeframe,
            "analysis": "Strong bullish trend detected",
            "confidence": 0.85,
            "recommendations": ["BUY", "HOLD"],
            "timestamp": datetime.now().isoformat()
        }
        
        with patch('src.bot.extensions.analyze.execute_parallel_analyze_pipeline', new_callable=AsyncMock) as mock_pipeline:
            with patch('src.bot.extensions.analyze.add_disclaimer') as mock_disclaimer:
                # Setup mocks
                mock_pipeline.return_value = mock_analysis
                mock_disclaimer.return_value = "Analysis with disclaimer"
                
                # Execute command
                await analyze_commands.analyze_command(mock_interaction, symbol, timeframe)
                
                # Verify pipeline was called
                mock_pipeline.assert_called_once()
                call_args = mock_pipeline.call_args
                assert call_args[1]['symbol'] == symbol
                assert call_args[1]['timeframe'] == timeframe
                
                # Verify response was sent
                mock_interaction.response.send_message.assert_called_once()
                
                # Verify disclaimer was added
                mock_disclaimer.assert_called_once()

    @pytest.mark.asyncio
    async def test_analyze_command_with_caching(self, analyze_commands, mock_interaction):
        """Test analyze command with caching"""
        symbol = "AAPL"
        timeframe = "1D"
        cache_key = analyze_commands._get_cache_key(symbol, timeframe)
        
        # Mock the pipeline execution
        mock_analysis = {
            "symbol": symbol,
            "timeframe": timeframe,
            "analysis": "Cached analysis",
            "confidence": 0.90
        }
        
        with patch('src.bot.extensions.analyze.execute_parallel_analyze_pipeline', new_callable=AsyncMock) as mock_pipeline:
            with patch('src.bot.extensions.analyze.add_disclaimer') as mock_disclaimer:
                # Setup mocks
                mock_pipeline.return_value = mock_analysis
                mock_disclaimer.return_value = "Analysis with disclaimer"
                
                # First call - should cache the result
                await analyze_commands.analyze_command(mock_interaction, symbol, timeframe)
                
                # Verify result was cached
                assert cache_key in analyze_commands.cache
                assert cache_key in analyze_commands.cache_timestamps
                
                # Second call - should use cache
                mock_pipeline.reset_mock()
                await analyze_commands.analyze_command(mock_interaction, symbol, timeframe)
                
                # Should not call pipeline again (using cache)
                mock_pipeline.assert_not_called()

    @pytest.mark.asyncio
    async def test_analyze_command_pipeline_error(self, analyze_commands, mock_interaction):
        """Test analyze command when pipeline execution fails"""
        symbol = "INVALID_SYMBOL"
        timeframe = "1D"
        
        with patch('src.bot.extensions.analyze.execute_parallel_analyze_pipeline', new_callable=AsyncMock) as mock_pipeline:
            # Setup mocks
            mock_pipeline.side_effect = Exception("Pipeline error")
            
            # Execute command
            await analyze_commands.analyze_command(mock_interaction, symbol, timeframe)
            
            # Should handle error gracefully
            mock_interaction.response.send_message.assert_called_once()

    @pytest.mark.asyncio
    async def test_analyze_command_permission_denied(self, analyze_commands, mock_interaction):
        """Test analyze command with permission denied"""
        symbol = "AAPL"
        timeframe = "1D"
        
        # Mock permission checker to deny access
        analyze_commands.permission_checker.has_permission.return_value = False
        
        # Execute command
        await analyze_commands.analyze_command(mock_interaction, symbol, timeframe)
        
        # Should send permission denied message
        mock_interaction.response.send_message.assert_called_once()
        call_args = mock_interaction.response.send_message.call_args
        assert "permission" in call_args[0][0].lower() or "access" in call_args[0][0].lower()

    @pytest.mark.asyncio
    async def test_analyze_command_semaphore_limit(self, analyze_commands, mock_interaction):
        """Test analyze command with semaphore limit"""
        symbol = "AAPL"
        timeframe = "1D"
        
        # Mock the pipeline to take a long time
        async def slow_pipeline(*args, **kwargs):
            await asyncio.sleep(0.1)  # Simulate slow processing
            return {"analysis": "Slow analysis"}
        
        with patch('src.bot.extensions.analyze.execute_parallel_analyze_pipeline', side_effect=slow_pipeline):
            with patch('src.bot.extensions.analyze.add_disclaimer') as mock_disclaimer:
                mock_disclaimer.return_value = "Analysis with disclaimer"
                
                # Start multiple concurrent analyses
                tasks = []
                for i in range(5):  # More than max_concurrent_analyses
                    task = asyncio.create_task(
                        analyze_commands.analyze_command(mock_interaction, f"SYMBOL{i}", timeframe)
                    )
                    tasks.append(task)
                
                # Wait for all tasks to complete
                await asyncio.gather(*tasks, return_exceptions=True)
                
                # All should complete (semaphore should limit concurrency)
                assert len(tasks) == 5

    @pytest.mark.asyncio
    async def test_analyze_command_different_timeframes(self, analyze_commands, mock_interaction):
        """Test analyze command with different timeframes"""
        symbol = "AAPL"
        timeframes = ["1D", "1W", "1M", "3M"]
        
        mock_analysis = {
            "symbol": symbol,
            "analysis": "Analysis result",
            "confidence": 0.85
        }
        
        with patch('src.bot.extensions.analyze.execute_parallel_analyze_pipeline', new_callable=AsyncMock) as mock_pipeline:
            with patch('src.bot.extensions.analyze.add_disclaimer') as mock_disclaimer:
                # Setup mocks
                mock_pipeline.return_value = mock_analysis
                mock_disclaimer.return_value = "Analysis with disclaimer"
                
                # Test each timeframe
                for timeframe in timeframes:
                    await analyze_commands.analyze_command(mock_interaction, symbol, timeframe)
                    
                    # Verify pipeline was called with correct timeframe
                    call_args = mock_pipeline.call_args
                    assert call_args[1]['timeframe'] == timeframe
                    
                    # Reset mock for next iteration
                    mock_pipeline.reset_mock()

    @pytest.mark.asyncio
    async def test_analyze_command_special_characters(self, analyze_commands, mock_interaction):
        """Test analyze command with special characters in symbol"""
        special_symbols = ["AAPL", "BRK.A", "BRK-B", "C$", "BTC-USD"]
        
        mock_analysis = {
            "symbol": "TEST",
            "analysis": "Analysis result",
            "confidence": 0.85
        }
        
        with patch('src.bot.extensions.analyze.execute_parallel_analyze_pipeline', new_callable=AsyncMock) as mock_pipeline:
            with patch('src.bot.extensions.analyze.add_disclaimer') as mock_disclaimer:
                # Setup mocks
                mock_pipeline.return_value = mock_analysis
                mock_disclaimer.return_value = "Analysis with disclaimer"
                
                # Test each special symbol
                for symbol in special_symbols:
                    await analyze_commands.analyze_command(mock_interaction, symbol, "1D")
                    
                    # Verify pipeline was called with correct symbol
                    call_args = mock_pipeline.call_args
                    assert call_args[1]['symbol'] == symbol
                    
                    # Reset mock for next iteration
                    mock_pipeline.reset_mock()

    def test_analyze_command_attributes(self, analyze_commands):
        """Test that AsyncAnalyzeCommands has required attributes and methods"""
        assert hasattr(analyze_commands, 'bot')
        assert hasattr(analyze_commands, 'analyze_command')
        assert callable(analyze_commands.analyze_command)
        assert hasattr(analyze_commands, 'semaphore')
        assert hasattr(analyze_commands, 'cache')
        assert hasattr(analyze_commands, 'cache_timestamps')

    @pytest.mark.asyncio
    async def test_analyze_command_cache_invalidation(self, analyze_commands, mock_interaction):
        """Test cache invalidation after timeout"""
        symbol = "AAPL"
        timeframe = "1D"
        cache_key = analyze_commands._get_cache_key(symbol, timeframe)
        
        # Add stale data to cache
        stale_time = datetime.now() - timedelta(hours=2)
        analyze_commands.cache[cache_key] = {"analysis": "Stale analysis"}
        analyze_commands.cache_timestamps[cache_key] = stale_time
        
        mock_analysis = {
            "symbol": symbol,
            "timeframe": timeframe,
            "analysis": "Fresh analysis",
            "confidence": 0.85
        }
        
        with patch('src.bot.extensions.analyze.execute_parallel_analyze_pipeline', new_callable=AsyncMock) as mock_pipeline:
            with patch('src.bot.extensions.analyze.add_disclaimer') as mock_disclaimer:
                # Setup mocks
                mock_pipeline.return_value = mock_analysis
                mock_disclaimer.return_value = "Analysis with disclaimer"
                
                # Execute command - should bypass stale cache
                await analyze_commands.analyze_command(mock_interaction, symbol, timeframe)
                
                # Verify pipeline was called (cache was invalid)
                mock_pipeline.assert_called_once()
                
                # Verify fresh data was cached
                assert analyze_commands.cache[cache_key] == mock_analysis


class TestAnalyzeCommandsIntegration:
    """Integration tests for AsyncAnalyzeCommands"""

    @pytest.fixture
    def mock_bot(self):
        """Create a mock bot for integration testing"""
        bot = Mock()
        bot.user = Mock()
        bot.user.display_name = "TestBot"
        bot.permission_checker = Mock()
        bot.permission_checker.has_permission.return_value = True
        return bot

    @pytest.fixture
    def analyze_commands(self, mock_bot):
        """Create an AsyncAnalyzeCommands instance for integration testing"""
        return AsyncAnalyzeCommands(mock_bot)

    @pytest.mark.asyncio
    async def test_analyze_command_full_workflow(self, analyze_commands):
        """Test the complete analyze command workflow"""
        # Create a more realistic mock interaction
        interaction = Mock()
        interaction.user = Mock()
        interaction.user.id = 12345
        interaction.user.display_name = "TestUser"
        interaction.response = Mock()
        interaction.followup = Mock()
        interaction.guild_id = 67890
        interaction.channel = Mock()
        interaction.channel.id = 11111
        
        symbol = "AAPL"
        timeframe = "1D"
        
        # Mock the entire pipeline
        with patch('src.bot.extensions.analyze.execute_parallel_analyze_pipeline', new_callable=AsyncMock) as mock_pipeline:
            with patch('src.bot.extensions.analyze.add_disclaimer') as mock_disclaimer:
                # Setup comprehensive mocks
                mock_analysis = {
                    "symbol": symbol,
                    "timeframe": timeframe,
                    "analysis": "Comprehensive analysis of AAPL...",
                    "confidence": 0.92,
                    "recommendations": ["BUY", "STRONG_BUY"],
                    "technical_indicators": {
                        "rsi": 65.5,
                        "macd": 1.2,
                        "sma_50": 150.0
                    },
                    "timestamp": datetime.now().isoformat()
                }
                
                mock_pipeline.return_value = mock_analysis
                mock_disclaimer.return_value = "**Analysis:** Comprehensive analysis of AAPL...\n\n*Disclaimer: This is not financial advice.*"
                
                # Execute the complete workflow
                await analyze_commands.analyze_command(interaction, symbol, timeframe)
                
                # Verify all components were called correctly
                mock_pipeline.assert_called_once()
                mock_disclaimer.assert_called_once_with(mock_analysis)
                
                # Verify interaction methods were called
                interaction.response.send_message.assert_called_once()
                
                # Verify caching
                cache_key = analyze_commands._get_cache_key(symbol, timeframe)
                assert cache_key in analyze_commands.cache
                assert cache_key in analyze_commands.cache_timestamps
