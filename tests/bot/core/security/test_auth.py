"""
Tests for AuthManager

Simplified tests for auth functionality
"""

import pytest
from unittest.mock import MagicMock

from src.bot.core.security.auth import AuthManager, AuthResult

@pytest.fixture
def auth_manager():
    return AuthManager()

def test_validate_permissions(auth_manager):
    """Test basic permissions"""
    result = auth_manager.validate_permissions("user123", "ask")
    assert isinstance(result, AuthResult)
    assert result.is_authorized is True
    assert result.user_tier == "basic"

def test_generate_token(auth_manager):
    """Test token generation"""
    token = auth_manager.generate_session_token("user123")
    assert isinstance(token, str)
    assert len(token) > 0

def test_validate_session(auth_manager):
    """Test session validation"""
    token = auth_manager.generate_session_token("user123")
    assert auth_manager.validate_session("user123", token) is True
    assert auth_manager.validate_session("user123", "invalid") is False
