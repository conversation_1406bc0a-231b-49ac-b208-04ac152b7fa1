"""
Simple tests for ML models module
Tests basic functionality with proper mocking
"""

import pytest
import numpy as np
import pandas as pd
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from analysis.ai.ml_models import (
    TradingMLModel, MLPrediction, ModelPerformance,
    trading_ml_model
)


class TestMLPrediction:
    """Test MLPrediction dataclass"""
    
    def test_ml_prediction_creation(self):
        """Test MLPrediction object creation"""
        prediction = MLPrediction(
            predicted_price=150.0,
            confidence=85.5,
            direction="UP",
            probability=0.75,
            model_used="RandomForest",
            features_used=["price", "volume", "rsi"],
            prediction_horizon="1D"
        )
        
        assert prediction.predicted_price == 150.0
        assert prediction.confidence == 85.5
        assert prediction.direction == "UP"
        assert prediction.probability == 0.75
        assert prediction.model_used == "RandomForest"
        assert prediction.features_used == ["price", "volume", "rsi"]
        assert prediction.prediction_horizon == "1D"


class TestModelPerformance:
    """Test ModelPerformance dataclass"""
    
    def test_model_performance_creation(self):
        """Test ModelPerformance object creation"""
        performance = ModelPerformance(
            mse=0.05,
            mae=0.03,
            r2_score=0.85,
            accuracy=0.92,
            precision=0.88,
            recall=0.90,
            last_updated=datetime.now()
        )
        
        assert performance.mse == 0.05
        assert performance.mae == 0.03
        assert performance.r2_score == 0.85
        assert performance.accuracy == 0.92
        assert performance.precision == 0.88
        assert performance.recall == 0.90
        assert isinstance(performance.last_updated, datetime)


class TestTradingMLModel:
    """Test TradingMLModel class"""
    
    @pytest.fixture
    def sample_historical_data(self):
        """Create sample historical data for testing"""
        return {
            'close': 150.0,
            'volume': 1000000,
            'high': 155.0,
            'low': 145.0,
            'open': 148.0
        }
    
    @pytest.fixture
    def sample_technical_indicators(self):
        """Create sample technical indicators"""
        return {
            'rsi': 65.0,
            'macd': 0.5,
            'bb_upper': 160.0,
            'bb_lower': 140.0,
            'sma_20': 148.0,
            'ema_12': 149.0
        }
    
    @pytest.fixture
    def ml_model(self):
        """Create TradingMLModel instance"""
        return TradingMLModel()
    
    def test_ml_model_initialization(self, ml_model):
        """Test ML model initialization"""
        assert len(ml_model.models) == 3  # random_forest, gradient_boosting, linear_ridge
        assert len(ml_model.scalers) == 3
        assert ml_model.performance_metrics == {}
        assert ml_model.feature_importance == {}
        assert ml_model.is_trained is False
        assert ml_model.model_type == "ensemble"
    
    def test_prepare_features(self, ml_model, sample_historical_data, sample_technical_indicators):
        """Test feature preparation"""
        features = ml_model.prepare_features(sample_historical_data, sample_technical_indicators)
        
        # Check that features are created
        assert isinstance(features, pd.DataFrame)
        assert len(features) == 1  # Single row for single data point
        
        # Check for some expected features
        expected_features = ['close', 'volume', 'rsi', 'macd']
        for feature in expected_features:
            if feature in features.columns:
                assert not features[feature].isna().all(), f"{feature} should not be all NaN"
    
    def test_prepare_features_with_empty_data(self, ml_model):
        """Test feature preparation with empty data"""
        features = ml_model.prepare_features({}, {})
        
        # Should return empty DataFrame for empty input
        assert isinstance(features, pd.DataFrame)
        assert len(features) == 0
    
    def test_prepare_features_with_missing_columns(self, ml_model):
        """Test feature preparation with missing required columns"""
        incomplete_data = {'close': 150.0}  # Missing other required columns
        features = ml_model.prepare_features(incomplete_data, {})
        
        # Should return empty DataFrame for incomplete data
        assert isinstance(features, pd.DataFrame)
        assert len(features) == 0
    
    def test_train_models(self, ml_model, sample_historical_data, sample_technical_indicators):
        """Test model training"""
        # Create training data in the expected format
        training_data = [{
            'historical': sample_historical_data,
            'technical': sample_technical_indicators,
            'target': 150.0
        }] * 60  # Need at least 50 data points
        
        # Train models
        result = ml_model.train_models(training_data, "1D")
        
        # Should return performance metrics for successful training
        assert isinstance(result, dict)
        if len(result) > 0:  # If training succeeded
            assert ml_model.is_trained is True
    
    def test_train_models_with_empty_data(self, ml_model):
        """Test model training with empty data"""
        empty_training_data = []
        
        result = ml_model.train_models(empty_training_data, "1D")
        
        # Should return empty dict for empty data
        assert result == {}
    
    def test_predict_price(self, ml_model, sample_historical_data, sample_technical_indicators):
        """Test price prediction"""
        # First train the model
        training_data = [{
            'historical': sample_historical_data,
            'technical': sample_technical_indicators,
            'target': 150.0
        }] * 60  # Need at least 50 data points
        
        ml_model.train_models(training_data, "1D")
        
        # Make prediction
        prediction = ml_model.predict(sample_historical_data, sample_technical_indicators)
        
        if prediction:
            assert isinstance(prediction, MLPrediction)
            assert prediction.predicted_price > 0
            assert prediction.model_used == 'ensemble'  # ML models use ensemble prediction
            assert prediction.direction in ['UP', 'DOWN', 'SIDEWAYS']
            assert 0 <= prediction.confidence <= 100
            assert 0 <= prediction.probability <= 1
    
    def test_predict_price_without_training(self, ml_model, sample_historical_data, sample_technical_indicators):
        """Test prediction without training"""
        prediction = ml_model.predict(sample_historical_data, sample_technical_indicators)
        
        # Should return fallback prediction or handle gracefully
        assert prediction is None or isinstance(prediction, MLPrediction)
    
    def test_get_model_summary(self, ml_model):
        """Test getting model summary"""
        # Add mock performance data
        ml_model.performance_metrics['test_model'] = ModelPerformance(
            mse=0.05,
            mae=0.03,
            r2_score=0.85,
            accuracy=0.92,
            precision=0.88,
            recall=0.90,
            last_updated=datetime.now()
        )
        
        summary = ml_model.get_model_summary()
        assert isinstance(summary, dict)
        assert 'models_available' in summary
        assert 'performance_metrics' in summary
    
    def test_model_summary_with_training(self, ml_model, sample_historical_data, sample_technical_indicators):
        """Test model summary after training"""
        # Train the model first
        training_data = [{
            'historical': sample_historical_data,
            'technical': sample_technical_indicators,
            'target': 150.0
        }] * 60  # Need at least 50 data points
        
        ml_model.train_models(training_data, "1D")
        
        # Get summary
        summary = ml_model.get_model_summary()
        assert isinstance(summary, dict)
        assert 'models_available' in summary
        assert 'performance_metrics' in summary
    
    def test_feature_importance_after_training(self, ml_model, sample_historical_data, sample_technical_indicators):
        """Test feature importance after training"""
        # Train the model first
        training_data = [{
            'historical': sample_historical_data,
            'technical': sample_technical_indicators,
            'target': 150.0
        }] * 60  # Need at least 50 data points
        
        ml_model.train_models(training_data, "1D")
        
        # Check if feature importance was stored
        assert isinstance(ml_model.feature_importance, dict)
        # Feature importance should be populated for models that support it
    
    def test_train_on_sample_data(self, ml_model):
        """Test training on sample data"""
        # This is an async method, so we'll test it exists
        assert hasattr(ml_model, 'train_on_sample_data')
        assert callable(ml_model.train_on_sample_data)


class TestGlobalTradingMLModel:
    """Test the global trading_ml_model instance"""
    
    def test_global_instance_exists(self):
        """Test that global instance is created"""
        assert trading_ml_model is not None
        assert isinstance(trading_ml_model, TradingMLModel)
    
    def test_global_instance_initialization(self):
        """Test global instance initialization"""
        assert len(trading_ml_model.models) == 3
        assert len(trading_ml_model.scalers) == 3
        assert trading_ml_model.performance_metrics == {}
        assert trading_ml_model.is_trained is False


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
