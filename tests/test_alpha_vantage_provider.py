"""
Test suite for Alpha Vantage Data Provider

Tests the Alpha Vantage provider functionality including:
- Provider initialization
- API key configuration
- Rate limiting
- Data fetching methods
- Error handling
- Session management
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from datetime import datetime, timezone
import aiohttp

from src.shared.data_providers.alpha_vantage import AlphaVantageProvider
from src.core.exceptions import MarketDataError


class TestAlphaVantageProvider:
    """Test the AlphaVantageProvider class"""

    @pytest.fixture
    def mock_api_key(self):
        """Mock API key for testing"""
        return "test_api_key_12345"

    @pytest.fixture
    def provider(self, mock_api_key):
        """Create an AlphaVantageProvider instance for testing"""
        return AlphaVantageProvider(api_key=mock_api_key)

    @pytest.fixture
    def provider_no_key(self):
        """Create an AlphaVantageProvider instance without API key"""
        return AlphaVantageProvider(api_key=None)

    def test_provider_initialization(self, provider, mock_api_key):
        """Test AlphaVantageProvider initialization"""
        assert provider.api_key == mock_api_key
        assert provider.base_url == "https://www.alphavantage.co/query"
        assert provider.is_configured is True
        assert provider.calls_per_minute == 5
        assert provider.session is None  # Not initialized yet

    def test_provider_initialization_no_key(self, provider_no_key):
        """Test AlphaVantageProvider initialization without API key"""
        assert provider_no_key.api_key == ""
        assert provider_no_key.is_configured is False

    def test_provider_initialization_with_config(self):
        """Test AlphaVantageProvider initialization with config"""
        config = {
            'api_key': 'config_api_key',
            'calls_per_minute': 10
        }
        provider = AlphaVantageProvider(config=config)
        assert provider.api_key == 'config_api_key'
        assert provider.calls_per_minute == 5  # Should use default

    @pytest.mark.asyncio
    async def test_ensure_session(self, provider):
        """Test session initialization"""
        assert provider.session is None
        
        await provider._ensure_session()
        
        assert provider.session is not None
        assert isinstance(provider.session, aiohttp.ClientSession)

    @pytest.mark.asyncio
    async def test_ensure_session_multiple_calls(self, provider):
        """Test that multiple calls to _ensure_session don't create multiple sessions"""
        await provider._ensure_session()
        session1 = provider.session
        
        await provider._ensure_session()
        session2 = provider.session
        
        assert session1 is session2

    @pytest.mark.asyncio
    async def test_rate_limiting(self, provider):
        """Test rate limiting functionality"""
        # Test that rate limiter is properly initialized
        assert provider.rate_limiter is not None
        assert provider.rate_limiter._value == 1  # Semaphore value

    @pytest.mark.asyncio
    async def test_get_ticker_success(self, provider):
        """Test successful ticker data retrieval"""
        mock_response_data = {
            "Global Quote": {
                "01. symbol": "AAPL",
                "02. open": "150.00",
                "03. high": "155.00",
                "04. low": "149.00",
                "05. price": "152.50",
                "06. volume": "1000000",
                "07. latest trading day": "2023-01-01",
                "08. previous close": "151.00",
                "09. change": "1.50",
                "10. change percent": "0.99%"
            }
        }
        
        with patch.object(provider, '_make_request', new_callable=AsyncMock) as mock_request:
            mock_request.return_value = mock_response_data
            
            result = await provider.get_ticker("AAPL")
            
            assert result is not None
            assert result.get("symbol") == "AAPL"
            assert result.get("price") == 152.50
            assert result.get("volume") == 1000000
            mock_request.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_ticker_not_configured(self, provider_no_key):
        """Test ticker retrieval when provider is not configured"""
        result = await provider_no_key.get_ticker("AAPL")
        
        assert result is None

    @pytest.mark.asyncio
    async def test_get_ticker_api_error(self, provider):
        """Test ticker retrieval with API error"""
        with patch.object(provider, '_make_request', new_callable=AsyncMock) as mock_request:
            mock_request.side_effect = MarketDataError("API Error")
            
            result = await provider.get_ticker("AAPL")
            
            assert result is None

    @pytest.mark.asyncio
    async def test_get_ticker_invalid_response(self, provider):
        """Test ticker retrieval with invalid response"""
        mock_response_data = {
            "Error Message": "Invalid API call"
        }
        
        with patch.object(provider, '_make_request', new_callable=AsyncMock) as mock_request:
            mock_request.return_value = mock_response_data
            
            result = await provider.get_ticker("INVALID")
            
            assert result is None

    @pytest.mark.asyncio
    async def test_get_historical_data_success(self, provider):
        """Test successful historical data retrieval"""
        mock_response_data = {
            "Time Series (Daily)": {
                "2023-01-01": {
                    "1. open": "150.00",
                    "2. high": "155.00",
                    "3. low": "149.00",
                    "4. close": "152.50",
                    "5. volume": "1000000"
                },
                "2023-01-02": {
                    "1. open": "152.50",
                    "2. high": "158.00",
                    "3. low": "151.00",
                    "4. close": "156.00",
                    "5. volume": "1200000"
                }
            }
        }
        
        with patch.object(provider, '_make_request', new_callable=AsyncMock) as mock_request:
            mock_request.return_value = mock_response_data
            
            result = await provider.get_historical_data("AAPL", "1d")
            
            assert result is not None
            assert len(result) == 2
            assert "2023-01-01" in result
            assert "2023-01-02" in result
            assert result["2023-01-01"]["close"] == 152.50

    @pytest.mark.asyncio
    async def test_get_historical_data_not_configured(self, provider_no_key):
        """Test historical data retrieval when provider is not configured"""
        result = await provider_no_key.get_historical_data("AAPL", "1d")
        
        assert result is None

    @pytest.mark.asyncio
    async def test_make_request_success(self, provider):
        """Test successful API request"""
        mock_response_data = {"test": "data"}
        
        with patch('aiohttp.ClientSession.get', new_callable=AsyncMock) as mock_get:
            mock_response = AsyncMock()
            mock_response.json.return_value = mock_response_data
            mock_response.status = 200
            mock_get.return_value.__aenter__.return_value = mock_response
            
            await provider._ensure_session()
            result = await provider._make_request("test_function", {"symbol": "AAPL"})
            
            assert result == mock_response_data

    @pytest.mark.asyncio
    async def test_make_request_http_error(self, provider):
        """Test API request with HTTP error"""
        with patch('aiohttp.ClientSession.get', new_callable=AsyncMock) as mock_get:
            mock_response = AsyncMock()
            mock_response.status = 400
            mock_response.text.return_value = "Bad Request"
            mock_get.return_value.__aenter__.return_value = mock_response
            
            await provider._ensure_session()
            
            with pytest.raises(MarketDataError):
                await provider._make_request("test_function", {"symbol": "AAPL"})

    @pytest.mark.asyncio
    async def test_make_request_network_error(self, provider):
        """Test API request with network error"""
        with patch('aiohttp.ClientSession.get', new_callable=AsyncMock) as mock_get:
            mock_get.side_effect = aiohttp.ClientError("Network error")
            
            await provider._ensure_session()
            
            with pytest.raises(MarketDataError):
                await provider._make_request("test_function", {"symbol": "AAPL"})

    @pytest.mark.asyncio
    async def test_make_request_timeout(self, provider):
        """Test API request with timeout"""
        with patch('aiohttp.ClientSession.get', new_callable=AsyncMock) as mock_get:
            mock_get.side_effect = asyncio.TimeoutError("Request timeout")
            
            await provider._ensure_session()
            
            with pytest.raises(MarketDataError):
                await provider._make_request("test_function", {"symbol": "AAPL"})

    @pytest.mark.asyncio
    async def test_cleanup_session(self, provider):
        """Test session cleanup"""
        await provider._ensure_session()
        assert provider.session is not None
        
        await provider.cleanup()
        assert provider.session is None

    @pytest.mark.asyncio
    async def test_cleanup_no_session(self, provider):
        """Test cleanup when no session exists"""
        # Should not raise an error
        await provider.cleanup()
        assert provider.session is None

    def test_provider_name_and_type(self, provider):
        """Test provider name and type"""
        assert provider.provider_name == 'alpha_vantage'
        assert provider.provider_type == 'market_data'

    @pytest.mark.asyncio
    async def test_get_multiple_tickers(self, provider):
        """Test getting data for multiple tickers"""
        symbols = ["AAPL", "GOOGL", "MSFT"]
        results = []
        
        with patch.object(provider, 'get_ticker', new_callable=AsyncMock) as mock_get_ticker:
            mock_get_ticker.return_value = {"symbol": "TEST", "price": 100.0}
            
            for symbol in symbols:
                result = await provider.get_ticker(symbol)
                results.append(result)
            
            assert len(results) == 3
            assert all(result is not None for result in results)

    @pytest.mark.asyncio
    async def test_rate_limiting_enforcement(self, provider):
        """Test that rate limiting is enforced"""
        # This test would need to be more sophisticated in a real scenario
        # For now, we just test that the semaphore exists
        assert provider.rate_limiter is not None
        
        # Test that we can acquire the semaphore
        async with provider.rate_limiter:
            assert True  # If we get here, semaphore was acquired

    def test_config_handling(self):
        """Test configuration handling"""
        config = {
            'api_key': 'test_key',
            'base_url': 'https://custom.url',
            'calls_per_minute': 3
        }
        
        provider = AlphaVantageProvider(config=config)
        
        assert provider.api_key == 'test_key'
        # base_url should not be overridden by config
        assert provider.base_url == "https://www.alphavantage.co/query"
        # calls_per_minute should use default
        assert provider.calls_per_minute == 5


class TestAlphaVantageProviderIntegration:
    """Integration tests for AlphaVantageProvider"""

    @pytest.fixture
    def provider(self):
        """Create a provider for integration testing"""
        return AlphaVantageProvider(api_key="test_key")

    @pytest.mark.asyncio
    async def test_full_workflow(self, provider):
        """Test complete workflow from initialization to data retrieval"""
        # Mock the entire request flow
        mock_response_data = {
            "Global Quote": {
                "01. symbol": "AAPL",
                "05. price": "152.50",
                "06. volume": "1000000"
            }
        }
        
        with patch('aiohttp.ClientSession.get', new_callable=AsyncMock) as mock_get:
            mock_response = AsyncMock()
            mock_response.json.return_value = mock_response_data
            mock_response.status = 200
            mock_get.return_value.__aenter__.return_value = mock_response
            
            # Test the complete workflow
            result = await provider.get_ticker("AAPL")
            
            assert result is not None
            assert result.get("symbol") == "AAPL"
            assert result.get("price") == 152.50
            
            # Verify session was created
            assert provider.session is not None

    @pytest.mark.asyncio
    async def test_error_recovery(self, provider):
        """Test error recovery and retry logic"""
        call_count = 0
        
        async def mock_request_with_retry(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            if call_count == 1:
                raise MarketDataError("Temporary error")
            else:
                return {"Global Quote": {"01. symbol": "AAPL", "05. price": "152.50"}}
        
        with patch.object(provider, '_make_request', side_effect=mock_request_with_retry):
            # First call should fail
            result1 = await provider.get_ticker("AAPL")
            assert result1 is None
            
            # Second call should succeed
            result2 = await provider.get_ticker("AAPL")
            assert result2 is not None
            assert result2.get("symbol") == "AAPL"

    @pytest.mark.asyncio
    async def test_concurrent_requests(self, provider):
        """Test handling of concurrent requests"""
        mock_response_data = {
            "Global Quote": {
                "01. symbol": "AAPL",
                "05. price": "152.50"
            }
        }
        
        with patch.object(provider, '_make_request', new_callable=AsyncMock) as mock_request:
            mock_request.return_value = mock_response_data
            
            # Make multiple concurrent requests
            tasks = [provider.get_ticker("AAPL") for _ in range(3)]
            results = await asyncio.gather(*tasks)
            
            # All should succeed
            assert len(results) == 3
            assert all(result is not None for result in results)
            assert all(result.get("symbol") == "AAPL" for result in results)
