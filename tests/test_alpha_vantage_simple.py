"""
Simple test suite for Alpha Vantage Data Provider

Tests the core functionality of the Alpha Vantage provider including:
- Provider initialization
- Session management
- Data fetching methods
- Error handling
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timezone

from src.shared.data_providers.alpha_vantage import AlphaVantageProvider
from src.core.exceptions import MarketDataError


class TestAlphaVantageProviderSimple:
    """Test the AlphaVantageProvider class with simplified tests"""

    @pytest.fixture
    def provider(self):
        """Create an AlphaVantageProvider instance for testing"""
        return AlphaVantageProvider(api_key="test_api_key")

    @pytest.fixture
    def provider_no_key(self):
        """Create an AlphaVantageProvider instance without API key"""
        with patch.dict('os.environ', {}, clear=True):
            return AlphaVantageProvider(api_key=None)

    def test_provider_initialization(self, provider):
        """Test AlphaVantageProvider initialization"""
        assert provider.api_key == "test_api_key"
        assert provider.base_url == "https://www.alphavantage.co/query"
        assert provider.is_configured is True
        assert provider.calls_per_minute == 5
        assert provider.session is None  # Not initialized yet

    def test_provider_initialization_no_key(self, provider_no_key):
        """Test AlphaVantageProvider initialization without API key"""
        assert provider_no_key.api_key == ""
        assert provider_no_key.is_configured is False

    def test_provider_initialization_with_config(self):
        """Test AlphaVantageProvider initialization with config"""
        config = {
            'api_key': 'config_api_key',
            'calls_per_minute': 10
        }
        provider = AlphaVantageProvider(config=config)
        assert provider.api_key == 'config_api_key'
        # base_url should not be overridden by config
        assert provider.base_url == "https://www.alphavantage.co/query"
        # calls_per_minute should use default
        assert provider.calls_per_minute == 5

    @pytest.mark.asyncio
    async def test_ensure_session(self, provider):
        """Test session initialization"""
        assert provider.session is None
        
        await provider._ensure_session()
        
        assert provider.session is not None
        assert hasattr(provider.session, 'get')

    @pytest.mark.asyncio
    async def test_ensure_session_multiple_calls(self, provider):
        """Test that multiple calls to _ensure_session don't create multiple sessions"""
        await provider._ensure_session()
        session1 = provider.session
        
        await provider._ensure_session()
        session2 = provider.session
        
        assert session1 is session2

    @pytest.mark.asyncio
    async def test_rate_limiting(self, provider):
        """Test rate limiting functionality"""
        # Test that rate limiter is properly initialized
        assert provider.rate_limiter is not None
        assert provider.rate_limiter._value == 1  # Semaphore value

    def test_is_valid_symbol(self, provider):
        """Test symbol validation"""
        assert provider._is_valid_symbol("AAPL") is True
        assert provider._is_valid_symbol("GOOGL") is True
        assert provider._is_valid_symbol("") is False
        assert provider._is_valid_symbol("123") is False
        assert provider._is_valid_symbol("A") is True

    @pytest.mark.asyncio
    async def test_get_current_price_success(self, provider):
        """Test successful current price retrieval"""
        mock_response_data = {
            "Global Quote": {
                "01. symbol": "AAPL",
                "02. open": "150.00",
                "03. high": "155.00",
                "04. low": "149.00",
                "05. price": "152.50",
                "06. volume": "1000000",
                "07. latest trading day": "2023-01-01",
                "08. previous close": "151.00",
                "09. change": "1.50",
                "10. change percent": "0.99%"
            }
        }
        
        with patch('aiohttp.ClientSession.get', new_callable=AsyncMock) as mock_get:
            mock_response = AsyncMock()
            mock_response.json.return_value = mock_response_data
            mock_response.status = 200
            mock_response.__aenter__.return_value = mock_response
            mock_get.return_value = mock_response
            
            await provider._ensure_session()
            result = await provider.get_current_price("AAPL")
            
            assert result is not None
            assert result.get("symbol") == "AAPL"
            assert result.get("price") == 152.50
            assert result.get("volume") == 1000000
            assert result.get("success") is True

    @pytest.mark.asyncio
    async def test_get_current_price_not_configured(self, provider_no_key):
        """Test current price retrieval when provider is not configured"""
        result = await provider_no_key.get_current_price("AAPL")
        
        assert result is None

    @pytest.mark.asyncio
    async def test_get_current_price_api_error(self, provider):
        """Test current price retrieval with API error"""
        mock_response_data = {
            "Error Message": "Invalid API call"
        }
        
        with patch('aiohttp.ClientSession.get', new_callable=AsyncMock) as mock_get:
            mock_response = AsyncMock()
            mock_response.json.return_value = mock_response_data
            mock_response.status = 200
            mock_response.__aenter__.return_value = mock_response
            mock_get.return_value = mock_response
            
            await provider._ensure_session()
            result = await provider.get_current_price("INVALID")
            
            assert result is None

    @pytest.mark.asyncio
    async def test_get_ticker_success(self, provider):
        """Test successful ticker data retrieval"""
        mock_response_data = {
            "Global Quote": {
                "01. symbol": "AAPL",
                "05. price": "152.50",
                "06. volume": "1000000"
            }
        }
        
        with patch('aiohttp.ClientSession.get', new_callable=AsyncMock) as mock_get:
            mock_response = AsyncMock()
            mock_response.json.return_value = mock_response_data
            mock_response.status = 200
            mock_response.__aenter__.return_value = mock_response
            mock_get.return_value = mock_response
            
            await provider._ensure_session()
            result = await provider.get_ticker("AAPL")
            
            assert result is not None
            assert result.get("symbol") == "AAPL"
            assert result.get("price") == 152.50

    @pytest.mark.asyncio
    async def test_get_ticker_not_configured(self, provider_no_key):
        """Test ticker retrieval when provider is not configured"""
        result = await provider_no_key.get_ticker("AAPL")
        
        assert result is None

    @pytest.mark.asyncio
    async def test_get_historical_data_success(self, provider):
        """Test successful historical data retrieval"""
        mock_response_data = {
            "Time Series (Daily)": {
                "2023-01-01": {
                    "1. open": "150.00",
                    "2. high": "155.00",
                    "3. low": "149.00",
                    "4. close": "152.50",
                    "5. volume": "1000000"
                },
                "2023-01-02": {
                    "1. open": "152.50",
                    "2. high": "158.00",
                    "3. low": "151.00",
                    "4. close": "156.00",
                    "5. volume": "1200000"
                }
            }
        }
        
        with patch('aiohttp.ClientSession.get', new_callable=AsyncMock) as mock_get:
            mock_response = AsyncMock()
            mock_response.json.return_value = mock_response_data
            mock_response.status = 200
            mock_response.__aenter__.return_value = mock_response
            mock_get.return_value = mock_response
            
            await provider._ensure_session()
            result = await provider.get_historical_data("AAPL", days=2)
            
            assert result is not None
            assert len(result) == 2
            assert "2023-01-01" in result
            assert "2023-01-02" in result
            assert result["2023-01-01"]["close"] == 152.50

    @pytest.mark.asyncio
    async def test_get_historical_data_not_configured(self, provider_no_key):
        """Test historical data retrieval when provider is not configured"""
        result = await provider_no_key.get_historical_data("AAPL", days=1)
        
        assert result is None

    @pytest.mark.asyncio
    async def test_get_history_success(self, provider):
        """Test successful history data retrieval"""
        mock_response_data = {
            "Time Series (Daily)": {
                "2023-01-01": {
                    "1. open": "150.00",
                    "2. high": "155.00",
                    "3. low": "149.00",
                    "4. close": "152.50",
                    "5. volume": "1000000"
                }
            }
        }
        
        with patch('aiohttp.ClientSession.get', new_callable=AsyncMock) as mock_get:
            mock_response = AsyncMock()
            mock_response.json.return_value = mock_response_data
            mock_response.status = 200
            mock_response.__aenter__.return_value = mock_response
            mock_get.return_value = mock_response
            
            await provider._ensure_session()
            result = await provider.get_history("AAPL", "1mo", "1d")
            
            assert result is not None
            assert "2023-01-01" in result

    @pytest.mark.asyncio
    async def test_close_session(self, provider):
        """Test session cleanup"""
        await provider._ensure_session()
        assert provider.session is not None
        
        await provider.close()
        assert provider.session is None

    @pytest.mark.asyncio
    async def test_close_no_session(self, provider):
        """Test cleanup when no session exists"""
        # Should not raise an error
        await provider.close()
        assert provider.session is None

    def test_provider_name_and_type(self, provider):
        """Test provider name and type"""
        assert provider.provider_name == 'alpha_vantage'
        assert provider.provider_type == 'market_data'

    @pytest.mark.asyncio
    async def test_rate_limiting_enforcement(self, provider):
        """Test that rate limiting is enforced"""
        # Test that we can acquire the semaphore
        async with provider.rate_limiter:
            assert True  # If we get here, semaphore was acquired

    @pytest.mark.asyncio
    async def test_concurrent_requests(self, provider):
        """Test handling of concurrent requests"""
        mock_response_data = {
            "Global Quote": {
                "01. symbol": "AAPL",
                "05. price": "152.50"
            }
        }
        
        with patch('aiohttp.ClientSession.get', new_callable=AsyncMock) as mock_get:
            mock_response = AsyncMock()
            mock_response.json.return_value = mock_response_data
            mock_response.status = 200
            mock_response.__aenter__.return_value = mock_response
            mock_get.return_value = mock_response
            
            await provider._ensure_session()
            
            # Make multiple concurrent requests
            tasks = [provider.get_current_price("AAPL") for _ in range(3)]
            results = await asyncio.gather(*tasks)
            
            # All should succeed
            assert len(results) == 3
            assert all(result is not None for result in results)
            assert all(result.get("symbol") == "AAPL" for result in results)

    @pytest.mark.asyncio
    async def test_http_error_handling(self, provider):
        """Test HTTP error handling"""
        with patch('aiohttp.ClientSession.get', new_callable=AsyncMock) as mock_get:
            mock_response = AsyncMock()
            mock_response.status = 400
            mock_response.text.return_value = "Bad Request"
            mock_response.__aenter__.return_value = mock_response
            mock_get.return_value = mock_response
            
            await provider._ensure_session()
            
            result = await provider.get_current_price("AAPL")
            assert result is None

    @pytest.mark.asyncio
    async def test_network_error_handling(self, provider):
        """Test network error handling"""
        with patch('aiohttp.ClientSession.get', new_callable=AsyncMock) as mock_get:
            mock_get.side_effect = Exception("Network error")
            
            await provider._ensure_session()
            
            result = await provider.get_current_price("AAPL")
            assert result is None

    @pytest.mark.asyncio
    async def test_invalid_symbol_handling(self, provider):
        """Test handling of invalid symbols"""
        result = await provider.get_current_price("")
        assert result is None
        
        result = await provider.get_current_price("123")
        assert result is None

    def test_create_empty_historical_data(self, provider):
        """Test creation of empty historical data"""
        result = provider._create_empty_historical_data("AAPL")
        
        assert result is not None
        assert result.get("symbol") == "AAPL"
        assert result.get("error") is not None
        assert "No data available" in result.get("error", "")

    def test_create_historical_data_dict(self, provider):
        """Test creation of historical data dictionary"""
        data = {
            "2023-01-01": {
                "1. open": "150.00",
                "2. high": "155.00",
                "3. low": "149.00",
                "4. close": "152.50",
                "5. volume": "1000000"
            }
        }
        
        result = provider._create_historical_data_dict(data)
        
        assert result is not None
        assert "2023-01-01" in result
        assert result["2023-01-01"]["close"] == 152.50
        assert result["2023-01-01"]["volume"] == 1000000
