"""
Simple test suite for Bot Extensions

Tests the core functionality of bot extensions without Discord command complexity:
- Ask command logic
- Analyze command logic  
- Utility functions
- Error handling
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from datetime import datetime

from src.shared.error_handling.logging import get_logger

logger = get_logger(__name__)


class TestAskCommandLogic:
    """Test the core logic of the ask command without Discord wrapper"""

    @pytest.fixture
    def mock_bot(self):
        """Create a mock bot for testing"""
        bot = Mock()
        bot.user = Mock()
        bot.user.display_name = "TestBot"
        return bot

    @pytest.fixture
    def ask_command(self, mock_bot):
        """Create an AskCommand instance for testing"""
        from src.bot.extensions.ask import AskCommand
        return AskCommand(mock_bot)

    def test_ask_command_initialization(self, ask_command, mock_bot):
        """Test AskCommand initialization"""
        assert ask_command.bot == mock_bot
        assert hasattr(ask_command, 'ask_command')

    @pytest.mark.asyncio
    async def test_ask_command_core_functionality(self, ask_command):
        """Test the core ask command functionality by calling the underlying method"""
        # Create a mock interaction
        interaction = Mock()
        interaction.user = Mock()
        interaction.user.id = 12345
        interaction.user.display_name = "TestUser"
        interaction.response = AsyncMock()  # Make response async
        interaction.followup = AsyncMock()  # Make followup async
        interaction.guild_id = 67890
        interaction.channel = Mock()
        interaction.channel.id = 11111

        query = "What is the current price of AAPL?"

        # Mock the pipeline execution
        mock_response = {
            "response": "AAPL is currently trading at $150.00",
            "confidence": 0.95,
            "sources": ["market_data"],
            "timestamp": datetime.now().isoformat()
        }

        with patch('src.bot.extensions.ask.execute_ask_pipeline', new_callable=AsyncMock) as mock_pipeline:
            with patch('src.bot.extensions.ask.format_response_for_discord') as mock_format:
                with patch('src.bot.extensions.ask.create_audit_logger') as mock_audit:
                    # Setup mocks
                    mock_pipeline.return_value = mock_response
                    mock_format.return_value = "Formatted response"
                    mock_audit_logger = Mock()
                    mock_audit_logger.start_step = Mock()
                    mock_audit_logger.complete_step = Mock()
                    mock_audit_logger.log_error = Mock()
                    mock_audit.return_value = mock_audit_logger

                    # Get the actual method from the command
                    ask_method = ask_command.ask_command.callback

                    # Execute the method directly
                    await ask_method(ask_command, interaction, query)

                    # Verify response was sent (either through response or followup)
                    assert interaction.response.send_message.called or interaction.followup.send.called

    @pytest.mark.asyncio
    async def test_ask_command_error_handling(self, ask_command):
        """Test ask command error handling"""
        interaction = Mock()
        interaction.user = Mock()
        interaction.user.id = 12345
        interaction.user.display_name = "TestUser"
        interaction.response = AsyncMock()  # Make response async
        interaction.followup = AsyncMock()  # Make followup async

        query = "Test query"

        with patch('src.bot.extensions.ask.execute_ask_pipeline', new_callable=AsyncMock) as mock_pipeline:
            with patch('src.bot.extensions.ask.create_audit_logger') as mock_audit:
                # Setup mocks
                mock_pipeline.side_effect = Exception("Pipeline error")
                mock_audit_logger = Mock()
                mock_audit_logger.log_error = Mock()
                mock_audit.return_value = mock_audit_logger

                # Get the actual method from the command
                ask_method = ask_command.ask_command.callback

                # Execute the method directly
                await ask_method(ask_command, interaction, query)

                # Should handle error gracefully (either through response or followup)
                assert interaction.response.send_message.called or interaction.followup.send.called


class TestAnalyzeCommandLogic:
    """Test the core logic of the analyze command without Discord wrapper"""

    @pytest.fixture
    def mock_bot(self):
        """Create a mock bot for testing"""
        bot = Mock()
        bot.user = Mock()
        bot.user.display_name = "TestBot"
        bot.permission_checker = Mock()
        bot.permission_checker.has_permission.return_value = (True, "")
        return bot

    @pytest.fixture
    def analyze_commands(self, mock_bot):
        """Create an AsyncAnalyzeCommands instance for testing"""
        # Mock the missing import
        with patch.dict('sys.modules', {'src.bot.pipeline.commands.analyze.parallel_pipeline': Mock()}):
            from src.bot.extensions.analyze import AsyncAnalyzeCommands
            return AsyncAnalyzeCommands(mock_bot)

    def test_analyze_commands_initialization(self, analyze_commands, mock_bot):
        """Test AsyncAnalyzeCommands initialization"""
        assert analyze_commands.bot == mock_bot
        assert hasattr(analyze_commands, 'analyze_command')
        assert hasattr(analyze_commands, 'cache')
        assert hasattr(analyze_commands, 'cache_timestamps')

    def test_cache_key_generation(self, analyze_commands):
        """Test cache key generation"""
        symbol = "AAPL"
        timeframe = "1D"
        
        cache_key = analyze_commands._get_cache_key(symbol, timeframe)
        
        # Should generate consistent hash
        import hashlib
        expected_key = hashlib.md5(f"{symbol}:{timeframe}".encode()).hexdigest()
        assert cache_key == expected_key

    def test_cache_validation(self, analyze_commands):
        """Test cache validation logic"""
        cache_key = "test_key"
        import time
        now = time.time()

        # Add fresh data to cache
        analyze_commands.cache_timestamps[cache_key] = now

        # Should be valid for fresh data (fix API call)
        assert analyze_commands._is_cache_valid(cache_key, "technical_analysis") is True

    @pytest.mark.asyncio
    async def test_analyze_command_core_functionality(self, analyze_commands):
        """Test the core analyze command functionality"""
        interaction = Mock()
        interaction.user = Mock()
        interaction.user.id = 12345
        interaction.user.display_name = "TestUser"
        interaction.response = AsyncMock()  # Make response async
        interaction.followup = AsyncMock()  # Make followup async

        symbol = "AAPL"

        # Mock the pipeline execution
        mock_analysis = {
            "symbol": symbol,
            "analysis": "Strong bullish trend detected",
            "confidence": 0.85,
            "recommendations": ["BUY", "HOLD"],
            "timestamp": datetime.now().isoformat()
        }

        with patch('src.bot.extensions.analyze.execute_parallel_analyze_pipeline', new_callable=AsyncMock) as mock_pipeline:
            with patch('src.bot.extensions.analyze.add_disclaimer') as mock_disclaimer:
                # Setup mocks
                mock_pipeline.return_value = mock_analysis
                mock_disclaimer.return_value = "Analysis with disclaimer"

                # Get the actual method from the command
                analyze_method = analyze_commands.analyze_command.callback

                # Execute the method directly (only symbol parameter)
                await analyze_method(analyze_commands, interaction, symbol)

                # Verify response was sent
                assert interaction.response.send_message.called or interaction.followup.send.called

    @pytest.mark.asyncio
    async def test_analyze_command_caching(self, analyze_commands):
        """Test analyze command caching functionality"""
        interaction = Mock()
        interaction.user = Mock()
        interaction.user.id = 12345
        interaction.user.display_name = "TestUser"
        interaction.response = AsyncMock()  # Make response async
        interaction.followup = AsyncMock()  # Make followup async

        symbol = "AAPL"
        # Use multi_timeframe cache key as used in the actual command
        cache_key = analyze_commands._get_cache_key(symbol, "multi_timeframe")

        # Mock the pipeline execution
        mock_analysis = {
            "symbol": symbol,
            "analysis": "Cached analysis",
            "confidence": 0.90
        }

        with patch('src.bot.extensions.analyze.execute_parallel_analyze_pipeline', new_callable=AsyncMock) as mock_pipeline:
            with patch('src.bot.extensions.analyze.add_disclaimer') as mock_disclaimer:
                # Setup mocks
                mock_pipeline.return_value = mock_analysis
                mock_disclaimer.return_value = "Analysis with disclaimer"

                # Get the actual method from the command
                analyze_method = analyze_commands.analyze_command.callback

                # First call - should cache the result (only symbol parameter)
                await analyze_method(analyze_commands, interaction, symbol)

                # Verify result was cached
                assert cache_key in analyze_commands.cache
                assert cache_key in analyze_commands.cache_timestamps

                # Second call - should use cache
                mock_pipeline.reset_mock()
                await analyze_method(analyze_commands, interaction, symbol)

                # Should not call pipeline again (using cache)
                mock_pipeline.assert_not_called()


class TestUtilityCommandLogic:
    """Test the core logic of utility commands"""

    @pytest.fixture
    def mock_bot(self):
        """Create a mock bot for testing"""
        bot = Mock()
        bot.user = Mock()
        bot.user.display_name = "TestBot"
        bot.latency = 0.05  # 50ms latency
        return bot

    @pytest.fixture
    def utility_commands(self, mock_bot):
        """Create a UtilityCommands instance for testing"""
        from src.bot.extensions.utility import UtilityCommands
        return UtilityCommands(mock_bot)

    def test_utility_commands_initialization(self, utility_commands, mock_bot):
        """Test UtilityCommands initialization"""
        assert utility_commands.bot == mock_bot
        assert hasattr(utility_commands, 'ping_command')
        assert hasattr(utility_commands, 'test_command')

    @pytest.mark.asyncio
    async def test_ping_command_core_functionality(self, utility_commands):
        """Test the core ping command functionality"""
        interaction = Mock()
        interaction.user = Mock()
        interaction.user.display_name = "TestUser"
        interaction.response = AsyncMock()
        interaction.followup = AsyncMock()
        
        detailed = False
        
        with patch('src.bot.extensions.utility.handle_error_with_fallback') as mock_fallback:
            # Setup mocks
            mock_fallback.return_value = 25  # 25ms API latency
            
            # Get the actual method from the command
            ping_method = utility_commands.ping_command.callback
            
            # Execute the method directly
            await ping_method(utility_commands, interaction, detailed)
            
            # Verify response was sent
            interaction.response.send_message.assert_called_once()

            # Verify the response contains expected fields
            call_args = interaction.response.send_message.call_args
            if call_args and len(call_args[0]) > 0:
                # Check if embed was passed
                if hasattr(call_args[0][0], 'title'):
                    embed = call_args[0][0]
                    assert "Pong" in embed.title
                # Check if embed was passed as keyword argument
                elif 'embed' in call_args[1]:
                    embed = call_args[1]['embed']
                    assert hasattr(embed, 'title')
                    assert "Pong" in embed.title

    @pytest.mark.asyncio
    async def test_test_command_core_functionality(self, utility_commands):
        """Test the core test command functionality"""
        interaction = Mock()
        interaction.user = Mock()
        interaction.user.display_name = "TestUser"
        interaction.response = AsyncMock()
        interaction.followup = AsyncMock()
        
        full = False
        
        # Get the actual method from the command
        test_method = utility_commands.test_command.callback
        
        # Execute the method directly
        await test_method(utility_commands, interaction, full)
        
        # Verify response was deferred and followup was sent
        interaction.response.defer.assert_called_once()
        interaction.followup.send.assert_called()
        
        # Verify the response contains expected fields
        call_args = interaction.followup.send.call_args
        if call_args and len(call_args[0]) > 0:
            # Check if embed was passed
            if hasattr(call_args[0][0], 'title'):
                embed = call_args[0][0]
                assert "Test" in embed.title
            # Check if embed was passed as keyword argument
            elif 'embed' in call_args[1]:
                embed = call_args[1]['embed']
                assert hasattr(embed, 'title')
                assert "Test" in embed.title

    def test_utility_command_helper_methods(self, utility_commands):
        """Test utility command helper methods"""
        # Test _test_database method
        with patch('src.bot.extensions.utility.get_db_connection_status', new_callable=AsyncMock) as mock_db:
            mock_db.return_value = {"connected": True, "version": "PostgreSQL 13.0"}
            
            # This is an async method, so we need to run it
            import asyncio
            result = asyncio.run(utility_commands._test_database())
            
            assert "Connected" in result
            assert "PostgreSQL" in result

    def test_utility_command_cache_testing(self, utility_commands):
        """Test cache testing functionality"""
        with patch('src.shared.cache.cache_service.get_cache') as mock_get_cache:
            with patch('src.shared.cache.cache_service.get_cache_stats') as mock_stats:
                # Setup mocks
                mock_cache = Mock()
                mock_cache.set.return_value = None
                mock_cache.get.return_value = "value"
                mock_cache.delete.return_value = None
                mock_get_cache.return_value = mock_cache
                mock_stats.return_value = {"size": 100, "hit_rate": 85.5}
                
                # This is an async method, so we need to run it
                import asyncio
                result = asyncio.run(utility_commands._test_cache())
                
                assert "Cache OK" in result
                assert "items" in result


class TestBotExtensionsIntegration:
    """Integration tests for bot extensions"""

    @pytest.fixture
    def mock_bot(self):
        """Create a mock bot for integration testing"""
        bot = Mock()
        bot.user = Mock()
        bot.user.display_name = "TestBot"
        bot.latency = 0.05
        bot.permission_checker = Mock()
        bot.permission_checker.has_permission.return_value = True
        return bot

    def test_all_extensions_initialization(self, mock_bot):
        """Test that all bot extensions can be initialized"""
        # Test AskCommand
        from src.bot.extensions.ask import AskCommand
        ask_command = AskCommand(mock_bot)
        assert ask_command.bot == mock_bot
        
        # Test UtilityCommands
        from src.bot.extensions.utility import UtilityCommands
        utility_commands = UtilityCommands(mock_bot)
        assert utility_commands.bot == mock_bot
        
        # Test AsyncAnalyzeCommands (with mocked import)
        with patch.dict('sys.modules', {'src.bot.pipeline.commands.analyze.parallel_pipeline': Mock()}):
            from src.bot.extensions.analyze import AsyncAnalyzeCommands
            analyze_commands = AsyncAnalyzeCommands(mock_bot)
            assert analyze_commands.bot == mock_bot

    def test_extension_command_attributes(self, mock_bot):
        """Test that extensions have required command attributes"""
        from src.bot.extensions.ask import AskCommand
        from src.bot.extensions.utility import UtilityCommands
        
        ask_command = AskCommand(mock_bot)
        utility_commands = UtilityCommands(mock_bot)
        
        # Check ask command
        assert hasattr(ask_command, 'ask_command')
        assert hasattr(ask_command.ask_command, 'callback')
        
        # Check utility commands
        assert hasattr(utility_commands, 'ping_command')
        assert hasattr(utility_commands, 'test_command')
        assert hasattr(utility_commands.ping_command, 'callback')
        assert hasattr(utility_commands.test_command, 'callback')

    @pytest.mark.asyncio
    async def test_extension_error_handling(self, mock_bot):
        """Test error handling across extensions"""
        from src.bot.extensions.ask import AskCommand
        
        ask_command = AskCommand(mock_bot)
        
        # Test with invalid interaction
        invalid_interaction = Mock()
        invalid_interaction.user = None  # This should cause an error
        
        ask_method = ask_command.ask_command.callback
        
        # Should handle the error gracefully
        try:
            await ask_method(ask_command, invalid_interaction, "test query")
        except Exception as e:
            # Should not raise unhandled exceptions
            assert "user" in str(e).lower() or "attribute" in str(e).lower()
