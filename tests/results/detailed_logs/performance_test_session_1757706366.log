2025-09-12 15:46:06,458 | performance | INFO | log_performance:290 | ⚡ PERFORMANCE: ENVIRONMENT_VALIDATION (0.001s)
2025-09-12 15:46:06,458 | performance | DEBUG | log_performance:291 | Performance details: {
  "operation_id": "OP_000009",
  "operation_type": "PERFORMANCE",
  "operation": "ENVIRONMENT_VALIDATION",
  "duration_seconds": 0.0008103847503662109,
  "memory_usage_mb": null,
  "timestamp": "2025-09-12T15:46:06.458067",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:06,458 | performance | INFO | log_performance:290 | ⚡ PERFORMANCE: import_pytest (0.000s)
2025-09-12 15:46:06,458 | performance | DEBUG | log_performance:291 | Performance details: {
  "operation_id": "OP_000012",
  "operation_type": "PERFORMANCE",
  "operation": "import_pytest",
  "duration_seconds": 5.9604644775390625e-06,
  "memory_usage_mb": null,
  "timestamp": "2025-09-12T15:46:06.458307",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:06,478 | performance | INFO | log_performance:290 | ⚡ PERFORMANCE: import_pytest_asyncio (0.019s)
2025-09-12 15:46:06,478 | performance | DEBUG | log_performance:291 | Performance details: {
  "operation_id": "OP_000014",
  "operation_type": "PERFORMANCE",
  "operation": "import_pytest_asyncio",
  "duration_seconds": 0.019376039505004883,
  "memory_usage_mb": null,
  "timestamp": "2025-09-12T15:46:06.478147",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:06,479 | performance | INFO | log_performance:290 | ⚡ PERFORMANCE: import_pytest_cov (0.001s)
2025-09-12 15:46:06,479 | performance | DEBUG | log_performance:291 | Performance details: {
  "operation_id": "OP_000016",
  "operation_type": "PERFORMANCE",
  "operation": "import_pytest_cov",
  "duration_seconds": 0.0005042552947998047,
  "memory_usage_mb": null,
  "timestamp": "2025-09-12T15:46:06.479217",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:06,578 | performance | INFO | log_performance:290 | ⚡ PERFORMANCE: import_sqlalchemy (0.098s)
2025-09-12 15:46:06,578 | performance | DEBUG | log_performance:291 | Performance details: {
  "operation_id": "OP_000018",
  "operation_type": "PERFORMANCE",
  "operation": "import_sqlalchemy",
  "duration_seconds": 0.09835648536682129,
  "memory_usage_mb": null,
  "timestamp": "2025-09-12T15:46:06.578046",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:06,741 | performance | INFO | log_performance:290 | ⚡ PERFORMANCE: import_discord (0.163s)
2025-09-12 15:46:06,741 | performance | DEBUG | log_performance:291 | Performance details: {
  "operation_id": "OP_000020",
  "operation_type": "PERFORMANCE",
  "operation": "import_discord",
  "duration_seconds": 0.16336631774902344,
  "memory_usage_mb": null,
  "timestamp": "2025-09-12T15:46:06.741853",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:06,991 | performance | INFO | log_performance:290 | ⚡ PERFORMANCE: import_openai (0.249s)
2025-09-12 15:46:06,991 | performance | DEBUG | log_performance:291 | Performance details: {
  "operation_id": "OP_000022",
  "operation_type": "PERFORMANCE",
  "operation": "import_openai",
  "duration_seconds": 0.24932456016540527,
  "memory_usage_mb": null,
  "timestamp": "2025-09-12T15:46:06.991565",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:07,372 | performance | INFO | log_performance:290 | ⚡ PERFORMANCE: import_yfinance (0.380s)
2025-09-12 15:46:07,372 | performance | DEBUG | log_performance:291 | Performance details: {
  "operation_id": "OP_000024",
  "operation_type": "PERFORMANCE",
  "operation": "import_yfinance",
  "duration_seconds": 0.38013219833374023,
  "memory_usage_mb": null,
  "timestamp": "2025-09-12T15:46:07.372009",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:07,441 | performance | INFO | log_performance:290 | ⚡ PERFORMANCE: import_supabase (0.069s)
2025-09-12 15:46:07,441 | performance | DEBUG | log_performance:291 | Performance details: {
  "operation_id": "OP_000026",
  "operation_type": "PERFORMANCE",
  "operation": "import_supabase",
  "duration_seconds": 0.06906580924987793,
  "memory_usage_mb": null,
  "timestamp": "2025-09-12T15:46:07.441370",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:07,465 | performance | INFO | log_performance:290 | ⚡ PERFORMANCE: import_redis (0.024s)
2025-09-12 15:46:07,465 | performance | DEBUG | log_performance:291 | Performance details: {
  "operation_id": "OP_000028",
  "operation_type": "PERFORMANCE",
  "operation": "import_redis",
  "duration_seconds": 0.023688077926635742,
  "memory_usage_mb": null,
  "timestamp": "2025-09-12T15:46:07.465351",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:07,469 | performance | INFO | log_performance:290 | ⚡ PERFORMANCE: import_tenacity (0.004s)
2025-09-12 15:46:07,469 | performance | DEBUG | log_performance:291 | Performance details: {
  "operation_id": "OP_000030",
  "operation_type": "PERFORMANCE",
  "operation": "import_tenacity",
  "duration_seconds": 0.00428009033203125,
  "memory_usage_mb": null,
  "timestamp": "2025-09-12T15:46:07.469909",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:07,478 | performance | INFO | log_performance:290 | ⚡ PERFORMANCE: import_structlog (0.008s)
2025-09-12 15:46:07,478 | performance | DEBUG | log_performance:291 | Performance details: {
  "operation_id": "OP_000032",
  "operation_type": "PERFORMANCE",
  "operation": "import_structlog",
  "duration_seconds": 0.008207559585571289,
  "memory_usage_mb": null,
  "timestamp": "2025-09-12T15:46:07.478586",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:07,479 | performance | INFO | log_performance:290 | ⚡ PERFORMANCE: import_psutil (0.000s)
2025-09-12 15:46:07,479 | performance | DEBUG | log_performance:291 | Performance details: {
  "operation_id": "OP_000034",
  "operation_type": "PERFORMANCE",
  "operation": "import_psutil",
  "duration_seconds": 3.814697265625e-06,
  "memory_usage_mb": null,
  "timestamp": "2025-09-12T15:46:07.479707",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:07,479 | performance | INFO | log_performance:290 | ⚡ PERFORMANCE: IMPORT_VALIDATION (1.022s)
2025-09-12 15:46:07,479 | performance | DEBUG | log_performance:291 | Performance details: {
  "operation_id": "OP_000035",
  "operation_type": "PERFORMANCE",
  "operation": "IMPORT_VALIDATION",
  "duration_seconds": 1.0217335224151611,
  "memory_usage_mb": null,
  "timestamp": "2025-09-12T15:46:07.479872",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:11,609 | performance | INFO | log_performance:290 | ⚡ PERFORMANCE: PARSE_PYTEST_OUTPUT_UNIT_TESTS (0.001s)
2025-09-12 15:46:11,609 | performance | DEBUG | log_performance:291 | Performance details: {
  "operation_id": "OP_000040",
  "operation_type": "PERFORMANCE",
  "operation": "PARSE_PYTEST_OUTPUT_UNIT_TESTS",
  "duration_seconds": 0.001142740249633789,
  "memory_usage_mb": null,
  "timestamp": "2025-09-12T15:46:11.609573",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:11,610 | performance | INFO | log_performance:290 | ⚡ PERFORMANCE: UNIT_TESTS (4.130s)
2025-09-12 15:46:11,610 | performance | DEBUG | log_performance:291 | Performance details: {
  "operation_id": "OP_000042",
  "operation_type": "PERFORMANCE",
  "operation": "UNIT_TESTS",
  "duration_seconds": 4.130159616470337,
  "memory_usage_mb": null,
  "timestamp": "2025-09-12T15:46:11.610104",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:15,937 | performance | INFO | log_performance:290 | ⚡ PERFORMANCE: PARSE_PYTEST_OUTPUT_INTEGRATION_TESTS (0.000s)
2025-09-12 15:46:15,937 | performance | DEBUG | log_performance:291 | Performance details: {
  "operation_id": "OP_000046",
  "operation_type": "PERFORMANCE",
  "operation": "PARSE_PYTEST_OUTPUT_INTEGRATION_TESTS",
  "duration_seconds": 0.00016927719116210938,
  "memory_usage_mb": null,
  "timestamp": "2025-09-12T15:46:15.937421",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:15,937 | performance | INFO | log_performance:290 | ⚡ PERFORMANCE: INTEGRATION_TESTS (4.328s)
2025-09-12 15:46:15,937 | performance | DEBUG | log_performance:291 | Performance details: {
  "operation_id": "OP_000048",
  "operation_type": "PERFORMANCE",
  "operation": "INTEGRATION_TESTS",
  "duration_seconds": 4.327500820159912,
  "memory_usage_mb": null,
  "timestamp": "2025-09-12T15:46:15.937661",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:22,764 | performance | INFO | log_performance:290 | ⚡ PERFORMANCE: PARSE_PYTEST_OUTPUT_COMPREHENSIVE_TESTS (0.001s)
2025-09-12 15:46:22,764 | performance | DEBUG | log_performance:291 | Performance details: {
  "operation_id": "OP_000053",
  "operation_type": "PERFORMANCE",
  "operation": "PARSE_PYTEST_OUTPUT_COMPREHENSIVE_TESTS",
  "duration_seconds": 0.0006971359252929688,
  "memory_usage_mb": null,
  "timestamp": "2025-09-12T15:46:22.764806",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:22,765 | performance | INFO | log_performance:290 | ⚡ PERFORMANCE: COMPREHENSIVE_TESTS (6.827s)
2025-09-12 15:46:22,765 | performance | DEBUG | log_performance:291 | Performance details: {
  "operation_id": "OP_000055",
  "operation_type": "PERFORMANCE",
  "operation": "COMPREHENSIVE_TESTS",
  "duration_seconds": 6.827324867248535,
  "memory_usage_mb": null,
  "timestamp": "2025-09-12T15:46:22.765045",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:22,766 | performance | INFO | log_performance:290 | ⚡ PERFORMANCE: GENERATE_FINAL_REPORT (0.001s)
2025-09-12 15:46:22,766 | performance | DEBUG | log_performance:291 | Performance details: {
  "operation_id": "OP_000058",
  "operation_type": "PERFORMANCE",
  "operation": "GENERATE_FINAL_REPORT",
  "duration_seconds": 0.000993967056274414,
  "memory_usage_mb": null,
  "timestamp": "2025-09-12T15:46:22.766095",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:22,766 | performance | INFO | log_performance:290 | ⚡ PERFORMANCE: COMPREHENSIVE_TEST_RUN (16.310s)
2025-09-12 15:46:22,766 | performance | DEBUG | log_performance:291 | Performance details: {
  "operation_id": "OP_000059",
  "operation_type": "PERFORMANCE",
  "operation": "COMPREHENSIVE_TEST_RUN",
  "duration_seconds": 16.309703826904297,
  "memory_usage_mb": null,
  "timestamp": "2025-09-12T15:46:22.766543",
  "session_id": "test_session_1757706366"
}
