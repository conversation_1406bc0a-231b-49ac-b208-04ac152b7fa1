2025-09-12 15:45:28,781 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: SINGLE_TEST_DEMO
2025-09-12 15:45:28,781 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000001",
  "operation_type": "OPERATION",
  "operation": "SINGLE_TEST_DEMO",
  "details": {},
  "timestamp": "2025-09-12T15:45:28.781266",
  "session_id": "test_session_1757706328"
}
2025-09-12 15:45:28,781 | comprehensive_test | INFO | log_test_start:154 | 🚀 TEST STARTED: test_ml_sentiment_analyzer
2025-09-12 15:45:28,782 | comprehensive_test | DEBUG | log_test_start:155 | Test details: {
  "operation_id": "OP_000002",
  "operation_type": "TEST_START",
  "test_name": "test_ml_sentiment_analyzer",
  "test_class": "TestMLSentimentAnalyzer",
  "timestamp": "2025-09-12T15:45:28.781947",
  "session_id": "test_session_1757706328"
}
2025-09-12 15:45:28,782 | comprehensive_test | INFO | log_assertion:350 | ✅ PASS ASSERTION: sentiment_analysis
2025-09-12 15:45:28,782 | comprehensive_test | DEBUG | log_assertion:351 | Assertion details: {
  "operation_id": "OP_000004",
  "operation_type": "ASSERTION",
  "assertion_type": "sentiment_analysis",
  "expected": "positive",
  "actual": "positive",
  "result": true,
  "test_name": "test_ml_sentiment_analyzer",
  "timestamp": "2025-09-12T15:45:28.782413",
  "session_id": "test_session_1757706328"
}
2025-09-12 15:45:28,782 | comprehensive_test | INFO | log_test_end:172 | ✅ TEST PASSED: test_ml_sentiment_analyzer (0.123s)
2025-09-12 15:45:28,782 | comprehensive_test | DEBUG | log_test_end:173 | Test end details: {
  "operation_id": "OP_000001",
  "operation_type": "TEST_END",
  "test_name": "test_ml_sentiment_analyzer",
  "result": "PASSED",
  "duration_seconds": 0.123,
  "timestamp": "2025-09-12T15:45:28.782542",
  "session_id": "test_session_1757706328"
}
