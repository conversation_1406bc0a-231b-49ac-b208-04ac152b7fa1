2025-09-12 15:44:29,002 | test_errors | ERROR | log_error:323 | 💥 ERROR: TypeError - log_performance_metric() takes 2 positional arguments but 3 were given
2025-09-12 15:44:29,002 | test_errors | DEBUG | log_error:324 | Error details: {
  "operation_id": "OP_000041",
  "operation_type": "ERROR",
  "error_type": "TypeError",
  "error_message": "log_performance_metric() takes 2 positional arguments but 3 were given",
  "error_traceback": "Traceback (most recent call last):\n  File \"/home/<USER>/Desktop/tradingview-automatio/test_results/detailed_logs/comprehensive_test_runner.py\", line 234, in run_unit_tests\n    log_performance_metric(\"unit_tests_execution\", duration, {\nTypeError: log_performance_metric() takes 2 positional arguments but 3 were given\n",
  "context": {
    "test_type": "unit_tests",
    "operation_id": "OP_000036"
  },
  "timestamp": "2025-09-12T15:44:29.002341",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:38,132 | test_errors | ERROR | log_error:323 | 💥 ERROR: TypeError - log_performance_metric() takes 2 positional arguments but 3 were given
2025-09-12 15:44:38,132 | test_errors | DEBUG | log_error:324 | Error details: {
  "operation_id": "OP_000047",
  "operation_type": "ERROR",
  "error_type": "TypeError",
  "error_message": "log_performance_metric() takes 2 positional arguments but 3 were given",
  "error_traceback": "Traceback (most recent call last):\n  File \"/home/<USER>/Desktop/tradingview-automatio/test_results/detailed_logs/comprehensive_test_runner.py\", line 291, in run_integration_tests\n    log_performance_metric(\"integration_tests_execution\", duration, {\nTypeError: log_performance_metric() takes 2 positional arguments but 3 were given\n",
  "context": {
    "test_type": "integration_tests",
    "operation_id": "OP_000043"
  },
  "timestamp": "2025-09-12T15:44:38.132755",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:49,606 | test_errors | ERROR | log_error:323 | 💥 ERROR: TypeError - log_performance_metric() takes 2 positional arguments but 3 were given
2025-09-12 15:44:49,606 | test_errors | DEBUG | log_error:324 | Error details: {
  "operation_id": "OP_000054",
  "operation_type": "ERROR",
  "error_type": "TypeError",
  "error_message": "log_performance_metric() takes 2 positional arguments but 3 were given",
  "error_traceback": "Traceback (most recent call last):\n  File \"/home/<USER>/Desktop/tradingview-automatio/test_results/detailed_logs/comprehensive_test_runner.py\", line 345, in run_comprehensive_tests\n    log_performance_metric(\"comprehensive_tests_execution\", duration, {\nTypeError: log_performance_metric() takes 2 positional arguments but 3 were given\n",
  "context": {
    "test_type": "comprehensive_tests",
    "operation_id": "OP_000049"
  },
  "timestamp": "2025-09-12T15:44:49.606207",
  "session_id": "test_session_1757706260"
}
