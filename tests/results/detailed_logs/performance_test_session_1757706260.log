2025-09-12 15:44:20,379 | performance | INFO | log_performance:290 | ⚡ PERFORMANCE: ENVIRONMENT_VALIDATION (0.001s)
2025-09-12 15:44:20,379 | performance | DEBUG | log_performance:291 | Performance details: {
  "operation_id": "OP_000009",
  "operation_type": "PERFORMANCE",
  "operation": "ENVIRONMENT_VALIDATION",
  "duration_seconds": 0.0010569095611572266,
  "memory_usage_mb": null,
  "timestamp": "2025-09-12T15:44:20.379688",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:20,380 | performance | INFO | log_performance:290 | ⚡ PERFORMANCE: import_pytest (0.000s)
2025-09-12 15:44:20,380 | performance | DEBUG | log_performance:291 | Performance details: {
  "operation_id": "OP_000012",
  "operation_type": "PERFORMANCE",
  "operation": "import_pytest",
  "duration_seconds": 1.0251998901367188e-05,
  "memory_usage_mb": null,
  "timestamp": "2025-09-12T15:44:20.380140",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:20,426 | performance | INFO | log_performance:290 | ⚡ PERFORMANCE: import_pytest_asyncio (0.045s)
2025-09-12 15:44:20,426 | performance | DEBUG | log_performance:291 | Performance details: {
  "operation_id": "OP_000014",
  "operation_type": "PERFORMANCE",
  "operation": "import_pytest_asyncio",
  "duration_seconds": 0.0451817512512207,
  "memory_usage_mb": null,
  "timestamp": "2025-09-12T15:44:20.425972",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:20,427 | performance | INFO | log_performance:290 | ⚡ PERFORMANCE: import_pytest_cov (0.001s)
2025-09-12 15:44:20,427 | performance | DEBUG | log_performance:291 | Performance details: {
  "operation_id": "OP_000016",
  "operation_type": "PERFORMANCE",
  "operation": "import_pytest_cov",
  "duration_seconds": 0.0008254051208496094,
  "memory_usage_mb": null,
  "timestamp": "2025-09-12T15:44:20.427478",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:20,623 | performance | INFO | log_performance:290 | ⚡ PERFORMANCE: import_sqlalchemy (0.195s)
2025-09-12 15:44:20,623 | performance | DEBUG | log_performance:291 | Performance details: {
  "operation_id": "OP_000018",
  "operation_type": "PERFORMANCE",
  "operation": "import_sqlalchemy",
  "duration_seconds": 0.19489002227783203,
  "memory_usage_mb": null,
  "timestamp": "2025-09-12T15:44:20.623216",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:20,921 | performance | INFO | log_performance:290 | ⚡ PERFORMANCE: import_discord (0.297s)
2025-09-12 15:44:20,921 | performance | DEBUG | log_performance:291 | Performance details: {
  "operation_id": "OP_000020",
  "operation_type": "PERFORMANCE",
  "operation": "import_discord",
  "duration_seconds": 0.29721689224243164,
  "memory_usage_mb": null,
  "timestamp": "2025-09-12T15:44:20.921014",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:21,383 | performance | INFO | log_performance:290 | ⚡ PERFORMANCE: import_openai (0.461s)
2025-09-12 15:44:21,383 | performance | DEBUG | log_performance:291 | Performance details: {
  "operation_id": "OP_000022",
  "operation_type": "PERFORMANCE",
  "operation": "import_openai",
  "duration_seconds": 0.4612107276916504,
  "memory_usage_mb": null,
  "timestamp": "2025-09-12T15:44:21.383012",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:22,131 | performance | INFO | log_performance:290 | ⚡ PERFORMANCE: import_yfinance (0.748s)
2025-09-12 15:44:22,131 | performance | DEBUG | log_performance:291 | Performance details: {
  "operation_id": "OP_000024",
  "operation_type": "PERFORMANCE",
  "operation": "import_yfinance",
  "duration_seconds": 0.7480349540710449,
  "memory_usage_mb": null,
  "timestamp": "2025-09-12T15:44:22.131796",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:22,249 | performance | INFO | log_performance:290 | ⚡ PERFORMANCE: import_supabase (0.117s)
2025-09-12 15:44:22,249 | performance | DEBUG | log_performance:291 | Performance details: {
  "operation_id": "OP_000026",
  "operation_type": "PERFORMANCE",
  "operation": "import_supabase",
  "duration_seconds": 0.11747932434082031,
  "memory_usage_mb": null,
  "timestamp": "2025-09-12T15:44:22.249751",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:22,302 | performance | INFO | log_performance:290 | ⚡ PERFORMANCE: import_redis (0.052s)
2025-09-12 15:44:22,303 | performance | DEBUG | log_performance:291 | Performance details: {
  "operation_id": "OP_000028",
  "operation_type": "PERFORMANCE",
  "operation": "import_redis",
  "duration_seconds": 0.05241680145263672,
  "memory_usage_mb": null,
  "timestamp": "2025-09-12T15:44:22.302829",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:22,310 | performance | INFO | log_performance:290 | ⚡ PERFORMANCE: import_tenacity (0.007s)
2025-09-12 15:44:22,310 | performance | DEBUG | log_performance:291 | Performance details: {
  "operation_id": "OP_000030",
  "operation_type": "PERFORMANCE",
  "operation": "import_tenacity",
  "duration_seconds": 0.007498979568481445,
  "memory_usage_mb": null,
  "timestamp": "2025-09-12T15:44:22.310918",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:22,328 | performance | INFO | log_performance:290 | ⚡ PERFORMANCE: import_structlog (0.017s)
2025-09-12 15:44:22,328 | performance | DEBUG | log_performance:291 | Performance details: {
  "operation_id": "OP_000032",
  "operation_type": "PERFORMANCE",
  "operation": "import_structlog",
  "duration_seconds": 0.01726078987121582,
  "memory_usage_mb": null,
  "timestamp": "2025-09-12T15:44:22.328548",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:22,329 | performance | INFO | log_performance:290 | ⚡ PERFORMANCE: import_psutil (0.000s)
2025-09-12 15:44:22,329 | performance | DEBUG | log_performance:291 | Performance details: {
  "operation_id": "OP_000034",
  "operation_type": "PERFORMANCE",
  "operation": "import_psutil",
  "duration_seconds": 5.4836273193359375e-06,
  "memory_usage_mb": null,
  "timestamp": "2025-09-12T15:44:22.329504",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:22,329 | performance | INFO | log_performance:290 | ⚡ PERFORMANCE: IMPORT_VALIDATION (1.950s)
2025-09-12 15:44:22,329 | performance | DEBUG | log_performance:291 | Performance details: {
  "operation_id": "OP_000035",
  "operation_type": "PERFORMANCE",
  "operation": "IMPORT_VALIDATION",
  "duration_seconds": 1.9499497413635254,
  "memory_usage_mb": null,
  "timestamp": "2025-09-12T15:44:22.329777",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:29,001 | performance | INFO | log_performance:290 | ⚡ PERFORMANCE: PARSE_PYTEST_OUTPUT_UNIT_TESTS (0.003s)
2025-09-12 15:44:29,001 | performance | DEBUG | log_performance:291 | Performance details: {
  "operation_id": "OP_000040",
  "operation_type": "PERFORMANCE",
  "operation": "PARSE_PYTEST_OUTPUT_UNIT_TESTS",
  "duration_seconds": 0.0025055408477783203,
  "memory_usage_mb": null,
  "timestamp": "2025-09-12T15:44:29.001009",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:29,002 | performance | INFO | log_performance:290 | ⚡ PERFORMANCE: UNIT_TESTS (6.673s)
2025-09-12 15:44:29,002 | performance | DEBUG | log_performance:291 | Performance details: {
  "operation_id": "OP_000042",
  "operation_type": "PERFORMANCE",
  "operation": "UNIT_TESTS",
  "duration_seconds": 6.672902345657349,
  "memory_usage_mb": null,
  "timestamp": "2025-09-12T15:44:29.002784",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:38,132 | performance | INFO | log_performance:290 | ⚡ PERFORMANCE: PARSE_PYTEST_OUTPUT_INTEGRATION_TESTS (0.000s)
2025-09-12 15:44:38,132 | performance | DEBUG | log_performance:291 | Performance details: {
  "operation_id": "OP_000046",
  "operation_type": "PERFORMANCE",
  "operation": "PARSE_PYTEST_OUTPUT_INTEGRATION_TESTS",
  "duration_seconds": 0.0003314018249511719,
  "memory_usage_mb": null,
  "timestamp": "2025-09-12T15:44:38.132434",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:38,132 | performance | INFO | log_performance:290 | ⚡ PERFORMANCE: INTEGRATION_TESTS (9.130s)
2025-09-12 15:44:38,132 | performance | DEBUG | log_performance:291 | Performance details: {
  "operation_id": "OP_000048",
  "operation_type": "PERFORMANCE",
  "operation": "INTEGRATION_TESTS",
  "duration_seconds": 9.129896879196167,
  "memory_usage_mb": null,
  "timestamp": "2025-09-12T15:44:38.132890",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:49,605 | performance | INFO | log_performance:290 | ⚡ PERFORMANCE: PARSE_PYTEST_OUTPUT_COMPREHENSIVE_TESTS (0.002s)
2025-09-12 15:44:49,605 | performance | DEBUG | log_performance:291 | Performance details: {
  "operation_id": "OP_000053",
  "operation_type": "PERFORMANCE",
  "operation": "PARSE_PYTEST_OUTPUT_COMPREHENSIVE_TESTS",
  "duration_seconds": 0.0020771026611328125,
  "memory_usage_mb": null,
  "timestamp": "2025-09-12T15:44:49.605691",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:49,606 | performance | INFO | log_performance:290 | ⚡ PERFORMANCE: COMPREHENSIVE_TESTS (11.473s)
2025-09-12 15:44:49,606 | performance | DEBUG | log_performance:291 | Performance details: {
  "operation_id": "OP_000055",
  "operation_type": "PERFORMANCE",
  "operation": "COMPREHENSIVE_TESTS",
  "duration_seconds": 11.473331212997437,
  "memory_usage_mb": null,
  "timestamp": "2025-09-12T15:44:49.606385",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:49,609 | performance | INFO | log_performance:290 | ⚡ PERFORMANCE: GENERATE_FINAL_REPORT (0.003s)
2025-09-12 15:44:49,609 | performance | DEBUG | log_performance:291 | Performance details: {
  "operation_id": "OP_000058",
  "operation_type": "PERFORMANCE",
  "operation": "GENERATE_FINAL_REPORT",
  "duration_seconds": 0.002655506134033203,
  "memory_usage_mb": null,
  "timestamp": "2025-09-12T15:44:49.609197",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:49,612 | performance | INFO | log_performance:290 | ⚡ PERFORMANCE: COMPREHENSIVE_TEST_RUN (29.235s)
2025-09-12 15:44:49,612 | performance | DEBUG | log_performance:291 | Performance details: {
  "operation_id": "OP_000059",
  "operation_type": "PERFORMANCE",
  "operation": "COMPREHENSIVE_TEST_RUN",
  "duration_seconds": 29.23454236984253,
  "memory_usage_mb": null,
  "timestamp": "2025-09-12T15:44:49.612651",
  "session_id": "test_session_1757706260"
}
