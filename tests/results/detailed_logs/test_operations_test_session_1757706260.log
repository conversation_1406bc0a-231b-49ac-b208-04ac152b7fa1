2025-09-12 15:44:20,378 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: COMPREHENSIVE_TEST_RUN
2025-09-12 15:44:20,378 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000001",
  "operation_type": "OPERATION",
  "operation": "COMPREHENSIVE_TEST_RUN",
  "details": {
    "start_time": "2025-09-12T15:44:20.378085"
  },
  "timestamp": "2025-09-12T15:44:20.378104",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:20,378 | comprehensive_test | INFO | log_test_start:154 | 🚀 TEST STARTED: COMPREHENSIVE_TEST_RUN
2025-09-12 15:44:20,378 | comprehensive_test | DEBUG | log_test_start:155 | Test details: {
  "operation_id": "OP_000002",
  "operation_type": "TEST_START",
  "test_name": "COMPREHENSIVE_TEST_RUN",
  "test_class": "ComprehensiveTestRunner",
  "timestamp": "2025-09-12T15:44:20.378389",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:20,378 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: ENVIRONMENT_VALIDATION
2025-09-12 15:44:20,378 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000003",
  "operation_type": "OPERATION",
  "operation": "ENVIRONMENT_VALIDATION",
  "details": {},
  "timestamp": "2025-09-12T15:44:20.378632",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:20,378 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: ENVIRONMENT_VALIDATION
2025-09-12 15:44:20,379 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000003",
  "operation_type": "OPERATION",
  "operation": "ENVIRONMENT_VALIDATION",
  "details": {
    "python_version": "3.12.3 (main, Aug 14 2025, 17:47:21) [GCC 13.3.0]",
    "python_executable": "/home/<USER>/Desktop/tradingview-automatio/venv/bin/python",
    "working_directory": "/home/<USER>/Desktop/tradingview-automatio",
    "environment_variables": {
      "SHELL": "/bin/bash",
      "SESSION_MANAGER": "local/fedora-workstation:@/tmp/.ICE-unix/1142,unix/fedora-workstation:/tmp/.ICE-unix/1142",
      "QT_ACCESSIBILITY": "1",
      "npm_config_yes": "true",
      "COLORTERM": "truecolor",
      "XDG_CONFIG_DIRS": "/etc/xdg/xdg-cinnamon:/etc/xdg",
      "VSCODE_DEBUGPY_ADAPTER_ENDPOINTS": "/home/<USER>/.cursor/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-e78ebade99b7b16e.txt",
      "PIP_NO_INPUT": "true",
      "XDG_SESSION_PATH": "/org/freedesktop/DisplayManager/Session0",
      "DISABLE_AUTO_UPDATE": "true",
      "TERM_PROGRAM_VERSION": "1.1.6",
      "GNOME_DESKTOP_SESSION_ID": "this-is-deprecated",
      "COMPOSER_NO_INTERACTION": "1",
      "GNOME_KEYRING_CONTROL": "/run/user/1000/keyring",
      "PERLLIB": "/tmp/.mount_cursordYGeAK/usr/share/perl5/:/tmp/.mount_cursordYGeAK/usr/lib/perl5/:",
      "LANGUAGE": "en_US",
      "SSH_AUTH_SOCK": "/run/user/1000/keyring/ssh",
      "ARGV0": "/opt/cursor/cursor.AppImage",
      "GEMINI_API_KEY": "",
      "PYDEVD_DISABLE_FILE_VALIDATION": "1",
      "DESKTOP_SESSION": "cinnamon",
      "GTK_MODULES": "gail:atk-bridge",
      "XDG_SEAT": "seat0",
      "PWD": "/home/<USER>/Desktop/tradingview-automatio",
      "GSETTINGS_SCHEMA_DIR": "/tmp/.mount_cursordYGeAK/usr/share/glib-2.0/schemas/:",
      "XDG_SESSION_DESKTOP": "cinnamon",
      "LOGNAME": "ami",
      "XDG_SESSION_TYPE": "x11",
      "GPG_AGENT_INFO": "/run/user/1000/gnupg/S.gpg-agent:0:1",
      "BUNDLED_DEBUGPY_PATH": "/home/<USER>/.cursor/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy",
      "XAUTHORITY": "/home/<USER>/.Xauthority",
      "VSCODE_GIT_ASKPASS_NODE": "/tmp/.mount_cursordYGeAK/usr/share/cursor/cursor",
      "XDG_GREETER_DATA_DIR": "/var/lib/lightdm-data/ami",
      "GDM_LANG": "en_US",
      "INSIDE_NEMO_PYTHON": "",
      "HOME": "/home/<USER>",
      "LANG": "en_US.UTF-8",
      "LS_COLORS": "rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:",
      "XDG_CURRENT_DESKTOP": "X-Cinnamon",
      "VIRTUAL_ENV": "/home/<USER>/Desktop/tradingview-automatio/venv",
      "GIT_ASKPASS": "/tmp/.mount_cursordYGeAK/usr/share/cursor/resources/app/extensions/git/dist/askpass.sh",
      "XDG_SEAT_PATH": "/org/freedesktop/DisplayManager/Seat0",
      "CHROME_DESKTOP": "cursor.desktop",
      "APPDIR": "/tmp/.mount_cursordYGeAK",
      "VSCODE_GIT_ASKPASS_EXTRA_ARGS": "",
      "LESSCLOSE": "/usr/bin/lesspipe %s %s",
      "XDG_SESSION_CLASS": "user",
      "TERM": "xterm-256color",
      "LESSOPEN": "| /usr/bin/lesspipe %s",
      "USER": "ami",
      "VSCODE_GIT_IPC_HANDLE": "/run/user/1000/vscode-git-8c0a4d5b6f.sock",
      "OWD": "/home/<USER>",
      "DISPLAY": ":0",
      "SHLVL": "1",
      "PAGER": "head -n 10000 | cat",
      "XDG_VTNR": "7",
      "XDG_SESSION_ID": "c1",
      "VIRTUAL_ENV_PROMPT": "(venv) ",
      "LD_LIBRARY_PATH": "/tmp/.mount_cursordYGeAK/usr/lib/:/tmp/.mount_cursordYGeAK/usr/lib32/:/tmp/.mount_cursordYGeAK/usr/lib64/:/tmp/.mount_cursordYGeAK/lib/:/tmp/.mount_cursordYGeAK/lib/i386-linux-gnu/:/tmp/.mount_cursordYGeAK/lib/x86_64-linux-gnu/:/tmp/.mount_cursordYGeAK/lib/aarch64-linux-gnu/:/tmp/.mount_cursordYGeAK/lib32/:/tmp/.mount_cursordYGeAK/lib64/:",
      "APPIMAGE": "/opt/cursor/cursor.AppImage",
      "XDG_RUNTIME_DIR": "/run/user/1000",
      "PS1": "(venv) \\[\u001b]633;A\u0007\\]\\[\\e]0;\\u@\\h: \\w\\a\\]${debian_chroot:+($debian_chroot)}\\[\\033[01;32m\\]\\u@\\h\\[\\033[00m\\]:\\[\\033[01;34m\\]\\w\\[\\033[00m\\]\\$ \\[\u001b]633;B\u0007\\]",
      "VSCODE_GIT_ASKPASS_MAIN": "/tmp/.mount_cursordYGeAK/usr/share/cursor/resources/app/extensions/git/dist/askpass-main.js",
      "GTK3_MODULES": "xapp-gtk3-module",
      "XDG_DATA_DIRS": "/tmp/.mount_cursordYGeAK/usr/share/:/usr/local/share:/usr/share:/usr/share/cinnamon:/usr/share/gnome:/home/<USER>/.local/share/flatpak/exports/share:/var/lib/flatpak/exports/share:/usr/local/share:/usr/share",
      "GDK_BACKEND": "x11",
      "PATH": "/home/<USER>/Desktop/tradingview-automatio/venv/bin:/home/<USER>/.npm-global/bin:/home/<USER>/.local/bin:/home/<USER>/bin:/home/<USER>/.cargo/bin:/home/<USER>/.avm/bin:/home/<USER>/.npm-global/bin:/home/<USER>/.local/bin:/home/<USER>/bin:/home/<USER>/.cargo/bin:/home/<USER>/.avm/bin:/tmp/.mount_cursordYGeAK/usr/bin/:/tmp/.mount_cursordYGeAK/usr/sbin/:/tmp/.mount_cursordYGeAK/usr/games/:/tmp/.mount_cursordYGeAK/bin/:/tmp/.mount_cursordYGeAK/sbin/:/home/<USER>/.npm-global/bin:/home/<USER>/.local/bin:/home/<USER>/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/home/<USER>/.cursor/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts",
      "GDMSESSION": "cinnamon",
      "ORIGINAL_XDG_CURRENT_DESKTOP": "X-Cinnamon",
      "DBUS_SESSION_BUS_ADDRESS": "unix:path=/run/user/1000/bus",
      "QT_PLUGIN_PATH": "/tmp/.mount_cursordYGeAK/usr/lib/qt4/plugins/:/tmp/.mount_cursordYGeAK/usr/lib/i386-linux-gnu/qt4/plugins/:/tmp/.mount_cursordYGeAK/usr/lib/x86_64-linux-gnu/qt4/plugins/:/tmp/.mount_cursordYGeAK/usr/lib/aarch64-linux-gnu/qt4/plugins/:/tmp/.mount_cursordYGeAK/usr/lib32/qt4/plugins/:/tmp/.mount_cursordYGeAK/usr/lib64/qt4/plugins/:/tmp/.mount_cursordYGeAK/usr/lib/qt5/plugins/:/tmp/.mount_cursordYGeAK/usr/lib/i386-linux-gnu/qt5/plugins/:/tmp/.mount_cursordYGeAK/usr/lib/x86_64-linux-gnu/qt5/plugins/:/tmp/.mount_cursordYGeAK/usr/lib/aarch64-linux-gnu/qt5/plugins/:/tmp/.mount_cursordYGeAK/usr/lib32/qt5/plugins/:/tmp/.mount_cursordYGeAK/usr/lib64/qt5/plugins/:",
      "GIO_LAUNCHED_DESKTOP_FILE_PID": "3169",
      "GIO_LAUNCHED_DESKTOP_FILE": "/home/<USER>/Desktop/cursor.desktop",
      "TERM_PROGRAM": "vscode",
      "CURSOR_TRACE_ID": "209196c0e5e643619deb1939974b3db6",
      "_": "/home/<USER>/Desktop/tradingview-automatio/venv/bin/python"
    }
  },
  "timestamp": "2025-09-12T15:44:20.378844",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:20,379 | comprehensive_test | INFO | log_assertion:350 | ✅ PASS ASSERTION: python_version_check
2025-09-12 15:44:20,379 | comprehensive_test | DEBUG | log_assertion:351 | Assertion details: {
  "operation_id": "OP_000004",
  "operation_type": "ASSERTION",
  "assertion_type": "python_version_check",
  "expected": ">= 3.8",
  "actual": "3.12",
  "result": true,
  "test_name": "ENVIRONMENT_VALIDATION",
  "timestamp": "2025-09-12T15:44:20.379057",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:20,379 | comprehensive_test | INFO | log_assertion:350 | ✅ PASS ASSERTION: directory_exists
2025-09-12 15:44:20,379 | comprehensive_test | DEBUG | log_assertion:351 | Assertion details: {
  "operation_id": "OP_000005",
  "operation_type": "ASSERTION",
  "assertion_type": "directory_exists",
  "expected": "True",
  "actual": "True",
  "result": true,
  "test_name": "ENVIRONMENT_VALIDATION_src",
  "timestamp": "2025-09-12T15:44:20.379180",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:20,379 | comprehensive_test | INFO | log_assertion:350 | ✅ PASS ASSERTION: directory_exists
2025-09-12 15:44:20,379 | comprehensive_test | DEBUG | log_assertion:351 | Assertion details: {
  "operation_id": "OP_000006",
  "operation_type": "ASSERTION",
  "assertion_type": "directory_exists",
  "expected": "True",
  "actual": "True",
  "result": true,
  "test_name": "ENVIRONMENT_VALIDATION_tests",
  "timestamp": "2025-09-12T15:44:20.379288",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:20,379 | comprehensive_test | INFO | log_assertion:350 | ✅ PASS ASSERTION: directory_exists
2025-09-12 15:44:20,379 | comprehensive_test | DEBUG | log_assertion:351 | Assertion details: {
  "operation_id": "OP_000007",
  "operation_type": "ASSERTION",
  "assertion_type": "directory_exists",
  "expected": "True",
  "actual": "True",
  "result": true,
  "test_name": "ENVIRONMENT_VALIDATION_test_results",
  "timestamp": "2025-09-12T15:44:20.379384",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:20,379 | comprehensive_test | INFO | log_assertion:350 | ✅ PASS ASSERTION: log_directory_exists
2025-09-12 15:44:20,379 | comprehensive_test | DEBUG | log_assertion:351 | Assertion details: {
  "operation_id": "OP_000008",
  "operation_type": "ASSERTION",
  "assertion_type": "log_directory_exists",
  "expected": "True",
  "actual": "True",
  "result": true,
  "test_name": "ENVIRONMENT_VALIDATION",
  "timestamp": "2025-09-12T15:44:20.379480",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:20,379 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: ENVIRONMENT_VALIDATION_COMPLETE
2025-09-12 15:44:20,379 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000003",
  "operation_type": "OPERATION",
  "operation": "ENVIRONMENT_VALIDATION_COMPLETE",
  "details": {
    "status": "SUCCESS",
    "python_version": "3.12.3",
    "required_directories": {
      "src": true,
      "tests": true,
      "test_results": true
    }
  },
  "timestamp": "2025-09-12T15:44:20.379585",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:20,379 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: IMPORT_VALIDATION
2025-09-12 15:44:20,379 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000010",
  "operation_type": "OPERATION",
  "operation": "IMPORT_VALIDATION",
  "details": {},
  "timestamp": "2025-09-12T15:44:20.379828",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:20,379 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: IMPORT_VALIDATION_START
2025-09-12 15:44:20,379 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000010",
  "operation_type": "OPERATION",
  "operation": "IMPORT_VALIDATION_START",
  "details": {
    "modules_to_check": [
      "pytest",
      "pytest_asyncio",
      "pytest_cov",
      "sqlalchemy",
      "discord",
      "openai",
      "yfinance",
      "supabase",
      "redis",
      "tenacity",
      "structlog",
      "psutil"
    ],
    "total_modules": 12
  },
  "timestamp": "2025-09-12T15:44:20.379919",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:20,380 | comprehensive_test | INFO | log_import:371 | ✅ SUCCESS IMPORT: pytest
2025-09-12 15:44:20,380 | comprehensive_test | DEBUG | log_import:372 | Import details: {
  "operation_id": "OP_000011",
  "operation_type": "IMPORT",
  "module_name": "pytest",
  "success": true,
  "error": null,
  "timestamp": "2025-09-12T15:44:20.380034",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:20,380 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: MODULE_IMPORTED
2025-09-12 15:44:20,380 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000010",
  "operation_type": "OPERATION",
  "operation": "MODULE_IMPORTED",
  "details": {
    "module_name": "pytest",
    "module_version": "8.4.2",
    "module_file": "/home/<USER>/Desktop/tradingview-automatio/venv/lib/python3.12/site-packages/pytest/__init__.py",
    "import_duration": 1.0251998901367188e-05
  },
  "timestamp": "2025-09-12T15:44:20.380233",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:20,425 | comprehensive_test | INFO | log_import:371 | ✅ SUCCESS IMPORT: pytest_asyncio
2025-09-12 15:44:20,425 | comprehensive_test | DEBUG | log_import:372 | Import details: {
  "operation_id": "OP_000013",
  "operation_type": "IMPORT",
  "module_name": "pytest_asyncio",
  "success": true,
  "error": null,
  "timestamp": "2025-09-12T15:44:20.425511",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:20,426 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: MODULE_IMPORTED
2025-09-12 15:44:20,426 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000010",
  "operation_type": "OPERATION",
  "operation": "MODULE_IMPORTED",
  "details": {
    "module_name": "pytest_asyncio",
    "module_version": "1.2.0",
    "module_file": "/home/<USER>/Desktop/tradingview-automatio/venv/lib/python3.12/site-packages/pytest_asyncio/__init__.py",
    "import_duration": 0.0451817512512207
  },
  "timestamp": "2025-09-12T15:44:20.426180",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:20,427 | comprehensive_test | INFO | log_import:371 | ✅ SUCCESS IMPORT: pytest_cov
2025-09-12 15:44:20,427 | comprehensive_test | DEBUG | log_import:372 | Import details: {
  "operation_id": "OP_000015",
  "operation_type": "IMPORT",
  "module_name": "pytest_cov",
  "success": true,
  "error": null,
  "timestamp": "2025-09-12T15:44:20.427145",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:20,427 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: MODULE_IMPORTED
2025-09-12 15:44:20,427 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000010",
  "operation_type": "OPERATION",
  "operation": "MODULE_IMPORTED",
  "details": {
    "module_name": "pytest_cov",
    "module_version": "7.0.0",
    "module_file": "/home/<USER>/Desktop/tradingview-automatio/venv/lib/python3.12/site-packages/pytest_cov/__init__.py",
    "import_duration": 0.0008254051208496094
  },
  "timestamp": "2025-09-12T15:44:20.427605",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:20,622 | comprehensive_test | INFO | log_import:371 | ✅ SUCCESS IMPORT: sqlalchemy
2025-09-12 15:44:20,623 | comprehensive_test | DEBUG | log_import:372 | Import details: {
  "operation_id": "OP_000017",
  "operation_type": "IMPORT",
  "module_name": "sqlalchemy",
  "success": true,
  "error": null,
  "timestamp": "2025-09-12T15:44:20.622678",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:20,623 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: MODULE_IMPORTED
2025-09-12 15:44:20,623 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000010",
  "operation_type": "OPERATION",
  "operation": "MODULE_IMPORTED",
  "details": {
    "module_name": "sqlalchemy",
    "module_version": "2.0.43",
    "module_file": "/home/<USER>/Desktop/tradingview-automatio/venv/lib/python3.12/site-packages/sqlalchemy/__init__.py",
    "import_duration": 0.19489002227783203
  },
  "timestamp": "2025-09-12T15:44:20.623353",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:20,920 | comprehensive_test | INFO | log_import:371 | ✅ SUCCESS IMPORT: discord
2025-09-12 15:44:20,920 | comprehensive_test | DEBUG | log_import:372 | Import details: {
  "operation_id": "OP_000019",
  "operation_type": "IMPORT",
  "module_name": "discord",
  "success": true,
  "error": null,
  "timestamp": "2025-09-12T15:44:20.920684",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:20,921 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: MODULE_IMPORTED
2025-09-12 15:44:20,921 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000010",
  "operation_type": "OPERATION",
  "operation": "MODULE_IMPORTED",
  "details": {
    "module_name": "discord",
    "module_version": "2.6.3",
    "module_file": "/home/<USER>/Desktop/tradingview-automatio/venv/lib/python3.12/site-packages/discord/__init__.py",
    "import_duration": 0.29721689224243164
  },
  "timestamp": "2025-09-12T15:44:20.921130",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:21,382 | comprehensive_test | INFO | log_import:371 | ✅ SUCCESS IMPORT: openai
2025-09-12 15:44:21,382 | comprehensive_test | DEBUG | log_import:372 | Import details: {
  "operation_id": "OP_000021",
  "operation_type": "IMPORT",
  "module_name": "openai",
  "success": true,
  "error": null,
  "timestamp": "2025-09-12T15:44:21.382449",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:21,383 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: MODULE_IMPORTED
2025-09-12 15:44:21,383 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000010",
  "operation_type": "OPERATION",
  "operation": "MODULE_IMPORTED",
  "details": {
    "module_name": "openai",
    "module_version": "1.107.1",
    "module_file": "/home/<USER>/Desktop/tradingview-automatio/venv/lib/python3.12/site-packages/openai/__init__.py",
    "import_duration": 0.4612107276916504
  },
  "timestamp": "2025-09-12T15:44:21.383202",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:22,131 | comprehensive_test | INFO | log_import:371 | ✅ SUCCESS IMPORT: yfinance
2025-09-12 15:44:22,131 | comprehensive_test | DEBUG | log_import:372 | Import details: {
  "operation_id": "OP_000023",
  "operation_type": "IMPORT",
  "module_name": "yfinance",
  "success": true,
  "error": null,
  "timestamp": "2025-09-12T15:44:22.131360",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:22,131 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: MODULE_IMPORTED
2025-09-12 15:44:22,132 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000010",
  "operation_type": "OPERATION",
  "operation": "MODULE_IMPORTED",
  "details": {
    "module_name": "yfinance",
    "module_version": "0.2.65",
    "module_file": "/home/<USER>/Desktop/tradingview-automatio/venv/lib/python3.12/site-packages/yfinance/__init__.py",
    "import_duration": 0.7480349540710449
  },
  "timestamp": "2025-09-12T15:44:22.131956",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:22,249 | comprehensive_test | INFO | log_import:371 | ✅ SUCCESS IMPORT: supabase
2025-09-12 15:44:22,249 | comprehensive_test | DEBUG | log_import:372 | Import details: {
  "operation_id": "OP_000025",
  "operation_type": "IMPORT",
  "module_name": "supabase",
  "success": true,
  "error": null,
  "timestamp": "2025-09-12T15:44:22.249556",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:22,249 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: MODULE_IMPORTED
2025-09-12 15:44:22,249 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000010",
  "operation_type": "OPERATION",
  "operation": "MODULE_IMPORTED",
  "details": {
    "module_name": "supabase",
    "module_version": "2.18.1",
    "module_file": "/home/<USER>/Desktop/tradingview-automatio/venv/lib/python3.12/site-packages/supabase/__init__.py",
    "import_duration": 0.11747932434082031
  },
  "timestamp": "2025-09-12T15:44:22.249844",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:22,302 | comprehensive_test | INFO | log_import:371 | ✅ SUCCESS IMPORT: redis
2025-09-12 15:44:22,302 | comprehensive_test | DEBUG | log_import:372 | Import details: {
  "operation_id": "OP_000027",
  "operation_type": "IMPORT",
  "module_name": "redis",
  "success": true,
  "error": null,
  "timestamp": "2025-09-12T15:44:22.302366",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:22,303 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: MODULE_IMPORTED
2025-09-12 15:44:22,303 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000010",
  "operation_type": "OPERATION",
  "operation": "MODULE_IMPORTED",
  "details": {
    "module_name": "redis",
    "module_version": "6.4.0",
    "module_file": "/home/<USER>/Desktop/tradingview-automatio/venv/lib/python3.12/site-packages/redis/__init__.py",
    "import_duration": 0.05241680145263672
  },
  "timestamp": "2025-09-12T15:44:22.303078",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:22,310 | comprehensive_test | INFO | log_import:371 | ✅ SUCCESS IMPORT: tenacity
2025-09-12 15:44:22,310 | comprehensive_test | DEBUG | log_import:372 | Import details: {
  "operation_id": "OP_000029",
  "operation_type": "IMPORT",
  "module_name": "tenacity",
  "success": true,
  "error": null,
  "timestamp": "2025-09-12T15:44:22.310722",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:22,311 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: MODULE_IMPORTED
2025-09-12 15:44:22,311 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000010",
  "operation_type": "OPERATION",
  "operation": "MODULE_IMPORTED",
  "details": {
    "module_name": "tenacity",
    "module_version": "unknown",
    "module_file": "/home/<USER>/Desktop/tradingview-automatio/venv/lib/python3.12/site-packages/tenacity/__init__.py",
    "import_duration": 0.007498979568481445
  },
  "timestamp": "2025-09-12T15:44:22.311006",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:22,328 | comprehensive_test | INFO | log_import:371 | ✅ SUCCESS IMPORT: structlog
2025-09-12 15:44:22,328 | comprehensive_test | DEBUG | log_import:372 | Import details: {
  "operation_id": "OP_000031",
  "operation_type": "IMPORT",
  "module_name": "structlog",
  "success": true,
  "error": null,
  "timestamp": "2025-09-12T15:44:22.328350",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:22,329 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: MODULE_IMPORTED
2025-09-12 15:44:22,329 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000010",
  "operation_type": "OPERATION",
  "operation": "MODULE_IMPORTED",
  "details": {
    "module_name": "structlog",
    "module_version": "25.4.0",
    "module_file": "/home/<USER>/Desktop/tradingview-automatio/venv/lib/python3.12/site-packages/structlog/__init__.py",
    "import_duration": 0.01726078987121582
  },
  "timestamp": "2025-09-12T15:44:22.329230",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:22,329 | comprehensive_test | INFO | log_import:371 | ✅ SUCCESS IMPORT: psutil
2025-09-12 15:44:22,329 | comprehensive_test | DEBUG | log_import:372 | Import details: {
  "operation_id": "OP_000033",
  "operation_type": "IMPORT",
  "module_name": "psutil",
  "success": true,
  "error": null,
  "timestamp": "2025-09-12T15:44:22.329390",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:22,329 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: MODULE_IMPORTED
2025-09-12 15:44:22,329 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000010",
  "operation_type": "OPERATION",
  "operation": "MODULE_IMPORTED",
  "details": {
    "module_name": "psutil",
    "module_version": "7.0.0",
    "module_file": "/home/<USER>/Desktop/tradingview-automatio/venv/lib/python3.12/site-packages/psutil/__init__.py",
    "import_duration": 5.4836273193359375e-06
  },
  "timestamp": "2025-09-12T15:44:22.329595",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:22,329 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: IMPORT_VALIDATION_COMPLETE
2025-09-12 15:44:22,329 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000010",
  "operation_type": "OPERATION",
  "operation": "IMPORT_VALIDATION_COMPLETE",
  "details": {
    "successful_imports": 12,
    "failed_imports": 0,
    "total_modules": 12,
    "success_rate": 100.0
  },
  "timestamp": "2025-09-12T15:44:22.329685",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:22,329 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: UNIT_TESTS
2025-09-12 15:44:22,330 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000036",
  "operation_type": "OPERATION",
  "operation": "UNIT_TESTS",
  "details": {},
  "timestamp": "2025-09-12T15:44:22.329876",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:22,330 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: UNIT_TESTS_START
2025-09-12 15:44:22,330 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000036",
  "operation_type": "OPERATION",
  "operation": "UNIT_TESTS_START",
  "details": {
    "test_directory": "tests/unit",
    "expected_tests": [
      "test_ml_sentiment_analyzer",
      "test_options_greeks_calculator",
      "test_strategy_calculator"
    ]
  },
  "timestamp": "2025-09-12T15:44:22.330069",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:28,997 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: UNIT_TESTS_EXECUTION
2025-09-12 15:44:28,997 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000036",
  "operation_type": "OPERATION",
  "operation": "UNIT_TESTS_EXECUTION",
  "details": {
    "command": "pytest tests/unit -v --tb=long",
    "return_code": 1,
    "duration": 6.666739463806152,
    "stdout_length": 49162,
    "stderr_length": 586
  },
  "timestamp": "2025-09-12T15:44:28.996965",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:28,998 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: PARSE_PYTEST_OUTPUT_UNIT_TESTS
2025-09-12 15:44:28,998 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000039",
  "operation_type": "OPERATION",
  "operation": "PARSE_PYTEST_OUTPUT_UNIT_TESTS",
  "details": {},
  "timestamp": "2025-09-12T15:44:28.998503",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:28,998 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INDIVIDUAL_TEST_RESULT
2025-09-12 15:44:28,998 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000039",
  "operation_type": "OPERATION",
  "operation": "INDIVIDUAL_TEST_RESULT",
  "details": {
    "test_type": "UNIT_TESTS",
    "test_file": "tests/unit/test_options_greeks_calculator.py",
    "test_name": "test_estimate_new_delta PASSED",
    "status": "PASSED",
    "full_line": "tests/unit/test_options_greeks_calculator.py::test_estimate_new_delta PASSED"
  },
  "timestamp": "2025-09-12T15:44:28.998813",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:28,998 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INDIVIDUAL_TEST_RESULT
2025-09-12 15:44:28,999 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000039",
  "operation_type": "OPERATION",
  "operation": "INDIVIDUAL_TEST_RESULT",
  "details": {
    "test_type": "UNIT_TESTS",
    "test_file": "tests/unit/test_options_greeks_calculator.py",
    "test_name": "test_estimate_new_price PASSED",
    "status": "PASSED",
    "full_line": "tests/unit/test_options_greeks_calculator.py::test_estimate_new_price PASSED"
  },
  "timestamp": "2025-09-12T15:44:28.998983",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:28,999 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INDIVIDUAL_TEST_RESULT
2025-09-12 15:44:28,999 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000039",
  "operation_type": "OPERATION",
  "operation": "INDIVIDUAL_TEST_RESULT",
  "details": {
    "test_type": "UNIT_TESTS",
    "test_file": "tests/unit/test_options_greeks_calculator.py",
    "test_name": "test_breakeven_calculation PASSED",
    "status": "PASSED",
    "full_line": "tests/unit/test_options_greeks_calculator.py::test_breakeven_calculation PASSED"
  },
  "timestamp": "2025-09-12T15:44:28.999096",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:28,999 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INDIVIDUAL_TEST_RESULT
2025-09-12 15:44:28,999 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000039",
  "operation_type": "OPERATION",
  "operation": "INDIVIDUAL_TEST_RESULT",
  "details": {
    "test_type": "UNIT_TESTS",
    "test_file": "tests/unit/test_options_greeks_calculator.py",
    "test_name": "test_probability_of_profit PASSED",
    "status": "PASSED",
    "full_line": "tests/unit/test_options_greeks_calculator.py::test_probability_of_profit PASSED"
  },
  "timestamp": "2025-09-12T15:44:28.999201",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:28,999 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INDIVIDUAL_TEST_RESULT
2025-09-12 15:44:28,999 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000039",
  "operation_type": "OPERATION",
  "operation": "INDIVIDUAL_TEST_RESULT",
  "details": {
    "test_type": "UNIT_TESTS",
    "test_file": "tests/unit/test_options_greeks_calculator.py",
    "test_name": "test_time_decay_effect PASSED",
    "status": "PASSED",
    "full_line": "tests/unit/test_options_greeks_calculator.py::test_time_decay_effect PASSED"
  },
  "timestamp": "2025-09-12T15:44:28.999310",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:28,999 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INDIVIDUAL_TEST_RESULT
2025-09-12 15:44:28,999 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000039",
  "operation_type": "OPERATION",
  "operation": "INDIVIDUAL_TEST_RESULT",
  "details": {
    "test_type": "UNIT_TESTS",
    "test_file": "tests/unit/test_options_greeks_calculator.py",
    "test_name": "test_volatility_effect PASSED",
    "status": "PASSED",
    "full_line": "tests/unit/test_options_greeks_calculator.py::test_volatility_effect PASSED"
  },
  "timestamp": "2025-09-12T15:44:28.999409",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:28,999 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INDIVIDUAL_TEST_RESULT
2025-09-12 15:44:28,999 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000039",
  "operation_type": "OPERATION",
  "operation": "INDIVIDUAL_TEST_RESULT",
  "details": {
    "test_type": "UNIT_TESTS",
    "test_file": "tests/unit/test_strategy_calculator.py",
    "test_name": "test_calculate_entry_strategy PASSED",
    "status": "PASSED",
    "full_line": "tests/unit/test_strategy_calculator.py::test_calculate_entry_strategy PASSED"
  },
  "timestamp": "2025-09-12T15:44:28.999512",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:28,999 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INDIVIDUAL_TEST_RESULT
2025-09-12 15:44:28,999 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000039",
  "operation_type": "OPERATION",
  "operation": "INDIVIDUAL_TEST_RESULT",
  "details": {
    "test_type": "UNIT_TESTS",
    "test_file": "tests/unit/test_strategy_calculator.py",
    "test_name": "test_estimate_options_strategy PASSED",
    "status": "PASSED",
    "full_line": "tests/unit/test_strategy_calculator.py::test_estimate_options_strategy PASSED"
  },
  "timestamp": "2025-09-12T15:44:28.999608",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:28,999 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INDIVIDUAL_TEST_RESULT
2025-09-12 15:44:28,999 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000039",
  "operation_type": "OPERATION",
  "operation": "INDIVIDUAL_TEST_RESULT",
  "details": {
    "test_type": "UNIT_TESTS",
    "test_file": "tests/unit/test_strategy_calculator.py",
    "test_name": "test_risk_classification PASSED",
    "status": "PASSED",
    "full_line": "tests/unit/test_strategy_calculator.py::test_risk_classification PASSED"
  },
  "timestamp": "2025-09-12T15:44:28.999701",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:28,999 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INDIVIDUAL_TEST_RESULT
2025-09-12 15:44:28,999 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000039",
  "operation_type": "OPERATION",
  "operation": "INDIVIDUAL_TEST_RESULT",
  "details": {
    "test_type": "UNIT_TESTS",
    "test_file": "tests/unit/test_strategy_calculator.py",
    "test_name": "test_stop_loss_calculation PASSED",
    "status": "PASSED",
    "full_line": "tests/unit/test_strategy_calculator.py::test_stop_loss_calculation PASSED"
  },
  "timestamp": "2025-09-12T15:44:28.999796",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:28,999 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INDIVIDUAL_TEST_RESULT
2025-09-12 15:44:28,999 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000039",
  "operation_type": "OPERATION",
  "operation": "INDIVIDUAL_TEST_RESULT",
  "details": {
    "test_type": "UNIT_TESTS",
    "test_file": "tests/unit/test_strategy_calculator.py",
    "test_name": "test_take_profit_calculation PASSED",
    "status": "PASSED",
    "full_line": "tests/unit/test_strategy_calculator.py::test_take_profit_calculation PASSED"
  },
  "timestamp": "2025-09-12T15:44:28.999894",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:29,000 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INDIVIDUAL_TEST_RESULT
2025-09-12 15:44:29,000 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000039",
  "operation_type": "OPERATION",
  "operation": "INDIVIDUAL_TEST_RESULT",
  "details": {
    "test_type": "UNIT_TESTS",
    "test_file": "tests/unit/test_strategy_calculator.py",
    "test_name": "test_supertrend_integration PASSED",
    "status": "PASSED",
    "full_line": "tests/unit/test_strategy_calculator.py::test_supertrend_integration PASSED"
  },
  "timestamp": "2025-09-12T15:44:29.000002",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:29,000 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INDIVIDUAL_TEST_RESULT
2025-09-12 15:44:29,000 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000039",
  "operation_type": "OPERATION",
  "operation": "INDIVIDUAL_TEST_RESULT",
  "details": {
    "test_type": "UNIT_TESTS",
    "test_file": "tests/unit/test_strategy_calculator.py",
    "test_name": "test_supertrend_flip_tracking PASSED",
    "status": "PASSED",
    "full_line": "tests/unit/test_strategy_calculator.py::test_supertrend_flip_tracking PASSED"
  },
  "timestamp": "2025-09-12T15:44:29.000095",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:29,000 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INDIVIDUAL_TEST_RESULT
2025-09-12 15:44:29,000 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000039",
  "operation_type": "OPERATION",
  "operation": "INDIVIDUAL_TEST_RESULT",
  "details": {
    "test_type": "UNIT_TESTS",
    "test_file": "tests/unit/test_strategy_calculator.py",
    "test_name": "test_supertrend_logging PASSED",
    "status": "PASSED",
    "full_line": "tests/unit/test_strategy_calculator.py::test_supertrend_logging PASSED"
  },
  "timestamp": "2025-09-12T15:44:29.000188",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:29,000 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INDIVIDUAL_TEST_RESULT
2025-09-12 15:44:29,000 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000039",
  "operation_type": "OPERATION",
  "operation": "INDIVIDUAL_TEST_RESULT",
  "details": {
    "test_type": "UNIT_TESTS",
    "test_file": "FAILED tests/unit/test_ml_sentiment_analyzer.py",
    "test_name": "test_negative_sentiment_analysis - AssertionError: assert 'neutral' == 'negative'",
    "status": "FAILED",
    "full_line": "FAILED tests/unit/test_ml_sentiment_analyzer.py::test_negative_sentiment_analysis - AssertionError: assert 'neutral' == 'negative'"
  },
  "timestamp": "2025-09-12T15:44:29.000504",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:29,000 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INDIVIDUAL_TEST_RESULT
2025-09-12 15:44:29,000 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000039",
  "operation_type": "OPERATION",
  "operation": "INDIVIDUAL_TEST_RESULT",
  "details": {
    "test_type": "UNIT_TESTS",
    "test_file": "FAILED tests/unit/test_ml_sentiment_analyzer.py",
    "test_name": "test_sentiment_extraction - AssertionError: assert 'risks' in ['weak']",
    "status": "FAILED",
    "full_line": "FAILED tests/unit/test_ml_sentiment_analyzer.py::test_sentiment_extraction - AssertionError: assert 'risks' in ['weak']"
  },
  "timestamp": "2025-09-12T15:44:29.000619",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:29,000 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: PYTEST_OUTPUT_PARSED_UNIT_TESTS
2025-09-12 15:44:29,000 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000039",
  "operation_type": "OPERATION",
  "operation": "PYTEST_OUTPUT_PARSED_UNIT_TESTS",
  "details": {
    "total_tests": 16,
    "passed": 14,
    "failed": 2,
    "errors": 0,
    "success_rate": 87.5,
    "test_results": [
      {
        "test_file": "tests/unit/test_options_greeks_calculator.py",
        "test_name": "test_estimate_new_delta PASSED",
        "status": "PASSED",
        "line": "tests/unit/test_options_greeks_calculator.py::test_estimate_new_delta PASSED"
      },
      {
        "test_file": "tests/unit/test_options_greeks_calculator.py",
        "test_name": "test_estimate_new_price PASSED",
        "status": "PASSED",
        "line": "tests/unit/test_options_greeks_calculator.py::test_estimate_new_price PASSED"
      },
      {
        "test_file": "tests/unit/test_options_greeks_calculator.py",
        "test_name": "test_breakeven_calculation PASSED",
        "status": "PASSED",
        "line": "tests/unit/test_options_greeks_calculator.py::test_breakeven_calculation PASSED"
      },
      {
        "test_file": "tests/unit/test_options_greeks_calculator.py",
        "test_name": "test_probability_of_profit PASSED",
        "status": "PASSED",
        "line": "tests/unit/test_options_greeks_calculator.py::test_probability_of_profit PASSED"
      },
      {
        "test_file": "tests/unit/test_options_greeks_calculator.py",
        "test_name": "test_time_decay_effect PASSED",
        "status": "PASSED",
        "line": "tests/unit/test_options_greeks_calculator.py::test_time_decay_effect PASSED"
      },
      {
        "test_file": "tests/unit/test_options_greeks_calculator.py",
        "test_name": "test_volatility_effect PASSED",
        "status": "PASSED",
        "line": "tests/unit/test_options_greeks_calculator.py::test_volatility_effect PASSED"
      },
      {
        "test_file": "tests/unit/test_strategy_calculator.py",
        "test_name": "test_calculate_entry_strategy PASSED",
        "status": "PASSED",
        "line": "tests/unit/test_strategy_calculator.py::test_calculate_entry_strategy PASSED"
      },
      {
        "test_file": "tests/unit/test_strategy_calculator.py",
        "test_name": "test_estimate_options_strategy PASSED",
        "status": "PASSED",
        "line": "tests/unit/test_strategy_calculator.py::test_estimate_options_strategy PASSED"
      },
      {
        "test_file": "tests/unit/test_strategy_calculator.py",
        "test_name": "test_risk_classification PASSED",
        "status": "PASSED",
        "line": "tests/unit/test_strategy_calculator.py::test_risk_classification PASSED"
      },
      {
        "test_file": "tests/unit/test_strategy_calculator.py",
        "test_name": "test_stop_loss_calculation PASSED",
        "status": "PASSED",
        "line": "tests/unit/test_strategy_calculator.py::test_stop_loss_calculation PASSED"
      },
      {
        "test_file": "tests/unit/test_strategy_calculator.py",
        "test_name": "test_take_profit_calculation PASSED",
        "status": "PASSED",
        "line": "tests/unit/test_strategy_calculator.py::test_take_profit_calculation PASSED"
      },
      {
        "test_file": "tests/unit/test_strategy_calculator.py",
        "test_name": "test_supertrend_integration PASSED",
        "status": "PASSED",
        "line": "tests/unit/test_strategy_calculator.py::test_supertrend_integration PASSED"
      },
      {
        "test_file": "tests/unit/test_strategy_calculator.py",
        "test_name": "test_supertrend_flip_tracking PASSED",
        "status": "PASSED",
        "line": "tests/unit/test_strategy_calculator.py::test_supertrend_flip_tracking PASSED"
      },
      {
        "test_file": "tests/unit/test_strategy_calculator.py",
        "test_name": "test_supertrend_logging PASSED",
        "status": "PASSED",
        "line": "tests/unit/test_strategy_calculator.py::test_supertrend_logging PASSED"
      },
      {
        "test_file": "FAILED tests/unit/test_ml_sentiment_analyzer.py",
        "test_name": "test_negative_sentiment_analysis - AssertionError: assert 'neutral' == 'negative'",
        "status": "FAILED",
        "line": "FAILED tests/unit/test_ml_sentiment_analyzer.py::test_negative_sentiment_analysis - AssertionError: assert 'neutral' == 'negative'"
      },
      {
        "test_file": "FAILED tests/unit/test_ml_sentiment_analyzer.py",
        "test_name": "test_sentiment_extraction - AssertionError: assert 'risks' in ['weak']",
        "status": "FAILED",
        "line": "FAILED tests/unit/test_ml_sentiment_analyzer.py::test_sentiment_extraction - AssertionError: assert 'risks' in ['weak']"
      }
    ]
  },
  "timestamp": "2025-09-12T15:44:29.000730",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:29,003 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INTEGRATION_TESTS
2025-09-12 15:44:29,003 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000043",
  "operation_type": "OPERATION",
  "operation": "INTEGRATION_TESTS",
  "details": {},
  "timestamp": "2025-09-12T15:44:29.002986",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:29,003 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INTEGRATION_TESTS_START
2025-09-12 15:44:29,003 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000043",
  "operation_type": "OPERATION",
  "operation": "INTEGRATION_TESTS_START",
  "details": {
    "test_directory": "tests/integration",
    "expected_tests": [
      "test_alpha_vantage_provider",
      "test_market_data_service",
      "test_polygon_provider"
    ]
  },
  "timestamp": "2025-09-12T15:44:29.003119",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:38,131 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INTEGRATION_TESTS_EXECUTION
2025-09-12 15:44:38,131 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000043",
  "operation_type": "OPERATION",
  "operation": "INTEGRATION_TESTS_EXECUTION",
  "details": {
    "command": "pytest tests/integration -v --tb=long",
    "return_code": 2,
    "duration": 9.127760171890259
  },
  "timestamp": "2025-09-12T15:44:38.131015",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:38,132 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: PARSE_PYTEST_OUTPUT_INTEGRATION_TESTS
2025-09-12 15:44:38,132 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000045",
  "operation_type": "OPERATION",
  "operation": "PARSE_PYTEST_OUTPUT_INTEGRATION_TESTS",
  "details": {},
  "timestamp": "2025-09-12T15:44:38.132102",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:38,132 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: PYTEST_OUTPUT_PARSED_INTEGRATION_TESTS
2025-09-12 15:44:38,132 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000045",
  "operation_type": "OPERATION",
  "operation": "PYTEST_OUTPUT_PARSED_INTEGRATION_TESTS",
  "details": {
    "total_tests": 0,
    "passed": 0,
    "failed": 0,
    "errors": 0,
    "success_rate": 0,
    "test_results": []
  },
  "timestamp": "2025-09-12T15:44:38.132321",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:38,133 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: COMPREHENSIVE_TESTS
2025-09-12 15:44:38,133 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000049",
  "operation_type": "OPERATION",
  "operation": "COMPREHENSIVE_TESTS",
  "details": {},
  "timestamp": "2025-09-12T15:44:38.133052",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:38,133 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: COMPREHENSIVE_TESTS_START
2025-09-12 15:44:38,133 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000049",
  "operation_type": "OPERATION",
  "operation": "COMPREHENSIVE_TESTS_START",
  "details": {
    "test_file": "tests/test_comprehensive.py",
    "expected_tests": [
      "TestRateLimiter",
      "TestErrorHandler",
      "TestWatchlistManager",
      "TestPerformanceOptimizer"
    ]
  },
  "timestamp": "2025-09-12T15:44:38.133158",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:49,602 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: COMPREHENSIVE_TESTS_EXECUTION
2025-09-12 15:44:49,602 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000049",
  "operation_type": "OPERATION",
  "operation": "COMPREHENSIVE_TESTS_EXECUTION",
  "details": {
    "command": "pytest tests/test_comprehensive.py -v --tb=long",
    "return_code": 1,
    "duration": 11.468727350234985
  },
  "timestamp": "2025-09-12T15:44:49.601997",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:49,603 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: PARSE_PYTEST_OUTPUT_COMPREHENSIVE_TESTS
2025-09-12 15:44:49,603 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000052",
  "operation_type": "OPERATION",
  "operation": "PARSE_PYTEST_OUTPUT_COMPREHENSIVE_TESTS",
  "details": {},
  "timestamp": "2025-09-12T15:44:49.603610",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:49,604 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INDIVIDUAL_TEST_RESULT
2025-09-12 15:44:49,604 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000052",
  "operation_type": "OPERATION",
  "operation": "INDIVIDUAL_TEST_RESULT",
  "details": {
    "test_type": "COMPREHENSIVE_TESTS",
    "test_file": "tests/test_comprehensive.py",
    "test_name": "TestErrorHandler",
    "status": "PASSED",
    "full_line": "tests/test_comprehensive.py::TestErrorHandler::test_user_friendly_messages PASSED"
  },
  "timestamp": "2025-09-12T15:44:49.604073",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:49,604 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INDIVIDUAL_TEST_RESULT
2025-09-12 15:44:49,604 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000052",
  "operation_type": "OPERATION",
  "operation": "INDIVIDUAL_TEST_RESULT",
  "details": {
    "test_type": "COMPREHENSIVE_TESTS",
    "test_file": "tests/test_comprehensive.py",
    "test_name": "TestWatchlistManager",
    "status": "ERROR",
    "full_line": "tests/test_comprehensive.py::TestWatchlistManager::test_watchlist_initialization ERROR"
  },
  "timestamp": "2025-09-12T15:44:49.604243",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:49,604 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INDIVIDUAL_TEST_RESULT
2025-09-12 15:44:49,604 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000052",
  "operation_type": "OPERATION",
  "operation": "INDIVIDUAL_TEST_RESULT",
  "details": {
    "test_type": "COMPREHENSIVE_TESTS",
    "test_file": "tests/test_comprehensive.py",
    "test_name": "TestWatchlistManager",
    "status": "ERROR",
    "full_line": "tests/test_comprehensive.py::TestWatchlistManager::test_create_watchlist ERROR"
  },
  "timestamp": "2025-09-12T15:44:49.604366",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:49,604 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INDIVIDUAL_TEST_RESULT
2025-09-12 15:44:49,604 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000052",
  "operation_type": "OPERATION",
  "operation": "INDIVIDUAL_TEST_RESULT",
  "details": {
    "test_type": "COMPREHENSIVE_TESTS",
    "test_file": "tests/test_comprehensive.py",
    "test_name": "TestDiscordUX",
    "status": "PASSED",
    "full_line": "tests/test_comprehensive.py::TestDiscordUX::test_interactive_embeds PASSED"
  },
  "timestamp": "2025-09-12T15:44:49.604507",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:49,604 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INDIVIDUAL_TEST_RESULT
2025-09-12 15:44:49,604 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000052",
  "operation_type": "OPERATION",
  "operation": "INDIVIDUAL_TEST_RESULT",
  "details": {
    "test_type": "COMPREHENSIVE_TESTS",
    "test_file": "tests/test_comprehensive.py",
    "test_name": "TestAdvancedSecurity",
    "status": "FAILED",
    "full_line": "tests/test_comprehensive.py::TestAdvancedSecurity::test_input_validation FAILED"
  },
  "timestamp": "2025-09-12T15:44:49.604642",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:49,604 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INDIVIDUAL_TEST_RESULT
2025-09-12 15:44:49,604 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000052",
  "operation_type": "OPERATION",
  "operation": "INDIVIDUAL_TEST_RESULT",
  "details": {
    "test_type": "COMPREHENSIVE_TESTS",
    "test_file": "tests/test_comprehensive.py",
    "test_name": "TestAdvancedSecurity",
    "status": "PASSED",
    "full_line": "tests/test_comprehensive.py::TestAdvancedSecurity::test_symbol_validation PASSED"
  },
  "timestamp": "2025-09-12T15:44:49.604761",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:49,605 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INDIVIDUAL_TEST_RESULT
2025-09-12 15:44:49,605 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000052",
  "operation_type": "OPERATION",
  "operation": "INDIVIDUAL_TEST_RESULT",
  "details": {
    "test_type": "COMPREHENSIVE_TESTS",
    "test_file": "FAILED tests/test_comprehensive.py",
    "test_name": "TestAdvancedSecurity",
    "status": "FAILED",
    "full_line": "FAILED tests/test_comprehensive.py::TestAdvancedSecurity::test_input_validation - AssertionError: assert 'injection' in 'input contains potentially dangerous content'"
  },
  "timestamp": "2025-09-12T15:44:49.604997",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:49,605 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INDIVIDUAL_TEST_RESULT
2025-09-12 15:44:49,605 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000052",
  "operation_type": "OPERATION",
  "operation": "INDIVIDUAL_TEST_RESULT",
  "details": {
    "test_type": "COMPREHENSIVE_TESTS",
    "test_file": "ERROR tests/test_comprehensive.py",
    "test_name": "TestWatchlistManager",
    "status": "ERROR",
    "full_line": "ERROR tests/test_comprehensive.py::TestWatchlistManager::test_watchlist_initialization - AttributeError: __aenter__"
  },
  "timestamp": "2025-09-12T15:44:49.605156",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:49,605 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INDIVIDUAL_TEST_RESULT
2025-09-12 15:44:49,605 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000052",
  "operation_type": "OPERATION",
  "operation": "INDIVIDUAL_TEST_RESULT",
  "details": {
    "test_type": "COMPREHENSIVE_TESTS",
    "test_file": "ERROR tests/test_comprehensive.py",
    "test_name": "TestWatchlistManager",
    "status": "ERROR",
    "full_line": "ERROR tests/test_comprehensive.py::TestWatchlistManager::test_create_watchlist - AttributeError: __aenter__"
  },
  "timestamp": "2025-09-12T15:44:49.605281",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:49,605 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: PYTEST_OUTPUT_PARSED_COMPREHENSIVE_TESTS
2025-09-12 15:44:49,605 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000052",
  "operation_type": "OPERATION",
  "operation": "PYTEST_OUTPUT_PARSED_COMPREHENSIVE_TESTS",
  "details": {
    "total_tests": 9,
    "passed": 3,
    "failed": 2,
    "errors": 4,
    "success_rate": 33.33333333333333,
    "test_results": [
      {
        "test_file": "tests/test_comprehensive.py",
        "test_name": "TestErrorHandler",
        "status": "PASSED",
        "line": "tests/test_comprehensive.py::TestErrorHandler::test_user_friendly_messages PASSED"
      },
      {
        "test_file": "tests/test_comprehensive.py",
        "test_name": "TestWatchlistManager",
        "status": "ERROR",
        "line": "tests/test_comprehensive.py::TestWatchlistManager::test_watchlist_initialization ERROR"
      },
      {
        "test_file": "tests/test_comprehensive.py",
        "test_name": "TestWatchlistManager",
        "status": "ERROR",
        "line": "tests/test_comprehensive.py::TestWatchlistManager::test_create_watchlist ERROR"
      },
      {
        "test_file": "tests/test_comprehensive.py",
        "test_name": "TestDiscordUX",
        "status": "PASSED",
        "line": "tests/test_comprehensive.py::TestDiscordUX::test_interactive_embeds PASSED"
      },
      {
        "test_file": "tests/test_comprehensive.py",
        "test_name": "TestAdvancedSecurity",
        "status": "FAILED",
        "line": "tests/test_comprehensive.py::TestAdvancedSecurity::test_input_validation FAILED"
      },
      {
        "test_file": "tests/test_comprehensive.py",
        "test_name": "TestAdvancedSecurity",
        "status": "PASSED",
        "line": "tests/test_comprehensive.py::TestAdvancedSecurity::test_symbol_validation PASSED"
      },
      {
        "test_file": "FAILED tests/test_comprehensive.py",
        "test_name": "TestAdvancedSecurity",
        "status": "FAILED",
        "line": "FAILED tests/test_comprehensive.py::TestAdvancedSecurity::test_input_validation - AssertionError: assert 'injection' in 'input contains potentially dangerous content'"
      },
      {
        "test_file": "ERROR tests/test_comprehensive.py",
        "test_name": "TestWatchlistManager",
        "status": "ERROR",
        "line": "ERROR tests/test_comprehensive.py::TestWatchlistManager::test_watchlist_initialization - AttributeError: __aenter__"
      },
      {
        "test_file": "ERROR tests/test_comprehensive.py",
        "test_name": "TestWatchlistManager",
        "status": "ERROR",
        "line": "ERROR tests/test_comprehensive.py::TestWatchlistManager::test_create_watchlist - AttributeError: __aenter__"
      }
    ]
  },
  "timestamp": "2025-09-12T15:44:49.605420",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:49,606 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: GENERATE_FINAL_REPORT
2025-09-12 15:44:49,606 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000056",
  "operation_type": "OPERATION",
  "operation": "GENERATE_FINAL_REPORT",
  "details": {},
  "timestamp": "2025-09-12T15:44:49.606537",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:49,606 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: FINAL_REPORT_GENERATED
2025-09-12 15:44:49,607 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000056",
  "operation_type": "OPERATION",
  "operation": "FINAL_REPORT_GENERATED",
  "details": {
    "session_id": "test_session_1757706260",
    "total_duration_seconds": 29.22857975959778,
    "test_results": {
      "UNIT_TESTS": {
        "total": 16,
        "passed": 14,
        "failed": 2,
        "errors": 0,
        "results": [
          {
            "test_file": "tests/unit/test_options_greeks_calculator.py",
            "test_name": "test_estimate_new_delta PASSED",
            "status": "PASSED",
            "line": "tests/unit/test_options_greeks_calculator.py::test_estimate_new_delta PASSED"
          },
          {
            "test_file": "tests/unit/test_options_greeks_calculator.py",
            "test_name": "test_estimate_new_price PASSED",
            "status": "PASSED",
            "line": "tests/unit/test_options_greeks_calculator.py::test_estimate_new_price PASSED"
          },
          {
            "test_file": "tests/unit/test_options_greeks_calculator.py",
            "test_name": "test_breakeven_calculation PASSED",
            "status": "PASSED",
            "line": "tests/unit/test_options_greeks_calculator.py::test_breakeven_calculation PASSED"
          },
          {
            "test_file": "tests/unit/test_options_greeks_calculator.py",
            "test_name": "test_probability_of_profit PASSED",
            "status": "PASSED",
            "line": "tests/unit/test_options_greeks_calculator.py::test_probability_of_profit PASSED"
          },
          {
            "test_file": "tests/unit/test_options_greeks_calculator.py",
            "test_name": "test_time_decay_effect PASSED",
            "status": "PASSED",
            "line": "tests/unit/test_options_greeks_calculator.py::test_time_decay_effect PASSED"
          },
          {
            "test_file": "tests/unit/test_options_greeks_calculator.py",
            "test_name": "test_volatility_effect PASSED",
            "status": "PASSED",
            "line": "tests/unit/test_options_greeks_calculator.py::test_volatility_effect PASSED"
          },
          {
            "test_file": "tests/unit/test_strategy_calculator.py",
            "test_name": "test_calculate_entry_strategy PASSED",
            "status": "PASSED",
            "line": "tests/unit/test_strategy_calculator.py::test_calculate_entry_strategy PASSED"
          },
          {
            "test_file": "tests/unit/test_strategy_calculator.py",
            "test_name": "test_estimate_options_strategy PASSED",
            "status": "PASSED",
            "line": "tests/unit/test_strategy_calculator.py::test_estimate_options_strategy PASSED"
          },
          {
            "test_file": "tests/unit/test_strategy_calculator.py",
            "test_name": "test_risk_classification PASSED",
            "status": "PASSED",
            "line": "tests/unit/test_strategy_calculator.py::test_risk_classification PASSED"
          },
          {
            "test_file": "tests/unit/test_strategy_calculator.py",
            "test_name": "test_stop_loss_calculation PASSED",
            "status": "PASSED",
            "line": "tests/unit/test_strategy_calculator.py::test_stop_loss_calculation PASSED"
          },
          {
            "test_file": "tests/unit/test_strategy_calculator.py",
            "test_name": "test_take_profit_calculation PASSED",
            "status": "PASSED",
            "line": "tests/unit/test_strategy_calculator.py::test_take_profit_calculation PASSED"
          },
          {
            "test_file": "tests/unit/test_strategy_calculator.py",
            "test_name": "test_supertrend_integration PASSED",
            "status": "PASSED",
            "line": "tests/unit/test_strategy_calculator.py::test_supertrend_integration PASSED"
          },
          {
            "test_file": "tests/unit/test_strategy_calculator.py",
            "test_name": "test_supertrend_flip_tracking PASSED",
            "status": "PASSED",
            "line": "tests/unit/test_strategy_calculator.py::test_supertrend_flip_tracking PASSED"
          },
          {
            "test_file": "tests/unit/test_strategy_calculator.py",
            "test_name": "test_supertrend_logging PASSED",
            "status": "PASSED",
            "line": "tests/unit/test_strategy_calculator.py::test_supertrend_logging PASSED"
          },
          {
            "test_file": "FAILED tests/unit/test_ml_sentiment_analyzer.py",
            "test_name": "test_negative_sentiment_analysis - AssertionError: assert 'neutral' == 'negative'",
            "status": "FAILED",
            "line": "FAILED tests/unit/test_ml_sentiment_analyzer.py::test_negative_sentiment_analysis - AssertionError: assert 'neutral' == 'negative'"
          },
          {
            "test_file": "FAILED tests/unit/test_ml_sentiment_analyzer.py",
            "test_name": "test_sentiment_extraction - AssertionError: assert 'risks' in ['weak']",
            "status": "FAILED",
            "line": "FAILED tests/unit/test_ml_sentiment_analyzer.py::test_sentiment_extraction - AssertionError: assert 'risks' in ['weak']"
          }
        ]
      },
      "INTEGRATION_TESTS": {
        "total": 0,
        "passed": 0,
        "failed": 0,
        "errors": 0,
        "results": []
      },
      "COMPREHENSIVE_TESTS": {
        "total": 9,
        "passed": 3,
        "failed": 2,
        "errors": 4,
        "results": [
          {
            "test_file": "tests/test_comprehensive.py",
            "test_name": "TestErrorHandler",
            "status": "PASSED",
            "line": "tests/test_comprehensive.py::TestErrorHandler::test_user_friendly_messages PASSED"
          },
          {
            "test_file": "tests/test_comprehensive.py",
            "test_name": "TestWatchlistManager",
            "status": "ERROR",
            "line": "tests/test_comprehensive.py::TestWatchlistManager::test_watchlist_initialization ERROR"
          },
          {
            "test_file": "tests/test_comprehensive.py",
            "test_name": "TestWatchlistManager",
            "status": "ERROR",
            "line": "tests/test_comprehensive.py::TestWatchlistManager::test_create_watchlist ERROR"
          },
          {
            "test_file": "tests/test_comprehensive.py",
            "test_name": "TestDiscordUX",
            "status": "PASSED",
            "line": "tests/test_comprehensive.py::TestDiscordUX::test_interactive_embeds PASSED"
          },
          {
            "test_file": "tests/test_comprehensive.py",
            "test_name": "TestAdvancedSecurity",
            "status": "FAILED",
            "line": "tests/test_comprehensive.py::TestAdvancedSecurity::test_input_validation FAILED"
          },
          {
            "test_file": "tests/test_comprehensive.py",
            "test_name": "TestAdvancedSecurity",
            "status": "PASSED",
            "line": "tests/test_comprehensive.py::TestAdvancedSecurity::test_symbol_validation PASSED"
          },
          {
            "test_file": "FAILED tests/test_comprehensive.py",
            "test_name": "TestAdvancedSecurity",
            "status": "FAILED",
            "line": "FAILED tests/test_comprehensive.py::TestAdvancedSecurity::test_input_validation - AssertionError: assert 'injection' in 'input contains potentially dangerous content'"
          },
          {
            "test_file": "ERROR tests/test_comprehensive.py",
            "test_name": "TestWatchlistManager",
            "status": "ERROR",
            "line": "ERROR tests/test_comprehensive.py::TestWatchlistManager::test_watchlist_initialization - AttributeError: __aenter__"
          },
          {
            "test_file": "ERROR tests/test_comprehensive.py",
            "test_name": "TestWatchlistManager",
            "status": "ERROR",
            "line": "ERROR tests/test_comprehensive.py::TestWatchlistManager::test_create_watchlist - AttributeError: __aenter__"
          }
        ]
      }
    },
    "import_errors": [],
    "test_errors": [
      {
        "test_type": "unit_tests",
        "error": "log_performance_metric() takes 2 positional arguments but 3 were given",
        "timestamp": "2025-09-12T15:44:29.002766"
      },
      {
        "test_type": "integration_tests",
        "error": "log_performance_metric() takes 2 positional arguments but 3 were given",
        "timestamp": "2025-09-12T15:44:38.132872"
      },
      {
        "test_type": "comprehensive_tests",
        "error": "log_performance_metric() takes 2 positional arguments but 3 were given",
        "timestamp": "2025-09-12T15:44:49.606372"
      }
    ],
    "summary": {
      "total_tests": 25,
      "total_passed": 17,
      "total_failed": 4,
      "total_errors": 4,
      "overall_success_rate": 68.0
    },
    "timestamp": "2025-09-12T15:44:49.606678"
  },
  "timestamp": "2025-09-12T15:44:49.606686",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:49,612 | comprehensive_test | INFO | generate_summary_report:455 | 📊 TEST SUMMARY GENERATED: test_results/detailed_logs/test_summary_test_session_1757706260.json
2025-09-12 15:44:49,612 | comprehensive_test | INFO | generate_summary_report:456 | Total operations: 58
2025-09-12 15:44:49,612 | comprehensive_test | INFO | generate_summary_report:457 | Total duration: 29.232 seconds
2025-09-12 15:44:49,612 | comprehensive_test | INFO | generate_summary_report:458 | Total errors: 3
