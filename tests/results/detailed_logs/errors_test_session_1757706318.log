2025-09-12 15:45:18,705 | test_errors | ERROR | log_error:323 | 💥 ERROR: TypeError - log_test_data() takes from 2 to 3 positional arguments but 4 were given
2025-09-12 15:45:18,705 | test_errors | DEBUG | log_error:324 | Error details: {
  "operation_id": "OP_000003",
  "operation_type": "ERROR",
  "error_type": "TypeError",
  "error_message": "log_test_data() takes from 2 to 3 positional arguments but 4 were given",
  "error_traceback": "Traceback (most recent call last):\n  File \"/home/<USER>/Desktop/tradingview-automatio/test_results/detailed_logs/comprehensive_test_logger.py\", line 513, in log_operation\n    yield operation_id\n  File \"<string>\", line 12, in <module>\nTypeError: log_test_data() takes from 2 to 3 positional arguments but 4 were given\n",
  "context": {
    "operation": "SINGLE_TEST_DEMO",
    "operation_id": "OP_000001"
  },
  "timestamp": "2025-09-12T15:45:18.705287",
  "session_id": "test_session_1757706318"
}
