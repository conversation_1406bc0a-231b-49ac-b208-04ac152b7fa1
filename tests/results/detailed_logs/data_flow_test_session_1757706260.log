2025-09-12 15:44:28,997 | data_flow | INFO | log_data_flow:222 | 📊 DATA FLOW: PYTEST → STDOUT (UNIT_TESTS_OUTPUT)
2025-09-12 15:44:28,997 | data_flow | DEBUG | log_data_flow:223 | Data flow details: {
  "operation_id": "OP_000037",
  "operation_type": "DATA_FLOW",
  "source": "PYTEST",
  "destination": "STDOUT",
  "operation": "UNIT_TESTS_OUTPUT",
  "data_type": "str",
  "data_size": 49162,
  "data_preview": "============================= test session starts ==============================\nplatform linux -- Python 3.12.3, pytest-8.4.2, pluggy-1.6.0 -- /home/<USER>/Desktop/tradingview-automatio/venv/bin/python\ncachedir: .pytest_cache\nrootdir: /home/<USER>/Desktop/tradingview-automatio\nconfigfile: pytest.ini\nplugins: asyncio-1.2.0, cov-7.0.0, anyio-4.10.0\nasyncio: mode=Mode.AUTO, debug=False, asyncio_default_fixture_loop_scope=None, asyncio_default_test_loop_scope=function\ncollecting ... \n--------------------...",
  "timestamp": "2025-09-12T15:44:28.997536",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:28,998 | data_flow | INFO | log_data_flow:222 | 📊 DATA FLOW: PYTEST → STDERR (UNIT_TESTS_ERRORS)
2025-09-12 15:44:28,998 | data_flow | DEBUG | log_data_flow:223 | Data flow details: {
  "operation_id": "OP_000038",
  "operation_type": "DATA_FLOW",
  "source": "PYTEST",
  "destination": "STDERR",
  "operation": "UNIT_TESTS_ERRORS",
  "data_type": "str",
  "data_size": 586,
  "data_preview": "/home/<USER>/Desktop/tradingview-automatio/venv/lib/python3.12/site-packages/coverage/report_core.py:107: CoverageWarning: Couldn't parse Python file '/home/<USER>/Desktop/tradingview-automatio/src/core/automation/report_engine.py' (couldnt-parse)\n  coverage._warn(msg, slug=\"couldnt-parse\")\n/home/<USER>/Desktop/tradingview-automatio/venv/lib/python3.12/site-packages/coverage/report_core.py:107: CoverageWarning: Couldn't parse Python file '/home/<USER>/Desktop/tradingview-automatio/src/shared/background/task...",
  "timestamp": "2025-09-12T15:44:28.998203",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:38,131 | data_flow | INFO | log_data_flow:222 | 📊 DATA FLOW: PYTEST → STDOUT (INTEGRATION_TESTS_OUTPUT)
2025-09-12 15:44:38,131 | data_flow | DEBUG | log_data_flow:223 | Data flow details: {
  "operation_id": "OP_000044",
  "operation_type": "DATA_FLOW",
  "source": "PYTEST",
  "destination": "STDOUT",
  "operation": "INTEGRATION_TESTS_OUTPUT",
  "data_type": "str",
  "data_size": 19562,
  "data_preview": "============================= test session starts ==============================\nplatform linux -- Python 3.12.3, pytest-8.4.2, pluggy-1.6.0 -- /home/<USER>/Desktop/tradingview-automatio/venv/bin/python\ncachedir: .pytest_cache\nrootdir: /home/<USER>/Desktop/tradingview-automatio\nconfigfile: pytest.ini\nplugins: asyncio-1.2.0, cov-7.0.0, anyio-4.10.0\nasyncio: mode=Mode.AUTO, debug=False, asyncio_default_fixture_loop_scope=None, asyncio_default_test_loop_scope=function\ncollecting ... \n--------------------...",
  "timestamp": "2025-09-12T15:44:38.131468",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:49,602 | data_flow | INFO | log_data_flow:222 | 📊 DATA FLOW: PYTEST → STDOUT (COMPREHENSIVE_TESTS_OUTPUT)
2025-09-12 15:44:49,602 | data_flow | DEBUG | log_data_flow:223 | Data flow details: {
  "operation_id": "OP_000050",
  "operation_type": "DATA_FLOW",
  "source": "PYTEST",
  "destination": "STDOUT",
  "operation": "COMPREHENSIVE_TESTS_OUTPUT",
  "data_type": "str",
  "data_size": 60879,
  "data_preview": "============================= test session starts ==============================\nplatform linux -- Python 3.12.3, pytest-8.4.2, pluggy-1.6.0 -- /home/<USER>/Desktop/tradingview-automatio/venv/bin/python\ncachedir: .pytest_cache\nrootdir: /home/<USER>/Desktop/tradingview-automatio\nconfigfile: pytest.ini\nplugins: asyncio-1.2.0, cov-7.0.0, anyio-4.10.0\nasyncio: mode=Mode.AUTO, debug=False, asyncio_default_fixture_loop_scope=None, asyncio_default_test_loop_scope=function\ncollecting ... \n--------------------...",
  "timestamp": "2025-09-12T15:44:49.602269",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:49,602 | data_flow | INFO | log_data_flow:222 | 📊 DATA FLOW: PYTEST → STDERR (COMPREHENSIVE_TESTS_ERRORS)
2025-09-12 15:44:49,603 | data_flow | DEBUG | log_data_flow:223 | Data flow details: {
  "operation_id": "OP_000051",
  "operation_type": "DATA_FLOW",
  "source": "PYTEST",
  "destination": "STDERR",
  "operation": "COMPREHENSIVE_TESTS_ERRORS",
  "data_type": "str",
  "data_size": 586,
  "data_preview": "/home/<USER>/Desktop/tradingview-automatio/venv/lib/python3.12/site-packages/coverage/report_core.py:107: CoverageWarning: Couldn't parse Python file '/home/<USER>/Desktop/tradingview-automatio/src/core/automation/report_engine.py' (couldnt-parse)\n  coverage._warn(msg, slug=\"couldnt-parse\")\n/home/<USER>/Desktop/tradingview-automatio/venv/lib/python3.12/site-packages/coverage/report_core.py:107: CoverageWarning: Couldn't parse Python file '/home/<USER>/Desktop/tradingview-automatio/src/shared/background/task...",
  "timestamp": "2025-09-12T15:44:49.602914",
  "session_id": "test_session_1757706260"
}
2025-09-12 15:44:49,608 | data_flow | INFO | log_data_flow:222 | 📊 DATA FLOW: TEST_RUNNER → REPORT_FILE (FINAL_REPORT)
2025-09-12 15:44:49,608 | data_flow | DEBUG | log_data_flow:223 | Data flow details: {
  "operation_id": "OP_000057",
  "operation_type": "DATA_FLOW",
  "source": "TEST_RUNNER",
  "destination": "REPORT_FILE",
  "operation": "FINAL_REPORT",
  "data_type": "dict",
  "data_size": 6609,
  "data_preview": "{'session_id': 'test_session_1757706260', 'total_duration_seconds': 29.22857975959778, 'test_results': {'UNIT_TESTS': {'total': 16, 'passed': 14, 'failed': 2, 'errors': 0, 'results': [{'test_file': 'tests/unit/test_options_greeks_calculator.py', 'test_name': 'test_estimate_new_delta PASSED', 'status': 'PASSED', 'line': 'tests/unit/test_options_greeks_calculator.py::test_estimate_new_delta PASSED'}, {'test_file': 'tests/unit/test_options_greeks_calculator.py', 'test_name': 'test_estimate_new_pric...",
  "timestamp": "2025-09-12T15:44:49.608358",
  "session_id": "test_session_1757706260"
}
