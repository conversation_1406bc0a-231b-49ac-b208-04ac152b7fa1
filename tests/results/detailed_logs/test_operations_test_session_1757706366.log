2025-09-12 15:46:06,456 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: COMPREHENSIVE_TEST_RUN
2025-09-12 15:46:06,457 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000001",
  "operation_type": "OPERATION",
  "operation": "COMPREHENSIVE_TEST_RUN",
  "details": {
    "start_time": "2025-09-12T15:46:06.456818"
  },
  "timestamp": "2025-09-12T15:46:06.456842",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:06,457 | comprehensive_test | INFO | log_test_start:154 | 🚀 TEST STARTED: COMPREHENSIVE_TEST_RUN
2025-09-12 15:46:06,457 | comprehensive_test | DEBUG | log_test_start:155 | Test details: {
  "operation_id": "OP_000002",
  "operation_type": "TEST_START",
  "test_name": "COMPREHENSIVE_TEST_RUN",
  "test_class": "ComprehensiveTestRunner",
  "timestamp": "2025-09-12T15:46:06.457147",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:06,457 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: ENVIRONMENT_VALIDATION
2025-09-12 15:46:06,457 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000003",
  "operation_type": "OPERATION",
  "operation": "ENVIRONMENT_VALIDATION",
  "details": {},
  "timestamp": "2025-09-12T15:46:06.457257",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:06,457 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: ENVIRONMENT_VALIDATION
2025-09-12 15:46:06,457 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000003",
  "operation_type": "OPERATION",
  "operation": "ENVIRONMENT_VALIDATION",
  "details": {
    "python_version": "3.12.3 (main, Aug 14 2025, 17:47:21) [GCC 13.3.0]",
    "python_executable": "/home/<USER>/Desktop/tradingview-automatio/venv/bin/python",
    "working_directory": "/home/<USER>/Desktop/tradingview-automatio",
    "environment_variables": {
      "SHELL": "/bin/bash",
      "SESSION_MANAGER": "local/fedora-workstation:@/tmp/.ICE-unix/1142,unix/fedora-workstation:/tmp/.ICE-unix/1142",
      "QT_ACCESSIBILITY": "1",
      "COLORTERM": "truecolor",
      "XDG_CONFIG_DIRS": "/etc/xdg/xdg-cinnamon:/etc/xdg",
      "VSCODE_DEBUGPY_ADAPTER_ENDPOINTS": "/home/<USER>/.cursor/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-e78ebade99b7b16e.txt",
      "XDG_SESSION_PATH": "/org/freedesktop/DisplayManager/Session0",
      "TERM_PROGRAM_VERSION": "1.1.6",
      "GNOME_DESKTOP_SESSION_ID": "this-is-deprecated",
      "GNOME_KEYRING_CONTROL": "/run/user/1000/keyring",
      "PERLLIB": "/tmp/.mount_cursordYGeAK/usr/share/perl5/:/tmp/.mount_cursordYGeAK/usr/lib/perl5/:",
      "LANGUAGE": "en_US",
      "SSH_AUTH_SOCK": "/run/user/1000/keyring/ssh",
      "ARGV0": "/opt/cursor/cursor.AppImage",
      "GEMINI_API_KEY": "",
      "PYDEVD_DISABLE_FILE_VALIDATION": "1",
      "DESKTOP_SESSION": "cinnamon",
      "GTK_MODULES": "gail:atk-bridge",
      "XDG_SEAT": "seat0",
      "PWD": "/home/<USER>/Desktop/tradingview-automatio",
      "GSETTINGS_SCHEMA_DIR": "/tmp/.mount_cursordYGeAK/usr/share/glib-2.0/schemas/:",
      "XDG_SESSION_DESKTOP": "cinnamon",
      "LOGNAME": "ami",
      "XDG_SESSION_TYPE": "x11",
      "GPG_AGENT_INFO": "/run/user/1000/gnupg/S.gpg-agent:0:1",
      "BUNDLED_DEBUGPY_PATH": "/home/<USER>/.cursor/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy",
      "XAUTHORITY": "/home/<USER>/.Xauthority",
      "VSCODE_GIT_ASKPASS_NODE": "/tmp/.mount_cursordYGeAK/usr/share/cursor/cursor",
      "XDG_GREETER_DATA_DIR": "/var/lib/lightdm-data/ami",
      "GDM_LANG": "en_US",
      "INSIDE_NEMO_PYTHON": "",
      "HOME": "/home/<USER>",
      "LANG": "en_US.UTF-8",
      "LS_COLORS": "rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:",
      "XDG_CURRENT_DESKTOP": "X-Cinnamon",
      "VIRTUAL_ENV": "/home/<USER>/Desktop/tradingview-automatio/venv",
      "GIT_ASKPASS": "/tmp/.mount_cursordYGeAK/usr/share/cursor/resources/app/extensions/git/dist/askpass.sh",
      "XDG_SEAT_PATH": "/org/freedesktop/DisplayManager/Seat0",
      "CHROME_DESKTOP": "cursor.desktop",
      "APPDIR": "/tmp/.mount_cursordYGeAK",
      "VSCODE_GIT_ASKPASS_EXTRA_ARGS": "",
      "LESSCLOSE": "/usr/bin/lesspipe %s %s",
      "XDG_SESSION_CLASS": "user",
      "TERM": "xterm-256color",
      "LESSOPEN": "| /usr/bin/lesspipe %s",
      "USER": "ami",
      "VSCODE_GIT_IPC_HANDLE": "/run/user/1000/vscode-git-8c0a4d5b6f.sock",
      "OWD": "/home/<USER>",
      "DISPLAY": ":0",
      "SHLVL": "1",
      "XDG_VTNR": "7",
      "XDG_SESSION_ID": "c1",
      "VIRTUAL_ENV_PROMPT": "(venv) ",
      "LD_LIBRARY_PATH": "/tmp/.mount_cursordYGeAK/usr/lib/:/tmp/.mount_cursordYGeAK/usr/lib32/:/tmp/.mount_cursordYGeAK/usr/lib64/:/tmp/.mount_cursordYGeAK/lib/:/tmp/.mount_cursordYGeAK/lib/i386-linux-gnu/:/tmp/.mount_cursordYGeAK/lib/x86_64-linux-gnu/:/tmp/.mount_cursordYGeAK/lib/aarch64-linux-gnu/:/tmp/.mount_cursordYGeAK/lib32/:/tmp/.mount_cursordYGeAK/lib64/:",
      "APPIMAGE": "/opt/cursor/cursor.AppImage",
      "XDG_RUNTIME_DIR": "/run/user/1000",
      "PS1": "\\[\u001b]633;A\u0007\\](venv) \\[\u001b]633;A\u0007\\]\\[\\e]0;\\u@\\h: \\w\\a\\]${debian_chroot:+($debian_chroot)}\\[\\033[01;32m\\]\\u@\\h\\[\\033[00m\\]:\\[\\033[01;34m\\]\\w\\[\\033[00m\\]\\$ \\[\u001b]633;B\u0007\\]\\[\u001b]633;B\u0007\\]",
      "VSCODE_GIT_ASKPASS_MAIN": "/tmp/.mount_cursordYGeAK/usr/share/cursor/resources/app/extensions/git/dist/askpass-main.js",
      "GTK3_MODULES": "xapp-gtk3-module",
      "XDG_DATA_DIRS": "/tmp/.mount_cursordYGeAK/usr/share/:/usr/local/share:/usr/share:/usr/share/cinnamon:/usr/share/gnome:/home/<USER>/.local/share/flatpak/exports/share:/var/lib/flatpak/exports/share:/usr/local/share:/usr/share",
      "GDK_BACKEND": "x11",
      "PATH": "/home/<USER>/Desktop/tradingview-automatio/venv/bin:/home/<USER>/.cargo/bin:/home/<USER>/.avm/bin:/home/<USER>/.npm-global/bin:/home/<USER>/.local/bin:/home/<USER>/bin:/home/<USER>/.cargo/bin:/home/<USER>/.avm/bin:/tmp/.mount_cursordYGeAK/usr/bin/:/tmp/.mount_cursordYGeAK/usr/sbin/:/tmp/.mount_cursordYGeAK/usr/games/:/tmp/.mount_cursordYGeAK/bin/:/tmp/.mount_cursordYGeAK/sbin/:/home/<USER>/.npm-global/bin:/home/<USER>/.local/bin:/home/<USER>/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/home/<USER>/.cursor/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts",
      "GDMSESSION": "cinnamon",
      "ORIGINAL_XDG_CURRENT_DESKTOP": "X-Cinnamon",
      "DBUS_SESSION_BUS_ADDRESS": "unix:path=/run/user/1000/bus",
      "QT_PLUGIN_PATH": "/tmp/.mount_cursordYGeAK/usr/lib/qt4/plugins/:/tmp/.mount_cursordYGeAK/usr/lib/i386-linux-gnu/qt4/plugins/:/tmp/.mount_cursordYGeAK/usr/lib/x86_64-linux-gnu/qt4/plugins/:/tmp/.mount_cursordYGeAK/usr/lib/aarch64-linux-gnu/qt4/plugins/:/tmp/.mount_cursordYGeAK/usr/lib32/qt4/plugins/:/tmp/.mount_cursordYGeAK/usr/lib64/qt4/plugins/:/tmp/.mount_cursordYGeAK/usr/lib/qt5/plugins/:/tmp/.mount_cursordYGeAK/usr/lib/i386-linux-gnu/qt5/plugins/:/tmp/.mount_cursordYGeAK/usr/lib/x86_64-linux-gnu/qt5/plugins/:/tmp/.mount_cursordYGeAK/usr/lib/aarch64-linux-gnu/qt5/plugins/:/tmp/.mount_cursordYGeAK/usr/lib32/qt5/plugins/:/tmp/.mount_cursordYGeAK/usr/lib64/qt5/plugins/:",
      "GIO_LAUNCHED_DESKTOP_FILE_PID": "3169",
      "GIO_LAUNCHED_DESKTOP_FILE": "/home/<USER>/Desktop/cursor.desktop",
      "TERM_PROGRAM": "vscode",
      "CURSOR_TRACE_ID": "209196c0e5e643619deb1939974b3db6",
      "_": "/home/<USER>/Desktop/tradingview-automatio/venv/bin/python"
    }
  },
  "timestamp": "2025-09-12T15:46:06.457387",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:06,457 | comprehensive_test | INFO | log_assertion:350 | ✅ PASS ASSERTION: python_version_check
2025-09-12 15:46:06,457 | comprehensive_test | DEBUG | log_assertion:351 | Assertion details: {
  "operation_id": "OP_000004",
  "operation_type": "ASSERTION",
  "assertion_type": "python_version_check",
  "expected": ">= 3.8",
  "actual": "3.12",
  "result": true,
  "test_name": "ENVIRONMENT_VALIDATION",
  "timestamp": "2025-09-12T15:46:06.457511",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:06,457 | comprehensive_test | INFO | log_assertion:350 | ✅ PASS ASSERTION: directory_exists
2025-09-12 15:46:06,457 | comprehensive_test | DEBUG | log_assertion:351 | Assertion details: {
  "operation_id": "OP_000005",
  "operation_type": "ASSERTION",
  "assertion_type": "directory_exists",
  "expected": "True",
  "actual": "True",
  "result": true,
  "test_name": "ENVIRONMENT_VALIDATION_src",
  "timestamp": "2025-09-12T15:46:06.457595",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:06,457 | comprehensive_test | INFO | log_assertion:350 | ✅ PASS ASSERTION: directory_exists
2025-09-12 15:46:06,457 | comprehensive_test | DEBUG | log_assertion:351 | Assertion details: {
  "operation_id": "OP_000006",
  "operation_type": "ASSERTION",
  "assertion_type": "directory_exists",
  "expected": "True",
  "actual": "True",
  "result": true,
  "test_name": "ENVIRONMENT_VALIDATION_tests",
  "timestamp": "2025-09-12T15:46:06.457683",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:06,457 | comprehensive_test | INFO | log_assertion:350 | ✅ PASS ASSERTION: directory_exists
2025-09-12 15:46:06,457 | comprehensive_test | DEBUG | log_assertion:351 | Assertion details: {
  "operation_id": "OP_000007",
  "operation_type": "ASSERTION",
  "assertion_type": "directory_exists",
  "expected": "True",
  "actual": "True",
  "result": true,
  "test_name": "ENVIRONMENT_VALIDATION_test_results",
  "timestamp": "2025-09-12T15:46:06.457845",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:06,457 | comprehensive_test | INFO | log_assertion:350 | ✅ PASS ASSERTION: log_directory_exists
2025-09-12 15:46:06,457 | comprehensive_test | DEBUG | log_assertion:351 | Assertion details: {
  "operation_id": "OP_000008",
  "operation_type": "ASSERTION",
  "assertion_type": "log_directory_exists",
  "expected": "True",
  "actual": "True",
  "result": true,
  "test_name": "ENVIRONMENT_VALIDATION",
  "timestamp": "2025-09-12T15:46:06.457920",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:06,458 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: ENVIRONMENT_VALIDATION_COMPLETE
2025-09-12 15:46:06,458 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000003",
  "operation_type": "OPERATION",
  "operation": "ENVIRONMENT_VALIDATION_COMPLETE",
  "details": {
    "status": "SUCCESS",
    "python_version": "3.12.3",
    "required_directories": {
      "src": true,
      "tests": true,
      "test_results": true
    }
  },
  "timestamp": "2025-09-12T15:46:06.458008",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:06,458 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: IMPORT_VALIDATION
2025-09-12 15:46:06,458 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000010",
  "operation_type": "OPERATION",
  "operation": "IMPORT_VALIDATION",
  "details": {},
  "timestamp": "2025-09-12T15:46:06.458138",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:06,458 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: IMPORT_VALIDATION_START
2025-09-12 15:46:06,458 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000010",
  "operation_type": "OPERATION",
  "operation": "IMPORT_VALIDATION_START",
  "details": {
    "modules_to_check": [
      "pytest",
      "pytest_asyncio",
      "pytest_cov",
      "sqlalchemy",
      "discord",
      "openai",
      "yfinance",
      "supabase",
      "redis",
      "tenacity",
      "structlog",
      "psutil"
    ],
    "total_modules": 12
  },
  "timestamp": "2025-09-12T15:46:06.458184",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:06,458 | comprehensive_test | INFO | log_import:371 | ✅ SUCCESS IMPORT: pytest
2025-09-12 15:46:06,458 | comprehensive_test | DEBUG | log_import:372 | Import details: {
  "operation_id": "OP_000011",
  "operation_type": "IMPORT",
  "module_name": "pytest",
  "success": true,
  "error": null,
  "timestamp": "2025-09-12T15:46:06.458242",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:06,458 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: MODULE_IMPORTED
2025-09-12 15:46:06,458 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000010",
  "operation_type": "OPERATION",
  "operation": "MODULE_IMPORTED",
  "details": {
    "module_name": "pytest",
    "module_version": "8.4.2",
    "module_file": "/home/<USER>/Desktop/tradingview-automatio/venv/lib/python3.12/site-packages/pytest/__init__.py",
    "import_duration": 5.9604644775390625e-06
  },
  "timestamp": "2025-09-12T15:46:06.458382",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:06,477 | comprehensive_test | INFO | log_import:371 | ✅ SUCCESS IMPORT: pytest_asyncio
2025-09-12 15:46:06,478 | comprehensive_test | DEBUG | log_import:372 | Import details: {
  "operation_id": "OP_000013",
  "operation_type": "IMPORT",
  "module_name": "pytest_asyncio",
  "success": true,
  "error": null,
  "timestamp": "2025-09-12T15:46:06.477826",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:06,478 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: MODULE_IMPORTED
2025-09-12 15:46:06,478 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000010",
  "operation_type": "OPERATION",
  "operation": "MODULE_IMPORTED",
  "details": {
    "module_name": "pytest_asyncio",
    "module_version": "1.2.0",
    "module_file": "/home/<USER>/Desktop/tradingview-automatio/venv/lib/python3.12/site-packages/pytest_asyncio/__init__.py",
    "import_duration": 0.019376039505004883
  },
  "timestamp": "2025-09-12T15:46:06.478304",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:06,478 | comprehensive_test | INFO | log_import:371 | ✅ SUCCESS IMPORT: pytest_cov
2025-09-12 15:46:06,479 | comprehensive_test | DEBUG | log_import:372 | Import details: {
  "operation_id": "OP_000015",
  "operation_type": "IMPORT",
  "module_name": "pytest_cov",
  "success": true,
  "error": null,
  "timestamp": "2025-09-12T15:46:06.478928",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:06,479 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: MODULE_IMPORTED
2025-09-12 15:46:06,479 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000010",
  "operation_type": "OPERATION",
  "operation": "MODULE_IMPORTED",
  "details": {
    "module_name": "pytest_cov",
    "module_version": "7.0.0",
    "module_file": "/home/<USER>/Desktop/tradingview-automatio/venv/lib/python3.12/site-packages/pytest_cov/__init__.py",
    "import_duration": 0.0005042552947998047
  },
  "timestamp": "2025-09-12T15:46:06.479342",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:06,577 | comprehensive_test | INFO | log_import:371 | ✅ SUCCESS IMPORT: sqlalchemy
2025-09-12 15:46:06,578 | comprehensive_test | DEBUG | log_import:372 | Import details: {
  "operation_id": "OP_000017",
  "operation_type": "IMPORT",
  "module_name": "sqlalchemy",
  "success": true,
  "error": null,
  "timestamp": "2025-09-12T15:46:06.577821",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:06,578 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: MODULE_IMPORTED
2025-09-12 15:46:06,578 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000010",
  "operation_type": "OPERATION",
  "operation": "MODULE_IMPORTED",
  "details": {
    "module_name": "sqlalchemy",
    "module_version": "2.0.43",
    "module_file": "/home/<USER>/Desktop/tradingview-automatio/venv/lib/python3.12/site-packages/sqlalchemy/__init__.py",
    "import_duration": 0.09835648536682129
  },
  "timestamp": "2025-09-12T15:46:06.578133",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:06,741 | comprehensive_test | INFO | log_import:371 | ✅ SUCCESS IMPORT: discord
2025-09-12 15:46:06,741 | comprehensive_test | DEBUG | log_import:372 | Import details: {
  "operation_id": "OP_000019",
  "operation_type": "IMPORT",
  "module_name": "discord",
  "success": true,
  "error": null,
  "timestamp": "2025-09-12T15:46:06.741599",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:06,741 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: MODULE_IMPORTED
2025-09-12 15:46:06,742 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000010",
  "operation_type": "OPERATION",
  "operation": "MODULE_IMPORTED",
  "details": {
    "module_name": "discord",
    "module_version": "2.6.3",
    "module_file": "/home/<USER>/Desktop/tradingview-automatio/venv/lib/python3.12/site-packages/discord/__init__.py",
    "import_duration": 0.16336631774902344
  },
  "timestamp": "2025-09-12T15:46:06.741968",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:06,991 | comprehensive_test | INFO | log_import:371 | ✅ SUCCESS IMPORT: openai
2025-09-12 15:46:06,991 | comprehensive_test | DEBUG | log_import:372 | Import details: {
  "operation_id": "OP_000021",
  "operation_type": "IMPORT",
  "module_name": "openai",
  "success": true,
  "error": null,
  "timestamp": "2025-09-12T15:46:06.991376",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:06,991 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: MODULE_IMPORTED
2025-09-12 15:46:06,991 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000010",
  "operation_type": "OPERATION",
  "operation": "MODULE_IMPORTED",
  "details": {
    "module_name": "openai",
    "module_version": "1.107.1",
    "module_file": "/home/<USER>/Desktop/tradingview-automatio/venv/lib/python3.12/site-packages/openai/__init__.py",
    "import_duration": 0.24932456016540527
  },
  "timestamp": "2025-09-12T15:46:06.991634",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:07,371 | comprehensive_test | INFO | log_import:371 | ✅ SUCCESS IMPORT: yfinance
2025-09-12 15:46:07,371 | comprehensive_test | DEBUG | log_import:372 | Import details: {
  "operation_id": "OP_000023",
  "operation_type": "IMPORT",
  "module_name": "yfinance",
  "success": true,
  "error": null,
  "timestamp": "2025-09-12T15:46:07.371831",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:07,372 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: MODULE_IMPORTED
2025-09-12 15:46:07,372 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000010",
  "operation_type": "OPERATION",
  "operation": "MODULE_IMPORTED",
  "details": {
    "module_name": "yfinance",
    "module_version": "0.2.65",
    "module_file": "/home/<USER>/Desktop/tradingview-automatio/venv/lib/python3.12/site-packages/yfinance/__init__.py",
    "import_duration": 0.38013219833374023
  },
  "timestamp": "2025-09-12T15:46:07.372077",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:07,441 | comprehensive_test | INFO | log_import:371 | ✅ SUCCESS IMPORT: supabase
2025-09-12 15:46:07,441 | comprehensive_test | DEBUG | log_import:372 | Import details: {
  "operation_id": "OP_000025",
  "operation_type": "IMPORT",
  "module_name": "supabase",
  "success": true,
  "error": null,
  "timestamp": "2025-09-12T15:46:07.441213",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:07,441 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: MODULE_IMPORTED
2025-09-12 15:46:07,441 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000010",
  "operation_type": "OPERATION",
  "operation": "MODULE_IMPORTED",
  "details": {
    "module_name": "supabase",
    "module_version": "2.18.1",
    "module_file": "/home/<USER>/Desktop/tradingview-automatio/venv/lib/python3.12/site-packages/supabase/__init__.py",
    "import_duration": 0.06906580924987793
  },
  "timestamp": "2025-09-12T15:46:07.441443",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:07,465 | comprehensive_test | INFO | log_import:371 | ✅ SUCCESS IMPORT: redis
2025-09-12 15:46:07,465 | comprehensive_test | DEBUG | log_import:372 | Import details: {
  "operation_id": "OP_000027",
  "operation_type": "IMPORT",
  "module_name": "redis",
  "success": true,
  "error": null,
  "timestamp": "2025-09-12T15:46:07.465190",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:07,465 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: MODULE_IMPORTED
2025-09-12 15:46:07,465 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000010",
  "operation_type": "OPERATION",
  "operation": "MODULE_IMPORTED",
  "details": {
    "module_name": "redis",
    "module_version": "6.4.0",
    "module_file": "/home/<USER>/Desktop/tradingview-automatio/venv/lib/python3.12/site-packages/redis/__init__.py",
    "import_duration": 0.023688077926635742
  },
  "timestamp": "2025-09-12T15:46:07.465419",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:07,469 | comprehensive_test | INFO | log_import:371 | ✅ SUCCESS IMPORT: tenacity
2025-09-12 15:46:07,469 | comprehensive_test | DEBUG | log_import:372 | Import details: {
  "operation_id": "OP_000029",
  "operation_type": "IMPORT",
  "module_name": "tenacity",
  "success": true,
  "error": null,
  "timestamp": "2025-09-12T15:46:07.469759",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:07,469 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: MODULE_IMPORTED
2025-09-12 15:46:07,470 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000010",
  "operation_type": "OPERATION",
  "operation": "MODULE_IMPORTED",
  "details": {
    "module_name": "tenacity",
    "module_version": "unknown",
    "module_file": "/home/<USER>/Desktop/tradingview-automatio/venv/lib/python3.12/site-packages/tenacity/__init__.py",
    "import_duration": 0.00428009033203125
  },
  "timestamp": "2025-09-12T15:46:07.469974",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:07,478 | comprehensive_test | INFO | log_import:371 | ✅ SUCCESS IMPORT: structlog
2025-09-12 15:46:07,478 | comprehensive_test | DEBUG | log_import:372 | Import details: {
  "operation_id": "OP_000031",
  "operation_type": "IMPORT",
  "module_name": "structlog",
  "success": true,
  "error": null,
  "timestamp": "2025-09-12T15:46:07.478265",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:07,479 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: MODULE_IMPORTED
2025-09-12 15:46:07,479 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000010",
  "operation_type": "OPERATION",
  "operation": "MODULE_IMPORTED",
  "details": {
    "module_name": "structlog",
    "module_version": "25.4.0",
    "module_file": "/home/<USER>/Desktop/tradingview-automatio/venv/lib/python3.12/site-packages/structlog/__init__.py",
    "import_duration": 0.008207559585571289
  },
  "timestamp": "2025-09-12T15:46:07.479455",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:07,479 | comprehensive_test | INFO | log_import:371 | ✅ SUCCESS IMPORT: psutil
2025-09-12 15:46:07,479 | comprehensive_test | DEBUG | log_import:372 | Import details: {
  "operation_id": "OP_000033",
  "operation_type": "IMPORT",
  "module_name": "psutil",
  "success": true,
  "error": null,
  "timestamp": "2025-09-12T15:46:07.479629",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:07,479 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: MODULE_IMPORTED
2025-09-12 15:46:07,479 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000010",
  "operation_type": "OPERATION",
  "operation": "MODULE_IMPORTED",
  "details": {
    "module_name": "psutil",
    "module_version": "7.0.0",
    "module_file": "/home/<USER>/Desktop/tradingview-automatio/venv/lib/python3.12/site-packages/psutil/__init__.py",
    "import_duration": 3.814697265625e-06
  },
  "timestamp": "2025-09-12T15:46:07.479763",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:07,479 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: IMPORT_VALIDATION_COMPLETE
2025-09-12 15:46:07,479 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000010",
  "operation_type": "OPERATION",
  "operation": "IMPORT_VALIDATION_COMPLETE",
  "details": {
    "successful_imports": 12,
    "failed_imports": 0,
    "total_modules": 12,
    "success_rate": 100.0
  },
  "timestamp": "2025-09-12T15:46:07.479817",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:07,479 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: UNIT_TESTS
2025-09-12 15:46:07,480 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000036",
  "operation_type": "OPERATION",
  "operation": "UNIT_TESTS",
  "details": {},
  "timestamp": "2025-09-12T15:46:07.479945",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:07,480 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: UNIT_TESTS_START
2025-09-12 15:46:07,480 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000036",
  "operation_type": "OPERATION",
  "operation": "UNIT_TESTS_START",
  "details": {
    "test_directory": "tests/unit",
    "expected_tests": [
      "test_ml_sentiment_analyzer",
      "test_options_greeks_calculator",
      "test_strategy_calculator"
    ]
  },
  "timestamp": "2025-09-12T15:46:07.480082",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:11,607 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: UNIT_TESTS_EXECUTION
2025-09-12 15:46:11,607 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000036",
  "operation_type": "OPERATION",
  "operation": "UNIT_TESTS_EXECUTION",
  "details": {
    "command": "pytest tests/unit -v --tb=long",
    "return_code": 1,
    "duration": 4.127614974975586,
    "stdout_length": 49162,
    "stderr_length": 586
  },
  "timestamp": "2025-09-12T15:46:11.607763",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:11,608 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: PARSE_PYTEST_OUTPUT_UNIT_TESTS
2025-09-12 15:46:11,608 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000039",
  "operation_type": "OPERATION",
  "operation": "PARSE_PYTEST_OUTPUT_UNIT_TESTS",
  "details": {},
  "timestamp": "2025-09-12T15:46:11.608430",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:11,608 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INDIVIDUAL_TEST_RESULT
2025-09-12 15:46:11,608 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000039",
  "operation_type": "OPERATION",
  "operation": "INDIVIDUAL_TEST_RESULT",
  "details": {
    "test_type": "UNIT_TESTS",
    "test_file": "tests/unit/test_options_greeks_calculator.py",
    "test_name": "test_estimate_new_delta PASSED",
    "status": "PASSED",
    "full_line": "tests/unit/test_options_greeks_calculator.py::test_estimate_new_delta PASSED"
  },
  "timestamp": "2025-09-12T15:46:11.608566",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:11,608 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INDIVIDUAL_TEST_RESULT
2025-09-12 15:46:11,608 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000039",
  "operation_type": "OPERATION",
  "operation": "INDIVIDUAL_TEST_RESULT",
  "details": {
    "test_type": "UNIT_TESTS",
    "test_file": "tests/unit/test_options_greeks_calculator.py",
    "test_name": "test_estimate_new_price PASSED",
    "status": "PASSED",
    "full_line": "tests/unit/test_options_greeks_calculator.py::test_estimate_new_price PASSED"
  },
  "timestamp": "2025-09-12T15:46:11.608629",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:11,608 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INDIVIDUAL_TEST_RESULT
2025-09-12 15:46:11,608 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000039",
  "operation_type": "OPERATION",
  "operation": "INDIVIDUAL_TEST_RESULT",
  "details": {
    "test_type": "UNIT_TESTS",
    "test_file": "tests/unit/test_options_greeks_calculator.py",
    "test_name": "test_breakeven_calculation PASSED",
    "status": "PASSED",
    "full_line": "tests/unit/test_options_greeks_calculator.py::test_breakeven_calculation PASSED"
  },
  "timestamp": "2025-09-12T15:46:11.608679",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:11,608 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INDIVIDUAL_TEST_RESULT
2025-09-12 15:46:11,608 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000039",
  "operation_type": "OPERATION",
  "operation": "INDIVIDUAL_TEST_RESULT",
  "details": {
    "test_type": "UNIT_TESTS",
    "test_file": "tests/unit/test_options_greeks_calculator.py",
    "test_name": "test_probability_of_profit PASSED",
    "status": "PASSED",
    "full_line": "tests/unit/test_options_greeks_calculator.py::test_probability_of_profit PASSED"
  },
  "timestamp": "2025-09-12T15:46:11.608733",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:11,608 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INDIVIDUAL_TEST_RESULT
2025-09-12 15:46:11,608 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000039",
  "operation_type": "OPERATION",
  "operation": "INDIVIDUAL_TEST_RESULT",
  "details": {
    "test_type": "UNIT_TESTS",
    "test_file": "tests/unit/test_options_greeks_calculator.py",
    "test_name": "test_time_decay_effect PASSED",
    "status": "PASSED",
    "full_line": "tests/unit/test_options_greeks_calculator.py::test_time_decay_effect PASSED"
  },
  "timestamp": "2025-09-12T15:46:11.608782",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:11,608 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INDIVIDUAL_TEST_RESULT
2025-09-12 15:46:11,608 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000039",
  "operation_type": "OPERATION",
  "operation": "INDIVIDUAL_TEST_RESULT",
  "details": {
    "test_type": "UNIT_TESTS",
    "test_file": "tests/unit/test_options_greeks_calculator.py",
    "test_name": "test_volatility_effect PASSED",
    "status": "PASSED",
    "full_line": "tests/unit/test_options_greeks_calculator.py::test_volatility_effect PASSED"
  },
  "timestamp": "2025-09-12T15:46:11.608829",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:11,608 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INDIVIDUAL_TEST_RESULT
2025-09-12 15:46:11,608 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000039",
  "operation_type": "OPERATION",
  "operation": "INDIVIDUAL_TEST_RESULT",
  "details": {
    "test_type": "UNIT_TESTS",
    "test_file": "tests/unit/test_strategy_calculator.py",
    "test_name": "test_calculate_entry_strategy PASSED",
    "status": "PASSED",
    "full_line": "tests/unit/test_strategy_calculator.py::test_calculate_entry_strategy PASSED"
  },
  "timestamp": "2025-09-12T15:46:11.608876",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:11,608 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INDIVIDUAL_TEST_RESULT
2025-09-12 15:46:11,608 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000039",
  "operation_type": "OPERATION",
  "operation": "INDIVIDUAL_TEST_RESULT",
  "details": {
    "test_type": "UNIT_TESTS",
    "test_file": "tests/unit/test_strategy_calculator.py",
    "test_name": "test_estimate_options_strategy PASSED",
    "status": "PASSED",
    "full_line": "tests/unit/test_strategy_calculator.py::test_estimate_options_strategy PASSED"
  },
  "timestamp": "2025-09-12T15:46:11.608932",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:11,608 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INDIVIDUAL_TEST_RESULT
2025-09-12 15:46:11,609 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000039",
  "operation_type": "OPERATION",
  "operation": "INDIVIDUAL_TEST_RESULT",
  "details": {
    "test_type": "UNIT_TESTS",
    "test_file": "tests/unit/test_strategy_calculator.py",
    "test_name": "test_risk_classification PASSED",
    "status": "PASSED",
    "full_line": "tests/unit/test_strategy_calculator.py::test_risk_classification PASSED"
  },
  "timestamp": "2025-09-12T15:46:11.608978",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:11,609 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INDIVIDUAL_TEST_RESULT
2025-09-12 15:46:11,609 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000039",
  "operation_type": "OPERATION",
  "operation": "INDIVIDUAL_TEST_RESULT",
  "details": {
    "test_type": "UNIT_TESTS",
    "test_file": "tests/unit/test_strategy_calculator.py",
    "test_name": "test_stop_loss_calculation PASSED",
    "status": "PASSED",
    "full_line": "tests/unit/test_strategy_calculator.py::test_stop_loss_calculation PASSED"
  },
  "timestamp": "2025-09-12T15:46:11.609024",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:11,609 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INDIVIDUAL_TEST_RESULT
2025-09-12 15:46:11,609 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000039",
  "operation_type": "OPERATION",
  "operation": "INDIVIDUAL_TEST_RESULT",
  "details": {
    "test_type": "UNIT_TESTS",
    "test_file": "tests/unit/test_strategy_calculator.py",
    "test_name": "test_take_profit_calculation PASSED",
    "status": "PASSED",
    "full_line": "tests/unit/test_strategy_calculator.py::test_take_profit_calculation PASSED"
  },
  "timestamp": "2025-09-12T15:46:11.609070",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:11,609 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INDIVIDUAL_TEST_RESULT
2025-09-12 15:46:11,609 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000039",
  "operation_type": "OPERATION",
  "operation": "INDIVIDUAL_TEST_RESULT",
  "details": {
    "test_type": "UNIT_TESTS",
    "test_file": "tests/unit/test_strategy_calculator.py",
    "test_name": "test_supertrend_integration PASSED",
    "status": "PASSED",
    "full_line": "tests/unit/test_strategy_calculator.py::test_supertrend_integration PASSED"
  },
  "timestamp": "2025-09-12T15:46:11.609113",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:11,609 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INDIVIDUAL_TEST_RESULT
2025-09-12 15:46:11,609 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000039",
  "operation_type": "OPERATION",
  "operation": "INDIVIDUAL_TEST_RESULT",
  "details": {
    "test_type": "UNIT_TESTS",
    "test_file": "tests/unit/test_strategy_calculator.py",
    "test_name": "test_supertrend_flip_tracking PASSED",
    "status": "PASSED",
    "full_line": "tests/unit/test_strategy_calculator.py::test_supertrend_flip_tracking PASSED"
  },
  "timestamp": "2025-09-12T15:46:11.609158",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:11,609 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INDIVIDUAL_TEST_RESULT
2025-09-12 15:46:11,609 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000039",
  "operation_type": "OPERATION",
  "operation": "INDIVIDUAL_TEST_RESULT",
  "details": {
    "test_type": "UNIT_TESTS",
    "test_file": "tests/unit/test_strategy_calculator.py",
    "test_name": "test_supertrend_logging PASSED",
    "status": "PASSED",
    "full_line": "tests/unit/test_strategy_calculator.py::test_supertrend_logging PASSED"
  },
  "timestamp": "2025-09-12T15:46:11.609210",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:11,609 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INDIVIDUAL_TEST_RESULT
2025-09-12 15:46:11,609 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000039",
  "operation_type": "OPERATION",
  "operation": "INDIVIDUAL_TEST_RESULT",
  "details": {
    "test_type": "UNIT_TESTS",
    "test_file": "FAILED tests/unit/test_ml_sentiment_analyzer.py",
    "test_name": "test_negative_sentiment_analysis - AssertionError: assert 'neutral' == 'negative'",
    "status": "FAILED",
    "full_line": "FAILED tests/unit/test_ml_sentiment_analyzer.py::test_negative_sentiment_analysis - AssertionError: assert 'neutral' == 'negative'"
  },
  "timestamp": "2025-09-12T15:46:11.609351",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:11,609 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INDIVIDUAL_TEST_RESULT
2025-09-12 15:46:11,609 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000039",
  "operation_type": "OPERATION",
  "operation": "INDIVIDUAL_TEST_RESULT",
  "details": {
    "test_type": "UNIT_TESTS",
    "test_file": "FAILED tests/unit/test_ml_sentiment_analyzer.py",
    "test_name": "test_sentiment_extraction - AssertionError: assert 'risks' in ['weak']",
    "status": "FAILED",
    "full_line": "FAILED tests/unit/test_ml_sentiment_analyzer.py::test_sentiment_extraction - AssertionError: assert 'risks' in ['weak']"
  },
  "timestamp": "2025-09-12T15:46:11.609403",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:11,609 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: PYTEST_OUTPUT_PARSED_UNIT_TESTS
2025-09-12 15:46:11,609 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000039",
  "operation_type": "OPERATION",
  "operation": "PYTEST_OUTPUT_PARSED_UNIT_TESTS",
  "details": {
    "total_tests": 16,
    "passed": 14,
    "failed": 2,
    "errors": 0,
    "success_rate": 87.5,
    "test_results": [
      {
        "test_file": "tests/unit/test_options_greeks_calculator.py",
        "test_name": "test_estimate_new_delta PASSED",
        "status": "PASSED",
        "line": "tests/unit/test_options_greeks_calculator.py::test_estimate_new_delta PASSED"
      },
      {
        "test_file": "tests/unit/test_options_greeks_calculator.py",
        "test_name": "test_estimate_new_price PASSED",
        "status": "PASSED",
        "line": "tests/unit/test_options_greeks_calculator.py::test_estimate_new_price PASSED"
      },
      {
        "test_file": "tests/unit/test_options_greeks_calculator.py",
        "test_name": "test_breakeven_calculation PASSED",
        "status": "PASSED",
        "line": "tests/unit/test_options_greeks_calculator.py::test_breakeven_calculation PASSED"
      },
      {
        "test_file": "tests/unit/test_options_greeks_calculator.py",
        "test_name": "test_probability_of_profit PASSED",
        "status": "PASSED",
        "line": "tests/unit/test_options_greeks_calculator.py::test_probability_of_profit PASSED"
      },
      {
        "test_file": "tests/unit/test_options_greeks_calculator.py",
        "test_name": "test_time_decay_effect PASSED",
        "status": "PASSED",
        "line": "tests/unit/test_options_greeks_calculator.py::test_time_decay_effect PASSED"
      },
      {
        "test_file": "tests/unit/test_options_greeks_calculator.py",
        "test_name": "test_volatility_effect PASSED",
        "status": "PASSED",
        "line": "tests/unit/test_options_greeks_calculator.py::test_volatility_effect PASSED"
      },
      {
        "test_file": "tests/unit/test_strategy_calculator.py",
        "test_name": "test_calculate_entry_strategy PASSED",
        "status": "PASSED",
        "line": "tests/unit/test_strategy_calculator.py::test_calculate_entry_strategy PASSED"
      },
      {
        "test_file": "tests/unit/test_strategy_calculator.py",
        "test_name": "test_estimate_options_strategy PASSED",
        "status": "PASSED",
        "line": "tests/unit/test_strategy_calculator.py::test_estimate_options_strategy PASSED"
      },
      {
        "test_file": "tests/unit/test_strategy_calculator.py",
        "test_name": "test_risk_classification PASSED",
        "status": "PASSED",
        "line": "tests/unit/test_strategy_calculator.py::test_risk_classification PASSED"
      },
      {
        "test_file": "tests/unit/test_strategy_calculator.py",
        "test_name": "test_stop_loss_calculation PASSED",
        "status": "PASSED",
        "line": "tests/unit/test_strategy_calculator.py::test_stop_loss_calculation PASSED"
      },
      {
        "test_file": "tests/unit/test_strategy_calculator.py",
        "test_name": "test_take_profit_calculation PASSED",
        "status": "PASSED",
        "line": "tests/unit/test_strategy_calculator.py::test_take_profit_calculation PASSED"
      },
      {
        "test_file": "tests/unit/test_strategy_calculator.py",
        "test_name": "test_supertrend_integration PASSED",
        "status": "PASSED",
        "line": "tests/unit/test_strategy_calculator.py::test_supertrend_integration PASSED"
      },
      {
        "test_file": "tests/unit/test_strategy_calculator.py",
        "test_name": "test_supertrend_flip_tracking PASSED",
        "status": "PASSED",
        "line": "tests/unit/test_strategy_calculator.py::test_supertrend_flip_tracking PASSED"
      },
      {
        "test_file": "tests/unit/test_strategy_calculator.py",
        "test_name": "test_supertrend_logging PASSED",
        "status": "PASSED",
        "line": "tests/unit/test_strategy_calculator.py::test_supertrend_logging PASSED"
      },
      {
        "test_file": "FAILED tests/unit/test_ml_sentiment_analyzer.py",
        "test_name": "test_negative_sentiment_analysis - AssertionError: assert 'neutral' == 'negative'",
        "status": "FAILED",
        "line": "FAILED tests/unit/test_ml_sentiment_analyzer.py::test_negative_sentiment_analysis - AssertionError: assert 'neutral' == 'negative'"
      },
      {
        "test_file": "FAILED tests/unit/test_ml_sentiment_analyzer.py",
        "test_name": "test_sentiment_extraction - AssertionError: assert 'risks' in ['weak']",
        "status": "FAILED",
        "line": "FAILED tests/unit/test_ml_sentiment_analyzer.py::test_sentiment_extraction - AssertionError: assert 'risks' in ['weak']"
      }
    ]
  },
  "timestamp": "2025-09-12T15:46:11.609454",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:11,610 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INTEGRATION_TESTS
2025-09-12 15:46:11,610 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000043",
  "operation_type": "OPERATION",
  "operation": "INTEGRATION_TESTS",
  "details": {},
  "timestamp": "2025-09-12T15:46:11.610161",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:11,610 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INTEGRATION_TESTS_START
2025-09-12 15:46:11,610 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000043",
  "operation_type": "OPERATION",
  "operation": "INTEGRATION_TESTS_START",
  "details": {
    "test_directory": "tests/integration",
    "expected_tests": [
      "test_alpha_vantage_provider",
      "test_market_data_service",
      "test_polygon_provider"
    ]
  },
  "timestamp": "2025-09-12T15:46:11.610213",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:15,936 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INTEGRATION_TESTS_EXECUTION
2025-09-12 15:46:15,936 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000043",
  "operation_type": "OPERATION",
  "operation": "INTEGRATION_TESTS_EXECUTION",
  "details": {
    "command": "pytest tests/integration -v --tb=long",
    "return_code": 2,
    "duration": 4.326520919799805
  },
  "timestamp": "2025-09-12T15:46:15.936790",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:15,937 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: PARSE_PYTEST_OUTPUT_INTEGRATION_TESTS
2025-09-12 15:46:15,937 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000045",
  "operation_type": "OPERATION",
  "operation": "PARSE_PYTEST_OUTPUT_INTEGRATION_TESTS",
  "details": {},
  "timestamp": "2025-09-12T15:46:15.937253",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:15,937 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: PYTEST_OUTPUT_PARSED_INTEGRATION_TESTS
2025-09-12 15:46:15,937 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000045",
  "operation_type": "OPERATION",
  "operation": "PYTEST_OUTPUT_PARSED_INTEGRATION_TESTS",
  "details": {
    "total_tests": 0,
    "passed": 0,
    "failed": 0,
    "errors": 0,
    "success_rate": 0,
    "test_results": []
  },
  "timestamp": "2025-09-12T15:46:15.937366",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:15,937 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: COMPREHENSIVE_TESTS
2025-09-12 15:46:15,937 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000049",
  "operation_type": "OPERATION",
  "operation": "COMPREHENSIVE_TESTS",
  "details": {},
  "timestamp": "2025-09-12T15:46:15.937720",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:15,937 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: COMPREHENSIVE_TESTS_START
2025-09-12 15:46:15,937 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000049",
  "operation_type": "OPERATION",
  "operation": "COMPREHENSIVE_TESTS_START",
  "details": {
    "test_file": "tests/test_comprehensive.py",
    "expected_tests": [
      "TestRateLimiter",
      "TestErrorHandler",
      "TestWatchlistManager",
      "TestPerformanceOptimizer"
    ]
  },
  "timestamp": "2025-09-12T15:46:15.937768",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:22,763 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: COMPREHENSIVE_TESTS_EXECUTION
2025-09-12 15:46:22,763 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000049",
  "operation_type": "OPERATION",
  "operation": "COMPREHENSIVE_TESTS_EXECUTION",
  "details": {
    "command": "pytest tests/test_comprehensive.py -v --tb=long",
    "return_code": 1,
    "duration": 6.82549262046814
  },
  "timestamp": "2025-09-12T15:46:22.763317",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:22,764 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: PARSE_PYTEST_OUTPUT_COMPREHENSIVE_TESTS
2025-09-12 15:46:22,764 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000052",
  "operation_type": "OPERATION",
  "operation": "PARSE_PYTEST_OUTPUT_COMPREHENSIVE_TESTS",
  "details": {},
  "timestamp": "2025-09-12T15:46:22.764109",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:22,764 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INDIVIDUAL_TEST_RESULT
2025-09-12 15:46:22,764 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000052",
  "operation_type": "OPERATION",
  "operation": "INDIVIDUAL_TEST_RESULT",
  "details": {
    "test_type": "COMPREHENSIVE_TESTS",
    "test_file": "tests/test_comprehensive.py",
    "test_name": "TestErrorHandler",
    "status": "PASSED",
    "full_line": "tests/test_comprehensive.py::TestErrorHandler::test_user_friendly_messages PASSED"
  },
  "timestamp": "2025-09-12T15:46:22.764252",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:22,764 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INDIVIDUAL_TEST_RESULT
2025-09-12 15:46:22,764 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000052",
  "operation_type": "OPERATION",
  "operation": "INDIVIDUAL_TEST_RESULT",
  "details": {
    "test_type": "COMPREHENSIVE_TESTS",
    "test_file": "tests/test_comprehensive.py",
    "test_name": "TestWatchlistManager",
    "status": "ERROR",
    "full_line": "tests/test_comprehensive.py::TestWatchlistManager::test_watchlist_initialization ERROR"
  },
  "timestamp": "2025-09-12T15:46:22.764313",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:22,764 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INDIVIDUAL_TEST_RESULT
2025-09-12 15:46:22,764 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000052",
  "operation_type": "OPERATION",
  "operation": "INDIVIDUAL_TEST_RESULT",
  "details": {
    "test_type": "COMPREHENSIVE_TESTS",
    "test_file": "tests/test_comprehensive.py",
    "test_name": "TestWatchlistManager",
    "status": "ERROR",
    "full_line": "tests/test_comprehensive.py::TestWatchlistManager::test_create_watchlist ERROR"
  },
  "timestamp": "2025-09-12T15:46:22.764360",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:22,764 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INDIVIDUAL_TEST_RESULT
2025-09-12 15:46:22,764 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000052",
  "operation_type": "OPERATION",
  "operation": "INDIVIDUAL_TEST_RESULT",
  "details": {
    "test_type": "COMPREHENSIVE_TESTS",
    "test_file": "tests/test_comprehensive.py",
    "test_name": "TestDiscordUX",
    "status": "PASSED",
    "full_line": "tests/test_comprehensive.py::TestDiscordUX::test_interactive_embeds PASSED"
  },
  "timestamp": "2025-09-12T15:46:22.764408",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:22,764 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INDIVIDUAL_TEST_RESULT
2025-09-12 15:46:22,764 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000052",
  "operation_type": "OPERATION",
  "operation": "INDIVIDUAL_TEST_RESULT",
  "details": {
    "test_type": "COMPREHENSIVE_TESTS",
    "test_file": "tests/test_comprehensive.py",
    "test_name": "TestAdvancedSecurity",
    "status": "FAILED",
    "full_line": "tests/test_comprehensive.py::TestAdvancedSecurity::test_input_validation FAILED"
  },
  "timestamp": "2025-09-12T15:46:22.764454",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:22,764 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INDIVIDUAL_TEST_RESULT
2025-09-12 15:46:22,764 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000052",
  "operation_type": "OPERATION",
  "operation": "INDIVIDUAL_TEST_RESULT",
  "details": {
    "test_type": "COMPREHENSIVE_TESTS",
    "test_file": "tests/test_comprehensive.py",
    "test_name": "TestAdvancedSecurity",
    "status": "PASSED",
    "full_line": "tests/test_comprehensive.py::TestAdvancedSecurity::test_symbol_validation PASSED"
  },
  "timestamp": "2025-09-12T15:46:22.764500",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:22,764 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INDIVIDUAL_TEST_RESULT
2025-09-12 15:46:22,764 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000052",
  "operation_type": "OPERATION",
  "operation": "INDIVIDUAL_TEST_RESULT",
  "details": {
    "test_type": "COMPREHENSIVE_TESTS",
    "test_file": "FAILED tests/test_comprehensive.py",
    "test_name": "TestAdvancedSecurity",
    "status": "FAILED",
    "full_line": "FAILED tests/test_comprehensive.py::TestAdvancedSecurity::test_input_validation - AssertionError: assert 'injection' in 'input contains potentially dangerous content'"
  },
  "timestamp": "2025-09-12T15:46:22.764577",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:22,764 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INDIVIDUAL_TEST_RESULT
2025-09-12 15:46:22,764 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000052",
  "operation_type": "OPERATION",
  "operation": "INDIVIDUAL_TEST_RESULT",
  "details": {
    "test_type": "COMPREHENSIVE_TESTS",
    "test_file": "ERROR tests/test_comprehensive.py",
    "test_name": "TestWatchlistManager",
    "status": "ERROR",
    "full_line": "ERROR tests/test_comprehensive.py::TestWatchlistManager::test_watchlist_initialization - AttributeError: __aenter__"
  },
  "timestamp": "2025-09-12T15:46:22.764625",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:22,764 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: INDIVIDUAL_TEST_RESULT
2025-09-12 15:46:22,764 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000052",
  "operation_type": "OPERATION",
  "operation": "INDIVIDUAL_TEST_RESULT",
  "details": {
    "test_type": "COMPREHENSIVE_TESTS",
    "test_file": "ERROR tests/test_comprehensive.py",
    "test_name": "TestWatchlistManager",
    "status": "ERROR",
    "full_line": "ERROR tests/test_comprehensive.py::TestWatchlistManager::test_create_watchlist - AttributeError: __aenter__"
  },
  "timestamp": "2025-09-12T15:46:22.764670",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:22,764 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: PYTEST_OUTPUT_PARSED_COMPREHENSIVE_TESTS
2025-09-12 15:46:22,764 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000052",
  "operation_type": "OPERATION",
  "operation": "PYTEST_OUTPUT_PARSED_COMPREHENSIVE_TESTS",
  "details": {
    "total_tests": 9,
    "passed": 3,
    "failed": 2,
    "errors": 4,
    "success_rate": 33.33333333333333,
    "test_results": [
      {
        "test_file": "tests/test_comprehensive.py",
        "test_name": "TestErrorHandler",
        "status": "PASSED",
        "line": "tests/test_comprehensive.py::TestErrorHandler::test_user_friendly_messages PASSED"
      },
      {
        "test_file": "tests/test_comprehensive.py",
        "test_name": "TestWatchlistManager",
        "status": "ERROR",
        "line": "tests/test_comprehensive.py::TestWatchlistManager::test_watchlist_initialization ERROR"
      },
      {
        "test_file": "tests/test_comprehensive.py",
        "test_name": "TestWatchlistManager",
        "status": "ERROR",
        "line": "tests/test_comprehensive.py::TestWatchlistManager::test_create_watchlist ERROR"
      },
      {
        "test_file": "tests/test_comprehensive.py",
        "test_name": "TestDiscordUX",
        "status": "PASSED",
        "line": "tests/test_comprehensive.py::TestDiscordUX::test_interactive_embeds PASSED"
      },
      {
        "test_file": "tests/test_comprehensive.py",
        "test_name": "TestAdvancedSecurity",
        "status": "FAILED",
        "line": "tests/test_comprehensive.py::TestAdvancedSecurity::test_input_validation FAILED"
      },
      {
        "test_file": "tests/test_comprehensive.py",
        "test_name": "TestAdvancedSecurity",
        "status": "PASSED",
        "line": "tests/test_comprehensive.py::TestAdvancedSecurity::test_symbol_validation PASSED"
      },
      {
        "test_file": "FAILED tests/test_comprehensive.py",
        "test_name": "TestAdvancedSecurity",
        "status": "FAILED",
        "line": "FAILED tests/test_comprehensive.py::TestAdvancedSecurity::test_input_validation - AssertionError: assert 'injection' in 'input contains potentially dangerous content'"
      },
      {
        "test_file": "ERROR tests/test_comprehensive.py",
        "test_name": "TestWatchlistManager",
        "status": "ERROR",
        "line": "ERROR tests/test_comprehensive.py::TestWatchlistManager::test_watchlist_initialization - AttributeError: __aenter__"
      },
      {
        "test_file": "ERROR tests/test_comprehensive.py",
        "test_name": "TestWatchlistManager",
        "status": "ERROR",
        "line": "ERROR tests/test_comprehensive.py::TestWatchlistManager::test_create_watchlist - AttributeError: __aenter__"
      }
    ]
  },
  "timestamp": "2025-09-12T15:46:22.764716",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:22,765 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: GENERATE_FINAL_REPORT
2025-09-12 15:46:22,765 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000056",
  "operation_type": "OPERATION",
  "operation": "GENERATE_FINAL_REPORT",
  "details": {},
  "timestamp": "2025-09-12T15:46:22.765100",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:22,765 | test_operations | INFO | log_operation:190 | 🔧 OPERATION: FINAL_REPORT_GENERATED
2025-09-12 15:46:22,765 | test_operations | DEBUG | log_operation:191 | Operation details: {
  "operation_id": "OP_000056",
  "operation_type": "OPERATION",
  "operation": "FINAL_REPORT_GENERATED",
  "details": {
    "session_id": "test_session_1757706366",
    "total_duration_seconds": 16.308329820632935,
    "test_results": {
      "UNIT_TESTS": {
        "total": 16,
        "passed": 14,
        "failed": 2,
        "errors": 0,
        "results": [
          {
            "test_file": "tests/unit/test_options_greeks_calculator.py",
            "test_name": "test_estimate_new_delta PASSED",
            "status": "PASSED",
            "line": "tests/unit/test_options_greeks_calculator.py::test_estimate_new_delta PASSED"
          },
          {
            "test_file": "tests/unit/test_options_greeks_calculator.py",
            "test_name": "test_estimate_new_price PASSED",
            "status": "PASSED",
            "line": "tests/unit/test_options_greeks_calculator.py::test_estimate_new_price PASSED"
          },
          {
            "test_file": "tests/unit/test_options_greeks_calculator.py",
            "test_name": "test_breakeven_calculation PASSED",
            "status": "PASSED",
            "line": "tests/unit/test_options_greeks_calculator.py::test_breakeven_calculation PASSED"
          },
          {
            "test_file": "tests/unit/test_options_greeks_calculator.py",
            "test_name": "test_probability_of_profit PASSED",
            "status": "PASSED",
            "line": "tests/unit/test_options_greeks_calculator.py::test_probability_of_profit PASSED"
          },
          {
            "test_file": "tests/unit/test_options_greeks_calculator.py",
            "test_name": "test_time_decay_effect PASSED",
            "status": "PASSED",
            "line": "tests/unit/test_options_greeks_calculator.py::test_time_decay_effect PASSED"
          },
          {
            "test_file": "tests/unit/test_options_greeks_calculator.py",
            "test_name": "test_volatility_effect PASSED",
            "status": "PASSED",
            "line": "tests/unit/test_options_greeks_calculator.py::test_volatility_effect PASSED"
          },
          {
            "test_file": "tests/unit/test_strategy_calculator.py",
            "test_name": "test_calculate_entry_strategy PASSED",
            "status": "PASSED",
            "line": "tests/unit/test_strategy_calculator.py::test_calculate_entry_strategy PASSED"
          },
          {
            "test_file": "tests/unit/test_strategy_calculator.py",
            "test_name": "test_estimate_options_strategy PASSED",
            "status": "PASSED",
            "line": "tests/unit/test_strategy_calculator.py::test_estimate_options_strategy PASSED"
          },
          {
            "test_file": "tests/unit/test_strategy_calculator.py",
            "test_name": "test_risk_classification PASSED",
            "status": "PASSED",
            "line": "tests/unit/test_strategy_calculator.py::test_risk_classification PASSED"
          },
          {
            "test_file": "tests/unit/test_strategy_calculator.py",
            "test_name": "test_stop_loss_calculation PASSED",
            "status": "PASSED",
            "line": "tests/unit/test_strategy_calculator.py::test_stop_loss_calculation PASSED"
          },
          {
            "test_file": "tests/unit/test_strategy_calculator.py",
            "test_name": "test_take_profit_calculation PASSED",
            "status": "PASSED",
            "line": "tests/unit/test_strategy_calculator.py::test_take_profit_calculation PASSED"
          },
          {
            "test_file": "tests/unit/test_strategy_calculator.py",
            "test_name": "test_supertrend_integration PASSED",
            "status": "PASSED",
            "line": "tests/unit/test_strategy_calculator.py::test_supertrend_integration PASSED"
          },
          {
            "test_file": "tests/unit/test_strategy_calculator.py",
            "test_name": "test_supertrend_flip_tracking PASSED",
            "status": "PASSED",
            "line": "tests/unit/test_strategy_calculator.py::test_supertrend_flip_tracking PASSED"
          },
          {
            "test_file": "tests/unit/test_strategy_calculator.py",
            "test_name": "test_supertrend_logging PASSED",
            "status": "PASSED",
            "line": "tests/unit/test_strategy_calculator.py::test_supertrend_logging PASSED"
          },
          {
            "test_file": "FAILED tests/unit/test_ml_sentiment_analyzer.py",
            "test_name": "test_negative_sentiment_analysis - AssertionError: assert 'neutral' == 'negative'",
            "status": "FAILED",
            "line": "FAILED tests/unit/test_ml_sentiment_analyzer.py::test_negative_sentiment_analysis - AssertionError: assert 'neutral' == 'negative'"
          },
          {
            "test_file": "FAILED tests/unit/test_ml_sentiment_analyzer.py",
            "test_name": "test_sentiment_extraction - AssertionError: assert 'risks' in ['weak']",
            "status": "FAILED",
            "line": "FAILED tests/unit/test_ml_sentiment_analyzer.py::test_sentiment_extraction - AssertionError: assert 'risks' in ['weak']"
          }
        ]
      },
      "INTEGRATION_TESTS": {
        "total": 0,
        "passed": 0,
        "failed": 0,
        "errors": 0,
        "results": []
      },
      "COMPREHENSIVE_TESTS": {
        "total": 9,
        "passed": 3,
        "failed": 2,
        "errors": 4,
        "results": [
          {
            "test_file": "tests/test_comprehensive.py",
            "test_name": "TestErrorHandler",
            "status": "PASSED",
            "line": "tests/test_comprehensive.py::TestErrorHandler::test_user_friendly_messages PASSED"
          },
          {
            "test_file": "tests/test_comprehensive.py",
            "test_name": "TestWatchlistManager",
            "status": "ERROR",
            "line": "tests/test_comprehensive.py::TestWatchlistManager::test_watchlist_initialization ERROR"
          },
          {
            "test_file": "tests/test_comprehensive.py",
            "test_name": "TestWatchlistManager",
            "status": "ERROR",
            "line": "tests/test_comprehensive.py::TestWatchlistManager::test_create_watchlist ERROR"
          },
          {
            "test_file": "tests/test_comprehensive.py",
            "test_name": "TestDiscordUX",
            "status": "PASSED",
            "line": "tests/test_comprehensive.py::TestDiscordUX::test_interactive_embeds PASSED"
          },
          {
            "test_file": "tests/test_comprehensive.py",
            "test_name": "TestAdvancedSecurity",
            "status": "FAILED",
            "line": "tests/test_comprehensive.py::TestAdvancedSecurity::test_input_validation FAILED"
          },
          {
            "test_file": "tests/test_comprehensive.py",
            "test_name": "TestAdvancedSecurity",
            "status": "PASSED",
            "line": "tests/test_comprehensive.py::TestAdvancedSecurity::test_symbol_validation PASSED"
          },
          {
            "test_file": "FAILED tests/test_comprehensive.py",
            "test_name": "TestAdvancedSecurity",
            "status": "FAILED",
            "line": "FAILED tests/test_comprehensive.py::TestAdvancedSecurity::test_input_validation - AssertionError: assert 'injection' in 'input contains potentially dangerous content'"
          },
          {
            "test_file": "ERROR tests/test_comprehensive.py",
            "test_name": "TestWatchlistManager",
            "status": "ERROR",
            "line": "ERROR tests/test_comprehensive.py::TestWatchlistManager::test_watchlist_initialization - AttributeError: __aenter__"
          },
          {
            "test_file": "ERROR tests/test_comprehensive.py",
            "test_name": "TestWatchlistManager",
            "status": "ERROR",
            "line": "ERROR tests/test_comprehensive.py::TestWatchlistManager::test_create_watchlist - AttributeError: __aenter__"
          }
        ]
      }
    },
    "import_errors": [],
    "test_errors": [
      {
        "test_type": "unit_tests",
        "error": "log_performance_metric() takes 2 positional arguments but 3 were given",
        "timestamp": "2025-09-12T15:46:11.610099"
      },
      {
        "test_type": "integration_tests",
        "error": "log_performance_metric() takes 2 positional arguments but 3 were given",
        "timestamp": "2025-09-12T15:46:15.937657"
      },
      {
        "test_type": "comprehensive_tests",
        "error": "log_performance_metric() takes 2 positional arguments but 3 were given",
        "timestamp": "2025-09-12T15:46:22.765040"
      }
    ],
    "summary": {
      "total_tests": 25,
      "total_passed": 17,
      "total_failed": 4,
      "total_errors": 4,
      "overall_success_rate": 68.0
    },
    "timestamp": "2025-09-12T15:46:22.765150"
  },
  "timestamp": "2025-09-12T15:46:22.765154",
  "session_id": "test_session_1757706366"
}
2025-09-12 15:46:22,766 | comprehensive_test | INFO | generate_summary_report:455 | 📊 TEST SUMMARY GENERATED: test_results/detailed_logs/test_summary_test_session_1757706366.json
2025-09-12 15:46:22,766 | comprehensive_test | INFO | generate_summary_report:456 | Total operations: 58
2025-09-12 15:46:22,766 | comprehensive_test | INFO | generate_summary_report:457 | Total duration: 16.310 seconds
2025-09-12 15:46:22,766 | comprehensive_test | INFO | generate_summary_report:458 | Total errors: 3
