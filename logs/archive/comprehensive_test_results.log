2025-09-12 16:25:15,183 - INFO - TEST A - Technical Analysis: Test passed successfully
2025-09-12 16:25:15,751 - INFO - TEST A - Data Providers: Test passed successfully
2025-09-12 16:25:16,495 - INFO - TEST A - AI Chat Processor: Test passed successfully
2025-09-12 16:25:16,496 - INFO - TEST A - Market Data Service: Test passed successfully
2025-09-12 16:25:16,497 - INFO - TEST A - Trading Signals: Test passed successfully
2025-09-12 16:25:16,497 - INFO - TEST A - Webhook Processing: Test passed successfully
2025-09-12 16:25:16,497 - INFO - TEST A - Discord Bot Commands: Test passed successfully
2025-09-12 16:25:16,497 - INFO - TEST F - Configuration Management: Test failed with error: cannot import name 'Config<PERSON>anager' from 'src.core.config_manager' (/home/<USER>/Desktop/tradingview-automatio/src/core/config_manager.py)
2025-09-12 16:25:16,497 - ERROR - 🚨 CRITICAL TEST FAILURE - Configuration Management: Test failed with error: cannot import name 'ConfigManager' from 'src.core.config_manager' (/home/<USER>/Desktop/tradingview-automatio/src/core/config_manager.py)
2025-09-12 16:25:16,497 - INFO - TEST A - Error Handling: Test passed successfully
2025-09-12 16:25:16,839 - INFO - TEST A - Database Connection: Test passed successfully
