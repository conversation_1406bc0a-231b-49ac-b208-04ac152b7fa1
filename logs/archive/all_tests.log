============================= test session starts ==============================
platform linux -- Python 3.12.3, pytest-8.4.2, pluggy-1.6.0 -- /home/<USER>/Desktop/tradingview-automatio/venv/bin/python3
cachedir: .pytest_cache
rootdir: /home/<USER>/Desktop/tradingview-automatio
configfile: pytest.ini
testpaths: tests
plugins: asyncio-1.2.0, cov-7.0.0, anyio-4.10.0
asyncio: mode=Mode.AUTO, debug=False, asyncio_default_fixture_loop_scope=None, asyncio_default_test_loop_scope=function
collecting ... collected 444 items / 55 errors

==================================== ERRORS ====================================
_______________ ERROR collecting tests/e2e/test_bot_commands.py ________________
tests/e2e/test_bot_commands.py:15: in <module>
    from src.bot.client import TradingBot
src/bot/client.py:28: in <module>
    from .watchlist_alerts import initialize_watchlist_alerts
src/bot/watchlist_alerts.py:18: in <module>
    from src.shared.watchlist.supabase_manager import SupabaseWatchlistManager
src/shared/watchlist/supabase_manager.py:10: in <module>
    from ..database.supabase_base import BaseSupabaseClient
src/shared/database/__init__.py:8: in <module>
    from .db_manager import (
src/shared/database/db_manager.py:32: in <module>
    class DatabaseConnectionManager:
src/shared/database/db_manager.py:51: in DatabaseConnectionManager
    _supabase_client: Optional[BaseSupabaseClient] = None
                               ^^^^^^^^^^^^^^^^^^
E   NameError: name 'BaseSupabaseClient' is not defined
_________________ ERROR collecting tests/load/test_bot_load.py _________________
ImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/load/test_bot_load.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/load/test_bot_load.py:19: in <module>
    from src.bot.pipeline.commands.ask.pipeline import execute_ask_pipeline
E   ImportError: cannot import name 'execute_ask_pipeline' from 'src.bot.pipeline.commands.ask.pipeline' (/home/<USER>/Desktop/tradingview-automatio/src/bot/pipeline/commands/ask/pipeline.py)
_________________ ERROR collecting tests/load/test_bot_load.py _________________
ImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/load/test_bot_load.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/load/test_bot_load.py:19: in <module>
    from src.bot.pipeline.commands.ask.pipeline import execute_ask_pipeline
E   ImportError: cannot import name 'execute_ask_pipeline' from 'src.bot.pipeline.commands.ask.pipeline' (/home/<USER>/Desktop/tradingview-automatio/src/bot/pipeline/commands/ask/pipeline.py)
_______________ ERROR collecting tests/test_advanced_security.py _______________
ImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/test_advanced_security.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/test_advanced_security.py:5: in <module>
    import pyotp
E   ModuleNotFoundError: No module named 'pyotp'
_______________ ERROR collecting tests/test_advanced_security.py _______________
ImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/test_advanced_security.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/test_advanced_security.py:5: in <module>
    import pyotp
E   ModuleNotFoundError: No module named 'pyotp'
_______________ ERROR collecting tests/test_ai_chat_processor.py _______________
ImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/test_ai_chat_processor.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/test_ai_chat_processor.py:15: in <module>
    from src.bot.pipeline.commands.ask.stages.ai_chat_processor import processor
E   ModuleNotFoundError: No module named 'src.bot.pipeline.commands.ask.stages.ai_chat_processor'
_______________ ERROR collecting tests/test_ai_chat_processor.py _______________
ImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/test_ai_chat_processor.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/test_ai_chat_processor.py:15: in <module>
    from src.bot.pipeline.commands.ask.stages.ai_chat_processor import processor
E   ModuleNotFoundError: No module named 'src.bot.pipeline.commands.ask.stages.ai_chat_processor'
_________________ ERROR collecting tests/test_api_endpoints.py _________________
ImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/test_api_endpoints.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/test_api_endpoints.py:21: in <module>
    from src.api.main import create_application
src/api/main.py:3: in <module>
    import uvicorn
E   ModuleNotFoundError: No module named 'uvicorn'
_________________ ERROR collecting tests/test_api_endpoints.py _________________
ImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/test_api_endpoints.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/test_api_endpoints.py:21: in <module>
    from src.api.main import create_application
src/api/main.py:3: in <module>
    import uvicorn
E   ModuleNotFoundError: No module named 'uvicorn'
______________ ERROR collecting tests/test_audit_visualization.py ______________
tests/test_audit_visualization.py:72: in <module>
    from src.bot.audit.request_visualizer import request_visualizer, AuditLevel
src/bot/audit/__init__.py:6: in <module>
    from .request_visualizer import request_visualizer, setup_request_visualizer, AuditLevel
src/bot/audit/request_visualizer.py:35: in <module>
    class RequestVisualizer:
src/bot/audit/request_visualizer.py:41: in RequestVisualizer
    def __init__(self, bot: Optional[discord.Client] = None):
                                     ^^^^^^^^^^^^^^
E   AttributeError: type object 'MockDiscord' has no attribute 'Client'
______________ ERROR collecting tests/test_audit_visualization.py ______________
tests/test_audit_visualization.py:72: in <module>
    from src.bot.audit.request_visualizer import request_visualizer, AuditLevel
src/bot/audit/__init__.py:6: in <module>
    from .request_visualizer import request_visualizer, setup_request_visualizer, AuditLevel
src/bot/audit/request_visualizer.py:35: in <module>
    class RequestVisualizer:
src/bot/audit/request_visualizer.py:41: in RequestVisualizer
    def __init__(self, bot: Optional[discord.Client] = None):
                                     ^^^^^^^^^^^^^^
E   AttributeError: type object 'MockDiscord' has no attribute 'Client'
____________ ERROR collecting tests/test_backward_compatibility.py _____________
ImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/test_backward_compatibility.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/test_backward_compatibility.py:14: in <module>
    from src.shared.ai_services.ai_service_wrapper import AIChatProcessor as CanonicalAIChatProcessor
E   ImportError: cannot import name 'AIChatProcessor' from 'src.shared.ai_services.ai_service_wrapper' (/home/<USER>/Desktop/tradingview-automatio/src/shared/ai_services/ai_service_wrapper.py)
____________ ERROR collecting tests/test_backward_compatibility.py _____________
ImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/test_backward_compatibility.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/test_backward_compatibility.py:14: in <module>
    from src.shared.ai_services.ai_service_wrapper import AIChatProcessor as CanonicalAIChatProcessor
E   ImportError: cannot import name 'AIChatProcessor' from 'src.shared.ai_services.ai_service_wrapper' (/home/<USER>/Desktop/tradingview-automatio/src/shared/ai_services/ai_service_wrapper.py)
_______________ ERROR collecting tests/test_contextual_logger.py _______________
ImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/test_contextual_logger.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/test_contextual_logger.py:4: in <module>
    from src.core.logger import get_logger, BaseLogger
E   ImportError: cannot import name 'BaseLogger' from 'src.core.logger' (/home/<USER>/Desktop/tradingview-automatio/src/core/logger.py)
_______________ ERROR collecting tests/test_contextual_logger.py _______________
ImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/test_contextual_logger.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/test_contextual_logger.py:4: in <module>
    from src.core.logger import get_logger, BaseLogger
E   ImportError: cannot import name 'BaseLogger' from 'src.core.logger' (/home/<USER>/Desktop/tradingview-automatio/src/core/logger.py)
_____________ ERROR collecting tests/test_data_provider_system.py ______________
ImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/test_data_provider_system.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/test_data_provider_system.py:20: in <module>
    from src.data.providers.manager import DataProviderManager, initialize_providers
src/data/__init__.py:9: in <module>
    from .providers import (
E   ModuleNotFoundError: No module named 'src.data.providers'
_____________ ERROR collecting tests/test_data_provider_system.py ______________
ImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/test_data_provider_system.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/test_data_provider_system.py:20: in <module>
    from src.data.providers.manager import DataProviderManager, initialize_providers
src/data/__init__.py:9: in <module>
    from .providers import (
E   ModuleNotFoundError: No module named 'src.data.providers'
________________ ERROR collecting tests/test_data_providers.py _________________
ImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/test_data_providers.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/test_data_providers.py:32: in <module>
    from src.data.providers import (
src/data/__init__.py:9: in <module>
    from .providers import (
E   ModuleNotFoundError: No module named 'src.data.providers'
________________ ERROR collecting tests/test_data_providers.py _________________
ImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/test_data_providers.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/test_data_providers.py:32: in <module>
    from src.data.providers import (
src/data/__init__.py:9: in <module>
    from .providers import (
E   ModuleNotFoundError: No module named 'src.data.providers'
_____________ ERROR collecting tests/test_data_quality_scoring.py ______________
ImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/test_data_quality_scoring.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/test_data_quality_scoring.py:10: in <module>
    from src.core.data_quality import (
E   ModuleNotFoundError: No module named 'src.core.data_quality'
_____________ ERROR collecting tests/test_data_quality_scoring.py ______________
ImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/test_data_quality_scoring.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/test_data_quality_scoring.py:10: in <module>
    from src.core.data_quality import (
E   ModuleNotFoundError: No module named 'src.core.data_quality'
_______________ ERROR collecting tests/test_enhanced_analysis.py _______________
ImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/test_enhanced_analysis.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/test_enhanced_analysis.py:17: in <module>
    from src.analysis.technical.price_targets import (
E   ModuleNotFoundError: No module named 'src.analysis.technical.price_targets'
_______________ ERROR collecting tests/test_enhanced_analysis.py _______________
ImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/test_enhanced_analysis.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/test_enhanced_analysis.py:17: in <module>
    from src.analysis.technical.price_targets import (
E   ModuleNotFoundError: No module named 'src.analysis.technical.price_targets'
_____________ ERROR collecting tests/test_fallback_remediation.py ______________
ImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/test_fallback_remediation.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/test_fallback_remediation.py:9: in <module>
    from src.core.config_validator import ConfigValidator
E   ModuleNotFoundError: No module named 'src.core.config_validator'
_____________ ERROR collecting tests/test_fallback_remediation.py ______________
ImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/test_fallback_remediation.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/test_fallback_remediation.py:9: in <module>
    from src.core.config_validator import ConfigValidator
E   ModuleNotFoundError: No module named 'src.core.config_validator'
_________________ ERROR collecting tests/test_full_analysis.py _________________
ImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/test_full_analysis.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/test_full_analysis.py:27: in <module>
    from src.shared.ai_services.ai_chat_processor import AIChatProcessor
E   ImportError: cannot import name 'AIChatProcessor' from 'src.shared.ai_services.ai_chat_processor' (/home/<USER>/Desktop/tradingview-automatio/src/shared/ai_services/ai_chat_processor.py)
------------------------------- Captured stdout --------------------------------
✅ Loaded environment variables from .env file.
_________________ ERROR collecting tests/test_full_analysis.py _________________
ImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/test_full_analysis.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/test_full_analysis.py:27: in <module>
    from src.shared.ai_services.ai_chat_processor import AIChatProcessor
E   ImportError: cannot import name 'AIChatProcessor' from 'src.shared.ai_services.ai_chat_processor' (/home/<USER>/Desktop/tradingview-automatio/src/shared/ai_services/ai_chat_processor.py)
------------------------------- Captured stdout --------------------------------
✅ Loaded environment variables from .env file.
_______________ ERROR collecting tests/test_market_hours_fix.py ________________
ImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/test_market_hours_fix.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/test_market_hours_fix.py:9: in <module>
    from src.bot.pipeline.commands.ask.stages.ai_chat_processor import AIChatProcessor
E   ModuleNotFoundError: No module named 'src.bot.pipeline.commands.ask.stages.ai_chat_processor'
_______________ ERROR collecting tests/test_market_hours_fix.py ________________
ImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/test_market_hours_fix.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/test_market_hours_fix.py:9: in <module>
    from src.bot.pipeline.commands.ask.stages.ai_chat_processor import AIChatProcessor
E   ModuleNotFoundError: No module named 'src.bot.pipeline.commands.ask.stages.ai_chat_processor'
__________________ ERROR collecting tests/test_metrics_api.py __________________
ImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/test_metrics_api.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/test_metrics_api.py:5: in <module>
    from src.api.main import app
src/api/main.py:3: in <module>
    import uvicorn
E   ModuleNotFoundError: No module named 'uvicorn'
__________________ ERROR collecting tests/test_metrics_api.py __________________
ImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/test_metrics_api.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/test_metrics_api.py:5: in <module>
    from src.api.main import app
src/api/main.py:3: in <module>
    import uvicorn
E   ModuleNotFoundError: No module named 'uvicorn'
___________________ ERROR collecting tests/test_mock_fix.py ____________________
ImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/test_mock_fix.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/test_mock_fix.py:9: in <module>
    from src.utils.fix_mock_issue import is_mock_object, restore_original_function, fix_execute_ask_pipeline_mock, verify_function_integrity
E   ModuleNotFoundError: No module named 'src.utils.fix_mock_issue'
___________________ ERROR collecting tests/test_mock_fix.py ____________________
ImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/test_mock_fix.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/test_mock_fix.py:9: in <module>
    from src.utils.fix_mock_issue import is_mock_object, restore_original_function, fix_execute_ask_pipeline_mock, verify_function_integrity
E   ModuleNotFoundError: No module named 'src.utils.fix_mock_issue'
___________ ERROR collecting tests/test_multi_symbol_integration.py ____________
ImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/test_multi_symbol_integration.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/test_multi_symbol_integration.py:13: in <module>
    from src.bot.pipeline.commands.ask.pipeline import execute_ask_pipeline
E   ImportError: cannot import name 'execute_ask_pipeline' from 'src.bot.pipeline.commands.ask.pipeline' (/home/<USER>/Desktop/tradingview-automatio/src/bot/pipeline/commands/ask/pipeline.py)
___________ ERROR collecting tests/test_multi_symbol_integration.py ____________
ImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/test_multi_symbol_integration.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/test_multi_symbol_integration.py:13: in <module>
    from src.bot.pipeline.commands.ask.pipeline import execute_ask_pipeline
E   ImportError: cannot import name 'execute_ask_pipeline' from 'src.bot.pipeline.commands.ask.pipeline' (/home/<USER>/Desktop/tradingview-automatio/src/bot/pipeline/commands/ask/pipeline.py)
_______________ ERROR collecting tests/test_outlier_detection.py _______________
ImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/test_outlier_detection.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/test_outlier_detection.py:11: in <module>
    from src.core.outlier_detector import (
E   ModuleNotFoundError: No module named 'src.core.outlier_detector'
_______________ ERROR collecting tests/test_outlier_detection.py _______________
ImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/test_outlier_detection.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/test_outlier_detection.py:11: in <module>
    from src.core.outlier_detector import (
E   ModuleNotFoundError: No module named 'src.core.outlier_detector'
______________ ERROR collecting tests/test_real_data_providers.py ______________
venv/lib/python3.12/site-packages/_pytest/python.py:498: in importtestmodule
    mod = import_path(
venv/lib/python3.12/site-packages/_pytest/pathlib.py:587: in import_path
    importlib.import_module(module_name)
/usr/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
<frozen importlib._bootstrap>:1387: in _gcd_import
    ???
<frozen importlib._bootstrap>:1360: in _find_and_load
    ???
<frozen importlib._bootstrap>:1331: in _find_and_load_unlocked
    ???
<frozen importlib._bootstrap>:935: in _load_unlocked
    ???
venv/lib/python3.12/site-packages/_pytest/assertion/rewrite.py:177: in exec_module
    source_stat, co = _rewrite_test(fn, self.config)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
venv/lib/python3.12/site-packages/_pytest/assertion/rewrite.py:357: in _rewrite_test
    tree = ast.parse(source, filename=strfn)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
/usr/lib/python3.12/ast.py:52: in parse
    return compile(source, filename, mode, flags,
E     File "/home/<USER>/Desktop/tradingview-automatio/tests/test_real_data_providers.py", line 36
E       from src.api.data.market_data_service import MarketDataService
E       ^^^^
E   IndentationError: expected an indented block after 'try' statement on line 35
______________ ERROR collecting tests/test_real_data_providers.py ______________
venv/lib/python3.12/site-packages/_pytest/python.py:498: in importtestmodule
    mod = import_path(
venv/lib/python3.12/site-packages/_pytest/pathlib.py:587: in import_path
    importlib.import_module(module_name)
/usr/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
<frozen importlib._bootstrap>:1387: in _gcd_import
    ???
<frozen importlib._bootstrap>:1360: in _find_and_load
    ???
<frozen importlib._bootstrap>:1331: in _find_and_load_unlocked
    ???
<frozen importlib._bootstrap>:935: in _load_unlocked
    ???
venv/lib/python3.12/site-packages/_pytest/assertion/rewrite.py:177: in exec_module
    source_stat, co = _rewrite_test(fn, self.config)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
venv/lib/python3.12/site-packages/_pytest/assertion/rewrite.py:357: in _rewrite_test
    tree = ast.parse(source, filename=strfn)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
/usr/lib/python3.12/ast.py:52: in parse
    return compile(source, filename, mode, flags,
E     File "/home/<USER>/Desktop/tradingview-automatio/tests/test_real_data_providers.py", line 36
E       from src.api.data.market_data_service import MarketDataService
E       ^^^^
E   IndentationError: expected an indented block after 'try' statement on line 35
_____________ ERROR collecting tests/test_recommendation_engine.py _____________
ImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/test_recommendation_engine.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/test_recommendation_engine.py:4: in <module>
    from src.analysis.ai.recommendation_engine import AIRecommendationEngine, Recommendation
src/analysis/ai/recommendation_engine.py:5: in <module>
    from src.data.models.stock_data import AnalysisResult
src/data/__init__.py:9: in <module>
    from .providers import (
E   ModuleNotFoundError: No module named 'src.data.providers'
_____________ ERROR collecting tests/test_recommendation_engine.py _____________
ImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/test_recommendation_engine.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/test_recommendation_engine.py:4: in <module>
    from src.analysis.ai.recommendation_engine import AIRecommendationEngine, Recommendation
src/analysis/ai/recommendation_engine.py:5: in <module>
    from src.data.models.stock_data import AnalysisResult
src/data/__init__.py:9: in <module>
    from .providers import (
E   ModuleNotFoundError: No module named 'src.data.providers'
________________ ERROR collecting tests/test_refactored_bot.py _________________
ImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/test_refactored_bot.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/test_refactored_bot.py:7: in <module>
    from src.bot.core.bot import create_bot
src/bot/core/bot.py:10: in <module>
    from discord import Intents
E   ImportError: cannot import name 'Intents' from 'MockDiscord' (unknown location)
________________ ERROR collecting tests/test_refactored_bot.py _________________
ImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/test_refactored_bot.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/test_refactored_bot.py:7: in <module>
    from src.bot.core.bot import create_bot
src/bot/core/bot.py:10: in <module>
    from discord import Intents
E   ImportError: cannot import name 'Intents' from 'MockDiscord' (unknown location)
______________ ERROR collecting tests/test_response_generator.py _______________
ImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/test_response_generator.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/test_response_generator.py:2: in <module>
    from src.core.response_generator import ResponseGenerator, ResponseType
src/core/response_generator.py:11: in <module>
    from .metrics_tracker import metrics_tracker
E   ModuleNotFoundError: No module named 'src.core.metrics_tracker'
______________ ERROR collecting tests/test_response_generator.py _______________
ImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/test_response_generator.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/test_response_generator.py:2: in <module>
    from src.core.response_generator import ResponseGenerator, ResponseType
src/core/response_generator.py:11: in <module>
    from .metrics_tracker import metrics_tracker
E   ModuleNotFoundError: No module named 'src.core.metrics_tracker'
______________ ERROR collecting tests/test_simple_correlation.py _______________
ImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/test_simple_correlation.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/test_simple_correlation.py:13: in <module>
    from src.database.supabase_client import test_supabase_connection
E   ImportError: cannot import name 'test_supabase_connection' from 'src.database.supabase_client' (/home/<USER>/Desktop/tradingview-automatio/src/database/supabase_client.py)
______________ ERROR collecting tests/test_simple_correlation.py _______________
ImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/test_simple_correlation.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/test_simple_correlation.py:13: in <module>
    from src.database.supabase_client import test_supabase_connection
E   ImportError: cannot import name 'test_supabase_connection' from 'src.database.supabase_client' (/home/<USER>/Desktop/tradingview-automatio/src/database/supabase_client.py)
_____________ ERROR collecting tests/test_stale_data_detection.py ______________
ImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/test_stale_data_detection.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/test_stale_data_detection.py:9: in <module>
    from src.core.stale_data_detector import (
E   ModuleNotFoundError: No module named 'src.core.stale_data_detector'
_____________ ERROR collecting tests/test_stale_data_detection.py ______________
ImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/test_stale_data_detection.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/test_stale_data_detection.py:9: in <module>
    from src.core.stale_data_detector import (
E   ModuleNotFoundError: No module named 'src.core.stale_data_detector'
______________ ERROR collecting tests/test_supply_demand_zones.py ______________
ImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/test_supply_demand_zones.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/test_supply_demand_zones.py:24: in <module>
    from src.data.providers.yfinance_provider import YFinanceProvider
src/data/__init__.py:9: in <module>
    from .providers import (
E   ModuleNotFoundError: No module named 'src.data.providers'
______________ ERROR collecting tests/test_supply_demand_zones.py ______________
ImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/test_supply_demand_zones.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/test_supply_demand_zones.py:24: in <module>
    from src.data.providers.yfinance_provider import YFinanceProvider
src/data/__init__.py:9: in <module>
    from .providers import (
E   ModuleNotFoundError: No module named 'src.data.providers'
________________ ERROR collecting tests/test_uniform_alerts.py _________________
ImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/test_uniform_alerts.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/test_uniform_alerts.py:10: in <module>
    from text_parser import PineScriptAlertParser, ParsedAlert
E   ModuleNotFoundError: No module named 'text_parser'
________________ ERROR collecting tests/test_uniform_alerts.py _________________
ImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/test_uniform_alerts.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/test_uniform_alerts.py:10: in <module>
    from text_parser import PineScriptAlertParser, ParsedAlert
E   ModuleNotFoundError: No module named 'text_parser'
________________ ERROR collecting tests/test_volume_analyzer.py ________________
venv/lib/python3.12/site-packages/_pytest/python.py:498: in importtestmodule
    mod = import_path(
venv/lib/python3.12/site-packages/_pytest/pathlib.py:587: in import_path
    importlib.import_module(module_name)
/usr/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
<frozen importlib._bootstrap>:1387: in _gcd_import
    ???
<frozen importlib._bootstrap>:1360: in _find_and_load
    ???
<frozen importlib._bootstrap>:1331: in _find_and_load_unlocked
    ???
<frozen importlib._bootstrap>:935: in _load_unlocked
    ???
venv/lib/python3.12/site-packages/_pytest/assertion/rewrite.py:177: in exec_module
    source_stat, co = _rewrite_test(fn, self.config)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
venv/lib/python3.12/site-packages/_pytest/assertion/rewrite.py:357: in _rewrite_test
    tree = ast.parse(source, filename=strfn)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
/usr/lib/python3.12/ast.py:52: in parse
    return compile(source, filename, mode, flags,
E     File "/home/<USER>/Desktop/tradingview-automatio/tests/test_volume_analyzer.py", line 157
E       def test_volume_divergence():
E   SyntaxError: expected 'except' or 'finally' block
________________ ERROR collecting tests/test_volume_analyzer.py ________________
venv/lib/python3.12/site-packages/_pytest/python.py:498: in importtestmodule
    mod = import_path(
venv/lib/python3.12/site-packages/_pytest/pathlib.py:587: in import_path
    importlib.import_module(module_name)
/usr/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
<frozen importlib._bootstrap>:1387: in _gcd_import
    ???
<frozen importlib._bootstrap>:1360: in _find_and_load
    ???
<frozen importlib._bootstrap>:1331: in _find_and_load_unlocked
    ???
<frozen importlib._bootstrap>:935: in _load_unlocked
    ???
venv/lib/python3.12/site-packages/_pytest/assertion/rewrite.py:177: in exec_module
    source_stat, co = _rewrite_test(fn, self.config)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
venv/lib/python3.12/site-packages/_pytest/assertion/rewrite.py:357: in _rewrite_test
    tree = ast.parse(source, filename=strfn)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
/usr/lib/python3.12/ast.py:52: in parse
    return compile(source, filename, mode, flags,
E     File "/home/<USER>/Desktop/tradingview-automatio/tests/test_volume_analyzer.py", line 157
E       def test_volume_divergence():
E   SyntaxError: expected 'except' or 'finally' block
=============================== warnings summary ===============================
venv/lib/python3.12/site-packages/discord/player.py:30
  /home/<USER>/Desktop/tradingview-automatio/venv/lib/python3.12/site-packages/discord/player.py:30: DeprecationWarning: 'audioop' is deprecated and slated for removal in Python 3.13
    import audioop

src/shared/database/supabase_sdk_client.py:22
  /home/<USER>/Desktop/tradingview-automatio/src/shared/database/supabase_sdk_client.py:22: PydanticDeprecatedSince20: Pydantic V1 style `@validator` validators are deprecated. You should migrate to Pydantic V2 style `@field_validator` validators, see the migration guide for more details. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.11/migration/
    @validator('url')

src/shared/database/supabase_sdk_client.py:36
  /home/<USER>/Desktop/tradingview-automatio/src/shared/database/supabase_sdk_client.py:36: PydanticDeprecatedSince20: Pydantic V1 style `@validator` validators are deprecated. You should migrate to Pydantic V2 style `@field_validator` validators, see the migration guide for more details. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.11/migration/
    @validator('key')

src/shared/database/__init__.py:8
  /home/<USER>/Desktop/tradingview-automatio/src/shared/database/__init__.py:8: DeprecationWarning: src.shared.database.db_manager is deprecated. Use src.database.unified_db.UnifiedDatabaseManager instead.
    from .db_manager import (

venv/lib/python3.12/site-packages/pydantic/_internal/_config.py:323
  /home/<USER>/Desktop/tradingview-automatio/venv/lib/python3.12/site-packages/pydantic/_internal/_config.py:323: PydanticDeprecatedSince20: Support for class-based `config` is deprecated, use ConfigDict instead. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.11/migration/
    warnings.warn(DEPRECATION_MESSAGE, DeprecationWarning)

venv/lib/python3.12/site-packages/pydantic/_internal/_config.py:373
  /home/<USER>/Desktop/tradingview-automatio/venv/lib/python3.12/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
  * 'allow_population_by_field_name' has been renamed to 'validate_by_name'
    warnings.warn(message, UserWarning)

tests/test_automation.py:21
  /home/<USER>/Desktop/tradingview-automatio/tests/test_automation.py:21: PytestCollectionWarning: cannot collect test class 'TestRunner' because it has a __init__ constructor (from: tests/test_automation.py)
    class TestRunner:

-- Docs: https://docs.pytest.org/en/stable/how-to/capture-warnings.html

🤖 AI Trading Bot Test Summary 🤖
----------------------------------------
Python Version: 3.12.3
Supabase Configured: Yes
Discord Bot Token: Configured
----------------------------------------
=========================== short test summary info ============================
ERROR tests/e2e/test_bot_commands.py - NameError: name 'BaseSupabaseClient' i...
ERROR tests/load/test_bot_load.py
ERROR tests/load/test_bot_load.py
ERROR tests/test_advanced_security.py
ERROR tests/test_advanced_security.py
ERROR tests/test_ai_chat_processor.py
ERROR tests/test_ai_chat_processor.py
ERROR tests/test_api_endpoints.py
ERROR tests/test_api_endpoints.py
ERROR tests/test_audit_visualization.py - AttributeError: type object 'MockDi...
ERROR tests/test_audit_visualization.py - AttributeError: type object 'MockDi...
ERROR tests/test_backward_compatibility.py
ERROR tests/test_backward_compatibility.py
ERROR tests/test_contextual_logger.py
ERROR tests/test_contextual_logger.py
ERROR tests/test_data_provider_system.py
ERROR tests/test_data_provider_system.py
ERROR tests/test_data_providers.py
ERROR tests/test_data_providers.py
ERROR tests/test_data_quality_scoring.py
ERROR tests/test_data_quality_scoring.py
ERROR tests/test_enhanced_analysis.py
ERROR tests/test_enhanced_analysis.py
ERROR tests/test_fallback_remediation.py
ERROR tests/test_fallback_remediation.py
ERROR tests/test_full_analysis.py
ERROR tests/test_full_analysis.py
ERROR tests/test_market_hours_fix.py
ERROR tests/test_market_hours_fix.py
ERROR tests/test_metrics_api.py
ERROR tests/test_metrics_api.py
ERROR tests/test_mock_fix.py
ERROR tests/test_mock_fix.py
ERROR tests/test_multi_symbol_integration.py
ERROR tests/test_multi_symbol_integration.py
ERROR tests/test_outlier_detection.py
ERROR tests/test_outlier_detection.py
ERROR tests/test_real_data_providers.py
ERROR tests/test_real_data_providers.py
ERROR tests/test_recommendation_engine.py
ERROR tests/test_recommendation_engine.py
ERROR tests/test_refactored_bot.py
ERROR tests/test_refactored_bot.py
ERROR tests/test_response_generator.py
ERROR tests/test_response_generator.py
ERROR tests/test_simple_correlation.py
ERROR tests/test_simple_correlation.py
ERROR tests/test_stale_data_detection.py
ERROR tests/test_stale_data_detection.py
ERROR tests/test_supply_demand_zones.py
ERROR tests/test_supply_demand_zones.py
ERROR tests/test_uniform_alerts.py
ERROR tests/test_uniform_alerts.py
ERROR tests/test_volume_analyzer.py
ERROR tests/test_volume_analyzer.py
!!!!!!!!!!!!!!!!!!! Interrupted: 55 errors during collection !!!!!!!!!!!!!!!!!!!
======================= 7 warnings, 55 errors in 37.70s ========================
