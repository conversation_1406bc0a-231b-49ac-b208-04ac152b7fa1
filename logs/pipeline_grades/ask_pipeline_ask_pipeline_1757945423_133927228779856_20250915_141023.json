{"pipeline_id": "ask_pipeline_1757945423_133927228779856", "pipeline_name": "ask_pipeline", "start_time": "2025-09-15T14:10:23.580405", "end_time": "2025-09-15T14:10:23.644687", "execution_time": 0.064282, "overall_score": 30.0, "grade": "F", "success_rate": 0.0, "avg_step_time": 0.0008698701858520508, "total_errors": 0, "metadata": {"avg_data_quality": 0, "avg_performance": 0, "avg_reliability": 0}, "steps": [{"step_name": "ai_processing", "execution_time": 0.0015537738800048828, "success": false, "error_message": "'RobustFinancialAnalyzer' object has no attribute 'process_query'", "data_quality_score": null, "performance_score": null, "reliability_score": null, "overall_score": 0.0, "grade": "F", "metrics": {"execution_time": 0.00022673606872558594}, "timestamp": "2025-09-15T14:10:23.582590"}, {"step_name": "ai_processing", "execution_time": 0.0007677078247070312, "success": false, "error_message": "'RobustFinancialAnalyzer' object has no attribute 'process_query'", "data_quality_score": null, "performance_score": null, "reliability_score": null, "overall_score": 0.0, "grade": "F", "metrics": {"execution_time": 0.0002410411834716797}, "timestamp": "2025-09-15T14:10:23.585523"}, {"step_name": "ai_processing", "execution_time": 0.0009105205535888672, "success": false, "error_message": "'RobustFinancialAnalyzer' object has no attribute 'process_query'", "data_quality_score": null, "performance_score": null, "reliability_score": null, "overall_score": 0.0, "grade": "F", "metrics": {"execution_time": 0.00015282630920410156}, "timestamp": "2025-09-15T14:10:23.587878"}, {"step_name": "ai_processing", "execution_time": 0.0007297992706298828, "success": false, "error_message": "'RobustFinancialAnalyzer' object has no attribute 'process_query'", "data_quality_score": null, "performance_score": null, "reliability_score": null, "overall_score": 0.0, "grade": "F", "metrics": {"execution_time": 0.0001533031463623047}, "timestamp": "2025-09-15T14:10:23.610119"}, {"step_name": "ai_processing", "execution_time": 0.0008618831634521484, "success": false, "error_message": "'RobustFinancialAnalyzer' object has no attribute 'process_query'", "data_quality_score": null, "performance_score": null, "reliability_score": null, "overall_score": 0.0, "grade": "F", "metrics": {"execution_time": 0.0002243518829345703}, "timestamp": "2025-09-15T14:10:23.612941"}, {"step_name": "ai_processing", "execution_time": 0.0007023811340332031, "success": false, "error_message": "'RobustFinancialAnalyzer' object has no attribute 'process_query'", "data_quality_score": null, "performance_score": null, "reliability_score": null, "overall_score": 0.0, "grade": "F", "metrics": {"execution_time": 0.00012230873107910156}, "timestamp": "2025-09-15T14:10:23.615161"}, {"step_name": "ai_processing", "execution_time": 0.0005996227264404297, "success": false, "error_message": "'RobustFinancialAnalyzer' object has no attribute 'process_query'", "data_quality_score": null, "performance_score": null, "reliability_score": null, "overall_score": 0.0, "grade": "F", "metrics": {"execution_time": 0.00012254714965820312}, "timestamp": "2025-09-15T14:10:23.617167"}, {"step_name": "ai_processing", "execution_time": 0.0009415149688720703, "success": false, "error_message": "'RobustFinancialAnalyzer' object has no attribute 'process_query'", "data_quality_score": null, "performance_score": null, "reliability_score": null, "overall_score": 0.0, "grade": "F", "metrics": {"execution_time": 0.0002243518829345703}, "timestamp": "2025-09-15T14:10:23.620790"}, {"step_name": "ai_processing", "execution_time": 0.001172780990600586, "success": false, "error_message": "'RobustFinancialAnalyzer' object has no attribute 'process_query'", "data_quality_score": null, "performance_score": null, "reliability_score": null, "overall_score": 0.0, "grade": "F", "metrics": {"execution_time": 0.00031447410583496094}, "timestamp": "2025-09-15T14:10:23.623896"}, {"step_name": "ai_processing", "execution_time": 0.0006153583526611328, "success": false, "error_message": "'RobustFinancialAnalyzer' object has no attribute 'process_query'", "data_quality_score": null, "performance_score": null, "reliability_score": null, "overall_score": 0.0, "grade": "F", "metrics": {"execution_time": 0.00015282630920410156}, "timestamp": "2025-09-15T14:10:23.626290"}, {"step_name": "ai_processing", "execution_time": 0.0009171962738037109, "success": false, "error_message": "'RobustFinancialAnalyzer' object has no attribute 'process_query'", "data_quality_score": null, "performance_score": null, "reliability_score": null, "overall_score": 0.0, "grade": "F", "metrics": {"execution_time": 0.0002219676971435547}, "timestamp": "2025-09-15T14:10:23.628998"}, {"step_name": "ai_processing", "execution_time": 0.0012507438659667969, "success": false, "error_message": "'RobustFinancialAnalyzer' object has no attribute 'process_query'", "data_quality_score": null, "performance_score": null, "reliability_score": null, "overall_score": 0.0, "grade": "F", "metrics": {"execution_time": 0.000331878662109375}, "timestamp": "2025-09-15T14:10:23.633406"}, {"step_name": "ai_processing", "execution_time": 0.0006709098815917969, "success": false, "error_message": "'RobustFinancialAnalyzer' object has no attribute 'process_query'", "data_quality_score": null, "performance_score": null, "reliability_score": null, "overall_score": 0.0, "grade": "F", "metrics": {"execution_time": 0.00011706352233886719}, "timestamp": "2025-09-15T14:10:23.636585"}, {"step_name": "ai_processing", "execution_time": 0.0010194778442382812, "success": false, "error_message": "'RobustFinancialAnalyzer' object has no attribute 'process_query'", "data_quality_score": null, "performance_score": null, "reliability_score": null, "overall_score": 0.0, "grade": "F", "metrics": {"execution_time": 0.00024390220642089844}, "timestamp": "2025-09-15T14:10:23.639486"}, {"step_name": "ai_processing", "execution_time": 0.0004782676696777344, "success": false, "error_message": "'RobustFinancialAnalyzer' object has no attribute 'process_query'", "data_quality_score": null, "performance_score": null, "reliability_score": null, "overall_score": 0.0, "grade": "F", "metrics": {"execution_time": 0.0001232624053955078}, "timestamp": "2025-09-15T14:10:23.641692"}, {"step_name": "ai_processing", "execution_time": 0.0007259845733642578, "success": false, "error_message": "'RobustFinancialAnalyzer' object has no attribute 'process_query'", "data_quality_score": null, "performance_score": null, "reliability_score": null, "overall_score": 0.0, "grade": "F", "metrics": {"execution_time": 0.00017499923706054688}, "timestamp": "2025-09-15T14:10:23.644528"}]}