{"pipeline_id": "low_quality_pipeline_1757710412_131195025111664", "pipeline_name": "low_quality_pipeline", "start_time": "2025-09-12T16:53:32.968785", "end_time": "2025-09-12T16:53:36.338087", "execution_time": 3.369302, "overall_score": 58.786278688501994, "grade": "F", "success_rate": 50.0, "avg_step_time": 0.8420689105987549, "total_errors": 0, "metadata": {"avg_data_quality": 18.35889555066214, "avg_performance": 7.603210854279261, "avg_reliability": 0}, "steps": [{"step_name": "fetch_data", "execution_time": 1.0083789825439453, "success": true, "error_message": null, "data_quality_score": 60.45916034773134, "performance_score": 47.99126279616823, "reliability_score": 49.27505397617153, "overall_score": 53.36355917079446, "grade": "F", "metrics": {"execution_time": 1.0082723988679168, "simulated": true}, "timestamp": "2025-09-12T16:53:33.977219"}, {"step_name": "process_data", "execution_time": 0.637706995010376, "success": false, "error_message": "Simulated failure in process_data", "data_quality_score": null, "performance_score": null, "reliability_score": null, "overall_score": 0.0, "grade": "F", "metrics": {"execution_time": 0.6376081881701349, "simulated": true}, "timestamp": "2025-09-12T16:53:34.615103"}, {"step_name": "analyze_results", "execution_time": 0.8934330940246582, "success": true, "error_message": null, "data_quality_score": 76.25863075359294, "performance_score": 67.2151589123903, "reliability_score": 43.729411224703895, "overall_score": 63.786823342565434, "grade": "D", "metrics": {"execution_time": 0.8933060551151885, "simulated": true}, "timestamp": "2025-09-12T16:53:35.508733"}, {"step_name": "generate_report", "execution_time": 0.82875657081604, "success": false, "error_message": "Simulated failure in generate_report", "data_quality_score": null, "performance_score": null, "reliability_score": null, "overall_score": 0.0, "grade": "F", "metrics": {"execution_time": 0.8286028035794228, "simulated": true}, "timestamp": "2025-09-12T16:53:36.337746"}]}