{"pipeline_id": "low_quality_pipeline_1757620472_134922079793184", "pipeline_name": "low_quality_pipeline", "start_time": "2025-09-11T15:54:32.040292", "end_time": "2025-09-11T15:54:37.379485", "execution_time": 5.339193, "overall_score": 58.714222679878446, "grade": "F", "success_rate": 50.0, "avg_step_time": 1.3343978524208069, "total_errors": 0, "metadata": {}, "steps": [{"step_name": "fetch_data", "execution_time": 1.6300809383392334, "success": false, "error_message": "Simulated failure in fetch_data", "data_quality_score": null, "performance_score": null, "reliability_score": null, "overall_score": 0.0, "grade": "F", "metrics": {"execution_time": 1.6299458805057143, "simulated": true}, "timestamp": "2025-09-11T15:54:33.670440"}, {"step_name": "process_data", "execution_time": 1.1005666255950928, "success": true, "error_message": null, "data_quality_score": 64.4314581210899, "performance_score": 63.15535896881639, "reliability_score": 55.938623941084316, "overall_score": 61.50077812140617, "grade": "D-", "metrics": {"execution_time": 1.100391137314591, "simulated": true}, "timestamp": "2025-09-11T15:54:34.771337"}, {"step_name": "analyze_results", "execution_time": 0.6748840808868408, "success": false, "error_message": "Simulated failure in analyze_results", "data_quality_score": null, "performance_score": null, "reliability_score": null, "overall_score": 0.0, "grade": "F", "metrics": {"execution_time": 0.6747116081401632, "simulated": true}, "timestamp": "2025-09-11T15:54:35.446529"}, {"step_name": "generate_report", "execution_time": 1.9320597648620605, "success": true, "error_message": null, "data_quality_score": 72.24229839126133, "performance_score": 51.155501836261514, "reliability_score": 34.81762567641155, "overall_score": 54.68885761030646, "grade": "F", "metrics": {"execution_time": 1.9319046544275869, "simulated": true}, "timestamp": "2025-09-11T15:54:37.379145"}]}