{"pipeline_id": "ask_pipeline_1757787272_124446812500432", "pipeline_name": "ask_pipeline", "start_time": "2025-09-13T18:14:32.206921", "end_time": "2025-09-13T18:14:32.220960", "execution_time": 0.014039, "overall_score": 97.6, "grade": "A+", "success_rate": 100.0, "avg_step_time": 0.00032458986554827007, "total_errors": 0, "metadata": {"avg_data_quality": 80.0, "avg_performance": 100.0, "avg_reliability": 100.0}, "steps": [{"step_name": "ai_processing", "execution_time": 0.000865936279296875, "success": true, "error_message": null, "data_quality_score": 80, "performance_score": 100.0, "reliability_score": 100, "overall_score": 92.0, "grade": "A-", "metrics": {"execution_time": 0.0006744861602783203}, "timestamp": "2025-09-13T18:14:32.215454"}, {"step_name": "ai_processing", "execution_time": 0.00031757354736328125, "success": true, "error_message": null, "data_quality_score": 80, "performance_score": 100.0, "reliability_score": 100, "overall_score": 92.0, "grade": "A-", "metrics": {"execution_time": 0.00022220611572265625}, "timestamp": "2025-09-13T18:14:32.216795"}, {"step_name": "ai_processing", "execution_time": 0.00021910667419433594, "success": true, "error_message": null, "data_quality_score": 80, "performance_score": 100.0, "reliability_score": 100, "overall_score": 92.0, "grade": "A-", "metrics": {"execution_time": 0.00015354156494140625}, "timestamp": "2025-09-13T18:14:32.217688"}, {"step_name": "ai_processing", "execution_time": 0.0001761913299560547, "success": true, "error_message": null, "data_quality_score": 80, "performance_score": 100.0, "reliability_score": 100, "overall_score": 92.0, "grade": "A-", "metrics": {"execution_time": 0.00011801719665527344}, "timestamp": "2025-09-13T18:14:32.218443"}, {"step_name": "ai_processing", "execution_time": 0.00032901763916015625, "success": true, "error_message": null, "data_quality_score": 80, "performance_score": 100.0, "reliability_score": 100, "overall_score": 92.0, "grade": "A-", "metrics": {"execution_time": 0.0002510547637939453}, "timestamp": "2025-09-13T18:14:32.219298"}, {"step_name": "ai_processing", "execution_time": 0.0002009868621826172, "success": true, "error_message": null, "data_quality_score": 80, "performance_score": 100.0, "reliability_score": 100, "overall_score": 92.0, "grade": "A-", "metrics": {"execution_time": 0.00012803077697753906}, "timestamp": "2025-09-13T18:14:32.220178"}, {"step_name": "ai_processing", "execution_time": 0.0001633167266845703, "success": true, "error_message": null, "data_quality_score": 80, "performance_score": 100.0, "reliability_score": 100, "overall_score": 92.0, "grade": "A-", "metrics": {"execution_time": 0.00010609626770019531}, "timestamp": "2025-09-13T18:14:32.220881"}]}