{"pipeline_id": "ask_pipeline_1757787480_128180036045968", "pipeline_name": "ask_pipeline", "start_time": "2025-09-13T18:18:00.974252", "end_time": "2025-09-13T18:18:01.002938", "execution_time": 0.028686, "overall_score": 97.6, "grade": "A+", "success_rate": 100.0, "avg_step_time": 0.00027213990688323975, "total_errors": 0, "metadata": {"avg_data_quality": 80.0, "avg_performance": 100.0, "avg_reliability": 100.0}, "steps": [{"step_name": "ai_processing", "execution_time": 0.0005617141723632812, "success": true, "error_message": null, "data_quality_score": 80, "performance_score": 100.0, "reliability_score": 100, "overall_score": 92.0, "grade": "A-", "metrics": {"execution_time": 0.00035881996154785156}, "timestamp": "2025-09-13T18:18:00.981707"}, {"step_name": "ai_processing", "execution_time": 0.00027823448181152344, "success": true, "error_message": null, "data_quality_score": 80, "performance_score": 100.0, "reliability_score": 100, "overall_score": 92.0, "grade": "A-", "metrics": {"execution_time": 0.00019025802612304688}, "timestamp": "2025-09-13T18:18:00.983703"}, {"step_name": "ai_processing", "execution_time": 0.0002503395080566406, "success": true, "error_message": null, "data_quality_score": 80, "performance_score": 100.0, "reliability_score": 100, "overall_score": 92.0, "grade": "A-", "metrics": {"execution_time": 0.00017595291137695312}, "timestamp": "2025-09-13T18:18:00.984768"}, {"step_name": "ai_processing", "execution_time": 0.0005202293395996094, "success": true, "error_message": null, "data_quality_score": 80, "performance_score": 100.0, "reliability_score": 100, "overall_score": 92.0, "grade": "A-", "metrics": {"execution_time": 0.0003991127014160156}, "timestamp": "2025-09-13T18:18:00.989557"}, {"step_name": "ai_processing", "execution_time": 0.0004391670227050781, "success": true, "error_message": null, "data_quality_score": 80, "performance_score": 100.0, "reliability_score": 100, "overall_score": 92.0, "grade": "A-", "metrics": {"execution_time": 0.00030922889709472656}, "timestamp": "2025-09-13T18:18:00.991256"}, {"step_name": "ai_processing", "execution_time": 0.0003418922424316406, "success": true, "error_message": null, "data_quality_score": 80, "performance_score": 100.0, "reliability_score": 100, "overall_score": 92.0, "grade": "A-", "metrics": {"execution_time": 0.00022482872009277344}, "timestamp": "2025-09-13T18:18:00.992762"}, {"step_name": "ai_processing", "execution_time": 0.0002663135528564453, "success": true, "error_message": null, "data_quality_score": 80, "performance_score": 100.0, "reliability_score": 100, "overall_score": 92.0, "grade": "A-", "metrics": {"execution_time": 0.00015544891357421875}, "timestamp": "2025-09-13T18:18:00.993886"}, {"step_name": "ai_processing", "execution_time": 0.0002944469451904297, "success": true, "error_message": null, "data_quality_score": 80, "performance_score": 100.0, "reliability_score": 100, "overall_score": 92.0, "grade": "A-", "metrics": {"execution_time": 0.0001983642578125}, "timestamp": "2025-09-13T18:18:00.995232"}, {"step_name": "ai_processing", "execution_time": 0.00019669532775878906, "success": true, "error_message": null, "data_quality_score": 80, "performance_score": 100.0, "reliability_score": 100, "overall_score": 92.0, "grade": "A-", "metrics": {"execution_time": 0.00013256072998046875}, "timestamp": "2025-09-13T18:18:00.996359"}, {"step_name": "ai_processing", "execution_time": 0.00017452239990234375, "success": true, "error_message": null, "data_quality_score": 80, "performance_score": 100.0, "reliability_score": 100, "overall_score": 92.0, "grade": "A-", "metrics": {"execution_time": 0.00011801719665527344}, "timestamp": "2025-09-13T18:18:00.997332"}, {"step_name": "ai_processing", "execution_time": 0.0001735687255859375, "success": true, "error_message": null, "data_quality_score": 80, "performance_score": 100.0, "reliability_score": 100, "overall_score": 92.0, "grade": "A-", "metrics": {"execution_time": 0.00011301040649414062}, "timestamp": "2025-09-13T18:18:00.998259"}, {"step_name": "ai_processing", "execution_time": 0.000186920166015625, "success": true, "error_message": null, "data_quality_score": 80, "performance_score": 100.0, "reliability_score": 100, "overall_score": 92.0, "grade": "A-", "metrics": {"execution_time": 0.00012803077697753906}, "timestamp": "2025-09-13T18:18:00.999067"}, {"step_name": "ai_processing", "execution_time": 0.0001647472381591797, "success": true, "error_message": null, "data_quality_score": 80, "performance_score": 100.0, "reliability_score": 100, "overall_score": 92.0, "grade": "A-", "metrics": {"execution_time": 0.00011157989501953125}, "timestamp": "2025-09-13T18:18:00.999937"}, {"step_name": "ai_processing", "execution_time": 0.00016307830810546875, "success": true, "error_message": null, "data_quality_score": 80, "performance_score": 100.0, "reliability_score": 100, "overall_score": 92.0, "grade": "A-", "metrics": {"execution_time": 0.00010633468627929688}, "timestamp": "2025-09-13T18:18:01.000802"}, {"step_name": "ai_processing", "execution_time": 0.00017023086547851562, "success": true, "error_message": null, "data_quality_score": 80, "performance_score": 100.0, "reliability_score": 100, "overall_score": 92.0, "grade": "A-", "metrics": {"execution_time": 0.00011348724365234375}, "timestamp": "2025-09-13T18:18:01.001692"}, {"step_name": "ai_processing", "execution_time": 0.00017213821411132812, "success": true, "error_message": null, "data_quality_score": 80, "performance_score": 100.0, "reliability_score": 100, "overall_score": 92.0, "grade": "A-", "metrics": {"execution_time": 0.00011134147644042969}, "timestamp": "2025-09-13T18:18:01.002863"}]}