{"pipeline_id": "ask_pipeline_1757838904_132070795462672", "pipeline_name": "ask_pipeline", "start_time": "2025-09-14T08:35:04.712136", "end_time": "2025-09-14T12:03:53.616364", "execution_time": 12528.904228, "overall_score": 97.25, "grade": "A+", "success_rate": 100.0, "avg_step_time": 3.3072886731889515, "total_errors": 0, "metadata": {"avg_data_quality": 80.0, "avg_performance": 96.11111111111111, "avg_reliability": 100.0}, "steps": [{"step_name": "ai_processing", "execution_time": 1.5184237957000732, "success": true, "error_message": null, "data_quality_score": 80, "performance_score": 100.0, "reliability_score": 100, "overall_score": 92.0, "grade": "A-", "metrics": {"execution_time": 1.5181584358215332}, "timestamp": "2025-09-14T09:00:25.249122"}, {"step_name": "ai_processing", "execution_time": 2.7034661769866943, "success": true, "error_message": null, "data_quality_score": 80, "performance_score": 100.0, "reliability_score": 100, "overall_score": 92.0, "grade": "A-", "metrics": {"execution_time": 2.702824592590332}, "timestamp": "2025-09-14T09:00:44.427475"}, {"step_name": "ai_processing", "execution_time": 3.280020236968994, "success": true, "error_message": null, "data_quality_score": 80, "performance_score": 95.0, "reliability_score": 100, "overall_score": 90.5, "grade": "A-", "metrics": {"execution_time": 3.2794570922851562}, "timestamp": "2025-09-14T09:01:13.291943"}, {"step_name": "ai_processing", "execution_time": 2.670621395111084, "success": true, "error_message": null, "data_quality_score": 80, "performance_score": 100.0, "reliability_score": 100, "overall_score": 92.0, "grade": "A-", "metrics": {"execution_time": 2.6705234050750732}, "timestamp": "2025-09-14T10:34:31.585693"}, {"step_name": "ai_processing", "execution_time": 2.8888776302337646, "success": true, "error_message": null, "data_quality_score": 80, "performance_score": 100.0, "reliability_score": 100, "overall_score": 92.0, "grade": "A-", "metrics": {"execution_time": 2.8886923789978027}, "timestamp": "2025-09-14T10:35:00.942417"}, {"step_name": "ai_processing", "execution_time": 2.7073729038238525, "success": true, "error_message": null, "data_quality_score": 80, "performance_score": 100.0, "reliability_score": 100, "overall_score": 92.0, "grade": "A-", "metrics": {"execution_time": 2.7072505950927734}, "timestamp": "2025-09-14T10:35:44.201539"}, {"step_name": "ai_processing", "execution_time": 2.4148051738739014, "success": true, "error_message": null, "data_quality_score": 80, "performance_score": 100.0, "reliability_score": 100, "overall_score": 92.0, "grade": "A-", "metrics": {"execution_time": 2.414633274078369}, "timestamp": "2025-09-14T10:36:33.652275"}, {"step_name": "ai_processing", "execution_time": 6.413299322128296, "success": true, "error_message": null, "data_quality_score": 80, "performance_score": 85.0, "reliability_score": 100, "overall_score": 87.5, "grade": "B+", "metrics": {"execution_time": 6.413128137588501}, "timestamp": "2025-09-14T10:50:17.950017"}, {"step_name": "ai_processing", "execution_time": 5.168711423873901, "success": true, "error_message": null, "data_quality_score": 80, "performance_score": 85.0, "reliability_score": 100, "overall_score": 87.5, "grade": "B+", "metrics": {"execution_time": 5.168562650680542}, "timestamp": "2025-09-14T12:03:53.616163"}]}