{"pipeline_id": "low_quality_pipeline_1757948709_134341435092208", "pipeline_name": "low_quality_pipeline", "start_time": "2025-09-15T11:05:09.068965", "end_time": "2025-09-15T11:05:13.179676", "execution_time": 4.110711, "overall_score": 73.89702809662292, "grade": "C", "success_rate": 75.0, "avg_step_time": 1.0273545384407043, "total_errors": 0, "metadata": {"avg_data_quality": 46.810229772395545, "avg_performance": 36.526710759410626, "avg_reliability": 23.608213938475508}, "steps": [{"step_name": "fetch_data", "execution_time": 1.234370231628418, "success": false, "error_message": "Simulated failure in fetch_data", "data_quality_score": null, "performance_score": null, "reliability_score": null, "overall_score": 0.0, "grade": "F", "metrics": {"execution_time": 1.2342520754279673, "simulated": true}, "timestamp": "2025-09-15T11:05:10.303417"}, {"step_name": "process_data", "execution_time": 1.0002257823944092, "success": true, "error_message": null, "data_quality_score": 78.54874447773499, "performance_score": 58.135756913711425, "reliability_score": 51.058756184792664, "overall_score": 64.17785172064522, "grade": "D", "metrics": {"execution_time": 1.000090711295177, "simulated": true}, "timestamp": "2025-09-15T11:05:11.303861"}, {"step_name": "analyze_results", "execution_time": 1.0314009189605713, "success": true, "error_message": null, "data_quality_score": 71.40157713067285, "performance_score": 61.58313907265509, "reliability_score": 53.53222140775897, "overall_score": 63.09523899639336, "grade": "D", "metrics": {"execution_time": 1.0312608370399705, "simulated": true}, "timestamp": "2025-09-15T11:05:12.335592"}, {"step_name": "generate_report", "execution_time": 0.843421220779419, "success": true, "error_message": null, "data_quality_score": 65.48036770877879, "performance_score": 64.86123629186535, "reliability_score": 41.23366422287491, "overall_score": 58.02061723793359, "grade": "F", "metrics": {"execution_time": 0.8431647464815359, "simulated": true}, "timestamp": "2025-09-15T11:05:13.179376"}]}