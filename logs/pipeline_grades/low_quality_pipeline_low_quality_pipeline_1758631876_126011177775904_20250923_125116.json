{"pipeline_id": "low_quality_pipeline_1758631876_126011177775904", "pipeline_name": "low_quality_pipeline", "start_time": "2025-09-23T12:51:16.767734", "end_time": "2025-09-23T12:51:20.830726", "execution_time": 4.062992, "overall_score": 42.20216854185726, "grade": "F", "success_rate": 50.0, "avg_step_time": 1.0148981809616089, "total_errors": 0, "metadata": {"avg_data_quality": 18.453267062938238, "avg_performance": 3.694307366871108, "avg_reliability": 0}, "steps": [{"step_name": "fetch_data", "execution_time": 0.9610939025878906, "success": false, "error_message": "Simulated failure in fetch_data", "data_quality_score": null, "performance_score": null, "reliability_score": null, "overall_score": 0.0, "grade": "F", "metrics": {"execution_time": 0.9609503960440351, "simulated": true}, "timestamp": "2025-09-23T12:51:17.729083"}, {"step_name": "process_data", "execution_time": 1.5599627494812012, "success": false, "error_message": "Simulated failure in process_data", "data_quality_score": null, "performance_score": null, "reliability_score": null, "overall_score": 0.0, "grade": "F", "metrics": {"execution_time": 1.559703961532549, "simulated": true}, "timestamp": "2025-09-23T12:51:19.289641"}, {"step_name": "analyze_results", "execution_time": 0.2724430561065674, "success": true, "error_message": null, "data_quality_score": 69.85546699506232, "performance_score": 66.93737560661575, "reliability_score": 49.048261696105655, "overall_score": 67.19112818747733, "grade": "D+", "metrics": {"execution_time": 0.2722798122503988, "simulated": true}, "timestamp": "2025-09-23T12:51:19.562856"}, {"step_name": "generate_report", "execution_time": 1.2660930156707764, "success": true, "error_message": null, "data_quality_score": 67.05106713081418, "performance_score": 40.45123912712647, "reliability_score": 46.524115204261065, "overall_score": 59.67840633742132, "grade": "F", "metrics": {"execution_time": 1.2658455051665125, "simulated": true}, "timestamp": "2025-09-23T12:51:20.829531"}]}