{"pipeline_id": "ask_pipeline_1757942912_137807794574672", "pipeline_name": "ask_pipeline", "start_time": "2025-09-15T13:28:32.094418", "end_time": "2025-09-15T13:28:32.146510", "execution_time": 0.052092, "overall_score": 100.0, "grade": "A+", "success_rate": 100.0, "avg_step_time": 0.0007594078779220581, "total_errors": 0, "metadata": {"avg_data_quality": 100.0, "avg_performance": 100.0, "avg_reliability": 100.0}, "steps": [{"step_name": "ai_processing", "execution_time": 0.0010905265808105469, "success": true, "error_message": null, "data_quality_score": 100, "performance_score": 100.0, "reliability_score": 100, "overall_score": 100.0, "grade": "A+", "metrics": {"execution_time": 0.0008566379547119141}, "timestamp": "2025-09-15T13:28:32.097082"}, {"step_name": "ai_processing", "execution_time": 0.0009036064147949219, "success": true, "error_message": null, "data_quality_score": 100, "performance_score": 100.0, "reliability_score": 100, "overall_score": 100.0, "grade": "A+", "metrics": {"execution_time": 0.0006663799285888672}, "timestamp": "2025-09-15T13:28:32.101174"}, {"step_name": "ai_processing", "execution_time": 0.00057220458984375, "success": true, "error_message": null, "data_quality_score": 100, "performance_score": 100.0, "reliability_score": 100, "overall_score": 100.0, "grade": "A+", "metrics": {"execution_time": 0.0003948211669921875}, "timestamp": "2025-09-15T13:28:32.103692"}, {"step_name": "ai_processing", "execution_time": 0.0005049705505371094, "success": true, "error_message": null, "data_quality_score": 100, "performance_score": 100.0, "reliability_score": 100, "overall_score": 100.0, "grade": "A+", "metrics": {"execution_time": 0.0003509521484375}, "timestamp": "2025-09-15T13:28:32.107130"}, {"step_name": "ai_processing", "execution_time": 0.00039315223693847656, "success": true, "error_message": null, "data_quality_score": 100, "performance_score": 100.0, "reliability_score": 100, "overall_score": 100.0, "grade": "A+", "metrics": {"execution_time": 0.0002460479736328125}, "timestamp": "2025-09-15T13:28:32.109351"}, {"step_name": "ai_processing", "execution_time": 0.0007178783416748047, "success": true, "error_message": null, "data_quality_score": 100, "performance_score": 100.0, "reliability_score": 100, "overall_score": 100.0, "grade": "A+", "metrics": {"execution_time": 0.0005183219909667969}, "timestamp": "2025-09-15T13:28:32.111908"}, {"step_name": "ai_processing", "execution_time": 0.0007035732269287109, "success": true, "error_message": null, "data_quality_score": 100, "performance_score": 100.0, "reliability_score": 100, "overall_score": 100.0, "grade": "A+", "metrics": {"execution_time": 0.0005004405975341797}, "timestamp": "2025-09-15T13:28:32.115174"}, {"step_name": "ai_processing", "execution_time": 0.0007894039154052734, "success": true, "error_message": null, "data_quality_score": 100, "performance_score": 100.0, "reliability_score": 100, "overall_score": 100.0, "grade": "A+", "metrics": {"execution_time": 0.0004792213439941406}, "timestamp": "2025-09-15T13:28:32.118712"}, {"step_name": "ai_processing", "execution_time": 0.0008959770202636719, "success": true, "error_message": null, "data_quality_score": 100, "performance_score": 100.0, "reliability_score": 100, "overall_score": 100.0, "grade": "A+", "metrics": {"execution_time": 0.0006392002105712891}, "timestamp": "2025-09-15T13:28:32.122464"}, {"step_name": "ai_processing", "execution_time": 0.0009274482727050781, "success": true, "error_message": null, "data_quality_score": 100, "performance_score": 100.0, "reliability_score": 100, "overall_score": 100.0, "grade": "A+", "metrics": {"execution_time": 0.0006389617919921875}, "timestamp": "2025-09-15T13:28:32.125890"}, {"step_name": "ai_processing", "execution_time": 0.0006282329559326172, "success": true, "error_message": null, "data_quality_score": 100, "performance_score": 100.0, "reliability_score": 100, "overall_score": 100.0, "grade": "A+", "metrics": {"execution_time": 0.0004570484161376953}, "timestamp": "2025-09-15T13:28:32.129305"}, {"step_name": "ai_processing", "execution_time": 0.000888824462890625, "success": true, "error_message": null, "data_quality_score": 100, "performance_score": 100.0, "reliability_score": 100, "overall_score": 100.0, "grade": "A+", "metrics": {"execution_time": 0.0005705356597900391}, "timestamp": "2025-09-15T13:28:32.132577"}, {"step_name": "ai_processing", "execution_time": 0.0006580352783203125, "success": true, "error_message": null, "data_quality_score": 100, "performance_score": 100.0, "reliability_score": 100, "overall_score": 100.0, "grade": "A+", "metrics": {"execution_time": 0.00048470497131347656}, "timestamp": "2025-09-15T13:28:32.135873"}, {"step_name": "ai_processing", "execution_time": 0.0008902549743652344, "success": true, "error_message": null, "data_quality_score": 100, "performance_score": 100.0, "reliability_score": 100, "overall_score": 100.0, "grade": "A+", "metrics": {"execution_time": 0.0005977153778076172}, "timestamp": "2025-09-15T13:28:32.139313"}, {"step_name": "ai_processing", "execution_time": 0.0007052421569824219, "success": true, "error_message": null, "data_quality_score": 100, "performance_score": 100.0, "reliability_score": 100, "overall_score": 100.0, "grade": "A+", "metrics": {"execution_time": 0.0004999637603759766}, "timestamp": "2025-09-15T13:28:32.142953"}, {"step_name": "ai_processing", "execution_time": 0.000881195068359375, "success": true, "error_message": null, "data_quality_score": 100, "performance_score": 100.0, "reliability_score": 100, "overall_score": 100.0, "grade": "A+", "metrics": {"execution_time": 0.0006413459777832031}, "timestamp": "2025-09-15T13:28:32.146177"}]}