{"pipeline_id": "ask_pipeline_1758288306_123899951266800", "pipeline_name": "ask_pipeline", "start_time": "2025-09-19T09:25:06.462880", "end_time": "2025-09-19T09:26:25.063236", "execution_time": 78.600356, "overall_score": 88.21290645599365, "grade": "B+", "success_rate": 100.0, "avg_step_time": 11.224259172167097, "total_errors": 0, "metadata": {"avg_data_quality": 82.35714285714286, "avg_performance": 77.55647468566895, "avg_reliability": 100.0}, "steps": [{"step_name": "ai_processing", "execution_time": 9.726187944412231, "success": true, "error_message": null, "data_quality_score": 84.80000000000001, "performance_score": 80.56218576431274, "reliability_score": 100, "overall_score": 85.47243715286255, "grade": "B", "metrics": {"execution_time": 9.718907117843628, "response_quality": 0.8480000000000001, "quality_grade": "A", "relevance_score": 0.66, "completeness_score": 0.95, "clarity_score": 0.95, "helpfulness_score": 0.9}, "timestamp": "2025-09-19T09:25:16.190211"}, {"step_name": "ai_processing", "execution_time": 3.739260673522949, "success": true, "error_message": null, "data_quality_score": 88.00000000000001, "performance_score": 92.52392911911011, "reliability_score": 100, "overall_score": 90.10478582382203, "grade": "A-", "metrics": {"execution_time": 3.7380354404449463, "response_quality": 0.8800000000000001, "quality_grade": "A", "relevance_score": 0.6, "completeness_score": 0.95, "clarity_score": 0.95, "helpfulness_score": 0.9}, "timestamp": "2025-09-19T09:25:19.933452"}, {"step_name": "ai_processing", "execution_time": 3.13755464553833, "success": true, "error_message": null, "data_quality_score": 89.80000000000001, "performance_score": 93.72777462005615, "reliability_score": 100, "overall_score": 91.60555492401124, "grade": "A-", "metrics": {"execution_time": 3.136112689971924, "response_quality": 0.8980000000000001, "quality_grade": "A", "relevance_score": 0.66, "completeness_score": 0.95, "clarity_score": 0.95, "helpfulness_score": 0.9}, "timestamp": "2025-09-19T09:25:23.074804"}, {"step_name": "ai_processing", "execution_time": 4.042288541793823, "success": true, "error_message": null, "data_quality_score": 73.60000000000001, "performance_score": 91.91960763931274, "reliability_score": 100, "overall_score": 79.90392152786255, "grade": "C+", "metrics": {"execution_time": 4.040196180343628, "response_quality": 0.7360000000000001, "quality_grade": "B", "relevance_score": 0.12, "completeness_score": 0.95, "clarity_score": 0.95, "helpfulness_score": 0.9}, "timestamp": "2025-09-19T09:25:27.120659"}, {"step_name": "ai_processing", "execution_time": 13.314748525619507, "success": true, "error_message": null, "data_quality_score": 70.2, "performance_score": 73.37386083602905, "reliability_score": 100, "overall_score": 73.81477216720582, "grade": "C", "metrics": {"execution_time": 13.313069581985474, "response_quality": 0.7020000000000001, "quality_grade": "B", "relevance_score": 0.64, "completeness_score": 0.95, "clarity_score": 0.95, "helpfulness_score": 0.6}, "timestamp": "2025-09-19T09:25:40.441348"}, {"step_name": "ai_processing", "execution_time": 12.542938470840454, "success": true, "error_message": null, "data_quality_score": 90.0, "performance_score": 74.91761875152588, "reliability_score": 100, "overall_score": 87.98352375030517, "grade": "B+", "metrics": {"execution_time": 12.54119062423706, "response_quality": 0.9, "quality_grade": "A+", "relevance_score": 0.95, "completeness_score": 0.95, "clarity_score": 0.95, "helpfulness_score": 0.95}, "timestamp": "2025-09-19T09:25:52.990781"}, {"step_name": "ai_processing", "execution_time": 32.06683540344238, "success": true, "error_message": null, "data_quality_score": 80.10000000000001, "performance_score": 35.87034606933594, "reliability_score": 100, "overall_score": 73.24406921386719, "grade": "C", "metrics": {"execution_time": 32.06482696533203, "response_quality": 0.801, "quality_grade": "A", "relevance_score": 0.62, "completeness_score": 0.95, "clarity_score": 0.95, "helpfulness_score": 0.95}, "timestamp": "2025-09-19T09:26:25.062781"}]}