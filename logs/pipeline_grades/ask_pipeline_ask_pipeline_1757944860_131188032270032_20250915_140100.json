{"pipeline_id": "ask_pipeline_1757944860_131188032270032", "pipeline_name": "ask_pipeline", "start_time": "2025-09-15T14:01:00.479163", "end_time": "2025-09-15T14:01:00.528455", "execution_time": 0.049292, "overall_score": 30.0, "grade": "F", "success_rate": 0.0, "avg_step_time": 0.0009473711252212524, "total_errors": 0, "metadata": {"avg_data_quality": 0, "avg_performance": 0, "avg_reliability": 0}, "steps": [{"step_name": "ai_processing", "execution_time": 0.0016777515411376953, "success": false, "error_message": "'RobustFinancialAnalyzer' object has no attribute 'process_query'", "data_quality_score": null, "performance_score": null, "reliability_score": null, "overall_score": 0.0, "grade": "F", "metrics": {"execution_time": 0.00034046173095703125}, "timestamp": "2025-09-15T14:01:00.481701"}, {"step_name": "ai_processing", "execution_time": 0.0004837512969970703, "success": false, "error_message": "'RobustFinancialAnalyzer' object has no attribute 'process_query'", "data_quality_score": null, "performance_score": null, "reliability_score": null, "overall_score": 0.0, "grade": "F", "metrics": {"execution_time": 0.00011205673217773438}, "timestamp": "2025-09-15T14:01:00.483966"}, {"step_name": "ai_processing", "execution_time": 0.0006058216094970703, "success": false, "error_message": "'RobustFinancialAnalyzer' object has no attribute 'process_query'", "data_quality_score": null, "performance_score": null, "reliability_score": null, "overall_score": 0.0, "grade": "F", "metrics": {"execution_time": 8.249282836914062e-05}, "timestamp": "2025-09-15T14:01:00.485699"}, {"step_name": "ai_processing", "execution_time": 0.0016226768493652344, "success": false, "error_message": "'RobustFinancialAnalyzer' object has no attribute 'process_query'", "data_quality_score": null, "performance_score": null, "reliability_score": null, "overall_score": 0.0, "grade": "F", "metrics": {"execution_time": 0.0005898475646972656}, "timestamp": "2025-09-15T14:01:00.492798"}, {"step_name": "ai_processing", "execution_time": 0.001176595687866211, "success": false, "error_message": "'RobustFinancialAnalyzer' object has no attribute 'process_query'", "data_quality_score": null, "performance_score": null, "reliability_score": null, "overall_score": 0.0, "grade": "F", "metrics": {"execution_time": 0.0003185272216796875}, "timestamp": "2025-09-15T14:01:00.496008"}, {"step_name": "ai_processing", "execution_time": 0.0009832382202148438, "success": false, "error_message": "'RobustFinancialAnalyzer' object has no attribute 'process_query'", "data_quality_score": null, "performance_score": null, "reliability_score": null, "overall_score": 0.0, "grade": "F", "metrics": {"execution_time": 0.00033926963806152344}, "timestamp": "2025-09-15T14:01:00.499522"}, {"step_name": "ai_processing", "execution_time": 0.001194000244140625, "success": false, "error_message": "'RobustFinancialAnalyzer' object has no attribute 'process_query'", "data_quality_score": null, "performance_score": null, "reliability_score": null, "overall_score": 0.0, "grade": "F", "metrics": {"execution_time": 0.00033855438232421875}, "timestamp": "2025-09-15T14:01:00.503089"}, {"step_name": "ai_processing", "execution_time": 0.0007841587066650391, "success": false, "error_message": "'RobustFinancialAnalyzer' object has no attribute 'process_query'", "data_quality_score": null, "performance_score": null, "reliability_score": null, "overall_score": 0.0, "grade": "F", "metrics": {"execution_time": 0.00020837783813476562}, "timestamp": "2025-09-15T14:01:00.505482"}, {"step_name": "ai_processing", "execution_time": 0.0013675689697265625, "success": false, "error_message": "'RobustFinancialAnalyzer' object has no attribute 'process_query'", "data_quality_score": null, "performance_score": null, "reliability_score": null, "overall_score": 0.0, "grade": "F", "metrics": {"execution_time": 0.00036644935607910156}, "timestamp": "2025-09-15T14:01:00.509658"}, {"step_name": "ai_processing", "execution_time": 0.00044918060302734375, "success": false, "error_message": "'RobustFinancialAnalyzer' object has no attribute 'process_query'", "data_quality_score": null, "performance_score": null, "reliability_score": null, "overall_score": 0.0, "grade": "F", "metrics": {"execution_time": 9.775161743164062e-05}, "timestamp": "2025-09-15T14:01:00.512078"}, {"step_name": "ai_processing", "execution_time": 0.0004630088806152344, "success": false, "error_message": "'RobustFinancialAnalyzer' object has no attribute 'process_query'", "data_quality_score": null, "performance_score": null, "reliability_score": null, "overall_score": 0.0, "grade": "F", "metrics": {"execution_time": 0.00010800361633300781}, "timestamp": "2025-09-15T14:01:00.514637"}, {"step_name": "ai_processing", "execution_time": 0.001070261001586914, "success": false, "error_message": "'RobustFinancialAnalyzer' object has no attribute 'process_query'", "data_quality_score": null, "performance_score": null, "reliability_score": null, "overall_score": 0.0, "grade": "F", "metrics": {"execution_time": 0.00022554397583007812}, "timestamp": "2025-09-15T14:01:00.517565"}, {"step_name": "ai_processing", "execution_time": 0.001207113265991211, "success": false, "error_message": "'RobustFinancialAnalyzer' object has no attribute 'process_query'", "data_quality_score": null, "performance_score": null, "reliability_score": null, "overall_score": 0.0, "grade": "F", "metrics": {"execution_time": 0.0002741813659667969}, "timestamp": "2025-09-15T14:01:00.520562"}, {"step_name": "ai_processing", "execution_time": 0.0006241798400878906, "success": false, "error_message": "'RobustFinancialAnalyzer' object has no attribute 'process_query'", "data_quality_score": null, "performance_score": null, "reliability_score": null, "overall_score": 0.0, "grade": "F", "metrics": {"execution_time": 0.00015807151794433594}, "timestamp": "2025-09-15T14:01:00.523356"}, {"step_name": "ai_processing", "execution_time": 0.0008985996246337891, "success": false, "error_message": "'RobustFinancialAnalyzer' object has no attribute 'process_query'", "data_quality_score": null, "performance_score": null, "reliability_score": null, "overall_score": 0.0, "grade": "F", "metrics": {"execution_time": 0.0002193450927734375}, "timestamp": "2025-09-15T14:01:00.525891"}, {"step_name": "ai_processing", "execution_time": 0.0005500316619873047, "success": false, "error_message": "'RobustFinancialAnalyzer' object has no attribute 'process_query'", "data_quality_score": null, "performance_score": null, "reliability_score": null, "overall_score": 0.0, "grade": "F", "metrics": {"execution_time": 9.965896606445312e-05}, "timestamp": "2025-09-15T14:01:00.528311"}]}