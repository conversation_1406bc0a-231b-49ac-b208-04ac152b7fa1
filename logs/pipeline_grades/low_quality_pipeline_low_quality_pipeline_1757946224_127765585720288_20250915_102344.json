{"pipeline_id": "low_quality_pipeline_1757946224_127765585720288", "pipeline_name": "low_quality_pipeline", "start_time": "2025-09-15T10:23:44.470987", "end_time": "2025-09-15T10:23:49.002341", "execution_time": 4.531354, "overall_score": 57.94462176862176, "grade": "F", "success_rate": 50.0, "avg_step_time": 1.132461130619049, "total_errors": 0, "metadata": {"avg_data_quality": 15.342995278578314, "avg_performance": 2.1555981538000637, "avg_reliability": 0}, "steps": [{"step_name": "fetch_data", "execution_time": 0.8663463592529297, "success": true, "error_message": null, "data_quality_score": 66.06965150769022, "performance_score": 46.01369128433896, "reliability_score": 36.83493465655612, "overall_score": 51.282448385344615, "grade": "F", "metrics": {"execution_time": 0.8662261509515047, "simulated": true}, "timestamp": "2025-09-15T10:23:45.337474"}, {"step_name": "process_data", "execution_time": 1.5356566905975342, "success": true, "error_message": null, "data_quality_score": 64.61633904946639, "performance_score": 58.29750502326117, "reliability_score": 37.70018245393526, "overall_score": 54.64584186294549, "grade": "F", "metrics": {"execution_time": 1.5354010662129425, "simulated": true}, "timestamp": "2025-09-15T10:23:46.873341"}, {"step_name": "analyze_results", "execution_time": 1.7018051147460938, "success": false, "error_message": "Simulated failure in analyze_results", "data_quality_score": null, "performance_score": null, "reliability_score": null, "overall_score": 0.0, "grade": "F", "metrics": {"execution_time": 1.7017012613102505, "simulated": true}, "timestamp": "2025-09-15T10:23:48.575550"}, {"step_name": "generate_report", "execution_time": 0.42603635787963867, "success": false, "error_message": "Simulated failure in generate_report", "data_quality_score": null, "performance_score": null, "reliability_score": null, "overall_score": 0.0, "grade": "F", "metrics": {"execution_time": 0.42587395350976265, "simulated": true}, "timestamp": "2025-09-15T10:23:49.001787"}]}