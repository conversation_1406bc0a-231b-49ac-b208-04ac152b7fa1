{"correlation_id": "test_4_18086", "user_id": "test_user_123", "query": "What are the support and resistance levels for TSLA?", "start_time": 1758473548.9534645, "end_time": 1758473557.962626, "total_duration": 9.008629560470581, "success": true, "steps": [{"step_id": "pipeline_process_start", "step_name": "ASK Pipeline Process", "stage": "pipeline_main", "timestamp": 1758473549.0012417, "duration_ms": 0.22172927856445312, "status": "completed", "input_data": {"query": "What are the support and resistance levels for TSLA?", "user_id": "test_user_123", "correlation_id": "test_4_18086"}, "output_data": {}, "decisions": [], "performance_metrics": {}, "error_details": null, "reasoning": null, "next_steps": []}, {"step_id": "intent_detection", "step_name": "Intent Detection", "stage": "intent_detection", "timestamp": 1758473549.0015278, "duration_ms": 2004.37331199646, "status": "completed", "input_data": {"query": "What are the support and resistance levels for TSLA?"}, "output_data": {"intent": "data_needed", "confidence": 0.5, "reasoning": "timeout_fallback"}, "decisions": [{"decision_id": "test_4_18086_0", "decision_point": "intent_detection_result", "options": ["casual", "data_needed"], "chosen_option": "data_needed", "reasoning": "Intent detected as data_needed with confidence 0.5", "confidence": 0.5, "timestamp": 1758473551.0057356, "context": {"intent": "data_needed", "confidence": 0.5, "reasoning": "timeout_fallback"}}], "performance_metrics": {}, "error_details": null, "reasoning": "Intent detection completed: data_needed", "next_steps": []}, {"step_id": "tool_orchestration", "step_name": "Tool Orchestration", "stage": "tool_orchestration", "timestamp": 1758473551.0059717, "duration_ms": 6951.66540145874, "status": "completed", "input_data": {"query": "What are the support and resistance levels for TSLA?", "intent": "data_needed", "intent_confidence": 0.5}, "output_data": {"tools_used": ["market_data_collection", "ml_predictions", "get_global_quote", "get_company_overview", "fact_verification"], "cache_hit": false, "execution_time": 6.950891971588135}, "decisions": [{"decision_id": "test_4_18086_1", "decision_point": "tool_execution_strategy", "options": ["execute_tools", "skip_tools"], "chosen_option": "execute_tools", "reasoning": "Intent is data_needed, tools needed for data gathering", "confidence": 1.0, "timestamp": 1758473557.9574203, "context": {"tools_used": ["market_data_collection", "ml_predictions", "get_global_quote", "get_company_overview", "fact_verification"], "cache_hit": false, "execution_time": 6.950891971588135}}], "performance_metrics": {}, "error_details": null, "reasoning": "Tools executed successfully: ['market_data_collection', 'ml_predictions', 'get_global_quote', 'get_company_overview', 'fact_verification']", "next_steps": []}, {"step_id": "response_generation", "step_name": "Response Generation", "stage": "response_generation", "timestamp": 1758473557.957713, "duration_ms": 0.6885528564453125, "status": "completed", "input_data": {"query": "What are the support and resistance levels for TSLA?", "intent": "data_needed", "tool_result_available": true}, "output_data": {"response_generated": true, "response_length": 0}, "decisions": [{"decision_id": "test_4_18086_2", "decision_point": "response_generation_strategy", "options": ["ai_generated", "cached", "fallback"], "chosen_option": "ai_generated", "reasoning": "Generated AI response using intent and tool data", "confidence": 1.0, "timestamp": 1758473557.9582949, "context": {"response_length": 0, "success": true}}], "performance_metrics": {}, "error_details": null, "reasoning": "AI response generated successfully", "next_steps": []}], "decisions": [{"decision_id": "test_4_18086_0", "decision_point": "intent_detection_result", "options": ["casual", "data_needed"], "chosen_option": "data_needed", "reasoning": "Intent detected as data_needed with confidence 0.5", "confidence": 0.5, "timestamp": 1758473551.0057356, "context": {"intent": "data_needed", "confidence": 0.5, "reasoning": "timeout_fallback"}}, {"decision_id": "test_4_18086_1", "decision_point": "tool_execution_strategy", "options": ["execute_tools", "skip_tools"], "chosen_option": "execute_tools", "reasoning": "Intent is data_needed, tools needed for data gathering", "confidence": 1.0, "timestamp": 1758473557.9574203, "context": {"tools_used": ["market_data_collection", "ml_predictions", "get_global_quote", "get_company_overview", "fact_verification"], "cache_hit": false, "execution_time": 6.950891971588135}}, {"decision_id": "test_4_18086_2", "decision_point": "response_generation_strategy", "options": ["ai_generated", "cached", "fallback"], "chosen_option": "ai_generated", "reasoning": "Generated AI response using intent and tool data", "confidence": 1.0, "timestamp": 1758473557.9582949, "context": {"response_length": 0, "success": true}}, {"decision_id": "test_4_18086_3", "decision_point": "response_format_type", "options": ["embed", "text"], "chosen_option": "embed", "reasoning": "Response formatted as embed for Discord", "confidence": 1.0, "timestamp": 1758473557.9590437, "context": {"response_type": "embed"}}], "plans": [{"plan_id": "test_4_18086_plan_0", "plan_name": "ASK Pipeline Execution", "strategy": "Execute 4-stage pipeline: Intent Detection → Tool Orchestration → Response Generation → Discord Formatting", "steps": ["intent_detection", "tool_orchestration", "response_generation", "discord_formatting"], "expected_duration": 15.0, "fallback_strategy": "Use error handler for any stage failures", "success_criteria": ["Intent detected successfully", "Tools executed (if needed)", "Response generated", "Response formatted for Discord"], "timestamp": 1758473549.0013368}], "final_result": {"test_number": 4, "query": "What are the support and resistance levels for TSLA?", "completed_at": 18095.948506268}, "summary": {"total_steps": 4, "total_decisions": 4, "total_plans": 1, "success_rate": 1.0, "average_step_duration": 2239.2372488975525}}