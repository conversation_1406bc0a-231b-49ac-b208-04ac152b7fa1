#!/usr/bin/env python3
"""
Deep Execution Tracer
Actually executes the code to trace the real import chain and execution path.
"""

import sys
import os
import importlib
import traceback
from pathlib import Path
from typing import Set, List, Dict, Any
from collections import defaultdict
import json

# Add src to path
sys.path.insert(0, str(Path("src").absolute()))

class DeepExecutionTracer:
    """Traces actual execution by running the code"""
    
    def __init__(self, src_path: str):
        self.src_path = Path(src_path)
        self.imported_modules: Set[str] = set()
        self.execution_log: List[str] = []
        self.original_import = __builtins__.__import__
        
    def start_tracing(self):
        """Start tracing imports"""
        def tracing_import(name, globals=None, locals=None, fromlist=(), level=0):
            # Log the import
            self.imported_modules.add(name)
            self.execution_log.append(f"IMPORT: {name}")
            
            # Call original import
            return self.original_import(name, globals, locals, fromlist, level)
        
        # Replace builtin import
        __builtins__.__import__ = tracing_import
    
    def stop_tracing(self):
        """Stop tracing imports"""
        __builtins__.__import__ = self.original_import
    
    def trace_entry_point(self, entry_file: str) -> Dict[str, Any]:
        """Trace execution from entry point"""
        print(f"🔍 Deep tracing: {entry_file}")
        
        self.imported_modules.clear()
        self.execution_log.clear()
        
        try:
            # Start tracing
            self.start_tracing()
            
            # Change to the directory containing the entry file
            original_cwd = os.getcwd()
            entry_path = Path(entry_file)
            if entry_path.exists():
                os.chdir(entry_path.parent)
                entry_file = entry_path.name
            
            # Execute the entry point
            with open(entry_file, 'r') as f:
                code = f.read()
            
            # Execute in a controlled environment
            exec_globals = {
                '__name__': '__main__',
                '__file__': entry_file,
                '__package__': None,
                '__doc__': None,
                '__loader__': None,
                '__spec__': None
            }
            
            exec(code, exec_globals)
            
            # Restore original directory
            os.chdir(original_cwd)
            
            return {
                "entry_file": entry_file,
                "imported_modules": list(self.imported_modules),
                "execution_log": self.execution_log.copy(),
                "success": True
            }
            
        except Exception as e:
            print(f"❌ Error executing {entry_file}: {e}")
            traceback.print_exc()
            return {
                "entry_file": entry_file,
                "imported_modules": list(self.imported_modules),
                "execution_log": self.execution_log.copy(),
                "success": False,
                "error": str(e)
            }
        finally:
            self.stop_tracing()

def analyze_imports(imported_modules: Set[str]) -> Dict[str, Any]:
    """Analyze the imported modules"""
    analysis = {
        "total_imports": len(imported_modules),
        "src_imports": [],
        "external_imports": [],
        "standard_library_imports": [],
        "unknown_imports": []
    }
    
    # Categorize imports
    for module in imported_modules:
        if module.startswith('src.'):
            analysis["src_imports"].append(module)
        elif module in sys.builtin_module_names or module in ['os', 'sys', 'json', 'pathlib', 'typing', 'collections', 'datetime', 'time', 'asyncio', 'logging']:
            analysis["standard_library_imports"].append(module)
        elif '.' in module and not module.startswith('src.'):
            analysis["external_imports"].append(module)
        else:
            analysis["unknown_imports"].append(module)
    
    return analysis

def find_actual_used_files(imported_modules: Set[str], src_path: Path) -> Set[str]:
    """Find which files in src are actually used"""
    used_files = set()
    
    for module in imported_modules:
        if module.startswith('src.'):
            # Convert module name to file path
            module_path = module[4:]  # Remove 'src.'
            file_path = src_path / f"{module_path}.py"
            if file_path.exists():
                used_files.add(str(file_path.relative_to(src_path)))
            
            # Also check for __init__.py
            init_path = src_path / module_path / "__init__.py"
            if init_path.exists():
                used_files.add(str(init_path.relative_to(src_path)))
    
    return used_files

def main():
    """Main function"""
    print("🚀 Starting Deep Execution Tracing")
    
    tracer = DeepExecutionTracer("src")
    
    # Entry points to test
    entry_points = [
        "start_bot.py",
        "start_enhanced_bot.py",
        "start_ai_automation.py"
    ]
    
    all_results = []
    all_imported = set()
    
    for entry_point in entry_points:
        print(f"\n{'='*60}")
        result = tracer.trace_entry_point(entry_point)
        all_results.append(result)
        
        if result["success"]:
            all_imported.update(result["imported_modules"])
            print(f"✅ Successfully traced {entry_point}")
            print(f"   Imported {len(result['imported_modules'])} modules")
        else:
            print(f"❌ Failed to trace {entry_point}")
    
    # Analyze results
    print(f"\n{'='*60}")
    print("📊 ANALYSIS RESULTS")
    print("="*60)
    
    analysis = analyze_imports(all_imported)
    used_files = find_actual_used_files(all_imported, Path("src"))
    
    print(f"📦 Total unique imports: {len(all_imported)}")
    print(f"📁 Src imports: {len(analysis['src_imports'])}")
    print(f"🔧 External imports: {len(analysis['external_imports'])}")
    print(f"📚 Standard library: {len(analysis['standard_library_imports'])}")
    print(f"❓ Unknown imports: {len(analysis['unknown_imports'])}")
    print(f"📄 Files actually used: {len(used_files)}")
    
    # Count total files in src
    total_files = 0
    for py_file in Path("src").rglob("*.py"):
        if "__pycache__" not in str(py_file):
            total_files += 1
    
    print(f"📁 Total files in src: {total_files}")
    print(f"📈 Usage percentage: {(len(used_files) / total_files) * 100:.1f}%")
    
    print(f"\n🔍 SRC IMPORTS FOUND:")
    for module in sorted(analysis['src_imports'])[:20]:
        print(f"  - {module}")
    
    print(f"\n📄 FILES ACTUALLY USED:")
    for file_path in sorted(used_files)[:20]:
        print(f"  - {file_path}")
    
    # Save results
    with open("deep_trace_results.json", "w") as f:
        json.dump({
            "all_results": all_results,
            "analysis": analysis,
            "used_files": list(used_files),
            "total_files_in_src": total_files,
            "usage_percentage": (len(used_files) / total_files) * 100
        }, f, indent=2)
    
    print(f"\n✅ Deep tracing complete!")
    print(f"📄 Results saved to: deep_trace_results.json")
    
    return {
        "used_files": used_files,
        "analysis": analysis,
        "total_files": total_files
    }

if __name__ == "__main__":
    main()
