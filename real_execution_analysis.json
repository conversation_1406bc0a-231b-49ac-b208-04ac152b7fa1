{"analysis": {"entry_point_results": {"start_bot.py": {"success": true, "imported_files": [], "import_chain": [], "total_imports": 6}, "start_enhanced_bot.py": {"success": true, "imported_files": [], "import_chain": [], "total_imports": 13}, "start_ai_automation.py": {"success": true, "imported_files": [], "import_chain": [], "total_imports": 13}}, "all_imported_files": [], "unused_files": ["src(old)/bot/pipeline/commands/ask/performance/database_optimizer.py", "src(old)/core/monitoring_pkg/bot_monitor.py", "src(old)/bot/pipeline/commands/ask/audit/audit_logger.py", "src(old)/shared/ai_services/ai_tool_registry.py", "src(old)/bot/pipeline/commands/ask/modernization/modern_python.py", "src(old)/api/data/providers/modules/config.py", "src(old)/bot/audit/request_visualizer.py", "src(old)/bot/pipeline/commands/ask/modernization/__init__.py", "src(old)/shared/ai/depth_controller.py", "src(old)/bot/advanced_trading_strategies.py", "src(old)/bot/client_audit_integration.py", "src(old)/bot/pipeline/monitoring/__init__.py", "src(old)/core/feedback_mechanism.py", "src(old)/bot/pipeline/commands/analyze/stages/report_template.py", "src(old)/shared/services/performance_monitor.py", "src(old)/shared/redis/redis_manager.py", "src(old)/bot/security/__init__.py", "src(old)/core/prompts/utils/__init__.py", "src(old)/api/data/providers/modules/validation.py", "src(old)/shared/ai_services/anti_hallucination_prompt.py", "src(old)/shared/redis/__init__.py", "src(old)/bot/pipeline/commands/ask/architecture/service_registry.py", "src(old)/core/prompts/templates/__init__.py", "src(old)/shared/ai_chat/fallbacks.py", "src(old)/api/middleware/security_utils.py", "src(old)/analysis/utils/__init__.py", "src(old)/shared/ai_services/timeout_manager.py", "src(old)/shared/background/celery_app.py", "src(old)/analysis/ai/__init__.py", "src(old)/analysis/risk/enhanced_risk_assessment.py", "src(old)/shared/ai_services/cross_validation_ai.py", "src(old)/bot/pipeline/utils/circuit_breaker.py", "src(old)/core/prompts/commands/ask_prompts.py", "src(old)/bot/core/services.py", "src(old)/bot/pipeline/commands/ask/performance/benchmark.py", "src(old)/shared/monitoring/pipeline_grader.py", "src(old)/bot/enhancements/__init__.py", "src(old)/bot/pipeline/commands/ask/modernization/containerization.py", "src(old)/core/enums/stock_analysis.py", "src(old)/bot/token_validator.py", "src(old)/bot/pipeline/commands/ask/deployment/cicd_pipeline.py", "src(old)/api/data/providers/modules/rate_limiting.py", "src(old)/bot/pipeline/commands/ask/stages/ai_synthesizer.py", "src(old)/database/models/__init__.py", "src(old)/shared/monitoring/__init__.py", "src(old)/database/models/market_data.py", "src(old)/analysis/risk/calculators/beta_calculator.py", "src(old)/analysis/probability/probability_response_service.py", "src(old)/services/__init__.py", "src(old)/shared/database/__init__.py", "src(old)/bot/pipeline/commands/ask/security/test_input_validator.py", "src(old)/bot/pipeline/commands/ask/stages/__init__.py", "src(old)/shared/error_handling/fallback.py", "src(old)/api/data/providers/__init__.py", "src(old)/bot/pipeline/commands/ask/quality/type_safety.py", "src(old)/analysis/fundamental/metrics.py", "src(old)/bot/pipeline/shared/formatters/__init__.py", "src(old)/data/cache/__init__.py", "src(old)/security/middleware.py", "src(old)/analysis/risk/__init__.py", "src(old)/data/models/indicators.py", "src(old)/bot/pipeline/commands/ask/core/error_coordinator.py", "src(old)/core/scheduler.py", "src(old)/shared/ai_chat/__init__.py", "src(old)/core/watchlist/__init__.py", "src(old)/core/data_quality_validator.py", "src(old)/bot/pipeline/commands/analyze/stages/technical_analysis.py", "src(old)/bot/pipeline/commands/ask/cleanup/dead_code_analyzer.py", "src(old)/core/formatting/text_formatting.py", "src(old)/bot/pipeline/commands/ask/config/feature_flags.py", "src(old)/bot/pipeline/utils/metrics.py", "src(old)/analysis/ai/recommendation_engine.py", "src(old)/database/query_optimizer.py", "src(old)/shared/ai_services/enhanced_intent_detector.py", "src(old)/bot/pipeline/commands/analyze/parallel_pipeline.py", "src(old)/shared/mcp/docker_mcp_client.py", "src(old)/shared/ai_debugger/local_pattern_debugger.py", "src(old)/bot/pipeline/commands/ask/core/controller.py", "src(old)/bot/pipeline/commands/ask/api/integration.py", "src(old)/bot/pipeline/commands/ask/quality/documentation.py", "src(old)/bot/pipeline/commands/ask/stages/analysis_components.py", "src(old)/shared/ai_services/ai_processor_robust.py", "src(old)/bot/pipeline/commands/ask/tools/fallback_handler.py", "src(old)/bot/pipeline/commands/ask/performance/resource_manager.py", "src(old)/analysis/orchestration/__init__.py", "src(old)/bot/pipeline/commands/ask/config/environment_profiles.py", "src(old)/analysis/risk/calculators/__init__.py", "src(old)/shared/ai_debugger/__init__.py", "src(old)/bot/pipeline/commands/ask/compliance/audit_logger.py", "src(old)/shared/ai_services/enhanced_ai_client.py", "src(old)/shared/watchlist/__init__.py", "src(old)/bot/enhancements/pipeline_visualizer.py", "src(old)/bot/pipeline/commands/ask/errors/fallback_strategy.py", "src(old)/bot/pipeline/commands/ask/stages/data_collector.py", "src(old)/bot/events/__init__.py", "src(old)/bot/pipeline/commands/ask/observability/__init__.py", "src(old)/mcp_server/internal_tools_client.py", "src(old)/shared/utils/__init__.py", "src(old)/shared/utils/symbol_extraction.py", "src(old)/bot/pipeline/shared/data_collectors/__init__.py", "src(old)/api/routes/bot_health.py", "src(old)/shared/ai_services/fact_verifier.py", "src(old)/shared/ai_services/openrouter_key.py", "src(old)/shared/market_analysis/signals.py", "src(old)/shared/data_providers/alpaca_provider.py", "src(old)/templates/analysis_response.py", "src(old)/shared/background/tasks/__init__.py", "src(old)/bot/pipeline/commands/ask/config/config_manager.py", "src(old)/analysis/utils/data_validators.py", "src(old)/bot/pipeline/commands/analyze/multi_timeframe_pipeline.py", "src(old)/analysis/risk/assessment.py", "src(old)/core/prompts/base/system_prompts.py", "src(old)/api/routes/debug.py", "src(old)/core/prompts/utils/formatters.py", "src(old)/shared/ai_chat/response_formatter.py", "src(old)/bot/pipeline/commands/__init__.py", "src(old)/bot/pipeline/commands/ask/api/backward_compatibility.py", "src(old)/bot/pipeline/ask/stages/__init__.py", "src(old)/analysis/templates/analysis_response_template.py", "src(old)/bot/pipeline/commands/ask/observability/tracer.py", "src(old)/shared/ai_services/intelligent_text_parser.py", "src(old)/bot/pipeline/core/pipeline_engine.py", "src(old)/shared/technical_analysis/volume_analyzer.py", "src(old)/data/models/__init__.py", "src(old)/api/routers/__init__.py", "src(old)/shared/ai/__init__.py", "src(old)/shared/data_pipeline/websocket_handler.py", "src(old)/analysis/fundamental/calculators/pe_calculator.py", "src(old)/shared/ai_services/simple_model_config.py", "src(old)/shared/metrics/unified_metrics_service.py", "src(old)/core/prompts/services/security_analysis.py", "src(old)/bot/pipeline/commands/ask/cost/resource_optimizer.py", "src(old)/templates/__init__.py", "src(old)/bot/market_sentiment_analyzer.py", "src(old)/bot/extensions/ask.py", "src(old)/shared/technical_analysis/zones.py", "src(old)/api/main.py", "src(old)/analysis/ai/enhancement_strategy.py", "src(old)/shared/ai_services/simple_query_analyzer.py", "src(old)/api/webhooks/__init__.py", "src(old)/bot/pipeline/commands/ask/performance/async_optimizer.py", "src(old)/bot/audit/request_visualizer_patch.py", "src(old)/core/prompts/__init__.py", "src(old)/bot/pipeline/core/pipeline_optimizer.py", "src(old)/shared/analytics/performance_tracker.py", "src(old)/__init__.py", "src(old)/bot/performance_analytics.py", "src(old)/analysis/templates/__init__.py", "src(old)/core/risk_management/compliance_framework.py", "src(old)/bot/ai/unified_intent_system.py", "src(old)/shared/utils/deprecation.py", "src(old)/shared/data_providers/unified_base.py", "src(old)/bot/pipeline/commands/ask/__init__.py", "src(old)/bot/pipeline/commands/ask/quality/__init__.py", "src(old)/bot/pipeline/commands/ask/performance/request_batcher.py", "src(old)/shared/market_analysis/__init__.py", "src(old)/shared/ai_services/tool_registry.py", "src(old)/api/analytics/__init__.py", "src(old)/bot/pipeline/test_pipeline.py", "src(old)/analysis/orchestration/analysis_orchestrator.py", "src(old)/bot/pipeline/commands/ask/ask_config.py", "src(old)/core/prompts/prompt_manager.py", "src(old)/bot/pipeline/shared/validators/__init__.py", "src(old)/bot/pipeline/core/circuit_breaker.py", "src(old)/shared/database/usage_example.py", "src(old)/data/cache/manager.py", "src(old)/bot/pipeline/commands/analyze/stages/report_generator.py", "src(old)/api/routes/feedback.py", "src(old)/bot/pipeline/commands/ask/observability/log_analyzer.py", "src(old)/bot/watchlist_alerts.py", "src(old)/shared/ai_services/unified_ai_processor.py", "src(old)/shared/ai_services/response_synthesizer.py", "src(old)/core/monitoring_pkg/__init__.py", "src(old)/shared/background/tasks/indicators.py", "src(old)/shared/technical_analysis/multi_timeframe_analyzer.py", "src(old)/shared/configuration/validators.py", "src(old)/bot/pipeline/__init__.py", "src(old)/core/formatting/__init__.py", "src(old)/bot/watchlist_realtime.py", "src(old)/bot/utils/component_checker.py", "src(old)/api/middleware/__init__.py", "src(old)/api/routes/dashboard.py", "src(old)/shared/ai_chat/processor.py", "src(old)/database/query_wrapper.py", "src(old)/shared/technical_analysis/strategy_calculator.py", "src(old)/api/data/cache.py", "src(old)/api/data/market_data_service.py", "src(old)/bot/pipeline/shared/__init__.py", "src(old)/core/risk_management/__init__.py", "src(old)/bot/pipeline/utils/__init__.py", "src(old)/services/analytics_service.py", "src(old)/bot/pipeline/commands/ask/api/versioning.py", "src(old)/database/__init__.py", "src(old)/shared/data_providers/alpha_vantage_mcp.py", "src(old)/shared/validation/enhanced_fact_checker.py", "src(old)/bot/pipeline/commands/ask/cost/cost_tracker.py", "src(old)/shared/data_pipeline/buffer_manager.py", "src(old)/shared/watchlist/webhook_manager.py", "src(old)/core/automation/__init__.py", "src(old)/bot/pipeline/core/__init__.py", "src(old)/core/prompts/commands/general_prompts.py", "src(old)/bot/audit/session_manager.py", "src(old)/mcp_server/internal_tools_mcp_server.py", "src(old)/data/models/stock_data.py", "src(old)/bot/pipeline/commands/ask/stages/response_generator.py", "src(old)/bot/extensions/error_handler.py", "src(old)/shared/configuration/__init__.py", "src(old)/analysis/probability/probability_engine.py", "src(old)/bot/pipeline/commands/ask/core/stage_manager.py", "src(old)/shared/services/enhanced_performance_optimizer.py", "src(old)/core/prompts/base/__init__.py", "src(old)/shared/mcp/mcp_client_config.py", "src(old)/shared/error_handling/__init__.py", "src(old)/shared/ai_chat/data_fetcher.py", "src(old)/shared/market_analysis/unified_signal_analyzer.py", "src(old)/shared/background/tasks/market_intelligence.py", "src(old)/mcp_server/__init__.py", "src(old)/api/data/constants.py", "src(old)/bot/pipeline/commands/ask/compliance/data_manager.py", "src(old)/core/automation/discord_handler.py", "src(old)/shared/data_providers/polygon_provider.py", "src(old)/bot/database_manager.py", "src(old)/api/routes/__init__.py", "src(old)/bot/security/advanced_security.py", "src(old)/bot/pipeline/commands/ask/security/test_rate_limiter.py", "src(old)/bot/extensions/batch_analyze.py", "src(old)/bot/pipeline/commands/ask/api/__init__.py", "src(old)/shared/data_providers/yfinance_provider.py", "src(old)/bot/pipeline/commands/ask/api/contracts.py", "src(old)/mcp_server/mcp_client_integration.py", "src(old)/bot/core/error_handler.py", "src(old)/core/prompts/base/compliance.py", "src(old)/bot/pipeline/commands/watchlist/__init__.py", "src(old)/bot/extensions/status.py", "src(old)/bot/real_time_data_stream.py", "src(old)/bot/pipeline/commands/ask/observability/metrics.py", "src(old)/shared/config/config_manager.py", "src(old)/core/prompts/base/personas.py", "src(old)/shared/data_providers/fallback_provider.py", "src(old)/logs/__init__.py", "src(old)/core/prompts/unified_prompts.py", "src(old)/shared/monitoring/step_logger.py", "src(old)/api/routes/metrics.py", "src(old)/bot/utils/input_sanitizer.py", "src(old)/bot/extensions/ml_admin.py", "src(old)/bot/pipeline/commands/analyze/stages/__init__.py", "src(old)/shared/data_providers/enhanced_error_handler.py", "src(old)/bot/update_imports.py", "src(old)/core/prompts/commands/__init__.py", "src(old)/shared/config_loader.py", "src(old)/shared/ai_services/fast_price_lookup.py", "src(old)/shared/data_providers/base.py", "src(old)/bot/pipeline/commands/ask/deployment/__init__.py", "src(old)/analysis/probability/monte_carlo_simulator.py", "src(old)/core/enums/__init__.py", "src(old)/analysis/__init__.py", "src(old)/core/formatting/technical_analysis.py", "src(old)/bot/risk_management_system.py", "src(old)/shared/market_analysis/signal_analyzer.py", "src(old)/analysis/fundamental/calculators/__init__.py", "src(old)/database/unified_db.py", "src(old)/shared/ai/model_fine_tuner.py", "src(old)/bot/extensions/utility.py", "src(old)/core/risk_management/atr_calculator.py", "src(old)/bot/client_with_monitoring.py", "src(old)/shared/utils/discord_helpers.py", "src(old)/shared/monitoring/intelligent_grader.py", "src(old)/shared/ai_services/enhanced_symbol_extractor.py", "src(old)/bot/utils/enhanced_input_validator.py", "src(old)/bot/pipeline/commands/ask/audit/__init__.py", "src(old)/api/data/metrics.py", "src(old)/mcp_server/trading_mcp_server.py", "src(old)/shared/market_analysis/utils.py", "src(old)/core/prompts/services/text_parsing.py", "src(old)/bot/enhancements/discord_ux.py", "src(old)/bot/pipeline/commands/ask/compliance/compliance_logger.py", "src(old)/bot/pipeline/commands/ask/pipeline.py", "src(old)/bot/pipeline/commands/ask/cache/intelligent_cache.py", "src(old)/shared/technical_analysis/indicators.py", "src(old)/shared/ai_services/circuit_breaker.py", "src(old)/bot/pipeline/commands/ask/security/test_auth_manager.py", "src(old)/shared/data_validation.py", "src(old)/api/data/providers/polygon.py", "src(old)/bot/__init__.py", "src(old)/database/models/analysis.py", "src(old)/core/prompts/models.py", "src(old)/shared/ai_services/ai_security_detector.py", "src(old)/bot/pipeline/commands/ask/security/auth_manager.py", "src(old)/bot/pipeline/commands/ask/stages/simplified_tool_orchestrator.py", "src(old)/bot/alerts/real_time_alerts.py", "src(old)/api/routes/analytics.py", "src(old)/core/prompts/services/intent_detection.py", "src(old)/bot/pipeline/commands/ask/errors/__init__.py", "src(old)/bot/extensions/performance_monitor.py", "src(old)/shared/data_providers/aggregator.py", "src(old)/analysis/probability/__init__.py", "src(old)/api/data/providers/base.py", "src(old)/bot/pipeline/performance_optimizer.py", "src(old)/api/data/providers/modules/__init__.py", "src(old)/bot/pipeline/commands/ask/modernization/dependency_manager.py", "src(old)/bot/extensions/__init__.py", "src(old)/api/routers/market_data.py", "src(old)/database/unified_client.py", "src(old)/analysis/ai/ml_models.py", "src(old)/bot/utils/disclaimer_manager.py", "src(old)/bot/pipeline/ask/__init__.py", "src(old)/shared/__init__.py", "src(old)/bot/audit/__init__.py", "src(old)/core/response_generator.py", "src(old)/bot/pipeline/commands/ask/core/stage_executor.py", "src(old)/bot/pipeline/commands/ask/cache/__init__.py", "src(old)/bot/pipeline/commands/ask/executor.py", "src(old)/shared/ai_services/query_cache.py", "src(old)/bot/extensions/alerts.py", "src(old)/shared/technical_analysis/config.py", "src(old)/shared/metrics/metrics_service.py", "src(old)/shared/utils/lazy_import.py", "src(old)/shared/ai_chat/ai_client.py", "src(old)/api/config.py", "src(old)/shared/ai_services/smart_model_router.py", "src(old)/shared/sentiment/__init__.py", "src(old)/database/repositories/__init__.py", "src(old)/shared/watchlist/bot_manager.py", "src(old)/bot/metrics_collector.py", "src(old)/shared/watchlist/base_manager.py", "src(old)/core/pipeline_engine.py", "src(old)/shared/ai/recommendation_engine.py", "src(old)/bot/pipeline/commands/ask/tools/__init__.py", "src(old)/bot/pipeline/commands/ask/cleanup/test_consolidator.py", "src(old)/analysis/enhanced_evaluator.py", "src(old)/core/exceptions.py", "src(old)/shared/watchlist/supabase_manager.py", "src(old)/api/routes/health.py", "src(old)/bot/monitoring/health_monitor.py", "src(old)/shared/analytics/__init__.py", "src(old)/shared/data_providers/hybrid_mcp_provider.py", "src(old)/bot/extensions/portfolio.py", "src(old)/shared/technical_analysis/enhanced_indicators.py", "src(old)/shared/data_providers/health_monitor.py", "src(old)/api/schemas/metrics_schema.py", "src(old)/main.py", "src(old)/bot/utils/__init__.py", "src(old)/bot/pipeline/commands/ask/tools/mcp_manager.py", "src(old)/bot/__main__.py", "src(old)/core/automation/report_engine.py", "src(old)/bot/pipeline/commands/ask/architecture/event_bus.py", "src(old)/bot/pipeline/commands/ask/deployment/monitoring.py", "src(old)/bot/extensions/zones.py", "src(old)/bot/client.py", "src(old)/bot/extensions/watchlist.py", "src(old)/analysis/fundamental/__init__.py", "src(old)/core/formatting/analysis_template.py", "src(old)/api/schemas/feedback_schema.py", "src(old)/shared/data_providers/__init__.py", "src(old)/api/__init__.py", "src(old)/bot/pipeline/commands/analyze/__init__.py", "src(old)/shared/error_handling/retry.py", "src(old)/bot/setup_audit.py", "src(old)/bot/pipeline/commands/ask/stages/intent_detector.py", "src(old)/bot/pipeline/commands/analyze/stages/fetch_data.py", "src(old)/analysis/fundamental/calculators/growth_calculator.py", "src(old)/bot/pipeline/commands/ask/security/__init__.py", "src(old)/shared/ai_services/query_router.py", "src(old)/bot/rate_limiter.py", "src(old)/shared/ai_services/ai_chat_processor.py", "src(old)/api/data/cache_warming_scheduler.py", "src(old)/shared/data_providers/finnhub_provider.py", "src(old)/shared/services/optimization_service.py", "src(old)/shared/ai_services/__init__.py", "src(old)/security/__init__.py", "src(old)/bot/extensions/help.py", "src(old)/core/validation/__init__.py", "src(old)/bot/pipeline/commands/ask/errors/error_manager.py", "src(old)/analysis/risk/calculators/volatility_calculator.py", "src(old)/analysis/orchestration/enhancement_strategy.py", "src(old)/api/data/providers/modules/auditing.py", "src(old)/bot/pipeline/commands/ask/security/security_scanner.py", "src(old)/shared/ai_services/local_fallback_ai.py", "src(old)/database/migrations/env.py", "src(old)/bot/main.py", "src(old)/shared/technical_analysis/test_indicators.py", "src(old)/database/migrations/__init__.py", "src(old)/core/formatting/response_templates.py", "src(old)/bot/permissions.py", "src(old)/bot/utils/error_handler.py", "src(old)/shared/market_analysis/confidence_scorer.py", "src(old)/core/market_calendar.py", "src(old)/core/prompts/utils/context_injection.py", "src(old)/bot/pipeline/commands/ask/stages/formatter.py", "src(old)/shared/monitoring/performance_monitor.py", "src(old)/bot/utils/rate_limiter.py", "src(old)/shared/monitoring/pipeline_monitor.py", "src(old)/core/prompts/commands/ask_prompt_replacement_ideas.py", "src(old)/core/__init__.py", "src(old)/bot/pipeline/core/context_manager.py", "src(old)/core/logger.py", "src(old)/core/monitoring_pkg/performance_tracker.py", "src(old)/core/secure_cache.py", "src(old)/core/prompts/services/__init__.py", "src(old)/bot/pipeline/logs/__init__.py", "src(old)/core/prompts/services/anti_hallucination.py", "src(old)/analysis/technical/__init__.py", "src(old)/bot/pipeline/commands/ask/config/__init__.py", "src(old)/shared/ai_debugger/live_ai_debugger.py", "src(old)/api/data/providers/finnhub.py", "src(old)/bot/pipeline/commands/analyze/stages/enhanced_analysis.py", "src(old)/bot/pipeline/commands/ask/performance/smart_cache.py", "src(old)/shared/watchlist/models.py", "src(old)/bot/pipeline/commands/ask/performance/connection_pool.py", "src(old)/api/routes/market_data.py", "src(old)/data/__init__.py", "src(old)/bot/pipeline/commands/ask/cache/unified_cache.py", "src(old)/shared/data_providers/alpha_vantage.py", "src(old)/bot/monitoring/__init__.py", "src(old)/bot/pipeline/commands/ask/performance/__init__.py", "src(old)/api/data/__init__.py", "src(old)/bot/pipeline/core/parallel_pipeline.py", "src(old)/bot/pipeline/commands/watchlist/stages/__init__.py", "src(old)/bot/pipeline/commands/ask/config/secrets_manager.py", "src(old)/bot/pipeline/commands/ask/observability/health_checker.py", "src(old)/bot/pipeline/data/__init__.py", "src(old)/database/models/alerts.py", "src(old)/shared/metrics/naming_conventions.py", "src(old)/shared/background/__init__.py", "src(old)/bot/pipeline/commands/ask/architecture/__init__.py", "src(old)/analysis/ai/calculators/__init__.py", "src(old)/api/data/scheduled_tasks.py", "src(old)/bot/pipeline/commands/ask/cleanup/legacy_archiver.py", "src(old)/bot/ai/context_aware_processor.py", "src(old)/shared/ai_chat/config.py", "src(old)/shared/technical_analysis/signal_generator.py", "src(old)/bot/pipeline/commands/ask/deployment/documentation.py", "src(old)/bot/pipeline/commands/analyze/pipeline.py", "src(old)/analysis/ai/ml_training_service.py", "src(old)/bot/pipeline/ask/stages/conversation_memory_service.py", "src(old)/shared/ai_services/ai_service_wrapper.py", "src(old)/shared/sentiment/sentiment_analyzer.py", "src(old)/bot/pipeline_framework.py", "src(old)/bot/pipeline/commands/ask/security/input_validator.py", "src(old)/api/data/providers/data_source_manager.py", "src(old)/shared/error_handling/logging.py", "src(old)/mcp_server/free_mcp_registry.py", "src(old)/bot/pipeline/commands/ask/observability/logger.py", "src(old)/bot/pipeline/commands/ask/compliance/__init__.py", "src(old)/database/config.py", "src(old)/bot/pipeline/commands/ask/stages/query_analyzer.py", "src(old)/bot/pipeline/commands/ask/security/rate_limiter.py", "src(old)/shared/technical_analysis/enhanced_calculator.py", "src(old)/core/automation/analysis_scheduler.py", "src(old)/core/trade_scanner.py", "src(old)/shared/ai_chat/models.py", "src(old)/shared/technical_analysis/calculator.py", "src(old)/core/automation/report_formatter.py", "src(old)/api/schemas/__init__.py", "src(old)/shared/technical_analysis/__init__.py", "src(old)/bot/pipeline/commands/analyze/stages/price_targets.py", "src(old)/shared/technical_analysis/options_greeks_calculator.py", "src(old)/analysis/ai/calculators/sentiment_calculator.py", "src(old)/core/config_manager.py", "src(old)/bot/extensions/analyze.py", "src(old)/bot/pipeline/commands/ask/cleanup/__init__.py", "src(old)/bot/pipeline/commands/ask/security/simplified_security.py", "src(old)/bot/pipeline/commands/ask/core/__init__.py", "src(old)/api/middleware/security.py", "src(old)/shared/cache/cache_service.py", "src(old)/core/utils.py", "src(old)/bot/extensions/recommendations.py", "src(old)/database/models/interactions.py", "src(old)/core/automation/report_scheduler.py", "src(old)/bot/pipeline/commands/ask/quality/code_standards.py", "src(old)/core/validation/financial_validator.py", "src(old)/bot/pipeline/commands/ask/security/test_security_scanner.py", "src(old)/bot/audit/rate_limiter.py", "src(old)/bot/pipeline/commands/ask/cost/__init__.py", "src(old)/api/data/providers/alpha_vantage.py", "src(old)/bot/core/bot.py", "src(old)/core/prompts/commands/analyze_prompts.py", "src(old)/shared/technical_analysis/unified_calculator.py", "src(old)/core/prompts/utils/validation.py", "src(old)/analysis/sentiment/enhanced_sentiment_analyzer.py", "src(old)/shared/ai_services/rate_limit_handler.py"], "total_src_files": 481, "usage_percentage": 0.0}, "cleanup_plan": {"safe_to_delete": ["src(old)/bot/pipeline/commands/ask/performance/database_optimizer.py", "src(old)/core/monitoring_pkg/bot_monitor.py", "src(old)/bot/pipeline/commands/ask/audit/audit_logger.py", "src(old)/shared/ai_services/ai_tool_registry.py", "src(old)/bot/pipeline/commands/ask/modernization/modern_python.py", "src(old)/api/data/providers/modules/config.py", "src(old)/bot/audit/request_visualizer.py", "src(old)/bot/pipeline/commands/ask/modernization/__init__.py", "src(old)/shared/ai/depth_controller.py", "src(old)/bot/advanced_trading_strategies.py", "src(old)/bot/client_audit_integration.py", "src(old)/bot/pipeline/monitoring/__init__.py", "src(old)/core/feedback_mechanism.py", "src(old)/bot/pipeline/commands/analyze/stages/report_template.py", "src(old)/shared/services/performance_monitor.py", "src(old)/shared/redis/redis_manager.py", "src(old)/bot/security/__init__.py", "src(old)/core/prompts/utils/__init__.py", "src(old)/api/data/providers/modules/validation.py", "src(old)/shared/ai_services/anti_hallucination_prompt.py", "src(old)/shared/redis/__init__.py", "src(old)/bot/pipeline/commands/ask/architecture/service_registry.py", "src(old)/core/prompts/templates/__init__.py", "src(old)/shared/ai_chat/fallbacks.py", "src(old)/api/middleware/security_utils.py", "src(old)/analysis/utils/__init__.py", "src(old)/shared/ai_services/timeout_manager.py", "src(old)/shared/background/celery_app.py", "src(old)/analysis/ai/__init__.py", "src(old)/analysis/risk/enhanced_risk_assessment.py", "src(old)/shared/ai_services/cross_validation_ai.py", "src(old)/bot/pipeline/utils/circuit_breaker.py", "src(old)/core/prompts/commands/ask_prompts.py", "src(old)/bot/core/services.py", "src(old)/bot/pipeline/commands/ask/performance/benchmark.py", "src(old)/shared/monitoring/pipeline_grader.py", "src(old)/bot/enhancements/__init__.py", "src(old)/bot/pipeline/commands/ask/modernization/containerization.py", "src(old)/core/enums/stock_analysis.py", "src(old)/bot/token_validator.py", "src(old)/bot/pipeline/commands/ask/deployment/cicd_pipeline.py", "src(old)/api/data/providers/modules/rate_limiting.py", "src(old)/bot/pipeline/commands/ask/stages/ai_synthesizer.py", "src(old)/database/models/__init__.py", "src(old)/shared/monitoring/__init__.py", "src(old)/database/models/market_data.py", "src(old)/analysis/risk/calculators/beta_calculator.py", "src(old)/analysis/probability/probability_response_service.py", "src(old)/services/__init__.py", "src(old)/shared/database/__init__.py", "src(old)/bot/pipeline/commands/ask/security/test_input_validator.py", "src(old)/bot/pipeline/commands/ask/stages/__init__.py", "src(old)/shared/error_handling/fallback.py", "src(old)/api/data/providers/__init__.py", "src(old)/bot/pipeline/commands/ask/quality/type_safety.py", "src(old)/analysis/fundamental/metrics.py", "src(old)/bot/pipeline/shared/formatters/__init__.py", "src(old)/data/cache/__init__.py", "src(old)/security/middleware.py", "src(old)/analysis/risk/__init__.py", "src(old)/data/models/indicators.py", "src(old)/bot/pipeline/commands/ask/core/error_coordinator.py", "src(old)/core/scheduler.py", "src(old)/shared/ai_chat/__init__.py", "src(old)/core/watchlist/__init__.py", "src(old)/core/data_quality_validator.py", "src(old)/bot/pipeline/commands/analyze/stages/technical_analysis.py", "src(old)/bot/pipeline/commands/ask/cleanup/dead_code_analyzer.py", "src(old)/core/formatting/text_formatting.py", "src(old)/bot/pipeline/commands/ask/config/feature_flags.py", "src(old)/bot/pipeline/utils/metrics.py", "src(old)/analysis/ai/recommendation_engine.py", "src(old)/database/query_optimizer.py", "src(old)/shared/ai_services/enhanced_intent_detector.py", "src(old)/bot/pipeline/commands/analyze/parallel_pipeline.py", "src(old)/shared/mcp/docker_mcp_client.py", "src(old)/shared/ai_debugger/local_pattern_debugger.py", "src(old)/bot/pipeline/commands/ask/core/controller.py", "src(old)/bot/pipeline/commands/ask/api/integration.py", "src(old)/bot/pipeline/commands/ask/quality/documentation.py", "src(old)/bot/pipeline/commands/ask/stages/analysis_components.py", "src(old)/shared/ai_services/ai_processor_robust.py", "src(old)/bot/pipeline/commands/ask/tools/fallback_handler.py", "src(old)/bot/pipeline/commands/ask/performance/resource_manager.py", "src(old)/analysis/orchestration/__init__.py", "src(old)/bot/pipeline/commands/ask/config/environment_profiles.py", "src(old)/analysis/risk/calculators/__init__.py", "src(old)/shared/ai_debugger/__init__.py", "src(old)/bot/pipeline/commands/ask/compliance/audit_logger.py", "src(old)/shared/ai_services/enhanced_ai_client.py", "src(old)/shared/watchlist/__init__.py", "src(old)/bot/enhancements/pipeline_visualizer.py", "src(old)/bot/pipeline/commands/ask/errors/fallback_strategy.py", "src(old)/bot/pipeline/commands/ask/stages/data_collector.py", "src(old)/bot/events/__init__.py", "src(old)/bot/pipeline/commands/ask/observability/__init__.py", "src(old)/mcp_server/internal_tools_client.py", "src(old)/shared/utils/__init__.py", "src(old)/shared/utils/symbol_extraction.py", "src(old)/bot/pipeline/shared/data_collectors/__init__.py", "src(old)/api/routes/bot_health.py", "src(old)/shared/ai_services/fact_verifier.py", "src(old)/shared/ai_services/openrouter_key.py", "src(old)/shared/market_analysis/signals.py", "src(old)/shared/data_providers/alpaca_provider.py", "src(old)/templates/analysis_response.py", "src(old)/shared/background/tasks/__init__.py", "src(old)/bot/pipeline/commands/ask/config/config_manager.py", "src(old)/analysis/utils/data_validators.py", "src(old)/bot/pipeline/commands/analyze/multi_timeframe_pipeline.py", "src(old)/analysis/risk/assessment.py", "src(old)/core/prompts/base/system_prompts.py", "src(old)/api/routes/debug.py", "src(old)/core/prompts/utils/formatters.py", "src(old)/shared/ai_chat/response_formatter.py", "src(old)/bot/pipeline/commands/__init__.py", "src(old)/bot/pipeline/commands/ask/api/backward_compatibility.py", "src(old)/bot/pipeline/ask/stages/__init__.py", "src(old)/analysis/templates/analysis_response_template.py", "src(old)/bot/pipeline/commands/ask/observability/tracer.py", "src(old)/shared/ai_services/intelligent_text_parser.py", "src(old)/bot/pipeline/core/pipeline_engine.py", "src(old)/shared/technical_analysis/volume_analyzer.py", "src(old)/data/models/__init__.py", "src(old)/api/routers/__init__.py", "src(old)/shared/ai/__init__.py", "src(old)/shared/data_pipeline/websocket_handler.py", "src(old)/analysis/fundamental/calculators/pe_calculator.py", "src(old)/shared/ai_services/simple_model_config.py", "src(old)/shared/metrics/unified_metrics_service.py", "src(old)/core/prompts/services/security_analysis.py", "src(old)/bot/pipeline/commands/ask/cost/resource_optimizer.py", "src(old)/templates/__init__.py", "src(old)/bot/market_sentiment_analyzer.py", "src(old)/bot/extensions/ask.py", "src(old)/shared/technical_analysis/zones.py", "src(old)/api/main.py", "src(old)/analysis/ai/enhancement_strategy.py", "src(old)/shared/ai_services/simple_query_analyzer.py", "src(old)/api/webhooks/__init__.py", "src(old)/bot/pipeline/commands/ask/performance/async_optimizer.py", "src(old)/bot/audit/request_visualizer_patch.py", "src(old)/core/prompts/__init__.py", "src(old)/bot/pipeline/core/pipeline_optimizer.py", "src(old)/shared/analytics/performance_tracker.py", "src(old)/__init__.py", "src(old)/bot/performance_analytics.py", "src(old)/analysis/templates/__init__.py", "src(old)/core/risk_management/compliance_framework.py", "src(old)/bot/ai/unified_intent_system.py", "src(old)/shared/utils/deprecation.py", "src(old)/shared/data_providers/unified_base.py", "src(old)/bot/pipeline/commands/ask/__init__.py", "src(old)/bot/pipeline/commands/ask/quality/__init__.py", "src(old)/bot/pipeline/commands/ask/performance/request_batcher.py", "src(old)/shared/market_analysis/__init__.py", "src(old)/shared/ai_services/tool_registry.py", "src(old)/api/analytics/__init__.py", "src(old)/bot/pipeline/test_pipeline.py", "src(old)/analysis/orchestration/analysis_orchestrator.py", "src(old)/bot/pipeline/commands/ask/ask_config.py", "src(old)/core/prompts/prompt_manager.py", "src(old)/bot/pipeline/shared/validators/__init__.py", "src(old)/bot/pipeline/core/circuit_breaker.py", "src(old)/shared/database/usage_example.py", "src(old)/data/cache/manager.py", "src(old)/bot/pipeline/commands/analyze/stages/report_generator.py", "src(old)/api/routes/feedback.py", "src(old)/bot/pipeline/commands/ask/observability/log_analyzer.py", "src(old)/bot/watchlist_alerts.py", "src(old)/shared/ai_services/unified_ai_processor.py", "src(old)/shared/ai_services/response_synthesizer.py", "src(old)/core/monitoring_pkg/__init__.py", "src(old)/shared/background/tasks/indicators.py", "src(old)/shared/technical_analysis/multi_timeframe_analyzer.py", "src(old)/shared/configuration/validators.py", "src(old)/bot/pipeline/__init__.py", "src(old)/core/formatting/__init__.py", "src(old)/bot/watchlist_realtime.py", "src(old)/bot/utils/component_checker.py", "src(old)/api/middleware/__init__.py", "src(old)/api/routes/dashboard.py", "src(old)/shared/ai_chat/processor.py", "src(old)/database/query_wrapper.py", "src(old)/shared/technical_analysis/strategy_calculator.py", "src(old)/api/data/cache.py", "src(old)/api/data/market_data_service.py", "src(old)/bot/pipeline/shared/__init__.py", "src(old)/core/risk_management/__init__.py", "src(old)/bot/pipeline/utils/__init__.py", "src(old)/services/analytics_service.py", "src(old)/bot/pipeline/commands/ask/api/versioning.py", "src(old)/database/__init__.py", "src(old)/shared/data_providers/alpha_vantage_mcp.py", "src(old)/shared/validation/enhanced_fact_checker.py", "src(old)/bot/pipeline/commands/ask/cost/cost_tracker.py", "src(old)/shared/data_pipeline/buffer_manager.py", "src(old)/shared/watchlist/webhook_manager.py", "src(old)/core/automation/__init__.py", "src(old)/bot/pipeline/core/__init__.py", "src(old)/core/prompts/commands/general_prompts.py", "src(old)/bot/audit/session_manager.py", "src(old)/mcp_server/internal_tools_mcp_server.py", "src(old)/data/models/stock_data.py", "src(old)/bot/pipeline/commands/ask/stages/response_generator.py", "src(old)/bot/extensions/error_handler.py", "src(old)/shared/configuration/__init__.py", "src(old)/analysis/probability/probability_engine.py", "src(old)/bot/pipeline/commands/ask/core/stage_manager.py", "src(old)/shared/services/enhanced_performance_optimizer.py", "src(old)/core/prompts/base/__init__.py", "src(old)/shared/mcp/mcp_client_config.py", "src(old)/shared/error_handling/__init__.py", "src(old)/shared/ai_chat/data_fetcher.py", "src(old)/shared/market_analysis/unified_signal_analyzer.py", "src(old)/shared/background/tasks/market_intelligence.py", "src(old)/mcp_server/__init__.py", "src(old)/api/data/constants.py", "src(old)/bot/pipeline/commands/ask/compliance/data_manager.py", "src(old)/core/automation/discord_handler.py", "src(old)/shared/data_providers/polygon_provider.py", "src(old)/bot/database_manager.py", "src(old)/api/routes/__init__.py", "src(old)/bot/security/advanced_security.py", "src(old)/bot/pipeline/commands/ask/security/test_rate_limiter.py", "src(old)/bot/extensions/batch_analyze.py", "src(old)/bot/pipeline/commands/ask/api/__init__.py", "src(old)/shared/data_providers/yfinance_provider.py", "src(old)/bot/pipeline/commands/ask/api/contracts.py", "src(old)/mcp_server/mcp_client_integration.py", "src(old)/bot/core/error_handler.py", "src(old)/core/prompts/base/compliance.py", "src(old)/bot/pipeline/commands/watchlist/__init__.py", "src(old)/bot/extensions/status.py", "src(old)/bot/real_time_data_stream.py", "src(old)/bot/pipeline/commands/ask/observability/metrics.py", "src(old)/shared/config/config_manager.py", "src(old)/core/prompts/base/personas.py", "src(old)/shared/data_providers/fallback_provider.py", "src(old)/logs/__init__.py", "src(old)/core/prompts/unified_prompts.py", "src(old)/shared/monitoring/step_logger.py", "src(old)/api/routes/metrics.py", "src(old)/bot/utils/input_sanitizer.py", "src(old)/bot/extensions/ml_admin.py", "src(old)/bot/pipeline/commands/analyze/stages/__init__.py", "src(old)/shared/data_providers/enhanced_error_handler.py", "src(old)/bot/update_imports.py", "src(old)/core/prompts/commands/__init__.py", "src(old)/shared/config_loader.py", "src(old)/shared/ai_services/fast_price_lookup.py", "src(old)/shared/data_providers/base.py", "src(old)/bot/pipeline/commands/ask/deployment/__init__.py", "src(old)/analysis/probability/monte_carlo_simulator.py", "src(old)/core/enums/__init__.py", "src(old)/analysis/__init__.py", "src(old)/core/formatting/technical_analysis.py", "src(old)/bot/risk_management_system.py", "src(old)/shared/market_analysis/signal_analyzer.py", "src(old)/analysis/fundamental/calculators/__init__.py", "src(old)/database/unified_db.py", "src(old)/shared/ai/model_fine_tuner.py", "src(old)/bot/extensions/utility.py", "src(old)/core/risk_management/atr_calculator.py", "src(old)/bot/client_with_monitoring.py", "src(old)/shared/utils/discord_helpers.py", "src(old)/shared/monitoring/intelligent_grader.py", "src(old)/shared/ai_services/enhanced_symbol_extractor.py", "src(old)/bot/utils/enhanced_input_validator.py", "src(old)/bot/pipeline/commands/ask/audit/__init__.py", "src(old)/api/data/metrics.py", "src(old)/mcp_server/trading_mcp_server.py", "src(old)/shared/market_analysis/utils.py", "src(old)/core/prompts/services/text_parsing.py", "src(old)/bot/enhancements/discord_ux.py", "src(old)/bot/pipeline/commands/ask/compliance/compliance_logger.py", "src(old)/bot/pipeline/commands/ask/pipeline.py", "src(old)/bot/pipeline/commands/ask/cache/intelligent_cache.py", "src(old)/shared/technical_analysis/indicators.py", "src(old)/shared/ai_services/circuit_breaker.py", "src(old)/bot/pipeline/commands/ask/security/test_auth_manager.py", "src(old)/shared/data_validation.py", "src(old)/api/data/providers/polygon.py", "src(old)/bot/__init__.py", "src(old)/database/models/analysis.py", "src(old)/core/prompts/models.py", "src(old)/shared/ai_services/ai_security_detector.py", "src(old)/bot/pipeline/commands/ask/security/auth_manager.py", "src(old)/bot/pipeline/commands/ask/stages/simplified_tool_orchestrator.py", "src(old)/bot/alerts/real_time_alerts.py", "src(old)/api/routes/analytics.py", "src(old)/core/prompts/services/intent_detection.py", "src(old)/bot/pipeline/commands/ask/errors/__init__.py", "src(old)/bot/extensions/performance_monitor.py", "src(old)/shared/data_providers/aggregator.py", "src(old)/analysis/probability/__init__.py", "src(old)/api/data/providers/base.py", "src(old)/bot/pipeline/performance_optimizer.py", "src(old)/api/data/providers/modules/__init__.py", "src(old)/bot/pipeline/commands/ask/modernization/dependency_manager.py", "src(old)/bot/extensions/__init__.py", "src(old)/api/routers/market_data.py", "src(old)/database/unified_client.py", "src(old)/analysis/ai/ml_models.py", "src(old)/bot/utils/disclaimer_manager.py", "src(old)/bot/pipeline/ask/__init__.py", "src(old)/shared/__init__.py", "src(old)/bot/audit/__init__.py", "src(old)/core/response_generator.py", "src(old)/bot/pipeline/commands/ask/core/stage_executor.py", "src(old)/bot/pipeline/commands/ask/cache/__init__.py", "src(old)/bot/pipeline/commands/ask/executor.py", "src(old)/shared/ai_services/query_cache.py", "src(old)/bot/extensions/alerts.py", "src(old)/shared/technical_analysis/config.py", "src(old)/shared/metrics/metrics_service.py", "src(old)/shared/utils/lazy_import.py", "src(old)/shared/ai_chat/ai_client.py", "src(old)/api/config.py", "src(old)/shared/ai_services/smart_model_router.py", "src(old)/shared/sentiment/__init__.py", "src(old)/database/repositories/__init__.py", "src(old)/shared/watchlist/bot_manager.py", "src(old)/bot/metrics_collector.py", "src(old)/shared/watchlist/base_manager.py", "src(old)/core/pipeline_engine.py", "src(old)/shared/ai/recommendation_engine.py", "src(old)/bot/pipeline/commands/ask/tools/__init__.py", "src(old)/bot/pipeline/commands/ask/cleanup/test_consolidator.py", "src(old)/analysis/enhanced_evaluator.py", "src(old)/core/exceptions.py", "src(old)/shared/watchlist/supabase_manager.py", "src(old)/api/routes/health.py", "src(old)/bot/monitoring/health_monitor.py", "src(old)/shared/analytics/__init__.py", "src(old)/shared/data_providers/hybrid_mcp_provider.py", "src(old)/bot/extensions/portfolio.py", "src(old)/shared/technical_analysis/enhanced_indicators.py", "src(old)/shared/data_providers/health_monitor.py", "src(old)/api/schemas/metrics_schema.py", "src(old)/main.py", "src(old)/bot/utils/__init__.py", "src(old)/bot/pipeline/commands/ask/tools/mcp_manager.py", "src(old)/bot/__main__.py", "src(old)/core/automation/report_engine.py", "src(old)/bot/pipeline/commands/ask/architecture/event_bus.py", "src(old)/bot/pipeline/commands/ask/deployment/monitoring.py", "src(old)/bot/extensions/zones.py", "src(old)/bot/client.py", "src(old)/bot/extensions/watchlist.py", "src(old)/analysis/fundamental/__init__.py", "src(old)/core/formatting/analysis_template.py", "src(old)/api/schemas/feedback_schema.py", "src(old)/shared/data_providers/__init__.py", "src(old)/api/__init__.py", "src(old)/bot/pipeline/commands/analyze/__init__.py", "src(old)/shared/error_handling/retry.py", "src(old)/bot/setup_audit.py", "src(old)/bot/pipeline/commands/ask/stages/intent_detector.py", "src(old)/bot/pipeline/commands/analyze/stages/fetch_data.py", "src(old)/analysis/fundamental/calculators/growth_calculator.py", "src(old)/bot/pipeline/commands/ask/security/__init__.py", "src(old)/shared/ai_services/query_router.py", "src(old)/bot/rate_limiter.py", "src(old)/shared/ai_services/ai_chat_processor.py", "src(old)/api/data/cache_warming_scheduler.py", "src(old)/shared/data_providers/finnhub_provider.py", "src(old)/shared/services/optimization_service.py", "src(old)/shared/ai_services/__init__.py", "src(old)/security/__init__.py", "src(old)/bot/extensions/help.py", "src(old)/core/validation/__init__.py", "src(old)/bot/pipeline/commands/ask/errors/error_manager.py", "src(old)/analysis/risk/calculators/volatility_calculator.py", "src(old)/analysis/orchestration/enhancement_strategy.py", "src(old)/api/data/providers/modules/auditing.py", "src(old)/bot/pipeline/commands/ask/security/security_scanner.py", "src(old)/shared/ai_services/local_fallback_ai.py", "src(old)/database/migrations/env.py", "src(old)/bot/main.py", "src(old)/shared/technical_analysis/test_indicators.py", "src(old)/database/migrations/__init__.py", "src(old)/core/formatting/response_templates.py", "src(old)/bot/permissions.py", "src(old)/bot/utils/error_handler.py", "src(old)/shared/market_analysis/confidence_scorer.py", "src(old)/core/market_calendar.py", "src(old)/core/prompts/utils/context_injection.py", "src(old)/bot/pipeline/commands/ask/stages/formatter.py", "src(old)/shared/monitoring/performance_monitor.py", "src(old)/bot/utils/rate_limiter.py", "src(old)/shared/monitoring/pipeline_monitor.py", "src(old)/core/prompts/commands/ask_prompt_replacement_ideas.py", "src(old)/core/__init__.py", "src(old)/bot/pipeline/core/context_manager.py", "src(old)/core/logger.py", "src(old)/core/monitoring_pkg/performance_tracker.py", "src(old)/core/secure_cache.py", "src(old)/core/prompts/services/__init__.py", "src(old)/bot/pipeline/logs/__init__.py", "src(old)/core/prompts/services/anti_hallucination.py", "src(old)/analysis/technical/__init__.py", "src(old)/bot/pipeline/commands/ask/config/__init__.py", "src(old)/shared/ai_debugger/live_ai_debugger.py", "src(old)/api/data/providers/finnhub.py", "src(old)/bot/pipeline/commands/analyze/stages/enhanced_analysis.py", "src(old)/bot/pipeline/commands/ask/performance/smart_cache.py", "src(old)/shared/watchlist/models.py", "src(old)/bot/pipeline/commands/ask/performance/connection_pool.py", "src(old)/api/routes/market_data.py", "src(old)/data/__init__.py", "src(old)/bot/pipeline/commands/ask/cache/unified_cache.py", "src(old)/shared/data_providers/alpha_vantage.py", "src(old)/bot/monitoring/__init__.py", "src(old)/bot/pipeline/commands/ask/performance/__init__.py", "src(old)/api/data/__init__.py", "src(old)/bot/pipeline/core/parallel_pipeline.py", "src(old)/bot/pipeline/commands/watchlist/stages/__init__.py", "src(old)/bot/pipeline/commands/ask/config/secrets_manager.py", "src(old)/bot/pipeline/commands/ask/observability/health_checker.py", "src(old)/bot/pipeline/data/__init__.py", "src(old)/database/models/alerts.py", "src(old)/shared/metrics/naming_conventions.py", "src(old)/shared/background/__init__.py", "src(old)/bot/pipeline/commands/ask/architecture/__init__.py", "src(old)/analysis/ai/calculators/__init__.py", "src(old)/api/data/scheduled_tasks.py", "src(old)/bot/pipeline/commands/ask/cleanup/legacy_archiver.py", "src(old)/bot/ai/context_aware_processor.py", "src(old)/shared/ai_chat/config.py", "src(old)/shared/technical_analysis/signal_generator.py", "src(old)/bot/pipeline/commands/ask/deployment/documentation.py", "src(old)/bot/pipeline/commands/analyze/pipeline.py", "src(old)/analysis/ai/ml_training_service.py", "src(old)/bot/pipeline/ask/stages/conversation_memory_service.py", "src(old)/shared/ai_services/ai_service_wrapper.py", "src(old)/shared/sentiment/sentiment_analyzer.py", "src(old)/bot/pipeline_framework.py", "src(old)/bot/pipeline/commands/ask/security/input_validator.py", "src(old)/api/data/providers/data_source_manager.py", "src(old)/shared/error_handling/logging.py", "src(old)/mcp_server/free_mcp_registry.py", "src(old)/bot/pipeline/commands/ask/observability/logger.py", "src(old)/bot/pipeline/commands/ask/compliance/__init__.py", "src(old)/database/config.py", "src(old)/bot/pipeline/commands/ask/stages/query_analyzer.py", "src(old)/bot/pipeline/commands/ask/security/rate_limiter.py", "src(old)/shared/technical_analysis/enhanced_calculator.py", "src(old)/core/automation/analysis_scheduler.py", "src(old)/core/trade_scanner.py", "src(old)/shared/ai_chat/models.py", "src(old)/shared/technical_analysis/calculator.py", "src(old)/core/automation/report_formatter.py", "src(old)/api/schemas/__init__.py", "src(old)/shared/technical_analysis/__init__.py", "src(old)/bot/pipeline/commands/analyze/stages/price_targets.py", "src(old)/shared/technical_analysis/options_greeks_calculator.py", "src(old)/analysis/ai/calculators/sentiment_calculator.py", "src(old)/core/config_manager.py", "src(old)/bot/extensions/analyze.py", "src(old)/bot/pipeline/commands/ask/cleanup/__init__.py", "src(old)/bot/pipeline/commands/ask/security/simplified_security.py", "src(old)/bot/pipeline/commands/ask/core/__init__.py", "src(old)/api/middleware/security.py", "src(old)/shared/cache/cache_service.py", "src(old)/core/utils.py", "src(old)/bot/extensions/recommendations.py", "src(old)/database/models/interactions.py", "src(old)/core/automation/report_scheduler.py", "src(old)/bot/pipeline/commands/ask/quality/code_standards.py", "src(old)/core/validation/financial_validator.py", "src(old)/bot/pipeline/commands/ask/security/test_security_scanner.py", "src(old)/bot/audit/rate_limiter.py", "src(old)/bot/pipeline/commands/ask/cost/__init__.py", "src(old)/api/data/providers/alpha_vantage.py", "src(old)/bot/core/bot.py", "src(old)/core/prompts/commands/analyze_prompts.py", "src(old)/shared/technical_analysis/unified_calculator.py", "src(old)/core/prompts/utils/validation.py", "src(old)/analysis/sentiment/enhanced_sentiment_analyzer.py", "src(old)/shared/ai_services/rate_limit_handler.py"], "keep_files": [], "recommendations": ["Delete 481 unused files to reduce complexity", "Very low usage percentage - consider major refactoring"]}}