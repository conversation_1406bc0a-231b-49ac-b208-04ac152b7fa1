# ASK Pipeline Documentation

## Overview

The ASK Pipeline is an advanced AI-powered trading assistant for Discord. It provides intelligent market analysis, technical indicators, and trading insights through natural language queries. The system has been upgraded to enterprise-grade standards with comprehensive observability, security, and performance monitoring.

## Architecture

### Core Components

1. **Intent Detection** - AI-powered query understanding using unified AI processor
2. **Tool Orchestration** - MCP integration with fallback strategies
3. **Response Generation** - AI synthesis with context-aware prompting
4. **Error Coordination** - Advanced error handling with circuit breakers
5. **Caching System** - Multi-level Redis caching with intelligent TTL
6. **Observability** - Structured logging, metrics, and performance grading
7. **Audit & Compliance** - Comprehensive logging and data governance
8. **Cost Tracking** - Resource optimization and budget management
9. **Security** - Input validation, rate limiting, and threat detection

### Data Flow

```
User Query → Security Validation → Rate Limiting → Intent Detection → Tool Orchestration → Response Synthesis → Output Sanitization → Discord Response
     ↓              ↓                 ↓                ↓                 ↓                  ↓                 ↓                ↓
Observability → Audit Logging → Cost Tracking → Performance Monitoring → Cache Management → Error Handling → Health Checks
```

## Configuration

### Environment Variables

#### Core Configuration
- `ASK_INTENT_TIMEOUT` - Intent detection timeout (default: 10.0s)
- `ASK_MAX_CONCURRENT_TOOLS` - Maximum concurrent tool executions (default: 5)
- `ASK_RESPONSE_MODEL` - AI model for response generation (default: gpt-4-mini)
- `ASK_MAX_TOKENS` - Maximum tokens for AI responses (default: 1500)
- `ASK_CACHE_ENABLED` - Enable caching (default: true)
- `REDIS_URL` - Redis connection URL

#### Monitoring & Observability
- `ASK_METRICS_ENABLED` - Enable metrics collection (default: true)
- `ASK_LOG_LEVEL` - Logging level (DEBUG/INFO/WARNING/ERROR) (default: INFO)
- `ASK_OBSERVABILITY_LEVEL` - Observability level (basic/extended/full) (default: full)

#### Security
- `ASK_RATE_LIMITING_ENABLED` - Enable rate limiting (default: true)
- `ASK_MAX_REQUESTS_PER_MINUTE` - Maximum requests per minute (default: 100)
- `ASK_INPUT_VALIDATION_ENABLED` - Enable input validation (default: true)

#### Feature Flags
- `ASK_USE_AI_SYNTHESIS` - Enable AI response synthesis (default: true)
- `ASK_PARALLEL_TOOLS` - Enable parallel tool execution (default: true)
- `ASK_ADAPTIVE_CACHING` - Enable intelligent TTL (default: true)

### Configuration Structure

The ASK configuration is managed through `AskConfig` dataclass with the following sections:

```python
@dataclass
class AskConfig:
    intent_detection: IntentDetectionConfig
    tools: ToolsConfig
    response: ResponseConfig
    cache: CacheConfig
    monitoring: MonitoringConfig
    security: SecurityConfig
    max_response_time: float = 3.0
    target_success_rate: float = 0.99
    environment: ConfigEnvironment = ConfigEnvironment.DEVELOPMENT
    feature_flags: Dict[str, bool]
    ab_testing: Dict[str, Any]
```

## Usage

### Basic Usage

```python
from src.bot.pipeline.commands.ask import get_ask_config
from src.bot.pipeline.commands.ask.core.controller import AskPipelineController

# Get configuration
config = get_ask_config(user_id="user123")

# Create pipeline controller
controller = AskPipelineController()

# Process user query
result = await controller.process(
    query="What's the current price of AAPL?",
    user_id="user123"
)

# Result contains response, execution time, and metadata
print(result.response)
```

### Advanced Usage with Observability

```python
import asyncio
from src.shared.monitoring.observability import start_operation_tracking, end_operation_tracking
from src.bot.pipeline.commands.ask.core.controller import AskPipelineController

async def handle_query(query: str, user_id: str):
    # Start operation tracking
    operation_id = start_operation_tracking(
        operation_name="user_query_processing",
        correlation_id="req-123",
        user_id=user_id
    )

    try:
        controller = AskPipelineController()
        result = await controller.process(query, user_id)
        
        # Log success metrics
        end_operation_tracking(operation_id, success=True)
        
        return result
    except Exception as e:
        # Log error metrics
        end_operation_tracking(operation_id, success=False, error_message=str(e))
        raise

# Example usage
asyncio.run(handle_query("Analyze TSLA technicals", "user123"))
```

## Security Features

### Input Validation

All user inputs are validated against security rules:

- Length limits on queries (max 1000 characters)
- Pattern matching for SQL injection, XSS, command injection
- Type validation for structured data
- Whitelist validation for user IDs and session IDs
- Automatic sanitization of potentially dangerous content

### Rate Limiting

Multi-level rate limiting protects against abuse:

- Global rate limit: 100 requests/minute
- User rate limit: 50 requests/minute
- IP rate limit: 200 requests/minute
- Burst protection: Maximum 5 requests in 10 seconds

### Threat Detection

Real-time scanning for:

- SQL injection patterns
- XSS attempts
- Command injection attempts
- Malformed input detection
- Suspicious activity monitoring

## Performance Monitoring

### Pipeline Grading

Each pipeline execution is graded on:

- **Data Quality (70%)**: Response accuracy and completeness
- **Performance (20%)**: Execution time and resource usage
- **Reliability (10%)**: Success rate and error handling

### Metrics Collected

- Response time (P95, P99 percentiles)
- Success rate per operation
- Error rates by component
- Cache hit/miss ratios
- AI token usage and costs
- Database query performance

### Observability Integration

- Structured logging with correlation IDs
- Distributed tracing across services
- Real-time health checks
- Alerting on performance degradation
- Comprehensive audit trails

## Deployment

### Docker Setup

```dockerfile
# Dockerfile
FROM python:3.11-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

EXPOSE 8000

CMD ["python", "src/bot/main.py"]
```

### Docker Compose

```yaml
# docker-compose.yml
version: '3.11'
services:
  bot:
    build: .
    environment:
      - ENVIRONMENT=production
      - REDIS_URL=redis://redis:6379
      - LOG_LEVEL=INFO
    depends_on:
      - redis
    volumes:
      - ./logs:/app/logs

  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
```

### Kubernetes Deployment

```yaml
# k8s-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ask-bot
spec:
  replicas: 3
  selector:
    matchLabels:
      app: ask-bot
  template:
    metadata:
      labels:
        app: ask-bot
    spec:
      containers:
      - name: ask-bot
        image: ask-bot:latest
        env:
        - name: ENVIRONMENT
          value: "production"
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
```

## Testing

### Unit Tests

```python
# tests/test_intent_detector.py
import pytest
from src.bot.pipeline.commands.ask.stages.intent_detector import IntentDetector

@pytest.fixture
def intent_detector():
    return IntentDetector()

def test_intent_detection(intent_detector):
    result = intent_detector.detect("What's the price of AAPL?")
    assert result.intent == "price_check"
    assert result.confidence > 0.8

def test_tool_orchestration():
    from src.bot.pipeline.commands.ask.stages.simplified_tool_orchestrator import get_tool_orchestrator
    orchestrator = get_tool_orchestrator()
    result = asyncio.run(orchestrator.execute("AAPL", None, "req-123"))
    assert result.success or result.error is not None
```

### Integration Tests

```python
# tests/integration/test_pipeline.py
import asyncio
from src.bot.pipeline.commands.ask.core.controller import AskPipelineController

async def test_full_pipeline():
    controller = AskPipelineController()
    result = await controller.process("Show me AAPL chart", "test_user")
    
    assert result.success
    assert result.response is not None
    assert result.execution_time > 0
    assert len(result.response) > 10

# Run with: pytest tests/integration/test_pipeline.py -v
```

### Performance Tests

```python
# tests/performance/test_load.py
import asyncio
import time
from concurrent.futures import ThreadPoolExecutor
from src.bot.pipeline.commands.ask.core.controller import AskPipelineController

async def single_query(query):
    controller = AskPipelineController()
    start = time.time()
    result = await controller.process(query, "load_test_user")
    return time.time() - start

async def load_test(concurrency=10, queries_per_worker=100):
    controller = AskPipelineController()
    tasks = []
    
    for i in range(concurrency):
        task = asyncio.create_task(
            asyncio.gather(*[single_query(f"Query {j}") for j in range(queries_per_worker)])
        )
        tasks.append(task)
    
    results = await asyncio.gather(*tasks)
    avg_time = sum(sum(worker_results) for worker_results in results) / (concurrency * queries_per_worker)
    
    print(f"Average response time: {avg_time:.2f}s")
    assert avg_time < 3.0  # Target < 3s under load

# Run with: pytest tests/performance/test_load.py
```

## Monitoring & Maintenance

### Health Checks

```bash
# Check service health
curl http://localhost:8000/health

# Expected response:
{
  "status": "healthy",
  "services": {
    "redis": "healthy",
    "ai_client": "healthy",
    "database": "healthy"
  },
  "metrics": {
    "uptime": "99.9%",
    "response_time_p95": "1.2s"
  }
}
```

### Log Analysis

```bash
# View recent pipeline grades
cat logs/pipeline_grades/*.json | jq '.grade'

# Monitor error rates
grep "ERROR" logs/development.log | wc -l

# Check cache performance
python -c "
from src.shared.cache.cache_service import cache_service
import asyncio
stats = asyncio.run(cache_service.get_stats())
print(f'Hit Rate: {stats[\"cache_performance\"][\"hit_rate\"]:.2%}')
"
```

## Troubleshooting

### Common Issues

1. **Redis Connection Failed**
   - Check `REDIS_URL` environment variable
   - Verify Redis service is running: `docker ps | grep redis`
   - Check network connectivity

2. **AI API Rate Limits**
   - Monitor `ASK_MAX_REQUESTS_PER_MINUTE`
   - Check cost tracking: `python scripts/cost_report.py`
   - Implement caching for repeated queries

3. **Pipeline Timeouts**
   - Increase `ASK_MAX_RESPONSE_TIME` if needed
   - Check tool execution times in logs
   - Review `max_concurrent` settings

4. **Configuration Errors**
   - Validate config: `python -c "from src.bot.pipeline.commands.ask.config.ask_config import get_ask_config; config = get_ask_config(); config.validate()"`
   - Check environment variables

### Log Levels

- **DEBUG**: Development - Full tracing
- **INFO**: Production - Normal operation
- **WARNING**: Non-critical issues
- **ERROR**: Errors that affect functionality
- **CRITICAL**: System failures

Set with: `export LOG_LEVEL=INFO`

## Development Setup

1. **Clone and Install**
   ```bash
   git clone <repo-url>
   cd tradingview-automatio
   pip install -r requirements.txt
   ```

2. **Environment Setup**
   ```bash
   cp .env.example .env
   # Edit .env with your API keys
   export ENVIRONMENT=development
   ```

3. **Run Development Server**
   ```bash
   python src/bot/main.py
   ```

4. **Run Tests**
   ```bash
   pytest tests/ -v
   ```

5. **Monitor Performance**
   ```bash
   # In another terminal
   tail -f logs/development.log
   ```

## Production Deployment

1. **Build Docker Image**
   ```bash
   docker build -t ask-bot .
   ```

2. **Run with Docker Compose**
   ```bash
   docker-compose up -d
   ```

3. **Deploy to Kubernetes**
   ```bash
   kubectl apply -f k8s-deployment.yaml
   ```

4. **Monitor Production**
   ```bash
   # Check pod status
   kubectl get pods
   
   # View logs
   kubectl logs -f deployment/ask-bot
   
   # Check metrics
   curl http://ask-bot-service:8000/metrics
   ```

## Support

For issues and feature requests:

- **GitHub Issues**: Report bugs and suggest improvements
- **Discord**: Join the development community
- **Documentation**: Refer to this guide for setup and troubleshooting

---

*Last Updated: 2025-09-28*  
*Version: 2.0 - Enterprise Edition*