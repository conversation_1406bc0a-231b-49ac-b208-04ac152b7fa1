version: '3.8'

# NOTICE: This file has been moved to docker/compose/development.yml
# Please use: docker-compose -f docker/compose/development.yml up -d
# This file is kept for backward compatibility

services:
  # Main API service
  api:
    build:
      context: .
      target: development
    container_name: tradingview-api-dev
    command: uvicorn src.api.main:app --host 0.0.0.0 --port 8000 --reload
    volumes:
      - ./src:/app/src
      - ./config:/app/config:ro
      - ./logs:/app/logs
      - ./data:/app/data
      - ./tests:/app/tests
    environment:
      - ENVIRONMENT=development
      - DEBUG=true
      - PYTHONPATH=/app
      - LOG_LEVEL=INFO
      - REDIS_URL=redis://${REDIS_PASSWORD}@redis:6379/0
      - JWT_SECRET=${JWT_SECRET}
      - USE_SUPABASE=true
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - DATABASE_URL=${DATABASE_URL}
      - SUPABASE_FALLBACK_IP=${SUPABASE_FALLBACK_IP}
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - MODEL_GLOBAL_FALLBACK=${MODEL_GLOBAL_FALLBACK}
      - POLYGON_API_KEY=${POLYGON_API_KEY}
      - FINNHUB_API_KEY=${FINNHUB_API_KEY}
      - ALPACA_API_KEY=${ALPACA_API_KEY}
      - DISCORD_BOT_TOKEN=${DISCORD_BOT_TOKEN}
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    ports:
      - "8000:8000"
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - internal-network
      - tradingview-network
    restart: unless-stopped
    env_file: .env
    dns:
      - *******
      - *******

  # Discord bot service
  discord-bot:
    build:
      context: .
      target: development
    container_name: tradingview-discord-bot-dev
    command: python -m src.bot.main
    environment:
      - ENVIRONMENT=development
      - PYTHONPATH=/app
      - LOG_LEVEL=INFO
      - REDIS_URL=redis://${REDIS_PASSWORD}@redis:6379/0
      - DISCORD_BOT_TOKEN=${DISCORD_BOT_TOKEN}
      # AI Model Configuration - Using cheap gpt-4o-mini model
      - AI_MODEL=gpt-4o-mini                                     # Very cheap and fast model
      - MODEL_QUICK=gpt-4o-mini                                  # Very cheap and fast model
      - MODEL_ANALYSIS=gpt-4o-mini                               # Cheap but capable model
      - MODEL_HEAVY=gpt-4o-mini                                  # Cheap but capable model
      - MODEL_GLOBAL_FALLBACK=gpt-4o-mini                        # Cheap and reliable fallback
      - MODEL_LLM=gpt-4o-mini                                    # Cheap but capable model

      - DISCORD_GUILD_ID=${DISCORD_GUILD_ID:-}
      - USE_SUPABASE=true
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - DATABASE_URL=${DATABASE_URL}
      - SUPABASE_FALLBACK_IP=${SUPABASE_FALLBACK_IP}
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - POLYGON_API_KEY=${POLYGON_API_KEY}
      - FINNHUB_API_KEY=${FINNHUB_API_KEY}
      - ALPACA_API_KEY=${ALPACA_API_KEY}
      - JWT_SECRET=${JWT_SECRET}
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - API_BASE_URL=http://tradingview-api-dev:8000
      # Fix yfinance cache permission issues
      - YFINANCE_CACHE_DIR=/tmp/yfinance_cache
      # MCP Server Configuration - Disabled until proper setup
      # - MCP_ALPHAVANTAGE_URL=https://mcp.alphavantage.co/mcp?apikey=${ALPHA_VANTAGE_API_KEY}
      # - MCP_DUCKDUCKGO_URL=http://tradingview-mcp-servers-dev:3001
      # - MCP_ARXIV_URL=http://tradingview-mcp-servers-dev:3002
      # - MCP_REDDIT_URL=http://tradingview-mcp-servers-dev:3003
      # - MCP_FETCH_URL=http://tradingview-mcp-servers-dev:3004
      # - MCP_TIME_URL=http://tradingview-mcp-servers-dev:3005
      # - MCP_TRADING_URL=http://tradingview-mcp-servers-dev:3006
    volumes:
      - ./src:/app/src
      - ./config:/app/config:ro
      - ./logs:/app/logs
      - ./data:/app/data
      - ./tests:/app/tests
    depends_on:
      - redis
      # - mcp-servers  # Disabled until proper setup
    networks:
      - tradingview-network
      - external-network
      - mcp-network
    dns:
      - *******  # Google DNS
      - *******  # Cloudflare DNS
    extra_hosts:
      - "host.docker.internal:host-gateway"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python", "-c", "import asyncio; from src.bot.client import TradingBot; print('Health check passed')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    env_file: .env

  # Redis cache
  redis:
    image: redis:7-alpine
    container_name: tradingview-redis-dev
    command: redis-server --requirepass ${REDIS_PASSWORD} --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - internal-network
      - tradingview-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD}", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
    env_file: .env

  # Webhook ingest service
  webhook-ingest:
    build:
      context: ./tradingview-ingest
      dockerfile: Dockerfile
    container_name: tradingview-webhook-ingest-dev
    ports:
      - "8001:8001"
    environment:
      - ENVIRONMENT=development
      - DEBUG=true
      - PYTHONPATH=/app
      - LOG_LEVEL=INFO
      - REDIS_URL=redis://${REDIS_PASSWORD}@redis:6379/0
      - DATABASE_URL=${DATABASE_URL}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - USE_SUPABASE=true
      - WEBHOOK_SECRET=${WEBHOOK_SECRET}
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    volumes:
      - ./tradingview-ingest/src:/app/src
      - ./tradingview-ingest/config:/app/config:ro
      - ./logs:/app/logs
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - internal-network
      - tradingview-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    dns:
      - *******
      - *******
    env_file: .env

  # MCP Servers - Disabled until proper setup
  # mcp-servers:
  #   build:
  #     context: ./docker/mcp-server
  #     dockerfile: Dockerfile
  #   container_name: tradingview-mcp-servers-dev
  #   # ... rest of config commented out

volumes:
  redis_data:

networks:
  tradingview-network:
    driver: bridge
    internal: false
    enable_ipv6: true
    ipam:
      config:
        - subnet: 2001:3984:3989::/64
          gateway: 2001:3984:3989::1
  
  internal-network:
    driver: bridge
    internal: true  # Keep core services isolated
    enable_ipv6: true
    ipam:
      config:
        - subnet: 2001:3984:3990::/64
          gateway: 2001:3984:3990::1

  external-network:
    driver: bridge
    # Allow both external and internal communication
    enable_ipv6: true
    ipam:
      config:
        - subnet: 2001:3984:3991::/64
          gateway: 2001:3984:3991::1

  mcp-network:
    driver: bridge
    internal: false  # Allow external API access
    enable_ipv6: true
    ipam:
      config:
        - subnet: 2001:3984:3992::/64
          gateway: 2001:3984:3992::1