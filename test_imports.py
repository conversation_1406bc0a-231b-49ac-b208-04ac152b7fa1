#!/usr/bin/env python3
"""
Test script to verify imports work correctly
"""

try:
    from src.bot.core.security.auth import AuthManager, AuthResult
    print("✅ AuthManager import successful")
except ImportError as e:
    print(f"❌ AuthManager import failed: {e}")

try:
    from src.bot.core.security.input_validator import InputValidator, ValidationResult
    print("✅ InputValidator import successful")
except ImportError as e:
    print(f"❌ InputValidator import failed: {e}")

try:
    from src.bot.core.security.rate_limiter import RateLimiter, RateLimitResult
    print("✅ RateLimiter import successful")
except ImportError as e:
    print(f"❌ RateLimiter import failed: {e}")

try:
    from src.bot.core.security.security_scanner import SecurityScanner, SecurityResult
    print("✅ SecurityScanner import successful")
except ImportError as e:
    print(f"❌ SecurityScanner import failed: {e}")

print("\nTesting basic functionality:")

try:
    auth_manager = AuthManager()
    print("✅ AuthManager instantiation successful")
except Exception as e:
    print(f"❌ AuthManager instantiation failed: {e}")

try:
    validator = InputValidator()
    print("✅ InputValidator instantiation successful")
except Exception as e:
    print(f"❌ InputValidator instantiation failed: {e}")
