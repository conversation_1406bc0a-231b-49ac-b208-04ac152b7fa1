# src(old) References Report

**Total References Found:** 33

## Summary by Type

- **comment**: 1
- **import**: 3
- **string_literal**: 29

## Summary by Confidence

- **high**: 17
- **low**: 13
- **medium**: 3

## Detailed References

### comprehensive_refactoring_plan.py

**Line 19** (string_literal, low confidence)
```
self.src_old_path = Path("src(old)")
```
- **Old:** `src(old)`
- **Suggested:** `src`

### deep_execution_tracer.py

**Line 17** (string_literal, low confidence)
```
sys.path.insert(0, str(Path("src(old)").absolute()))
```
- **Old:** `src(old)`
- **Suggested:** `src`

**Line 148** (string_literal, low confidence)
```
tracer = DeepExecutionTracer("src(old)")
```
- **Old:** `src(old)`
- **Suggested:** `src`

**Line 178** (string_literal, low confidence)
```
used_files = find_actual_used_files(all_imported, Path("src(old)"))
```
- **Old:** `src(old)`
- **Suggested:** `src`

**Line 189** (string_literal, low confidence)
```
for py_file in Path("src(old)").rglob("*.py"):
```
- **Old:** `src(old)`
- **Suggested:** `src`

### src/shared/data_providers/MIGRATION_GUIDE.md

**Line 18** (import, medium confidence)
```
from src(old).shared.ai_chat.data_fetcher import DataFetcher
```
- **Old:** `src(old).shared.ai_chat.data_fetcher`
- **Suggested:** `src.shared.ai_chat.data_fetcher`

**Line 70** (import, medium confidence)
```
from src(old).shared.ai_chat.data_fetcher import DataFetcher
```
- **Old:** `src(old).shared.ai_chat.data_fetcher`
- **Suggested:** `src.shared.ai_chat.data_fetcher`

**Line 71** (import, medium confidence)
```
from src(old).shared.ai_chat.models import AIAskResult
```
- **Old:** `src(old).shared.ai_chat.models`
- **Suggested:** `src.shared.ai_chat.models`

### src_old_reference_scanner.py

**Line 56** (string_literal, high confidence)
```
"src(old).mcp_server": "src.mcp",
```
- **Old:** `src(old).mcp_server`
- **Suggested:** `src.mcp`

**Line 57** (string_literal, high confidence)
```
"src(old).shared": "src.shared",
```
- **Old:** `src(old).shared`
- **Suggested:** `src.shared`

**Line 58** (string_literal, high confidence)
```
"src(old).core": "src.core",
```
- **Old:** `src(old).core`
- **Suggested:** `src.core`

**Line 59** (string_literal, high confidence)
```
"src(old).data": "src.database",  # Main data models moved here
```
- **Old:** `src(old).data`
- **Suggested:** `src.database`

**Line 60** (string_literal, high confidence)
```
"src(old).bot": "src.bot",
```
- **Old:** `src(old).bot`
- **Suggested:** `src.bot`

**Line 61** (string_literal, high confidence)
```
"src(old).security": "src.security",
```
- **Old:** `src(old).security`
- **Suggested:** `src.security`

**Line 62** (string_literal, high confidence)
```
"src(old).services": "src.services",
```
- **Old:** `src(old).services`
- **Suggested:** `src.services`

**Line 63** (string_literal, high confidence)
```
"src(old).templates": "src.core.prompts",  # likely moved here
```
- **Old:** `src(old).templates`
- **Suggested:** `src.core.prompts`

**Line 64** (string_literal, high confidence)
```
"src(old).utils": "src.utils",
```
- **Old:** `src(old).utils`
- **Suggested:** `src.utils`

**Line 67** (string_literal, high confidence)
```
"src(old).data.models": "src.database.models",
```
- **Old:** `src(old).data.models`
- **Suggested:** `src.database.models`

**Line 68** (string_literal, high confidence)
```
"src(old).data.cache": "src.shared.cache",  # likely moved
```
- **Old:** `src(old).data.cache`
- **Suggested:** `src.shared.cache`

**Line 69** (string_literal, high confidence)
```
"src(old).bot.enhancements": "src.bot.commands",  # likely consolidated
```
- **Old:** `src(old).bot.enhancements`
- **Suggested:** `src.bot.commands`

**Line 70** (string_literal, high confidence)
```
"src(old).bot.events": "src.bot.commands",  # likely consolidated
```
- **Old:** `src(old).bot.events`
- **Suggested:** `src.bot.commands`

**Line 71** (string_literal, high confidence)
```
"src(old).bot.security": "src.security",  # likely moved up
```
- **Old:** `src(old).bot.security`
- **Suggested:** `src.security`

**Line 72** (string_literal, high confidence)
```
"src(old).core.secure_cache": "src.shared.cache",  # likely moved
```
- **Old:** `src(old).core.secure_cache`
- **Suggested:** `src.shared.cache`

**Line 73** (string_literal, high confidence)
```
"src(old).services.analytics_service": "src.services.ai",  # likely moved
```
- **Old:** `src(old).services.analytics_service`
- **Suggested:** `src.services.ai`

**Line 74** (string_literal, high confidence)
```
"src(old).templates.analysis_response": "src.core.prompts",  # likely moved
```
- **Old:** `src(old).templates.analysis_response`
- **Suggested:** `src.core.prompts`

**Line 96** (string_literal, low confidence)
```
old_module = f"src(old).{match.group(1)}" if match.groups() else "src(old)"
```
- **Old:** `src(old).{match.group(1)}`
- **Suggested:** `src.{match.group(1)}`

**Line 96** (string_literal, low confidence)
```
old_module = f"src(old).{match.group(1)}" if match.groups() else "src(old)"
```
- **Old:** `src(old)`
- **Suggested:** `src`

**Line 112** (string_literal, low confidence)
```
old_module = f"src(old).{match.group(1)}" if match.groups() else "src(old)"
```
- **Old:** `src(old).{match.group(1)}`
- **Suggested:** `src.{match.group(1)}`

**Line 112** (string_literal, low confidence)
```
old_module = f"src(old).{match.group(1)}" if match.groups() else "src(old)"
```
- **Old:** `src(old)`
- **Suggested:** `src`

**Line 128** (string_literal, low confidence)
```
old_module = f"src(old).{match.group(1)}" if match.groups() else "src(old)"
```
- **Old:** `src(old).{match.group(1)}`
- **Suggested:** `src.{match.group(1)}`

**Line 128** (string_literal, low confidence)
```
old_module = f"src(old).{match.group(1)}" if match.groups() else "src(old)"
```
- **Old:** `src(old)`
- **Suggested:** `src`

**Line 158** (string_literal, low confidence)
```
return old_module.replace("src(old)", "src")
```
- **Old:** `src(old)`
- **Suggested:** `src`

**Line 221** (comment, low confidence)
```
"# src(old) References Report",
```
- **Old:** `src(old)`
- **Suggested:** `src`
