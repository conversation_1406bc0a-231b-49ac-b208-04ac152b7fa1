
#!/usr/bin/env python3
"""
Comprehensive Refactoring Plan
Creates a detailed plan for refactoring the src(old) folder
"""

import os
import json
from pathlib import Path
from typing import Dict, List, Any, Set
from collections import defaultdict

class ComprehensiveRefactoringPlan:
    """Creates a comprehensive refactoring plan"""
    
    def __init__(self):
        self.root_path = Path(".")
        self.src_old_path = Path("src(old)")
        self.src_path = Path("src")
        
    def analyze_current_state(self) -> Dict[str, Any]:
        """Analyze the current state of the codebase"""
        print("🔍 Analyzing current state...")
        
        # Get all files in src(old)
        all_files = []
        for py_file in self.src_old_path.rglob("*.py"):
            if "__pycache__" not in str(py_file):
                rel_path = str(py_file.relative_to(self.root_path))
                all_files.append(rel_path)
        
        # Analyze by module
        modules = defaultdict(list)
        for file_path in all_files:
            if file_path.startswith('src(old)/'):
                module = file_path.replace('src(old)/', '').split('/')[0]
                modules[module].append(file_path)
        
        return {
            "total_files": len(all_files),
            "all_files": all_files,
            "modules": dict(modules),
            "module_counts": {k: len(v) for k, v in modules.items()}
        }
    
    def identify_critical_issues(self) -> Dict[str, Any]:
        """Identify critical issues that need to be fixed"""
        print("🚨 Identifying critical issues...")
        
        issues = {
            "missing_files": [
                "src.bot.client.create_bot",
                "src.shared.ai_services.timeout_manager.timeout_manager",
                "src.shared.ai_services.circuit_breaker.ai_circuit_breaker_manager",
                "src.shared.ai_services.query_cache.simple_query_cache",
                "src.shared.monitoring.performance_monitor.performance_monitor",
                "src.core.automation.report_scheduler.initialize_ai_report_scheduler",
                "src.core.automation.discord_handler.DiscordWebhookHandler"
            ],
            "broken_imports": [],
            "circular_dependencies": [],
            "dead_code": [],
            "architectural_issues": []
        }
        
        # Check for missing files
        missing_files = []
        for missing_file in issues["missing_files"]:
            if missing_file.startswith('src.'):
                module_path = missing_file[4:]  # Remove 'src.' prefix
                candidate = self.src_old_path / f"{module_path}.py"
                if not candidate.exists():
                    missing_files.append(missing_file)
        
        issues["missing_files"] = missing_files
        
        return issues
    
    def create_minimal_src_structure(self) -> Dict[str, Any]:
        """Create a minimal src structure with only essential files"""
        print("🏗️ Creating minimal src structure...")
        
        # Essential modules based on entry points
        essential_modules = {
            "bot": {
                "client.py": "Main bot client with create_bot function",
                "main.py": "Bot main entry point",
                "__init__.py": "Bot module init"
            },
            "shared": {
                "ai_services": {
                    "timeout_manager.py": "Timeout management for AI services",
                    "circuit_breaker.py": "Circuit breaker for AI services",
                    "query_cache.py": "Query caching system",
                    "__init__.py": "AI services module init"
                },
                "monitoring": {
                    "performance_monitor.py": "Performance monitoring",
                    "__init__.py": "Monitoring module init"
                },
                "__init__.py": "Shared module init"
            },
            "core": {
                "automation": {
                    "report_scheduler.py": "AI report scheduling",
                    "discord_handler.py": "Discord webhook handling",
                    "__init__.py": "Automation module init"
                },
                "__init__.py": "Core module init"
            }
        }
        
        return {
            "structure": essential_modules,
            "total_files": self._count_files_in_structure(essential_modules)
        }
    
    def _count_files_in_structure(self, structure: Dict) -> int:
        """Count total files in structure"""
        count = 0
        for key, value in structure.items():
            if isinstance(value, dict):
                if key.endswith('.py'):
                    count += 1
                else:
                    count += self._count_files_in_structure(value)
            else:
                count += 1
        return count
    
    def generate_refactoring_plan(self) -> Dict[str, Any]:
        """Generate the complete refactoring plan"""
        print("📋 Generating refactoring plan...")
        
        current_state = self.analyze_current_state()
        critical_issues = self.identify_critical_issues()
        minimal_structure = self.create_minimal_src_structure()
        
        plan = {
            "current_state": current_state,
            "critical_issues": critical_issues,
            "minimal_structure": minimal_structure,
            "refactoring_steps": [
                {
                    "step": 1,
                    "title": "Create minimal src directory structure",
                    "description": "Create the basic directory structure for essential modules",
                    "files_to_create": self._get_files_to_create(minimal_structure),
                    "priority": "high"
                },
                {
                    "step": 2,
                    "title": "Fix missing critical files",
                    "description": "Create or restore missing critical files",
                    "files_to_fix": critical_issues["missing_files"],
                    "priority": "critical"
                },
                {
                    "step": 3,
                    "title": "Move essential files from src(old)",
                    "description": "Move only the files that are actually used",
                    "files_to_move": [],
                    "priority": "high"
                },
                {
                    "step": 4,
                    "title": "Clean up src(old)",
                    "description": "Remove unused files from src(old)",
                    "files_to_delete": [],
                    "priority": "medium"
                },
                {
                    "step": 5,
                    "title": "Test refactored code",
                    "description": "Ensure all entry points work correctly",
                    "tests": ["start_bot.py", "start_enhanced_bot.py", "start_ai_automation.py"],
                    "priority": "high"
                }
            ],
            "recommendations": [
                "Start with step 1 - create minimal structure",
                "Fix critical missing files before moving anything",
                "Test each step before proceeding to the next",
                "Keep src(old) as backup until everything is working",
                "Use Docker for testing to avoid local environment issues"
            ]
        }
        
        return plan
    
    def _get_files_to_create(self, structure: Dict) -> List[str]:
        """Get list of files to create"""
        files = []
        for module, contents in structure.items():
            if isinstance(contents, dict):
                for key, value in contents.items():
                    if key.endswith('.py'):
                        files.append(f"src/{module}/{key}")
                    elif isinstance(value, dict):
                        for sub_key, sub_value in value.items():
                            if sub_key.endswith('.py'):
                                files.append(f"src/{module}/{key}/{sub_key}")
        return files
    
    def print_plan(self, plan: Dict[str, Any]):
        """Print the refactoring plan"""
        print("\n" + "="*80)
        print("📋 COMPREHENSIVE REFACTORING PLAN")
        print("="*80)
        
        print(f"📊 CURRENT STATE:")
        print(f"  Total files in src(old): {plan['current_state']['total_files']}")
        print(f"  Modules: {len(plan['current_state']['modules'])}")
        for module, count in plan['current_state']['module_counts'].items():
            print(f"    - {module}: {count} files")
        
        print(f"\n🚨 CRITICAL ISSUES:")
        print(f"  Missing files: {len(plan['critical_issues']['missing_files'])}")
        for file in plan['critical_issues']['missing_files']:
            print(f"    - {file}")
        
        print(f"\n🏗️ MINIMAL STRUCTURE:")
        print(f"  Total files to create: {plan['minimal_structure']['total_files']}")
        print(f"  Essential modules: {list(plan['minimal_structure']['structure'].keys())}")
        
        print(f"\n📋 REFACTORING STEPS:")
        for step in plan['refactoring_steps']:
            print(f"  {step['step']}. {step['title']} ({step['priority']})")
            print(f"     {step['description']}")
            if 'files_to_create' in step:
                print(f"     Files to create: {len(step['files_to_create'])}")
            if 'files_to_fix' in step:
                print(f"     Files to fix: {len(step['files_to_fix'])}")
        
        print(f"\n💡 RECOMMENDATIONS:")
        for rec in plan['recommendations']:
            print(f"  - {rec}")

def main():
    """Main function"""
    print("🚀 Starting Comprehensive Refactoring Plan Generation")
    
    planner = ComprehensiveRefactoringPlan()
    plan = planner.generate_refactoring_plan()
    
    planner.print_plan(plan)
    
    # Save plan
    with open("comprehensive_refactoring_plan.json", "w") as f:
        json.dump(plan, f, indent=2)
    
    print(f"\n✅ Refactoring plan complete!")
    print(f"📄 Plan saved to: comprehensive_refactoring_plan.json")
    
    return plan

if __name__ == "__main__":
    main()
