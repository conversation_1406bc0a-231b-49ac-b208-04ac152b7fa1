{"scan_timestamp": "/home/<USER>/Desktop/tradingview-automatio", "total_references": 33, "module_mapping": {"src(old).mcp_server": "src.mcp", "src(old).shared": "src.shared", "src(old).core": "src.core", "src(old).data": "src.database", "src(old).bot": "src.bot", "src(old).security": "src.security", "src(old).services": "src.services", "src(old).templates": "src.core.prompts", "src(old).utils": "src.utils", "src(old).data.models": "src.database.models", "src(old).data.cache": "src.shared.cache", "src(old).bot.enhancements": "src.bot.commands", "src(old).bot.events": "src.bot.commands", "src(old).bot.security": "src.security", "src(old).core.secure_cache": "src.shared.cache", "src(old).services.analytics_service": "src.services.ai", "src(old).templates.analysis_response": "src.core.prompts"}, "references": [{"file_path": "deep_execution_tracer.py", "line_number": 17, "line_content": "sys.path.insert(0, str(Path(\"src(old)\").absolute()))", "reference_type": "string_literal", "old_module": "src(old)", "suggested_new_module": "src", "confidence": "low"}, {"file_path": "deep_execution_tracer.py", "line_number": 148, "line_content": "tracer = DeepExecutionTracer(\"src(old)\")", "reference_type": "string_literal", "old_module": "src(old)", "suggested_new_module": "src", "confidence": "low"}, {"file_path": "deep_execution_tracer.py", "line_number": 178, "line_content": "used_files = find_actual_used_files(all_imported, Path(\"src(old)\"))", "reference_type": "string_literal", "old_module": "src(old)", "suggested_new_module": "src", "confidence": "low"}, {"file_path": "deep_execution_tracer.py", "line_number": 189, "line_content": "for py_file in Path(\"src(old)\").rglob(\"*.py\"):", "reference_type": "string_literal", "old_module": "src(old)", "suggested_new_module": "src", "confidence": "low"}, {"file_path": "src_old_reference_scanner.py", "line_number": 56, "line_content": "\"src(old).mcp_server\": \"src.mcp\",", "reference_type": "string_literal", "old_module": "src(old).mcp_server", "suggested_new_module": "src.mcp", "confidence": "high"}, {"file_path": "src_old_reference_scanner.py", "line_number": 57, "line_content": "\"src(old).shared\": \"src.shared\",", "reference_type": "string_literal", "old_module": "src(old).shared", "suggested_new_module": "src.shared", "confidence": "high"}, {"file_path": "src_old_reference_scanner.py", "line_number": 58, "line_content": "\"src(old).core\": \"src.core\",", "reference_type": "string_literal", "old_module": "src(old).core", "suggested_new_module": "src.core", "confidence": "high"}, {"file_path": "src_old_reference_scanner.py", "line_number": 59, "line_content": "\"src(old).data\": \"src.database\",  # Main data models moved here", "reference_type": "string_literal", "old_module": "src(old).data", "suggested_new_module": "src.database", "confidence": "high"}, {"file_path": "src_old_reference_scanner.py", "line_number": 60, "line_content": "\"src(old).bot\": \"src.bot\",", "reference_type": "string_literal", "old_module": "src(old).bot", "suggested_new_module": "src.bot", "confidence": "high"}, {"file_path": "src_old_reference_scanner.py", "line_number": 61, "line_content": "\"src(old).security\": \"src.security\",", "reference_type": "string_literal", "old_module": "src(old).security", "suggested_new_module": "src.security", "confidence": "high"}, {"file_path": "src_old_reference_scanner.py", "line_number": 62, "line_content": "\"src(old).services\": \"src.services\",", "reference_type": "string_literal", "old_module": "src(old).services", "suggested_new_module": "src.services", "confidence": "high"}, {"file_path": "src_old_reference_scanner.py", "line_number": 63, "line_content": "\"src(old).templates\": \"src.core.prompts\",  # likely moved here", "reference_type": "string_literal", "old_module": "src(old).templates", "suggested_new_module": "src.core.prompts", "confidence": "high"}, {"file_path": "src_old_reference_scanner.py", "line_number": 64, "line_content": "\"src(old).utils\": \"src.utils\",", "reference_type": "string_literal", "old_module": "src(old).utils", "suggested_new_module": "src.utils", "confidence": "high"}, {"file_path": "src_old_reference_scanner.py", "line_number": 67, "line_content": "\"src(old).data.models\": \"src.database.models\",", "reference_type": "string_literal", "old_module": "src(old).data.models", "suggested_new_module": "src.database.models", "confidence": "high"}, {"file_path": "src_old_reference_scanner.py", "line_number": 68, "line_content": "\"src(old).data.cache\": \"src.shared.cache\",  # likely moved", "reference_type": "string_literal", "old_module": "src(old).data.cache", "suggested_new_module": "src.shared.cache", "confidence": "high"}, {"file_path": "src_old_reference_scanner.py", "line_number": 69, "line_content": "\"src(old).bot.enhancements\": \"src.bot.commands\",  # likely consolidated", "reference_type": "string_literal", "old_module": "src(old).bot.enhancements", "suggested_new_module": "src.bot.commands", "confidence": "high"}, {"file_path": "src_old_reference_scanner.py", "line_number": 70, "line_content": "\"src(old).bot.events\": \"src.bot.commands\",  # likely consolidated", "reference_type": "string_literal", "old_module": "src(old).bot.events", "suggested_new_module": "src.bot.commands", "confidence": "high"}, {"file_path": "src_old_reference_scanner.py", "line_number": 71, "line_content": "\"src(old).bot.security\": \"src.security\",  # likely moved up", "reference_type": "string_literal", "old_module": "src(old).bot.security", "suggested_new_module": "src.security", "confidence": "high"}, {"file_path": "src_old_reference_scanner.py", "line_number": 72, "line_content": "\"src(old).core.secure_cache\": \"src.shared.cache\",  # likely moved", "reference_type": "string_literal", "old_module": "src(old).core.secure_cache", "suggested_new_module": "src.shared.cache", "confidence": "high"}, {"file_path": "src_old_reference_scanner.py", "line_number": 73, "line_content": "\"src(old).services.analytics_service\": \"src.services.ai\",  # likely moved", "reference_type": "string_literal", "old_module": "src(old).services.analytics_service", "suggested_new_module": "src.services.ai", "confidence": "high"}, {"file_path": "src_old_reference_scanner.py", "line_number": 74, "line_content": "\"src(old).templates.analysis_response\": \"src.core.prompts\",  # likely moved", "reference_type": "string_literal", "old_module": "src(old).templates.analysis_response", "suggested_new_module": "src.core.prompts", "confidence": "high"}, {"file_path": "src_old_reference_scanner.py", "line_number": 96, "line_content": "old_module = f\"src(old).{match.group(1)}\" if match.groups() else \"src(old)\"", "reference_type": "string_literal", "old_module": "src(old).{match.group(1)}", "suggested_new_module": "src.{match.group(1)}", "confidence": "low"}, {"file_path": "src_old_reference_scanner.py", "line_number": 96, "line_content": "old_module = f\"src(old).{match.group(1)}\" if match.groups() else \"src(old)\"", "reference_type": "string_literal", "old_module": "src(old)", "suggested_new_module": "src", "confidence": "low"}, {"file_path": "src_old_reference_scanner.py", "line_number": 112, "line_content": "old_module = f\"src(old).{match.group(1)}\" if match.groups() else \"src(old)\"", "reference_type": "string_literal", "old_module": "src(old).{match.group(1)}", "suggested_new_module": "src.{match.group(1)}", "confidence": "low"}, {"file_path": "src_old_reference_scanner.py", "line_number": 112, "line_content": "old_module = f\"src(old).{match.group(1)}\" if match.groups() else \"src(old)\"", "reference_type": "string_literal", "old_module": "src(old)", "suggested_new_module": "src", "confidence": "low"}, {"file_path": "src_old_reference_scanner.py", "line_number": 128, "line_content": "old_module = f\"src(old).{match.group(1)}\" if match.groups() else \"src(old)\"", "reference_type": "string_literal", "old_module": "src(old).{match.group(1)}", "suggested_new_module": "src.{match.group(1)}", "confidence": "low"}, {"file_path": "src_old_reference_scanner.py", "line_number": 128, "line_content": "old_module = f\"src(old).{match.group(1)}\" if match.groups() else \"src(old)\"", "reference_type": "string_literal", "old_module": "src(old)", "suggested_new_module": "src", "confidence": "low"}, {"file_path": "src_old_reference_scanner.py", "line_number": 158, "line_content": "return old_module.replace(\"src(old)\", \"src\")", "reference_type": "string_literal", "old_module": "src(old)", "suggested_new_module": "src", "confidence": "low"}, {"file_path": "src_old_reference_scanner.py", "line_number": 221, "line_content": "\"# src(old) References Report\",", "reference_type": "comment", "old_module": "src(old)", "suggested_new_module": "src", "confidence": "low"}, {"file_path": "comprehensive_refactoring_plan.py", "line_number": 19, "line_content": "self.src_old_path = Path(\"src(old)\")", "reference_type": "string_literal", "old_module": "src(old)", "suggested_new_module": "src", "confidence": "low"}, {"file_path": "src/shared/data_providers/MIGRATION_GUIDE.md", "line_number": 18, "line_content": "from src(old).shared.ai_chat.data_fetcher import DataFetcher", "reference_type": "import", "old_module": "src(old).shared.ai_chat.data_fetcher", "suggested_new_module": "src.shared.ai_chat.data_fetcher", "confidence": "medium"}, {"file_path": "src/shared/data_providers/MIGRATION_GUIDE.md", "line_number": 70, "line_content": "from src(old).shared.ai_chat.data_fetcher import DataFetcher", "reference_type": "import", "old_module": "src(old).shared.ai_chat.data_fetcher", "suggested_new_module": "src.shared.ai_chat.data_fetcher", "confidence": "medium"}, {"file_path": "src/shared/data_providers/MIGRATION_GUIDE.md", "line_number": 71, "line_content": "from src(old).shared.ai_chat.models import AIAskResult", "reference_type": "import", "old_module": "src(old).shared.ai_chat.models", "suggested_new_module": "src.shared.ai_chat.models", "confidence": "medium"}], "summary": {"by_type": {"string_literal": 29, "comment": 1, "import": 3}, "by_confidence": {"low": 13, "high": 17, "medium": 3}, "by_module": {"src(old)": 10, "src(old).mcp_server": 1, "src(old).shared": 1, "src(old).core": 1, "src(old).data": 1, "src(old).bot": 1, "src(old).security": 1, "src(old).services": 1, "src(old).templates": 1, "src(old).utils": 1, "src(old).data.models": 1, "src(old).data.cache": 1, "src(old).bot.enhancements": 1, "src(old).bot.events": 1, "src(old).bot.security": 1, "src(old).core.secure_cache": 1, "src(old).services.analytics_service": 1, "src(old).templates.analysis_response": 1, "src(old).{match.group(1)}": 3, "src(old).shared.ai_chat.data_fetcher": 2, "src(old).shared.ai_chat.models": 1}, "unique_files": 4, "unique_modules": 21}}