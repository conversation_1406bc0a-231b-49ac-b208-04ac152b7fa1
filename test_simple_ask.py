#!/usr/bin/env python3
"""Simple test for ASK pipeline functionality"""

import asyncio
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def test_ask_pipeline():
    """Test the ASK pipeline with a simple query"""
    print("🚀 Testing ASK Pipeline...")
    
    try:
        # Import the controller
        from src.bot.pipeline.commands.ask.core.controller import PipelineController
        print("✅ Controller imported successfully")
        
        # Create controller instance
        controller = PipelineController()
        print("✅ Controller created successfully")
        
        # Test with a simple query
        query = "What is the current market sentiment?"
        user_id = "test_user_123"
        
        print(f"📝 Testing query: '{query}'")
        result = await controller.process(query, user_id)
        
        print("✅ Pipeline executed successfully!")
        print(f"📊 Result type: {type(result)}")
        print(f"📊 Result: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Pipeline test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    print("🧪 Simple ASK Pipeline Test")
    print("=" * 50)
    
    success = await test_ask_pipeline()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ All tests passed!")
        sys.exit(0)
    else:
        print("❌ Tests failed!")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
