#!/usr/bin/env python3
"""
Simple Dead Code Analysis Script

Analyzes Python files for unused imports and functions.
Uses ast to parse and check for usage.
"""

import ast
import os
from pathlib import Path

def find_unused_imports(root_dir):
    """Find unused imports in Python files."""
    unused = []
    for py_file in Path(root_dir).rglob("*.py"):
        if py_file.is_file():
            tree = ast.parse(py_file.read_text())
            imports = []
            used = set()
            
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        imports.append(alias.name)
                elif isinstance(node, ast.ImportFrom):
                    imports.extend(alias.name for alias in node.names)
                elif isinstance(node, (ast.Attribute, ast.Name)):
                    if isinstance(node, ast.Name):
                        used.add(node.id)
                    elif isinstance(node, ast.Attribute):
                        used.add(node.attr)
            
            for imp in imports:
                if imp not in used:
                    unused.append(f"{py_file}:{imp}")
    
    return unused

def analyze_codebase(root_dir="."):
    """Run analysis."""
    print("Running dead code analysis...")
    unused_imports = find_unused_imports(root_dir)
    
    print(f"Found {len(unused_imports)} unused imports:")
    for item in unused_imports[:10]:  # Top 10
        print(f"  - {item}")
    
    if len(unused_imports) > 10:
        print(f"  ... and {len(unused_imports) - 10} more")
    
    return unused_imports

if __name__ == "__main__":
    analyze_codebase()